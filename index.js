const express = require("express");
const http = require("http");
const dotenv = require("dotenv");
const cors = require("cors");
const path = require("path");
const connectdb = require("./src/config/db");
const expressRoute = require("./src/framework/expressRoute");
const ChatRepository = require("./src/app/repo/chatRepo");
const ChatService = require("./src/app/services/chatService");
const Chat = require("./src/app/models/chatModel");
const { initRedis } = require('./src/utils/redisCache');
// Performance monitoring
const responseTime = require('response-time');
const compression = require('compression');
const mongoose = require('mongoose');

// Build information
const BUILD_TIMESTAMP = new Date().toISOString();
const PACKAGE_VERSION = require('./package.json').version;

// Load environment variables
dotenv.config();

// Initialize app and server
const app = express();
const server = http.createServer(app);
const io = require("socket.io")(server);

// Performance optimizations
app.use(responseTime((req, res, time) => {
  // Log slow responses (>500ms) for performance debugging
  if (time > 500) {
    console.warn(`[SLOW] ${req.method} ${req.originalUrl} - ${time.toFixed(2)}ms`);
  }
}));

// Enable compression for all responses
app.use(compression({
  threshold: 1024, // Only compress responses larger than 1KB
  filter: (req, res) => {
    // Don't compress responses with this header
    if (req.headers['x-no-compression']) {
      return false;
    }
    // Use compression filter defaults for other requests
    return compression.filter(req, res);
  }
}));

// Additional security headers
app.use((req, res, next) => {
  // Add security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  next();
});

// Route cache settings via headers
app.use((req, res, next) => {
  // Add cache control headers based on route
  if (req.path.includes('/api/chat/conversations')) {
    // Cache for 5 minutes, allow revalidation
    res.setHeader('Cache-Control', 'public, max-age=300, stale-while-revalidate=300');
  } else if (req.path.includes('/api/chat/') && req.method === 'GET') {
    // Cache for 1 minute, allow revalidation
    res.setHeader('Cache-Control', 'public, max-age=60, stale-while-revalidate=60');
  } else if (req.method === 'GET' && !req.path.includes('/api/auth/')) {
    // Default cache for GET requests that aren't auth related
    res.setHeader('Cache-Control', 'public, max-age=60');
  } else {
    // No caching for everything else
    res.setHeader('Cache-Control', 'no-store');
  }
  next();
});

// Production optimizations
if (process.env.NODE_ENV === 'production') {
  // Set higher keepalive timeout for long running connections
  server.keepAliveTimeout = 120 * 1000; // 120 seconds
  server.headersTimeout = 125 * 1000; // Adding 5s to keep-alive
  
  // Disable verbose logging
  if (process.env.LOG_LEVEL === 'info') {
    console.log = function() {
      if (arguments[0] && typeof arguments[0] === 'string' && 
          (arguments[0].includes('ERROR') || arguments[0].includes('error') || 
           arguments[0].includes('CRITICAL') || arguments[0].includes('critical'))) {
        console.info.apply(console, arguments);
      }
    };
  }
}

// Connect to database
connectdb();

// Initialize Redis
initRedis().then(() => {
  console.log('Redis connection established successfully');
}).catch(err => {
  console.warn('Redis connection failed, continuing without Redis caching:', err.message);
});

// Middleware setup
app.use(express.json({
  limit: '1mb', // Limit JSON payload size
  strict: true // Reject invalid JSON
}));
app.use(express.urlencoded({ 
  extended: true,
  limit: '1mb'  // Limit URL-encoded payload size
}));
app.use(cors());
app.use(expressRoute);
app.use("/uploads", express.static(path.join(__dirname, "uploads"), {
  maxAge: 86400000 // Cache static assets for 1 day
}));

// Initialize dependencies with optimized configuration
const chatRepository = new ChatRepository();
const chatService = new ChatService(chatRepository);

// Memory usage monitoring
const MEMORY_CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes
setInterval(() => {
  const memUsage = process.memoryUsage();
  console.log(`Memory usage: RSS=${Math.round(memUsage.rss / 1024 / 1024)}MB, Heap=${Math.round(memUsage.heapUsed / 1024 / 1024)}/${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`);
  
  // Force garbage collection if available and memory usage is high
  if (global.gc && memUsage.heapUsed > 0.8 * memUsage.heapTotal) {
    console.log('Forcing garbage collection');
    global.gc();
  }
}, MEMORY_CHECK_INTERVAL);

// Test route to check if the server is working
app.get("/test", (req, res) => {
  res.status(200).send("Server is up and running!");
});

// Deployment status endpoint with more details
app.get("/deployment-status", (req, res) => {
  const memUsage = process.memoryUsage();
  res.status(200).json({
    version: PACKAGE_VERSION,
    deployedAt: BUILD_TIMESTAMP,
    environment: process.env.NODE_ENV || 'production',
    serverTime: new Date().toISOString(),
    uptime: Math.round(process.uptime()),
    memory: {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024)
    }
  });
});

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "UP",
    time: new Date().toISOString()
  });
});

// WebSocket functionality
// Enhanced user connection tracking
const activeConnections = new Map(); // userId -> {socketId, connectedAt, lastActivity}

// Track connections by IP for rate limiting
const connectionAttemptsByIP = new Map(); // IP -> {count, resetTime}
const CONNECTION_LIMIT = 10; // Max 10 connections per minute per IP
const CONNECTION_WINDOW = 60 * 1000; // 1 minute window

// Function to clean up old connection attempts
setInterval(() => {
  const now = Date.now();
  for (const [ip, data] of connectionAttemptsByIP.entries()) {
    if (now > data.resetTime) {
      connectionAttemptsByIP.delete(ip);
    }
  }
}, 60000); // Clean up every minute

// Add socket.io middleware for connection rate limiting
io.use((socket, next) => {
  const clientIp = socket.handshake.address;
  const now = Date.now();
  
  // Initialize or update IP tracking
  if (!connectionAttemptsByIP.has(clientIp)) {
    connectionAttemptsByIP.set(clientIp, {
      count: 1,
      resetTime: now + CONNECTION_WINDOW
    });
  } else {
    const attempts = connectionAttemptsByIP.get(clientIp);
    
    // If window has expired, reset counter
    if (now > attempts.resetTime) {
      connectionAttemptsByIP.set(clientIp, {
        count: 1,
        resetTime: now + CONNECTION_WINDOW
      });
    } else {
      // If too many attempts, reject connection
      if (attempts.count >= CONNECTION_LIMIT) {
        return next(new Error('Too many connection attempts. Please try again later.'));
      }
      
      // Increment counter
      attempts.count++;
      connectionAttemptsByIP.set(clientIp, attempts);
    }
  }
  
  next();
});

// Add a Set to track processed messages
const processedMessages = new Set();
const processedMessagesByContent = new Map();

// Improved function to check and mark messages as processed with TTL
const MESSAGE_CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours
const DUPLICATE_WINDOW = 10 * 1000; // 10 seconds window for duplicate detection

const isDuplicate = (messageId, sender, recipient, message) => {
  const timestamp = Date.now();
  
  // If we have a specific messageId, check it first (most reliable)
  if (messageId && processedMessages.has(messageId)) {
    console.log(`Detected duplicate by messageId: ${messageId}`);
    return true;
  }
  
  // Also check content-based detection for a short time window
  const contentKey = `${sender}:${recipient}:${message}`;
  if (processedMessagesByContent.has(contentKey)) {
    const lastSentTime = processedMessagesByContent.get(contentKey);
    // Only consider it a duplicate if sent within the duplicate window timeframe
    if (timestamp - lastSentTime < DUPLICATE_WINDOW) {
      console.log(`Detected duplicate by content within ${DUPLICATE_WINDOW}ms: ${contentKey}`);
      return true;
    }
  }
  
  // Not a duplicate - track both the ID and content
  if (messageId) {
    processedMessages.add(messageId);
    // Set timeout to remove from tracking after TTL
    setTimeout(() => {
      processedMessages.delete(messageId);
    }, MESSAGE_CACHE_TTL);
  }
  
  // Update the timestamp for this content
  processedMessagesByContent.set(contentKey, timestamp);
  // Set timeout to remove content tracking after the duplicate window
  setTimeout(() => {
    // Only delete if the timestamp matches (to avoid deleting newer entries with same key)
    if (processedMessagesByContent.get(contentKey) === timestamp) {
      processedMessagesByContent.delete(contentKey);
    }
  }, DUPLICATE_WINDOW);
  
  return false;
};

io.on("connection", (socket) => {
  const socketId = socket.id;
  const timestamp = new Date().toISOString();

  console.log(
    `[${timestamp}] New WebSocket connection established: ${socketId}`
  );

  // Auto-register user on connection
  const userId = socket.handshake.query.userId; // Ensure userId is passed during connection
  if (userId) {
    activeConnections.set(userId, {
      socketId,
      connectedAt: Date.now(),
      lastActivity: Date.now()
    });
    console.log(
      `[${timestamp}] Auto-registered user: ${userId} (Socket ID: ${socketId})`
    );
  } else {
    console.warn(
      `[${timestamp}] Connection attempt without userId (Socket ID: ${socketId})`
    );
  }

  // Handle sending messages
  socket.on("sendMessage", async (messageData) => {
    const { message, sender, recipient, senderModel, recipientModel, tempId, messageId, isBroadcast, conversationId } = messageData;
    const timestamp = new Date().toISOString();
    
    // Log message request with key identifiers for debugging
    console.log(`[${timestamp}] Message request: ${sender} -> ${recipient} | Content: ${message?.substring(0, 30)}... | ID: ${tempId || messageId || 'none'} | isBroadcast: ${isBroadcast}`);

    if (!message || !sender || !recipient) {
      console.error(
        `[${timestamp}] Invalid message data: ${JSON.stringify(messageData)}`
      );
      // Send failure confirmation back to sender
      if (activeConnections.has(sender)) {
        io.to(activeConnections.get(sender).socketId).emit("messageConfirmation", {
          success: false,
          messageId: tempId || messageId || null,
          error: "Missing required message data"
        });
      }
      return;
    }

    // Use client-provided ID with preference for specific ID formats
    // Give preference to tempId (from Flutter client) over messageId
    const msgId = tempId || messageId || `gen_${Date.now().toString()}`;
    
    // Check for duplicates using enhanced duplicate detection
    if (isDuplicate(msgId, sender, recipient, message)) {
      console.log(`[${timestamp}] Ignoring duplicate message: ${msgId}`);
      
      // Still send confirmation to sender to prevent re-sending
      if (activeConnections.has(sender)) {
        io.to(activeConnections.get(sender).socketId).emit("messageConfirmation", {
          success: true,
          messageId: msgId,
          duplicateDetected: true,
          timestamp: new Date().toISOString()
        });
      }
      return;
    }
    
    try {
      // Log saving attempt
      console.log(`[${timestamp}] Saving message to database: ${msgId} | ${sender} -> ${recipient} | isBroadcast: ${isBroadcast}`);
      
      // Include conversationId and isBroadcast flags in the save
      const chatOptions = {};
      
      // If this is a broadcast message, ensure we have the right conversationId format
      if (isBroadcast === true) {
        chatOptions.isBroadcast = true;
        chatOptions.conversationId = conversationId || `-${sender}`;
        console.log(`[${timestamp}] Saving as broadcast message with conversationId: ${chatOptions.conversationId}`);
      }
      
      // Save the message in the database
      const savedMessage = await chatService.addChat(
        message,
        sender,
        recipient,
        senderModel,
        recipientModel,
        chatOptions
      );
      
      console.log(`[${timestamp}] Message saved to the database with ID: ${savedMessage._id}`);
      
      const messagePayload = {
        _id: savedMessage._id,
        message,
        sender,
        recipient,
        senderModel,
        recipientModel,
        timestamp: savedMessage.timestamp,
        status: 'sent',
        clientMessageId: msgId,
        isBroadcast: isBroadcast,
        conversationId: savedMessage.conversationId
      };

      let isDelivered = false;
      // Emit message to recipient if online
      const recipientSocketId = activeConnections.has(recipient) ? activeConnections.get(recipient).socketId : null;
      if (recipientSocketId) {
        try {
          io.to(recipientSocketId).emit("message", messagePayload);
          console.log(
            `[${timestamp}] Message sent to recipient ${recipient} from ${sender}: ${message}`
          );
          
          // Update status to 'delivered' when recipient is online
          savedMessage.status = 'delivered';
          await savedMessage.save();
          messagePayload.status = 'delivered';
          isDelivered = true;
        } catch (deliveryError) {
          console.error(`[${timestamp}] Error delivering message to recipient: ${deliveryError.message}`);
          // Continue with sent status instead of delivered
        }
      } else {
        console.warn(`[${timestamp}] Recipient ${recipient} not connected`);
      }

      // Emit confirmation to sender
      const senderSocketId = activeConnections.has(sender) ? activeConnections.get(sender).socketId : null;
      if (senderSocketId) {
        // Make sure we send the latest status
        io.to(senderSocketId).emit("messageConfirmation", {
          success: true,
          messageId: msgId,
          dbMessageId: savedMessage._id.toString(), // Ensure it's a string
          status: isDelivered ? 'delivered' : 'sent',
          timestamp: savedMessage.timestamp,
          isBroadcast: savedMessage.isBroadcast,
          conversationId: savedMessage.conversationId
        });
        
        // Also emit a messageSent event for backwards compatibility with older clients
        io.to(senderSocketId).emit("messageSent", {
          success: true,
          tempId: msgId,
          id: savedMessage._id.toString(),
          status: isDelivered ? 'delivered' : 'sent',
          timestamp: savedMessage.timestamp,
          isBroadcast: savedMessage.isBroadcast,
          conversationId: savedMessage.conversationId
        });
        console.log(
          `[${timestamp}] Confirmation sent to sender ${sender}: ${message} with status ${isDelivered ? 'delivered' : 'sent'}`
        );
      } else {
        console.warn(`[${timestamp}] Sender ${sender} not connected`);
      }
    } catch (error) {
      console.error(
        `[${timestamp}] Error processing message: ${error.message}`
      );
      
      // Send error notification to sender if they're still connected
      const senderSocketId = activeConnections.has(sender) ? activeConnections.get(sender).socketId : null;
      if (senderSocketId) {
        io.to(senderSocketId).emit("messageError", {
          success: false,
          messageId: msgId,
          error: "Failed to process message"
        });
      }
    }
  });

  // Add message status update handler
  socket.on("updateMessageStatus", async (data) => {
    const { messageId, status } = data;
    const timestamp = new Date().toISOString();
    
    if (!messageId || !status) {
      console.error(`[${timestamp}] Invalid message status update data: ${JSON.stringify(data)}`);
      return;
    }
    
    if (!['sent', 'delivered', 'read'].includes(status)) {
      console.error(`[${timestamp}] Invalid status value: ${status}`);
      return;
    }
    
    try {
      // Update message status in database
      const updatedMessage = await Chat.findByIdAndUpdate(
        messageId,
        { status },
        { new: true }
      );
      
      if (!updatedMessage) {
        console.warn(`[${timestamp}] Message not found for status update: ${messageId}`);
        return;
      }
      
      console.log(`[${timestamp}] Updated message ${messageId} status to ${status}`);
      
      // Notify sender of status change
      if (updatedMessage.sender) {
        const senderId = updatedMessage.sender.toString();
        const senderSocketId = activeConnections.has(senderId) ? activeConnections.get(senderId).socketId : null;
        
        if (senderSocketId) {
          io.to(senderSocketId).emit("messageStatusUpdate", {
            messageId: messageId,
            status: status
          });
          console.log(`[${timestamp}] Notified sender ${senderId} of status update`);
        } else {
          console.log(`[${timestamp}] Sender ${senderId} not connected to receive status update`);
        }
      }
    } catch (error) {
      console.error(`[${timestamp}] Error updating message status: ${error.message}`);
    }
  });

  // Add read receipt handler
  socket.on("messageRead", async (data) => {
    const { messageIds } = data;
    const timestamp = new Date().toISOString();
    
    if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
      console.error(`[${timestamp}] Invalid message read data: ${JSON.stringify(data)}`);
      return;
    }
    
    console.log(`[${timestamp}] Processing read receipts for ${messageIds.length} messages`);
    
    try {
      // Update multiple messages to 'read' status
      const result = await Chat.updateMany(
        { _id: { $in: messageIds } },
        { status: 'read' }
      );
      
      console.log(`[${timestamp}] Marked ${result.modifiedCount} messages as read`);
      
      if (result.modifiedCount === 0) {
        console.log(`[${timestamp}] No messages were updated to read status`);
        return;
      }
      
      // Fetch the updated messages to get sender info
      const updatedMessages = await Chat.find({ _id: { $in: messageIds } });
      
      if (updatedMessages.length === 0) {
        console.warn(`[${timestamp}] No messages found after status update`);
        return;
      }
      
      // Group messages by sender for efficient notifications
      const senderMessages = {};
      
      updatedMessages.forEach(msg => {
        if (!msg.sender) {
          console.warn(`[${timestamp}] Message ${msg._id} has no sender, skipping notification`);
          return;
        }
        
        const senderId = msg.sender.toString();
        if (!senderMessages[senderId]) {
          senderMessages[senderId] = [];
        }
        senderMessages[senderId].push(msg._id.toString()); // Ensure it's a string
      });
      
      // Notify each sender about read messages
      Object.entries(senderMessages).forEach(([senderId, ids]) => {
        const senderSocketId = activeConnections.has(senderId) ? activeConnections.get(senderId).socketId : null;
        if (senderSocketId) {
          io.to(senderSocketId).emit("messagesRead", {
            messageIds: ids
          });
          console.log(`[${timestamp}] Notified sender ${senderId} about ${ids.length} read messages`);
        } else {
          console.log(`[${timestamp}] Sender ${senderId} not connected to receive read receipts`);
        }
      });
    } catch (error) {
      console.error(`[${timestamp}] Error marking messages as read: ${error.message}`);
    }
  });

  // Fetch previous messages
  socket.on("fetchMessage", async ({ user1, user2, type1, type2, beforeTimestamp, limit, conversationId, isBroadcast }) => {
    console.log(
      `[${timestamp}] Fetching messages for users : ${user1}, ${user2}`
    );

    try {
      // If this is a broadcast/self-conversation (same user as sender and recipient)
      if (isBroadcast === true || (user1 === user2 && conversationId?.startsWith('-'))) {
        console.log(`[${timestamp}] Redis event: fetch_request - Fetching broadcast messages for user ${user1}`);
        
        // Use special query for broadcast messages (look for the special conversation ID format)
        const options = {
          beforeTimestamp,
          limit: limit || 30,
          isBroadcast: true
        };
        
        // Use the conversation ID directly if it's in the format -userId
        if (conversationId && conversationId.startsWith('-')) {
          options.conversationId = conversationId;
        }
        
        // Get broadcast messages
        const messagesData = await chatService.getBroadcastMessages(user1, options);
        
        socket.emit("fetchedMessage", messagesData);
        console.log(
          `[${timestamp}] Redis event: fetch_success - Retrieved ${messagesData.messages.length} broadcast messages`
        );
        return;
      }
      
      // Validate user IDs for regular conversations
      if (!user1 || !user2 || user1 === '' || user2 === '') {
        console.warn(`[${timestamp}] Invalid user IDs provided: user1=${user1}, user2=${user2}`);
        socket.emit("fetchedMessage", { messages: [], nextCursor: null });
        return;
      }

      const options = {
        beforeTimestamp,
        limit: limit || 30
      };
      
      console.log(`[${timestamp}] Redis event: fetch_request - Fetching messages between ${user1} and ${user2}`);
      
      const messagesData = await chatService.getChatsBetweenUsers(
        user1,
        user2,
        type1,
        type2,
        options
      );
      
      socket.emit("fetchedMessage", messagesData);
      console.log(
        `[${timestamp}] Redis event: fetch_success - Retrieved ${messagesData.messages.length} messages`
      );
    } catch (error) {
      console.error(`[${timestamp}] Error fetching messages: ${error.message}`);
      socket.emit("FetchedMessageError", {
        error: "Unable to fetch messages.",
      });
    }
  });

  // Handle user disconnection
  socket.on("disconnect", () => {
    const disconnectedUser = [...activeConnections.entries()].find(
      ([_, data]) => data.socketId === socketId
    )?.[0];

    if (disconnectedUser) {
      activeConnections.delete(disconnectedUser); // Remove the user from active connections
      console.log(
        `[${timestamp}] User disconnected: ${disconnectedUser} (Socket ID: ${socketId})`
      );
    } else {
      console.warn(`[${timestamp}] Unknown disconnect: Socket ID ${socketId}`);
    }
  });

  // Handle errors on the socket
  socket.on("error", (error) => {
    console.error(`[${timestamp}] Socket error: ${error.message}`);
  });
});

// Start server
const port = process.env.PORT ?? 3000;
server.listen(port, async () => {
  console.log(`Server is running on port ${port}`);
  
  // Initialize Redis if enabled
  if (process.env.ENABLE_REDIS_CACHE === 'true') {
    try {
      console.log('Attempting to initialize Redis cache...');
      const client = await initRedis();
      
      if (!client) {
        console.warn('Redis client initialization returned null. Falling back to non-Redis mode.');
        process.env.ENABLE_REDIS_CACHE = 'false';
      } else {
        // Test Redis connection with a simple operation
        try {
          await client.set('test-connection', 'success');
          const testResult = await client.get('test-connection');
          if (testResult === 'success') {
            console.log('Redis connection test successful. Redis caching is active.');
            await client.del('test-connection');
          } else {
            throw new Error('Redis test key returned unexpected value');
          }
        } catch (testError) {
          console.error('Redis connection test failed:', testError.message);
          console.log('Falling back to non-Redis mode due to connection test failure');
          process.env.ENABLE_REDIS_CACHE = 'false';
        }
      }
    } catch (error) {
      console.error('Failed to initialize Redis cache:', error.message);
      console.log('Server will continue without Redis caching');
      
      // Set environment variable to disable Redis for this session
      process.env.ENABLE_REDIS_CACHE = 'false';
    }
  } else {
    console.log('Redis caching is disabled by configuration');
  }
});