{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node ./index.js", "dev": "nodemon ./index.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.9.0", "basic-ftp": "^5.0.5", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "express": "^4.19.2", "firebase": "^11.1.0", "firebase-admin": "^13.0.2", "form-data": "^4.0.2", "http": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "mongodb": "^6.8.0", "mongoose": "^8.5.2", "multer": "^1.4.5-lts.1", "multiparty": "^4.2.3", "node-cron": "^3.0.3", "nodemailer": "^6.9.16", "qrcode": "^1.5.4", "redis": "^5.0.1", "response-time": "^2.3.3", "socket.io": "^4.8.1", "stripe": "^17.7.0", "uuid": "^10.0.0"}, "devDependencies": {"nodemon": "^3.1.9"}}