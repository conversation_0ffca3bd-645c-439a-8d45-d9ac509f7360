// import 'package:class_z/presentation/widgets/build_icon.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:integration_test/integration_test.dart';
// import '../main.dart' as app;
// import 'package:class_z/user/presentation/screen/user_main_page.dart'; // Adjust import if necessary

// void main() {
//   IntegrationTestWidgetsFlutterBinding.ensureInitialized();

//   testWidgets('Full app integration test', (WidgetTester tester) async {
//     // Start the app
//     app.main();
//     await tester.pumpAndSettle();

//     // Print the widget tree for debugging
//     print(tester.allWidgets.toString());

//     // Verify if the text containing "Hello," is present
//     final helloTextFinder = find.textContaining('Hello,');
//     if (helloTextFinder.evaluate().isEmpty) {
//       print('Text containing "Hello," not found');
//     } else {
//       print('Text containing "Hello," found');
//     }

//     // Add a delay if necessary
//     await tester.pump(Duration(seconds: 2));

//     // Check for the text again
//     expect(find.textContaining('Hello,'), findsOneWidget);

//     // Verify other widgets or actions if needed
//     expect(find.byType(CustomIconButton), findsOneWidget);
//     expect(find.byType(CircularProgressIndicator), findsNothing);
//     expect(find.byType(ListView), findsOneWidget);
//     expect(find.byType(Image), findsWidgets);

//     // Example: Verify and interact with other widgets
//     expect(find.text('see all'), findsOneWidget);
//     await tester.tap(find.text('see all'));
//     await tester.pumpAndSettle();
//   });
// }
