const mongoose = require('mongoose');
// Use the connection string from config
const config = require('../config');
const connectionString = config.database.connectionString;

// Import models and services
const CampaignModel = require('../src/app/models/campaignModel');
const DiscountModel = require('../src/app/models/discountModel');
const CampaignRepository = require('../src/app/repo/campaignRepo');
const DiscountRepository = require('../src/app/repo/discountRepo');
const CampaignDiscountService = require('../src/app/services/campaignDiscountService');

async function syncCampaignDiscounts() {
  try {
    // Connect to MongoDB
    await mongoose.connect(connectionString);
    console.log('✅ Connected to MongoDB');

    // Initialize repositories and service
    const campaignRepo = new CampaignRepository(CampaignModel);
    const discountRepo = new DiscountRepository(DiscountModel);
    const campaignDiscountService = new CampaignDiscountService(campaignRepo, discountRepo);

    // Sync all campaign discounts
    console.log('🔄 Syncing campaign discounts...');
    const synced = await campaignDiscountService.syncCampaignDiscounts();

    console.log(`✅ Successfully synced ${synced.length} campaign discounts:`);
    synced.forEach((discount, index) => {
      console.log(`${index + 1}. ${discount.code} - ${discount.discountPercentage}% off`);
    });

    // Test the LUNAR2024 discount specifically
    console.log('\n🧪 Testing LUNAR2024 discount application...');
    const testPrice = 100; // 100 ZCoin example
    const discountResult = await campaignDiscountService.applyCampaignDiscount('LUNAR2024', testPrice);
    
    console.log('Discount Test Result:');
    console.log(`- Original Price: ${discountResult.originalPrice} ZCoin`);
    console.log(`- Discount: ${discountResult.discountPercentage}% (${discountResult.discountAmount} ZCoin)`);
    console.log(`- Final Price: ${discountResult.finalPrice} ZCoin`);

    console.log('\n✅ Campaign discount sync completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error syncing campaign discounts:', error);
    process.exit(1);
  }
}

syncCampaignDiscounts(); 