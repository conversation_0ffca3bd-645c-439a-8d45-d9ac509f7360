const mongoose = require('mongoose');
const config = require('../config');

console.log('Starting campaign creation script...');

// Connect to MongoDB
mongoose.connect(config.database.connectionString, {
  connectTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  serverSelectionTimeoutMS: 60000,
}).then(() => {
  console.log('Connected to MongoDB successfully');
}).catch(err => {
  console.error('Failed to connect to MongoDB:', err);
  process.exit(1);
});

// Campaign Schema
const campaignSchema = new mongoose.Schema({
  title: { type: String, required: true, trim: true },
  subtitle: { type: String, required: true, trim: true },
  description: { type: String, default: "" },
  imageUrl: { type: String, default: "" },
  backgroundColor: { type: String, default: "#FF6B6B" },
  textColor: { type: String, default: "#FFFFFF" },
  discountPercentage: { type: Number, min: 0, max: 100, default: 0 },
  discountCode: { type: String, default: "" },
  validFrom: { type: Date, required: true },
  validUntil: { type: Date, required: true },
  isActive: { type: Boolean, default: true },
  priority: { type: Number, default: 0 },
  targetAudience: { type: String, enum: ["all", "parent", "center", "coach", "owner"], default: "all" },
  actionType: { type: String, enum: ["none", "discount", "navigate", "external_link"], default: "none" },
  actionData: { type: mongoose.Schema.Types.Mixed, default: {} },
  clickCount: { type: Number, default: 0 },
  impressionCount: { type: Number, default: 0 }
}, { timestamps: true });

const Campaign = mongoose.model('campaign', campaignSchema);

// Sample campaigns data
const sampleCampaigns = [
  {
    title: "Lunar New Year Special",
    subtitle: "20% Off All Classes",
    description: "Celebrate the Lunar New Year with amazing discounts on all classes!",
    backgroundColor: "#FF6B6B",
    textColor: "#FFFFFF",
    discountPercentage: 20,
    discountCode: "LUNAR2024",
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    isActive: true,
    priority: 10,
    targetAudience: "all",
    actionType: "discount"
  },
  {
    title: "Spring Special",
    subtitle: "Music Classes 15% Off",
    description: "Spring into action with our music classes!",
    backgroundColor: "#4ECDC4",
    textColor: "#FFFFFF",
    discountPercentage: 15,
    discountCode: "SPRING15",
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
    isActive: true,
    priority: 8,
    targetAudience: "parent",
    actionType: "discount"
  },
  {
    title: "Summer Camp Early Bird",
    subtitle: "30% Off Registration",
    description: "Register early for our summer camps and save big!",
    backgroundColor: "#FFD93D",
    textColor: "#333333",
    discountPercentage: 30,
    discountCode: "SUMMER30",
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
    isActive: true,
    priority: 9,
    targetAudience: "all",
    actionType: "discount"
  }
];

async function createSampleCampaigns() {
  try {
    console.log('Creating sample campaigns...');
    
    // Clear existing campaigns
    const deleteResult = await Campaign.deleteMany({});
    console.log(`Deleted ${deleteResult.deletedCount} existing campaigns`);
    
    // Insert sample campaigns
    const result = await Campaign.insertMany(sampleCampaigns);
    console.log(`Created ${result.length} sample campaigns successfully!`);
    
    // Display created campaigns
    result.forEach((campaign, index) => {
      console.log(`${index + 1}. ${campaign.title} - ${campaign.discountPercentage}% off`);
    });
    
  } catch (error) {
    console.error('Error creating sample campaigns:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
    process.exit(0);
  }
}

// Run the script
createSampleCampaigns(); 