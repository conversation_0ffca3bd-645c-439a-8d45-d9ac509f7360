# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ClassZ Backend is a Node.js/Express application for managing educational classes, centers, coaches, students, and related services. The system handles real-time chat via Socket.io, payment processing via Stripe, and includes comprehensive user management for multiple roles.

## Development Commands

### Server Operations
- `npm start` - Start production server (node ./index.js)
- `npm run dev` - Start development server with nodemon (auto-restart on changes)

### Database & Environment
- MongoDB connection configured in `src/config/db.js`
- Environment variables in `.env` (JWT_SECRET_KEY, NODE_ENV)
- Fallback configuration in `config.js` for missing environment variables

## Architecture Overview

### Clean Architecture Pattern
The codebase follows clean architecture principles with clear separation of concerns:

```
src/
├── app/
│   ├── controllers/     # HTTP request handlers
│   ├── models/         # Mongoose schemas and database models
│   ├── repo/          # Data access layer (repositories)
│   ├── use_case/      # Business logic layer
│   ├── services/      # External services and utilities
│   ├── route/         # Express route definitions
│   └── entities/      # Domain entities
├── config/            # Database and external service configs
├── framework/         # Express route configuration
├── interfaces/        # TypeScript-style interfaces for services
├── middleware/        # Authentication and validation middleware
└── utils/            # Shared utilities and helpers
```

### Key Components

**User Roles & Models:**
- `baseUserModel.js` - Base user schema with role-based inheritance
- Specialized models: `coachModel.js`, `centerModel.js`, `businessOwnerModel.js`, `childModel.js`
- User types: coach, center, business_owner, child, parent

**Core Business Logic:**
- `classModel.js` - Core class management with status tracking, cancellation, and rearrangement features
- `eventModel.js` - Event system for class scheduling
- `orderModel.js` - Order processing and payment tracking
- `subscriptionModel.js` - Subscription management for recurring services

**Real-time Features:**
- Socket.io integration in `index.js` for live chat functionality
- Chat system with message status tracking (sent/delivered/read)
- Connection management with rate limiting and duplicate message prevention

**Payment Integration:**
- Stripe integration for payment processing
- Card storage and management via `paymentService.js`
- Refund and cancellation handling

### Entry Points
- `index.js` - Main server file with Socket.io setup, middleware, and performance monitoring
- `src/framework/expressRoute.js` - Central route configuration
- `src/config/db.js` - Database connection with optimized connection pooling

### Data Flow
1. HTTP requests → Routes (`src/app/route/`) → Controllers (`src/app/controllers/`)
2. Controllers → Use Cases (`src/app/use_case/`) for business logic
3. Use Cases → Repositories (`src/app/repo/`) for data access
4. Repositories → Models (`src/app/models/`) for database operations

### Key Features
- **Multi-role user management** with inheritance-based user models
- **Class lifecycle management** including minimum student checks and automatic cancellation
- **Real-time chat system** with message status tracking and rate limiting
- **Payment processing** with Stripe integration and refund capabilities
- **Event system** for class scheduling and notifications
- **Redis caching** (optional) for performance optimization
- **File upload handling** with multiple image types (mainImage, businessCertificate, hkidCard)

### Important Dependencies
- `mongoose` - MongoDB ODM with optimized connection pooling
- `socket.io` - Real-time bidirectional communication
- `stripe` - Payment processing (v17.7.0)
- `express` - Web framework with compression and security headers
- `multer` - File upload handling
- `nodemailer` - Email service integration
- `redis` - Optional caching layer
- `firebase-admin` - Firebase integration for notifications

### Performance Considerations
- Connection pooling configured in database setup
- Response time monitoring for requests >500ms
- Memory usage monitoring with garbage collection
- Compression middleware for responses >1KB
- Cache headers for static assets and API responses
- Rate limiting for WebSocket connections

### Error Handling
- Centralized error handling in controllers
- Structured error responses with success/failure indicators
- Comprehensive logging for debugging and monitoring
- Graceful fallbacks when Redis/external services are unavailable