# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ClassZ is a Flutter application that connects parents with centers offering children's classes (music, arts, sports, etc.). The app supports multi-role users including parents, coaches, centers, and owners with dynamic scheduling and streamlined class booking.

## Development Commands

### Build & Run
```bash
# Install dependencies
flutter pub get

# Generate code (models, dependency injection)
flutter packages pub run build_runner build --delete-conflicting-outputs

# Run the app
flutter run

# Build for production
flutter build apk --release
flutter build ios --release
```

### Code Analysis & Testing
```bash
# Run static analysis
flutter analyze

# Run tests
flutter test

# Run integration tests
flutter test integration_test/
```

### Code Generation
The project uses code generation for several features:
- Hive adapters for local storage
- JSON serialization
- Dependency injection via injectable
- Floor database entities

Run this command after modifying models or adding new injectable classes:
```bash
flutter packages pub run build_runner build --delete-conflicting-outputs
```

## Architecture

### Core Architecture Pattern
- **Clean Architecture** with Domain-Driven Design
- **<PERSON><PERSON><PERSON><PERSON>** for state management using flutter_bloc
- **Dependency Injection** using get_it and injectable
- **Repository Pattern** for data layer abstraction

### Project Structure
```
lib/
├── core/                    # Shared utilities and configuration
│   ├── network/            # API service, endpoints
│   ├── utils/              # Helper utilities, shared preferences
│   ├── error/              # Exception handling
│   └── widgets/            # Reusable UI components
├── features/               # Feature-based modules
│   ├── authentications/    # Login, signup, verification
│   ├── roles/             # Role-specific features
│   │   ├── parent/        # Parent-specific screens and logic
│   │   ├── coach/         # Coach-specific screens and logic
│   │   ├── center/        # Center-specific screens and logic
│   │   └── owner/         # Owner-specific screens and logic
│   ├── chats/             # Real-time messaging
│   └── transactions/      # Payment and billing
├── dependency_injection/   # Service locator setup
└── routes/                # Navigation and routing
```

### Feature Structure
Each feature follows Clean Architecture layers:
```
feature_name/
├── data/
│   ├── datasources/       # Remote and local data sources
│   ├── models/           # Data models with JSON serialization
│   └── repositories/     # Repository implementations
├── domain/
│   ├── entities/         # Business entities
│   ├── repositories/     # Repository interfaces
│   └── usecases/         # Business logic use cases
└── presentation/
    ├── bloc/             # BLoC state management
    ├── screens/          # UI screens
    └── widgets/          # Feature-specific widgets
```

## Key Technical Concepts

### Multi-Role User System
The app supports different user roles (parent, coach, center, owner) with shared authentication but role-specific functionality. User data is managed through discriminators in the backend and handled via the `UserModel` class.

### State Management
- Uses **flutter_bloc** for predictable state management
- Each feature has its own BLoC for state isolation
- Shared state is managed through dependency injection

### Data Persistence
- **Hive** for local storage and caching
- **SharedPreferences** for simple key-value storage
- **Floor** database for complex local data relationships

### API Integration
- **Retrofit** for type-safe HTTP client
- **Dio** for advanced HTTP features
- Centralized API service in `lib/core/network/api_service.dart`
- Token-based authentication with automatic refresh

### Real-time Features
- **Socket.IO** for real-time chat functionality
- **Firebase** for push notifications
- WebSocket connections for live updates

## Development Guidelines

### Adding New Features
1. Create feature folder following the clean architecture pattern
2. Add data models with `@JsonSerializable()` annotations
3. Implement repository pattern (interface + implementation)
4. Create use cases for business logic
5. Build BLoC for state management
6. Register dependencies in `injection.dart`
7. Add routes in `routes.dart`

### Code Generation Dependencies
After adding new models or injectable classes, run:
```bash
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### Navigation
- Uses custom route management in `lib/routes/routes.dart`
- Route names defined as constants in `AppRoutes` class
- Support for typed route arguments

### Common Patterns
- **Repository Pattern**: Data access abstraction
- **Use Case Pattern**: Single responsibility business logic
- **BLoC Pattern**: Event-driven state management
- **Dependency Injection**: Service location and testing support

### Firebase Integration
- Firebase Core initialization in `main.dart`
- Firebase Messaging for push notifications
- Proper iOS/Android platform-specific setup

### Testing Approach
- Unit tests for use cases and repositories
- Widget tests for UI components
- Integration tests for complete user flows
- Mock services using `mockito`

## Environment Configuration

### Base URL Configuration
The API base URL is configured in `lib/core/constants/string.dart` under `AppText.device`. Update this for different environments.

### Firebase Configuration
- iOS: `ios/Runner/GoogleService-Info.plist`
- Android: `android/app/google-services.json`

### Build Configurations
- Development and production builds use the same configuration
- Environment-specific settings managed through `AppConfig` class