-------------------------------------
Translated Report (Full Report Below)
-------------------------------------

Incident Identifier: 9BE38676-31CE-4E21-A091-96CD7C7CF187
CrashReporter Key:   E409E2B7-4646-0FF5-FCAE-580E35126684
Hardware Model:      MacBookPro18,3
Process:             Runner [18971]
Path:                /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Runner
Identifier:          com.example.classz
Version:             1.0.3 (1)
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd_sim [47625]
Coalition:           com.apple.CoreSimulator.SimDevice.76C11A2B-E7B7-495C-84BA-B0248EF2D472 [9786]
Responsible Process: SimulatorTrampoline [2604]

Date/Time:           2025-07-22 00:49:20.0317 +0700
Launch Time:         2025-07-22 00:49:19.4250 +0700
OS Version:          macOS 15.2 (24C101)
Release Type:        User
Report Version:      104

Exception Type:  EXC_CRASH (SIGABRT)
Exception Codes: 0x0000000000000000, 0x0000000000000000
Termination Reason: SIGNAL 6 Abort trap: 6
Terminating Process: Runner [18971]

Triggered by Thread:  0

Last Exception Backtrace:
0   CoreFoundation                	       0x1804c88b4 __exceptionPreprocess + 160
1   libobjc.A.dylib               	       0x1800937cc objc_exception_throw + 72
2   CoreFoundation                	       0x1804c87d0 -[NSException initWithCoder:] + 0
3   FirebaseCore                  	       0x100b7d32c +[FIRApp configure] + 120 (FIRApp.m:115)
4   Runner.debug.dylib            	       0x10224f3b8 AppDelegate.application(_:didFinishLaunchingWithOptions:) + 80 (AppDelegate.swift:17)
5   Runner.debug.dylib            	       0x10224fbb4 @objc AppDelegate.application(_:didFinishLaunchingWithOptions:) + 220
6   UIKitCore                     	       0x185bcb5f4 -[UIApplication _handleDelegateCallbacksWithOptions:isSuspended:restoreState:] + 312
7   UIKitCore                     	       0x185bccab8 -[UIApplication _callInitializationDelegatesWithActions:forCanvas:payload:fromOriginatingProcess:] + 2916
8   UIKitCore                     	       0x185bd1e30 -[UIApplication _runWithMainScene:transitionContext:completion:] + 980
9   UIKitCore                     	       0x1851d4360 -[_UISceneLifecycleMultiplexer completeApplicationLaunchWithFBSScene:transitionContext:] + 104
10  UIKitCore                     	       0x18577962c _UIScenePerformActionsWithLifecycleActionMask + 96
11  UIKitCore                     	       0x1851d4c6c __101-[_UISceneLifecycleMultiplexer _evalTransitionToSettings:fromSettings:forceExit:withTransitionStore:]_block_invoke + 224
12  UIKitCore                     	       0x1851d47c8 -[_UISceneLifecycleMultiplexer _performBlock:withApplicationOfDeactivationReasons:fromReasons:] + 204
13  UIKitCore                     	       0x1851d4aa0 -[_UISceneLifecycleMultiplexer _evalTransitionToSettings:fromSettings:forceExit:withTransitionStore:] + 576
14  UIKitCore                     	       0x1851d44b8 -[_UISceneLifecycleMultiplexer uiScene:transitionedFromState:withTransitionContext:] + 240
15  UIKitCore                     	       0x1851df3e4 __186-[_UIWindowSceneFBSSceneTransitionContextDrivenLifecycleSettingsDiffAction _performActionsForUIScene:withUpdatedFBSScene:settingsDiff:fromSettings:transitionContext:lifecycleActionType:]_block_invoke + 140
16  UIKitCore                     	       0x1856688c8 +[BSAnimationSettings(UIKit) tryAnimatingWithSettings:fromCurrentState:actions:completion:] + 656
17  UIKitCore                     	       0x185791c2c _UISceneSettingsDiffActionPerformChangesWithTransitionContextAndCompletion + 196
18  UIKitCore                     	       0x1851df0f0 -[_UIWindowSceneFBSSceneTransitionContextDrivenLifecycleSettingsDiffAction _performActionsForUIScene:withUpdatedFBSScene:settingsDiff:fromSettings:transitionContext:lifecycleActionType:] + 288
19  UIKitCore                     	       0x18500f3bc __64-[UIScene scene:didUpdateWithDiff:transitionContext:completion:]_block_invoke.201 + 612
20  UIKitCore                     	       0x18500e160 -[UIScene _emitSceneSettingsUpdateResponseForCompletion:afterSceneUpdateWork:] + 200
21  UIKitCore                     	       0x18500f03c -[UIScene scene:didUpdateWithDiff:transitionContext:completion:] + 220
22  UIKitCore                     	       0x185bd0a38 -[UIApplication workspace:didCreateScene:withTransitionContext:completion:] + 772
23  UIKitCore                     	       0x1856922ec -[UIApplicationSceneClientAgent scene:didInitializeWithEvent:completion:] + 260
24  FrontBoardServices            	       0x187bfb03c __95-[FBSScene _callOutQueue_didCreateWithTransitionContext:alternativeCreationCallout:completion:]_block_invoke + 260
25  FrontBoardServices            	       0x187bfb3fc -[FBSScene _callOutQueue_coalesceClientSettingsUpdates:] + 60
26  FrontBoardServices            	       0x187bfaebc -[FBSScene _callOutQueue_didCreateWithTransitionContext:alternativeCreationCallout:completion:] + 408
27  FrontBoardServices            	       0x187c22318 __93-[FBSWorkspaceScenesClient _callOutQueue_sendDidCreateForScene:transitionContext:completion:]_block_invoke.156 + 232
28  FrontBoardServices            	       0x187c06c18 -[FBSWorkspace _calloutQueue_executeCalloutFromSource:withBlock:] + 160
29  FrontBoardServices            	       0x187c20944 -[FBSWorkspaceScenesClient _callOutQueue_sendDidCreateForScene:transitionContext:completion:] + 392
30  libdispatch.dylib             	       0x180197278 _dispatch_client_callout + 12
31  libdispatch.dylib             	       0x180181a30 _dispatch_block_invoke_direct + 376
32  FrontBoardServices            	       0x187c3dfb8 __FBSSERIALQUEUE_IS_CALLING_OUT_TO_A_BLOCK__ + 44
33  FrontBoardServices            	       0x187c3de94 -[FBSMainRunLoopSerialQueue _targetQueue_performNextIfPossible] + 196
34  FrontBoardServices            	       0x187c692ac -[FBSMainRunLoopSerialQueue _performNextFromRunLoopSource] + 24
35  CoreFoundation                	       0x1804284b8 __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__ + 24
36  CoreFoundation                	       0x180428400 __CFRunLoopDoSource0 + 168
37  CoreFoundation                	       0x180427be4 __CFRunLoopDoSources0 + 312
38  CoreFoundation                	       0x180422584 __CFRunLoopRun + 780
39  CoreFoundation                	       0x180421e3c CFRunLoopRunSpecific + 536
40  GraphicsServices              	       0x190f62d00 GSEventRunModal + 164
41  UIKitCore                     	       0x185bcec98 -[UIApplication _run] + 796
42  UIKitCore                     	       0x185bd3064 UIApplicationMain + 124
43  UIKitCore                     	       0x184f9ad6c 0x184d72000 + 2264428
44  Runner.debug.dylib            	       0x10225050c static UIApplicationDelegate.main() + 128
45  Runner.debug.dylib            	       0x10225047c static AppDelegate.$main() + 44
46  Runner.debug.dylib            	       0x102250dac __debug_main_executable_dylib_entry_point + 28
47  dyld_sim                      	       0x100c093d8 start_sim + 20
48  dyld                          	       0x100e3a274 start + 2840

Thread 0 Crashed::  Dispatch queue: com.apple.main-thread
0   libsystem_kernel.dylib        	       0x101721108 __pthread_kill + 8
1   libsystem_pthread.dylib       	       0x101317408 pthread_kill + 256
2   libsystem_c.dylib             	       0x180171ea8 abort + 100
3   libc++abi.dylib               	       0x1802b0144 abort_message + 128
4   libc++abi.dylib               	       0x18029fe4c demangling_terminate_handler() + 296
5   libobjc.A.dylib               	       0x18006f220 _objc_terminate() + 124
6   libc++abi.dylib               	       0x1802af570 std::__terminate(void (*)()) + 12
7   libc++abi.dylib               	       0x1802b2498 __cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*) + 32
8   libc++abi.dylib               	       0x1802b2478 __cxa_throw + 88
9   libobjc.A.dylib               	       0x180093904 objc_exception_throw + 384
10  CoreFoundation                	       0x1804c87d0 +[NSException raise:format:] + 124
11  FirebaseCore                  	       0x100b7d32c +[FIRApp configure] + 120 (FIRApp.m:110)
12  Runner.debug.dylib            	       0x10224f3b8 AppDelegate.application(_:didFinishLaunchingWithOptions:) + 80 (AppDelegate.swift:14)
13  Runner.debug.dylib            	       0x10224fbb4 @objc AppDelegate.application(_:didFinishLaunchingWithOptions:) + 220
14  UIKitCore                     	       0x185bcb5f4 -[UIApplication _handleDelegateCallbacksWithOptions:isSuspended:restoreState:] + 312
15  UIKitCore                     	       0x185bccab8 -[UIApplication _callInitializationDelegatesWithActions:forCanvas:payload:fromOriginatingProcess:] + 2916
16  UIKitCore                     	       0x185bd1e30 -[UIApplication _runWithMainScene:transitionContext:completion:] + 980
17  UIKitCore                     	       0x1851d4360 -[_UISceneLifecycleMultiplexer completeApplicationLaunchWithFBSScene:transitionContext:] + 104
18  UIKitCore                     	       0x18577962c _UIScenePerformActionsWithLifecycleActionMask + 96
19  UIKitCore                     	       0x1851d4c6c __101-[_UISceneLifecycleMultiplexer _evalTransitionToSettings:fromSettings:forceExit:withTransitionStore:]_block_invoke + 224
20  UIKitCore                     	       0x1851d47c8 -[_UISceneLifecycleMultiplexer _performBlock:withApplicationOfDeactivationReasons:fromReasons:] + 204
21  UIKitCore                     	       0x1851d4aa0 -[_UISceneLifecycleMultiplexer _evalTransitionToSettings:fromSettings:forceExit:withTransitionStore:] + 576
22  UIKitCore                     	       0x1851d44b8 -[_UISceneLifecycleMultiplexer uiScene:transitionedFromState:withTransitionContext:] + 240
23  UIKitCore                     	       0x1851df3e4 __186-[_UIWindowSceneFBSSceneTransitionContextDrivenLifecycleSettingsDiffAction _performActionsForUIScene:withUpdatedFBSScene:settingsDiff:fromSettings:transitionContext:lifecycleActionType:]_block_invoke + 140
24  UIKitCore                     	       0x1856688c8 +[BSAnimationSettings(UIKit) tryAnimatingWithSettings:fromCurrentState:actions:completion:] + 656
25  UIKitCore                     	       0x185791c2c _UISceneSettingsDiffActionPerformChangesWithTransitionContextAndCompletion + 196
26  UIKitCore                     	       0x1851df0f0 -[_UIWindowSceneFBSSceneTransitionContextDrivenLifecycleSettingsDiffAction _performActionsForUIScene:withUpdatedFBSScene:settingsDiff:fromSettings:transitionContext:lifecycleActionType:] + 288
27  UIKitCore                     	       0x18500f3bc __64-[UIScene scene:didUpdateWithDiff:transitionContext:completion:]_block_invoke.201 + 612
28  UIKitCore                     	       0x18500e160 -[UIScene _emitSceneSettingsUpdateResponseForCompletion:afterSceneUpdateWork:] + 200
29  UIKitCore                     	       0x18500f03c -[UIScene scene:didUpdateWithDiff:transitionContext:completion:] + 220
30  UIKitCore                     	       0x185bd0a38 -[UIApplication workspace:didCreateScene:withTransitionContext:completion:] + 772
31  UIKitCore                     	       0x1856922ec -[UIApplicationSceneClientAgent scene:didInitializeWithEvent:completion:] + 260
32  FrontBoardServices            	       0x187bfb03c __95-[FBSScene _callOutQueue_didCreateWithTransitionContext:alternativeCreationCallout:completion:]_block_invoke + 260
33  FrontBoardServices            	       0x187bfb3fc -[FBSScene _callOutQueue_coalesceClientSettingsUpdates:] + 60
34  FrontBoardServices            	       0x187bfaebc -[FBSScene _callOutQueue_didCreateWithTransitionContext:alternativeCreationCallout:completion:] + 408
35  FrontBoardServices            	       0x187c22318 __93-[FBSWorkspaceScenesClient _callOutQueue_sendDidCreateForScene:transitionContext:completion:]_block_invoke.156 + 232
36  FrontBoardServices            	       0x187c06c18 -[FBSWorkspace _calloutQueue_executeCalloutFromSource:withBlock:] + 160
37  FrontBoardServices            	       0x187c20944 -[FBSWorkspaceScenesClient _callOutQueue_sendDidCreateForScene:transitionContext:completion:] + 392
38  libdispatch.dylib             	       0x180197278 _dispatch_client_callout + 12
39  libdispatch.dylib             	       0x180181a30 _dispatch_block_invoke_direct + 376
40  FrontBoardServices            	       0x187c3dfb8 __FBSSERIALQUEUE_IS_CALLING_OUT_TO_A_BLOCK__ + 44
41  FrontBoardServices            	       0x187c3de94 -[FBSMainRunLoopSerialQueue _targetQueue_performNextIfPossible] + 196
42  FrontBoardServices            	       0x187c692ac -[FBSMainRunLoopSerialQueue _performNextFromRunLoopSource] + 24
43  CoreFoundation                	       0x1804284b8 __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__ + 24
44  CoreFoundation                	       0x180428400 __CFRunLoopDoSource0 + 168
45  CoreFoundation                	       0x180427be4 __CFRunLoopDoSources0 + 312
46  CoreFoundation                	       0x180422584 __CFRunLoopRun + 780
47  CoreFoundation                	       0x180421e3c CFRunLoopRunSpecific + 536
48  GraphicsServices              	       0x190f62d00 GSEventRunModal + 164
49  UIKitCore                     	       0x185bcec98 -[UIApplication _run] + 796
50  UIKitCore                     	       0x185bd3064 UIApplicationMain + 124
51  UIKitCore                     	       0x184f9ad6c 0x184d72000 + 2264428
52  Runner.debug.dylib            	       0x10225050c static UIApplicationDelegate.main() + 128
53  Runner.debug.dylib            	       0x10225047c static AppDelegate.$main() + 44
54  Runner.debug.dylib            	       0x102250dac __debug_main_executable_dylib_entry_point + 28
55  dyld_sim                      	       0x100c093d8 start_sim + 20
56  dyld                          	       0x100e3a274 start + 2840

Thread 1:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 2::  Dispatch queue: GULLoggingClientQueue
0   CoreFoundation                	       0x180534e88 -[__NSDictionaryM setObject:forKeyedSubscript:] + 596
1   GoogleUtilities               	       0x100dbd254 __GULOSLogBasic_block_invoke + 168 (GULLogger.m:163)
2   libdispatch.dylib             	       0x18017c788 _dispatch_call_block_and_release + 24
3   libdispatch.dylib             	       0x180197278 _dispatch_client_callout + 12
4   libdispatch.dylib             	       0x180185ad0 _dispatch_lane_serial_drain + 984
5   libdispatch.dylib             	       0x180186590 _dispatch_lane_invoke + 396
6   libdispatch.dylib             	       0x180191380 _dispatch_root_queue_drain_deferred_wlh + 288
7   libdispatch.dylib             	       0x1801909f4 _dispatch_workloop_worker_thread + 440
8   libsystem_pthread.dylib       	       0x101313b74 _pthread_wqthread + 284
9   libsystem_pthread.dylib       	       0x101312934 start_wqthread + 8

Thread 3:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 4::  Dispatch queue: com.apple.UIKit.KeyboardManagement
0   libsystem_kernel.dylib        	       0x10171af5c __ulock_wait + 8
1   libdispatch.dylib             	       0x18017e590 _dlock_wait + 52
2   libdispatch.dylib             	       0x18017e39c _dispatch_thread_event_wait_slow + 52
3   libdispatch.dylib             	       0x18018d220 __DISPATCH_WAIT_FOR_QUEUE__ + 392
4   libdispatch.dylib             	       0x18018cd1c _dispatch_sync_f_slow + 160
5   UIKitCore                     	       0x185a1ff28 __37-[_UIRemoteKeyboards startConnection]_block_invoke.385 + 116
6   CoreFoundation                	       0x1804cf210 __invoking___ + 144
7   CoreFoundation                	       0x1804cc3a8 -[NSInvocation invoke] + 276
8   Foundation                    	       0x181004b94 __NSXPCCONNECTION_IS_CALLING_OUT_TO_EXPORTED_OBJECT__ + 12
9   Foundation                    	       0x181004800 -[NSXPCConnection _decodeAndInvokeReplyBlockWithEvent:sequence:replyInfo:] + 484
10  Foundation                    	       0x1810086e8 __88-[NSXPCConnection _sendInvocation:orArguments:count:methodSignature:selector:withProxy:]_block_invoke_5 + 184
11  libxpc.dylib                  	       0x1800daab8 _xpc_connection_reply_callout + 60
12  libxpc.dylib                  	       0x1800ce370 _xpc_connection_call_reply_async + 92
13  libdispatch.dylib             	       0x1801972a0 _dispatch_client_callout3_a + 12
14  libdispatch.dylib             	       0x18019b9b4 _dispatch_mach_msg_async_reply_invoke + 508
15  libdispatch.dylib             	       0x180185828 _dispatch_lane_serial_drain + 304
16  libdispatch.dylib             	       0x180186590 _dispatch_lane_invoke + 396
17  libdispatch.dylib             	       0x180191380 _dispatch_root_queue_drain_deferred_wlh + 288
18  libdispatch.dylib             	       0x1801909f4 _dispatch_workloop_worker_thread + 440
19  libsystem_pthread.dylib       	       0x101313b74 _pthread_wqthread + 284
20  libsystem_pthread.dylib       	       0x101312934 start_wqthread + 8

Thread 5:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 6:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 7:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 8:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 9:: com.apple.uikit.eventfetch-thread
0   libsystem_kernel.dylib        	       0x101719390 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x10172a6e0 mach_msg2_internal + 76
2   libsystem_kernel.dylib        	       0x1017214f4 mach_msg_overwrite + 536
3   libsystem_kernel.dylib        	       0x1017196cc mach_msg + 20
4   CoreFoundation                	       0x180427d14 __CFRunLoopServiceMachPort + 156
5   CoreFoundation                	       0x1804226f4 __CFRunLoopRun + 1148
6   CoreFoundation                	       0x180421e3c CFRunLoopRunSpecific + 536
7   Foundation                    	       0x180f22ddc -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 208
8   Foundation                    	       0x180f22ffc -[NSRunLoop(NSRunLoop) runUntilDate:] + 60
9   UIKitCore                     	       0x185c7c724 -[UIEventFetcher threadMain] + 408
10  Foundation                    	       0x180f49b98 __NSThread__start__ + 716
11  libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
12  libsystem_pthread.dylib       	       0x101312940 thread_start + 8

Thread 10:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 11:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 12:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 13:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 14:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 15:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 16:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 17:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 18:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 19:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 20:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 21:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 22:
0   libsystem_pthread.dylib       	       0x10131292c start_wqthread + 0

Thread 23:: io.flutter.1.raster
0   libsystem_kernel.dylib        	       0x101719390 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x10172a6e0 mach_msg2_internal + 76
2   libsystem_kernel.dylib        	       0x1017214f4 mach_msg_overwrite + 536
3   libsystem_kernel.dylib        	       0x1017196cc mach_msg + 20
4   CoreFoundation                	       0x180427d14 __CFRunLoopServiceMachPort + 156
5   CoreFoundation                	       0x1804226f4 __CFRunLoopRun + 1148
6   CoreFoundation                	       0x180421e3c CFRunLoopRunSpecific + 536
7   Flutter                       	       0x1081de52c fml::MessageLoopDarwin::Run() + 88
8   Flutter                       	       0x1081d78f8 fml::MessageLoopImpl::DoRun() + 40
9   Flutter                       	       0x1081dd1c4 std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()() + 184
10  Flutter                       	       0x1081dce98 fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 36
11  libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
12  libsystem_pthread.dylib       	       0x101312940 thread_start + 8

Thread 24:: io.flutter.1.io
0   libsystem_kernel.dylib        	       0x101719390 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x10172a6e0 mach_msg2_internal + 76
2   libsystem_kernel.dylib        	       0x1017214f4 mach_msg_overwrite + 536
3   libsystem_kernel.dylib        	       0x1017196cc mach_msg + 20
4   CoreFoundation                	       0x180427d14 __CFRunLoopServiceMachPort + 156
5   CoreFoundation                	       0x1804226f4 __CFRunLoopRun + 1148
6   CoreFoundation                	       0x180421e3c CFRunLoopRunSpecific + 536
7   Flutter                       	       0x1081de52c fml::MessageLoopDarwin::Run() + 88
8   Flutter                       	       0x1081d78f8 fml::MessageLoopImpl::DoRun() + 40
9   Flutter                       	       0x1081dd1c4 std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()() + 184
10  Flutter                       	       0x1081dce98 fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 36
11  libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
12  libsystem_pthread.dylib       	       0x101312940 thread_start + 8

Thread 25:: io.flutter.1.profiler
0   libsystem_kernel.dylib        	       0x101719390 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x10172a6e0 mach_msg2_internal + 76
2   libsystem_kernel.dylib        	       0x1017214f4 mach_msg_overwrite + 536
3   libsystem_kernel.dylib        	       0x1017196cc mach_msg + 20
4   CoreFoundation                	       0x180427d14 __CFRunLoopServiceMachPort + 156
5   CoreFoundation                	       0x1804226f4 __CFRunLoopRun + 1148
6   CoreFoundation                	       0x180421e3c CFRunLoopRunSpecific + 536
7   Flutter                       	       0x1081de52c fml::MessageLoopDarwin::Run() + 88
8   Flutter                       	       0x1081d78f8 fml::MessageLoopImpl::DoRun() + 40
9   Flutter                       	       0x1081dd1c4 std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()() + 184
10  Flutter                       	       0x1081dce98 fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*) + 36
11  libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
12  libsystem_pthread.dylib       	       0x101312940 thread_start + 8

Thread 26:: io.worker.1
0   libsystem_kernel.dylib        	       0x10171c82c __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x101317c98 _pthread_cond_wait + 1192
2   Flutter                       	       0x1081b4a4c std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 24
3   Flutter                       	       0x1081d3f28 fml::ConcurrentMessageLoop::WorkerMain() + 140
4   Flutter                       	       0x1081d4854 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 212
5   libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x101312940 thread_start + 8

Thread 27:: io.worker.2
0   libsystem_kernel.dylib        	       0x10171c82c __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x101317c98 _pthread_cond_wait + 1192
2   Flutter                       	       0x1081b4a4c std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 24
3   Flutter                       	       0x1081d3f28 fml::ConcurrentMessageLoop::WorkerMain() + 140
4   Flutter                       	       0x1081d4854 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 212
5   libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x101312940 thread_start + 8

Thread 28:: io.worker.3
0   libsystem_kernel.dylib        	       0x10171c82c __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x101317c98 _pthread_cond_wait + 1192
2   Flutter                       	       0x1081b4a4c std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 24
3   Flutter                       	       0x1081d3f28 fml::ConcurrentMessageLoop::WorkerMain() + 140
4   Flutter                       	       0x1081d4854 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 212
5   libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x101312940 thread_start + 8

Thread 29:: io.worker.4
0   libsystem_kernel.dylib        	       0x10171c82c __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x101317c98 _pthread_cond_wait + 1192
2   Flutter                       	       0x1081b4a4c std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&) + 24
3   Flutter                       	       0x1081d3f28 fml::ConcurrentMessageLoop::WorkerMain() + 140
4   Flutter                       	       0x1081d4854 void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*) + 212
5   libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x101312940 thread_start + 8

Thread 30:: dart:io EventHandler
0   libsystem_kernel.dylib        	       0x10171ee84 kevent + 8
1   Flutter                       	       0x108621ef8 dart::bin::EventHandlerImplementation::EventHandlerEntry(unsigned long) + 300
2   Flutter                       	       0x10863e050 dart::bin::ThreadStart(void*) + 88
3   libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
4   libsystem_pthread.dylib       	       0x101312940 thread_start + 8

Thread 31:: Dart Profiler ThreadInterrupter
0   libsystem_kernel.dylib        	       0x10171c82c __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x101317cc4 _pthread_cond_wait + 1236
2   Flutter                       	       0x108689748 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x1087e66f4 dart::ThreadInterrupter::ThreadMain(unsigned long) + 328
4   Flutter                       	       0x10879e124 dart::ThreadStart(void*) + 204
5   libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x101312940 thread_start + 8

Thread 32:: Dart Profiler SampleBlockProcessor
0   libsystem_kernel.dylib        	       0x10171c82c __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x101317cc4 _pthread_cond_wait + 1236
2   Flutter                       	       0x108689748 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x1087a2bf0 dart::SampleBlockProcessor::ThreadMain(unsigned long) + 168
4   Flutter                       	       0x10879e124 dart::ThreadStart(void*) + 204
5   libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
6   libsystem_pthread.dylib       	       0x101312940 thread_start + 8

Thread 33:: DartWorker
0   libsystem_kernel.dylib        	       0x10171c82c __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x101317cc4 _pthread_cond_wait + 1236
2   Flutter                       	       0x108689748 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x1087e746c dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 508
4   Flutter                       	       0x1087e75c4 dart::ThreadPool::Worker::Main(unsigned long) + 116
5   Flutter                       	       0x10879e124 dart::ThreadStart(void*) + 204
6   libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
7   libsystem_pthread.dylib       	       0x101312940 thread_start + 8

Thread 34:: DartWorker
0   libsystem_kernel.dylib        	       0x10171c82c __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x101317cc4 _pthread_cond_wait + 1236
2   Flutter                       	       0x108689748 dart::ConditionVariable::WaitMicros(dart::Mutex*, long long) + 112
3   Flutter                       	       0x1087e746c dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 508
4   Flutter                       	       0x1087e75c4 dart::ThreadPool::Worker::Main(unsigned long) + 116
5   Flutter                       	       0x10879e124 dart::ThreadStart(void*) + 204
6   libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
7   libsystem_pthread.dylib       	       0x101312940 thread_start + 8

Thread 35:: DartWorker
0   libsystem_platform.dylib      	       0x1010aa0fc sys_icache_invalidate + 80
1   Flutter                       	       0x108758f40 dart::Code::FinalizeCode(dart::FlowGraphCompiler*, dart::compiler::Assembler*, dart::Code::PoolAttachment, bool, dart::CodeStatistics*) + 576
2   Flutter                       	       0x1087f8274 dart::CompileParsedFunctionHelper::FinalizeCompilation(dart::compiler::Assembler*, dart::FlowGraphCompiler*, dart::FlowGraph*) + 204
3   Flutter                       	       0x1087fa598 dart::LambdaCallable<dart::CompileParsedFunctionHelper::Compile()::$_0>::Call() + 36
4   Flutter                       	       0x1086ef6fc dart::IsolateGroup::RunWithStoppedMutatorsCallable(dart::Callable*, dart::Callable*, bool) + 352
5   Flutter                       	       0x1087f8dc4 dart::CompileParsedFunctionHelper::Compile() + 1824
6   Flutter                       	       0x1087f9334 dart::CompileFunctionHelper(dart::Function const&, bool, long) + 752
7   Flutter                       	       0x1087f9014 dart::Compiler::CompileFunction(dart::Thread*, dart::Function const&) + 200
8   Flutter                       	       0x108749694 dart::Function::EnsureHasCodeNoThrow() const + 88
9   Flutter                       	       0x1087495c4 dart::Function::EnsureHasCode() const + 84
10  Flutter                       	       0x1087b4f24 dart::TrySwitchInstanceCall(dart::Thread*, dart::StackFrame*, dart::Code const&, dart::Function const&, dart::ICData const&, dart::Function const&) + 228
11  Flutter                       	       0x1087b505c dart::PatchableCallHandler::DoICDataMissJIT(dart::ICData const&, dart::Object const&, dart::Function const&) + 120
12  Flutter                       	       0x1087b561c dart::PatchableCallHandler::ResolveSwitchAndReturn(dart::Object const&) + 288
13  Flutter                       	       0x1087ba38c dart::InlineCacheMissHandler(dart::Thread*, dart::Zone*, dart::GrowableArray<dart::Instance const*> const&, dart::ICData const&, dart::NativeArguments) + 220
14  Flutter                       	       0x1087b5844 dart::DRT_InlineCacheMissHandlerOneArg(dart::NativeArguments) + 388
15  ???                           	       0x10e8834ac ???
16  ???                           	       0x10e884a4c ???
17  ???                           	       0x110e2e7dc ???
18  ???                           	       0x110e2ca00 ???
19  ???                           	       0x110e2a43c ???
20  ???                           	       0x110e24d28 ???
21  ???                           	       0x110e2dbac ???
22  ???                           	       0x110e21428 ???
23  ???                           	       0x110e20f80 ???
24  ???                           	       0x110e20e68 ???
25  ???                           	       0x110e20d38 ???
26  ???                           	       0x110e1c2bc ???
27  ???                           	       0x10e883aa4 ???
28  Flutter                       	       0x1086ccb80 dart::DartEntry::InvokeFunction(dart::Function const&, dart::Array const&, dart::Array const&) + 236
29  Flutter                       	       0x1086ce518 dart::DartLibraryCalls::HandleMessage(long long, dart::Instance const&) + 304
30  Flutter                       	       0x1086ec7dc dart::IsolateMessageHandler::HandleMessage(std::_fl::unique_ptr<dart::Message, std::_fl::default_delete<dart::Message>>) + 736
31  Flutter                       	       0x108710d74 dart::MessageHandler::HandleMessages(dart::MonitorLocker*, bool, bool) + 308
32  Flutter                       	       0x1087111d8 dart::MessageHandler::TaskCallback() + 512
33  Flutter                       	       0x1087e737c dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*) + 268
34  Flutter                       	       0x1087e75c4 dart::ThreadPool::Worker::Main(unsigned long) + 116
35  Flutter                       	       0x10879e124 dart::ThreadStart(void*) + 204
36  libsystem_pthread.dylib       	       0x1013176f8 _pthread_start + 104
37  libsystem_pthread.dylib       	       0x101312940 thread_start + 8


Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x0000000000000000   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000000000000000
    x4: 0x00000001802b3e9b   x5: 0x000000016f3428a0   x6: 0x000000000000006e   x7: 0x0000000000000000
    x8: 0x0000000100ec4200   x9: 0x102458a17f5a681f  x10: 0x0000000000000051  x11: 0x000000000000000b
   x12: 0x000000000000000b  x13: 0x00000001806e7b3e  x14: 0x00000000000007fb  x15: 0x00000000c6611060
   x16: 0x0000000000000148  x17: 0x00000000c681085b  x18: 0x0000000000000000  x19: 0x0000000000000006
   x20: 0x0000000000000103  x21: 0x0000000100ec42e0  x22: 0x00000001f3c48000  x23: 0x0000000000000000
   x24: 0x0000000000000000  x25: 0x00000001f3e42000  x26: 0x00000001f3e0c000  x27: 0x000000002b870064
   x28: 0x0000000000000010   fp: 0x000000016f342810   lr: 0x0000000101317408
    sp: 0x000000016f3427f0   pc: 0x0000000101721108 cpsr: 0x40001000
   far: 0x0000000000000000  esr: 0x56000080  Address size fault

Binary Images:
       0x100e34000 -        0x100eb7fff dyld (*) <4ce86d18-f3fa-3d2a-a1b8-e7cd8a52fb0d> /usr/lib/dyld
       0x100ab8000 -        0x100abbfff com.example.classz (1.0.3) <735c9755-a007-32a9-84ec-99bb8a717224> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Runner
       0x100c08000 -        0x100c53fff dyld_sim (*) <6c88bc8b-f6b4-3f86-a9b1-60af6e8cc691> /Volumes/VOLUME/*/dyld_sim
       0x10224c000 -        0x102c03fff Runner.debug.dylib (*) <954e5de3-f54a-3d66-901a-c6b499aa4fd7> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Runner.debug.dylib
       0x100b48000 -        0x100b5bfff org.cocoapods.FBLPromises (2.4.0) <6ffefc41-cdf7-38b1-ac82-cd5da5fba2a1> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/FBLPromises.framework/FBLPromises
       0x100b7c000 -        0x100b8ffff org.cocoapods.FirebaseCore (11.15.0) <a7d36c1e-2b18-31bc-aba1-c6e5e1b7c3d7> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/FirebaseCore.framework/FirebaseCore
       0x100d3c000 -        0x100d63fff org.cocoapods.FirebaseCoreInternal (11.15.0) <5ef96399-4143-38a1-9c30-3c26dec6d63f> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/FirebaseCoreInternal.framework/FirebaseCoreInternal
       0x100cc0000 -        0x100cdbfff org.cocoapods.FirebaseInstallations (11.15.0) <b4b812d5-d608-32c7-b841-5f819f6ad74e> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/FirebaseInstallations.framework/FirebaseInstallations
       0x100ff0000 -        0x101037fff org.cocoapods.FirebaseMessaging (11.15.0) <018aed98-4f1e-3d41-b23d-59c02b3b62a4> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/FirebaseMessaging.framework/FirebaseMessaging
       0x100f5c000 -        0x100f8ffff org.cocoapods.GoogleDataTransport (10.1.0) <555f87ec-678f-35a4-93dc-0b664dafc6e7> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/GoogleDataTransport.framework/GoogleDataTransport
       0x100db4000 -        0x100dd7fff org.cocoapods.GoogleUtilities (8.1.0) <d35c7857-8dd5-37d6-a73b-763282bff2a9> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/GoogleUtilities.framework/GoogleUtilities
       0x100b10000 -        0x100b2bfff org.cocoapods.Mantle (2.2.0) <924daec4-2a61-3747-b3e7-9aa6106e9204> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/Mantle.framework/Mantle
       0x101184000 -        0x101203fff org.cocoapods.SDWebImage (5.21.1) <5d3f9e18-7589-3a0d-bb4c-ef51f31aef9d> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/SDWebImage.framework/SDWebImage
       0x100bd8000 -        0x100be3fff org.cocoapods.SDWebImageWebPCoder (0.14.6) <bda9da7d-4f45-393c-ae57-4fd53fac1ab0> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/SDWebImageWebPCoder.framework/SDWebImageWebPCoder
       0x1010ec000 -        0x101113fff org.cocoapods.Stripe (24.7.0) <69e8274b-b9e7-3f86-bc30-5e04c61f1f37> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/Stripe.framework/Stripe
       0x1013f0000 -        0x101483fff org.cocoapods.StripeApplePay (24.7.0) <f36638fb-39d7-35a9-9fad-fe770311f88b> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/StripeApplePay.framework/StripeApplePay
       0x101784000 -        0x101863fff org.cocoapods.StripeCore (24.7.0) <6ab6c45c-5eb5-378b-a5a9-055daea2343e> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/StripeCore.framework/StripeCore
       0x103558000 -        0x10387bfff org.cocoapods.StripeFinancialConnections (24.7.0) <64ef84d3-eba9-3290-88de-a0ddede2e638> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/StripeFinancialConnections.framework/StripeFinancialConnections
       0x10483c000 -        0x104ca3fff org.cocoapods.StripePaymentSheet (24.7.0) <a6a1dd9f-279c-3ca1-9ac2-6c3f143770f9> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/StripePaymentSheet.framework/StripePaymentSheet
       0x103d74000 -        0x10403ffff org.cocoapods.StripePayments (24.7.0) <db879431-b76a-382b-b1f6-648b1ab6dc1f> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/StripePayments.framework/StripePayments
       0x1019ac000 -        0x101a8bfff org.cocoapods.StripePaymentsUI (24.7.0) <978dc589-4ce6-37eb-854e-134ca9b2c8b0> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/StripePaymentsUI.framework/StripePaymentsUI
       0x101e0c000 -        0x101eebfff org.cocoapods.StripeUICore (24.7.0) <c9b5dc3b-6ddd-34df-97b9-35df4e4b9d5a> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/StripeUICore.framework/StripeUICore
       0x100d04000 -        0x100d0bfff org.cocoapods.app-settings (5.1.1) <ea2aad2e-e978-3de7-94ae-5a7e421db65d> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/app_settings.framework/app_settings
       0x101284000 -        0x1012a7fff org.cocoapods.flutter-image-compress-common (1.0.0) <2f8cf055-5805-3ae1-8235-21eaa79020bd> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/flutter_image_compress_common.framework/flutter_image_compress_common
       0x100bb0000 -        0x100bbffff org.cocoapods.flutter-local-notifications (0.0.1) <7f55823e-5c94-3e5b-b7f2-31e940357fed> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/flutter_local_notifications.framework/flutter_local_notifications
       0x100fcc000 -        0x100fd7fff org.cocoapods.fluttertoast (0.0.2) <dc65834e-49db-3493-b18c-9329ed6b6a4c> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/fluttertoast.framework/fluttertoast
       0x101080000 -        0x10108bfff org.cocoapods.geolocator-apple (1.2.0) <c99ff30e-ae8a-3d80-9c88-1cc0e06524d2> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/geolocator_apple.framework/geolocator_apple
       0x100e00000 -        0x100e13fff org.cocoapods.image-picker-ios (0.0.1) <4fa4e6ae-e665-347c-89c1-e30a2e8653e8> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/image_picker_ios.framework/image_picker_ios
       0x1010c4000 -        0x1010cbfff org.cocoapods.integration-test (0.0.1) <882bfd50-d6cc-3876-b52e-e31fe879215f> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/integration_test.framework/integration_test
       0x101560000 -        0x1015fffff org.cocoapods.libwebp (1.5.0) <0341be3a-eaaf-3540-8295-7442a3b87f16> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/libwebp.framework/libwebp
       0x101354000 -        0x10137bfff org.cocoapods.mobile-scanner (7.0.0) <41463d2a-5f83-3033-ab1d-40be4f11c888> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/mobile_scanner.framework/mobile_scanner
       0x100d24000 -        0x100d2bfff org.cocoapods.nanopb (3.30910.0) <299f0a13-6610-3f7a-b203-430066bb2e2f> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/nanopb.framework/nanopb
       0x1012e8000 -        0x1012f3fff org.cocoapods.path-provider-foundation (0.0.1) <cd4b5c19-786f-31b8-a118-70d705dab292> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/path_provider_foundation.framework/path_provider_foundation
       0x101154000 -        0x10115bfff org.cocoapods.share-plus (0.0.1) <3772bfd6-8649-322b-b588-38f6c5a2e16f> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/share_plus.framework/share_plus
       0x10164c000 -        0x10165ffff org.cocoapods.shared-preferences-foundation (0.0.1) <6d0d436e-161f-3c2b-92a7-ffbb7d9b46a6> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation
       0x1016d0000 -        0x1016effff org.cocoapods.sqflite-darwin (0.0.4) <9843ff8b-1e44-3c07-b7b8-b17c83100a2b> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/sqflite_darwin.framework/sqflite_darwin
       0x10442c000 -        0x104547fff org.cocoapods.stripe-ios (0.0.1) <48493791-032a-3895-b4df-cbdf94057c3b> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/stripe_ios.framework/stripe_ios
       0x101684000 -        0x101697fff org.cocoapods.url-launcher-ios (0.0.1) <e996ef3d-2f73-3484-b1c6-37429b617d74> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/url_launcher_ios.framework/url_launcher_ios
       0x108150000 -        0x10a2cbfff io.flutter.flutter (1.0) <4c4c4456-5555-3144-a115-e347b8e7daf1> /Users/<USER>/Library/Developer/CoreSimulator/Devices/76C11A2B-E7B7-495C-84BA-B0248EF2D472/data/Containers/Bundle/Application/9475D525-89E0-4C64-8DB8-4BEE6873E5E2/Runner.app/Frameworks/Flutter.framework/Flutter
       0x1010a4000 -        0x1010abfff libsystem_platform.dylib (*) <7e70a817-68f6-3ad4-98e8-d8e8c4c72f30> /usr/lib/system/libsystem_platform.dylib
       0x101718000 -        0x101753fff libsystem_kernel.dylib (*) <eb416efd-de68-327d-bf87-167e83297032> /usr/lib/system/libsystem_kernel.dylib
       0x101310000 -        0x10131ffff libsystem_pthread.dylib (*) <149fcf58-196b-369a-8c9e-c7d8dddc1408> /usr/lib/system/libsystem_pthread.dylib
       0x100af8000 -        0x100b03fff libobjc-trampolines.dylib (*) <22dfc621-7386-3de3-87b2-9ed27c8be84d> /Volumes/VOLUME/*/libobjc-trampolines.dylib
       0x1800fe000 -        0x18017a79b libsystem_c.dylib (*) <bfa3ae49-5373-3d1f-a47d-724abaf1ee45> /Volumes/VOLUME/*/libsystem_c.dylib
       0x18029b000 -        0x1802b6fff libc++abi.dylib (*) <3a16c8a1-792d-38cf-967a-72505dfb87e4> /Volumes/VOLUME/*/libc++abi.dylib
       0x180068000 -        0x1800a491f libobjc.A.dylib (*) <3286911c-7f92-3620-b8f7-18ac6061e9d9> /Volumes/VOLUME/*/libobjc.A.dylib
       0x180395000 -        0x1807acfff com.apple.CoreFoundation (6.9) <3d4aa1d5-03aa-3365-b767-944509b9bbfd> /Volumes/VOLUME/*/CoreFoundation.framework/CoreFoundation
       0x184d72000 -        0x186adb8df com.apple.UIKitCore (1.0) <d4c23b9a-c567-3e42-86ef-697aec976159> /Volumes/VOLUME/*/UIKitCore.framework/UIKitCore
       0x187bec000 -        0x187cab3df com.apple.FrontBoardServices (943.5.17) <fd5fcf2c-bbed-3086-ac10-6df96be66614> /Volumes/VOLUME/*/FrontBoardServices.framework/FrontBoardServices
       0x18017b000 -        0x1801bfb5f libdispatch.dylib (*) <7bee27fd-f519-330d-aebe-d6ace467df22> /Volumes/VOLUME/*/libdispatch.dylib
       0x190f60000 -        0x190f681ff com.apple.GraphicsServices (1.0) <eeb999f0-53c2-31ce-b203-3c78fb303dab> /Volumes/VOLUME/*/GraphicsServices.framework/GraphicsServices
               0x0 - 0xffffffffffffffff ??? (*) <00000000-0000-0000-0000-000000000000> ???
       0x18082c000 -        0x1813f18df com.apple.Foundation (6.9) <6ec60314-780a-318f-8bdb-5d173b13970e> /Volumes/VOLUME/*/Foundation.framework/Foundation
       0x1800bf000 -        0x1800faa9f libxpc.dylib (*) <c8560cb4-518f-37c4-915d-0554f320b3a0> /Volumes/VOLUME/*/libxpc.dylib

EOF

-----------
Full Report
-----------

{"app_name":"Runner","timestamp":"2025-07-22 00:49:20.00 +0700","app_version":"1.0.3","slice_uuid":"735c9755-a007-32a9-84ec-99bb8a717224","build_version":"1","platform":7,"bundleID":"com.example.classz","share_with_app_devs":0,"is_first_party":0,"bug_type":"309","os_version":"macOS 15.2 (24C101)","roots_installed":0,"name":"Runner","incident_id":"9BE38676-31CE-4E21-A091-96CD7C7CF187"}
{
  "uptime" : 59000,
  "procRole" : "Foreground",
  "version" : 2,
  "userID" : 501,
  "deployVersion" : 210,
  "modelCode" : "MacBookPro18,3",
  "coalitionID" : 9786,
  "osVersion" : {
    "train" : "macOS 15.2",
    "build" : "24C101",
    "releaseType" : "User"
  },
  "captureTime" : "2025-07-22 00:49:20.0317 +0700",
  "codeSigningMonitor" : 1,
  "incident" : "9BE38676-31CE-4E21-A091-96CD7C7CF187",
  "pid" : 18971,
  "translated" : false,
  "cpuType" : "ARM-64",
  "roots_installed" : 0,
  "bug_type" : "309",
  "procLaunch" : "2025-07-22 00:49:19.4250 +0700",
  "procStartAbsTime" : 1431094625501,
  "procExitAbsTime" : 1431109077583,
  "procName" : "Runner",
  "procPath" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Runner",
  "bundleInfo" : {"CFBundleShortVersionString":"1.0.3","CFBundleVersion":"1","CFBundleIdentifier":"com.example.classz"},
  "storeInfo" : {"deviceIdentifierForVendor":"635B140B-7209-52B7-80A2-B172AB0C9588","thirdParty":true},
  "parentProc" : "launchd_sim",
  "parentPid" : 47625,
  "coalitionName" : "com.apple.CoreSimulator.SimDevice.76C11A2B-E7B7-495C-84BA-B0248EF2D472",
  "crashReporterKey" : "E409E2B7-4646-0FF5-FCAE-580E35126684",
  "responsiblePid" : 2604,
  "responsibleProc" : "SimulatorTrampoline",
  "codeSigningID" : "com.example.classz",
  "codeSigningTeamID" : "",
  "codeSigningFlags" : 570425857,
  "codeSigningValidationCategory" : 10,
  "codeSigningTrustLevel" : 4294967295,
  "instructionByteStream" : {"beforePC":"4wAAVP17v6n9AwCR+OL\/l78DAJH9e8GowANf1sADX9YQKYDSARAA1A==","atPC":"4wAAVP17v6n9AwCR7uL\/l78DAJH9e8GowANf1sADX9ZwCoDSARAA1A=="},
  "bootSessionUUID" : "823421EA-6B36-4541-A77C-243C59B3D562",
  "wakeTime" : 3530,
  "sleepWakeUUID" : "2E9ECECF-4898-4F22-BED8-4A810B18B314",
  "sip" : "enabled",
  "exception" : {"codes":"0x0000000000000000, 0x0000000000000000","rawCodes":[0,0],"type":"EXC_CRASH","signal":"SIGABRT"},
  "termination" : {"flags":0,"code":6,"namespace":"SIGNAL","indicator":"Abort trap: 6","byProc":"Runner","byPid":18971},
  "extMods" : {"caller":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"system":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"targeted":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"warnings":0},
  "lastExceptionBacktrace" : [{"imageOffset":1259700,"symbol":"__exceptionPreprocess","symbolLocation":160,"imageIndex":46},{"imageOffset":178124,"symbol":"objc_exception_throw","symbolLocation":72,"imageIndex":45},{"imageOffset":1259472,"symbol":"-[NSException initWithCoder:]","symbolLocation":0,"imageIndex":46},{"imageOffset":4908,"sourceLine":115,"sourceFile":"FIRApp.m","symbol":"+[FIRApp configure]","imageIndex":5,"symbolLocation":120},{"imageOffset":13240,"sourceLine":17,"sourceFile":"AppDelegate.swift","symbol":"AppDelegate.application(_:didFinishLaunchingWithOptions:)","imageIndex":3,"symbolLocation":80},{"imageOffset":15284,"sourceFile":"\/<compiler-generated>","symbol":"@objc AppDelegate.application(_:didFinishLaunchingWithOptions:)","symbolLocation":220,"imageIndex":3},{"imageOffset":15046132,"symbol":"-[UIApplication _handleDelegateCallbacksWithOptions:isSuspended:restoreState:]","symbolLocation":312,"imageIndex":47},{"imageOffset":15051448,"symbol":"-[UIApplication _callInitializationDelegatesWithActions:forCanvas:payload:fromOriginatingProcess:]","symbolLocation":2916,"imageIndex":47},{"imageOffset":15072816,"symbol":"-[UIApplication _runWithMainScene:transitionContext:completion:]","symbolLocation":980,"imageIndex":47},{"imageOffset":4596576,"symbol":"-[_UISceneLifecycleMultiplexer completeApplicationLaunchWithFBSScene:transitionContext:]","symbolLocation":104,"imageIndex":47},{"imageOffset":10516012,"symbol":"_UIScenePerformActionsWithLifecycleActionMask","symbolLocation":96,"imageIndex":47},{"imageOffset":4598892,"symbol":"__101-[_UISceneLifecycleMultiplexer _evalTransitionToSettings:fromSettings:forceExit:withTransitionStore:]_block_invoke","symbolLocation":224,"imageIndex":47},{"imageOffset":4597704,"symbol":"-[_UISceneLifecycleMultiplexer _performBlock:withApplicationOfDeactivationReasons:fromReasons:]","symbolLocation":204,"imageIndex":47},{"imageOffset":4598432,"symbol":"-[_UISceneLifecycleMultiplexer _evalTransitionToSettings:fromSettings:forceExit:withTransitionStore:]","symbolLocation":576,"imageIndex":47},{"imageOffset":4596920,"symbol":"-[_UISceneLifecycleMultiplexer uiScene:transitionedFromState:withTransitionContext:]","symbolLocation":240,"imageIndex":47},{"imageOffset":4641764,"symbol":"__186-[_UIWindowSceneFBSSceneTransitionContextDrivenLifecycleSettingsDiffAction _performActionsForUIScene:withUpdatedFBSScene:settingsDiff:fromSettings:transitionContext:lifecycleActionType:]_block_invoke","symbolLocation":140,"imageIndex":47},{"imageOffset":9398472,"symbol":"+[BSAnimationSettings(UIKit) tryAnimatingWithSettings:fromCurrentState:actions:completion:]","symbolLocation":656,"imageIndex":47},{"imageOffset":10615852,"symbol":"_UISceneSettingsDiffActionPerformChangesWithTransitionContextAndCompletion","symbolLocation":196,"imageIndex":47},{"imageOffset":4641008,"symbol":"-[_UIWindowSceneFBSSceneTransitionContextDrivenLifecycleSettingsDiffAction _performActionsForUIScene:withUpdatedFBSScene:settingsDiff:fromSettings:transitionContext:lifecycleActionType:]","symbolLocation":288,"imageIndex":47},{"imageOffset":2741180,"symbol":"__64-[UIScene scene:didUpdateWithDiff:transitionContext:completion:]_block_invoke.201","symbolLocation":612,"imageIndex":47},{"imageOffset":2736480,"symbol":"-[UIScene _emitSceneSettingsUpdateResponseForCompletion:afterSceneUpdateWork:]","symbolLocation":200,"imageIndex":47},{"imageOffset":2740284,"symbol":"-[UIScene scene:didUpdateWithDiff:transitionContext:completion:]","symbolLocation":220,"imageIndex":47},{"imageOffset":15067704,"symbol":"-[UIApplication workspace:didCreateScene:withTransitionContext:completion:]","symbolLocation":772,"imageIndex":47},{"imageOffset":9569004,"symbol":"-[UIApplicationSceneClientAgent scene:didInitializeWithEvent:completion:]","symbolLocation":260,"imageIndex":47},{"imageOffset":61500,"symbol":"__95-[FBSScene _callOutQueue_didCreateWithTransitionContext:alternativeCreationCallout:completion:]_block_invoke","symbolLocation":260,"imageIndex":48},{"imageOffset":62460,"symbol":"-[FBSScene _callOutQueue_coalesceClientSettingsUpdates:]","symbolLocation":60,"imageIndex":48},{"imageOffset":61116,"symbol":"-[FBSScene _callOutQueue_didCreateWithTransitionContext:alternativeCreationCallout:completion:]","symbolLocation":408,"imageIndex":48},{"imageOffset":221976,"symbol":"__93-[FBSWorkspaceScenesClient _callOutQueue_sendDidCreateForScene:transitionContext:completion:]_block_invoke.156","symbolLocation":232,"imageIndex":48},{"imageOffset":109592,"symbol":"-[FBSWorkspace _calloutQueue_executeCalloutFromSource:withBlock:]","symbolLocation":160,"imageIndex":48},{"imageOffset":215364,"symbol":"-[FBSWorkspaceScenesClient _callOutQueue_sendDidCreateForScene:transitionContext:completion:]","symbolLocation":392,"imageIndex":48},{"imageOffset":115320,"symbol":"_dispatch_client_callout","symbolLocation":12,"imageIndex":49},{"imageOffset":27184,"symbol":"_dispatch_block_invoke_direct","symbolLocation":376,"imageIndex":49},{"imageOffset":335800,"symbol":"__FBSSERIALQUEUE_IS_CALLING_OUT_TO_A_BLOCK__","symbolLocation":44,"imageIndex":48},{"imageOffset":335508,"symbol":"-[FBSMainRunLoopSerialQueue _targetQueue_performNextIfPossible]","symbolLocation":196,"imageIndex":48},{"imageOffset":512684,"symbol":"-[FBSMainRunLoopSerialQueue _performNextFromRunLoopSource]","symbolLocation":24,"imageIndex":48},{"imageOffset":603320,"symbol":"__CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__","symbolLocation":24,"imageIndex":46},{"imageOffset":603136,"symbol":"__CFRunLoopDoSource0","symbolLocation":168,"imageIndex":46},{"imageOffset":601060,"symbol":"__CFRunLoopDoSources0","symbolLocation":312,"imageIndex":46},{"imageOffset":578948,"symbol":"__CFRunLoopRun","symbolLocation":780,"imageIndex":46},{"imageOffset":577084,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":46},{"imageOffset":11520,"symbol":"GSEventRunModal","symbolLocation":164,"imageIndex":50},{"imageOffset":15060120,"symbol":"-[UIApplication _run]","symbolLocation":796,"imageIndex":47},{"imageOffset":15077476,"symbol":"UIApplicationMain","symbolLocation":124,"imageIndex":47},{"imageOffset":2264428,"imageIndex":47},{"imageOffset":17676,"sourceFile":"\/<compiler-generated>","symbol":"static UIApplicationDelegate.main()","symbolLocation":128,"imageIndex":3},{"imageOffset":17532,"sourceFile":"\/<compiler-generated>","symbol":"static AppDelegate.$main()","symbolLocation":44,"imageIndex":3},{"imageOffset":19884,"sourceFile":"\/<compiler-generated>","symbol":"__debug_main_executable_dylib_entry_point","symbolLocation":28,"imageIndex":3},{"imageOffset":5080,"symbol":"start_sim","symbolLocation":20,"imageIndex":2},{"imageOffset":25204,"symbol":"start","symbolLocation":2840,"imageIndex":0}],
  "faultingThread" : 0,
  "threads" : [{"triggered":true,"id":1452496,"threadState":{"x":[{"value":0},{"value":0},{"value":0},{"value":0},{"value":6445285019},{"value":6160656544},{"value":110},{"value":0},{"value":4310450688,"symbolLocation":0,"symbol":"_main_thread"},{"value":1163152054418040863},{"value":81},{"value":11},{"value":11},{"value":6449691454},{"value":2043},{"value":3328249952},{"value":328},{"value":3330345051},{"value":0},{"value":6},{"value":259},{"value":4310450912,"symbolLocation":224,"symbol":"_main_thread"},{"value":8384708608,"symbolLocation":112,"symbol":"objc_debug_taggedpointer_classes"},{"value":0},{"value":0},{"value":8386781184,"symbolLocation":24,"symbol":"OBJC_METACLASS_$__UIForceLevelClassifier"},{"value":8386560000,"symbolLocation":0,"symbol":"OBJC_IVAR_$_UIPreviewItemController._previewIndicatorSnapshotView"},{"value":730267748},{"value":16}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4314985480},"cpsr":{"value":1073745920},"fp":{"value":6160656400},"sp":{"value":6160656368},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319219976,"matchesCrashFrame":1},"far":{"value":0}},"queue":"com.apple.main-thread","frames":[{"imageOffset":37128,"symbol":"__pthread_kill","symbolLocation":8,"imageIndex":40},{"imageOffset":29704,"symbol":"pthread_kill","symbolLocation":256,"imageIndex":41},{"imageOffset":474792,"symbol":"abort","symbolLocation":100,"imageIndex":43},{"imageOffset":86340,"symbol":"abort_message","symbolLocation":128,"imageIndex":44},{"imageOffset":20044,"symbol":"demangling_terminate_handler()","symbolLocation":296,"imageIndex":44},{"imageOffset":29216,"symbol":"_objc_terminate()","symbolLocation":124,"imageIndex":45},{"imageOffset":83312,"symbol":"std::__terminate(void (*)())","symbolLocation":12,"imageIndex":44},{"imageOffset":95384,"symbol":"__cxxabiv1::failed_throw(__cxxabiv1::__cxa_exception*)","symbolLocation":32,"imageIndex":44},{"imageOffset":95352,"symbol":"__cxa_throw","symbolLocation":88,"imageIndex":44},{"imageOffset":178436,"symbol":"objc_exception_throw","symbolLocation":384,"imageIndex":45},{"imageOffset":1259472,"symbol":"+[NSException raise:format:]","symbolLocation":124,"imageIndex":46},{"imageOffset":4908,"sourceLine":110,"sourceFile":"FIRApp.m","symbol":"+[FIRApp configure]","imageIndex":5,"symbolLocation":120},{"imageOffset":13240,"sourceLine":14,"sourceFile":"AppDelegate.swift","symbol":"AppDelegate.application(_:didFinishLaunchingWithOptions:)","imageIndex":3,"symbolLocation":80},{"imageOffset":15284,"sourceFile":"\/<compiler-generated>","symbol":"@objc AppDelegate.application(_:didFinishLaunchingWithOptions:)","symbolLocation":220,"imageIndex":3},{"imageOffset":15046132,"symbol":"-[UIApplication _handleDelegateCallbacksWithOptions:isSuspended:restoreState:]","symbolLocation":312,"imageIndex":47},{"imageOffset":15051448,"symbol":"-[UIApplication _callInitializationDelegatesWithActions:forCanvas:payload:fromOriginatingProcess:]","symbolLocation":2916,"imageIndex":47},{"imageOffset":15072816,"symbol":"-[UIApplication _runWithMainScene:transitionContext:completion:]","symbolLocation":980,"imageIndex":47},{"imageOffset":4596576,"symbol":"-[_UISceneLifecycleMultiplexer completeApplicationLaunchWithFBSScene:transitionContext:]","symbolLocation":104,"imageIndex":47},{"imageOffset":10516012,"symbol":"_UIScenePerformActionsWithLifecycleActionMask","symbolLocation":96,"imageIndex":47},{"imageOffset":4598892,"symbol":"__101-[_UISceneLifecycleMultiplexer _evalTransitionToSettings:fromSettings:forceExit:withTransitionStore:]_block_invoke","symbolLocation":224,"imageIndex":47},{"imageOffset":4597704,"symbol":"-[_UISceneLifecycleMultiplexer _performBlock:withApplicationOfDeactivationReasons:fromReasons:]","symbolLocation":204,"imageIndex":47},{"imageOffset":4598432,"symbol":"-[_UISceneLifecycleMultiplexer _evalTransitionToSettings:fromSettings:forceExit:withTransitionStore:]","symbolLocation":576,"imageIndex":47},{"imageOffset":4596920,"symbol":"-[_UISceneLifecycleMultiplexer uiScene:transitionedFromState:withTransitionContext:]","symbolLocation":240,"imageIndex":47},{"imageOffset":4641764,"symbol":"__186-[_UIWindowSceneFBSSceneTransitionContextDrivenLifecycleSettingsDiffAction _performActionsForUIScene:withUpdatedFBSScene:settingsDiff:fromSettings:transitionContext:lifecycleActionType:]_block_invoke","symbolLocation":140,"imageIndex":47},{"imageOffset":9398472,"symbol":"+[BSAnimationSettings(UIKit) tryAnimatingWithSettings:fromCurrentState:actions:completion:]","symbolLocation":656,"imageIndex":47},{"imageOffset":10615852,"symbol":"_UISceneSettingsDiffActionPerformChangesWithTransitionContextAndCompletion","symbolLocation":196,"imageIndex":47},{"imageOffset":4641008,"symbol":"-[_UIWindowSceneFBSSceneTransitionContextDrivenLifecycleSettingsDiffAction _performActionsForUIScene:withUpdatedFBSScene:settingsDiff:fromSettings:transitionContext:lifecycleActionType:]","symbolLocation":288,"imageIndex":47},{"imageOffset":2741180,"symbol":"__64-[UIScene scene:didUpdateWithDiff:transitionContext:completion:]_block_invoke.201","symbolLocation":612,"imageIndex":47},{"imageOffset":2736480,"symbol":"-[UIScene _emitSceneSettingsUpdateResponseForCompletion:afterSceneUpdateWork:]","symbolLocation":200,"imageIndex":47},{"imageOffset":2740284,"symbol":"-[UIScene scene:didUpdateWithDiff:transitionContext:completion:]","symbolLocation":220,"imageIndex":47},{"imageOffset":15067704,"symbol":"-[UIApplication workspace:didCreateScene:withTransitionContext:completion:]","symbolLocation":772,"imageIndex":47},{"imageOffset":9569004,"symbol":"-[UIApplicationSceneClientAgent scene:didInitializeWithEvent:completion:]","symbolLocation":260,"imageIndex":47},{"imageOffset":61500,"symbol":"__95-[FBSScene _callOutQueue_didCreateWithTransitionContext:alternativeCreationCallout:completion:]_block_invoke","symbolLocation":260,"imageIndex":48},{"imageOffset":62460,"symbol":"-[FBSScene _callOutQueue_coalesceClientSettingsUpdates:]","symbolLocation":60,"imageIndex":48},{"imageOffset":61116,"symbol":"-[FBSScene _callOutQueue_didCreateWithTransitionContext:alternativeCreationCallout:completion:]","symbolLocation":408,"imageIndex":48},{"imageOffset":221976,"symbol":"__93-[FBSWorkspaceScenesClient _callOutQueue_sendDidCreateForScene:transitionContext:completion:]_block_invoke.156","symbolLocation":232,"imageIndex":48},{"imageOffset":109592,"symbol":"-[FBSWorkspace _calloutQueue_executeCalloutFromSource:withBlock:]","symbolLocation":160,"imageIndex":48},{"imageOffset":215364,"symbol":"-[FBSWorkspaceScenesClient _callOutQueue_sendDidCreateForScene:transitionContext:completion:]","symbolLocation":392,"imageIndex":48},{"imageOffset":115320,"symbol":"_dispatch_client_callout","symbolLocation":12,"imageIndex":49},{"imageOffset":27184,"symbol":"_dispatch_block_invoke_direct","symbolLocation":376,"imageIndex":49},{"imageOffset":335800,"symbol":"__FBSSERIALQUEUE_IS_CALLING_OUT_TO_A_BLOCK__","symbolLocation":44,"imageIndex":48},{"imageOffset":335508,"symbol":"-[FBSMainRunLoopSerialQueue _targetQueue_performNextIfPossible]","symbolLocation":196,"imageIndex":48},{"imageOffset":512684,"symbol":"-[FBSMainRunLoopSerialQueue _performNextFromRunLoopSource]","symbolLocation":24,"imageIndex":48},{"imageOffset":603320,"symbol":"__CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__","symbolLocation":24,"imageIndex":46},{"imageOffset":603136,"symbol":"__CFRunLoopDoSource0","symbolLocation":168,"imageIndex":46},{"imageOffset":601060,"symbol":"__CFRunLoopDoSources0","symbolLocation":312,"imageIndex":46},{"imageOffset":578948,"symbol":"__CFRunLoopRun","symbolLocation":780,"imageIndex":46},{"imageOffset":577084,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":46},{"imageOffset":11520,"symbol":"GSEventRunModal","symbolLocation":164,"imageIndex":50},{"imageOffset":15060120,"symbol":"-[UIApplication _run]","symbolLocation":796,"imageIndex":47},{"imageOffset":15077476,"symbol":"UIApplicationMain","symbolLocation":124,"imageIndex":47},{"imageOffset":2264428,"imageIndex":47},{"imageOffset":17676,"sourceFile":"\/<compiler-generated>","symbol":"static UIApplicationDelegate.main()","symbolLocation":128,"imageIndex":3},{"imageOffset":17532,"sourceFile":"\/<compiler-generated>","symbol":"static AppDelegate.$main()","symbolLocation":44,"imageIndex":3},{"imageOffset":19884,"sourceFile":"\/<compiler-generated>","symbol":"__debug_main_executable_dylib_entry_point","symbolLocation":28,"imageIndex":3},{"imageOffset":5080,"symbol":"start_sim","symbolLocation":20,"imageIndex":2},{"imageOffset":25204,"symbol":"start","symbolLocation":2840,"imageIndex":0}]},{"id":1452555,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6161231872},{"value":7683},{"value":6160695296},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6161231872},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452556,"threadState":{"x":[{"value":0},{"value":8520563885,"objc-selector":"hash"},{"value":48},{"value":2252077784904504},{"value":6},{"value":0},{"value":0},{"value":0},{"value":1660150865889433892},{"value":11479944528347057004},{"value":4307077789},{"value":4307077789},{"value":6448620844,"symbolLocation":0,"symbol":"__CFMacRomanCharToUnicharTable"},{"value":101},{"value":6448620844,"symbolLocation":0,"symbol":"__CFMacRomanCharToUnicharTable"},{"value":1789887808},{"value":98},{"value":6446614592,"symbolLocation":0,"symbol":"-[__NSCFString hash]"},{"value":0},{"value":4307096304},{"value":3},{"value":0},{"value":0},{"value":105553118536808},{"value":0},{"value":1},{"value":3},{"value":105553128916576},{"value":8172544704,"symbolLocation":0,"symbol":"___NSDictionaryM_DeletedMarker"}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6447910356},"cpsr":{"value":2147487744},"fp":{"value":6161803200},"sp":{"value":6161803072},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":6447910536},"far":{"value":0}},"queue":"GULLoggingClientQueue","frames":[{"imageOffset":1703560,"symbol":"-[__NSDictionaryM setObject:forKeyedSubscript:]","symbolLocation":596,"imageIndex":46},{"imageOffset":37460,"sourceLine":163,"sourceFile":"GULLogger.m","symbol":"__GULOSLogBasic_block_invoke","imageIndex":10,"symbolLocation":168},{"imageOffset":6024,"symbol":"_dispatch_call_block_and_release","symbolLocation":24,"imageIndex":49},{"imageOffset":115320,"symbol":"_dispatch_client_callout","symbolLocation":12,"imageIndex":49},{"imageOffset":43728,"symbol":"_dispatch_lane_serial_drain","symbolLocation":984,"imageIndex":49},{"imageOffset":46480,"symbol":"_dispatch_lane_invoke","symbolLocation":396,"imageIndex":49},{"imageOffset":91008,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":288,"imageIndex":49},{"imageOffset":88564,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":440,"imageIndex":49},{"imageOffset":15220,"symbol":"_pthread_wqthread","symbolLocation":284,"imageIndex":41},{"imageOffset":10548,"symbol":"start_wqthread","symbolLocation":8,"imageIndex":41}]},{"id":1452557,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6162378752},{"value":6659},{"value":6161842176},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6162378752},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452561,"threadState":{"x":[{"value":18446744073709551612},{"value":0},{"value":4294967295},{"value":0},{"value":8384698624,"symbolLocation":0,"symbol":"_dispatch_main_q"},{"value":18},{"value":0},{"value":0},{"value":0},{"value":6},{"value":105553140394438},{"value":105553140394448},{"value":8384698728,"symbolLocation":104,"symbol":"_dispatch_main_q"},{"value":8384698672,"symbolLocation":48,"symbol":"_dispatch_main_q"},{"value":9005068950962176},{"value":8384895032,"symbolLocation":0,"symbol":"OBJC_CLASS_$_NSMethodSignature"},{"value":515},{"value":288230384538732385,"symbolLocation":288230376151711745,"symbol":"OBJC_CLASS_$__UIKeyboardChangedInformation"},{"value":0},{"value":4294967295},{"value":1},{"value":0},{"value":6162947808},{"value":8384698624,"symbolLocation":0,"symbol":"_dispatch_main_q"},{"value":18},{"value":2148468479},{"value":4351},{"value":8447},{"value":105553140393856}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6444017040},"cpsr":{"value":1073745920},"fp":{"value":6162947616},"sp":{"value":6162947584},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319194972},"far":{"value":0}},"queue":"com.apple.UIKit.KeyboardManagement","frames":[{"imageOffset":12124,"symbol":"__ulock_wait","symbolLocation":8,"imageIndex":40},{"imageOffset":13712,"symbol":"_dlock_wait","symbolLocation":52,"imageIndex":49},{"imageOffset":13212,"symbol":"_dispatch_thread_event_wait_slow","symbolLocation":52,"imageIndex":49},{"imageOffset":74272,"symbol":"__DISPATCH_WAIT_FOR_QUEUE__","symbolLocation":392,"imageIndex":49},{"imageOffset":72988,"symbol":"_dispatch_sync_f_slow","symbolLocation":160,"imageIndex":49},{"imageOffset":13295400,"symbol":"__37-[_UIRemoteKeyboards startConnection]_block_invoke.385","symbolLocation":116,"imageIndex":47},{"imageOffset":1286672,"symbol":"__invoking___","symbolLocation":144,"imageIndex":46},{"imageOffset":1274792,"symbol":"-[NSInvocation invoke]","symbolLocation":276,"imageIndex":46},{"imageOffset":8227732,"symbol":"__NSXPCCONNECTION_IS_CALLING_OUT_TO_EXPORTED_OBJECT__","symbolLocation":12,"imageIndex":52},{"imageOffset":8226816,"symbol":"-[NSXPCConnection _decodeAndInvokeReplyBlockWithEvent:sequence:replyInfo:]","symbolLocation":484,"imageIndex":52},{"imageOffset":8242920,"symbol":"__88-[NSXPCConnection _sendInvocation:orArguments:count:methodSignature:selector:withProxy:]_block_invoke_5","symbolLocation":184,"imageIndex":52},{"imageOffset":113336,"symbol":"_xpc_connection_reply_callout","symbolLocation":60,"imageIndex":53},{"imageOffset":62320,"symbol":"_xpc_connection_call_reply_async","symbolLocation":92,"imageIndex":53},{"imageOffset":115360,"symbol":"_dispatch_client_callout3_a","symbolLocation":12,"imageIndex":49},{"imageOffset":133556,"symbol":"_dispatch_mach_msg_async_reply_invoke","symbolLocation":508,"imageIndex":49},{"imageOffset":43048,"symbol":"_dispatch_lane_serial_drain","symbolLocation":304,"imageIndex":49},{"imageOffset":46480,"symbol":"_dispatch_lane_invoke","symbolLocation":396,"imageIndex":49},{"imageOffset":91008,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":288,"imageIndex":49},{"imageOffset":88564,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":440,"imageIndex":49},{"imageOffset":15220,"symbol":"_pthread_wqthread","symbolLocation":284,"imageIndex":41},{"imageOffset":10548,"symbol":"start_wqthread","symbolLocation":8,"imageIndex":41}]},{"id":1452562,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6163525632},{"value":11523},{"value":6162989056},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6163525632},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452564,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6164099072},{"value":14595},{"value":6163562496},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6164099072},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452565,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6164672512},{"value":18435},{"value":6164135936},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6164672512},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452566,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6165245952},{"value":18179},{"value":6164709376},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6165245952},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452568,"name":"com.apple.uikit.eventfetch-thread","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":59386512801792},{"value":4294967295},{"value":59386512801792},{"value":2},{"value":4294967295},{"value":18446744073709550527},{"value":2},{"value":4294967295},{"value":0},{"value":0},{"value":13827},{"value":3072},{"value":2426564655},{"value":18446744073709551569},{"value":2428661755},{"value":0},{"value":4294967295},{"value":2},{"value":59386512801792},{"value":4294967295},{"value":59386512801792},{"value":6165814664},{"value":8589934592},{"value":21592279046},{"value":21592279046},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4319258336},"cpsr":{"value":4096},"fp":{"value":6165814512},"sp":{"value":6165814432},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319187856},"far":{"value":0}},"frames":[{"imageOffset":5008,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":40},{"imageOffset":75488,"symbol":"mach_msg2_internal","symbolLocation":76,"imageIndex":40},{"imageOffset":38132,"symbol":"mach_msg_overwrite","symbolLocation":536,"imageIndex":40},{"imageOffset":5836,"symbol":"mach_msg","symbolLocation":20,"imageIndex":40},{"imageOffset":601364,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":156,"imageIndex":46},{"imageOffset":579316,"symbol":"__CFRunLoopRun","symbolLocation":1148,"imageIndex":46},{"imageOffset":577084,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":46},{"imageOffset":7302620,"symbol":"-[NSRunLoop(NSRunLoop) runMode:beforeDate:]","symbolLocation":208,"imageIndex":52},{"imageOffset":7303164,"symbol":"-[NSRunLoop(NSRunLoop) runUntilDate:]","symbolLocation":60,"imageIndex":52},{"imageOffset":15771428,"symbol":"-[UIEventFetcher threadMain]","symbolLocation":408,"imageIndex":47},{"imageOffset":7461784,"symbol":"__NSThread__start__","symbolLocation":716,"imageIndex":52},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]},{"id":1452571,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6166392832},{"value":19203},{"value":6165856256},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6166392832},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452572,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6166966272},{"value":18947},{"value":6166429696},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6166966272},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452573,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6167539712},{"value":18691},{"value":6167003136},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6167539712},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452574,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6168113152},{"value":21763},{"value":6167576576},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6168113152},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452575,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6168686592},{"value":22019},{"value":6168150016},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6168686592},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452576,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6169260032},{"value":32515},{"value":6168723456},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6169260032},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452577,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6169833472},{"value":32259},{"value":6169296896},{"value":0},{"value":409602},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6169833472},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452578,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6170406912},{"value":32003},{"value":6169870336},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6170406912},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452579,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6170980352},{"value":22275},{"value":6170443776},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6170980352},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452580,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6171553792},{"value":22531},{"value":6171017216},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6171553792},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452581,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6172127232},{"value":31747},{"value":6171590656},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6172127232},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452582,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6172700672},{"value":22787},{"value":6172164096},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6172700672},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452583,"frames":[{"imageOffset":10540,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":41}],"threadState":{"x":[{"value":6173274112},{"value":31235},{"value":6172737536},{"value":0},{"value":409602},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6173274112},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4314966316},"far":{"value":0}}},{"id":1452584,"name":"io.flutter.1.raster","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":122058675585024},{"value":0},{"value":122058675585024},{"value":2},{"value":4294967295},{"value":18446744073709550527},{"value":2},{"value":0},{"value":0},{"value":0},{"value":28419},{"value":3072},{"value":0},{"value":18446744073709551569},{"value":2},{"value":0},{"value":4294967295},{"value":2},{"value":122058675585024},{"value":0},{"value":122058675585024},{"value":6175416328},{"value":8589934592},{"value":21592279046},{"value":21592279046},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4319258336},"cpsr":{"value":4096},"fp":{"value":6175416176},"sp":{"value":6175416096},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319187856},"far":{"value":0}},"frames":[{"imageOffset":5008,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":40},{"imageOffset":75488,"symbol":"mach_msg2_internal","symbolLocation":76,"imageIndex":40},{"imageOffset":38132,"symbol":"mach_msg_overwrite","symbolLocation":536,"imageIndex":40},{"imageOffset":5836,"symbol":"mach_msg","symbolLocation":20,"imageIndex":40},{"imageOffset":601364,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":156,"imageIndex":46},{"imageOffset":579316,"symbol":"__CFRunLoopRun","symbolLocation":1148,"imageIndex":46},{"imageOffset":577084,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":46},{"imageOffset":582956,"symbol":"fml::MessageLoopDarwin::Run()","symbolLocation":88,"imageIndex":38},{"imageOffset":555256,"symbol":"fml::MessageLoopImpl::DoRun()","symbolLocation":40,"imageIndex":38},{"imageOffset":577988,"symbol":"std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()()","symbolLocation":184,"imageIndex":38},{"imageOffset":577176,"symbol":"fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*)","symbolLocation":36,"imageIndex":38},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]},{"id":1452585,"name":"io.flutter.1.io","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":114362094190592},{"value":0},{"value":114362094190592},{"value":2},{"value":4294967295},{"value":18446744073709550527},{"value":2},{"value":0},{"value":0},{"value":0},{"value":26627},{"value":3072},{"value":0},{"value":18446744073709551569},{"value":2},{"value":0},{"value":4294967295},{"value":2},{"value":114362094190592},{"value":0},{"value":114362094190592},{"value":6177562632},{"value":8589934592},{"value":21592279046},{"value":21592279046},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4319258336},"cpsr":{"value":4096},"fp":{"value":6177562480},"sp":{"value":6177562400},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319187856},"far":{"value":0}},"frames":[{"imageOffset":5008,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":40},{"imageOffset":75488,"symbol":"mach_msg2_internal","symbolLocation":76,"imageIndex":40},{"imageOffset":38132,"symbol":"mach_msg_overwrite","symbolLocation":536,"imageIndex":40},{"imageOffset":5836,"symbol":"mach_msg","symbolLocation":20,"imageIndex":40},{"imageOffset":601364,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":156,"imageIndex":46},{"imageOffset":579316,"symbol":"__CFRunLoopRun","symbolLocation":1148,"imageIndex":46},{"imageOffset":577084,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":46},{"imageOffset":582956,"symbol":"fml::MessageLoopDarwin::Run()","symbolLocation":88,"imageIndex":38},{"imageOffset":555256,"symbol":"fml::MessageLoopImpl::DoRun()","symbolLocation":40,"imageIndex":38},{"imageOffset":577988,"symbol":"std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()()","symbolLocation":184,"imageIndex":38},{"imageOffset":577176,"symbol":"fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*)","symbolLocation":36,"imageIndex":38},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]},{"id":1452586,"name":"io.flutter.1.profiler","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":144048908140544},{"value":0},{"value":144048908140544},{"value":2},{"value":4294967295},{"value":18446744073709550527},{"value":2},{"value":0},{"value":0},{"value":0},{"value":33539},{"value":3072},{"value":0},{"value":18446744073709551569},{"value":2},{"value":0},{"value":4294967295},{"value":2},{"value":144048908140544},{"value":0},{"value":144048908140544},{"value":6179708936},{"value":8589934592},{"value":21592279046},{"value":21592279046},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4319258336},"cpsr":{"value":4096},"fp":{"value":6179708784},"sp":{"value":6179708704},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319187856},"far":{"value":0}},"frames":[{"imageOffset":5008,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":40},{"imageOffset":75488,"symbol":"mach_msg2_internal","symbolLocation":76,"imageIndex":40},{"imageOffset":38132,"symbol":"mach_msg_overwrite","symbolLocation":536,"imageIndex":40},{"imageOffset":5836,"symbol":"mach_msg","symbolLocation":20,"imageIndex":40},{"imageOffset":601364,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":156,"imageIndex":46},{"imageOffset":579316,"symbol":"__CFRunLoopRun","symbolLocation":1148,"imageIndex":46},{"imageOffset":577084,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":46},{"imageOffset":582956,"symbol":"fml::MessageLoopDarwin::Run()","symbolLocation":88,"imageIndex":38},{"imageOffset":555256,"symbol":"fml::MessageLoopImpl::DoRun()","symbolLocation":40,"imageIndex":38},{"imageOffset":577988,"symbol":"std::_fl::__function::__func<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0, std::_fl::allocator<fml::Thread::Thread(std::_fl::function<void (fml::Thread::ThreadConfig const&)> const&, fml::Thread::ThreadConfig const&)::$_0>, void ()>::operator()()","symbolLocation":184,"imageIndex":38},{"imageOffset":577176,"symbol":"fml::ThreadHandle::ThreadHandle(std::_fl::function<void ()>&&)::$_0::__invoke(void*)","symbolLocation":36,"imageIndex":38},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]},{"id":1452587,"name":"io.worker.1","threadState":{"x":[{"value":4},{"value":0},{"value":1024},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6180285912},{"value":0},{"value":768},{"value":3298534884098},{"value":3298534884098},{"value":768},{"value":0},{"value":3298534884096},{"value":305},{"value":54},{"value":0},{"value":4383083768},{"value":4383083832},{"value":6180286688},{"value":0},{"value":0},{"value":1024},{"value":1024},{"value":1792},{"value":1},{"value":105553118480064}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4314987672},"cpsr":{"value":1610616832},"fp":{"value":6180286032},"sp":{"value":6180285888},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319201324},"far":{"value":0}},"frames":[{"imageOffset":18476,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":40},{"imageOffset":31896,"symbol":"_pthread_cond_wait","symbolLocation":1192,"imageIndex":41},{"imageOffset":412236,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":24,"imageIndex":38},{"imageOffset":540456,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":140,"imageIndex":38},{"imageOffset":542804,"symbol":"void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":212,"imageIndex":38},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]},{"id":1452588,"name":"io.worker.2","threadState":{"x":[{"value":4},{"value":0},{"value":1024},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6180859352},{"value":0},{"value":768},{"value":3298534884098},{"value":3298534884098},{"value":768},{"value":0},{"value":3298534884096},{"value":305},{"value":469},{"value":0},{"value":4383083768},{"value":4383083832},{"value":6180860128},{"value":0},{"value":0},{"value":1024},{"value":1024},{"value":1536},{"value":1},{"value":105553118493344}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4314987672},"cpsr":{"value":1610616832},"fp":{"value":6180859472},"sp":{"value":6180859328},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319201324},"far":{"value":0}},"frames":[{"imageOffset":18476,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":40},{"imageOffset":31896,"symbol":"_pthread_cond_wait","symbolLocation":1192,"imageIndex":41},{"imageOffset":412236,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":24,"imageIndex":38},{"imageOffset":540456,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":140,"imageIndex":38},{"imageOffset":542804,"symbol":"void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":212,"imageIndex":38},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]},{"id":1452589,"name":"io.worker.3","threadState":{"x":[{"value":4},{"value":0},{"value":1024},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6181432792},{"value":0},{"value":768},{"value":3298534884098},{"value":3298534884098},{"value":768},{"value":0},{"value":3298534884096},{"value":305},{"value":49},{"value":0},{"value":4383083768},{"value":4383083832},{"value":6181433568},{"value":0},{"value":0},{"value":1024},{"value":1024},{"value":2048},{"value":1},{"value":105553118479904}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4314987672},"cpsr":{"value":1610616832},"fp":{"value":6181432912},"sp":{"value":6181432768},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319201324},"far":{"value":0}},"frames":[{"imageOffset":18476,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":40},{"imageOffset":31896,"symbol":"_pthread_cond_wait","symbolLocation":1192,"imageIndex":41},{"imageOffset":412236,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":24,"imageIndex":38},{"imageOffset":540456,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":140,"imageIndex":38},{"imageOffset":542804,"symbol":"void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":212,"imageIndex":38},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]},{"id":1452590,"name":"io.worker.4","threadState":{"x":[{"value":260},{"value":0},{"value":1024},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6182006232},{"value":0},{"value":768},{"value":3298534884098},{"value":3298534884098},{"value":768},{"value":0},{"value":3298534884096},{"value":305},{"value":481},{"value":0},{"value":4383083768},{"value":4383083832},{"value":6182007008},{"value":0},{"value":0},{"value":1024},{"value":1025},{"value":1280},{"value":1},{"value":105553118493728}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4314987672},"cpsr":{"value":1610616832},"fp":{"value":6182006352},"sp":{"value":6182006208},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319201324},"far":{"value":0}},"frames":[{"imageOffset":18476,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":40},{"imageOffset":31896,"symbol":"_pthread_cond_wait","symbolLocation":1192,"imageIndex":41},{"imageOffset":412236,"symbol":"std::_fl::condition_variable::wait(std::_fl::unique_lock<std::_fl::mutex>&)","symbolLocation":24,"imageIndex":38},{"imageOffset":540456,"symbol":"fml::ConcurrentMessageLoop::WorkerMain()","symbolLocation":140,"imageIndex":38},{"imageOffset":542804,"symbol":"void* std::_fl::__thread_proxy[abi:nn210000]<std::_fl::tuple<std::_fl::unique_ptr<std::_fl::__thread_struct, std::_fl::default_delete<std::_fl::__thread_struct>>, fml::ConcurrentMessageLoop::ConcurrentMessageLoop(unsigned long)::$_0>>(void*)","symbolLocation":212,"imageIndex":38},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]},{"id":1452591,"name":"dart:io EventHandler","threadState":{"x":[{"value":4},{"value":0},{"value":0},{"value":6183103768},{"value":16},{"value":0},{"value":0},{"value":0},{"value":0},{"value":4319461376,"symbolLocation":0,"symbol":"_current_pid"},{"value":15},{"value":20},{"value":20},{"value":4439884824,"symbolLocation":23982,"symbol":"flutter::kFontChange"},{"value":3018359012},{"value":3016259808},{"value":363},{"value":228},{"value":0},{"value":105553156135488},{"value":0},{"value":67108864},{"value":2147483647},{"value":274877907},{"value":4294966296},{"value":1000000},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4435615480},"cpsr":{"value":536875008},"fp":{"value":6183104368},"sp":{"value":6183102704},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319211140},"far":{"value":0}},"frames":[{"imageOffset":28292,"symbol":"kevent","symbolLocation":8,"imageIndex":40},{"imageOffset":5054200,"symbol":"dart::bin::EventHandlerImplementation::EventHandlerEntry(unsigned long)","symbolLocation":300,"imageIndex":38},{"imageOffset":5169232,"symbol":"dart::bin::ThreadStart(void*)","symbolLocation":88,"imageIndex":38},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]},{"id":1452592,"name":"Dart Profiler ThreadInterrupter","threadState":{"x":[{"value":260},{"value":0},{"value":256},{"value":0},{"value":0},{"value":160},{"value":0},{"value":1000000},{"value":7425},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":31890132180224},{"value":0},{"value":105553162539648},{"value":105553162539720},{"value":1},{"value":1000000},{"value":0},{"value":256},{"value":7425},{"value":7680},{"value":4466429952,"symbolLocation":3816,"symbol":"dart::Symbols::symbol_handles_"},{"value":1}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4314987716},"cpsr":{"value":2684358656},"fp":{"value":6184200848},"sp":{"value":6184200704},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319201324},"far":{"value":0}},"frames":[{"imageOffset":18476,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":40},{"imageOffset":31940,"symbol":"_pthread_cond_wait","symbolLocation":1236,"imageIndex":41},{"imageOffset":5478216,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":38},{"imageOffset":6907636,"symbol":"dart::ThreadInterrupter::ThreadMain(unsigned long)","symbolLocation":328,"imageIndex":38},{"imageOffset":6611236,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":38},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]},{"id":1452593,"name":"Dart Profiler SampleBlockProcessor","threadState":{"x":[{"value":260},{"value":0},{"value":256},{"value":0},{"value":0},{"value":160},{"value":0},{"value":100000000},{"value":257},{"value":0},{"value":256},{"value":1099511628034},{"value":1099511628034},{"value":256},{"value":0},{"value":1099511628032},{"value":305},{"value":258},{"value":0},{"value":105553162539776},{"value":105553162539848},{"value":1},{"value":100000000},{"value":0},{"value":256},{"value":257},{"value":512},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4314987716},"cpsr":{"value":2684358656},"fp":{"value":6185298576},"sp":{"value":6185298432},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319201324},"far":{"value":0}},"frames":[{"imageOffset":18476,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":40},{"imageOffset":31940,"symbol":"_pthread_cond_wait","symbolLocation":1236,"imageIndex":41},{"imageOffset":5478216,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":38},{"imageOffset":6630384,"symbol":"dart::SampleBlockProcessor::ThreadMain(unsigned long)","symbolLocation":168,"imageIndex":38},{"imageOffset":6611236,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":38},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]},{"id":1452594,"name":"DartWorker","threadState":{"x":[{"value":260},{"value":0},{"value":0},{"value":0},{"value":0},{"value":160},{"value":5},{"value":0},{"value":1},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":451},{"value":0},{"value":4383087400},{"value":105553156121648},{"value":1},{"value":0},{"value":5},{"value":0},{"value":1},{"value":256},{"value":4466429952,"symbolLocation":3816,"symbol":"dart::Symbols::symbol_handles_"},{"value":1000}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4314987716},"cpsr":{"value":2684358656},"fp":{"value":6186396224},"sp":{"value":6186396080},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319201324},"far":{"value":0}},"frames":[{"imageOffset":18476,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":40},{"imageOffset":31940,"symbol":"_pthread_cond_wait","symbolLocation":1236,"imageIndex":41},{"imageOffset":5478216,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":38},{"imageOffset":6911084,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":508,"imageIndex":38},{"imageOffset":6911428,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":116,"imageIndex":38},{"imageOffset":6611236,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":38},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]},{"id":1452597,"name":"DartWorker","threadState":{"x":[{"value":260},{"value":0},{"value":0},{"value":0},{"value":0},{"value":160},{"value":5},{"value":0},{"value":1},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":35},{"value":0},{"value":4384120056},{"value":105553156162544},{"value":1},{"value":0},{"value":5},{"value":0},{"value":1},{"value":256},{"value":4466429952,"symbolLocation":3816,"symbol":"dart::Symbols::symbol_handles_"},{"value":1000}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4314987716},"cpsr":{"value":2684358656},"fp":{"value":6187493952},"sp":{"value":6187493808},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4319201324},"far":{"value":0}},"frames":[{"imageOffset":18476,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":40},{"imageOffset":31940,"symbol":"_pthread_cond_wait","symbolLocation":1236,"imageIndex":41},{"imageOffset":5478216,"symbol":"dart::ConditionVariable::WaitMicros(dart::Mutex*, long long)","symbolLocation":112,"imageIndex":38},{"imageOffset":6911084,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":508,"imageIndex":38},{"imageOffset":6911428,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":116,"imageIndex":38},{"imageOffset":6611236,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":38},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]},{"id":1452598,"name":"DartWorker","threadState":{"x":[{"value":4578274544},{"value":316},{"value":14},{"value":0},{"value":18446744073456775488},{"value":16},{"value":0},{"value":0},{"value":4578274529},{"value":4578274880},{"value":0},{"value":18446744073709551615},{"value":15},{"value":68},{"value":12257958089310865275},{"value":15447069372903946749},{"value":4312441004,"symbolLocation":0,"symbol":"sys_icache_invalidate"},{"value":105553156157632},{"value":0},{"value":6188585296},{"value":4404181824},{"value":4404295472},{"value":4404181840},{"value":0},{"value":0},{"value":4404295456},{"value":4465943016,"symbolLocation":16,"symbol":"vtable for dart::StoppedMutatorsScope"},{"value":4465938400,"symbolLocation":16,"symbol":"vtable for dart::SafepointReadRwLocker"},{"value":4465926336,"symbolLocation":0,"symbol":"vtable for dart::StackResource"}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4436889408},"cpsr":{"value":1610616832},"fp":{"value":6188583408},"sp":{"value":6188583312},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4312441084},"far":{"value":0}},"frames":[{"imageOffset":24828,"symbol":"sys_icache_invalidate","symbolLocation":80,"imageIndex":39},{"imageOffset":6328128,"symbol":"dart::Code::FinalizeCode(dart::FlowGraphCompiler*, dart::compiler::Assembler*, dart::Code::PoolAttachment, bool, dart::CodeStatistics*)","symbolLocation":576,"imageIndex":38},{"imageOffset":6980212,"symbol":"dart::CompileParsedFunctionHelper::FinalizeCompilation(dart::compiler::Assembler*, dart::FlowGraphCompiler*, dart::FlowGraph*)","symbolLocation":204,"imageIndex":38},{"imageOffset":6989208,"symbol":"dart::LambdaCallable<dart::CompileParsedFunctionHelper::Compile()::$_0>::Call()","symbolLocation":36,"imageIndex":38},{"imageOffset":5895932,"symbol":"dart::IsolateGroup::RunWithStoppedMutatorsCallable(dart::Callable*, dart::Callable*, bool)","symbolLocation":352,"imageIndex":38},{"imageOffset":6983108,"symbol":"dart::CompileParsedFunctionHelper::Compile()","symbolLocation":1824,"imageIndex":38},{"imageOffset":6984500,"symbol":"dart::CompileFunctionHelper(dart::Function const&, bool, long)","symbolLocation":752,"imageIndex":38},{"imageOffset":6983700,"symbol":"dart::Compiler::CompileFunction(dart::Thread*, dart::Function const&)","symbolLocation":200,"imageIndex":38},{"imageOffset":6264468,"symbol":"dart::Function::EnsureHasCodeNoThrow() const","symbolLocation":88,"imageIndex":38},{"imageOffset":6264260,"symbol":"dart::Function::EnsureHasCode() const","symbolLocation":84,"imageIndex":38},{"imageOffset":6704932,"symbol":"dart::TrySwitchInstanceCall(dart::Thread*, dart::StackFrame*, dart::Code const&, dart::Function const&, dart::ICData const&, dart::Function const&)","symbolLocation":228,"imageIndex":38},{"imageOffset":6705244,"symbol":"dart::PatchableCallHandler::DoICDataMissJIT(dart::ICData const&, dart::Object const&, dart::Function const&)","symbolLocation":120,"imageIndex":38},{"imageOffset":6706716,"symbol":"dart::PatchableCallHandler::ResolveSwitchAndReturn(dart::Object const&)","symbolLocation":288,"imageIndex":38},{"imageOffset":6726540,"symbol":"dart::InlineCacheMissHandler(dart::Thread*, dart::Zone*, dart::GrowableArray<dart::Instance const*> const&, dart::ICData const&, dart::NativeArguments)","symbolLocation":220,"imageIndex":38},{"imageOffset":6707268,"symbol":"dart::DRT_InlineCacheMissHandlerOneArg(dart::NativeArguments)","symbolLocation":388,"imageIndex":38},{"imageOffset":4538774700,"imageIndex":51},{"imageOffset":4538780236,"imageIndex":51},{"imageOffset":4578273244,"imageIndex":51},{"imageOffset":4578265600,"imageIndex":51},{"imageOffset":4578255932,"imageIndex":51},{"imageOffset":4578233640,"imageIndex":51},{"imageOffset":4578270124,"imageIndex":51},{"imageOffset":4578219048,"imageIndex":51},{"imageOffset":4578217856,"imageIndex":51},{"imageOffset":4578217576,"imageIndex":51},{"imageOffset":4578217272,"imageIndex":51},{"imageOffset":4578198204,"imageIndex":51},{"imageOffset":4538776228,"imageIndex":51},{"imageOffset":5753728,"symbol":"dart::DartEntry::InvokeFunction(dart::Function const&, dart::Array const&, dart::Array const&)","symbolLocation":236,"imageIndex":38},{"imageOffset":5760280,"symbol":"dart::DartLibraryCalls::HandleMessage(long long, dart::Instance const&)","symbolLocation":304,"imageIndex":38},{"imageOffset":5883868,"symbol":"dart::IsolateMessageHandler::HandleMessage(std::_fl::unique_ptr<dart::Message, std::_fl::default_delete<dart::Message>>)","symbolLocation":736,"imageIndex":38},{"imageOffset":6032756,"symbol":"dart::MessageHandler::HandleMessages(dart::MonitorLocker*, bool, bool)","symbolLocation":308,"imageIndex":38},{"imageOffset":6033880,"symbol":"dart::MessageHandler::TaskCallback()","symbolLocation":512,"imageIndex":38},{"imageOffset":6910844,"symbol":"dart::ThreadPool::WorkerLoop(dart::ThreadPool::Worker*)","symbolLocation":268,"imageIndex":38},{"imageOffset":6911428,"symbol":"dart::ThreadPool::Worker::Main(unsigned long)","symbolLocation":116,"imageIndex":38},{"imageOffset":6611236,"symbol":"dart::ThreadStart(void*)","symbolLocation":204,"imageIndex":38},{"imageOffset":30456,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":41},{"imageOffset":10560,"symbol":"thread_start","symbolLocation":8,"imageIndex":41}]}],
  "usedImages" : [
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4309860352,
    "size" : 540672,
    "uuid" : "4ce86d18-f3fa-3d2a-a1b8-e7cd8a52fb0d",
    "path" : "\/usr\/lib\/dyld",
    "name" : "dyld"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4306206720,
    "CFBundleShortVersionString" : "1.0.3",
    "CFBundleIdentifier" : "com.example.classz",
    "size" : 16384,
    "uuid" : "735c9755-a007-32a9-84ec-99bb8a717224",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Runner",
    "name" : "Runner",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4307582976,
    "size" : 311296,
    "uuid" : "6c88bc8b-f6b4-3f86-a9b1-60af6e8cc691",
    "path" : "\/Volumes\/VOLUME\/*\/dyld_sim",
    "name" : "dyld_sim"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4330930176,
    "size" : 10190848,
    "uuid" : "954e5de3-f54a-3d66-901a-c6b499aa4fd7",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Runner.debug.dylib",
    "name" : "Runner.debug.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4306796544,
    "CFBundleShortVersionString" : "2.4.0",
    "CFBundleIdentifier" : "org.cocoapods.FBLPromises",
    "size" : 81920,
    "uuid" : "6ffefc41-cdf7-38b1-ac82-cd5da5fba2a1",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/FBLPromises.framework\/FBLPromises",
    "name" : "FBLPromises",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4307009536,
    "CFBundleShortVersionString" : "11.15.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseCore",
    "size" : 81920,
    "uuid" : "a7d36c1e-2b18-31bc-aba1-c6e5e1b7c3d7",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/FirebaseCore.framework\/FirebaseCore",
    "name" : "FirebaseCore",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4308844544,
    "CFBundleShortVersionString" : "11.15.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseCoreInternal",
    "size" : 163840,
    "uuid" : "5ef96399-4143-38a1-9c30-3c26dec6d63f",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/FirebaseCoreInternal.framework\/FirebaseCoreInternal",
    "name" : "FirebaseCoreInternal",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4308336640,
    "CFBundleShortVersionString" : "11.15.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseInstallations",
    "size" : 114688,
    "uuid" : "b4b812d5-d608-32c7-b841-5f819f6ad74e",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/FirebaseInstallations.framework\/FirebaseInstallations",
    "name" : "FirebaseInstallations",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4311678976,
    "CFBundleShortVersionString" : "11.15.0",
    "CFBundleIdentifier" : "org.cocoapods.FirebaseMessaging",
    "size" : 294912,
    "uuid" : "018aed98-4f1e-3d41-b23d-59c02b3b62a4",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/FirebaseMessaging.framework\/FirebaseMessaging",
    "name" : "FirebaseMessaging",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4311072768,
    "CFBundleShortVersionString" : "10.1.0",
    "CFBundleIdentifier" : "org.cocoapods.GoogleDataTransport",
    "size" : 212992,
    "uuid" : "555f87ec-678f-35a4-93dc-0b664dafc6e7",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/GoogleDataTransport.framework\/GoogleDataTransport",
    "name" : "GoogleDataTransport",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4309336064,
    "CFBundleShortVersionString" : "8.1.0",
    "CFBundleIdentifier" : "org.cocoapods.GoogleUtilities",
    "size" : 147456,
    "uuid" : "d35c7857-8dd5-37d6-a73b-763282bff2a9",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/GoogleUtilities.framework\/GoogleUtilities",
    "name" : "GoogleUtilities",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4306567168,
    "CFBundleShortVersionString" : "2.2.0",
    "CFBundleIdentifier" : "org.cocoapods.Mantle",
    "size" : 114688,
    "uuid" : "924daec4-2a61-3747-b3e7-9aa6106e9204",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/Mantle.framework\/Mantle",
    "name" : "Mantle",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4313333760,
    "CFBundleShortVersionString" : "5.21.1",
    "CFBundleIdentifier" : "org.cocoapods.SDWebImage",
    "size" : 524288,
    "uuid" : "5d3f9e18-7589-3a0d-bb4c-ef51f31aef9d",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/SDWebImage.framework\/SDWebImage",
    "name" : "SDWebImage",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4307386368,
    "CFBundleShortVersionString" : "0.14.6",
    "CFBundleIdentifier" : "org.cocoapods.SDWebImageWebPCoder",
    "size" : 49152,
    "uuid" : "bda9da7d-4f45-393c-ae57-4fd53fac1ab0",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/SDWebImageWebPCoder.framework\/SDWebImageWebPCoder",
    "name" : "SDWebImageWebPCoder",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4312711168,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.Stripe",
    "size" : 163840,
    "uuid" : "69e8274b-b9e7-3f86-bc30-5e04c61f1f37",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/Stripe.framework\/Stripe",
    "name" : "Stripe",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4315873280,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripeApplePay",
    "size" : 606208,
    "uuid" : "f36638fb-39d7-35a9-9fad-fe770311f88b",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/StripeApplePay.framework\/StripeApplePay",
    "name" : "StripeApplePay",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4319625216,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripeCore",
    "size" : 917504,
    "uuid" : "6ab6c45c-5eb5-378b-a5a9-055daea2343e",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/StripeCore.framework\/StripeCore",
    "name" : "StripeCore",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4350902272,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripeFinancialConnections",
    "size" : 3293184,
    "uuid" : "64ef84d3-eba9-3290-88de-a0ddede2e638",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/StripeFinancialConnections.framework\/StripeFinancialConnections",
    "name" : "StripeFinancialConnections",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4370710528,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripePaymentSheet",
    "size" : 4620288,
    "uuid" : "a6a1dd9f-279c-3ca1-9ac2-6c3f143770f9",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/StripePaymentSheet.framework\/StripePaymentSheet",
    "name" : "StripePaymentSheet",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4359405568,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripePayments",
    "size" : 2932736,
    "uuid" : "db879431-b76a-382b-b1f6-648b1ab6dc1f",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/StripePayments.framework\/StripePayments",
    "name" : "StripePayments",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4321886208,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripePaymentsUI",
    "size" : 917504,
    "uuid" : "978dc589-4ce6-37eb-854e-134ca9b2c8b0",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/StripePaymentsUI.framework\/StripePaymentsUI",
    "name" : "StripePaymentsUI",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4326473728,
    "CFBundleShortVersionString" : "24.7.0",
    "CFBundleIdentifier" : "org.cocoapods.StripeUICore",
    "size" : 917504,
    "uuid" : "c9b5dc3b-6ddd-34df-97b9-35df4e4b9d5a",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/StripeUICore.framework\/StripeUICore",
    "name" : "StripeUICore",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4308615168,
    "CFBundleShortVersionString" : "5.1.1",
    "CFBundleIdentifier" : "org.cocoapods.app-settings",
    "size" : 32768,
    "uuid" : "ea2aad2e-e978-3de7-94ae-5a7e421db65d",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/app_settings.framework\/app_settings",
    "name" : "app_settings",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4314382336,
    "CFBundleShortVersionString" : "1.0.0",
    "CFBundleIdentifier" : "org.cocoapods.flutter-image-compress-common",
    "size" : 147456,
    "uuid" : "2f8cf055-5805-3ae1-8235-21eaa79020bd",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/flutter_image_compress_common.framework\/flutter_image_compress_common",
    "name" : "flutter_image_compress_common",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4307222528,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.flutter-local-notifications",
    "size" : 65536,
    "uuid" : "7f55823e-5c94-3e5b-b7f2-31e940357fed",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/flutter_local_notifications.framework\/flutter_local_notifications",
    "name" : "flutter_local_notifications",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4311531520,
    "CFBundleShortVersionString" : "0.0.2",
    "CFBundleIdentifier" : "org.cocoapods.fluttertoast",
    "size" : 49152,
    "uuid" : "dc65834e-49db-3493-b18c-9329ed6b6a4c",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/fluttertoast.framework\/fluttertoast",
    "name" : "fluttertoast",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4312268800,
    "CFBundleShortVersionString" : "1.2.0",
    "CFBundleIdentifier" : "org.cocoapods.geolocator-apple",
    "size" : 49152,
    "uuid" : "c99ff30e-ae8a-3d80-9c88-1cc0e06524d2",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/geolocator_apple.framework\/geolocator_apple",
    "name" : "geolocator_apple",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4309647360,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.image-picker-ios",
    "size" : 81920,
    "uuid" : "4fa4e6ae-e665-347c-89c1-e30a2e8653e8",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/image_picker_ios.framework\/image_picker_ios",
    "name" : "image_picker_ios",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4312547328,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.integration-test",
    "size" : 32768,
    "uuid" : "882bfd50-d6cc-3876-b52e-e31fe879215f",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/integration_test.framework\/integration_test",
    "name" : "integration_test",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4317380608,
    "CFBundleShortVersionString" : "1.5.0",
    "CFBundleIdentifier" : "org.cocoapods.libwebp",
    "size" : 655360,
    "uuid" : "0341be3a-eaaf-3540-8295-7442a3b87f16",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/libwebp.framework\/libwebp",
    "name" : "libwebp",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4315234304,
    "CFBundleShortVersionString" : "7.0.0",
    "CFBundleIdentifier" : "org.cocoapods.mobile-scanner",
    "size" : 163840,
    "uuid" : "41463d2a-5f83-3033-ab1d-40be4f11c888",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/mobile_scanner.framework\/mobile_scanner",
    "name" : "mobile_scanner",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4308746240,
    "CFBundleShortVersionString" : "3.30910.0",
    "CFBundleIdentifier" : "org.cocoapods.nanopb",
    "size" : 32768,
    "uuid" : "299f0a13-6610-3f7a-b203-430066bb2e2f",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/nanopb.framework\/nanopb",
    "name" : "nanopb",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : **********,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.path-provider-foundation",
    "size" : 49152,
    "uuid" : "cd4b5c19-786f-31b8-a118-70d705dab292",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/path_provider_foundation.framework\/path_provider_foundation",
    "name" : "path_provider_foundation",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : **********,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.share-plus",
    "size" : 32768,
    "uuid" : "3772bfd6-8649-322b-b588-38f6c5a2e16f",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/share_plus.framework\/share_plus",
    "name" : "share_plus",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : **********,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.shared-preferences-foundation",
    "size" : 81920,
    "uuid" : "6d0d436e-161f-3c2b-92a7-ffbb7d9b46a6",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/shared_preferences_foundation.framework\/shared_preferences_foundation",
    "name" : "shared_preferences_foundation",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4318887936,
    "CFBundleShortVersionString" : "0.0.4",
    "CFBundleIdentifier" : "org.cocoapods.sqflite-darwin",
    "size" : 131072,
    "uuid" : "9843ff8b-1e44-3c07-b7b8-b17c83100a2b",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/sqflite_darwin.framework\/sqflite_darwin",
    "name" : "sqflite_darwin",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4366450688,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.stripe-ios",
    "size" : 1163264,
    "uuid" : "48493791-032a-3895-b4df-cbdf94057c3b",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/stripe_ios.framework\/stripe_ios",
    "name" : "stripe_ios",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4318576640,
    "CFBundleShortVersionString" : "0.0.1",
    "CFBundleIdentifier" : "org.cocoapods.url-launcher-ios",
    "size" : 81920,
    "uuid" : "e996ef3d-2f73-3484-b1c6-37429b617d74",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/url_launcher_ios.framework\/url_launcher_ios",
    "name" : "url_launcher_ios",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4430561280,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "io.flutter.flutter",
    "size" : 35110912,
    "uuid" : "4c4c4456-5555-3144-a115-e347b8e7daf1",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/76C11A2B-E7B7-495C-84BA-B0248EF2D472\/data\/Containers\/Bundle\/Application\/9475D525-89E0-4C64-8DB8-4BEE6873E5E2\/Runner.app\/Frameworks\/Flutter.framework\/Flutter",
    "name" : "Flutter",
    "CFBundleVersion" : "1.0"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4312416256,
    "size" : 32768,
    "uuid" : "7e70a817-68f6-3ad4-98e8-d8e8c4c72f30",
    "path" : "\/usr\/lib\/system\/libsystem_platform.dylib",
    "name" : "libsystem_platform.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4319182848,
    "size" : 245760,
    "uuid" : "eb416efd-de68-327d-bf87-167e83297032",
    "path" : "\/usr\/lib\/system\/libsystem_kernel.dylib",
    "name" : "libsystem_kernel.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4314955776,
    "size" : 65536,
    "uuid" : "149fcf58-196b-369a-8c9e-c7d8dddc1408",
    "path" : "\/usr\/lib\/system\/libsystem_pthread.dylib",
    "name" : "libsystem_pthread.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4306468864,
    "size" : 49152,
    "uuid" : "22dfc621-7386-3de3-87b2-9ed27c8be84d",
    "path" : "\/Volumes\/VOLUME\/*\/libobjc-trampolines.dylib",
    "name" : "libobjc-trampolines.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6443491328,
    "size" : 509852,
    "uuid" : "bfa3ae49-5373-3d1f-a47d-724abaf1ee45",
    "path" : "\/Volumes\/VOLUME\/*\/libsystem_c.dylib",
    "name" : "libsystem_c.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6445182976,
    "size" : 114688,
    "uuid" : "3a16c8a1-792d-38cf-967a-72505dfb87e4",
    "path" : "\/Volumes\/VOLUME\/*\/libc++abi.dylib",
    "name" : "libc++abi.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6442876928,
    "size" : 248096,
    "uuid" : "3286911c-7f92-3620-b8f7-18ac6061e9d9",
    "path" : "\/Volumes\/VOLUME\/*\/libobjc.A.dylib",
    "name" : "libobjc.A.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6446206976,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.CoreFoundation",
    "size" : 4292608,
    "uuid" : "3d4aa1d5-03aa-3365-b767-944509b9bbfd",
    "path" : "\/Volumes\/VOLUME\/*\/CoreFoundation.framework\/CoreFoundation",
    "name" : "CoreFoundation",
    "CFBundleVersion" : "3423"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6523658240,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.UIKitCore",
    "size" : 30841056,
    "uuid" : "d4c23b9a-c567-3e42-86ef-697aec976159",
    "path" : "\/Volumes\/VOLUME\/*\/UIKitCore.framework\/UIKitCore",
    "name" : "UIKitCore",
    "CFBundleVersion" : "8444.1.105"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6572392448,
    "CFBundleShortVersionString" : "943.5.17",
    "CFBundleIdentifier" : "com.apple.FrontBoardServices",
    "size" : 783328,
    "uuid" : "fd5fcf2c-bbed-3086-ac10-6df96be66614",
    "path" : "\/Volumes\/VOLUME\/*\/FrontBoardServices.framework\/FrontBoardServices",
    "name" : "FrontBoardServices",
    "CFBundleVersion" : "943.5.17"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6444003328,
    "size" : 281440,
    "uuid" : "7bee27fd-f519-330d-aebe-d6ace467df22",
    "path" : "\/Volumes\/VOLUME\/*\/libdispatch.dylib",
    "name" : "libdispatch.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6727008256,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.GraphicsServices",
    "size" : 33280,
    "uuid" : "eeb999f0-53c2-31ce-b203-3c78fb303dab",
    "path" : "\/Volumes\/VOLUME\/*\/GraphicsServices.framework\/GraphicsServices",
    "name" : "GraphicsServices",
    "CFBundleVersion" : "1.0"
  },
  {
    "size" : 0,
    "source" : "A",
    "base" : 0,
    "uuid" : "00000000-0000-0000-0000-000000000000"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6451019776,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.Foundation",
    "size" : 12343520,
    "uuid" : "6ec60314-780a-318f-8bdb-5d173b13970e",
    "path" : "\/Volumes\/VOLUME\/*\/Foundation.framework\/Foundation",
    "name" : "Foundation",
    "CFBundleVersion" : "3423"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6443233280,
    "size" : 244384,
    "uuid" : "c8560cb4-518f-37c4-915d-0554f320b3a0",
    "path" : "\/Volumes\/VOLUME\/*\/libxpc.dylib",
    "name" : "libxpc.dylib"
  }
],
  "sharedCache" : {
  "base" : 6442450944,
  "size" : 3904126976,
  "uuid" : "c0a1c7b7-d07a-355d-b679-************"
},
  "vmSummary" : "ReadOnly portion of Libraries: Total=1.8G resident=0K(0%) swapped_out_or_unallocated=1.8G(100%)\nWritable regions: Total=660.1M written=1783K(0%) resident=1783K(0%) swapped_out=0K(0%) unallocated=658.4M(100%)\n\n                                VIRTUAL   REGION \nREGION TYPE                        SIZE    COUNT (non-coalesced) \n===========                     =======  ======= \nActivity Tracing                   256K        1 \nColorSync                           64K        4 \nFoundation                          16K        1 \nKernel Alloc Once                   32K        1 \nMALLOC                           587.8M       36 \nMALLOC guard page                  128K        8 \nSTACK GUARD                       56.6M       36 \nStack                             34.1M       36 \nVM_ALLOCATE                       36.4M       45 \n__DATA                            44.8M      934 \n__DATA_CONST                     113.1M      949 \n__DATA_DIRTY                       107K       12 \n__FONT_DATA                        2352        1 \n__LINKEDIT                       731.3M       44 \n__OBJC_RO                         61.2M        1 \n__OBJC_RW                         2723K        1 \n__TEXT                             1.1G      962 \n__TPRO_CONST                       308K        3 \ndyld private memory                2.5G       13 \nmapped file                       34.0M        7 \nowned unmapped memory               32K        1 \npage table in kernel              1783K        1 \nshared memory                       16K        1 \n===========                     =======  ======= \nTOTAL                              5.2G     3098 \n",
  "legacyInfo" : {
  "threadTriggered" : {
    "queue" : "com.apple.main-thread"
  }
},
  "logWritingSignature" : "4720324807166538d68f09069e21cd27cd705d17",
  "trialInfo" : {
  "rollouts" : [
    {
      "rolloutId" : "6246d6a916a70b047e454124",
      "factorPackIds" : {

      },
      "deploymentId" : 240000010
    },
    {
      "rolloutId" : "652eff3d1bce5442b8d753c9",
      "factorPackIds" : {

      },
      "deploymentId" : 240000009
    }
  ],
  "experiments" : [

  ]
}
}

Model: MacBookPro18,3, BootROM 11881.61.3, proc 8:6:2 processors, 16 GB, SMC 
Graphics: Apple M1 Pro, Apple M1 Pro, Built-In
Display: Color LCD, 3024 x 1964 Retina, Main, MirrorOff, Online
Memory Module: LPDDR5, Samsung
AirPort: spairport_wireless_card_type_wifi (0x14E4, 0x4387), wl0: Oct 31 2024 06:06:06 version 20.10.1135.4.8.7.191 FWID 01-e648b845
IO80211_driverkit-1345.8 "IO80211_driverkit-1345.8" Nov  9 2024 17:02:32
AirPort: 
Bluetooth: Version (null), 0 services, 0 devices, 0 incoming serial ports
Network Service: Wi-Fi, AirPort, en0
USB Device: USB31Bus
USB Device: USB31Bus
USB Device: USB31Bus
Thunderbolt Bus: MacBook Pro, Apple Inc.
Thunderbolt Bus: MacBook Pro, Apple Inc.
Thunderbolt Bus: MacBook Pro, Apple Inc.
