name: class_z
description: A new Flutter project.
publish_to: "none"

version: 1.0.3+1

environment:
  sdk: ">=3.1.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  intl:
  floor:
  retrofit:
  flutter_hooks:
  cached_network_image:
  flutter_cache_manager: ^3.4.1
  pin_code_fields: ^8.0.1
  fluttertoast:
  google_nav_bar: ^5.0.6
  equatable: ^2.0.5
  bloc:
  flutter_bloc:
  smooth_page_indicator: ^1.1.0
  qr_flutter:
  mobile_scanner:
  flutter_svg:
  table_calendar:
  image_picker:
  font_awesome_flutter:
  shared_preferences: ^2.2.3
  hive: ^2.0.4
  hive_flutter: ^1.1.0
  path_provider: ^2.0.10
  dartz: ^0.10.1
  provider:
  flutter_radar_chart:
  fl_chart:
  kg_charts:
  http: ^1.1.0
  device_preview:
  get_it: ^7.2.0
  injectable: ^2.3.0
  json_annotation: ^4.7.0
  flutter_stripe: ^11.3.0
  flutter_dotenv:
  socket_io_client: ^3.0.2
  pixel_perfect:
  firebase_core: ^3.9.0
  firebase_messaging: ^15.1.6
  app_settings: ^5.1.1
  flutter_local_notifications: ^18.0.1
  http_parser: ^4.1.2
  dio:
  geolocator: ^11.0.0
  mime: ^1.0.4
  flutter_image_compress: ^2.1.0
  share_plus: ^7.2.1
  google_maps_flutter: ^2.5.5
  url_launcher: ^6.1.14
  shimmer: ^3.0.0
  flutter_screenutil: ^5.9.3
  # stripe_payment: ^1.1.0
  flutter_localizations:
    sdk: flutter
  go_router: ^14.2.0
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  flutter_launcher_icons: ^0.13.1
  retrofit_generator: ^8.2.1
  floor_generator: ^1.0.0
  hive_generator: 2.0.1
  build_runner: ^2.3.3
  injectable_generator: ^2.1.3 # Version 2
  json_serializable: ^6.5.4
  mockito: ^5.3.2
  test: ^1.21.0

dependency_overrides:
  meta: ^1.16.0

flutter_lints: ^2.0.0

flutter:
  uses-material-design: true

  assets:
    - lib/assets/images/
    - lib/assets/fonts/
  fonts:
    - family: SF
      fonts:
        - asset: lib/assets/fonts/SFPRODISPLAYREGULAR.OTF
          weight: 400
          style: normal
    #        - asset: lib/assets/fonts/SFPRODISPLAYBOLD.OTF
    #          weight: 700
    #          style: normal  # Removed 'bold', 'normal' is the correct value for style

    - family: Roboto
      fonts:
        - asset: lib/assets/fonts/Roboto-Regular.ttf
          weight: 400
          style: normal
        - asset: lib/assets/fonts/Roboto-Bold.ttf
          weight: 700
          style: normal # 'normal' is correct here for bold font weight

    - family: ballo
      fonts:
        - asset: lib/assets/fonts/BalooTamma-Regular.ttf
          weight: 400
          style: normal
