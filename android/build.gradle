//buildscript {
//    ext {
//        agp_version = '8.5.0'
//    }
//    ext.kotlin_version = '2.0.21'
//    repositories {
//        google()
//        mavenCentral()
//       maven { url 'https://storage.googleapis.com/download.flutter.io' }
//    }
//    dependencies {
//        classpath "com.android.tools.build:gradle:$agp_version"
//        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
//        classpath 'com.google.gms:google-services:4.4.2'
//    }
//}
//
//allprojects {
//    repositories {
//        google()
//        mavenCentral()
//        maven { url 'https://storage.googleapis.com/download.flutter.io' }
//    }
//}
//
//rootProject.buildDir = '../build'
//subprojects {
//    project.buildDir = "${rootProject.buildDir}/${project.name}"
//}
//subprojects {
//    project.evaluationDependsOn(':app')
//}
//
//tasks.register("clean", Delete) {
//    delete rootProject.buildDir
//}
//buildscript {
//    ext.kotlin_version = '1.9.25'
//    repositories {
//        google()
//        mavenCentral()
//        maven { url 'https://storage.googleapis.com/download.flutter.io' }
//    }
//
//    dependencies {
//        classpath 'com.android.tools.build:gradle:8.3.2'
//        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
//        classpath 'com.google.gms:google-services:4.4.2'
//    }
//}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://storage.googleapis.com/download.flutter.io' }
    }
    
    // Handle SDK XML version warning
    configurations.all {
        resolutionStrategy {
            force 'androidx.core:core-ktx:1.12.0'
            force 'androidx.activity:activity:1.8.0'
            force 'androidx.fragment:fragment:1.6.2'
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}