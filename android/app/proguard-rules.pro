#-keep class com.stripe.android.pushProvisioning.EphemeralKeyUpdateListener { *; }
#-keep class com.stripe.android.pushProvisioning.PushProvisioningActivity$g { *; }
#-keep class com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Args { *; }
#-keep class com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Error { *; }
#-keep class com.stripe.android.pushProvisioning.PushProvisioningActivityStarter { *; }
#-keep class com.stripe.android.pushProvisioning.PushProvisioningEphemeralKeyProvider { *; }
#-keepinterface com.stripe.android.pushProvisioning.EphemeralKeyUpdateListener { *; } // Important for interfaces!
#-keep class com.stripe.android.model.Card$Builder {*;} // <-- Potentially needed
#-keep class com.stripe.android.model.** { *; } // <-- Potentially needed for other models
#-keep class com.stripe.android.view.** { *; } // <-- Potentially needed for views
#-keep class com.stripe.android.**.R$* {*;} //Keep R file
#-keep class com.stripe.android.R$* {*;}
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivity$g
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Args
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Error
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningEphemeralKeyProvider

# Flutter specific rules
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# Firebase rules
-keep class com.google.firebase.** { *; }
-keep class com.firebase.** { *; }

# Keep Stripe
-keep class com.stripe.android.** { *; }

# Keep Gson stuff
-keep class sun.misc.Unsafe { *; }
-keep class com.google.gson.** { *; }

# Keep essential classes
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer
-keep class androidx.lifecycle.** { *; }

# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory

# Retrofit rules
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }

# OkHttp rules
-dontwarn okhttp3.**
-dontwarn okio.**
-keep class okhttp3.** { *; }

# Kotlin serialization rules
-keepattributes *Annotation*, InnerClasses
-dontnote kotlinx.serialization.AnnotationsKt
-keepclassmembers class kotlinx.serialization.json.** { *** Companion; }

# Uncomment the following rules if you face issues with the specific libraries
# -keep class your.problematic.package.** { *; }