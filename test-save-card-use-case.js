// Mock dependencies
class MockUserRepository {
  async findById(userId) {
    console.log(`MockUserRepository.findById called with userId: ${userId}`);
    // Return a mock user with a stripeCustomerId
    return {
      id: userId,
      email: '<EMAIL>',
      stripeCustomerId: 'cus_mock_123456'
    };
  }
  
  async update(userId, data) {
    console.log(`MockUserRepository.update called with userId: ${userId}, data:`, data);
    return true;
  }
}

class MockPaymentService {
  async createCustomer(email, cardToken) {
    console.log(`MockPaymentService.createCustomer called with email: ${email}, cardToken: ${cardToken?.substring(0, 8)}...`);
    return { id: 'cus_new_123456' };
  }
  
  async addCardToCustomer(customerId, cardToken) {
    console.log(`MockPaymentService.addCardToCustomer called with customerId: ${customerId}, cardToken: ${cardToken?.substring(0, 8)}...`);
    // This is the critical part - we want to make sure customerId is user.stripeCustomerId
    if (customerId !== 'cus_mock_123456') {
      throw new Error(`Expected customerId to be 'cus_mock_123456', but got '${customerId}'`);
    }
    return { id: 'card_123456' };
  }
}

// Import the SaveCardUseCase
const SaveCardUseCase = require('./src/app/use_case/saveCarduseCase');

async function testSaveCardUseCase() {
  try {
    console.log('Testing SaveCardUseCase...');
    
    // Create instances of the mocks
    const mockUserRepository = new MockUserRepository();
    const mockPaymentService = new MockPaymentService();
    
    // Create an instance of SaveCardUseCase with the mocks
    const saveCardUseCase = new SaveCardUseCase(mockUserRepository, mockPaymentService);
    
    // Test with a user that has a stripeCustomerId
    console.log('\nTest 1: User with stripeCustomerId');
    const result1 = await saveCardUseCase.execute('user_123', 'tok_mock_123456');
    console.log('Result:', result1);
    
    // Test with a user that doesn't have a stripeCustomerId
    console.log('\nTest 2: User without stripeCustomerId');
    // Override the findById method to return a user without stripeCustomerId
    mockUserRepository.findById = async (userId) => {
      console.log(`MockUserRepository.findById called with userId: ${userId}`);
      return {
        id: userId,
        email: '<EMAIL>',
        stripeCustomerId: null
      };
    };
    
    const result2 = await saveCardUseCase.execute('user_456', 'tok_mock_789012');
    console.log('Result:', result2);
    
    console.log('\nAll tests passed!');
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testSaveCardUseCase(); 