# 🎯 Campaign Discount Guide: How to Get 20% Off Classes

## Overview

When users see the "**LUNAR NEW YEAR SPECIAL - 20% OFF ALL CLASSES**" campaign in the highlights section, they can now actually get 20% off their class bookings using the discount code **LUNAR2024**.

## 💰 How Users Can Apply the 20% Discount

### Step 1: See the Campaign
- Users see the beautiful campaign highlight showing "20% OFF ALL CLASSES"
- The campaign displays the offer period: "6 Jul - 5 Aug"
- Campaign is visible but not clickable (as designed)

### Step 2: Remember the Discount Code
- The discount code for this campaign is: **LUNAR2024**
- This code gives 20% off on all class bookings
- Valid until August 5, 2025

### Step 3: Book a Class and Apply Discount

1. **Find a Class**: Browse and select any class/course
2. **Go to Booking**: Proceed to the request/booking page
3. **Enter Discount Code**: In the "Discount" section, enter **LUNAR2024**
4. **Apply Coupon**: 
   - Either type the code directly in the discount field
   - Or tap "Apply Coupon" and select LUNAR2024 from available coupons
5. **See Savings**: The 20% discount will be automatically calculated
6. **Complete Payment**: Pay the reduced amount

## 🧮 Discount Calculation Example

**Original Class Price**: 100 ZCoin  
**Discount (20%)**: -20 ZCoin  
**Final Price**: **80 ZCoin** ✅

**Original Course Price**: 500 ZCoin (5 classes × 100 each)  
**Discount (20%)**: -100 ZCoin  
**Final Price**: **400 ZCoin** ✅

## 🎨 Available Campaign Discounts

Currently active campaign discounts:

### 1. Lunar New Year Special
- **Code**: `LUNAR2024`
- **Discount**: 20% off all classes
- **Valid Until**: August 5, 2025
- **Campaign**: Pink gradient card with "LUNAR NEW YEAR SPECIAL"

### 2. Summer Camp Early Bird
- **Code**: `SUMMER30`
- **Discount**: 30% off all classes
- **Valid Until**: September 4, 2025
- **Campaign**: Yellow gradient card with "SUMMER CAMP EARLY BIRD"

## 📱 User Interface Flow

### Where to Enter Discount Code

When booking a class, users will see:

```
Discount
────────────────────────────────────
Discount code: [If Applicable    ]
────────────────────────────────────
[💰 Apply Coupon              >]
────────────────────────────────────
```

### Two Ways to Apply:

1. **Direct Entry**: Type `LUNAR2024` in the discount code field
2. **Coupon Selection**: Tap "Apply Coupon" → Select "LUNAR2024 (20% off)" from the list

### Bill Display
```
Subtotal:           100 ZCoin
Discount:           -20 ZCoin (LUNAR2024 - 20% off)
─────────────────────────────
Total:              80 ZCoin
```

## 🚀 Implementation Details

### Backend Integration
- Campaign discounts are automatically synced from active campaigns
- Discount codes are created when campaigns are created/updated
- All validation is handled automatically (expiry, active status, etc.)

### API Endpoints
- `GET /api/campaigns/active` - Shows active campaigns
- `GET /api/campaigns/discounts/:userId` - Gets available campaign discounts
- `POST /api/campaigns/sync-discounts` - Syncs campaign discounts (admin)

### Database
- Campaign discounts are stored in the `discounts` collection
- Linked to original campaigns via `campaignId` field
- Universal discounts (available to all users)

## 🎯 Marketing Benefits

### For Users
- Clear visual campaigns show available offers
- Easy discount code usage
- Significant savings on class bookings
- Multiple active campaigns to choose from

### For Business
- Increased conversion rates from campaign highlights
- Trackable discount code usage
- Automated campaign-to-discount sync
- Flexible campaign management

## 🔧 How to Create New Campaign Discounts

### For Administrators

1. **Create Campaign**: Use the campaign management API to create a new campaign
2. **Include Discount Details**:
   ```json
   {
     "title": "Spring Special",
     "subtitle": "25% Off All Classes",
     "discountPercentage": 25,
     "discountCode": "SPRING25",
     "validFrom": "2025-03-01T00:00:00.000Z",
     "validUntil": "2025-03-31T23:59:59.000Z"
   }
   ```
3. **Auto-Sync**: Discount codes are automatically created
4. **Manual Sync** (if needed): `POST /api/campaigns/sync-discounts`

### Testing New Discounts

```bash
# Test discount application
curl -X POST http://*************:3000/api/campaigns/sync-discounts

# Verify discount exists
curl http://*************:3000/api/discount/SPRING25
```

## 📊 Tracking and Analytics

### Campaign Performance
- Campaign impression counts (when campaigns are viewed)
- Click counts (if clicking was enabled)
- Discount code usage statistics

### Discount Usage
- Track how many times each code is used
- Monitor conversion rates from campaigns to bookings
- Measure total savings provided to users

## ⚠️ Important Notes

1. **Case Sensitive**: Discount codes are case-sensitive (`LUNAR2024` not `lunar2024`)
2. **One Discount Per Booking**: Users can only apply one discount code per booking
3. **Universal Codes**: Campaign discounts are available to all users
4. **Auto-Expiry**: Discounts automatically become invalid after campaign end date
5. **Minimum Price**: Discounts cannot reduce prices below 0

## 🎉 User Experience

### What Users See
1. **Campaign Highlights**: Beautiful promotional cards they can swipe through
2. **Clear Offers**: "20% OFF ALL CLASSES" messaging
3. **Easy Application**: Simple discount code entry during booking
4. **Immediate Savings**: Real-time price calculation with discount applied

### Result
Users who see the "20% OFF ALL CLASSES" campaign can now actually get 20% off by using the code **LUNAR2024** when booking any class or course!

---

## Quick Reference

**Current Active Codes:**
- `LUNAR2024` → 20% off (expires Aug 5, 2025)
- `SUMMER30` → 30% off (expires Sep 4, 2025)

**User Instructions:**
1. See campaign highlight → Remember the discount code
2. Book any class → Enter discount code → Save money! 💰 