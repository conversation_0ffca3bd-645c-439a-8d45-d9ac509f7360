/**
 * Pre-deployment script to optimize the deployment process
 * Run this script before pushing to Git with: node deploy.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Starting pre-deployment optimization...');

// Function to delete unnecessary files and directories
const cleanupFiles = () => {
  const filesToDelete = [
    // Logs
    'npm-debug.log',
    'yarn-debug.log',
    'yarn-error.log',
    // Editor files
    '.DS_Store',
    // Test files
    'test-results',
    'coverage'
  ];

  filesToDelete.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`Removing ${file}...`);
      fs.unlinkSync(file);
    }
  });

  console.log('File cleanup complete.');
};

// Update package.json version (patch increment)
const updateVersion = () => {
  const packagePath = path.join(__dirname, 'package.json');
  const packageData = require(packagePath);
  
  // Split the version by dots
  const versionParts = packageData.version.split('.');
  // Increment the patch version (last part)
  versionParts[2] = (parseInt(versionParts[2], 10) + 1).toString();
  // Join it back together
  packageData.version = versionParts.join('.');
  
  // Write the updated version back to package.json
  fs.writeFileSync(packagePath, JSON.stringify(packageData, null, 2));
  
  console.log(`Version updated to ${packageData.version}`);
  return packageData.version;
};

// Create a deployment info file
const createDeploymentInfo = (version) => {
  const deploymentInfo = {
    version,
    deployedAt: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'production'
  };
  
  fs.writeFileSync(
    path.join(__dirname, 'deployment.json'), 
    JSON.stringify(deploymentInfo, null, 2)
  );
  
  console.log('Deployment info created.');
};

// Run optimization functions
try {
  cleanupFiles();
  const newVersion = updateVersion();
  createDeploymentInfo(newVersion);
  
  // Prepare git commit
  console.log('\nTo complete the deployment preparation:');
  console.log('1. Review the changes made by this script');
  console.log('2. Run the following commands:');
  console.log('   git add .');
  console.log(`   git commit -m "Deploy version ${newVersion}"`);
  console.log('   git push origin main');
  console.log('\nYour deployment will be optimized and should go live faster on cPanel.');
} catch (error) {
  console.error('Error during pre-deployment optimization:', error);
  process.exit(1);
} 