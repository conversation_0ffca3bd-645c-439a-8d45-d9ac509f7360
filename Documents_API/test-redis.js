/**
 * Redis Test Script
 * 
 * This script tests the connection to Redis server and performs basic operations.
 * Run this to verify that Redis is working correctly with your application.
 */

const redis = require('redis');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

async function testRedisConnection() {
  console.log('Redis Test Script');
  console.log('=================\n');
  
  console.log('Checking environment variables:');
  console.log(`ENABLE_REDIS_CACHE: ${process.env.ENABLE_REDIS_CACHE}`);
  console.log(`REDIS_HOST: ${process.env.REDIS_HOST || 'localhost (default)'}`);
  console.log(`REDIS_PORT: ${process.env.REDIS_PORT || '6379 (default)'}\n`);
  
  // Create Redis client
  const client = redis.createClient({
    socket: {
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379
    },
    username: process.env.REDIS_USERNAME,
    password: process.env.REDIS_PASSWORD
  });
  
  // Set up event handlers
  client.on('error', (err) => {
    console.error('Redis Error:', err.message);
  });
  
  try {
    console.log('Connecting to Redis...');
    await client.connect();
    console.log('✅ Successfully connected to Redis server\n');
    
    // Test basic operations
    console.log('Testing basic Redis operations:');
    
    console.log('1. Setting test key...');
    await client.set('test_key', 'Hello from ClassZ Chat Backend');
    console.log('✅ Key set successfully');
    
    console.log('2. Getting test key...');
    const value = await client.get('test_key');
    console.log(`✅ Retrieved value: "${value}"`);
    
    console.log('3. Deleting test key...');
    await client.del('test_key');
    console.log('✅ Key deleted successfully');
    
    console.log('\nAll tests passed! Redis is working properly.');
    
    // Disconnect from Redis
    await client.disconnect();
    console.log('Disconnected from Redis');
    
  } catch (error) {
    console.error('\n❌ Redis test failed:', error.message);
    console.error('Please check your Redis server installation and configuration.');
    
    console.log('\nTroubleshooting tips:');
    console.log('1. Make sure Redis server is installed and running');
    console.log('2. Check that Redis is running on the configured host and port');
    console.log('3. Run the setup-redis.js script: node setup-redis.js');
    console.log('4. Verify firewall settings allow connections to Redis port');
    
    process.exit(1);
  }
}

// Run the test
testRedisConnection().catch(console.error); 