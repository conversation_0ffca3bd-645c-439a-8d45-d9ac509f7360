{"info": {"name": "ClassZ Review API", "description": "A collection of requests for the ClassZ Review API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Create Review", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "body": {"mode": "raw", "raw": "{\n  \"targetType\": \"center\",\n  \"targetId\": \"{{centerId}}\",\n  \"rating\": 4.5,\n  \"comment\": \"Great facility with excellent coaches. My child really enjoys the classes here.\",\n  \"title\": \"Excellent Center\"\n}"}, "url": {"raw": "{{baseUrl}}/api/review", "host": ["{{baseUrl}}"], "path": ["api", "review"]}, "description": "Submits a new review for a center, class, or coach."}}, {"name": "Get Reviews for Entity", "request": {"method": "GET", "header": [{"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication (optional)"}], "url": {"raw": "{{baseUrl}}/api/review/{{revieweeId}}/{{revieweeType}}", "host": ["{{baseUrl}}"], "path": ["api", "review", "{{revieweeId}}", "{{revieweeType}}"]}, "description": "Retrieves reviews for a specific center, class, or coach."}}, {"name": "Get Pending Reviews (Center)", "request": {"method": "GET", "header": [{"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "url": {"raw": "{{baseUrl}}/api/review/student/pending/{{centerId}}", "host": ["{{baseUrl}}"], "path": ["api", "review", "student", "pending", "{{centerId}}"]}, "description": "Retrieves a list of pending reviews for a center."}}, {"name": "Get Pending Reviews (Class)", "request": {"method": "GET", "header": [{"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "url": {"raw": "{{baseUrl}}/api/review/student/pending/class/{{classId}}", "host": ["{{baseUrl}}"], "path": ["api", "review", "student", "pending", "class", "{{classId}}"]}, "description": "Retrieves a list of pending reviews for a class."}}, {"name": "Get Pending Reviews (Coach)", "request": {"method": "GET", "header": [{"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "url": {"raw": "{{baseUrl}}/api/review/student/pending/coach/{{coachId}}", "host": ["{{baseUrl}}"], "path": ["api", "review", "student", "pending", "coach", "{{coachId}}"]}, "description": "Retrieves a list of pending reviews for a coach."}}, {"name": "Get Average Rating", "request": {"method": "GET", "header": [{"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "url": {"raw": "{{baseUrl}}/api/review/average/{{revieweeId}}/{{revieweeType}}", "host": ["{{baseUrl}}"], "path": ["api", "review", "average", "{{revieweeId}}", "{{revieweeType}}"]}, "description": "Retrieves the average rating for a specific entity."}}, {"name": "Get Reviews by Reviewer", "request": {"method": "GET", "header": [{"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "url": {"raw": "{{baseUrl}}/api/review/reviewer/{{reviewerId}}/{{reviewerType}}", "host": ["{{baseUrl}}"], "path": ["api", "review", "reviewer", "{{reviewerId}}", "{{reviewerType}}"]}, "description": "Retrieves reviews created by a specific reviewer."}}, {"name": "Get Review by Class and ID", "request": {"method": "GET", "header": [{"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "url": {"raw": "{{baseUrl}}/api/review/{{classId}}/{{revieweeId}}/{{revieweeType}}", "host": ["{{baseUrl}}"], "path": ["api", "review", "{{classId}}", "{{revieweeId}}", "{{revieweeType}}"]}, "description": "Retrieves a specific review associated with a class."}}, {"name": "Get Review Moments", "request": {"method": "GET", "header": [{"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "url": {"raw": "{{baseUrl}}/api/review/moments?reviewId={{reviewId}}", "host": ["{{baseUrl}}"], "path": ["api", "review", "moments"], "query": [{"key": "reviewId", "value": "{{reviewId}}"}]}, "description": "Retrieves moments associated with a review."}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "YOUR_JWT_TOKEN", "type": "string"}, {"key": "centerId", "value": "YOUR_CENTER_ID", "type": "string"}, {"key": "classId", "value": "YOUR_CLASS_ID", "type": "string"}, {"key": "coachId", "value": "YOUR_COACH_ID", "type": "string"}, {"key": "revieweeId", "value": "YOUR_REVIEWEE_ID", "type": "string"}, {"key": "revieweeType", "value": "center", "type": "string"}, {"key": "reviewerId", "value": "YOUR_REVIEWER_ID", "type": "string"}, {"key": "reviewerType", "value": "user", "type": "string"}, {"key": "reviewId", "value": "YOUR_REVIEW_ID", "type": "string"}]}