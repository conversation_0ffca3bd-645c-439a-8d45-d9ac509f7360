# Balance API - Usage Guide

This guide provides instructions on how to use the Balance API in the ClassZ Backend system.

## Introduction

The Balance API allows you to manage user coin balances within the ClassZ application. Users can accumulate coins and spend them on various features and services in the platform.

## Prerequisites

To use this API, you need:
- A valid JWT token for authentication
- A user ID for which to create/manage a balance
- Postman or similar API testing tool (optional)

## Basic Balance Management

### 1. Creating a User Balance

When a new user joins the platform, you can create a balance wallet for them:

1. Make a POST request to `/api/balance`
2. Include the `auth-token` header with your JWT token
3. In the request body, specify:
   ```json
   {
     "userId": "USER_ID_HERE",
     "balance": 0
   }
   ```
4. The response will contain the newly created balance object

**Note**: Balance defaults to 0 if not specified.

### 2. Checking a User's Balance

To retrieve a user's current balance:

1. Make a GET request to `/api/balance/:userId` (replace `:userId` with the actual user ID)
2. Include the `auth-token` header with your JWT token
3. The response will contain the user's balance object

**Tip**: Use this before operations that require sufficient funds to check if a user has enough coins.

### 3. Updating a User's Balance

To update a user's balance:

1. Make a PUT request to `/api/balance/:userId` (replace `:userId` with the actual user ID)
2. Include the `auth-token` header with your JWT token
3. In the request body, specify:
   ```json
   {
     "balance": 1000
   }
   ```
4. The response will contain the updated balance object

**Warning**: This replaces the entire balance amount. For incremental changes, first get the current balance.

## Using the API with Postman

We've provided a Postman collection (`BALANCE_API_POSTMAN_COLLECTION.json`) that you can import to quickly test the API.

1. Import the collection into Postman
2. Set up your environment variables:
   - `base_url`: The base URL of your API (e.g., `http://localhost:3000`)
   - `user_token`: Your JWT token
   - `user_id`: The ID of the user whose balance you want to manage

3. Use the collection to test the different balance operations

## Authentication Note

Remember that ClassZ Backend uses the `auth-token` header for authentication:

```
auth-token: YOUR_JWT_TOKEN
```

Make sure to include this header with your JWT token for all requests.

## Common Use Cases

### Reward System

You can use the Balance API to implement a reward system:

1. **Event completion**: When a user completes a task or activity, reward them with coins
2. **Achievements**: Award coins for reaching milestones
3. **Referrals**: Give coins to users who refer others to the platform

### Example: Rewarding a User for Activity Completion

```javascript
// Reward a user with 50 coins for completing an activity
const rewardUserForActivity = async (userId) => {
  // First, get the current balance
  const currentBalance = await getUserBalance(userId);
  
  // Add 50 coins to the current balance
  const newBalance = currentBalance.balance + 50;
  
  // Update the user's balance with the new amount
  return await updateUserBalance(userId, newBalance);
};
```

### Spending Coins

Users can spend coins on:

1. **Premium content**: Allow users to unlock special content
2. **Virtual goods**: Let users purchase virtual items
3. **Service upgrades**: Enable users to upgrade their service level

### Example: Deducting Coins for a Purchase

```javascript
// Deduct coins when a user makes a purchase
const processCoinsForPurchase = async (userId, itemCost) => {
  // First, get the current balance
  const currentBalance = await getUserBalance(userId);
  
  // Check if the user has enough coins
  if (currentBalance.balance < itemCost) {
    throw new Error('Insufficient balance');
  }
  
  // Deduct the cost from the current balance
  const newBalance = currentBalance.balance - itemCost;
  
  // Update the user's balance with the new amount
  return await updateUserBalance(userId, newBalance);
};
```

## Best Practices

1. **Always validate user input**: Ensure balance values are valid numbers
2. **Check for sufficient funds**: Before deducting coins, verify the user has enough
3. **Implement transactions**: When performing multiple operations, use transactions to maintain data consistency
4. **Log all balance changes**: Keep an audit trail of all balance modifications
5. **Handle errors gracefully**: Implement proper error handling, especially for user-facing operations

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- 200 OK: Request successful
- 500 Internal Server Error: Server-side issue or validation error

Common errors include:
- "Child not found" - When the specified user ID doesn't exist
- "Failed to fetch balance: No wallet found for the user" - When trying to get a balance for a user without one
- "Failed to update balance: error message" - When a balance update fails

## Additional Information

For more details, see the complete API documentation in `BALANCE_API_DOCUMENTATION.md`. 