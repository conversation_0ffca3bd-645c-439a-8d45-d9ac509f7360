{"info": {"name": "ClassZ Attendance Verification API", "description": "A collection of requests for the ClassZ Attendance Verification API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Generate Attendance Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "body": {"mode": "raw", "raw": "{\n  \"classId\": \"{{classId}}\",\n  \"studentId\": \"{{studentId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/attendance/generate", "host": ["{{baseUrl}}"], "path": ["api", "attendance", "generate"]}, "description": "Generates a unique verification code and QR code for a student attending a specific class."}}, {"name": "Verify Attendance Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "body": {"mode": "raw", "raw": "{\n  \"verificationCode\": \"{{code}}\",\n  \"classId\": \"{{classId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/attendance/verify", "host": ["{{baseUrl}}"], "path": ["api", "attendance", "verify"]}, "description": "Verifies an attendance code entered by a coach and marks the student as present."}}, {"name": "Verify Attendance via QR Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "body": {"mode": "raw", "raw": "{\n  \"qrCodeData\": \"{{qrCodeData}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/attendance/verify-qr", "host": ["{{baseUrl}}"], "path": ["api", "attendance", "verify-qr"]}, "description": "Verifies attendance by scanning a QR code."}}, {"name": "Get Attendance History (by ClassId)", "request": {"method": "GET", "header": [{"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "url": {"raw": "{{baseUrl}}/api/attendance/{{classId}}", "host": ["{{baseUrl}}"], "path": ["api", "attendance", "{{classId}}"]}, "description": "Retrieves attendance history for a specific class."}}, {"name": "Get Attendance History (by ChildId)", "request": {"method": "GET", "header": [{"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "url": {"raw": "{{baseUrl}}/api/attendance/child/{{childId}}", "host": ["{{baseUrl}}"], "path": ["api", "attendance", "child", "{{childId}}"]}, "description": "Retrieves attendance history for a specific student."}}, {"name": "Get Present Attendance", "request": {"method": "GET", "header": [{"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "url": {"raw": "{{baseUrl}}/api/attendance/present/{{classId}}", "host": ["{{baseUrl}}"], "path": ["api", "attendance", "present", "{{classId}}"]}, "description": "Retrieves the list of students who are present in a specific class."}}, {"name": "Get Existing QR Code", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "body": {"mode": "raw", "raw": "{\n  \"classId\": \"{{classId}}\",\n  \"studentId\": \"{{studentId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/attendance/qr", "host": ["{{baseUrl}}"], "path": ["api", "attendance", "qr"]}, "description": "Retrieves an existing QR code for a student in a specific class."}}, {"name": "Mark Attendance Manually", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "auth-token", "value": "{{token}}", "description": "JWT token for authentication"}], "body": {"mode": "raw", "raw": "{\n  \"classId\": \"{{classId}}\",\n  \"studentId\": \"{{studentId}}\",\n  \"status\": \"present\"\n}"}, "url": {"raw": "{{baseUrl}}/api/attendance/mark", "host": ["{{baseUrl}}"], "path": ["api", "attendance", "mark"]}, "description": "Manually marks attendance for a student in a specific class."}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "token", "value": "YOUR_JWT_TOKEN", "type": "string"}, {"key": "classId", "value": "YOUR_CLASS_ID", "type": "string"}, {"key": "studentId", "value": "YOUR_STUDENT_ID", "type": "string"}, {"key": "code", "value": "YOUR_VERIFICATION_CODE", "type": "string"}, {"key": "qrCodeData", "value": "YOUR_QR_CODE_DATA", "type": "string"}, {"key": "childId", "value": "YOUR_CHILD_ID", "type": "string"}]}