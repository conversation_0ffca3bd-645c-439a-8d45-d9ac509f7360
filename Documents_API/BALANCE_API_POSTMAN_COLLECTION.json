{"info": {"name": "Balance API", "description": "A collection of requests for the Balance API in ClassZ Backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Create Balance", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "auth-token", "value": "{{user_token}}", "description": "JWT Token"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{user_id}}\",\n  \"balance\": 0\n}"}, "url": {"raw": "{{base_url}}/api/balance", "host": ["{{base_url}}"], "path": ["api", "balance"]}, "description": "Creates a new balance wallet for a user"}}, {"name": "Get User Balance", "request": {"method": "GET", "header": [{"key": "auth-token", "value": "{{user_token}}", "description": "JWT Token"}], "url": {"raw": "{{base_url}}/api/balance/:userId", "host": ["{{base_url}}"], "path": ["api", "balance", ":userId"], "variable": [{"key": "userId", "value": "{{user_id}}", "description": "ID of the user to retrieve balance for"}]}, "description": "Retrieves the balance for a specific user"}}, {"name": "Update User Balance", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "auth-token", "value": "{{user_token}}", "description": "JWT Token"}], "body": {"mode": "raw", "raw": "{\n  \"balance\": 1000\n}"}, "url": {"raw": "{{base_url}}/api/balance/:userId", "host": ["{{base_url}}"], "path": ["api", "balance", ":userId"], "variable": [{"key": "userId", "value": "{{user_id}}", "description": "ID of the user to update balance for"}]}, "description": "Updates the balance for a specific user"}}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "user_token", "value": "YOUR_JWT_TOKEN", "type": "string"}, {"key": "user_id", "value": "USER_ID_HERE", "type": "string"}]}