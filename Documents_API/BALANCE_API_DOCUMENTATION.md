# Balance API Documentation

This document outlines the API endpoints for managing user coin balances in the ClassZ Backend application.

## Overview

The Balance API allows for the creation, retrieval, and updating of user coin balances. Coins can be used within the application for various features and transactions.

## Authentication

All API requests require authentication using a JW<PERSON> token. Add your token to the request headers:

```
auth-token: YOUR_JWT_TOKEN
```

## API Endpoints

### 1. Create Balance

Creates a new balance wallet for a user.

- **URL**: `/api/balance`
- **Method**: `POST`
- **Authentication**: Required

#### Request Headers

| Header       | Value            | Description                  |
|--------------|------------------|------------------------------|
| Content-Type | application/json | Format of the request body   |
| auth-token   | YOUR_JWT_TOKEN   | JWT token for authentication |

#### Request Body

```json
{
  "userId": "userId123",
  "balance": 0
}
```

| Field   | Type   | Required | Description                                     |
|---------|--------|----------|-------------------------------------------------|
| userId  | string | Yes      | ID of the user to create the balance for        |
| balance | number | No       | Initial balance (defaults to 0 if not provided) |

#### Success Response

- **Code**: 200 OK
- **Content**:

```json
{
  "_id": "balance123",
  "userId": "userId123",
  "balance": 0
}
```

#### Error Responses

- **Code**: 500 Internal Server Error
- **Content**:

```json
"Failed to create balance: error message"
```

### 2. Get User Balance

Retrieves the balance for a specific user.

- **URL**: `/api/balance/:userId`
- **Method**: `GET`
- **Authentication**: Required

#### Request Headers

| Header     | Value          | Description                  |
|------------|----------------|------------------------------|
| auth-token | YOUR_JWT_TOKEN | JWT token for authentication |

#### Request Parameters

| Parameter | Type   | Required | Description               |
|-----------|--------|----------|---------------------------|
| userId    | string | Yes      | ID of the user to look up |

#### Success Response

- **Code**: 200 OK
- **Content**:

```json
{
  "_id": "balance123",
  "userId": "userId123",
  "balance": 500
}
```

#### Error Responses

- **Code**: 500 Internal Server Error
- **Content**:

```json
"Failed to fetch balance: No wallet found for the user"
```

### 3. Update User Balance

Updates the balance for a specific user.

- **URL**: `/api/balance/:userId`
- **Method**: `PUT`
- **Authentication**: Required

#### Request Headers

| Header       | Value            | Description                  |
|--------------|------------------|------------------------------|
| Content-Type | application/json | Format of the request body   |
| auth-token   | YOUR_JWT_TOKEN   | JWT token for authentication |

#### Request Parameters

| Parameter | Type   | Required | Description             |
|-----------|--------|----------|-------------------------|
| userId    | string | Yes      | ID of the user to update |

#### Request Body

```json
{
  "balance": 1000
}
```

| Field   | Type   | Required | Description         |
|---------|--------|----------|---------------------|
| balance | number | Yes      | New balance amount  |

#### Success Response

- **Code**: 200 OK
- **Content**:

```json
{
  "_id": "balance123",
  "userId": "userId123",
  "balance": 1000
}
```

#### Error Responses

- **Code**: 500 Internal Server Error
- **Content**:

```json
"Child not found"
```

or

```json
"Failed to update balance: error message"
```

## Implementation Examples

### Creating a Balance

```javascript
// Example using fetch API
const createBalance = async (userId, initialBalance = 0) => {
  try {
    const response = await fetch('http://localhost:3000/api/balance', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'auth-token': 'YOUR_JWT_TOKEN'
      },
      body: JSON.stringify({
        userId,
        balance: initialBalance
      })
    });
    
    return await response.json();
  } catch (error) {
    console.error('Error creating balance:', error);
  }
};
```

### Getting a User's Balance

```javascript
// Example using fetch API
const getUserBalance = async (userId) => {
  try {
    const response = await fetch(`http://localhost:3000/api/balance/${userId}`, {
      method: 'GET',
      headers: {
        'auth-token': 'YOUR_JWT_TOKEN'
      }
    });
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching balance:', error);
  }
};
```

### Updating a User's Balance

```javascript
// Example using fetch API
const updateUserBalance = async (userId, newBalance) => {
  try {
    const response = await fetch(`http://localhost:3000/api/balance/${userId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'auth-token': 'YOUR_JWT_TOKEN'
      },
      body: JSON.stringify({
        balance: newBalance
      })
    });
    
    return await response.json();
  } catch (error) {
    console.error('Error updating balance:', error);
  }
};
```

## Internal Repository Methods

The following methods are available in the balance repository but are not directly exposed as API endpoints:

- **addBalance**: Adds a specified amount to a user's balance
- **deductBalance**: Deducts a specified amount from a user's balance (checks for sufficient funds)

These methods can be used internally by other parts of the application to manage balance transactions.

## Error Handling

All endpoints return appropriate HTTP status codes and error messages:

- 200 OK: Request successful
- 500 Internal Server Error: Server-side issue or validation error

## Data Model

The balance data model consists of the following fields:

| Field   | Type     | Description                      |
|---------|----------|----------------------------------|
| _id     | ObjectId | MongoDB generated ID             |
| userId  | ObjectId | Reference to the user            |
| balance | Number   | Current coin balance (default: 0)|

## Best Practices

1. **Always validate user existence**: Before performing balance operations, ensure the user exists
2. **Log all balance changes**: Maintain an audit trail of all balance modifications
3. **Handle concurrent updates**: Be cautious of race conditions when multiple operations affect the same balance
4. **Include balance validation**: Ensure balance never goes below zero when performing deductions 