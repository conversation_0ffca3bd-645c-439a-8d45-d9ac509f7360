# ClassZ Chat Backend Optimization Guide

This guide focuses specifically on the chat optimization features we've implemented to improve performance and reliability.

## What Was Fixed

We identified and fixed several key issues that were causing slow performance in the chat system:

1. **Inefficient Message Fetching**: Replaced with cursor-based pagination
2. **Missing Database Indexes**: Added proper indexes for common query patterns
3. **No Conversation Management**: Added a Conversation model with metadata tracking
4. **Poor Socket Connection Management**: Implemented robust connection tracking
5. **No Message Deduplication**: Added server-side deduplication system
6. **Missing Caching Layer**: Added Redis caching with proper invalidation

## Using Redis for Chat Optimization

Redis is a crucial component for a high-performance chat application. Here's how to verify it's working correctly:

### 1. Test Redis Connection

Run the included test script:

```bash
node test-redis.js
```

This will verify that Redis is properly installed and accessible by your application.

### 2. Enable Redis in Production

To enable Redis caching in production, add these settings to your `.env` file:

```
ENABLE_REDIS_CACHE=true
REDIS_HOST=localhost  # Change if Redis is on a different server
REDIS_PORT=6379
# REDIS_USERNAME=  # Uncomment and set if authentication is required
# REDIS_PASSWORD=  # Uncomment and set if authentication is required
```

### 3. Verifying Redis Is Working

When Redis is working correctly, you should see these messages in your logs:

- `Redis connected successfully`
- `Redis client connection established`
- `Cache hit for conversation [id]` (when retrieving cached data)
- `Cached [n] messages for conversation [id]` (when saving to cache)

## Key Optimization Features

### 1. Cursor-Based Pagination

The new pagination system allows efficient message loading without timeouts:

```javascript
// Client request example
socket.emit("fetchMessage", {
  user1: "user1Id", 
  user2: "user2Id",
  type1: "user",
  type2: "center",
  beforeTimestamp: "2023-05-15T12:30:45.123Z", // Optional, for pagination
  limit: 30 // Optional, defaults to 30
});

// Server response includes a cursor for next page
{
  messages: [...],
  nextCursor: "2023-05-15T12:00:12.456Z" // Use this timestamp for the next page
}
```

### 2. Conversation Model

The new Conversation model efficiently tracks metadata and provides quick access to recent conversations:

```javascript
// Get user conversations API
GET /api/chat/conversations/:userId?limit=20&skip=0

// Response includes essential conversation data
{
  "success": true,
  "count": 5,
  "total": 12,
  "data": [
    {
      "conversationId": "user1-center1",
      "recipientId": "centerId",
      "recipientType": "center",
      "name": "Center Name",
      "mainImage": "image_url",
      "lastMessage": "Hello there!",
      "lastMessageSender": "senderId",
      "timestamp": "2023-05-15T18:30:00.000Z",
      "messageCount": 42
    },
    // More conversations...
  ]
}
```

### 3. Message Deduplication

The system now prevents duplicate messages from being processed:

```javascript
// When sending the same message twice within the duplicate window
socket.emit("sendMessage", messageData);
socket.emit("sendMessage", messageData); // Same message data

// The second message will be detected as a duplicate
// Client receives a confirmation with duplicateDetected flag
{
  "success": true,
  "messageId": "clientMessageId",
  "duplicateDetected": true
}
```

## Monitoring Performance

You can monitor chat performance in these ways:

1. **Socket Connection Status**:
   - Check current connections: `Object.keys(activeConnections).length`
   - Monitor connection rate in the logs

2. **Redis Cache Efficiency**:
   - Track cache hit ratio in logs
   - Monitor Redis memory usage: `redis-cli info memory`

3. **Message Delivery Times**:
   - Compare message sent timestamp with delivery timestamp
   - Target: < 100ms delivery time

## Troubleshooting Redis Issues

If you encounter Redis connection errors like:

```
Error invalidating cache: ClientClosedError: The client is closed
```

Verify:

1. Redis is running: `redis-cli ping`
2. Your connection settings match the actual Redis server
3. No firewall is blocking the Redis port
4. Redis has sufficient memory: `redis-cli info memory`

## Reverting to Non-Redis Mode

If Redis is unavailable, the system will automatically fall back to direct database access. You can also explicitly disable Redis:

```
# In .env file
ENABLE_REDIS_CACHE=false
```

## Future Optimizations

Consider these additional optimizations for even better performance:

1. Implement WebSocket connection clustering for horizontal scaling
2. Add message read status tracking with batch updates
3. Implement typing indicators with debouncing
4. Add media message handling with CDN integration
5. Consider socket.io-redis for multi-server deployments

These optimizations have significantly improved the ClassZ Chat backend performance and reliability. 