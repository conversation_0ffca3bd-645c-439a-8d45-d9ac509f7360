/**
 * Redis Setup Script for ClassZ Chat Backend
 * 
 * This script checks if Redis is installed and running.
 * If not, it provides instructions for installing Redis.
 */

const { exec } = require('child_process');
const readline = require('readline');
const fs = require('fs');
const redis = require('redis');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Check if Redis is installed
function checkRedisInstalled() {
  return new Promise((resolve) => {
    console.log('Checking if Redis is installed...');
    
    exec('redis-cli --version', (error) => {
      if (error) {
        console.log('❌ Redis CLI not found. Redis may not be installed.');
        resolve(false);
      } else {
        console.log('✅ Redis CLI detected.');
        resolve(true);
      }
    });
  });
}

// Check if Redis server is running
function checkRedisRunning() {
  return new Promise((resolve) => {
    console.log('Checking if Redis server is running...');
    
    const client = redis.createClient({
      socket: {
        host: 'localhost',
        port: 6379
      }
    });
    
    client.on('connect', async () => {
      console.log('✅ Successfully connected to Redis server.');
      await client.disconnect();
      resolve(true);
    });
    
    client.on('error', async (err) => {
      console.log('❌ Could not connect to Redis server:', err.message);
      resolve(false);
    });
    
    // Connect to Redis
    client.connect().catch(() => {
      // Error is handled by the error event
    });
  });
}

// Show installation instructions
function showInstallationInstructions() {
  console.log('\n===== Redis Installation Instructions =====');
  console.log('\nWindows:');
  console.log('1. Download Redis for Windows from https://github.com/microsoftarchive/redis/releases');
  console.log('2. Install Redis by running the .msi file');
  console.log('3. Start Redis server by running "redis-server" in Command Prompt');
  
  console.log('\nMacOS (using Homebrew):');
  console.log('1. Install Homebrew if not already installed: /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"');
  console.log('2. Install Redis: brew install redis');
  console.log('3. Start Redis: brew services start redis');
  
  console.log('\nLinux (Ubuntu/Debian):');
  console.log('1. Update package list: sudo apt update');
  console.log('2. Install Redis: sudo apt install redis-server');
  console.log('3. Start Redis: sudo systemctl start redis-server');
  
  console.log('\nOnce Redis is installed and running, run this script again to verify the setup.');
  console.log('=====================================\n');
}

// Update .env file to enable Redis caching
function updateEnvFile() {
  return new Promise((resolve) => {
    const envPath = './.env';
    
    // Check if .env file exists
    if (!fs.existsSync(envPath)) {
      console.log('Creating .env file...');
      fs.writeFileSync(envPath, 'ENABLE_REDIS_CACHE=true\nREDIS_HOST=localhost\nREDIS_PORT=6379\n');
      console.log('✅ Created .env file with Redis configuration.');
      return resolve();
    }
    
    // Read existing .env file
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    // Check if Redis config already exists
    if (envContent.includes('ENABLE_REDIS_CACHE=')) {
      // Update existing config
      const updatedContent = envContent
        .replace(/ENABLE_REDIS_CACHE=.*\n/, 'ENABLE_REDIS_CACHE=true\n')
        .replace(/REDIS_HOST=.*\n/, 'REDIS_HOST=localhost\n')
        .replace(/REDIS_PORT=.*\n/, 'REDIS_PORT=6379\n');
      
      fs.writeFileSync(envPath, updatedContent);
      console.log('✅ Updated existing Redis configuration in .env file.');
    } else {
      // Add Redis config
      const updatedContent = envContent + '\n# Redis Configuration\nENABLE_REDIS_CACHE=true\nREDIS_HOST=localhost\nREDIS_PORT=6379\n';
      fs.writeFileSync(envPath, updatedContent);
      console.log('✅ Added Redis configuration to .env file.');
    }
    
    resolve();
  });
}

// Main function
async function main() {
  console.log('=======================================');
  console.log('ClassZ Chat Backend - Redis Setup Tool');
  console.log('=======================================\n');
  
  const isInstalled = await checkRedisInstalled();
  
  if (isInstalled) {
    const isRunning = await checkRedisRunning();
    
    if (isRunning) {
      console.log('\n✅ Redis is installed and running!');
      await updateEnvFile();
      console.log('\n🎉 Setup complete! Your chat backend is now configured to use Redis caching.');
      console.log('Restart your server to apply these changes.');
    } else {
      console.log('\n⚠️ Redis is installed but not running. Please start Redis server first.');
      console.log('Run this script again after starting Redis to complete the setup.');
      showInstallationInstructions();
    }
  } else {
    console.log('\n⚠️ Redis is not installed or not in your PATH.');
    showInstallationInstructions();
  }
  
  rl.close();
}

main().catch(console.error); 