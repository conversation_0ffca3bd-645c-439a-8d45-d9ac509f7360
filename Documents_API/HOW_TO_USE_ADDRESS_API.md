# How to Use the Address API

This guide provides instructions on how to test and use the Address API with mapping functionality.

## Prerequisites

Before you begin, make sure you have:

1. Node.js installed (v14+ recommended)
2. MongoDB running
3. The backend server running
4. A valid user ID and auth token for testing

## Running the Test Script

The test script allows you to verify that all address API endpoints are working correctly.

1. Open the `test_address_api.js` file and update the configuration variables:
   ```javascript
   const API_BASE_URL = 'http://localhost:3000/api/address'; // Change if your server runs on a different port
   const AUTH_TOKEN = 'your_auth_token'; // Replace with a valid JWT token
   const TEST_USER_ID = '6464e5b1f8b95a001db5c3e9'; // Replace with an existing user ID
   ```

2. Install the required dependency:
   ```bash
   npm install axios
   ```

3. Run the test script:
   ```bash
   node test_address_api.js
   ```

4. The script will execute a series of tests and print the results to the console:
   - Fetching all addresses
   - Adding a new address
   - Getting an address with map data
   - Getting a specific address with map data
   - Updating an address
   - Getting the default address
   - Setting a default address
   - Deleting an address (commented out by default)

5. If you want to delete the test address after testing, uncomment the `await deleteAddress()` line in the `runTests()` function.

## Using the Demo Interface

We've provided a HTML demo interface to interact with the Address API visually.

1. Open the `address_demo.html` file and update the Google Maps API key:
   ```html
   <script src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap" async defer></script>
   ```

2. If your backend server is not running on localhost:3000, update the API base URL:
   ```javascript
   const API_BASE_URL = 'http://localhost:3000/api/address';
   ```

3. Open the HTML file in a browser.

4. Enter your User ID and Auth Token in the form fields at the top.

5. Click "Load Addresses" to fetch and display existing addresses.

6. Use the map functionality:
   - Click on the map to set coordinates for a new address
   - View existing addresses on the map by clicking "View on Map"
   - Addresses with coordinates will display markers on the map

7. Add a new address:
   - Fill in the address form (all fields except coordinates are required)
   - Optionally set as default address
   - Click "Add Address"

8. Manage existing addresses:
   - View an address on the map
   - Edit an address
   - Set an address as default
   - Delete an address

## API Endpoints Quick Reference

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/user/:userId` | GET | Get all addresses for a user |
| `/user/:userId/default` | GET | Get default address for a user |
| `/user/:userId` | POST | Add a new address |
| `/user/:userId/:addressId` | PUT | Update an address |
| `/user/:userId/:addressId` | DELETE | Delete an address |
| `/user/:userId/:addressId/default` | PUT | Set an address as default |
| `/user/:userId/map` | GET | Get address with map data |
| `/user/:userId/:addressId/map` | GET | Get specific address with map data |

For detailed API documentation, refer to the `ADDRESS_API_DOCUMENTATION.md` file.

## Integration with Flutter

To integrate with a Flutter frontend:

1. Create API service classes to handle HTTP requests to these endpoints
2. Set up models that match the JSON response structure
3. Use Google Maps for Flutter to display the address coordinates

Example API service class in Flutter:

```dart
import 'package:http/http.dart' as http;
import 'dart:convert';

class AddressService {
  final String baseUrl;
  final String token;

  AddressService({
    required this.token,
    this.baseUrl = 'https://your-server.com/api/address',
  });

  Future<Map<String, dynamic>> getAllAddresses(String userId) async {
    final response = await http.get(
      Uri.parse('$baseUrl/user/$userId'),
      headers: {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to load addresses: ${response.body}');
    }
  }

  Future<Map<String, dynamic>> getAddressWithMap(String userId, [String? addressId]) async {
    final endpoint = addressId != null 
      ? '$baseUrl/user/$userId/$addressId/map'
      : '$baseUrl/user/$userId/map';
      
    final response = await http.get(
      Uri.parse(endpoint),
      headers: {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to load address map data: ${response.body}');
    }
  }

  Future<Map<String, dynamic>> addAddress(String userId, Map<String, dynamic> addressData) async {
    final response = await http.post(
      Uri.parse('$baseUrl/user/$userId'),
      headers: {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      },
      body: json.encode(addressData),
    );

    if (response.statusCode == 201) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to add address: ${response.body}');
    }
  }

  // Implement other methods as needed (update, delete, set default, etc.)
}
```

## Troubleshooting

If you encounter issues:

1. **Authentication Errors**: Make sure your auth token is valid and not expired
2. **User Not Found**: Verify that the user ID is correct
3. **CORS Issues**: If testing from a browser, ensure your server has proper CORS headers
4. **Map Not Loading**: Check that your Google Maps API key is valid and has the required permissions
5. **API URLs**: Ensure the API base URL is correct and the server is running

For additional help, refer to the error messages in the console. 