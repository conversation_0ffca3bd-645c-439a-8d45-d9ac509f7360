# Redis Integration: Step-by-Step Implementation Guide

## Overview

This document provides a detailed, step-by-step guide for implementing the Redis integration between the ClassZ Flutter app and the optimized backend. Follow these steps in order to ensure a smooth integration process.

## Prerequisites

- Ensure the backend is properly configured with Redis as described in the `REDIS_SETUP_CHECKLIST.md`
- The Flutter app should be updated to the latest version of the codebase
- Understanding of the current chat architecture as outlined in the `INTEGRATION_PLAN.md`

## Step 1: Update Data Models

### 1.1 Update ChatModel

Update the `ChatModel` class in `lib/features/chats/data/models/chatModel.dart` to support the new Redis features:

```dart
class ChatModel extends HiveObject {
  @HiveField(0)
  String? id;
  
  @HiveField(1)
  String? tempId;
  
  @HiveField(2)
  String? message;
  
  @HiveField(3)
  String? sender;
  
  @HiveField(4)
  String? recipient;
  
  @HiveField(5)
  String? senderModel;
  
  @HiveField(6)
  String? recipientModel;
  
  @HiveField(7)
  DateTime? timestamp;
  
  // Add new fields
  @HiveField(8)
  String? status; // 'sent', 'delivered', 'read'
  
  @HiveField(9)
  String? conversationId;
  
  ChatModel({
    this.id,
    this.tempId,
    this.message,
    this.sender,
    this.recipient,
    this.senderModel,
    this.recipientModel,
    this.timestamp,
    this.status,
    this.conversationId,
  });
  
  // Update factory constructor
  factory ChatModel.fromJson(Map<String, dynamic> json) {
    // Create conversation ID if not provided
    String? convId = json['conversationId'];
    if (convId == null && json['sender'] != null && json['recipient'] != null) {
      final List<String> users = [json['sender'], json['recipient']];
      users.sort();
      convId = users.join('-');
    }
    
    return ChatModel(
      id: json['_id'] ?? json['id'],
      tempId: json['tempId'] ?? json['clientMessageId'],
      message: json['message'],
      sender: json['sender'],
      recipient: json['recipient'],
      senderModel: json['senderModel'],
      recipientModel: json['recipientModel'],
      timestamp: json['timestamp'] != null 
        ? DateTime.parse(json['timestamp'])
        : DateTime.now(),
      status: json['status'] ?? 'sent',
      conversationId: convId,
    );
  }
  
  // Update toJson
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tempId': tempId,
      'message': message,
      'sender': sender,
      'recipient': recipient,
      'senderModel': senderModel,
      'recipientModel': recipientModel,
      'timestamp': timestamp?.toIso8601String(),
      'status': status ?? 'sent',
      'conversationId': conversationId,
    };
  }
}
```

### 1.2 Update Hive Adapter

Register the new fields in the Hive adapter:

```dart
class ChatModelAdapter extends TypeAdapter<ChatModel> {
  @override
  final typeId = 50; // Keep the same typeId

  @override
  ChatModel read(BinaryReader reader) {
    // Read all fields including the new ones
    // ...
    return ChatModel(
      // Existing fields
      status: reader.readString(),      // New: index 8
      conversationId: reader.readString(), // New: index 9
    );
  }

  @override
  void write(BinaryWriter writer, ChatModel obj) {
    // Write all fields including the new ones
    // ...
    writer.writeString(obj.status ?? 'sent');      // New: index 8
    writer.writeString(obj.conversationId ?? '');  // New: index 9
  }
}
```

## Step 2: Update Data Sources

### 2.1 Enhance SocketDataSource

Update `lib/features/chats/data/data_sources/chat_data_sources.dart` to support Redis features:

```dart
class SocketDataSource {
  // Existing properties
  String? _lastCursor; // New: For pagination
  
  // Update connect method
  Future<void> connect(String userId) async {
    // Existing connection code
    socket = socket_io.io(serverUrl, <String, dynamic>{
      'transports': ['websocket'],
      'query': {'userId': userId},
      'reconnection': true,
      'reconnectionDelay': 1000,
      'reconnectionAttempts': 3,
      'timeout': 8000 // 8 seconds timeout
    });
    
    // Add new event handlers
    socket?.on('messageStatusUpdate', (data) {
      _handleMessageStatusUpdate(data);
    });
    
    socket?.on('messagesRead', (data) {
      _handleMessagesRead(data);
    });
    
    // Update messageConfirmation handler
    socket?.on('messageConfirmation', (data) {
      final messageId = data['messageId'];
      final success = data['success'] ?? false;
      final status = data['status'] ?? 'sent';
      final duplicateDetected = data['duplicateDetected'] ?? false;
      
      if (duplicateDetected) {
        print('Server detected duplicate message: $messageId');
      }
      
      print('Message confirmation - ID: $messageId, Status: $status, Success: $success');
    });
  }
  
  // Add method for sending read receipts
  void markMessagesAsRead(List<String> messageIds) {
    if (!_isConnected || socket == null) {
      print('Cannot mark messages as read: Socket not connected');
      return;
    }
    
    socket!.emit('messageRead', {'messageIds': messageIds});
  }
  
  // Add method to handle message status updates
  void _handleMessageStatusUpdate(Map<String, dynamic> data) {
    final messageId = data['messageId'];
    final status = data['status'];
    
    if (messageId != null && status != null) {
      print('Message status update - ID: $messageId, Status: $status');
      // You could emit an event to notify listeners of the status change
    }
  }
  
  // Add method to handle messages read notification
  void _handleMessagesRead(Map<String, dynamic> data) {
    final messageIds = data['messageIds'] as List<dynamic>?;
    
    if (messageIds != null && messageIds.isNotEmpty) {
      print('Messages marked as read: ${messageIds.length} messages');
      // You could emit an event to notify listeners of the read status
    }
  }
  
  // Update fetch messages method to support cursor-based pagination
  Future<List<ChatModel>> fetchMessages(Map<String, dynamic> payload) async {
    // Existing code
    
    // Add support for cursor-based pagination
    if (payload.containsKey('beforeTimestamp')) {
      // Use the provided timestamp for pagination
      print('Fetching messages before timestamp: ${payload['beforeTimestamp']}');
    } else if (_lastCursor != null && payload.containsKey('useCursor') && payload['useCursor'] == true) {
      // Use the stored cursor for pagination if requested
      payload['beforeTimestamp'] = _lastCursor;
      print('Using stored cursor for pagination: $_lastCursor');
    }
    
    // Update fetchedMessage handler
    socket!.once('fetchedMessage', (data) {
      try {
        // Handle the new response format with cursor
        if (data is Map && data.containsKey('messages') && data.containsKey('nextCursor')) {
          final messages = data['messages'] as List;
          final nextCursor = data['nextCursor'] as String?;
          
          // Store the cursor for potential future pagination
          if (nextCursor != null) {
            _lastCursor = nextCursor;
            print('Stored cursor for pagination: $nextCursor');
          }
          
          // Process messages as before
        } else {
          // Handle legacy format
        }
      } catch (e) {
        // Error handling
      }
    });
  }
}
```

### 2.2 Update HttpDataSource

Modify `lib/features/chats/data/data_sources/chat_http_data_source.dart` to use the new Redis-cached endpoints:

```dart
class HttpDataSource {
  Future<List<LastMessageModel>> getLastMessages(String id) async {
    try {
      // Use the new conversations endpoint that leverages Redis caching
      String uri = "${AppText.device}/api/chat/conversations/$id";
      var response = await http.get(Uri.parse(uri));

      if (response.statusCode >= 200 && response.statusCode <= 300) {
        Map<String, dynamic> jsonData = jsonDecode(response.body);
        
        if (jsonData['success'] == true && jsonData['data'] != null) {
          // Parse the cached conversation data
          List<dynamic> conversationList = jsonData['data'];
          
          print('Received ${conversationList.length} conversations from Redis-cached endpoint');
          
          // Map to LastMessageModel format
          return conversationList.map((conv) => LastMessageModel(
            id: conv['recipientId'],
            oppositeModel: conv['recipientType'],
            name: conv['name'],
            mainImage: conv['mainImage'],
            lastMessage: conv['lastMessage'],
            lastMessageTime: DateTime.parse(conv['timestamp']),
          )).toList();
        }
      }
      throw Exception('Failed to fetch conversations');
    } catch (e) {
      print('Error in GetLastMessages: $e');
      return []; // Return empty list on error instead of throwing
    }
  }
}
```

## Step 3: Update Chat UI

### 3.1 Add Message Status Indicators

Update the message rendering in the chat UI to show message status:

```dart
Widget _buildMessageStatus(ChatModel message) {
  // Only show status for sent messages (not received)
  if (message.sender != widget.data['senderId']) {
    return SizedBox.shrink();
  }
  
  IconData statusIcon;
  Color statusColor;
  
  switch (message.status) {
    case 'sent':
      statusIcon = Icons.check;
      statusColor = Colors.grey;
      break;
    case 'delivered':
      statusIcon = Icons.done_all;
      statusColor = Colors.grey;
      break;
    case 'read':
      statusIcon = Icons.done_all;
      statusColor = Colors.blue;
      break;
    default:
      statusIcon = Icons.access_time;
      statusColor = Colors.grey;
  }
  
  return Icon(
    statusIcon,
    size: 12,
    color: statusColor,
  );
}

// Use this in your message bubble widget
Row(
  mainAxisSize: MainAxisSize.min,
  children: [
    Flexible(
      child: Text(message.message ?? ''),
    ),
    SizedBox(width: 4),
    _buildMessageStatus(message),
  ],
)
```

### 3.2 Implement Mark as Read Functionality

Add code to mark messages as read when they are viewed:

```dart
void _markMessagesAsRead() {
  // Find messages that are not from the current user and not marked as read
  final unreadMessages = messages.where((msg) => 
    msg.sender != senderId && 
    msg.status != 'read' &&
    msg.id != null
  ).toList();
  
  if (unreadMessages.isEmpty) return;
  
  // Get the IDs of these messages
  final messageIds = unreadMessages.map((msg) => msg.id!).toList();
  
  // Call the repository to mark these as read
  try {
    // Get the data source through DI
    final socketDataSource = GetIt.instance.get<SocketDataSource>();
    socketDataSource.markMessagesAsRead(messageIds);
    
    // Update local status
    for (var msg in unreadMessages) {
      msg.status = 'read';
    }
    
    // Save updated status to local storage
    for (var msg in unreadMessages) {
      _chatLocalRepository.saveMessage(msg);
    }
  } catch (e) {
    print('Error marking messages as read: $e');
  }
}

// Call this when viewing messages, e.g., in initState and when receiving new messages
@override
void initState() {
  super.initState();
  // Other initialization code
  
  // Add post-frame callback to mark messages as read after UI is built
  WidgetsBinding.instance.addPostFrameCallback((_) {
    if (mounted && !_disposed) {
      _markMessagesAsRead();
    }
  });
}
```

### 3.3 Implement Pagination UI

Add support for loading more messages when scrolling to the top:

```dart
bool _isLoadingMoreMessages = false;

Widget _buildMessageList() {
  return NotificationListener<ScrollNotification>(
    onNotification: (ScrollNotification scrollInfo) {
      // Detect when user scrolls to the top
      if (scrollInfo.metrics.pixels == scrollInfo.metrics.minScrollExtent && 
          !_isLoadingMoreMessages) {
        _loadMoreMessages();
      }
      return true;
    },
    child: ListView.builder(
      // Existing list view code
    ),
  );
}

void _loadMoreMessages() async {
  if (_lastCursor == null) return; // No more messages to load
  
  setState(() {
    _isLoadingMoreMessages = true;
  });
  
  try {
    // Create a consistent conversation ID
    final List<String> users = [senderId, oppositeId];
    users.sort();
    final String conversationId = users.join('-');
    
    // Create payload with the cursor timestamp
    final payload = {
      'user1': senderId,
      'user2': oppositeId,
      'type1': widget.data['senderType'] ?? 'parent',
      'type2': widget.data['oppositeModel'] ?? 'center',
      'limit': 20,
      'beforeTimestamp': _lastCursor,
      'conversationId': conversationId,
    };
    
    // Fetch older messages
    _chatBloc.add(ChatFetchMessagesEvent(payload));
  } catch (e) {
    print('Error loading more messages: $e');
  } finally {
    setState(() {
      _isLoadingMoreMessages = false;
    });
  }
}
```

## Step 4: Implement Error Handling for Redis

Add Redis-specific error handling and fallback mechanisms:

```dart
// In SocketDataSource
socket?.onConnectError((error) {
  print('Socket connection error: $error');
  
  // Check for potential Redis-related errors
  if (error.toString().contains('Redis') || error.toString().contains('cache')) {
    print('Potential Redis-related error, server might be operating without caching');
    // Connection can still work without Redis, just slower
    // Don't treat this as a fatal connection error
  } else {
    // Handle as normal connection error
    _isConnected = false;
    _messageController?.addError(error);
  }
});

// In fetchMessages method
Future<List<ChatModel>> fetchMessages(Map<String, dynamic> payload) async {
  try {
    // Existing fetch code
  } catch (e) {
    print('Error fetching messages from server: $e');
    
    // If server error contains Redis-related information
    if (e.toString().contains('Redis') || e.toString().contains('cache')) {
      print('Redis-related error, falling back to local storage');
      
      // Extract user IDs from payload
      final String? user1 = payload['user1'];
      final String? user2 = payload['user2'];
      
      if (user1 != null && user2 != null) {
        // Try to load from local storage as fallback
        try {
          final localRepo = GetIt.instance.get<ChatLocalRepository>();
          return await localRepo.loadMessages(user1, user2);
        } catch (localError) {
          print('Error accessing local storage fallback: $localError');
        }
      }
    }
    
    // Re-throw if not a Redis-specific error or can't load locally
    throw e;
  }
}
```

## Step 5: Testing

### 5.1 Basic Connection Test

Test the basic connection to the Redis-enabled backend:

```dart
void testConnection() async {
  try {
    final socketDataSource = GetIt.instance.get<SocketDataSource>();
    await socketDataSource.connect('test-user-id');
    print('Successfully connected to Redis-enabled backend');
  } catch (e) {
    print('Error connecting to Redis-enabled backend: $e');
  }
}
```

### 5.2 Fetch Messages Test

Test fetching messages with the new cursor-based pagination:

```dart
void testFetchMessages() async {
  try {
    final socketDataSource = GetIt.instance.get<SocketDataSource>();
    await socketDataSource.connect('test-user-id');
    
    final payload = {
      'user1': 'test-user-id',
      'user2': 'test-recipient-id',
      'type1': 'user',
      'type2': 'center',
      'limit': 20,
    };
    
    final messages = await socketDataSource.fetchMessages(payload);
    print('Successfully fetched ${messages.length} messages');
    print('Next cursor: ${socketDataSource._lastCursor}');
  } catch (e) {
    print('Error fetching messages: $e');
  }
}
```

## Step 6: Monitoring Integration

Add monitoring to track Redis performance improvements:

```dart
class RedisPerformanceMonitor {
  static Stopwatch _stopwatch = Stopwatch();
  static Map<String, List<int>> _timings = {};
  
  static void startOperation(String operation) {
    _stopwatch.reset();
    _stopwatch.start();
  }
  
  static void endOperation(String operation) {
    _stopwatch.stop();
    final duration = _stopwatch.elapsedMilliseconds;
    
    if (!_timings.containsKey(operation)) {
      _timings[operation] = [];
    }
    
    _timings[operation]!.add(duration);
    print('$operation completed in ${duration}ms');
  }
  
  static Map<String, dynamic> getStats() {
    Map<String, dynamic> stats = {};
    
    _timings.forEach((operation, durations) {
      if (durations.isEmpty) return;
      
      final avg = durations.reduce((a, b) => a + b) / durations.length;
      final min = durations.reduce((a, b) => a < b ? a : b);
      final max = durations.reduce((a, b) => a > b ? a : b);
      
      stats[operation] = {
        'avg': avg,
        'min': min,
        'max': max,
        'count': durations.length,
      };
    });
    
    return stats;
  }
  
  static void reset() {
    _timings.clear();
  }
}

// Usage example
Future<List<ChatModel>> fetchMessages(Map<String, dynamic> payload) async {
  RedisPerformanceMonitor.startOperation('fetchMessages');
  try {
    // Existing fetch code
    return result;
  } finally {
    RedisPerformanceMonitor.endOperation('fetchMessages');
  }
}
```

## Deployment

### Staged Rollout

It's recommended to deploy the Redis integration in stages:

1. **Development Testing**: Test with a development backend with Redis enabled
2. **Alpha Release**: Deploy to internal testers with monitoring enabled
3. **Beta Release**: Extended to a small group of real users
4. **Full Rollout**: Deploy to all users once stability is confirmed

### Feature Flag

Implement a feature flag to control the Redis features:

```dart
class FeatureFlags {
  static bool useRedisCaching = true;
  
  static Future<void> initialize() async {
    // Load flags from remote config or local storage
  }
}

// Usage example
if (FeatureFlags.useRedisCaching) {
  // Use Redis-optimized code path
} else {
  // Use fallback path
}
```

## Conclusion

Following these steps will enable the ClassZ Flutter app to fully integrate with the Redis-optimized backend. The integration provides significant performance improvements while maintaining backward compatibility with the existing functionality. 