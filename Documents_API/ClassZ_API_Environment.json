{"id": "d8b3df15-d41c-4e6a-98b7-c847c60c0a7a", "name": "ClassZ API Environment", "values": [{"key": "base_url", "value": "http://localhost:3000", "type": "default", "enabled": true}, {"key": "email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "password", "value": "your_password", "type": "secret", "enabled": true}, {"key": "otp", "value": "", "type": "default", "enabled": true}, {"key": "token", "value": "", "type": "secret", "enabled": true}, {"key": "userId", "value": "", "type": "default", "enabled": true}, {"key": "addressId", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2023-04-15T12:00:00.000Z", "_postman_exported_using": "Postman/9.1.1"}