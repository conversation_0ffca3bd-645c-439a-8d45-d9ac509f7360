/**
 * Redis Health Check Script for ClassZ Backend
 * 
 * This script performs comprehensive diagnostics on your Redis setup:
 * 1. Tests basic connectivity
 * 2. Verifies cache operations (set/get/delete)
 * 3. Tests conversation and message caching functions
 * 4. Checks for common configuration issues
 * 
 * Usage: node redis-health-check.js [--verbose]
 */

const redis = require('redis');
const dotenv = require('dotenv');
const { promisify } = require('util');
const fs = require('fs');
const path = require('path');

// Load environment variables
dotenv.config();

// Configuration
const REDIS_CONFIG = {
  socket: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379
  },
  username: process.env.REDIS_USERNAME,
  password: process.env.REDIS_PASSWORD
};

// Check if verbose logging is enabled
const VERBOSE = process.argv.includes('--verbose');

// Mock conversation and message data for testing
const TEST_CONVERSATION_ID = 'test-conversation-123';
const TEST_USER_ID = 'test-user-456';
const TEST_MESSAGE = {
  messages: [
    {
      _id: 'msg123',
      message: 'Test message',
      sender: TEST_USER_ID,
      recipient: 'recipient789',
      timestamp: new Date().toISOString(),
      status: 'sent'
    }
  ],
  nextCursor: null
};

const TEST_CONVERSATION = {
  conversations: [
    {
      conversationId: TEST_CONVERSATION_ID,
      recipientId: 'recipient789',
      recipientType: 'user',
      name: 'Test User',
      lastMessage: 'Test message',
      timestamp: new Date().toISOString()
    }
  ],
  total: 1
};

// Color codes for terminal output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Print colored status messages
function logStatus(status, message) {
  const statusColors = {
    'SUCCESS': colors.green,
    'ERROR': colors.red,
    'WARNING': colors.yellow,
    'INFO': colors.blue,
    'CHECKING': colors.cyan,
    'SKIPPED': colors.magenta
  };
  
  console.log(`[${statusColors[status] || ''}${status}${colors.reset}] ${message}`);
}

// Verbose logging helper
function logVerbose(message) {
  if (VERBOSE) {
    console.log(`${colors.cyan}[VERBOSE]${colors.reset} ${message}`);
  }
}

// Check if Redis cache is enabled
function checkRedisEnabled() {
  logStatus('CHECKING', 'Verifying Redis cache is enabled in configuration...');
  
  if (process.env.ENABLE_REDIS_CACHE !== 'true') {
    logStatus('WARNING', 'Redis caching is disabled in .env file (ENABLE_REDIS_CACHE != true)');
    return false;
  }
  
  logStatus('SUCCESS', 'Redis caching is enabled in configuration');
  return true;
}

// Load and test Redis cache utility functions
async function testRedisCacheFunctions() {
  try {
    logStatus('CHECKING', 'Testing if redisCache.js functions can be imported...');
    
    // Path to the Redis cache utility file
    const redisCachePath = path.join(__dirname, 'src', 'utils', 'redisCache.js');
    
    // Check if the file exists
    if (!fs.existsSync(redisCachePath)) {
      logStatus('ERROR', `Redis cache utility file not found at: ${redisCachePath}`);
      return false;
    }
    
    // Try to import the functions
    const {
      initRedis,
      cacheMessages,
      getCachedMessages,
      cacheConversations,
      getCachedConversations,
      invalidateConversationCache,
      invalidateUserConversationsCache
    } = require('./src/utils/redisCache');
    
    if (!initRedis || !cacheMessages || !getCachedMessages || 
        !cacheConversations || !getCachedConversations || 
        !invalidateConversationCache || !invalidateUserConversationsCache) {
      logStatus('ERROR', 'Some Redis cache functions are missing or undefined');
      return false;
    }
    
    logStatus('SUCCESS', 'All Redis cache functions imported successfully');
    return {
      initRedis,
      cacheMessages,
      getCachedMessages,
      cacheConversations,
      getCachedConversations,
      invalidateConversationCache,
      invalidateUserConversationsCache
    };
  } catch (error) {
    logStatus('ERROR', `Failed to import Redis cache functions: ${error.message}`);
    console.error(error);
    return false;
  }
}

// Test basic Redis connectivity
async function testRedisConnection() {
  logStatus('CHECKING', 'Testing basic Redis connectivity...');
  
  try {
    // Create Redis client
    const client = redis.createClient(REDIS_CONFIG);
    
    // Set up event handlers
    client.on('error', (err) => {
      logVerbose(`Redis client error: ${err.message}`);
    });
    
    // Connect to Redis
    await client.connect();
    logStatus('SUCCESS', 'Successfully connected to Redis server');
    
    // Test basic operations
    logVerbose('Testing basic SET operation...');
    await client.set('health-check-test-key', 'test-value');
    
    logVerbose('Testing basic GET operation...');
    const value = await client.get('health-check-test-key');
    
    if (value !== 'test-value') {
      logStatus('ERROR', `Expected 'test-value' but got '${value}'`);
      await client.disconnect();
      return false;
    }
    
    logVerbose('Testing basic DEL operation...');
    await client.del('health-check-test-key');
    
    logStatus('SUCCESS', 'Basic Redis operations (SET/GET/DEL) working correctly');
    
    // Disconnect
    await client.disconnect();
    return true;
  } catch (error) {
    logStatus('ERROR', `Redis connection test failed: ${error.message}`);
    if (error.message.includes('ECONNREFUSED')) {
      logStatus('INFO', 'Check if Redis server is running on the configured host and port');
    } else if (error.message.includes('WRONGPASS')) {
      logStatus('INFO', 'Authentication failed. Check your Redis password');
    }
    return false;
  }
}

// Test the Redis cache functions
async function testCachingFunctions(redisFunctions) {
  if (!redisFunctions) {
    logStatus('SKIPPED', 'Skipping cache function tests as imports failed');
    return false;
  }
  
  try {
    logStatus('CHECKING', 'Testing Redis caching functions...');
    
    // Initialize Redis
    const client = await redisFunctions.initRedis();
    
    if (!client) {
      logStatus('ERROR', 'Failed to initialize Redis client');
      return false;
    }
    
    // Test message caching
    logVerbose('Testing message caching...');
    await redisFunctions.cacheMessages(TEST_CONVERSATION_ID, TEST_MESSAGE);
    
    // Get cached messages
    const cachedMessages = await redisFunctions.getCachedMessages(TEST_CONVERSATION_ID);
    
    if (!cachedMessages || !cachedMessages.messages || cachedMessages.messages.length !== 1) {
      logStatus('ERROR', 'Failed to retrieve cached messages or unexpected format');
      logVerbose(`Cached messages: ${JSON.stringify(cachedMessages)}`);
      await client.disconnect();
      return false;
    }
    
    // Test conversation caching
    logVerbose('Testing conversation caching...');
    await redisFunctions.cacheConversations(TEST_USER_ID, TEST_CONVERSATION);
    
    // Get cached conversations
    const cachedConversations = await redisFunctions.getCachedConversations(TEST_USER_ID);
    
    if (!cachedConversations || !cachedConversations.conversations || cachedConversations.conversations.length !== 1) {
      logStatus('ERROR', 'Failed to retrieve cached conversations or unexpected format');
      logVerbose(`Cached conversations: ${JSON.stringify(cachedConversations)}`);
      await client.disconnect();
      return false;
    }
    
    // Test cache invalidation
    logVerbose('Testing cache invalidation...');
    await redisFunctions.invalidateConversationCache(TEST_CONVERSATION_ID);
    
    // Verify invalidation
    const invalidatedMessages = await redisFunctions.getCachedMessages(TEST_CONVERSATION_ID);
    
    if (invalidatedMessages !== null) {
      logStatus('WARNING', 'Cache invalidation did not clear conversation messages');
    }
    
    // Test user conversations cache invalidation
    await redisFunctions.invalidateUserConversationsCache(TEST_USER_ID);
    
    // Verify user cache invalidation
    const invalidatedUserConversations = await redisFunctions.getCachedConversations(TEST_USER_ID);
    
    if (invalidatedUserConversations !== null) {
      logStatus('WARNING', 'Cache invalidation did not clear user conversations');
    }
    
    logStatus('SUCCESS', 'Redis caching functions are working correctly');
    
    // Clean up
    await client.disconnect();
    return true;
  } catch (error) {
    logStatus('ERROR', `Redis caching function test failed: ${error.message}`);
    console.error(error);
    return false;
  }
}

// Check for common configuration issues
function checkConfigurationIssues() {
  logStatus('CHECKING', 'Checking for common configuration issues...');
  
  // Check Redis host
  if (!process.env.REDIS_HOST) {
    logStatus('WARNING', 'REDIS_HOST not set in .env file, using default (localhost)');
  } else {
    logStatus('INFO', `REDIS_HOST is set to: ${process.env.REDIS_HOST}`);
  }
  
  // Check Redis port
  if (!process.env.REDIS_PORT) {
    logStatus('WARNING', 'REDIS_PORT not set in .env file, using default (6379)');
  } else {
    logStatus('INFO', `REDIS_PORT is set to: ${process.env.REDIS_PORT}`);
    
    // Check if port is a valid number
    if (isNaN(parseInt(process.env.REDIS_PORT))) {
      logStatus('ERROR', `REDIS_PORT is not a valid number: ${process.env.REDIS_PORT}`);
    }
  }
  
  // Check for auth settings
  if (process.env.REDIS_USERNAME || process.env.REDIS_PASSWORD) {
    logStatus('INFO', 'Redis authentication is configured');
    
    if (process.env.REDIS_USERNAME && !process.env.REDIS_PASSWORD) {
      logStatus('WARNING', 'REDIS_USERNAME is set but REDIS_PASSWORD is missing');
    }
  } else {
    logStatus('INFO', 'Redis authentication is not configured (running without auth)');
  }
  
  // Other configuration checks
  if (process.env.NODE_ENV === 'production' && !process.env.REDIS_PASSWORD) {
    logStatus('WARNING', 'Running in production without Redis authentication is not recommended');
  }
  
  return true;
}

// Diagnose common issues based on test results
function provideDiagnostics(results) {
  console.log('\n=== Diagnostics ===');
  
  if (!results.redisEnabled) {
    logStatus('WARNING', 'Redis is disabled in your configuration');
    console.log('To enable Redis, set ENABLE_REDIS_CACHE=true in your .env file');
    return;
  }
  
  if (!results.connectionTest) {
    logStatus('ERROR', 'Redis connection failed');
    console.log('\nPossible solutions:');
    console.log('1. Make sure Redis server is installed and running');
    console.log('2. Check that the Redis host and port are correct in .env');
    console.log('3. Verify network connectivity to the Redis server');
    console.log('4. Check if Redis requires authentication (username/password)');
    return;
  }
  
  if (!results.cacheFunctionsImported) {
    logStatus('ERROR', 'Redis cache functions could not be imported');
    console.log('\nPossible solutions:');
    console.log('1. Check that src/utils/redisCache.js exists and is correctly implemented');
    console.log('2. Verify that all required functions are exported from the file');
    return;
  }
  
  if (!results.cacheFunctionsTest) {
    logStatus('ERROR', 'Redis cache functions test failed');
    console.log('\nPossible solutions:');
    console.log('1. Check for errors in the implementation of cache functions');
    console.log('2. Verify that Redis is properly storing and retrieving JSON data');
    console.log('3. Look for type conversion issues in the cache functions');
    return;
  }
  
  logStatus('SUCCESS', 'All Redis tests passed successfully!');
  console.log('\nYour Redis configuration appears to be working correctly.');
}

// Check type handling in Redis cache functions
function checkTypeHandling() {
  logStatus('CHECKING', 'Analyzing redisCache.js for potential type conversion issues...');
  
  try {
    // Path to the Redis cache utility file
    const redisCachePath = path.join(__dirname, 'src', 'utils', 'redisCache.js');
    
    // Check if the file exists
    if (!fs.existsSync(redisCachePath)) {
      logStatus('ERROR', `Redis cache utility file not found at: ${redisCachePath}`);
      return false;
    }
    
    // Read the file content
    const fileContent = fs.readFileSync(redisCachePath, 'utf8');
    
    // Check for common patterns that might indicate issues
    const issues = [];
    
    // 1. Check for direct array access without validation
    if (fileContent.includes('.messages[') && !fileContent.includes('Array.isArray(')) {
      issues.push('Direct array access without Array.isArray() check');
    }
    
    // 2. Check for proper error handling in JSON parsing
    if (fileContent.includes('JSON.parse(') && !fileContent.includes('try {')) {
      issues.push('JSON.parse() without try/catch error handling');
    }
    
    // 3. Check for type validation
    const hasTypeofChecks = fileContent.includes('typeof') || 
                            fileContent.includes('instanceof') || 
                            fileContent.includes('Array.isArray');
    
    if (!hasTypeofChecks) {
      issues.push('No type checking found (typeof, instanceof, Array.isArray)');
    }
    
    // Report findings
    if (issues.length > 0) {
      logStatus('WARNING', 'Potential type handling issues found in redisCache.js:');
      issues.forEach(issue => {
        console.log(`  - ${issue}`);
      });
      return false;
    } else {
      logStatus('SUCCESS', 'No obvious type handling issues found in redisCache.js');
      return true;
    }
  } catch (error) {
    logStatus('ERROR', `Failed to analyze redisCache.js: ${error.message}`);
    return false;
  }
}

// Main function
async function main() {
  console.log('='.repeat(60));
  console.log(`${colors.cyan}ClassZ Backend - Redis Health Check${colors.reset}`);
  console.log('='.repeat(60));
  console.log('\nRunning Redis diagnostics...\n');
  
  // Store test results
  const results = {
    redisEnabled: false,
    connectionTest: false,
    cacheFunctionsImported: false,
    cacheFunctionsTest: false,
    configCheck: false,
    typeHandling: false
  };
  
  // Check if Redis is enabled
  results.redisEnabled = checkRedisEnabled();
  
  if (!results.redisEnabled) {
    logStatus('WARNING', 'Redis is disabled. Skipping remaining tests.');
    provideDiagnostics(results);
    return;
  }
  
  // Check basic Redis connectivity
  results.connectionTest = await testRedisConnection();
  
  // Import and test Redis cache functions
  const redisFunctions = await testRedisCacheFunctions();
  results.cacheFunctionsImported = !!redisFunctions;
  
  if (results.connectionTest && results.cacheFunctionsImported) {
    // Test caching functions
    results.cacheFunctionsTest = await testCachingFunctions(redisFunctions);
  } else {
    logStatus('SKIPPED', 'Skipping caching function tests due to previous failures');
  }
  
  // Check configuration
  results.configCheck = checkConfigurationIssues();
  
  // Check type handling
  results.typeHandling = checkTypeHandling();
  
  // Provide diagnosis
  provideDiagnostics(results);
}

// Run the script
main().catch(error => {
  console.error('Unhandled error during health check:', error);
  process.exit(1);
}); 