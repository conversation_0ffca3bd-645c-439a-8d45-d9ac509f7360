# Review API Documentation

This document outlines the API endpoints for the review system in the ClassZ Backend application.

## Overview

The Review API allows students and parents to submit, read, and manage reviews for centers, classes, and coaches. The system helps maintain quality by gathering user feedback and displaying ratings across the platform.

## Authentication

All API requests require authentication using a JWT token. Add your token to the request headers:

```
auth-token: YOUR_JWT_TOKEN
```

## API Endpoints

### 1. Create Review

Submits a new review for a center, class, or coach.

- **URL**: `/api/review`
- **Method**: `POST`
- **Authentication**: Required (Student/Parent)

#### Request Headers

| Header       | Value            | Description                  |
|--------------|------------------|------------------------------|
| Content-Type | application/json | Format of the request body   |
| auth-token   | YOUR_JWT_TOKEN   | JWT token for authentication |

#### Request Body

```json
{
  "targetType": "center",
  "targetId": "center123",
  "rating": 4.5,
  "comment": "Great facility with excellent coaches. My child really enjoys the classes here.",
  "title": "Excellent Center"
}
```

| Field      | Type   | Required | Description                                     |
|------------|--------|----------|-------------------------------------------------|
| targetType | string | Yes      | Type of entity being reviewed (center/class/coach) |
| targetId   | string | Yes      | ID of the entity being reviewed                |
| rating     | number | Yes      | Rating from 1 to 5 (can include half points)    |
| comment    | string | No       | Detailed review text                            |
| title      | string | No       | Short review title/headline                     |

#### Success Response

- **Code**: 201 Created
- **Content**:

```json
{
  "_id": "review123",
  "targetType": "center",
  "targetId": "center123",
  "userId": "user456",
  "rating": 4.5,
  "comment": "Great facility with excellent coaches. My child really enjoys the classes here.",
  "title": "Excellent Center",
  "createdAt": "2023-05-20T14:30:22Z",
  "status": "published"
}
```

#### Error Responses

- **Code**: 400 Bad Request
- **Content**:

```json
{
  "message": "You can only review centers you've attended"
}
```

or

```json
{
  "message": "Rating must be between 1 and 5"
}
```

- **Code**: 500 Internal Server Error
- **Content**:

```json
{
  "message": "Failed to create review"
}
```

### 2. Get Reviews for Entity

Retrieves reviews for a specific center, class, or coach.

- **URL**: `/api/review/:targetType/:targetId`
- **Method**: `GET`
- **Authentication**: Optional

#### Request Headers

| Header     | Value          | Description                  |
|------------|----------------|------------------------------|
| auth-token | YOUR_JWT_TOKEN | JWT token for authentication (optional) |

#### Request Parameters

| Parameter  | Type   | Required | Description                               |
|------------|--------|----------|-------------------------------------------|
| targetType | string | Yes      | Type of entity (center/class/coach)       |
| targetId   | string | Yes      | ID of the entity to get reviews for       |

#### Query Parameters

| Parameter | Type   | Required | Description                            |
|-----------|--------|----------|----------------------------------------|
| page      | number | No       | Page number for pagination (default: 1)  |
| limit     | number | No       | Number of reviews per page (default: 10) |
| sort      | string | No       | Sort field (e.g., "createdAt", "rating") |
| order     | string | No       | Sort order ("asc" or "desc")            |

#### Success Response

- **Code**: 200 OK
- **Content**:

```json
{
  "total": 25,
  "averageRating": 4.3,
  "page": 1,
  "limit": 10,
  "reviews": [
    {
      "_id": "review123",
      "targetType": "center",
      "targetId": "center123",
      "userId": "user456",
      "userDetails": {
        "name": "John Doe",
        "profilePicture": "url/to/profile.jpg"
      },
      "rating": 4.5,
      "comment": "Great facility with excellent coaches. My child really enjoys the classes here.",
      "title": "Excellent Center",
      "createdAt": "2023-05-20T14:30:22Z",
      "status": "published"
    },
    {
      "_id": "review124",
      "targetType": "center",
      "targetId": "center123",
      "userId": "user789",
      "userDetails": {
        "name": "Jane Smith",
        "profilePicture": "url/to/profile2.jpg"
      },
      "rating": 4,
      "comment": "Good center overall. Very clean facilities.",
      "title": "Good Experience",
      "createdAt": "2023-05-19T10:15:45Z",
      "status": "published"
    }
  ]
}
```

### 3. Get Pending Reviews

Retrieves a list of entities that the user has attended but not yet reviewed.

- **URL**: `/api/review/pending`
- **Method**: `GET`
- **Authentication**: Required (Student/Parent)

#### Request Headers

| Header     | Value          | Description                  |
|------------|----------------|------------------------------|
| auth-token | YOUR_JWT_TOKEN | JWT token for authentication |

#### Success Response

- **Code**: 200 OK
- **Content**:

```json
{
  "pendingReviews": [
    {
      "targetType": "class",
      "targetId": "class456",
      "targetName": "Advanced Swimming",
      "lastAttended": "2023-05-18T09:00:00Z",
      "centerName": "Aquatic Center",
      "coachName": "Michael Trainer"
    },
    {
      "targetType": "center",
      "targetId": "center789",
      "targetName": "Sports Academy",
      "lastAttended": "2023-05-15T15:30:00Z"
    }
  ]
}
```

### 4. Update Review

Updates an existing review submitted by the user.

- **URL**: `/api/review/:reviewId`
- **Method**: `PUT`
- **Authentication**: Required (Review Author)

#### Request Headers

| Header       | Value            | Description                  |
|--------------|------------------|------------------------------|
| Content-Type | application/json | Format of the request body   |
| auth-token   | YOUR_JWT_TOKEN   | JWT token for authentication |

#### Request Parameters

| Parameter | Type   | Required | Description                 |
|-----------|--------|----------|-----------------------------|
| reviewId  | string | Yes      | ID of the review to update |

#### Request Body

```json
{
  "rating": 5,
  "comment": "Updated comment - even better than I initially thought!",
  "title": "Updated Review Title"
}
```

| Field   | Type   | Required | Description                  |
|---------|--------|----------|------------------------------|
| rating  | number | No       | Updated rating from 1 to 5   |
| comment | string | No       | Updated review text          |
| title   | string | No       | Updated review title         |

#### Success Response

- **Code**: 200 OK
- **Content**:

```json
{
  "_id": "review123",
  "targetType": "center",
  "targetId": "center123",
  "userId": "user456",
  "rating": 5,
  "comment": "Updated comment - even better than I initially thought!",
  "title": "Updated Review Title",
  "createdAt": "2023-05-20T14:30:22Z",
  "updatedAt": "2023-05-21T09:45:12Z",
  "status": "published"
}
```

#### Error Responses

- **Code**: 403 Forbidden
- **Content**:

```json
{
  "message": "You can only update your own reviews"
}
```

### 5. Delete Review

Deletes an existing review submitted by the user.

- **URL**: `/api/review/:reviewId`
- **Method**: `DELETE`
- **Authentication**: Required (Review Author or Admin)

#### Request Headers

| Header     | Value          | Description                  |
|------------|----------------|------------------------------|
| auth-token | YOUR_JWT_TOKEN | JWT token for authentication |

#### Request Parameters

| Parameter | Type   | Required | Description                 |
|-----------|--------|----------|-----------------------------|
| reviewId  | string | Yes      | ID of the review to delete |

#### Success Response

- **Code**: 200 OK
- **Content**:

```json
{
  "message": "Review deleted successfully"
}
```

#### Error Responses

- **Code**: 403 Forbidden
- **Content**:

```json
{
  "message": "You can only delete your own reviews"
}
```

## Data Models

### Review Model

```javascript
const reviewSchema = new mongoose.Schema({
  targetType: {
    type: String,
    enum: ["center", "class", "coach"],
    required: true
  },
  targetId: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: "targetType",
    required: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "user",
    required: true
  },
  rating: {
    type: Number,
    min: 1,
    max: 5,
    required: true
  },
  comment: {
    type: String,
    required: false
  },
  title: {
    type: String,
    required: false
  },
  status: {
    type: String,
    enum: ["pending", "published", "rejected"],
    default: "published"
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: null
  }
});

// Compound index to ensure a user can only leave one review per target
reviewSchema.index({ userId: 1, targetType: 1, targetId: 1 }, { unique: true });
```

## Implementation Examples

### Submitting a Review

```javascript
// Example using fetch API
const submitReview = async (targetType, targetId, rating, comment, title) => {
  try {
    const response = await fetch('http://localhost:3000/api/review', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'auth-token': 'YOUR_JWT_TOKEN'
      },
      body: JSON.stringify({
        targetType,
        targetId,
        rating,
        comment,
        title
      })
    });
    
    return await response.json();
  } catch (error) {
    console.error('Error submitting review:', error);
  }
};
```

### Getting Reviews for a Center

```javascript
// Example using fetch API
const getCenterReviews = async (centerId, page = 1, limit = 10) => {
  try {
    const response = await fetch(`http://localhost:3000/api/review/center/${centerId}?page=${page}&limit=${limit}`, {
      method: 'GET',
      headers: {
        'auth-token': 'YOUR_JWT_TOKEN'
      }
    });
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching center reviews:', error);
  }
};
```

## Best Practices

1. **Verify Attendance**: Ensure users have actually attended a class/center before allowing them to leave a review
2. **Review Policy**: Establish clear guidelines for appropriate review content
3. **Moderation System**: Implement a system to handle inappropriate reviews
4. **Update Aggregates**: When a review is created/updated/deleted, update aggregate ratings on the target entity
5. **Limit Review Frequency**: Allow only one review per user per entity, with the ability to update
6. **Notification System**: Notify center owners or coaches when they receive new reviews 