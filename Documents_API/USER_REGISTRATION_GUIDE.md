# User Registration Guide

This guide explains how to register a new user account and use the address API.

## Registration Process Overview

In this system, user registration follows these steps:
1. Request an OTP (One-Time Password)
2. Verify the OTP
3. Register with the verified OTP
4. Login to get an authentication token
5. Use the token for API access (e.g., address management)

## Using Postman for Registration

### Step 1: Request an OTP

1. **Create a new request** in Postman
2. **Set the request type** to `GET`
3. **Enter the URL**: `http://localhost:3000/api/otp?email=<EMAIL>`
   (Replace `<EMAIL>` with your actual email address)
4. **Click Send**

You should receive an OTP in your email (or see it in the server console for development environments).

### Step 2: Register with the OTP

1. **Create a new request** in Postman
2. **Set the request type** to `POST`
3. **Enter the URL**: `http://localhost:3000/api/auth/signup`
4. **Headers**:
   ```
   Content-Type: application/json
   ```
5. **Body**:
   ```json
   {
     "email": "<EMAIL>",
     "password": "yourpassword",
     "type": "parent",
     "otp": "1234"
   }
   ```
   - Replace `<EMAIL>` with your actual email
   - Replace `yourpassword` with your desired password
   - Replace `1234` with the actual OTP you received
   - The `type` can be one of: `parent`, `coach`, `center`, `owner`, or `manager`

6. **Click Send**

You should receive a response with a JWT token and user information:

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "_id": "...",
    "email": "<EMAIL>",
    "roles": ["parent"],
    "createdAt": "...",
    "updatedAt": "..."
  }
}
```

Save the token for future API calls.

### Step 3: Login (If Token Expires)

1. **Create a new request** in Postman
2. **Set the request type** to `POST`
3. **Enter the URL**: `http://localhost:3000/api/auth/signin`
4. **Headers**:
   ```
   Content-Type: application/json
   ```
5. **Body**:
   ```json
   {
     "email": "<EMAIL>",
     "password": "yourpassword",
     "type": "parent"
   }
   ```

6. **Click Send**

You'll receive a new token in the response. Save this token for future API calls.

## Using the Address API

After registration, you can use the token to access the Address API as described in the API documentation.

For all API requests, include the token in the Authorization header:
```
Authorization: Bearer your_jwt_token_here
```

# API Documentation

## Authentication API

### 1. Request OTP

Sends a one-time password to the specified email.

- **URL**: `/api/otp`
- **Method**: `GET`
- **URL Parameters**: 
  - `email`: The email address to send the OTP to
- **Success Response**: `true`
- **Error Response**: Error message

### 2. Verify OTP

Verifies an OTP for an email address.

- **URL**: `/api/otp/verify`  
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "otp": "1234"
  }
  ```
- **Success Response**: `true`
- **Error Response**: `false` or error message

### 3. Sign Up

Registers a new user.

- **URL**: `/api/auth/signup`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "yourpassword",
    "type": "parent",
    "otp": "1234"
  }
  ```
- **Success Response**:
  ```json
  {
    "token": "jwt_token_here",
    "user": {
      "_id": "user_id",
      "email": "<EMAIL>",
      "roles": ["parent"],
      "createdAt": "timestamp",
      "updatedAt": "timestamp"
    }
  }
  ```
- **Error Response**: Error message

### 4. Sign In

Authenticates a user and returns a token.

- **URL**: `/api/auth/signin`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "yourpassword",
    "type": "parent"
  }
  ```
- **Success Response**:
  ```json
  {
    "token": "jwt_token_here",
    "data": {
      "parent": {
        "_id": "user_id",
        "baseUser": "base_user_id",
        "email": "<EMAIL>",
        "location": [],
        "other_fields": "..."
      }
    }
  }
  ```
- **Error Response**: Error message

## Address API

### 1. Get All Addresses for a User

Retrieves all addresses associated with a specific user.

- **URL**: `/api/address/user/:userId`
- **Method**: `GET`
- **URL Parameters**: 
  - `userId` (required): The ID of the user
- **Headers**:
  ```
  Authorization: Bearer your_jwt_token_here
  ```
- **Success Response**:
  ```json
  {
    "success": true,
    "addresses": [
      {
        "_id": "address_id_1",
        "flatFloorBlock": "G032, G/F",
        "buildingEstate": "ABC Mall",
        "district": "Causeway Bay",
        "region": "Hong Kong",
        "country": "Hong Kong",
        "coordinates": {
          "lat": 22.2799,
          "lng": 114.1880
        },
        "default": true
      }
    ]
  }
  ```
- **Error Response**: Error message

### 2. Add a New Address

Adds a new address for a specific user.

- **URL**: `/api/address/user/:userId`
- **Method**: `POST`
- **URL Parameters**: 
  - `userId` (required): The ID of the user
- **Headers**:
  ```
  Content-Type: application/json
  Authorization: Bearer your_jwt_token_here
  ```
- **Request Body**:
  ```json
  {
    "flatFloorBlock": "G032, G/F",
    "buildingEstate": "ABC Mall",
    "district": "Causeway Bay",
    "region": "Hong Kong",
    "country": "Hong Kong",
    "coordinates": {
      "lat": 22.2799,
      "lng": 114.1880
    },
    "default": true
  }
  ```
- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Address added successfully",
    "user": {
      "_id": "user_id",
      "email": "<EMAIL>",
      "location": [
        {
          "_id": "address_id_1",
          "flatFloorBlock": "G032, G/F",
          "buildingEstate": "ABC Mall",
          "district": "Causeway Bay",
          "region": "Hong Kong",
          "country": "Hong Kong",
          "coordinates": {
            "lat": 22.2799,
            "lng": 114.1880
          },
          "default": true
        }
      ],
      "other_user_fields": "..."
    }
  }
  ```
- **Error Response**: Error message

### 3. Get an Address with Map Data

Retrieves an address with map formatting for a specific user.

- **URL**: `/api/address/user/:userId/map`
- **Method**: `GET`
- **URL Parameters**: 
  - `userId` (required): The ID of the user
- **Headers**:
  ```
  Authorization: Bearer your_jwt_token_here
  ```
- **Success Response**:
  ```json
  {
    "success": true,
    "address": {
      "id": "address_id_1",
      "fullAddress": "G032, G/F, ABC Mall, Causeway Bay, Hong Kong, Hong Kong",
      "flatFloorBlock": "G032, G/F",
      "buildingEstate": "ABC Mall",
      "district": "Causeway Bay",
      "region": "Hong Kong",
      "country": "Hong Kong",
      "default": true,
      "coordinates": {
        "lat": 22.2799,
        "lng": 114.1880
      }
    }
  }
  ```
- **Error Response**: Error message

### 4. Update an Address

Updates an existing address for a specific user.

- **URL**: `/api/address/user/:userId/:addressId`
- **Method**: `PUT`
- **URL Parameters**: 
  - `userId` (required): The ID of the user
  - `addressId` (required): The ID of the address to update
- **Headers**:
  ```
  Content-Type: application/json
  Authorization: Bearer your_jwt_token_here
  ```
- **Request Body**:
  ```json
  {
    "flatFloorBlock": "G032, G/F (Updated)",
    "buildingEstate": "ABC Mall (Updated)",
    "district": "Causeway Bay",
    "region": "Hong Kong",
    "coordinates": {
      "lat": 22.2800,
      "lng": 114.1881
    }
  }
  ```
- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Address updated successfully",
    "user": {
      "_id": "user_id",
      "email": "<EMAIL>",
      "location": [
        {
          "_id": "address_id_1",
          "flatFloorBlock": "G032, G/F (Updated)",
          "buildingEstate": "ABC Mall (Updated)",
          "district": "Causeway Bay",
          "region": "Hong Kong",
          "country": "Hong Kong",
          "coordinates": {
            "lat": 22.2800,
            "lng": 114.1881
          },
          "default": true
        }
      ],
      "other_user_fields": "..."
    }
  }
  ```
- **Error Response**: Error message

### 5. Set an Address as Default

Sets a specific address as the default address for a user.

- **URL**: `/api/address/user/:userId/:addressId/default`
- **Method**: `PUT`
- **URL Parameters**: 
  - `userId` (required): The ID of the user
  - `addressId` (required): The ID of the address to set as default
- **Headers**:
  ```
  Authorization: Bearer your_jwt_token_here
  ```
- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Default address updated successfully",
    "user": {
      "_id": "user_id",
      "email": "<EMAIL>",
      "location": [
        {
          "_id": "address_id_1",
          "flatFloorBlock": "G032, G/F",
          "buildingEstate": "ABC Mall",
          "district": "Causeway Bay",
          "region": "Hong Kong",
          "country": "Hong Kong",
          "coordinates": {
            "lat": 22.2799,
            "lng": 114.1880
          },
          "default": true
        },
        {
          "_id": "address_id_2",
          "flatFloorBlock": "Flat B, 7/F",
          "buildingEstate": "Ocean View",
          "district": "North Point",
          "region": "Hong Kong",
          "country": "Hong Kong",
          "coordinates": {
            "lat": 22.2913,
            "lng": 114.1950
          },
          "default": false
        }
      ],
      "other_user_fields": "..."
    }
  }
  ```
- **Error Response**: Error message

### 6. Delete an Address

Deletes an address for a specific user.

- **URL**: `/api/address/user/:userId/:addressId`
- **Method**: `DELETE`
- **URL Parameters**: 
  - `userId` (required): The ID of the user
  - `addressId` (required): The ID of the address to delete
- **Headers**:
  ```
  Authorization: Bearer your_jwt_token_here
  ```
- **Success Response**:
  ```json
  {
    "success": true,
    "message": "Address deleted successfully",
    "user": {
      "_id": "user_id",
      "email": "<EMAIL>",
      "location": [],
      "other_user_fields": "..."
    }
  }
  ```
- **Error Response**: Error message

## Troubleshooting

### Common Issues

1. **OTP Verification Failed**: Make sure you're using the correct OTP and it hasn't expired (typically valid for 5 minutes).
2. **JWT Token Invalid**: The token might be expired. Try logging in again to get a new token.
3. **Authorization Failure**: Ensure the token is correctly formatted in the header as `Bearer your_token_here`.
4. **User Not Found**: Verify that you're using the correct user ID in the API requests.
5. **Server Connection Issues**: Make sure the backend server is running at the specified URL.

### Error Status Codes

- **400** - Bad Request: Invalid parameters or request body
- **401** - Unauthorized: Invalid or missing authentication token
- **404** - Not Found: Resource (user, address) not found
- **500** - Internal Server Error: Server-side issue

For additional help, check the server logs for detailed error messages. 