const axios = require('axios');

// Configuration - replace with your actual values
const API_BASE_URL = 'http://localhost:3000/api/address';
const AUTH_TOKEN = 'your_auth_token'; // Replace with a valid JWT token
const TEST_USER_ID = '6464e5b1f8b95a001db5c3e9'; // Replace with an existing user ID

// Test data
const testAddress = {
  flatFloorBlock: 'G032, G/F',
  buildingEstate: 'ABC Mall',
  district: 'Causeway Bay',
  region: 'Hong Kong',
  country: 'Hong Kong',
  coordinates: {
    lat: 22.2799,
    lng: 114.1880
  },
  default: true
};

let createdAddressId;

// HTTP client with authorization header
const client = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Test functions
async function getAllAddresses() {
  console.log('\n--- Testing Get All Addresses ---');
  try {
    const response = await client.get(`/user/${TEST_USER_ID}`);
    console.log('Status:', response.status);
    console.log(`Found ${response.data.addresses.length} addresses`);
    console.log('Response:', JSON.stringify(response.data, null, 2).substring(0, 300) + '...');
    return response.data;
  } catch (error) {
    console.error('Error getting addresses:', error.response?.data || error.message);
  }
}

async function addNewAddress() {
  console.log('\n--- Testing Add New Address ---');
  try {
    const response = await client.post(`/user/${TEST_USER_ID}`, testAddress);
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2).substring(0, 300) + '...');
    
    // Extract the created address ID for further tests
    if (response.data.user && response.data.user.location && response.data.user.location.length > 0) {
      // Find the address we just added (usually the last one)
      const newAddress = response.data.user.location.find(addr => 
        addr.flatFloorBlock === testAddress.flatFloorBlock && 
        addr.buildingEstate === testAddress.buildingEstate
      );
      
      if (newAddress) {
        createdAddressId = newAddress._id;
        console.log('Created address ID:', createdAddressId);
      }
    }
    
    return response.data;
  } catch (error) {
    console.error('Error adding address:', error.response?.data || error.message);
  }
}

async function getAddressWithMap() {
  console.log('\n--- Testing Get Address With Map ---');
  try {
    const response = await client.get(`/user/${TEST_USER_ID}/map`);
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error getting address with map:', error.response?.data || error.message);
  }
}

async function getSpecificAddressWithMap() {
  if (!createdAddressId) {
    console.log('No address ID available for testing specific address map view.');
    return;
  }
  
  console.log('\n--- Testing Get Specific Address With Map ---');
  try {
    const response = await client.get(`/user/${TEST_USER_ID}/${createdAddressId}/map`);
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error getting specific address with map:', error.response?.data || error.message);
  }
}

async function updateAddress() {
  if (!createdAddressId) {
    console.log('No address ID available for testing update.');
    return;
  }
  
  console.log('\n--- Testing Update Address ---');
  const updateData = {
    flatFloorBlock: 'G032, G/F (Updated)',
    buildingEstate: 'ABC Mall (Updated)',
    coordinates: {
      lat: 22.2800,
      lng: 114.1881
    }
  };
  
  try {
    const response = await client.put(`/user/${TEST_USER_ID}/${createdAddressId}`, updateData);
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2).substring(0, 300) + '...');
    return response.data;
  } catch (error) {
    console.error('Error updating address:', error.response?.data || error.message);
  }
}

async function getDefaultAddress() {
  console.log('\n--- Testing Get Default Address ---');
  try {
    const response = await client.get(`/user/${TEST_USER_ID}/default`);
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error getting default address:', error.response?.data || error.message);
  }
}

async function setDefaultAddress() {
  if (!createdAddressId) {
    console.log('No address ID available for testing set default.');
    return;
  }
  
  console.log('\n--- Testing Set Default Address ---');
  try {
    const response = await client.put(`/user/${TEST_USER_ID}/${createdAddressId}/default`);
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2).substring(0, 300) + '...');
    return response.data;
  } catch (error) {
    console.error('Error setting default address:', error.response?.data || error.message);
  }
}

async function deleteAddress() {
  if (!createdAddressId) {
    console.log('No address ID available for testing delete.');
    return;
  }
  
  console.log('\n--- Testing Delete Address ---');
  try {
    const response = await client.delete(`/user/${TEST_USER_ID}/${createdAddressId}`);
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(response.data, null, 2).substring(0, 300) + '...');
    return response.data;
  } catch (error) {
    console.error('Error deleting address:', error.response?.data || error.message);
  }
}

// Run all tests in sequence
async function runTests() {
  console.log('Starting Address API Tests...');
  
  // Get initial addresses
  await getAllAddresses();
  
  // Add a new address
  await addNewAddress();
  
  // Get address with map
  await getAddressWithMap();
  
  // Get specific address with map
  await getSpecificAddressWithMap();
  
  // Update the address
  await updateAddress();
  
  // Get the default address
  await getDefaultAddress();
  
  // Set as default address
  await setDefaultAddress();
  
  // Delete the address (uncomment if you want to delete the created address)
  // await deleteAddress();
  
  // Check final state
  await getAllAddresses();
  
  console.log('\nAddress API Tests completed!');
}

// Run the tests
runTests().catch(error => {
  console.error('Test runner error:', error);
}); 