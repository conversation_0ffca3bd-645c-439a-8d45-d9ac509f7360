# Center Approval API - Usage Guide

This guide provides instructions on how to use the center approval API in the ClassZ Backend system.

## Introduction

The center approval feature allows administrators to review and approve centers before they can fully operate on the platform. When a center is created, its `verified` field is set to `false` by default, and an administrator needs to review and approve it.

## Prerequisites

To use this API, you need:
- An administrator account with valid JWT token
- The ID of the center you want to approve
- Postman or similar API testing tool (optional)

## Approval Process

### 1. Review Center Details

Before approving a center, review its details including:
- Legal documentation (business certificates, HKID cards)
- Center information (name, address, contact details)
- Services offered

You can get this information by making a GET request to:
```
GET /api/center/:id
```

Add the `auth-token` header with your JWT token for authentication:
```
auth-token: YOUR_JWT_TOKEN
```

### 2. Approve the Center

Once you've reviewed the center's details and determined it meets all requirements, approve it by making a PUT request to:
```
PUT /api/center/:id/verify
```

With the following headers:
```
Content-Type: application/json
auth-token: YOUR_JWT_TOKEN
```

And the following request body:
```json
{
  "verified": true
}
```

### 3. Revoking Approval

If necessary, you can revoke a center's approval by making a PUT request to the same endpoint with:
```json
{
  "verified": false
}
```

## Using the API with Postman

We've provided a Postman collection (`CENTER_APPROVAL_POSTMAN_COLLECTION.json`) that you can import to quickly test the API.

1. Import the collection into Postman
2. Set up your environment variables:
   - `base_url`: The base URL of your API (e.g., `http://localhost:3000`)
   - `admin_token`: Your administrator JWT token (without "Bearer " prefix)

3. For each request, the center ID is pre-filled with "6823f668a8a52c65b63c02fb" - update this with your specific center ID if needed

## Important Note About Authentication

**Your system uses `auth-token` header instead of the standard `Authorization: Bearer` format.**

Make sure all your requests include the `auth-token` header with your JWT token:
```
auth-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Implementing Admin Authentication

To ensure only administrators can approve centers, make sure your authentication middleware includes role checking:

```javascript
// Example auth middleware implementation (simplified)
const authorizeAdmin = async (req, res, next) => {
  try {
    const user = req.user; // Set by previous authenticate middleware
    
    if (!user || user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized. Admin access required'
      });
    }
    
    next();
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error checking admin authorization',
      error: error.message
    });
  }
};
```

## Best Practices

1. **Always verify center documentation**: Check that all legal documents are valid before approval
2. **Maintain an audit trail**: Log all approval and revocation actions
3. **Send notifications**: Notify center owners when their center is approved or approval is revoked
4. **Implement rate limiting**: Prevent abuse of the API endpoints
5. **Use HTTPS**: Ensure all API calls are made over secure connections

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- 400 Bad Request: Missing required fields or invalid token
- 401 Unauthorized: Missing or invalid auth-token
- 404 Not Found: Center doesn't exist
- 500 Internal Server Error: Server-side issues

## Additional Information

For more details, see the complete API documentation in `CENTER_APPROVAL_API_DOCUMENTATION.md`. 