# Center Approval API Documentation

This document outlines the API endpoint for approving centers in the ClassZ Backend application.

## Overview

Centers in the ClassZ system need to be approved before they can fully operate on the platform. When a center is created, the `verified` field is set to `false` by default (as defined in the `centerModel.js`), and an admin needs to review and approve the center.

## API Endpoint

### Approve Center

Approves a center by updating its verification status.

- **URL**: `/api/center/:id/verify`
- **Method**: `PUT`
- **Authentication**: Required (Admin only)

#### Request Headers

| Header         | Value             | Description                   |
|----------------|-------------------|-------------------------------|
| Content-Type   | application/json  | Format of the request body    |
| auth-token     | YOUR_JWT_TOKEN    | JWT token for authentication  |

#### Request Body

```json
{
  "verified": true
}
```

#### Request Parameters

| Parameter | Type   | Description        |
|-----------|--------|--------------------|
| id        | string | The ID of the center to approve |

#### Success Response

- **Code**: 200 OK
- **Content**:

```json
{
  "success": true,
  "message": "Center has been approved successfully",
  "data": {
    "_id": "center_id",
    "legalName": "Center Legal Name",
    "displayName": "Center Display Name",
    "verified": true,
    "address": {
      // Address details
    },
    // Other center details
  }
}
```

#### Error Responses

- **Code**: 401 Unauthorized
- **Content**:

```json
{
  "message": "Access Denied"
}
```

- **Code**: 400 Bad Request
- **Content**:

```json
{
  "message": "Invalid Token"
}
```

- **Code**: 404 Not Found
- **Content**:

```json
{
  "success": false,
  "message": "Center not found"
}
```

- **Code**: 500 Internal Server Error
- **Content**:

```json
{
  "success": false,
  "message": "Error updating center verification status",
  "error": "Error message"
}
```

## Implementation Requirements

To implement this API endpoint, the following changes need to be made to the codebase:

1. Add a new route in `centerRoute.js`:
   ```javascript
   router.put(
     "/:id/verify",
     authMiddleware.authenticate.bind(authMiddleware),
     centerController.verifyCenter.bind(centerController)
   );
   ```

2. Add the controller method in `centerController.js`:
   ```javascript
   async verifyCenter(req, res) {
     try {
       const centerId = req.params.id;
       const { verified } = req.body;
       
       if (verified === undefined) {
         return res.status(400).json({
           success: false,
           message: "Verification status is required"
         });
       }
       
       const updatedCenter = await this.centerUseCase.verifyCenterById(centerId, verified);
       
       res.status(200).json({
         success: true,
         message: verified ? "Center has been approved successfully" : "Center approval has been revoked",
         data: updatedCenter
       });
     } catch (error) {
       this._handleError(res, error, "Error updating center verification status");
     }
   }
   ```

3. Add the use case method in `CenterUseCase.js`:
   ```javascript
   async verifyCenterById(centerId, verified) {
     try {
       const existingCenter = await this.centerRepository.findById(centerId);
       if (!existingCenter) {
         throw new Error("Center not found");
       }
       
       const updateData = { verified };
       const updatedCenter = await this.centerRepository.update(centerId, updateData);
       
       return updatedCenter;
     } catch (error) {
       throw new Error(`Error verifying center: ${error.message}`);
     }
   }
   ```

## Testing

You can test this API using Postman or curl:

```
curl -X PUT \
  http://localhost:3000/api/center/6823f668a8a52c65b63c02fb/verify \
  -H 'Content-Type: application/json' \
  -H 'auth-token: YOUR_JWT_TOKEN' \
  -d '{
    "verified": true
  }'
```

## Authentication Note

Your system uses the `auth-token` header for authentication instead of the standard `Authorization: Bearer` format. Make sure to include this header with your JWT token for all requests.

## Notes

- Only users with administrative permissions should be able to approve centers
- When a center is approved, notifications may be sent to the center owner (optional feature)
- The approval process may involve reviewing the center's documents (business certificates, HKID cards, etc.) 