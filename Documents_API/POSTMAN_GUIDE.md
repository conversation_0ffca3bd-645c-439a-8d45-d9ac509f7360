# Guide to Using the ClassZ API Postman Collection

This guide will help you import and use the Postman collection for testing the ClassZ API.

## Prerequisites

- [<PERSON><PERSON>](https://www.postman.com/downloads/) installed on your computer
- ClassZ Backend running locally or on a server

## Importing the Collection and Environment

1. Open Postman
2. Click on the "Import" button in the top left corner
3. Drag and drop or select the `AddressAPI_Postman_Collection.json` and `ClassZ_API_Environment.json` files
4. Both the collection and environment should now be imported into Postman

## Setting Up the Environment

1. In the top right corner of Postman, click on the environments dropdown
2. Select "ClassZ API Environment"
3. Update the following variables:
   - `base_url`: If your API is running on a different host or port than `http://localhost:3000`
   - `email`: Your email address for registration and login
   - `password`: Your password for registration and login

## Using the Collection

The collection is organized into two folders:

1. **Authentication** - Endpoints for user registration and login
2. **Address API** - Endpoints for managing addresses

### User Registration Process

Follow these steps in order to register and start using the API:

1. **Request OTP**
   - Open the "Request OTP" request in the Authentication folder
   - Ensure your email is set in the environment
   - Send the request
   - You should receive an OTP code via email or see it in the console logs

2. **Verify OTP**
   - Open the "Verify OTP" request
   - Update the `otp` environment variable with the OTP you received
   - Send the request
   - Verify that the OTP was validated successfully

3. **Sign Up**
   - Open the "Sign Up" request
   - The request will use your email, password, and OTP from the environment variables
   - Send the request
   - This will automatically save the received token and userId to your environment variables

4. **Sign In** (if needed)
   - If your token expires, use the "Sign In" request to get a new one
   - This will automatically update your token in the environment

### Using the Address API

After successful authentication, you can use the Address API endpoints:

1. **Add New Address**
   - Opens the "Add New Address" request
   - Modify the JSON body as needed
   - Send the request
   - This will automatically store the new address ID in your environment

2. **Get All Addresses**
   - View all addresses associated with your user

3. **Get Default Address**
   - View your default address

4. **Update Address**
   - Modify an existing address

5. **Set Default Address**
   - Change which address is set as default

6. **Delete Address**
   - Remove an address from your profile

## Troubleshooting

If you encounter errors:

1. **401 Unauthorized**
   - Your token may have expired. Use the "Sign In" request to get a new token.

2. **400 Bad Request**
   - Check your request body for missing or invalid fields.

3. **404 Not Found**
   - Ensure the user ID or address ID is correct.
   - Verify that the API server is running.

4. **500 Server Error**
   - Check the server logs for more information.

## Scripts and Automatic Variable Setting

The collection includes scripts that automatically:

1. Extract and store the authentication token from login responses
2. Store the user ID after registration or login
3. Store the address ID after creating a new address

This makes testing the API flow much easier as you don't have to manually copy these values between requests.

---

For more information, please refer to the `USER_REGISTRATION_GUIDE.md` file for detailed API documentation. 