# Redis Setup Checklist for ClassZ Chat

A step-by-step checklist to ensure <PERSON><PERSON> is properly set up and configured for your production ClassZ Chat application.

## 1. Installation

- [ ] Install Redis on your server:

   **Ubuntu/Debian:**
   ```bash
   sudo apt update
   sudo apt install redis-server
   ```

   **CentOS/RHEL:**
   ```bash
   sudo yum install redis
   ```

   **Windows:**
   Download MSI installer from https://github.com/microsoftarchive/redis/releases

- [ ] Verify Redis is installed correctly:
   ```bash
   redis-cli --version
   ```

## 2. Configuration

- [ ] Create/edit Redis configuration file:
   ```bash
   sudo nano /etc/redis/redis.conf
   ```

- [ ] Configure fundamental settings:
   ```
   # Bind to appropriate network interfaces (localhost for security)
   bind 127.0.0.1
   
   # Set port (default 6379)
   port 6379
   
   # Set memory limit (adjust based on your server resources)
   maxmemory 256mb
   
   # Set eviction policy (recommended for chat caching)
   maxmemory-policy allkeys-lru
   
   # Optional: Enable persistence
   appendonly yes
   appendfsync everysec
   ```

- [ ] Secure Redis (for production):
   ```
   # Set a strong password
   requirepass YourStrongPasswordHere
   
   # Disable or rename potentially dangerous commands
   rename-command FLUSHALL ""
   rename-command FLUSHDB ""
   rename-command CONFIG ""
   ```

- [ ] Restart Redis to apply settings:
   ```bash
   sudo systemctl restart redis-server
   ```

## 3. Verify Installation

- [ ] Check if Redis is running:
   ```bash
   sudo systemctl status redis-server
   ```

- [ ] Test Redis connection:
   ```bash
   # No password
   redis-cli ping
   
   # With password
   redis-cli -a YourStrongPasswordHere ping
   ```

- [ ] Run the included test script:
   ```bash
   node test-redis.js
   ```

## 4. Application Configuration

- [ ] Update `.env` file with Redis settings:
   ```
   ENABLE_REDIS_CACHE=true
   REDIS_HOST=localhost
   REDIS_PORT=6379
   REDIS_PASSWORD=YourStrongPasswordHere  # If using password
   ```

- [ ] Test connection from your application:
   ```bash
   # Start the app in test mode
   NODE_ENV=production node test-redis.js
   ```

## 5. Performance Tuning

- [ ] Monitor Redis memory usage:
   ```bash
   redis-cli info memory
   ```

- [ ] Check hit ratio to ensure caching is effective:
   ```bash
   redis-cli info stats | grep hit_rate
   ```
   
   Target: Hit rate > 0.8 (80%)

- [ ] Set appropriate TTL values in the code:
   ```javascript
   // Example from your codebase
   const TTL = 300; // 5 minutes cache expiration
   await cacheMessages(conversationId, messages, TTL);
   ```

## 6. System Integration

- [ ] Enable Redis to start on boot:
   ```bash
   sudo systemctl enable redis-server
   ```

- [ ] Set up basic monitoring:
   ```bash
   # Create a simple monitoring script
   echo "redis-cli info | grep connected_clients" > monitor-redis.sh
   chmod +x monitor-redis.sh
   ```

- [ ] Configure logrotate for Redis logs:
   ```bash
   sudo nano /etc/logrotate.d/redis-server
   ```

## 7. Backup Strategy

- [ ] Set up Redis persistence:
   ```
   # In redis.conf
   dir /var/lib/redis
   dbfilename dump.rdb
   save 900 1
   save 300 10
   save 60 10000
   ```

- [ ] Create a backup script:
   ```bash
   #!/bin/bash
   # redis-backup.sh
   BACKUP_DIR="/var/backups/redis"
   DATE=$(date +%Y%m%d-%H%M%S)
   mkdir -p $BACKUP_DIR
   redis-cli -a YourStrongPasswordHere --rdb $BACKUP_DIR/redis-$DATE.rdb
   find $BACKUP_DIR -name "redis-*.rdb" -mtime +7 -delete
   ```

## 8. Verification Checks

- [ ] Application connects to Redis successfully
- [ ] Cache hits are occurring (check logs)
- [ ] Memory usage is stable
- [ ] No error messages in Redis logs
- [ ] Performance improvement is measurable
- [ ] System handles Redis unavailability gracefully

## Emergency Procedures

If Redis fails:

1. Check Redis logs:
   ```bash
   sudo tail -f /var/log/redis/redis-server.log
   ```

2. Restart Redis:
   ```bash
   sudo systemctl restart redis-server
   ```

3. If Redis cannot be restarted, disable Redis in the application:
   ```
   # In .env
   ENABLE_REDIS_CACHE=false
   ```

4. Restart the application to apply changes:
   ```bash
   sudo systemctl restart classzChat  # Or your service name
   ```

By following this checklist, you'll ensure that Redis is properly set up and optimized for your ClassZ Chat application in production. 