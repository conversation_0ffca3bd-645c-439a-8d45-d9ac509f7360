# Review API Usage Guide

This guide provides step-by-step instructions for implementing and using the Review API in the ClassZ application.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Authentication](#authentication)
4. [Common Use Cases](#common-use-cases)
   - [Submitting a Review](#submitting-a-review)
   - [Retrieving Reviews](#retrieving-reviews)
   - [Checking for Pending Reviews](#checking-for-pending-reviews)
   - [Getting Average Ratings](#getting-average-ratings)
5. [Error Handling](#error-handling)
6. [Best Practices](#best-practices)
7. [Code Examples](#code-examples)

## Overview

The Review API enables users to submit, retrieve, and manage reviews for centers, classes, and coaches within the ClassZ platform. Reviews help maintain quality by gathering user feedback and displaying ratings across the platform, assisting other users in making informed decisions.

## Prerequisites

Before using the Review API, ensure you have:

1. A valid ClassZ account with appropriate permissions (Student, Parent, Coach, or Center)
2. Authentication credentials (JWT token)
3. Knowledge of relevant IDs (center IDs, class IDs, coach IDs, etc.)
4. Prior attendance or enrollment in the entity being reviewed (for creating reviews)

## Authentication

All API requests require authentication using a JWT token. Add your token to the request headers:

```
auth-token: YOUR_JWT_TOKEN
```

## Common Use Cases

### Submitting a Review

**User Flow:**

1. After attending a class, visiting a center, or working with a coach, a user can submit a review.
2. The user selects the entity type (center, class, or coach) and provides a rating and comments.
3. The app calls the `/api/review` endpoint with the review details.
4. The review is saved and becomes visible to others.

**API Call:**

```javascript
fetch('https://api.classz.com/api/review', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'auth-token': 'YOUR_JWT_TOKEN'
  },
  body: JSON.stringify({
    targetType: 'center',
    targetId: '64a12bc7a92d3f4e8b9c0d1e',
    rating: 4.5,
    comment: 'Great facility with excellent coaches. My child really enjoys the classes here.',
    title: 'Excellent Center'
  })
});
```

**Sample Response:**

```json
{
  "_id": "review123",
  "targetType": "center",
  "targetId": "64a12bc7a92d3f4e8b9c0d1e",
  "userId": "user456",
  "rating": 4.5,
  "comment": "Great facility with excellent coaches. My child really enjoys the classes here.",
  "title": "Excellent Center",
  "createdAt": "2023-05-20T14:30:22Z",
  "status": "published"
}
```

### Retrieving Reviews

**User Flow:**

1. A user browses a center, class, or coach profile.
2. The app calls the appropriate endpoint to retrieve reviews for that entity.
3. The reviews are displayed to the user to help them make informed decisions.

**API Call:**

```javascript
fetch('https://api.classz.com/api/review/64a12bc7a92d3f4e8b9c0d1e/center', {
  method: 'GET',
  headers: {
    'auth-token': 'YOUR_JWT_TOKEN'
  }
});
```

**Sample Response:**

```json
[
  {
    "_id": "review123",
    "reviewerId": "user789",
    "reviewerType": "user",
    "revieweeId": "64a12bc7a92d3f4e8b9c0d1e",
    "revieweeType": "center",
    "rating": 4.5,
    "title": "Excellent Center",
    "comment": "Great facility with excellent coaches. My child really enjoys the classes here.",
    "date": "2023-05-20T14:30:22Z"
  },
  {
    "_id": "review124",
    "reviewerId": "user456",
    "reviewerType": "user",
    "revieweeId": "64a12bc7a92d3f4e8b9c0d1e",
    "revieweeType": "center",
    "rating": 4,
    "title": "Good Experience",
    "comment": "Good center overall. Very clean facilities.",
    "date": "2023-05-19T10:15:45Z"
  }
]
```

### Checking for Pending Reviews

**User Flow:**

1. After a user attends a class or visits a center, the app reminds them to leave a review.
2. The app calls the appropriate endpoint to check for pending reviews.
3. The app displays a list of entities that the user has experienced but not yet reviewed.

**API Call (For Center):**

```javascript
fetch('https://api.classz.com/api/review/student/pending/64a12bc7a92d3f4e8b9c0d1e', {
  method: 'GET',
  headers: {
    'auth-token': 'YOUR_JWT_TOKEN'
  }
});
```

**API Call (For Class):**

```javascript
fetch('https://api.classz.com/api/review/student/pending/class/64a12bc7a92d3f4e8b9c0d2f', {
  method: 'GET',
  headers: {
    'auth-token': 'YOUR_JWT_TOKEN'
  }
});
```

**API Call (For Coach):**

```javascript
fetch('https://api.classz.com/api/review/student/pending/coach/64a12bc7a92d3f4e8b9c0d3f', {
  method: 'GET',
  headers: {
    'auth-token': 'YOUR_JWT_TOKEN'
  }
});
```

**Sample Response:**

```json
[
  {
    "centerId": "64a12bc7a92d3f4e8b9c0d1e",
    "centerName": "Aquatic Center",
    "lastAttended": "2023-05-18T09:00:00Z"
  },
  {
    "classId": "64a12bc7a92d3f4e8b9c0d2f",
    "className": "Advanced Swimming",
    "lastAttended": "2023-05-15T15:30:00Z",
    "centerName": "Sports Academy"
  }
]
```

### Getting Average Ratings

**User Flow:**

1. A user views the summary information for a center, class, or coach.
2. The app calls the appropriate endpoint to retrieve the average rating.
3. The average rating is displayed prominently to help the user make a decision.

**API Call:**

```javascript
fetch('https://api.classz.com/api/review/average/64a12bc7a92d3f4e8b9c0d1e/center', {
  method: 'GET',
  headers: {
    'auth-token': 'YOUR_JWT_TOKEN'
  }
});
```

**Sample Response:**

```json
{
  "averageRating": 4.3
}
```

## Error Handling

Here are common error responses and how to handle them:

1. **Authentication Error (401)**: The JWT token is invalid or expired. Re-authenticate the user.

2. **Not Eligible to Review (400)**: The user hasn't attended/experienced the entity they're trying to review. Verify eligibility status.

3. **Invalid Rating (400)**: The provided rating is outside the acceptable range (1-5). Validate input before submission.

4. **Already Reviewed (400)**: The user has already submitted a review for this entity. Redirect to edit the existing review instead.

5. **Server Error (500)**: An unexpected error occurred. Retry the request or contact support.

## Best Practices

1. **Timely Reviews**: Encourage users to submit reviews soon after their experience for more accurate feedback.

2. **Balanced Feedback**: Prompt users to provide both positive aspects and areas for improvement.

3. **Review Moderation**: Implement a moderation system for reviews to maintain quality and prevent abuse.

4. **Review Incentives**: Consider offering small incentives (like ClassZ points) for submitting thoughtful reviews.

5. **Filter and Sort**: Provide options for users to filter and sort reviews based on rating, recency, etc.

## Code Examples

### React Native Example (Submit Review)

```javascript
import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity } from 'react-native';
import { AirbnbRating } from 'react-native-ratings';
import AsyncStorage from '@react-native-async-storage/async-storage';

const SubmitReviewScreen = ({ route, navigation }) => {
  const { targetId, targetType, targetName } = route.params;
  
  const [rating, setRating] = useState(0);
  const [title, setTitle] = useState('');
  const [comment, setComment] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const submitReview = async () => {
    if (rating === 0) {
      setError('Please select a rating');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const token = await AsyncStorage.getItem('auth-token');
      
      const response = await fetch('https://api.classz.com/api/review', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'auth-token': token
        },
        body: JSON.stringify({
          targetType,
          targetId,
          rating,
          title,
          comment
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to submit review');
      }
      
      navigation.navigate('ReviewSuccess', { targetName });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <View style={styles.container}>
      <Text style={styles.header}>Review {targetName}</Text>
      
      <Text style={styles.label}>Rating</Text>
      <AirbnbRating
        count={5}
        defaultRating={0}
        size={30}
        showRating={false}
        onFinishRating={setRating}
      />
      
      <Text style={styles.label}>Title</Text>
      <TextInput
        style={styles.titleInput}
        value={title}
        onChangeText={setTitle}
        placeholder="Summarize your experience"
        maxLength={50}
      />
      
      <Text style={styles.label}>Comments</Text>
      <TextInput
        style={styles.commentInput}
        value={comment}
        onChangeText={setComment}
        placeholder="Share details of your experience"
        multiline
        numberOfLines={4}
        maxLength={500}
      />
      
      {error && <Text style={styles.error}>{error}</Text>}
      
      <TouchableOpacity
        style={[styles.submitButton, loading ? styles.disabledButton : null]}
        onPress={submitReview}
        disabled={loading}
      >
        <Text style={styles.submitButtonText}>
          {loading ? 'Submitting...' : 'Submit Review'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#fff',
  },
  header: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 5,
  },
  titleInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 10,
    fontSize: 16,
  },
  commentInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 10,
    fontSize: 16,
    textAlignVertical: 'top',
    minHeight: 100,
  },
  error: {
    color: 'red',
    marginTop: 10,
  },
  submitButton: {
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 5,
    marginTop: 20,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#A5D6A7',
  },
  submitButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default SubmitReviewScreen;
```

### React Native Example (Display Reviews)

```javascript
import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, StyleSheet, ActivityIndicator } from 'react-native';
import { AirbnbRating } from 'react-native-ratings';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ReviewsScreen = ({ route }) => {
  const { revieweeId, revieweeType, revieweeName } = route.params;
  
  const [reviews, setReviews] = useState([]);
  const [averageRating, setAverageRating] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    fetchReviews();
    fetchAverageRating();
  }, []);
  
  const fetchReviews = async () => {
    try {
      const token = await AsyncStorage.getItem('auth-token');
      
      const response = await fetch(`https://api.classz.com/api/review/${revieweeId}/${revieweeType}`, {
        method: 'GET',
        headers: {
          'auth-token': token
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch reviews');
      }
      
      setReviews(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  const fetchAverageRating = async () => {
    try {
      const token = await AsyncStorage.getItem('auth-token');
      
      const response = await fetch(`https://api.classz.com/api/review/average/${revieweeId}/${revieweeType}`, {
        method: 'GET',
        headers: {
          'auth-token': token
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch average rating');
      }
      
      setAverageRating(data.averageRating);
    } catch (err) {
      console.error('Error fetching average rating:', err);
    }
  };
  
  const renderReviewItem = ({ item }) => (
    <View style={styles.reviewItem}>
      <View style={styles.reviewHeader}>
        <Text style={styles.reviewTitle}>{item.title}</Text>
        <AirbnbRating
          count={5}
          defaultRating={item.rating}
          size={15}
          showRating={false}
          isDisabled
          readonly
        />
      </View>
      <Text style={styles.reviewDate}>
        {new Date(item.date).toLocaleDateString()}
      </Text>
      <Text style={styles.reviewComment}>{item.comment}</Text>
      <Text style={styles.reviewerName}>
        - {item.reviewerName || 'Anonymous'}
      </Text>
    </View>
  );
  
  if (loading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text>Loading reviews...</Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <Text style={styles.header}>Reviews for {revieweeName}</Text>
      
      <View style={styles.ratingContainer}>
        <Text style={styles.averageRating}>
          {averageRating.toFixed(1)}
        </Text>
        <AirbnbRating
          count={5}
          defaultRating={averageRating}
          size={20}
          showRating={false}
          isDisabled
          readonly
        />
        <Text style={styles.reviewCount}>
          Based on {reviews.length} {reviews.length === 1 ? 'review' : 'reviews'}
        </Text>
      </View>
      
      {error && <Text style={styles.error}>{error}</Text>}
      
      <FlatList
        data={reviews}
        renderItem={renderReviewItem}
        keyExtractor={(item) => item._id}
        ListEmptyComponent={
          <Text style={styles.emptyText}>
            No reviews yet. Be the first to review!
          </Text>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    backgroundColor: '#fff',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  ratingContainer: {
    alignItems: 'center',
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
  },
  averageRating: {
    fontSize: 36,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  reviewCount: {
    color: '#666',
    marginTop: 5,
  },
  reviewItem: {
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 15,
    marginBottom: 15,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  reviewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  reviewDate: {
    color: '#666',
    fontSize: 12,
    marginBottom: 10,
  },
  reviewComment: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 10,
  },
  reviewerName: {
    textAlign: 'right',
    fontStyle: 'italic',
    color: '#666',
  },
  error: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 15,
  },
  emptyText: {
    textAlign: 'center',
    color: '#666',
    marginTop: 20,
  },
});

export default ReviewsScreen;
```

### React Native Example (Pending Reviews Screen)

```javascript
import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, StyleSheet, TouchableOpacity } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const PendingReviewsScreen = ({ navigation }) => {
  const [pendingReviews, setPendingReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    fetchPendingReviews();
  }, []);
  
  const fetchPendingReviews = async () => {
    try {
      setLoading(true);
      const token = await AsyncStorage.getItem('auth-token');
      
      // Fetch center pending reviews
      const centerResponse = await fetch('https://api.classz.com/api/review/student/pending/all', {
        method: 'GET',
        headers: {
          'auth-token': token
        }
      });
      
      if (!centerResponse.ok) {
        throw new Error('Failed to fetch pending reviews');
      }
      
      const centerData = await centerResponse.json();
      
      setPendingReviews(centerData);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  const navigateToSubmitReview = (item) => {
    let targetId, targetType, targetName;
    
    if (item.centerId) {
      targetId = item.centerId;
      targetType = 'center';
      targetName = item.centerName;
    } else if (item.classId) {
      targetId = item.classId;
      targetType = 'class';
      targetName = item.className;
    } else if (item.coachId) {
      targetId = item.coachId;
      targetType = 'coach';
      targetName = item.coachName;
    }
    
    navigation.navigate('SubmitReview', {
      targetId,
      targetType,
      targetName
    });
  };
  
  const renderItem = ({ item }) => {
    let title, subtitle, date;
    
    if (item.centerId) {
      title = item.centerName;
      subtitle = 'Center';
      date = item.lastAttended;
    } else if (item.classId) {
      title = item.className;
      subtitle = `Class at ${item.centerName}`;
      date = item.lastAttended;
    } else if (item.coachId) {
      title = item.coachName;
      subtitle = 'Coach';
      date = item.lastInteraction;
    }
    
    return (
      <TouchableOpacity 
        style={styles.item}
        onPress={() => navigateToSubmitReview(item)}
      >
        <View style={styles.itemContent}>
          <Text style={styles.itemTitle}>{title}</Text>
          <Text style={styles.itemSubtitle}>{subtitle}</Text>
          <Text style={styles.itemDate}>
            Last visited: {new Date(date).toLocaleDateString()}
          </Text>
        </View>
        <Text style={styles.reviewButton}>Review</Text>
      </TouchableOpacity>
    );
  };
  
  if (loading) {
    return (
      <View style={styles.centered}>
        <Text>Loading pending reviews...</Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <Text style={styles.header}>Pending Reviews</Text>
      
      {error && <Text style={styles.error}>{error}</Text>}
      
      <FlatList
        data={pendingReviews}
        renderItem={renderItem}
        keyExtractor={(item, index) => index.toString()}
        ListEmptyComponent={
          <Text style={styles.emptyText}>
            You have no pending reviews. Thanks for keeping up!
          </Text>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    backgroundColor: '#fff',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
  },
  itemContent: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  itemSubtitle: {
    color: '#666',
    marginTop: 3,
  },
  itemDate: {
    color: '#888',
    fontSize: 12,
    marginTop: 5,
  },
  reviewButton: {
    color: '#2196F3',
    fontWeight: 'bold',
  },
  error: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 15,
  },
  emptyText: {
    textAlign: 'center',
    color: '#666',
    marginTop: 20,
  },
});

export default PendingReviewsScreen;
```

These examples demonstrate basic implementation of the review system in a React Native application. Adjust them according to your specific UI requirements and application architecture.

For more detailed information about the API endpoints, refer to the [Review API Documentation](./REVIEW_API_DOCUMENTATION.md) and the [Postman Collection](./REVIEW_API_POSTMAN_COLLECTION.json). 