# Attendance Verification API Documentation

This document outlines the API endpoints for the attendance verification system in the ClassZ Backend application.

## Overview

The Attendance Verification API enables students to verify their attendance at classes by generating unique verification codes and QR codes that coaches can scan or enter manually. This system ensures accurate attendance tracking and provides a seamless check-in experience.

## Authentication

All API requests require authentication using a JWT token. Add your token to the request headers:

```
auth-token: YOUR_JWT_TOKEN
```

## API Endpoints

### 1. Generate Attendance Code

Generates a unique verification code and QR code for a student attending a specific class.

- **URL**: `/api/attendance/generate`
- **Method**: `POST`
- **Authentication**: Required (Student)

#### Request Headers

| Header       | Value            | Description                  |
|--------------|------------------|------------------------------|
| Content-Type | application/json | Format of the request body   |
| auth-token   | YOUR_JWT_TOKEN   | JWT token for authentication |

#### Request Body

```json
{
  "classId": "class123",
  "studentId": "student456"
}
```

| Field     | Type   | Required | Description                           |
|-----------|--------|----------|---------------------------------------|
| classId   | string | Yes      | ID of the class to generate code for  |
| studentId | string | Yes      | ID of the student attending the class |

#### Success Response

- **Code**: 200 OK
- **Content**:

```json
{
  "verificationCode": "2735",
  "qrCodeData": "data:image/png;base64,iVBOR...",
  "expiresAt": "2023-05-20T10:30:00Z"
}
```

#### Error Responses

- **Code**: 400 Bad Request
- **Content**:

```json
{
  "message": "Student not enrolled in this class"
}
```

- **Code**: 500 Internal Server Error
- **Content**:

```json
{
  "message": "Failed to generate attendance code"
}
```

### 2. Verify Attendance Code

Verifies an attendance code entered by a coach and marks the student as present.

- **URL**: `/api/attendance/verify`
- **Method**: `POST`
- **Authentication**: Required (Coach)

#### Request Headers

| Header       | Value            | Description                  |
|--------------|------------------|------------------------------|
| Content-Type | application/json | Format of the request body   |
| auth-token   | YOUR_JWT_TOKEN   | JWT token for authentication |

#### Request Body

```json
{
  "verificationCode": "2735",
  "classId": "class123"
}
```

| Field            | Type   | Required | Description                                |
|------------------|--------|----------|--------------------------------------------|
| verificationCode | string | Yes      | The verification code to validate          |
| classId          | string | Yes      | ID of the class for attendance verification |

#### Success Response

- **Code**: 200 OK
- **Content**:

```json
{
  "success": true,
  "studentId": "student456",
  "className": "Advanced Swimming",
  "verifiedAt": "2023-05-20T09:15:32Z"
}
```

#### Error Responses

- **Code**: 400 Bad Request
- **Content**:

```json
{
  "success": false,
  "message": "Invalid verification code"
}
```

### 3. Verify Attendance via QR Code

Verifies attendance by scanning a QR code.

- **URL**: `/api/attendance/verify-qr`
- **Method**: `POST`
- **Authentication**: Required (Coach)

#### Request Headers

| Header       | Value            | Description                  |
|--------------|------------------|------------------------------|
| Content-Type | application/json | Format of the request body   |
| auth-token   | YOUR_JWT_TOKEN   | JWT token for authentication |

#### Request Body

```json
{
  "qrCodeData": "{\"code\":\"2735\",\"classId\":\"class123\",\"timestamp\":1621497600000}"
}
```

| Field       | Type   | Required | Description                        |
|-------------|--------|----------|------------------------------------|
| qrCodeData  | string | Yes      | The data extracted from the QR code |

#### Success Response

- **Code**: 200 OK
- **Content**:

```json
{
  "success": true,
  "studentId": "student456",
  "className": "Advanced Swimming",
  "verifiedAt": "2023-05-20T09:15:32Z"
}
```

#### Error Responses

- **Code**: 400 Bad Request
- **Content**:

```json
{
  "success": false,
  "message": "Invalid QR code data or expired code"
}
```

### 4. Get Attendance History

Retrieves attendance history for a specific class or student.

- **URL**: `/api/attendance/history`
- **Method**: `GET`
- **Authentication**: Required (Coach or Parent)

#### Request Headers

| Header     | Value          | Description                  |
|------------|----------------|------------------------------|
| auth-token | YOUR_JWT_TOKEN | JWT token for authentication |

#### Query Parameters

| Parameter | Type   | Required | Description                            |
|-----------|--------|----------|----------------------------------------|
| classId   | string | No       | ID of the class to get attendance for  |
| studentId | string | No       | ID of the student to get attendance for|
| startDate | string | No       | Start date for attendance records (ISO format) |
| endDate   | string | No       | End date for attendance records (ISO format)   |

#### Success Response

- **Code**: 200 OK
- **Content**:

```json
{
  "attendanceRecords": [
    {
      "studentId": "student456",
      "studentName": "Jane Doe",
      "classId": "class123",
      "className": "Advanced Swimming",
      "date": "2023-05-20T09:00:00Z",
      "status": "present",
      "verifiedAt": "2023-05-20T09:15:32Z"
    },
    {
      "studentId": "student456",
      "studentName": "Jane Doe",
      "classId": "class123",
      "className": "Advanced Swimming",
      "date": "2023-05-13T09:00:00Z",
      "status": "absent",
      "verifiedAt": null
    }
  ]
}
```

## Data Models

### Attendance Code Model

```javascript
const attendanceCodeSchema = new mongoose.Schema({
  classId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "class",
    required: true
  },
  studentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "user",
    required: true
  },
  code: {
    type: String,
    required: true
  },
  isUsed: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  expiresAt: {
    type: Date,
    required: true
  }
});
```

### Attendance Record Model

```javascript
const attendanceRecordSchema = new mongoose.Schema({
  classId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "class",
    required: true
  },
  studentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "user",
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ["present", "absent", "late", "excused"],
    default: "absent"
  },
  verifiedAt: {
    type: Date,
    default: null
  },
  verifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "user",
    default: null
  }
});
```

## Implementation Examples

### Generating a Verification Code

```javascript
// Example using fetch API
const generateAttendanceCode = async (classId, studentId) => {
  try {
    const response = await fetch('http://localhost:3000/api/attendance/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'auth-token': 'YOUR_JWT_TOKEN'
      },
      body: JSON.stringify({
        classId,
        studentId
      })
    });
    
    return await response.json();
  } catch (error) {
    console.error('Error generating attendance code:', error);
  }
};
```

### Verifying an Attendance Code

```javascript
// Example using fetch API
const verifyAttendanceCode = async (verificationCode, classId) => {
  try {
    const response = await fetch('http://localhost:3000/api/attendance/verify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'auth-token': 'YOUR_JWT_TOKEN'
      },
      body: JSON.stringify({
        verificationCode,
        classId
      })
    });
    
    return await response.json();
  } catch (error) {
    console.error('Error verifying attendance code:', error);
  }
};
```

## Best Practices

1. **Code Expiration**: Set verification codes to expire after a reasonable time period (e.g., 30 minutes)
2. **Security**: Ensure verification codes are sufficiently random and not easily guessable
3. **Rate Limiting**: Implement rate limiting to prevent brute force attacks
4. **Validation**: Validate that students are enrolled in the class before generating codes
5. **Logging**: Maintain detailed logs of attendance verifications for audit purposes 