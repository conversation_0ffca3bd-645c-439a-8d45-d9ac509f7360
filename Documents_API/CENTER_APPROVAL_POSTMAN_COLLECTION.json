{"info": {"name": "Center Approval API", "description": "A collection of requests for the Center Approval API in ClassZ Backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Verify Center", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "auth-token", "value": "{{admin_token}}", "description": "Admin JWT Token"}], "body": {"mode": "raw", "raw": "{\n  \"verified\": true\n}"}, "url": {"raw": "{{base_url}}/api/center/:id/verify", "host": ["{{base_url}}"], "path": ["api", "center", ":id", "verify"], "variable": [{"key": "id", "value": "6823f668a8a52c65b63c02fb", "description": "ID of the center to verify"}]}, "description": "Approves a center by setting its verified status to true"}}, {"name": "Revoke Center Verification", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "auth-token", "value": "{{admin_token}}", "description": "Admin JWT Token"}], "body": {"mode": "raw", "raw": "{\n  \"verified\": false\n}"}, "url": {"raw": "{{base_url}}/api/center/:id/verify", "host": ["{{base_url}}"], "path": ["api", "center", ":id", "verify"], "variable": [{"key": "id", "value": "6823f668a8a52c65b63c02fb", "description": "ID of the center to revoke verification for"}]}, "description": "Revokes a center's verification by setting its verified status to false"}}, {"name": "Get Center Details", "request": {"method": "GET", "header": [{"key": "auth-token", "value": "{{admin_token}}", "description": "Admin JWT Token"}], "url": {"raw": "{{base_url}}/api/center/:id", "host": ["{{base_url}}"], "path": ["api", "center", ":id"], "variable": [{"key": "id", "value": "6823f668a8a52c65b63c02fb", "description": "ID of the center to retrieve"}]}, "description": "Retrieves the details of a center, including its verification status"}}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "admin_token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MjIxYTA4MWUzMzliNzQzYTkyM2NiMiIsImlhdCI6MTc0NzE5MDQyM30.MDUL_f3tv6wGqBWzCPUpYpe84B3nP7U7HVQ1CL5iwsg", "type": "string"}]}