# ClassZ Chat Backend: Production Deployment Guide

This guide explains how to deploy the ClassZ Chat backend in production mode for optimal performance and reliability.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Configuration](#environment-configuration)
3. [Redis Setup](#redis-setup)
4. [Application Optimization](#application-optimization)
5. [Security Considerations](#security-considerations)
6. [Deployment Steps](#deployment-steps)
7. [Performance Monitoring](#performance-monitoring)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

Before deploying to production, ensure you have the following:

- Node.js 16.x or higher
- MongoDB 4.4 or higher
- Redis 6.x or higher (for caching)
- A Linux server (recommended Ubuntu 20.04 LTS or later)
- Domain name with SSL certificate (for secure WebSocket connections)
- PM2 or similar process manager for Node.js applications

## Environment Configuration

Create a production `.env` file with the following settings:

```
# Server Configuration
NODE_ENV=production
PORT=3000  # Adjust if needed
LOG_LEVEL=info
ENABLE_COMPRESSION=true

# MongoDB Configuration
DB_CONNECTION_STRING=mongodb://your-production-mongodb-uri

# Redis Configuration 
ENABLE_REDIS_CACHE=true
REDIS_HOST=localhost  # Use internal network address for cloud deployments
REDIS_PORT=6379
# REDIS_USERNAME=  # Uncomment and set if needed
# REDIS_PASSWORD=  # Uncomment and set if needed

# Security Settings
JWT_SECRET=your-strong-jwt-secret
```

### Important Environment Variables Explained

- `NODE_ENV=production`: Enables production optimizations
- `ENABLE_COMPRESSION=true`: Enables HTTP compression for better bandwidth usage
- `LOG_LEVEL=info`: Limits logging to essential information
- `ENABLE_REDIS_CACHE=true`: Enables Redis caching for improved performance
- `JWT_SECRET`: Set to a secure, unique value (minimum 32 characters)

## Redis Setup

Redis provides significant performance improvements for chat applications by caching conversation data and reducing database load.

### Installing Redis on Ubuntu

```bash
# Update package lists
sudo apt update

# Install Redis
sudo apt install redis-server

# Configure Redis to start on boot
sudo systemctl enable redis-server

# Start Redis service
sudo systemctl start redis-server

# Verify Redis is running
redis-cli ping
# Should return PONG
```

### Securing Redis

For production environments, configure Redis security:

1. Edit the Redis configuration:

```bash
sudo nano /etc/redis/redis.conf
```

2. Make the following changes:

```
# Bind to localhost only
bind 127.0.0.1
# Set a strong password
requirepass YourStrongPasswordHere
# Disable dangerous commands
rename-command FLUSHALL ""
rename-command FLUSHDB ""
rename-command DEBUG ""
```

3. Restart Redis:

```bash
sudo systemctl restart redis-server
```

4. Update your `.env` file with the Redis password:

```
REDIS_PASSWORD=YourStrongPasswordHere
```

## Application Optimization

### Node.js Settings

For production deployments, optimize Node.js:

1. Create an `ecosystem.config.js` file for PM2:

```javascript
module.exports = {
  apps: [{
    name: "classzChat",
    script: "./index.js",
    instances: "max",
    exec_mode: "cluster",
    env_production: {
      NODE_ENV: "production",
      PORT: 3000
    },
    max_memory_restart: "1G",
    log_date_format: "YYYY-MM-DD HH:mm:ss Z"
  }]
};
```

2. Start with PM2:

```bash
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

### MongoDB Optimization

For production MongoDB:

1. Ensure indexes are created for all collections used in querying
2. Consider using MongoDB Atlas for managed hosting
3. Enable connection pooling with appropriate settings

## Security Considerations

1. **SSL/TLS**: Always use HTTPS in production. Configure reverse proxy (Nginx/Apache) with SSL.

2. **Rate Limiting**: Configure rate limiting on public endpoints to prevent abuse.

3. **Input Validation**: Ensure all inputs are properly validated.

4. **Environment Variables**: Never commit `.env` files to repositories.

5. **Regular Updates**: Keep all dependencies updated to patch security vulnerabilities.

## Deployment Steps

### 1. Clone Repository

```bash
git clone https://your-repo-url/classZ_Backend.git
cd classZ_Backend
```

### 2. Install Dependencies

```bash
npm ci --production
```

### 3. Set Up Environment Variables

```bash
cp .env.example .env
nano .env
# Configure as detailed in the Environment Configuration section
```

### 4. Verify Redis Connection

```bash
node test-redis.js
```

### 5. Test the Application

```bash
NODE_ENV=production node index.js
```

### 6. Deploy with PM2

```bash
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

### 7. Set Up Reverse Proxy

Configure Nginx as a reverse proxy:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Performance Monitoring

### Setting Up Monitoring

1. **Server Monitoring**:
   - Use PM2 monitoring: `pm2 monit`
   - Set up PM2 Plus for advanced monitoring

2. **Application Monitoring**:
   - Consider implementing a monitoring solution like Prometheus + Grafana
   - Monitor key metrics like response times, error rates, and socket connections

3. **Redis Monitoring**:
   - Monitor Redis memory usage: `redis-cli info memory`
   - Check Redis hit ratio: `redis-cli info stats | grep hit_rate`

### Performance Benchmarks

A well-optimized ClassZ Chat backend should achieve:

- Message delivery latency < 100ms
- Connection establishment < 500ms
- Efficient handling of 1000+ concurrent connections

## Troubleshooting

### Redis Connection Issues

If Redis connection fails:

1. Verify Redis is running:
   ```bash
   sudo systemctl status redis-server
   ```

2. Check Redis log for errors:
   ```bash
   sudo tail -f /var/log/redis/redis-server.log
   ```

3. Test Redis connectivity:
   ```bash
   node test-redis.js
   ```

### Socket Connection Issues

If WebSocket connections are failing:

1. Check firewall settings to ensure WebSocket traffic is allowed
2. Verify Nginx or Apache configuration properly handles WebSocket upgrade requests
3. Test connection without reverse proxy to isolate issue

### Performance Issues

If chat is still slow:

1. Check database indexes:
   ```javascript
   // Verify these indexes are created
   chatModel.index({ sender: 1, recipient: 1 });
   chatModel.index({ recipient: 1, sender: 1 });
   chatModel.index({ timestamp: -1 });
   conversationSchema.index({ "participants.userId": 1 });
   conversationSchema.index({ updatedAt: -1 });
   ```

2. Verify Redis cache is working:
   ```
   // Look for these messages in logs
   "Cache hit for conversation ${conversationId}"
   ```

3. Profile MongoDB queries:
   ```javascript
   // Run with profiling
   db.setProfilingLevel(2, 100); // Profile queries taking more than 100ms
   ```

## Conclusion

Following this guide will help you deploy a high-performance, reliable ClassZ Chat backend in production. Proper configuration of Redis caching and application settings significantly improves chat performance and user experience.

For additional assistance, refer to the `setup-redis.js` and `test-redis.js` utilities included in the codebase. 