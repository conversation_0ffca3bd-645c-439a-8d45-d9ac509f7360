# Attendance Verification API Usage Guide

This guide provides step-by-step instructions for implementing and using the Attendance Verification API in the ClassZ application.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Authentication](#authentication)
4. [Common Use Cases](#common-use-cases)
   - [Generating QR Codes for Attendance](#generating-qr-codes-for-attendance)
   - [Verifying Attendance via QR Code](#verifying-attendance-via-qr-code)
   - [Manual Attendance Verification](#manual-attendance-verification)
   - [Retrieving Attendance Records](#retrieving-attendance-records)
5. [Error Handling](#error-handling)
6. [Best Practices](#best-practices)
7. [Code Examples](#code-examples)

## Overview

The Attendance Verification API enables a seamless digital check-in process for students attending classes. It provides functionality for generating and verifying unique codes and QR codes, as well as retrieving attendance history. The system ensures accurate attendance tracking while minimizing administrative overhead.

## Prerequisites

Before using the Attendance Verification API, ensure you have:

1. A valid ClassZ account with appropriate permissions (Student, Coach, or Administrator)
2. Authentication credentials (JWT token)
3. Knowledge of relevant IDs (student IDs, class IDs, etc.)
4. For mobile apps: QR code scanning capabilities

## Authentication

All API requests require authentication using a JWT token. Add your token to the request headers:

```
auth-token: YOUR_JWT_TOKEN
```

## Common Use Cases

### Generating QR Codes for Attendance

**Student Flow:**

1. When a student arrives at a class, they should generate an attendance verification code.
2. The student opens their ClassZ app and navigates to the class check-in screen.
3. The app calls the `/api/attendance/generate` endpoint with the student's ID and the class ID.
4. The API returns a unique verification code and QR code data.
5. The student shows the QR code to the coach for scanning.

**API Call:**

```javascript
fetch('https://api.classz.com/api/attendance/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'auth-token': 'YOUR_JWT_TOKEN'
  },
  body: JSON.stringify({
    classId: '64a12bc7a92d3f4e8b9c0d1e',
    studentId: '64a12bc7a92d3f4e8b9c0d2f'
  })
});
```

**Sample Response:**

```json
{
  "verificationCode": "2735",
  "qrCodeData": "data:image/png;base64,iVBOR...",
  "expiresAt": "2023-05-20T10:30:00Z"
}
```

### Verifying Attendance via QR Code

**Coach Flow:**

1. The coach opens the ClassZ app and navigates to the attendance verification screen.
2. The coach selects the class from the list.
3. The coach scans the QR code shown by the student.
4. The app calls the `/api/attendance/verify` endpoint with the QR code data.
5. If successful, the student is marked as present for the class.

**API Call:**

```javascript
fetch('https://api.classz.com/api/attendance/verify', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'auth-token': 'YOUR_JWT_TOKEN'
  },
  body: JSON.stringify({
    qrCodeData: "{\"code\":\"2735\",\"classId\":\"64a12bc7a92d3f4e8b9c0d1e\",\"timestamp\":1621497600000}"
  })
});
```

**Sample Response:**

```json
{
  "isVerified": true
}
```

### Manual Attendance Verification

In cases where QR code scanning is not feasible, the coach can enter the verification code manually:

**Coach Flow:**

1. The coach asks the student for their verification code.
2. The coach enters the code in the ClassZ app along with the class ID.
3. The app calls the `/api/attendance/verify` endpoint with the code and class ID.
4. If successful, the student is marked as present for the class.

**API Call:**

```javascript
fetch('https://api.classz.com/api/attendance/verify', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'auth-token': 'YOUR_JWT_TOKEN'
  },
  body: JSON.stringify({
    verificationCode: "2735",
    classId: "64a12bc7a92d3f4e8b9c0d1e"
  })
});
```

### Retrieving Attendance Records

**Coach/Administrator Flow:**

1. The coach or administrator navigates to the attendance records screen in the ClassZ app.
2. They select a class to view attendance history.
3. The app calls the appropriate endpoint to retrieve attendance records.
4. The attendance history is displayed, showing who was present/absent for each class session.

**API Call (Class Attendance):**

```javascript
fetch('https://api.classz.com/api/attendance/64a12bc7a92d3f4e8b9c0d1e', {
  method: 'GET',
  headers: {
    'auth-token': 'YOUR_JWT_TOKEN'
  }
});
```

**API Call (Student Attendance):**

```javascript
fetch('https://api.classz.com/api/attendance/child/64a12bc7a92d3f4e8b9c0d2f', {
  method: 'GET',
  headers: {
    'auth-token': 'YOUR_JWT_TOKEN'
  }
});
```

## Error Handling

Here are common error responses and how to handle them:

1. **Authentication Error (401)**: The JWT token is invalid or expired. Re-authenticate the user.

2. **Invalid Code (400)**: The verification code is incorrect or has expired. Ask the student to generate a new code.

3. **Student Not Enrolled (400)**: The student is not enrolled in the specified class. Verify enrollment status.

4. **Server Error (500)**: An unexpected error occurred. Retry the request or contact support.

## Best Practices

1. **Code Expiration**: Verification codes should expire after a short period (typically 15-30 minutes) to prevent misuse.

2. **Offline Support**: Implement a mechanism to store attendance records locally when connectivity is limited, and sync them when back online.

3. **Batch Processing**: For classes with many students, implement batch verification to streamline the process.

4. **QR Code Security**: Ensure QR codes include timestamps and encrypt sensitive information to prevent replay attacks.

5. **Attendance Reminders**: Send notifications to students who frequently miss classes.

## Code Examples

### React Native Example (Student App)

```javascript
import React, { useState } from 'react';
import { View, Button, Image, Text, StyleSheet } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const AttendanceScreen = ({ classId, studentId }) => {
  const [qrCode, setQrCode] = useState(null);
  const [verificationCode, setVerificationCode] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const generateCode = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const token = await AsyncStorage.getItem('auth-token');
      
      const response = await fetch('https://api.classz.com/api/attendance/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'auth-token': token
        },
        body: JSON.stringify({
          classId,
          studentId
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to generate code');
      }
      
      setQrCode(data.qrCodeData);
      setVerificationCode(data.verificationCode);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Class Check-In</Text>
      
      {qrCode ? (
        <View style={styles.codeContainer}>
          <Image source={{ uri: qrCode }} style={styles.qrCode} />
          <Text style={styles.codeText}>Verification Code: {verificationCode}</Text>
          <Text style={styles.hint}>Show this to your coach to verify attendance</Text>
        </View>
      ) : (
        <Button 
          title={loading ? "Generating..." : "Generate Check-In Code"} 
          onPress={generateCode}
          disabled={loading}
        />
      )}
      
      {error && <Text style={styles.error}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  codeContainer: {
    alignItems: 'center',
  },
  qrCode: {
    width: 250,
    height: 250,
    marginBottom: 20,
  },
  codeText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  hint: {
    fontSize: 14,
    color: 'gray',
    textAlign: 'center',
  },
  error: {
    color: 'red',
    marginTop: 20,
  },
});

export default AttendanceScreen;
```

### React Native Example (Coach App)

```javascript
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, Button, FlatList } from 'react-native';
import { Camera } from 'expo-camera';
import { BarCodeScanner } from 'expo-barcode-scanner';
import AsyncStorage from '@react-native-async-storage/async-storage';

const VerifyAttendanceScreen = ({ classId }) => {
  const [hasPermission, setHasPermission] = useState(null);
  const [scanMode, setScanMode] = useState(false);
  const [manualCode, setManualCode] = useState('');
  const [attendanceList, setAttendanceList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState(null);

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
    
    fetchAttendance();
  }, []);

  const fetchAttendance = async () => {
    try {
      setLoading(true);
      const token = await AsyncStorage.getItem('auth-token');
      
      const response = await fetch(`https://api.classz.com/api/attendance/${classId}`, {
        method: 'GET',
        headers: {
          'auth-token': token
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch attendance');
      }
      
      setAttendanceList(data);
    } catch (err) {
      setMessage({ type: 'error', text: err.message });
    } finally {
      setLoading(false);
    }
  };

  const verifyAttendance = async (code) => {
    try {
      setLoading(true);
      const token = await AsyncStorage.getItem('auth-token');
      
      const response = await fetch('https://api.classz.com/api/attendance/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'auth-token': token
        },
        body: JSON.stringify({
          verificationCode: code,
          classId
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Verification failed');
      }
      
      setMessage({ type: 'success', text: 'Attendance verified successfully!' });
      fetchAttendance();
      setManualCode('');
    } catch (err) {
      setMessage({ type: 'error', text: err.message });
    } finally {
      setLoading(false);
      setScanMode(false);
    }
  };

  const handleBarCodeScanned = ({ data }) => {
    try {
      // Parse QR code data
      const parsedData = JSON.parse(data);
      if (parsedData.code && parsedData.classId) {
        verifyAttendance(parsedData.code);
      } else {
        setMessage({ type: 'error', text: 'Invalid QR code' });
      }
    } catch (err) {
      setMessage({ type: 'error', text: 'Failed to parse QR code' });
    }
  };

  if (hasPermission === null) {
    return <Text>Requesting camera permission...</Text>;
  }
  
  if (hasPermission === false) {
    return <Text>No access to camera. Please enable camera permissions.</Text>;
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Verify Attendance</Text>
      
      {scanMode ? (
        <View style={styles.cameraContainer}>
          <BarCodeScanner
            onBarCodeScanned={handleBarCodeScanned}
            style={styles.camera}
          />
          <Button title="Cancel Scan" onPress={() => setScanMode(false)} />
        </View>
      ) : (
        <View style={styles.manualContainer}>
          <Button title="Scan QR Code" onPress={() => setScanMode(true)} />
          
          <Text style={styles.orText}>OR</Text>
          
          <Text style={styles.label}>Enter verification code manually:</Text>
          <TextInput
            style={styles.input}
            value={manualCode}
            onChangeText={setManualCode}
            placeholder="Enter 4-digit code"
            keyboardType="number-pad"
            maxLength={4}
          />
          <Button
            title={loading ? "Verifying..." : "Verify Code"}
            onPress={() => verifyAttendance(manualCode)}
            disabled={loading || manualCode.length !== 4}
          />
        </View>
      )}
      
      {message && (
        <Text style={[styles.message, message.type === 'error' ? styles.error : styles.success]}>
          {message.text}
        </Text>
      )}
      
      <View style={styles.attendanceContainer}>
        <Text style={styles.subtitle}>Attendance List</Text>
        <FlatList
          data={attendanceList}
          keyExtractor={(item) => item._id}
          renderItem={({ item }) => (
            <View style={styles.attendanceItem}>
              <Text style={styles.studentName}>{item.studentName}</Text>
              <Text style={[
                styles.statusBadge,
                item.attendanceStatus === 'present' ? styles.presentBadge : styles.absentBadge
              ]}>
                {item.attendanceStatus}
              </Text>
            </View>
          )}
          ListEmptyComponent={
            <Text style={styles.emptyText}>No attendance records found</Text>
          }
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  cameraContainer: {
    aspectRatio: 1,
    width: '100%',
    marginBottom: 20,
  },
  camera: {
    flex: 1,
  },
  manualContainer: {
    marginBottom: 20,
  },
  orText: {
    textAlign: 'center',
    marginVertical: 10,
    fontWeight: 'bold',
  },
  label: {
    marginTop: 10,
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    padding: 10,
    marginBottom: 10,
    fontSize: 18,
    textAlign: 'center',
  },
  message: {
    padding: 10,
    borderRadius: 5,
    marginBottom: 20,
    textAlign: 'center',
  },
  error: {
    backgroundColor: '#ffecec',
    color: '#f44336',
  },
  success: {
    backgroundColor: '#e8f5e9',
    color: '#4caf50',
  },
  attendanceContainer: {
    flex: 1,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  attendanceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  studentName: {
    fontSize: 16,
  },
  statusBadge: {
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 20,
    overflow: 'hidden',
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  presentBadge: {
    backgroundColor: '#e8f5e9',
    color: '#4caf50',
  },
  absentBadge: {
    backgroundColor: '#ffecec',
    color: '#f44336',
  },
  emptyText: {
    textAlign: 'center',
    marginTop: 20,
    color: 'gray',
  },
});

export default VerifyAttendanceScreen;
```

These examples demonstrate basic implementation of the attendance verification feature in a React Native application. Adjust them according to your specific UI requirements and application architecture.

For more detailed information about the API endpoints, refer to the [Attendance Verification API Documentation](./ATTENDANCE_VERIFICATION_API_DOCUMENTATION.md) and the [Postman Collection](./ATTENDANCE_VERIFICATION_POSTMAN_COLLECTION.json). 