{"info": {"_postman_id": "ff7e4e39-0c3d-4e2c-bfef-bcae92c7d8cb", "name": "ClassZ Address API", "description": "Collection for ClassZ Address API including authentication and address management endpoints.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "description": "Endpoints for authentication and user registration", "item": [{"name": "Request OTP", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/otp?email={{email}}", "host": ["{{base_url}}"], "path": ["api", "otp"], "query": [{"key": "email", "value": "{{email}}"}]}, "description": "Get an OTP (One-Time Password) to register or verify an account"}, "response": []}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{email}}\",\n    \"otp\": \"{{otp}}\"\n}"}, "url": {"raw": "{{base_url}}/api/otp/verify", "host": ["{{base_url}}"], "path": ["api", "otp", "verify"]}, "description": "Verify the OTP code received via email"}, "response": []}, {"name": "Sign Up", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);", "if (jsonData.token) {", "    pm.environment.set(\"token\", jsonData.token);", "}", "if (jsonData.user && jsonData.user._id) {", "    pm.environment.set(\"userId\", jsonData.user._id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{email}}\",\n    \"password\": \"{{password}}\",\n    \"type\": \"parent\",\n    \"otp\": \"{{otp}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/signup", "host": ["{{base_url}}"], "path": ["api", "auth", "signup"]}, "description": "Register a new user with the verified OTP"}, "response": []}, {"name": "Sign In", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);", "if (jsonData.token) {", "    pm.environment.set(\"token\", jsonData.token);", "}", "", "// Extract and set the userId from the parent object", "if (jsonData.data && jsonData.data.parent && jsonData.data.parent._id) {", "    pm.environment.set(\"userId\", jsonData.data.parent._id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{email}}\",\n    \"password\": \"{{password}}\",\n    \"type\": \"parent\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/signin", "host": ["{{base_url}}"], "path": ["api", "auth", "signin"]}, "description": "Sign in and get authentication token"}, "response": []}]}, {"name": "Address API", "description": "Endpoints for managing user addresses", "item": [{"name": "Get All Addresses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/address/user/{{userId}}", "host": ["{{base_url}}"], "path": ["api", "address", "user", "{{userId}}"]}, "description": "Get all addresses for a user"}, "response": []}, {"name": "Get Default Address", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/address/user/{{userId}}/default", "host": ["{{base_url}}"], "path": ["api", "address", "user", "{{userId}}", "default"]}, "description": "Get the default address for a user"}, "response": []}, {"name": "Get Address with Map", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/address/user/{{userId}}/map", "host": ["{{base_url}}"], "path": ["api", "address", "user", "{{userId}}", "map"]}, "description": "Get an address with map formatting"}, "response": []}, {"name": "Add New Address", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);", "", "// Extract and set the addressId from the first location in the user object", "if (jsonData.user && jsonData.user.location && jsonData.user.location.length > 0) {", "    pm.environment.set(\"addressId\", jsonData.user.location[0]._id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n    \"flatFloorBlock\": \"G032, G/F\",\n    \"buildingEstate\": \"ABC Mall\",\n    \"district\": \"Causeway Bay\",\n    \"region\": \"Hong Kong\",\n    \"country\": \"Hong Kong\",\n    \"coordinates\": {\n        \"lat\": 22.2799,\n        \"lng\": 114.1880\n    },\n    \"default\": true\n}"}, "url": {"raw": "{{base_url}}/api/address/user/{{userId}}", "host": ["{{base_url}}"], "path": ["api", "address", "user", "{{userId}}"]}, "description": "Add a new address for a user"}, "response": []}, {"name": "Get Specific Address with Map", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/address/user/{{userId}}/{{addressId}}/map", "host": ["{{base_url}}"], "path": ["api", "address", "user", "{{userId}}", "{{addressId}}", "map"]}, "description": "Get a specific address with map formatting"}, "response": []}, {"name": "Update Address", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n    \"flatFloorBlock\": \"G032, G/F (Updated)\",\n    \"buildingEstate\": \"ABC Mall (Updated)\",\n    \"district\": \"Causeway Bay\",\n    \"region\": \"Hong Kong\",\n    \"coordinates\": {\n        \"lat\": 22.2800,\n        \"lng\": 114.1881\n    }\n}"}, "url": {"raw": "{{base_url}}/api/address/user/{{userId}}/{{addressId}}", "host": ["{{base_url}}"], "path": ["api", "address", "user", "{{userId}}", "{{addressId}}"]}, "description": "Update an existing address"}, "response": []}, {"name": "<PERSON> Default Address", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/address/user/{{userId}}/{{addressId}}/default", "host": ["{{base_url}}"], "path": ["api", "address", "user", "{{userId}}", "{{addressId}}", "default"]}, "description": "Set an address as the default address"}, "response": []}, {"name": "Delete Address", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/address/user/{{userId}}/{{addressId}}", "host": ["{{base_url}}"], "path": ["api", "address", "user", "{{userId}}", "{{addressId}}"]}, "description": "Delete an address"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}, {"key": "email", "value": "<EMAIL>", "type": "string"}, {"key": "password", "value": "yourpassword", "type": "string"}, {"key": "otp", "value": "1234", "type": "string"}, {"key": "token", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "addressId", "value": "", "type": "string"}]}