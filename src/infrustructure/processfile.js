// src/infrastructure/fileProcessor/fileProcessor.js

function processFiles(req) {
  const centerData = req.body;

  if (req.files) {
    if (req.files.hkidCard) {
      console.log(
        "HKID Card file paths:",
        req.files.hkidCard.map((file) => file.path)
      );
      centerData.hkidCard = req.files.hkidCard.map((file) => ({
        path: file.path, // Preserve path explicitly
        url: file.path || file.url,
        contentType: file.mimetype,
        originalname: file.originalname,
      }));
    }
    if (req.files.businessCertificate) {
      console.log(
        "Business Certificate file paths:",
        req.files.businessCertificate.map((file) => file.path)
      );
      centerData.businessCertificate = req.files.businessCertificate.map(
        (file) => ({
          path: file.path, // Preserve path explicitly
          url: file.path || file.url,
          contentType: file.mimetype,
          originalname: file.originalname,
        })
      );
    }
    if (req.files.sexualConvictionRecord) {
      console.log(
        "Sexual Conviction Record file paths:",
        req.files.sexualConvictionRecord.map((file) => file.path)
      );
      centerData.sexualConvictionRecord = req.files.sexualConvictionRecord.map(
        (file) => ({
          path: file.path, // Preserve path explicitly
          url: file.path || file.url,
          contentType: file.mimetype,
          originalname: file.originalname,
        })
      );
    }
    if (req.files.mainImage) {
      console.log("Main Image file path:", req.files.mainImage[0].path);
      centerData.mainImage = {
        path: req.files.mainImage[0].path, // Preserve path explicitly
        url: req.files.mainImage[0].path || req.files.mainImage[0].url,
        contentType: req.files.mainImage[0].mimetype,
        originalname: req.files.mainImage[0].originalname,
      };
    }
    if (req.files.images) {
      console.log(
        "Images file paths:",
        req.files.images.map((file) => file.path)
      );
      centerData.images = req.files.images.map((file) => ({
        path: file.path, // Preserve path explicitly
        url: file.path || file.url,
        contentType: file.mimetype,
        originalname: file.originalname,
      }));
    }
  }

  return centerData;
}

module.exports = { processFiles };
