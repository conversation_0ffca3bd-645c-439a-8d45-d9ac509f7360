class INotificationRepository {
  async saveOrUpdateToken(userId, token) {
    throw new Error("Method 'saveOrUpdate' must be implemented");
  }
  async getTokenById(userId) {
    throw new Error("Method getTokenById must be implemented");
  }
  async getNotification(userId) {
    throw new Error('Method getNotification must be implemented');
  }

  async saveNotification(data) {
    throw new Error('Method saveNotification must be implemented');
  }
}

module.exports = INotificationRepository;
