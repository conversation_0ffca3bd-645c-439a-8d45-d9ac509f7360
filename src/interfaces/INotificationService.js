class INotificationService {
  constructor() {
    if (this.constructor === INotificationService) {
      throw new Error("Cannot instantiate an interface directly");
    }
  }

  async saveOrUpdateToken(userId, token) {
    throw new Error("Method 'saveOrUpdateToken' must be implemented");
  }

  static async getTokenById(userId) {
    throw new Error("Method 'getTokenById' must be implemented");
  }
  async getNotification(userId) {
    throw new Error("Method getNotification must be implemented");
  }
  static async sendNotification(userId, token, message) {
    throw new Error("Method sendNotification must be implemented");
  }
  static async scheduleNotification(userId, token, message, date) {
    throw new Error("Method scheduleNotification must be implemented");
  }
}

module.exports = INotificationService;
