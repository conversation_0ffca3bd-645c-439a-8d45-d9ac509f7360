class IPaymentService {
  async createCustomer(email, cardToken) {
    throw new Error("Method not implemented.");
  }

  async addCardToCustomer(customerId, cardToken) {
    throw new Error("Method not implemented.");
  }

  async getSavedCard(customerId) {
    throw new Error("Method not implemented");
  }

  async createToken(card) {
    throw new Error("Method not implemented");
  }

  async createZCoinPaymentIntent(userId, amount, cardToken) {
    throw new Error("Method not implemented.");
  }

  async confirmZCoinPaymentIntent(paymentIntentId) {
    throw new Error("Method not implemented.");
  }

  async createSubscription(customerId, priceId) {
    throw new Error("Method not implemented.");
  }
}

module.exports = IPaymentService;
