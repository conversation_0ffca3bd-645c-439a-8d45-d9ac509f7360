const Class = require("../models/classModel");

class ClassRepo {
  // Create a new class
  async create(classData) {
    try {
      // Basic validation
      if (!classData.classProviding || !classData.center) {
        throw new Error("Missing required fields: classProviding or center");
      }

      const classs = new Class(classData);
      return await classs.save();
    } catch (error) {
      console.error("Error creating class:", error);
      throw error;
    }
  }

  // Get a class by ID
  async getById(classId) {
    try {
      console.log("HI");
      const classDetails = await Class.findById(classId)
        .populate({
          path: "center",
          select: "legalName displayName address companyNumber",
        })
        .populate({ path: "coach" });
      //  .populate({ path: "student" });
      // Populate center fields
      if (!classDetails) {
        throw new Error("Class not found");
      }
      return classDetails;
    } catch (error) {
      console.error("Error fetching class:", error);
      throw error;
    }
  }
  async getByIdForCheck(classId) {
    return await Class.findById(classId);
  }
  async getByIdWithoutPopulate(classId) {
    try {
      const classDetails = await Class.findById(classId);
      return classDetails;
    } catch (error) {
      console.error("Error fetching class:", error);
      throw error;
    }
  }
  async getByCategory(category) {
    try {
      // Fetch classes that match the category, along with their centers
      const classes = await Class.find({
        category: { $in: [category] }, // Match category
      }).populate("center"); // Populate the center object

      // Safely filter out null centers and deduplicate
      const centers = classes
        .map((cls) => cls.center) // Extract centers from classes
        .filter((center) => center != null); // Remove null or undefined centers

      const uniqueCenters = Array.from(
        new Map(
          centers.map((center) => [center._id.toString(), center]) // Use _id as the key for deduplication
        ).values()
      );

      console.log("Unique Centers:", uniqueCenters); // Debugging output
      return uniqueCenters;
    } catch (error) {
      console.error("Error fetching centers by category:", error);
      return []; // Return an empty array on error
    }
  }
  async findByOnlyNameWithCenterAndCoach(classId) {
    return await Class.findById(classId)
      .select("mainImage center coach classProviding")
      .populate([
        {
          path: "center",
          select: "displayName",
        },
        {
          path: "coach",
          select: "displayName",
        }
      ]);
  }
  

  // Get all classes with optional pagination
  async getAll() {
    try {
      const classes = await Class.find()
        .populate({
          path: "center",
          select: "legalName displayName address companyNumber",
        })
        .populate({ path: "coach" })
        .populate({ path: "student" });
      return classes;
    } catch (error) {
      console.error("Error fetching classes:", error);
      throw error;
    }
  }

  // Update a class by ID
  async update(classId, classData) {
    try {
      const classs = await Class.findByIdAndUpdate(classId, classData, {
        new: true,
      });
      if (!classs) {
        throw new Error("Class not found");
      }
      return classs;
    } catch (error) {
      console.error("Error updating class:", error);
      throw error;
    }
  }

  // Delete a class by ID
  async delete(classId) {
    try {
      const classs = await Class.findByIdAndDelete(classId);
      if (!classs) {
        throw new Error("Class not found");
      }
      return classs;
    } catch (error) {
      console.error("Error deleting class:", error);
      throw error;
    }
  }

  // Get all classes by center ID
  async getByCenter(centerId) {
    try {
      console.log("HELLO: " + centerId);
      const classes = await Class.find({ center: centerId })
        .populate({
          path: "center",
          select: "legalName displayName address companyNumber",
        })
        .populate({ path: "coach" });
      console.log(classes);
      return classes;
    } catch (error) {
      console.error("Error fetching classes by center:", error);
      throw error;
    }
  }
  async getClassesByCoach(coachId) {
    try {
      const classes = await Class.find({ coach: coachId });
      return classes;
    } catch (error) {
      throw error;
    }
  }
  async getStudentsWithParent(classId) {
    try {
      console.log(classId);
      const students = await Class.findById(classId)
        .select("student")
        .populate({
          path: "student", // Populate student
          select: "_id parent",
        });
      // console.log(students);
      return students;
    } catch (error) {
      throw error;
    }
  }
  async getStudents(classId) {
    try {
      const classData = await Class.findById(classId)
        .select("student")
        .populate({
          path: "student",
          select: "fullname birthday rating phone mainImage parent",
        });

      // If class not found
      if (!classData) {
        throw new Error("Class not found");
      }

      // Return just the student array
      return classData.student;
    } catch (error) {
      throw error;
    }
  }
  async getCoachCenter(classId) {
    try {
      const classData = await Class.findById(classId)
        .select("coach center")
        .populate({
          path: "coach",
          select: "rating mainImage  skill.skillname displayName",
        })
        .populate({
          path: "center",
          select: "address centerNumber displayName",
        });
      if (!classData) {
        throw new Error("Class not found");
      }
      return classData;
    } catch (error) {
      throw error;
    }
  }

  async findByIds(classIds) {
    try {
      const classes = await Class.find({ _id: { $in: classIds } })
        // Select only fields needed by ReviewUseCase or other potential callers.
        // Adjust these fields as necessary.
        .select(
          "_id name classProviding description center coach student dates numberOfClass category"
        ) // Example fields
        .populate({
          path: "center",
          select: "legalName displayName address companyNumber mainImage", // Fields needed from center
        })
        .populate({ path: "coach", select: "displayName mainImage" }) // Fields needed from coach
        .lean(); // Use .lean() for performance
      return classes;
    } catch (error) {
      console.error("Error fetching classes by IDs:", error);
      throw error;
    }
  }

  // Update coach for a class
  async updateCoach(classId, coachId) {
    try {
      const updatedClass = await Class.findByIdAndUpdate(
        classId,
        { coach: coachId },
        { new: true }
      );

      if (!updatedClass) {
        throw new Error("Class not found");
      }

      return true;
    } catch (error) {
      console.error("❌ Error updating class coach:", error);
      throw new Error("Failed to update class coach: " + error.message);
    }
  }

  // Remove coach from all assigned classes
  async removeCoachFromAllClasses(coachId) {
    try {
      // Find all classes where this coach is assigned and update them
      const result = await Class.updateMany(
        { coach: coachId },
        { $unset: { coach: "" } }
      );

      return {
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount,
      };
    } catch (error) {
      console.error("❌ Error removing coach from classes:", error);
      throw new Error("Failed to remove coach from classes: " + error.message);
    }
  }

  // Update multiple classes based on query
  async updateMany(query, update, options = {}) {
    try {
      const result = await Class.updateMany(query, update, options);
      return {
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount,
      };
    } catch (error) {
      console.error("❌ Error updating multiple classes:", error);
      throw new Error("Failed to update classes: " + error.message);
    }
  }

  // Admin Controller required methods
  async count(query) {
    try {
      return await Class.countDocuments(query);
    } catch (error) {
      console.error("Error in ClassRepo count:", error);
      throw new Error("Error counting classes: " + error.message);
    }
  }

  async findById(classId) {
    try {
      const classData = await Class.findById(classId);
      if (!classData) {
        throw new Error("Class not found");
      }
      return classData;
    } catch (error) {
      console.error("Error in ClassRepo findById:", error);
      throw new Error("Error finding class by ID: " + error.message);
    }
  }
}

module.exports = ClassRepo;
