// data/repositories/coachRepository.js
const Coach = require("../models/coachModel");
const BaseUser = require("../models/baseUserModel");
const mongoose = require("mongoose");

class CoachRepository {
  async save(coachData) {
    console.log("coachData");
    const coach = new Coach(coachData);
    return await coach.save();
  }

  async create(coachData) {
    const { baseUser, ...otherCoachData } = coachData;

    const existingUser = await BaseUser.findById(baseUser);
    if (!existingUser) {
      throw new Error(
        "BaseUser not found. Cannot create center without a base account."
      );
    }
    if (!existingUser.roles.includes("coach")) {
      existingUser.roles.push("coach");
      await existingUser.save(); // Save the updated roles in BaseUser
    }
    const coach = new Coach({
      ...otherCoachData,
      baseUser: existingUser._id, // Associate the existing BaseUser with the new Center
    });

    return await coach.save();
  }

  async findById(id) {
    try {
      return await Coach.findById(id);
    } catch (error) {
      throw error;
    }
  }

  async findByIdWithCenter(id) {
    try {
      return await Coach.findById(id).populate({
        path: "center",
        select: "-businessCertificate -hkidCard",
        populate: {
          path: "owner",
          select: "isIndividualCreator"
        }
      });
    } catch (error) {
      throw error;
    }
  }

  async findByEmail(email) {
    return await Coach.findOne({ email }).populate({
      path: "center",
      select: "_id displayName owner", // add 'name' too if you want it
    });
  }

  async findByBaseUserId(baseUserId) {
    console.log(baseUserId);

    return await Coach.findOne({ baseUser: baseUserId }).populate({
      path: "center",
      select: "_id displayName owner", // add 'name' too if you want it
    });
  }

  // Update coach information (with session)
  async update(coachId, coachData, session = null) {
    try {
      console.log("Updating coach:", coachId);
      console.log(coachData);

      const updatedCoach = await Coach.findByIdAndUpdate(coachId, coachData, {
        new: true,
        session: session, // Use session for the update
      }).populate({
        path: "center",
        select: "_id displayName owner", // add 'name' too if you want it
      });

      return updatedCoach;
    } catch (error) {
      console.error("Error updating coach:", error);
      throw new Error("Error updating coach: " + error.message);
    }
  }

  async delete(coachId) {
    return Coach.findByIdAndDelete(coachId);
  }

  async findAll() {
    return await Coach.find().populate({
      path: "center",
      select: "_id displayName owner isFreelanceEducator", // add isFreelanceEducator to selection
      populate: {
        path: "owner",
        select: "isIndividualCreator" // also get owner's individual creator status
      }
    });
  }

  // New method to find all coaches excluding individual educators
  async findAllExcludingIndividualEducators() {
    return await Coach.find().populate({
      path: "center",
      select: "_id displayName owner isFreelanceEducator",
      populate: {
        path: "owner",
        select: "isIndividualCreator"
      }
    }).then(coaches => {
      // Filter out coaches from individual educator centers
      return coaches.filter(coach => {
        if (!coach.center) return true; // Include coaches without centers
        const isFreelanceCenter = coach.center.isFreelanceEducator;
        const isIndividualOwner = coach.center.owner?.isIndividualCreator;
        return !isFreelanceCenter && !isIndividualOwner;
      });
    });
  }

  async getProgramByCoachId(coachId) {
    const coach = await Coach.findById(coachId).populate("programs");
    if (!coach) {
      throw new Error("Coach not found");
    }
    return coach.programs;
  }
  async findByCenterId(centerId) {
    return await Coach.find({ center: centerId }).populate({
      path: "center",
      select: "_id displayName owner", // add 'name' too if you want it
    });
  }
  async findManagerByCenterId(centerId) {
    return await Coach.findOne({ manager: centerId }).populate({
      path: "center",
      select: "_id displayName owner", // add 'name' too if you want it
    });
  }
  // Start a session for transaction management
  async startSession() {
    return await mongoose.startSession();
  }
}

module.exports = CoachRepository;
