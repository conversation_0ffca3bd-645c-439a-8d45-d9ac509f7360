const BaseUser = require("../models/baseUserModel");
const Center = require("../models/centerModel");
const Chat = require("../models/chatModel");
const Coach = require("../models/coachModel");
const User = require("../models/userModel");
const Conversation = require("../models/conversationModel");
const { 
  getCachedMessages, 
  cacheMessages, 
  getCachedConversations,
  cacheConversations,
  invalidateConversationCache,
  invalidateUserConversationsCache 
} = require('../../utils/redisCache');

// Function to generate consistent conversation ID
const generateConversationId = (user1, user2) => {
  return [user1, user2].sort().join("-");
};

class ChatRepository {
  getAllChats(senderId, recipientId, page, limit) {
    try {
      const skip = (page - 1) * limit;

      return Chat.find({
        $or: [
          { sender: senderId, recipient: recipientId },
          { sender: recipientId, recipient: senderId },
        ],
      })
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      console.log(error);
      throw new Error("Something went wrong getting chats");
    }
  }
  async getUserChats(userId, page, limit) {
    console.log(`Fetching user chats for userId: ${userId}, page: ${page}, limit: ${limit}`);

    const skip = (page - 1) * limit;

    try {
      // Fetch unique chat users
      const userChats = await Chat.find({
        $or: [{ sender: userId }, { recipient: userId }],
      })
        .sort({ timestamp: -1 }) // Most recent first
        .skip(skip)
        .limit(limit)
        .exec();

      console.log(`Found ${userChats.length} chat documents for user ${userId}`);
      
      // Return empty array early if no chats found
      if (userChats.length === 0) {
        console.log(`No chats found for user ${userId}`);
        return [];
      }

      // Use a Map to store unique opposite users
      const userMap = new Map();

      for (const chat of userChats) {
        const isSender = chat.sender.toString() === userId;
        const oppositeId = isSender ? chat.recipient : chat.sender;
        const oppositeModel = isSender ? chat.recipientModel : chat.senderModel;

        // Avoid duplicate entries
        if (!userMap.has(oppositeId.toString())) {
          userMap.set(oppositeId.toString(), { chat, oppositeModel });
        }
      }
      
      console.log(`Found ${userMap.size} unique conversation partners for user ${userId}`);
      
      // Batch fetch opposite user details
      const userPromises = [...userMap.entries()].map(
        async ([oppositeId, { chat, oppositeModel }]) => {
          let oppositeUser;
          const projection = "mainImage fullname displayName id";

          switch (oppositeModel) {
            case "user":
              oppositeUser = await User.findById(oppositeId, projection);
              break;
            case "center":
              oppositeUser = await Center.findById(oppositeId, projection);
              break;
            case "coach":
              oppositeUser = await Coach.findById(oppositeId, projection);
              break;
          }

          if (!oppositeUser) {
            console.warn(`Could not find opposite user with ID ${oppositeId} of model ${oppositeModel}`);
            return null;
          }

          return {
            oppositeId,
            oppositeModel,
            name: oppositeUser.displayName ?? oppositeUser.fullname,
            mainImage: oppositeUser.mainImage,
            lastMessage: chat.message,
            lastMessageTime: chat.timestamp,
            status: chat.status || 'sent' // Include message status
          };
        }
      );

      // Wait for all user fetch promises and remove null values
      const results = (await Promise.all(userPromises)).filter(Boolean);
      console.log(`Returning ${results.length} conversation summaries for user ${userId}`);
      return results;
    } catch (error) {
      console.error(`Error fetching user chats for ${userId}:`, error);
      throw new Error(
        "Could not fetch chats at this time. Please try again later."
      );
    }
  }

  async createChat(message, sender, recipient, senderModel, recipientModel, chatOptions = {}) {
    try {
      console.log(`Creating chat: ${sender} -> ${recipient}: ${message} | Options: ${JSON.stringify(chatOptions)}`);
      
      // Determine conversation ID - respect provided conversationId for broadcasts
      let conversationId = chatOptions.conversationId;
      
      // If no conversationId provided, generate one
      if (!conversationId) {
        // For broadcast messages, use special format
        if (chatOptions.isBroadcast) {
          conversationId = `-${sender}`;
        } else {
          // For regular messages, use the standard format
          conversationId = generateConversationId(sender, recipient);
        }
      }
      
      console.log(`Using conversationId: ${conversationId} for message`);
      
      // Create and save chat message with all provided options
      const chat = new Chat({
        message,
        sender,
        recipient,
        senderModel,
        recipientModel,
        status: 'sent',
        conversationId,
        isBroadcast: chatOptions.isBroadcast
      });
      
      const savedChat = await chat.save();
      
      // Update or create conversation record
      await this.updateConversation(
        conversationId,
        sender,
        recipient,
        senderModel,
        recipientModel,
        message,
        savedChat.timestamp,
        chatOptions.isBroadcast
      );
      
      // Invalidate related caches
      await invalidateConversationCache(conversationId);
      await invalidateUserConversationsCache(sender);
      await invalidateUserConversationsCache(recipient);
      
      console.log(`Invalidated caches for conversation ${conversationId} and users ${sender}, ${recipient}`);
      
      return savedChat;
    } catch (error) {
      console.error('Error creating chat:', error);
      throw new Error(`Failed to save message: ${error.message}`);
    }
  }
  
  // Helper method to update conversation
  async updateConversation(conversationId, sender, recipient, senderModel, recipientModel, message, timestamp, isBroadcast = false) {
    try {
      // Find participants with proper types
      const participants = [
        { userId: sender, userType: senderModel },
        { userId: recipient, userType: recipientModel }
      ];
      
      // For broadcast messages, ensure we set the broadcast flag in conversation
      const updateData = {
        $set: {
          participants,
          lastMessage: {
            text: message,
            sender,
            timestamp
          },
          updatedAt: new Date()
        },
        $inc: { messageCount: 1 },
        $setOnInsert: { createdAt: new Date() }
      };
      
      // Add isBroadcast flag if this is a broadcast message
      if (isBroadcast) {
        updateData.$set.isBroadcast = true;
      }
      
      console.log(`Updating conversation ${conversationId} with isBroadcast=${isBroadcast}`);
      
      // Update or create the conversation
      const updatedConversation = await Conversation.findOneAndUpdate(
        { conversationId },
        updateData,
        { upsert: true, new: true }
      );
      
      console.log(`Conversation updated: ${updatedConversation._id}, messageCount: ${updatedConversation.messageCount}`);
    } catch (error) {
      console.error('Error updating conversation:', error);
      // Don't throw here to prevent affecting message sending
    }
  }

  async getChatsBetweenUsers(user1, user2, type1, type2, options = {}) {
    try {
      console.log(`Fetching chats between users: ${user1} (${type1}) and ${user2} (${type2})`);
      
      // Add validation for empty user IDs
      if (!user1 || !user2 || user1 === '' || user2 === '') {
        console.warn(`Invalid user IDs provided: user1=${user1}, user2=${user2}`);
        return { messages: [], nextCursor: null };
      }
      
      // Generate conversation ID
      const conversationId = generateConversationId(user1, user2);
      
      // Set default pagination options
      const limit = options.limit || 30;
      const beforeTimestamp = options.beforeTimestamp ? new Date(options.beforeTimestamp) : null;
      
      // Try to get from cache first if no pagination
      let cachedMessages = null;
      if (!beforeTimestamp) {
        try {
          cachedMessages = await getCachedMessages(conversationId);
          if (cachedMessages) {
            console.log(`Using cached messages for conversation ${conversationId}`);
            // Ensure proper data structure
            if (!cachedMessages.messages) {
              console.warn(`Invalid cache format for conversation ${conversationId}, falling back to database`);
              cachedMessages = null;
            }
          }
        } catch (cacheError) {
          console.error(`Error retrieving from Redis cache: ${cacheError.message}`);
          // Continue execution without cache
        }
      }
      
      if (cachedMessages) {
        return cachedMessages;
      }
      
      // Build query
      let query = {
        $or: [
          { sender: user1, recipient: user2 },
          { sender: user2, recipient: user1 },
        ],
      };
      
      // Add timestamp filter if provided
      if (beforeTimestamp) {
        query.timestamp = { $lt: beforeTimestamp };
      }
      
      // Fetch messages with limit and sort
      const chats = await Chat.find(query)
        .sort({ timestamp: -1 })
        .limit(parseInt(limit))
        .lean();
      
      // Get the oldest message timestamp for next pagination request
      const nextCursor = chats.length > 0 ? 
        chats[chats.length - 1].timestamp.toISOString() : null;
      
      console.log(`Found ${chats.length} chats between the users`);
      
      const result = {
        messages: chats.reverse(), // Return in chronological order
        nextCursor
      };
      
      // Cache the result if this is the first page
      if (!beforeTimestamp) {
        try {
          await cacheMessages(conversationId, result);
          console.log(`Cached messages for conversation ${conversationId}`);
        } catch (cacheError) {
          console.error(`Error caching messages: ${cacheError.message}`);
          // Continue without caching
        }
      }
      
      return result;
    } catch (error) {
      console.error("Error fetching chats between users: ", error);
      throw new Error(`Failed to fetch chats between users: ${error.message}`);
    }
  }

  async getChatsBetweenParentAndCenter(user1, user2, options = {}) {
    try {
      console.log("Fetching chats between parent and center ");
      console.log(`user1 ${user1}`);
      console.log(`user2 ${user2}`);
      
      // Add validation for empty user IDs
      if (!user1 || !user2 || user1 === '' || user2 === '') {
        console.warn(`Invalid user IDs provided: user1=${user1}, user2=${user2}`);
        return { messages: [], nextCursor: null };
      }
      
      // Set default pagination options
      const limit = options.limit || 30;
      const beforeTimestamp = options.beforeTimestamp ? new Date(options.beforeTimestamp) : null;
      
      // Build query
      let query = {
        $or: [
          { sender: user1, recipient: user2 },
          { sender: user2, recipient: user1 },
        ],
      };
      
      // Add timestamp filter if provided
      if (beforeTimestamp) {
        query.timestamp = { $lt: beforeTimestamp };
      }
      
      // Fetch messages with limit and sort
      const chats = await Chat.find(query)
        .sort({ timestamp: -1 })
        .limit(parseInt(limit))
        .lean();
      
      // Get the oldest message timestamp for next pagination request
      const nextCursor = chats.length > 0 ? 
        chats[chats.length - 1].timestamp.toISOString() : null;
      
      console.log(`Found ${chats.length} chats between parent and center`);
      
      return {
        messages: chats.reverse(), // Return in chronological order
        nextCursor
      };
    } catch (error) {
      console.error("Error fetching chats: ", error);
      throw error;
    }
  }

  async getChatsBetweenParentAndCoach(user1, user2, options = {}) {
    try {
      console.log("Fetching chats between parent and coach");
      console.log(`user1 ${user1}`);
      console.log(`user2 ${user2}`);
      
      // Add validation for empty user IDs
      if (!user1 || !user2 || user1 === '' || user2 === '') {
        console.warn(`Invalid user IDs provided: user1=${user1}, user2=${user2}`);
        return { messages: [], nextCursor: null };
      }
      
      // Set default pagination options
      const limit = options.limit || 30;
      const beforeTimestamp = options.beforeTimestamp ? new Date(options.beforeTimestamp) : null;
      
      // Build query
      let query = {
        $or: [
          { sender: user1, recipient: user2 },
          { sender: user2, recipient: user1 },
        ],
      };
      
      // Add timestamp filter if provided
      if (beforeTimestamp) {
        query.timestamp = { $lt: beforeTimestamp };
      }
      
      // Fetch messages with limit and sort
      const chats = await Chat.find(query)
        .sort({ timestamp: -1 })
        .limit(parseInt(limit))
        .lean();
      
      // Get the oldest message timestamp for next pagination request
      const nextCursor = chats.length > 0 ? 
        chats[chats.length - 1].timestamp.toISOString() : null;
      
      console.log(`Found ${chats.length} chats between parent and coach`);
      
      return {
        messages: chats.reverse(), // Return in chronological order
        nextCursor
      };
    } catch (error) {
      console.error("Error fetching chats between parent and coach: ", error);
      throw new Error(`Failed to fetch chats between parent and coach: ${error.message}`);
    }
  }
  
  async getChatsBetweenCenterAndCoach(user1, user2, options = {}) {
    try {
      console.log("Fetching chats between center and coach");
      console.log(`user1 ${user1}`);
      console.log(`user2 ${user2}`);
      
      // Add validation for empty user IDs
      if (!user1 || !user2 || user1 === '' || user2 === '') {
        console.warn(`Invalid user IDs provided: user1=${user1}, user2=${user2}`);
        return { messages: [], nextCursor: null };
      }
      
      // Set default pagination options
      const limit = options.limit || 30;
      const beforeTimestamp = options.beforeTimestamp ? new Date(options.beforeTimestamp) : null;
      
      // Build query
      let query = {
        $or: [
          { sender: user1, recipient: user2 },
          { sender: user2, recipient: user1 },
        ],
      };
      
      // Add timestamp filter if provided
      if (beforeTimestamp) {
        query.timestamp = { $lt: beforeTimestamp };
      }
      
      // Fetch messages with limit and sort
      const chats = await Chat.find(query)
        .sort({ timestamp: -1 })
        .limit(parseInt(limit))
        .lean();
      
      // Get the oldest message timestamp for next pagination request
      const nextCursor = chats.length > 0 ? 
        chats[chats.length - 1].timestamp.toISOString() : null;
      
      console.log(`Found ${chats.length} chats between center and coach`);
      
      return {
        messages: chats.reverse(), // Return in chronological order
        nextCursor
      };
    } catch (error) {
      console.error("Error fetching chats between center and coach: ", error);
      throw new Error(`Failed to fetch chats between center and coach: ${error.message}`);
    }
  }

  // Get conversations for a user
  async getConversationsForUser(userId, limit = 20, skip = 0) {
    try {
      console.log(`Fetching conversations for user: ${userId}, limit: ${limit}, skip: ${skip}`);
      
      // Validate userId to prevent ObjectId casting errors
      if (!userId || userId === '') {
        console.warn('Empty userId provided to getConversationsForUser');
        return { conversations: [], total: 0 };
      }
      
      // Convert parameters to integers with fallbacks
      const parsedLimit = parseInt(limit) || 20;
      const parsedSkip = parseInt(skip) || 0;
      
      // Generate a cache key that includes pagination parameters
      const cacheKey = `conversations:${userId}:${parsedLimit}:${parsedSkip}`;
      
      // Try to get from cache first (for any pagination now, not just first page)
      let cachedConversations = null;
      try {
        cachedConversations = await getCachedConversations(userId);
        if (cachedConversations && 
            Array.isArray(cachedConversations.conversations)) {
          
          console.log(`Using cached conversations for user ${userId}`);
          
          // If we have enough cached conversations to satisfy this request
          if (parsedSkip + parsedLimit <= cachedConversations.conversations.length) {
            // Extract the requested slice from the cached data
            const slicedConversations = cachedConversations.conversations.slice(
              parsedSkip, 
              parsedSkip + parsedLimit
            );
            
            console.log(`Returning ${slicedConversations.length} conversations from cache for user ${userId}`);
            
            return {
              conversations: slicedConversations,
              total: cachedConversations.total || cachedConversations.conversations.length
            };
          }
        }
      } catch (cacheError) {
        console.error(`Error retrieving conversations from cache: ${cacheError.message}`);
        // Continue without using cache
      }
      
      // Cache miss or not enough cached data - query the database
      console.log(`Cache miss or insufficient data for user ${userId}, querying database`);
      
      // Use a more efficient query with proper projection
      // Only fetch the fields we actually need
      const conversations = await Conversation.find(
        { 'participants.userId': userId },
        {
          conversationId: 1,
          participants: 1,
          lastMessage: 1,
          updatedAt: 1,
          messageCount: 1,
          isBroadcast: 1 // Include isBroadcast flag for filtering
        }
      )
        .sort({ updatedAt: -1 })
        .skip(parsedSkip)
        .limit(parsedLimit)
        .lean();
      
      console.log(`Found ${conversations.length} conversations for user ${userId}`);
      
      // Get all unique participant IDs to batch fetch user details
      const participantIds = new Set();
      const participantTypes = new Map();
      
      // Extract all unique participant IDs that are not the current user
      conversations.forEach(conv => {
        conv.participants.forEach(p => {
          if (p.userId && p.userId.toString() !== userId) {
            participantIds.add(p.userId.toString());
            participantTypes.set(p.userId.toString(), p.userType);
          }
        });
      });
      
      // Batch fetch all user details in one query per user type
      const userDetailsMap = new Map();
      
      // Group IDs by user type
      const userIdsByType = {
        user: [],
        center: [],
        coach: []
      };
      
      participantIds.forEach(id => {
        const type = participantTypes.get(id);
        if (type && userIdsByType[type]) {
          userIdsByType[type].push(id);
        }
      });
      
      // Batch fetch users by type
      const projection = "mainImage fullname displayName _id";
      
      // Fetch all user types in parallel
      await Promise.all([
        // Fetch users
        userIdsByType.user.length > 0 ? 
          User.find({ _id: { $in: userIdsByType.user } }, projection).lean()
            .then(users => users.forEach(u => userDetailsMap.set(u._id.toString(), u))) : 
          Promise.resolve(),
        
        // Fetch centers
        userIdsByType.center.length > 0 ? 
          Center.find({ _id: { $in: userIdsByType.center } }, projection).lean()
            .then(centers => centers.forEach(c => userDetailsMap.set(c._id.toString(), c))) : 
          Promise.resolve(),
        
        // Fetch coaches
        userIdsByType.coach.length > 0 ? 
          Coach.find({ _id: { $in: userIdsByType.coach } }, projection).lean()
            .then(coaches => coaches.forEach(c => userDetailsMap.set(c._id.toString(), c))) : 
          Promise.resolve()
      ]);
      
      // Also fetch the current user's details for broadcast conversations
      let currentUserDetails = null;
      try {
        currentUserDetails = await User.findById(userId, projection).lean();
      } catch (e) {
        console.warn(`Could not fetch current user details for broadcast conversations: ${e.message}`);
      }
      
      // Format conversations using the batch-fetched user details
      const formattedConversations = conversations.map(conversation => {
        try {
          // Special handling for broadcast conversations (isBroadcast flag or conversationId starts with -)
          const isBroadcast = conversation.isBroadcast === true || 
                             (conversation.conversationId && conversation.conversationId.startsWith('-'));
                             
          if (isBroadcast) {
            console.log(`Processing broadcast conversation: ${conversation.conversationId}`);
            
            // Use current user's details for the "recipient" of broadcast
            const name = currentUserDetails ? 
                        (currentUserDetails.displayName ?? currentUserDetails.fullname ?? 'Broadcast Messages') : 
                        'Broadcast Messages';
            
            return {
              conversationId: conversation.conversationId,
              recipientId: '', // Empty recipient ID for broadcasts
              recipientType: 'user',
              name: name + ' (Broadcast)',
              mainImage: currentUserDetails?.mainImage || '',
              lastMessage: conversation.lastMessage?.text || '',
              lastMessageSender: conversation.lastMessage?.sender || '',
              timestamp: conversation.lastMessage?.timestamp || conversation.updatedAt,
              messageCount: conversation.messageCount || 0,
              isBroadcast: true
            };
          }
          
          // Regular conversation processing
          // Find the other participant (not the current user)
          const otherParticipant = conversation.participants.find(
            p => p.userId && p.userId.toString() !== userId
          );
          
          if (!otherParticipant) {
            console.warn(`No other participant found in conversation ${conversation.conversationId}`);
            return null;
          }
          
          // Get user details from our pre-fetched map
          const oppositeUserId = otherParticipant.userId.toString();
          const oppositeUser = userDetailsMap.get(oppositeUserId);
          
          if (!oppositeUser) {
            console.warn(`Could not find opposite user with ID ${oppositeUserId} of model ${otherParticipant.userType}`);
            return null;
          }
          
          return {
            conversationId: conversation.conversationId,
            recipientId: otherParticipant.userId,
            recipientType: otherParticipant.userType,
            name: oppositeUser.displayName ?? oppositeUser.fullname,
            mainImage: oppositeUser.mainImage,
            lastMessage: conversation.lastMessage?.text || '',
            lastMessageSender: conversation.lastMessage?.sender || '',
            timestamp: conversation.lastMessage?.timestamp || conversation.updatedAt,
            messageCount: conversation.messageCount || 0,
            isBroadcast: false
          };
        } catch (err) {
          console.error(`Error processing conversation ${conversation.conversationId}:`, err);
          return null;
        }
      }).filter(Boolean); // Filter out null values
      
      console.log(`Returning ${formattedConversations.length} conversations for user ${userId}`);
      
      // Get total count (use countDocuments for better performance)
      // Use a cached value if we already have it
      let total;
      if (cachedConversations && typeof cachedConversations.total === 'number') {
        total = cachedConversations.total;
      } else {
        // Only query for count if we don't have it cached
        total = await Conversation.countDocuments({'participants.userId': userId});
      }
      
      const result = {
        conversations: formattedConversations,
        total
      };
      
      // Cache the result - store more data than requested for future pagination
      // But only if we're on the first page
      if (parsedSkip === 0) {
        try {
          // If we have fewer results than the limit, we have all conversations
          // Otherwise, fetch more to cache
          if (formattedConversations.length < parsedLimit) {
            await cacheConversations(userId, result);
          } else {
            // Fetch more conversations for caching (up to 100)
            const additionalConversations = await this.getConversationsForUser(userId, 100, 0);
            await cacheConversations(userId, additionalConversations);
          }
          console.log(`Cached conversations for user ${userId}`);
        } catch (cacheError) {
          console.error(`Error caching conversations: ${cacheError.message}`);
          // Continue without caching
        }
      }
      
      return result;
    } catch (error) {
      console.error(`Error fetching conversations for user ${userId}:`, error);
      // Return empty result instead of throwing to prevent client crashes
      return { conversations: [], total: 0 };
    }
  }

  // Enhanced method to find chats by any query
  async findChatsByQuery(query, limit = 30) {
    try {
      console.log(`Finding chats with query: ${JSON.stringify(query)}, limit: ${limit}`);
      
      // Ensure we have a valid query
      if (!query || (typeof query === 'object' && Object.keys(query).length === 0)) {
        console.warn('Empty query provided to findChatsByQuery');
        return [];
      }
      
      // Handle ObjectId conversion for user IDs if needed
      if (query.sender && typeof query.sender === 'string') {
        try {
          const mongoose = require('mongoose');
          query.sender = mongoose.Types.ObjectId(query.sender);
        } catch (e) {
          console.warn(`Invalid ObjectId for sender: ${query.sender}`);
          // Continue with string value
        }
      }
      
      if (query.recipient && typeof query.recipient === 'string') {
        try {
          const mongoose = require('mongoose');
          query.recipient = mongoose.Types.ObjectId(query.recipient);
        } catch (e) {
          console.warn(`Invalid ObjectId for recipient: ${query.recipient}`);
          // Continue with string value
        }
      }
      
      // If we have $or conditions that involve sender/recipient, process them too
      if (query.$or && Array.isArray(query.$or)) {
        query.$or = query.$or.map(condition => {
          // Process sender in condition
          if (condition.sender && typeof condition.sender === 'string') {
            try {
              const mongoose = require('mongoose');
              condition.sender = mongoose.Types.ObjectId(condition.sender);
            } catch (e) {
              // Continue with string value
            }
          }
          
          // Process recipient in condition
          if (condition.recipient && typeof condition.recipient === 'string') {
            try {
              const mongoose = require('mongoose');
              condition.recipient = mongoose.Types.ObjectId(condition.recipient);
            } catch (e) {
              // Continue with string value
            }
          }
          
          // Handle nested $or if present
          if (condition.$or && Array.isArray(condition.$or)) {
            condition.$or = condition.$or.map(subcondition => {
              if (subcondition.sender && typeof subcondition.sender === 'string') {
                try {
                  const mongoose = require('mongoose');
                  subcondition.sender = mongoose.Types.ObjectId(subcondition.sender);
                } catch (e) {
                  // Continue with string value
                }
              }
              
              if (subcondition.recipient && typeof subcondition.recipient === 'string') {
                try {
                  const mongoose = require('mongoose');
                  subcondition.recipient = mongoose.Types.ObjectId(subcondition.recipient);
                } catch (e) {
                  // Continue with string value
                }
              }
              return subcondition;
            });
          }
          
          return condition;
        });
      }
      
      // Fetch messages with limit and sort
      const chats = await Chat.find(query)
        .sort({ timestamp: -1 })
        .limit(parseInt(limit))
        .lean();
      
      console.log(`Found ${chats.length} chats matching query`);
      return chats;
    } catch (error) {
      console.error("Error finding chats by query: ", error);
      // Return empty array instead of throwing to prevent client crashes
      return [];
    }
  }

  // Get user details by user ID
  async getUserDetails(userId) {
    try {
      if (!userId) {
        console.warn('Empty userId provided to getUserDetails');
        return null;
      }
      
      console.log(`Fetching user details for ${userId}`);
      
      // Try to find in User first
      const userDetails = await User.findById(userId, "mainImage fullname displayName _id").lean();
      
      if (userDetails) {
        console.log(`Found user details in User collection`);
        return userDetails;
      }
      
      // If not found, try Center and Coach
      const centerDetails = await Center.findById(userId, "mainImage fullname displayName _id").lean();
      if (centerDetails) {
        console.log(`Found user details in Center collection`);
        return centerDetails;
      }
      
      const coachDetails = await Coach.findById(userId, "mainImage fullname displayName _id").lean();
      if (coachDetails) {
        console.log(`Found user details in Coach collection`);
        return coachDetails;
      }
      
      console.warn(`Could not find details for user ${userId} in any collection`);
      return null;
    } catch (error) {
      console.error(`Error fetching user details: ${error.message}`);
      return null;
    }
  }
}

module.exports = ChatRepository;
