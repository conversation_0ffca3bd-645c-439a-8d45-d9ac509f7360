// src/repo/attendanceRepo.js
const Attendance = require("../models/attendenceModel");
const mongoose = require("mongoose");
class AttendanceRepo {
  async getExistingQRCode(classId, studentId, classDate) {
    const attendance = await Attendance.findOne({
      classId,
      studentId,
      classDate,
    });
    console.log("Attendance Record:", attendance);
    return attendance ? attendance.qrCodeData : null;
  }
  async saveCode(classId, studentId, classDate, code, qrCodeData) {
    const attendance = new Attendance({
      classId,
      studentId,
      classDate,
      code,
      qrCodeData,
      attendanceStatus: "absent",
    });
    await attendance.save();
  }

  async updateQRCode(classId, studentId, classDate, qrCodeData) {
    const attendance = await Attendance.findOne({
      classId,
      studentId,
      classDate,
    });
    if (attendance) {
      attendance.qrCodeData = qrCodeData;
      await attendance.save();
    }
  }

  async updateAttendance(classId, studentId, classDate, status) {
    const attendance = await Attendance.findOne({
      classId,
      studentId,
      classDate
    }).populate('studentId');
    console.log(`attendance :${attendance}`);
    if (attendance) {
      attendance.attendanceStatus = status;
      await attendance.save();

      // Extract student details
      const student = attendance.studentId;
      return {
        attendanceStatus: attendance.attendanceStatus,
        studentImage: student.mainImage.url,
        studentName: student.fullname,
        id: student.id,
      };
    }
    return false;
  }

  async getClassAttendance(classId) {
    return await Attendance.find({ classId }).populate("studentId");
  }
  async getPresentAttendance(classId, classDate) {
    try {
      const attendances = await Attendance.find({
        classId,
        classDate,
        attendanceStatus: "present",
      })
        .select("attendanceStatus studentId")
        .populate("studentId", "fullname mainImage.url");

      // Map the results to the desired structure
      const formattedAttendances = attendances.map((attendance) => {
        const student = attendance.studentId;
        return {
          isVerified: {
            attendanceStatus: attendance.attendanceStatus,
            studentImage: student.mainImage.url,
            studentName: student.fullname,
            id: student._id.toString(), // Convert ObjectId to string
          },
        };
      });

      return formattedAttendances; // Return the formatted list of attendances
    } catch (err) {
      console.error("Error in getPresentAttendance method:", err);
      return []; // Return an empty array on error
    }
  }

  async getAttendanceRecord(classId, studentId, classDate) {
    return await Attendance.findOne({ classId, studentId, classDate });
  }
  async getAttendanceRecordByClassId(classId, code, classDate) {
    return await Attendance.findOne({
      classId: classId,
      code: code,
      classDate: classDate,
    });
  }
  async getAttendanceByClassId(classId) {
    try {
      const attendanceRecords = await Attendance.find({
        attendanceStatus: "present",
        classId: classId, // Filter directly by classId
      })
        .select("_id") // Only select _id from Attendance
        .populate({
          path: "classId",
          select: "student classProviding sen center coach", // Required fields from class
          populate: [
            {
              path: "center",
              select: "address", // Include necessary center fields
            },
            {
              path: "coach",
              select: "displayName", // Include necessary coach fields
            },
            {
              path: "student",
              select: "mainImage fullname", // Student info
            },
          ],
        });

      // Filter out records where classId is not populated
      const filteredRecords = attendanceRecords.filter(
        (record) => record.classId && record.classId !== null
      );

      return filteredRecords;
    } catch (error) {
      throw new Error(
        "Error fetching attendance records by classId: " + error.message
      );
    }
  }
  async getAttendanceByCoachId(coachId) {
    try {
      const attendanceRecords = await Attendance.find({
        attendanceStatus: "present", // Filter attendance records with "present" status
      })
        .select("_id") // Only select _id from Attendance
        .populate({
          path: "classId",
          match: { coach: coachId }, // Match classes with the given coachId
          select: "student classProviding sen center coach", // Include center and student details
          populate: [
            {
              path: "center",
              select: "address", // Add center fields you need here
            },
            {
              path: "coach",
              select: "displayName", // Add coach fields you need here
            },
            {
              path: "student",
              select: "mainImage fullname", // Include student details
            },
          ],
        });

      // Filter out records where classId is null or undefined
      const filteredRecords = attendanceRecords.filter(
        (record) => record.classId && record.classId !== null
      );

      return filteredRecords;
    } catch (error) {
      throw new Error(
        "Error fetching attendance records by coach: " + error.message
      );
    }
  }

  async getAttendanceByCenterId(centerId) {
    try {
      const attendanceRecords = await Attendance.find({
        attendanceStatus: "present",
      })
        .select("_id") // Only select _id from Attendance
        .populate({
          path: "classId",
          match: { center: centerId },
          select: "student classProviding sen center coach", // Include center and coach fields
          populate: [
            {
              path: "center",
              select: "address", // Add any center fields you need here
            },
            {
              path: "coach",
              select: "displayName", // Add coach fields you need here
            },
            {
              path: "student",
              select: "mainImage fullname",
            },
          ],
        });

      // Filter out records where classId is null or undefined
      const filteredRecords = attendanceRecords.filter(
        (record) => record.classId && record.classId !== null
      );

      return filteredRecords;
    } catch (error) {
      throw new Error(
        "Error fetching attendance records by center: " + error.message
      );
    }
  }

  async getHistoryBychildId(childId) {
    try {
      console.log(childId);
      const attendance = await Attendance.find(
        {
          attendanceStatus: "present",
          studentId: new mongoose.Types.ObjectId(childId),
        },
        { studentId: 1, createdAt: 1, _id: 0 }
      ).populate({
        path: "classId",
        select: "classProviding mainImage _id durationMinutes", // Added durationMinutes
        populate: [
          {
            path: "center",
            select: "displayName _id",
          },
          {
            path: "coach",
            select: "displayName _id",
          },
        ],
      });

      return attendance;
    } catch (error) {
      throw new Error("Error fetching attendacne for child: " + error.message);
    }
  }

  async getUniqueClassIdsByStudent(studentId) {
    try {
      const attendances = await Attendance.find({
        studentId: new mongoose.Types.ObjectId(studentId),
        // attendanceStatus: "present", // Optionally filter by present status if only present sessions are reviewable
      }).distinct("classId");
      return attendances.map(id => id.toString()); // Ensure IDs are strings
    } catch (error) {
      console.error("Error in AttendanceRepo getUniqueClassIdsByStudent:", error);
      throw new Error("Error fetching unique class IDs by student: " + error.message);
    }
  }

  async getAttendancesByStudentIds(studentIds) {
    try {
      // Ensure studentIds are ObjectIds if they are not already
      const objectIdStudentIds = studentIds.map(id => new mongoose.Types.ObjectId(id));
      const attendances = await Attendance.find({
        studentId: { $in: objectIdStudentIds },
        // Optionally filter by attendanceStatus if needed, e.g., only 'present'
        // attendanceStatus: "present", 
      })
      .select('studentId classId date') // Select fields needed by ReviewUseCase. Add 'date' if your attendance model stores event date
      .lean(); // Use .lean() for performance, returns plain JS objects
      return attendances;
    } catch (error) {
      console.error("Error in AttendanceRepo getAttendancesByStudentIds:", error);
      throw new Error("Error fetching attendances by student IDs: " + error.message);
    }
  }
}

module.exports = AttendanceRepo;
