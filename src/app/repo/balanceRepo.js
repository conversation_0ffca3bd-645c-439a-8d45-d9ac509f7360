const BalanceModel = require("../models/coinBalanceModel");
const { createError } = require("../../utils/createErrorUtil");
class BalanceRepo {
  async createBalance(data) {
    try {
      console.log("Creating new balance with data:");
      console.log(data);
      const balance = new BalanceModel(data);
      await balance.save();
      return balance;
    } catch (error) {
      throw new Error({ "Failed to create balance": error.message });
    }
  }
  async getWalletByUserId(userId) {
    return await BalanceModel.findOne({ userId });
  }
  async getBalance(userId) {
    try {
      const balance = await BalanceModel.findOne({ userId });
      if (!balance) {
        throw createError(
          "Failed to load balance",
          "No wallet found for the user"
        );
      }
      return balance;
    } catch (error) {
      throw createError("Failed to fetch balance", error.message);
    }
  }

  async update(userId, data) {
    try {
      return await BalanceModel.findOneAndUpdate({ userId }, data, {
        new: true,
      });
    } catch (error) {
      throw new Error(`Failed to update balance: ${error.message}`);
    }
  }
  
  async addBalance(userId, amount) {
    console.log(`BalanceRepo: Adding ${amount} to balance for user ${userId}`);
    
    try {
      let wallet = await BalanceModel.findOne({ userId });
      
      // If wallet doesn't exist, create it
      if (!wallet) {
        console.log(`No wallet found for user ${userId}, creating new wallet`);
        wallet = new BalanceModel({
          userId: userId,
          balance: 0
        });
      }
      
      const previousBalance = wallet.balance;
      wallet.balance += amount;
      const newBalance = wallet.balance;
      
      console.log(`Balance update: ${previousBalance} + ${amount} = ${newBalance}`);
      
      await wallet.save();
      console.log(`Wallet saved successfully. New balance: ${wallet.balance}`);
      
      return wallet;
    } catch (error) {
      console.error(`Error in BalanceRepo.addBalance: ${error.message}`);
      throw new Error(`Failed to update balance: ${error.message}`);
    }
  }
  
  async deductBalance(userId, amount) {
    const wallet = await BalanceModel.findOne({ userId });
    if (wallet) {
      if (wallet.balance < amount) {
        throw new Error("Insufficient balance");
      }
      wallet.balance -= amount;
      await wallet.save();
      return wallet;
    }
    throw new Error("Wallet not found");
  }
}

module.exports = BalanceRepo;
