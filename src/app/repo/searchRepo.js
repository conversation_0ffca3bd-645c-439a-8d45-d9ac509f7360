const Class = require("../models/classModel");
const Center = require("../models/centerModel");
const Coach = require("../models/coachModel");
const FilterUtil = require("../../utils/filterUtil");

class SearchRepository {
  // Search classes with filters, sorting, and pagination
  async searchClasses(query, filters, sortBy, pagination) {
    try {
      // Start with text search filter if query is provided
      let filter = query ? { $text: { $search: query } } : {};

      // Add additional filters
      const additionalFilters = FilterUtil.buildFilters(filters);
      filter = { ...filter, ...additionalFilters };

      // Debug output
      console.log(
        "Searching classes with filters:",
        JSON.stringify(filter, null, 2)
      );

      // Build the query
      let classQuery = Class.find(filter);

      // Apply sorting
      if (sortBy) {
        classQuery = FilterUtil.applySorting(classQuery, sortBy);
      }

      // Apply pagination
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 50;
      const skip = (page - 1) * limit;

      // Get total count for pagination
      const totalCount = await Class.countDocuments(filter);

      // Execute the query with pagination
      const results = await classQuery
        .skip(skip)
        .limit(limit)
        .populate("center");

      // Add pricing calculation for each center (like centerUseCase.getAllCenters())
      const resultsWithPricing = await Promise.all(results.map(async (classItem) => {
        try {
          if (classItem.center && classItem.center._id) {
            // Get all classes for this center to calculate pricing
            const centerClasses = await Class.find({ center: classItem.center._id });
            
            let priceFrom = null;
            let priceTo = null;

            if (centerClasses && centerClasses.length > 0) {
              // Filter out SEN classes (they should be free) - consistent with centerUseCase
              const nonSenClasses = centerClasses.filter(c => !c.sen);
              const prices = nonSenClasses.map((p) => p.charge || (p.classDate && p.classDate.charge)).filter((p) => p != null && p > 0);
              
              if (prices.length > 0) {
                priceFrom = Math.min(...prices);
                priceTo = Math.max(...prices);
              }
            }

            // Add pricing to the center object
            const centerObject = classItem.center.toObject ? classItem.center.toObject() : classItem.center;
            classItem.center = {
              ...centerObject,
              priceFrom,
              priceTo,
            };
          }
          return classItem;
        } catch (pricingError) {
          // If there's an error calculating pricing, just return the class as-is
          console.warn(`Error calculating pricing for center ${classItem.center?._id}:`, pricingError.message);
          return classItem;
        }
      }));

      // Apply price range filtering on calculated pricing (after DB query)
      let filteredResults = resultsWithPricing;
      if (filters.priceMin !== undefined && filters.priceMax !== undefined && 
          !isNaN(filters.priceMin) && !isNaN(filters.priceMax)) {
        console.log(`Filtering results by price range: ${filters.priceMin} - ${filters.priceMax}`);
        
        filteredResults = resultsWithPricing.filter(classItem => {
          if (!classItem.center || !classItem.center.priceFrom || !classItem.center.priceTo) {
            return false; // Exclude centers without pricing
          }
          
          const centerPriceFrom = classItem.center.priceFrom;
          const centerPriceTo = classItem.center.priceTo;
          
          // Center matches if its price range overlaps with the filter range
          const rangeOverlaps = (
            centerPriceFrom <= filters.priceMax && 
            centerPriceTo >= filters.priceMin
          );
          
          return rangeOverlaps;
        });
        
        console.log(`Filtered from ${resultsWithPricing.length} to ${filteredResults.length} results`);
      }

      // Apply sorting on calculated pricing (post-query sorting)
      if (sortBy === 'price') {
        filteredResults.sort((a, b) => {
          const priceA = a.center?.priceFrom || 0;
          const priceB = b.center?.priceFrom || 0;
          return priceA - priceB; // Ascending order
        });
        console.log('Applied price sorting based on calculated priceFrom');
      }

      return {
        data: filteredResults,
        totalCount: filteredResults.length, // Use filtered count for accurate pagination
      };
    } catch (error) {
      console.error("Error searching classes:", error);
      throw error;
    }
  }

  // Search centers with filters, sorting, and pagination
  async searchCenters(query, filters, sortBy, pagination) {
    try {
      // Start with text search filter if query is provided
      let filter = query ? { $text: { $search: query } } : {};

      // Add additional filters
      const additionalFilters = FilterUtil.buildFilters(filters);
      filter = { ...filter, ...additionalFilters };

      // Debug output
      console.log(
        "Searching centers with filters:",
        JSON.stringify(filter, null, 2)
      );

      // Build the query
      let centerQuery = Center.find(filter);

      // Apply sorting
      if (sortBy) {
        centerQuery = FilterUtil.applySorting(centerQuery, sortBy);
      }

      // Apply pagination
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 50;
      const skip = (page - 1) * limit;

      // Get total count for pagination
      const totalCount = await Center.countDocuments(filter);

      // Execute the query with pagination
      const results = await centerQuery.skip(skip).limit(limit);

      // Calculate pricing for each center (like centerUseCase.getAllCenters())
      const centersWithPricing = await Promise.all(results.map(async (center) => {
        try {
          const centerClasses = await Class.find({ center: center._id });
          
          let priceFrom = null;
          let priceTo = null;

          if (centerClasses && centerClasses.length > 0) {
            // Filter out SEN classes (they should be free) - consistent with centerUseCase
            const nonSenClasses = centerClasses.filter(c => !c.sen);
            const prices = nonSenClasses.map((p) => p.charge || (p.classDate && p.classDate.charge)).filter((p) => p != null && p > 0);
            
            if (prices.length > 0) {
              priceFrom = Math.min(...prices);
              priceTo = Math.max(...prices);
            }
          }

          const centerObject = center.toObject ? center.toObject() : center;
          return {
            ...centerObject,
            priceFrom,
            priceTo,
          };
        } catch (classError) {
          // If there's an error fetching classes, just return center without pricing
          console.warn(`Error fetching classes for center ${center._id}:`, classError.message);
          const centerObject = center.toObject ? center.toObject() : center;
          return {
            ...centerObject,
            priceFrom: null,
            priceTo: null,
          };
        }
      }));

      // Apply price range filtering on calculated pricing (after DB query)
      let filteredResults = centersWithPricing;
      if (filters.priceMin !== undefined && filters.priceMax !== undefined && 
          !isNaN(filters.priceMin) && !isNaN(filters.priceMax)) {
        console.log(`Filtering centers by price range: ${filters.priceMin} - ${filters.priceMax}`);
        
        filteredResults = centersWithPricing.filter(center => {
          if (!center.priceFrom || !center.priceTo) {
            return false; // Exclude centers without pricing
          }
          
          // Center matches if its price range overlaps with the filter range
          const rangeOverlaps = (
            center.priceFrom <= filters.priceMax && 
            center.priceTo >= filters.priceMin
          );
          
          return rangeOverlaps;
        });
        
        console.log(`Filtered from ${centersWithPricing.length} to ${filteredResults.length} centers`);
      }

      // Apply sorting on calculated pricing (post-query sorting)
      if (sortBy === 'price') {
        filteredResults.sort((a, b) => {
          const priceA = a.priceFrom || 0;
          const priceB = b.priceFrom || 0;
          return priceA - priceB; // Ascending order
        });
        console.log('Applied price sorting based on calculated priceFrom');
      }

      return {
        data: filteredResults,
        totalCount: filteredResults.length, // Use filtered count for accurate pagination
      };
    } catch (error) {
      console.error("Error searching centers:", error);
      throw error;
    }
  }

  // Search coaches with filters, sorting, and pagination
  async searchCoaches(query, filters, sortBy, pagination) {
    try {
      // Start with text search filter if query is provided
      let filter = query ? { $text: { $search: query } } : {};

      // Add additional filters
      const additionalFilters = FilterUtil.buildFilters(filters);
      filter = { ...filter, ...additionalFilters };

      // Debug output
      console.log(
        "Searching coaches with filters:",
        JSON.stringify(filter, null, 2)
      );

      // Build the query with population to check for individual educators
      let coachQuery = Coach.find(filter).populate({
        path: "center",
        select: "isFreelanceEducator owner",
        populate: {
          path: "owner",
          select: "isIndividualCreator"
        }
      });

      // Apply sorting
      if (sortBy) {
        coachQuery = FilterUtil.applySorting(coachQuery, sortBy);
      }

      // Apply pagination
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 50;
      const skip = (page - 1) * limit;

      // Execute the query first
      const allResults = await coachQuery;

      // Filter out individual educators
      const filteredResults = allResults.filter(coach => {
        if (!coach.center) return true; // Include coaches without centers
        const isFreelanceCenter = coach.center.isFreelanceEducator;
        const isIndividualOwner = coach.center.owner?.isIndividualCreator;
        return !isFreelanceCenter && !isIndividualOwner;
      });

      // Apply pagination to filtered results
      const totalCount = filteredResults.length;
      const results = filteredResults.slice(skip, skip + limit);

      return {
        data: results,
        totalCount: totalCount,
      };
    } catch (error) {
      console.error("Error searching coaches:", error);
      throw error;
    }
  }

  // Filter centers without search query
  async filterCenters(filters, sortBy, pagination) {
    try {
      // Build filters
      const filter = FilterUtil.buildFilters(filters);

      // Debug output
      console.log(
        "Filtering centers with filters:",
        JSON.stringify(filter, null, 2)
      );

      // Build the query
      let centerQuery = Center.find(filter);

      // Apply sorting
      if (sortBy) {
        centerQuery = FilterUtil.applySorting(centerQuery, sortBy);
      }

      // Apply pagination
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 50;
      const skip = (page - 1) * limit;

      // Get total count for pagination
      const totalCount = await Center.countDocuments(filter);

      // Execute the query with pagination
      const results = await centerQuery.skip(skip).limit(limit);

      // Calculate pricing for each center (like centerUseCase.getAllCenters())
      const centersWithPricing = await Promise.all(results.map(async (center) => {
        try {
          const centerClasses = await Class.find({ center: center._id });
          
          let priceFrom = null;
          let priceTo = null;

          if (centerClasses && centerClasses.length > 0) {
            // Filter out SEN classes (they should be free) - consistent with centerUseCase
            const nonSenClasses = centerClasses.filter(c => !c.sen);
            const prices = nonSenClasses.map((p) => p.charge || (p.classDate && p.classDate.charge)).filter((p) => p != null && p > 0);
            
            if (prices.length > 0) {
              priceFrom = Math.min(...prices);
              priceTo = Math.max(...prices);
            }
          }

          const centerObject = center.toObject ? center.toObject() : center;
          return {
            ...centerObject,
            priceFrom,
            priceTo,
          };
        } catch (classError) {
          // If there's an error fetching classes, just return center without pricing
          console.warn(`Error fetching classes for center ${center._id}:`, classError.message);
          const centerObject = center.toObject ? center.toObject() : center;
          return {
            ...centerObject,
            priceFrom: null,
            priceTo: null,
          };
        }
      }));

      // Apply price range filtering on calculated pricing (after DB query)
      let filteredResults = centersWithPricing;
      if (filters.priceMin !== undefined && filters.priceMax !== undefined && 
          !isNaN(filters.priceMin) && !isNaN(filters.priceMax)) {
        console.log(`Filtering centers by price range: ${filters.priceMin} - ${filters.priceMax}`);
        
        filteredResults = centersWithPricing.filter(center => {
          if (!center.priceFrom || !center.priceTo) {
            return false; // Exclude centers without pricing
          }
          
          // Center matches if its price range overlaps with the filter range
          const rangeOverlaps = (
            center.priceFrom <= filters.priceMax && 
            center.priceTo >= filters.priceMin
          );
          
          return rangeOverlaps;
        });
        
        console.log(`Filtered from ${centersWithPricing.length} to ${filteredResults.length} centers`);
      }

      // Apply sorting on calculated pricing (post-query sorting)
      if (sortBy === 'price') {
        filteredResults.sort((a, b) => {
          const priceA = a.priceFrom || 0;
          const priceB = b.priceFrom || 0;
          return priceA - priceB; // Ascending order
        });
        console.log('Applied price sorting based on calculated priceFrom');
      }

      return {
        data: filteredResults,
        totalCount: filteredResults.length, // Use filtered count for accurate pagination
      };
    } catch (error) {
      console.error("Error filtering centers:", error);
      throw error;
    }
  }

  // Filter classes without search query
  async filterClasses(filters, sortBy, pagination) {
    try {
      // Build filters
      const filter = FilterUtil.buildFilters(filters);

      // Debug output
      console.log(
        "Filtering classes with filters:",
        JSON.stringify(filter, null, 2)
      );

      // Build the query
      let classQuery = Class.find(filter);

      // Apply sorting
      if (sortBy) {
        classQuery = FilterUtil.applySorting(classQuery, sortBy);
      }

      // Apply pagination
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 50;
      const skip = (page - 1) * limit;

      // Get total count for pagination
      const totalCount = await Class.countDocuments(filter);

      // Execute the query with pagination
      const results = await classQuery
        .skip(skip)
        .limit(limit)
        .populate("center");

      // Add pricing calculation for each center (like centerUseCase.getAllCenters())
      const resultsWithPricing = await Promise.all(results.map(async (classItem) => {
        try {
          if (classItem.center && classItem.center._id) {
            // Get all classes for this center to calculate pricing
            const centerClasses = await Class.find({ center: classItem.center._id });
            
            let priceFrom = null;
            let priceTo = null;

            if (centerClasses && centerClasses.length > 0) {
              // Filter out SEN classes (they should be free) - consistent with centerUseCase
              const nonSenClasses = centerClasses.filter(c => !c.sen);
              const prices = nonSenClasses.map((p) => p.charge || (p.classDate && p.classDate.charge)).filter((p) => p != null && p > 0);
              
              if (prices.length > 0) {
                priceFrom = Math.min(...prices);
                priceTo = Math.max(...prices);
              }
            }

            // Add pricing to the center object
            const centerObject = classItem.center.toObject ? classItem.center.toObject() : classItem.center;
            classItem.center = {
              ...centerObject,
              priceFrom,
              priceTo,
            };
          }
          return classItem;
        } catch (pricingError) {
          // If there's an error calculating pricing, just return the class as-is
          console.warn(`Error calculating pricing for center ${classItem.center?._id}:`, pricingError.message);
          return classItem;
        }
      }));

      // Apply price range filtering on calculated pricing (after DB query)
      let filteredResults = resultsWithPricing;
      if (filters.priceMin !== undefined && filters.priceMax !== undefined && 
          !isNaN(filters.priceMin) && !isNaN(filters.priceMax)) {
        console.log(`Filtering results by price range: ${filters.priceMin} - ${filters.priceMax}`);
        
        filteredResults = resultsWithPricing.filter(classItem => {
          if (!classItem.center || !classItem.center.priceFrom || !classItem.center.priceTo) {
            return false; // Exclude centers without pricing
          }
          
          const centerPriceFrom = classItem.center.priceFrom;
          const centerPriceTo = classItem.center.priceTo;
          
          // Center matches if its price range overlaps with the filter range
          const rangeOverlaps = (
            centerPriceFrom <= filters.priceMax && 
            centerPriceTo >= filters.priceMin
          );
          
          return rangeOverlaps;
        });
        
        console.log(`Filtered from ${resultsWithPricing.length} to ${filteredResults.length} results`);
      }

      // Apply sorting on calculated pricing (post-query sorting)
      if (sortBy === 'price') {
        filteredResults.sort((a, b) => {
          const priceA = a.center?.priceFrom || 0;
          const priceB = b.center?.priceFrom || 0;
          return priceA - priceB; // Ascending order
        });
        console.log('Applied price sorting based on calculated priceFrom');
      }

      return {
        data: filteredResults,
        totalCount: filteredResults.length, // Use filtered count for accurate pagination
      };
    } catch (error) {
      console.error("Error filtering classes:", error);
      throw error;
    }
  }

  // Find nearby centers with geospatial query
  async findNearbyCenters(geoLocation, filters, sortBy, pagination) {
    try {
      // 1. Build the $geoNear stage - THIS MUST BE THE FIRST STAGE
      const geoNearStage = FilterUtil.buildGeoNearStage(
        geoLocation.longitude,
        geoLocation.latitude,
        geoLocation.maxDistance
      );

      // Initialize the aggregation pipeline
      const pipeline = [geoNearStage];

      // 2. Build the $match stage for all other filters
      const { searchQuery, ...restFilters } = filters;
      const matchFilters = FilterUtil.buildFilters(restFilters);

      // Add text search to match filters if provided
      if (searchQuery) {
        matchFilters.$text = { $search: searchQuery };
      }

      // Add the $match stage to the pipeline if there are any filters
      if (Object.keys(matchFilters).length > 0) {
        pipeline.push({ $match: matchFilters });
      }

      // Debug output
      console.log(
        "Finding nearby centers with pipeline:",
        JSON.stringify(pipeline, null, 2)
      );
      
      // 3. Add pagination stages
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 50;
      const skip = (page - 1) * limit;

      // For aggregation, we use a $facet to get both data and total count
      // in one query.
      const facetStage = {
        $facet: {
          paginatedResults: [{ $skip: skip }, { $limit: limit }],
          totalCount: [{ $count: "count" }],
        },
      };
      pipeline.push(facetStage);

      // Execute the aggregation pipeline
      const aggregationResult = await Center.aggregate(pipeline);

      const results = aggregationResult[0].paginatedResults;
      const totalCount = aggregationResult[0].totalCount[0] ? aggregationResult[0].totalCount[0].count : 0;
      
      // 4. Post-processing (pricing calculation, etc.) - remains the same
      // Calculate pricing for each center (like centerUseCase.getAllCenters())
      const centersWithPricing = await Promise.all(results.map(async (center) => {
        try {
          const centerClasses = await Class.find({ center: center._id });
          
          let priceFrom = null;
          let priceTo = null;

          if (centerClasses && centerClasses.length > 0) {
            // Filter out SEN classes (they should be free) - consistent with centerUseCase
            const nonSenClasses = centerClasses.filter(c => !c.sen);
            const prices = nonSenClasses.map((p) => p.charge || (p.classDate && p.classDate.charge)).filter((p) => p != null && p > 0);
            
            if (prices.length > 0) {
              priceFrom = Math.min(...prices);
              priceTo = Math.max(...prices);
            }
          }

          const centerObject = center.toObject ? center.toObject() : center;
          return {
            ...centerObject,
            priceFrom,
            priceTo,
          };
        } catch (classError) {
          // If there's an error fetching classes, just return center without pricing
          console.warn(`Error fetching classes for center ${center._id}:`, classError.message);
          const centerObject = center.toObject ? center.toObject() : center;
          return {
            ...centerObject,
            priceFrom: null,
            priceTo: null,
          };
        }
      }));

      // Apply price range filtering on calculated pricing (after DB query)
      let filteredResults = centersWithPricing;
      if (filters.priceMin !== undefined && filters.priceMax !== undefined && 
          !isNaN(filters.priceMin) && !isNaN(filters.priceMax)) {
        console.log(`Filtering nearby centers by price range: ${filters.priceMin} - ${filters.priceMax}`);
        
        filteredResults = centersWithPricing.filter(center => {
          if (!center.priceFrom || !center.priceTo) {
            return false; // Exclude centers without pricing
          }
          
          // Center matches if its price range overlaps with the filter range
          const rangeOverlaps = (
            center.priceFrom <= filters.priceMax && 
            center.priceTo >= filters.priceMin
          );
          
          return rangeOverlaps;
        });
        
        console.log(`Filtered from ${centersWithPricing.length} to ${filteredResults.length} nearby centers`);
      }

      // Apply sorting on calculated pricing (post-query sorting)
      if (sortBy === 'price') {
        filteredResults.sort((a, b) => {
          const priceA = a.priceFrom || 0;
          const priceB = b.priceFrom || 0;
          return priceA - priceB; // Ascending order
        });
        console.log('Applied price sorting to nearby centers based on calculated priceFrom');
      }

      return {
        data: filteredResults,
        totalCount: totalCount, // Use the count from the aggregation pipeline
      };
    } catch (error) {
      console.error("Error finding nearby centers:", error);
      throw error;
    }
  }
  async searchCoachesByName(name, pagination) {
    try {
      // Build the query
      let coachQuery = Coach.find({ displayName: { $regex: name, $options: "i" },
      legalName: { $regex: name, $options: "i" } });

      // Apply pagination
      const page = pagination?.page || 1;
      const limit = pagination?.limit || 50;
      const skip = (page - 1) * limit;

      // Get total count for pagination
      const totalCount = await Coach.countDocuments({
        name: { $regex: name, $options: "i" },
      });

      // Execute the query with pagination
      const results = await coachQuery.skip(skip).limit(limit);

      return {
        data: results,
        totalCount: totalCount,
      };
    } catch (error) {
      console.error("Error searching coaches by name:", error);
      throw error;
    }
  }
}

module.exports = SearchRepository;
