class DiscountRepository {
  constructor(model) {
    this.model = model;
  }

  async createDiscount(discountData) {
    return await this.model.create(discountData);
  }

  async getDiscountByCode(code) {
    return await this.model.findOne({ code });
  }

  async getAllDiscounts() {
    return await this.model.find();
  }
  async getById(userId) {
    console.log(userId);
    const discounts = await this.model.find({
      $or: [
        { userSpecific: false }, // Universal discounts
        { userSpecific: true, allowedUsers: userId }, // User-specific discounts
      ],
    });
    console.log(discounts);
    return discounts;
  }
  async getUserSpecificDiscounts(userId) {
    return await this.model.find({ userSpecific: true, allowedUsers: userId });
  }

  async getUniversalDiscounts() {
    return await this.model.find({ userSpecific: false });
  }

  async deleteDiscount(code) {
    return await this.model.findOneAndDelete({ code });
  }

  // New method for campaign discount sync
  async updateDiscount(code, updateData) {
    return await this.model.findOneAndUpdate(
      { code },
      updateData,
      { new: true, upsert: false }
    );
  }
}

module.exports = DiscountRepository;
