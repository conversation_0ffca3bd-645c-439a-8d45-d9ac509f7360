const User = require("../models/userModel");
const mongoose = require("mongoose");

class UserRepository {
  async findByEmail(email) {
    try {
      return await User.findOne({ email }); // Use User model
    } catch (error) {
      console.error("Error finding user by email:", error);
      throw error;
    }
  }

  async save(user) {
    console.log("Saving user...");
    console.log(user);

    try {
      const newUser = new User(user); // Make sure 'user' matches the User model schema
      const savedUser = await newUser.save();
      console.log("User saved successfully:", savedUser);
      return savedUser;
    } catch (error) {
      console.error("Error saving user:", error.message); // Log only the error message for better clarity
      throw new Error(`Failed to save user: ${error.message}`); // Provide detailed error message
    }
  }

  async exists(userId) {
    try {
      console.log("userCheking ", userId);

      // Validate userId format
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error("Invalid user ID format");
      }
      const userExists = await User.findById(userId);
      return userExists !== null;
    } catch (error) {
      throw new Error(`Failed to check if user exists: ${error.message}`);
    }
  }

  async findById(id) {
    try {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        console.error("Invalid ID format:", id);
        return null;
      }

      console.log("Querying for user with ID:", id);
      const user = await User.findById(id); // Use User model for finding by ID
      console.log("User fetched by ID:", user);
      return user;
    } catch (error) {
      console.error("Error finding user by ID:", error);
      throw error;
    }
  }

  async deleteById(id) {
    try {
      return await User.findByIdAndDelete(id); // Use User model for deletion
    } catch (error) {
      console.error("Error deleting user by ID:", error);
      throw error;
    }
  }

  async update(id, updateData) {
    try {
      console.log(updateData);
      return await User.findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true, // Ensure validators are run
      }); // Use User model for updating
    } catch (error) {
      console.error("Error updating user:", error);
      throw error;
    }
  }
  
  async addBalance(userId, amount) {
    try {
      console.log(`Adding ${amount} to balance for user ${userId}`);
      
      // Validate the user ID format
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error("Invalid user ID format");
      }
      
      // Find the user and increment their balance
      const updatedUser = await User.findByIdAndUpdate(
        userId,
        { $inc: { balance: amount } }, // Use $inc to add to the existing balance
        { new: true, runValidators: true } // Return the updated document
      );
      
      if (!updatedUser) {
        throw new Error("User not found");
      }
      
      console.log(`Balance updated successfully. New balance: ${updatedUser.balance}`);
      return updatedUser;
    } catch (error) {
      console.error("Error adding balance:", error);
      throw new Error(`Failed to add balance: ${error.message}`);
    }
  }
  
  async deleteAddress(userId, addressId) {
    try {
      return await User.findByIdAndUpdate(
        userId,
        { $pull: { location: { _id: addressId } } },
        { new: true, runValidators: true } // Ensure validators are run
      ); // Use User model for updating
    } catch (error) {
      throw new Error(`Delete failed: ${error.message}`);
    }
  }

  async findByBaseUserId(baseUserId) {
    try {
      if (!mongoose.Types.ObjectId.isValid(baseUserId)) {
        console.error("Invalid baseUser ID format:", baseUserId);
        return null;
      }
      
      console.log("Querying for parent user with baseUser ID:", baseUserId);
      const user = await User.findOne({ baseUser: baseUserId });
      console.log("Parent user found:", !!user);
      return user;
    } catch (error) {
      console.error("Error finding user by baseUser ID:", error);
      throw error;
    }
  }
}

module.exports = UserRepository;
