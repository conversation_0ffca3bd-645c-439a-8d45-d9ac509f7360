const subscriptionPlanModel = require("../models/subcriptionPlanModel");

class SubscriptionPlanRepo {
  async getAllPlans() {
    try {
      const plans = await subscriptionPlanModel.find();
      console.log(plans);
      return plans;
    } catch (error) {
      throw new Error("Something went Wrong getting all plans");
    }
  }

  async createNewPlan(planData) {
    try {
      const newPlan = new subscriptionPlanModel(planData);
      return await newPlan.save();
    } catch (error) {
      console.log(error);
      throw new Error("Something went wrong while creating a new plan.");
    }
  }

  async updatePlan(planId, planData) {
    try {
      return await subscriptionPlanModel.findByIdAndUpdate(planId, planData, {
        new: true,
      });
    } catch (error) {
      throw new Error("Something went wrong while updating a plan.");
    }
  }
  async findPlanById(planId) {
    try {
      return await subscriptionPlanModel.findById(planId);
    } catch (error) {
      console.log(error);
      throw new Error("Something went Wrong finding a plan");
    }
  }
  async deletePlan(planId) {
    try {
      return await subscriptionPlanModel.findByIdAndDelete(planId);
    } catch (error) {
      console.log(error);
      throw new Error("Something went Wrong deleting a plan");
    }
  }
}

module.exports = SubscriptionPlanRepo;
