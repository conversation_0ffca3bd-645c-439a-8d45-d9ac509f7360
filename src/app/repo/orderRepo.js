const Order = require("../models/ordersModel");
const mongoose = require("mongoose");
class OrderRepository {
  async create(orderData, options = {}) {
    const session = options.session || (await mongoose.startSession());
    let sessionStarted = !options.session; // Determine if this method started the session
    if (sessionStarted) {
      session.startTransaction();
    }
    try {
      console.log("Order Data Received:", orderData);
      const order = new Order(orderData);
      await order.save({ session });
      console.log("Order Created:", order);
      if (sessionStarted) {
        await session.commitTransaction();
        console.log("Transaction Committed");
      }
      return order;
    } catch (error) {
      if (sessionStarted) {
        await session.abortTransaction();
        console.error("Transaction Aborted:", error);
      }
      throw new Error(`Failed to create order: ${error.message}`);
    } finally {
      if (sessionStarted) {
        session.endSession();
      }
    }
  }

  async getById(orderId) {
    return await Order.findById(orderId).populate({
      path: "classId", // Populate the classId field
    });
  }

  async getAll(skip = 0, limit = 10, filter = {}) {
    try {
      // Convert string IDs to ObjectId if needed for $in operator
      if (filter.classId && filter.classId.$in) {
        const { ObjectId } = require("mongoose").Types;
        filter.classId.$in = filter.classId.$in.map((id) =>
          typeof id === "string" ? new ObjectId(id) : id
        );
      }

      // Build the query
      let query = Order.find(filter);

      // Apply sorting, skipping, and limiting
      query = query
        .sort({ createdAt: -1 })
        .skip(parseInt(skip))
        .limit(parseInt(limit));

      // Populate necessary fields
      query = query.populate([
        { path: "classId", select: "name" },
        { path: "userId", select: "name email" },
        { path: "childId", select: "name" },
      ]);

      // Execute the query
      return await query.exec();
    } catch (error) {
      console.error("Error in OrderRepository.getAll:", error);
      throw error;
    }
  }

  async update(orderId, orderData) {
    return await Order.findByIdAndUpdate(orderId, orderData, {
      new: true,
    }).populate({
      path: "classId", // Populate the classId field
    });
  }

  async delete(orderId) {
    return await Order.findByIdAndDelete(orderId);
  }

  async getByUser(userId) {
    const order = await Order.find({ userId })
      .populate({
        path: "classId",
        select: "-student -dates",
        populate: [
          {
            path: "center",
            select: "displayName address",
          },
          {
            path: "coach",
            select: "displayName",
          },
        ],
      })
      .populate("childId", "fullname")
      .lean();
    console.log(order.length);
    return order;
  }
  async getByUserForPending(userId) {
    try {
      const orders = await Order.find({ userId })
        .sort({ createdAt: -1 })
        .select("classId childId createdAt")
        .populate({
          path: "classId",
          select: "coach classProviding center",
          populate: [
            {
              path: "center",
              select: "displayName",
            },
            {
              path: "coach",
              select: "displayName",
            },
          ],
        })
        .populate({
          path: "childId",
          select: "fullname", // Adjust field name based on your child schema
        });

      return orders || [];
    } catch (error) {
      console.error("Error in getByUserForPending:", error);
      throw new Error(`Failed to fetch orders for pending reviews: ${error.message}`);
    }
  }

  // NEW METHOD: Check if a child has already booked a specific class
  async checkChildAlreadyBooked(childId, classId) {
    try {
      const existingOrder = await Order.findOne({
        childId: childId,
        classId: classId,
        paid: true // Only consider paid orders as confirmed bookings
      });
      
      return existingOrder !== null;
    } catch (error) {
      console.error("Error checking if child already booked:", error);
      throw new Error(`Failed to check booking status: ${error.message}`);
    }
  }

  // NEW METHOD: Get all children of a user who have already booked a specific class
  async getChildrenWhoBookedClass(userId, classId) {
    try {
      const orders = await Order.find({
        userId: userId,
        classId: classId,
        paid: true // Only consider paid orders
      }).populate('childId', 'fullname').lean();
      
      return orders.map(order => order.childId);
    } catch (error) {
      console.error("Error getting children who booked class:", error);
      throw new Error(`Failed to get booked children: ${error.message}`);
    }
  }

  // NEW METHOD: Get eligible children for a class (children who haven't booked it yet)
  async getEligibleChildrenForClass(userId, classId, allChildren) {
    try {
      const bookedChildren = await this.getChildrenWhoBookedClass(userId, classId);
      const bookedChildIds = bookedChildren.map(child => child._id.toString());
      
      // Filter out children who have already booked this class
      const eligibleChildren = allChildren.filter(child => 
        !bookedChildIds.includes(child._id.toString())
      );
      
      return {
        eligibleChildren,
        bookedChildren,
        totalChildren: allChildren.length,
        eligibleCount: eligibleChildren.length,
        bookedCount: bookedChildren.length
      };
    } catch (error) {
      console.error("Error getting eligible children:", error);
      throw new Error(`Failed to get eligible children: ${error.message}`);
    }
  }

}

module.exports = OrderRepository;
