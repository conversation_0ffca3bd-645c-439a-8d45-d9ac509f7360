const PurchasedHistory = require("../models/purchaseHistoryModel");

class PurchasedHistoryRepo {
  async create(purchaseHistoryData, options = {}) {
    try {
      const purchaseHistory = new PurchasedHistory(purchaseHistoryData);
      return await purchaseHistory.save(options);
    } catch (error) {
      throw new Error(`Failed to create purchase history: ${error.message}`);
    }
  }
  async get(userId) {
    try {
      return await PurchasedHistory.find({ user: userId })
        .populate({
          path: "order",
          select: "amount quantity",
          populate:[
            {
                path:"classId",
                select:"classProviding"
            }
          ]
        })
        .sort({ createdAt: -1 });
    } catch (error) {
      throw new Error(`Failed to find purchase history: ${error.message}`);
    }
  }
}

module.exports = PurchasedHistoryRepo;
