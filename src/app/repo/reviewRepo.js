const mongoose = require("mongoose");
const Review = require("../models/reviewModel");

class ReviewRepository {
  async createReview(reviewData) {
    if (reviewData.rating) {
      reviewData.rating = parseFloat(reviewData.rating).toFixed(2);
    }

    const review = new Review(reviewData);
    try {
      await review.save();
      return review;
    } catch (error) {
      console.error("Error creating review:", error);
      throw new Error("Error creating review: " + error.message);
    }
  }

  async getReviewsByRevieweeIdAndType(revieweeId, revieweeType) {
    try {
      const reviews = await Review.find({ revieweeId, revieweeType }).populate({
        path: "classId",
        select: "mainImage",
      });

      return reviews;
    } catch (error) {
      throw new Error("Error fetching reviews: " + error.message);
    }
  }

  async getReviewsByReviewerIdAndType(reviewerId, reviewerType) {
    try {
      const castReviewerId = new mongoose.Types.ObjectId(reviewerId);

      const reviews = await Review.find({
        reviewerId: castReviewerId,
        reviewerType,
      })
        .populate("revieweeId", "name")
        .populate("classId");
      return reviews;
    } catch (error) {
      console.error(
        `[ReviewRepository] Error fetching reviews for reviewerId: ${reviewerId}, reviewerType: ${reviewerType}. Error: ${error.message}`,
        error
      );
      throw new Error("Error fetching reviews: " + error.message);
    }
  }

  async getAverageRatingByRevieweeId(revieweeId, revieweeType) {
    try {
      const result = await Review.aggregate([
        {
          $match: {
            revieweeId: new mongoose.Types.ObjectId(revieweeId),
            revieweeType,
          },
        },
        {
          $group: {
            _id: "$revieweeId",
            avgRating: { $avg: "$rating" },
            reviewCount: { $sum: 1 },
          },
        },
      ]);

      if (result.length > 0) {
        const avgRating = Number(result[0].avgRating.toFixed(2)); // ensure number type
        const reviewCount = result[0].reviewCount;
        return { avgRating, reviewCount };
      } else {
        return { avgRating: 0, reviewCount: 0 };
      }
    } catch (error) {
      console.error("Error calculating average rating:", error);
      throw new Error("Error calculating average rating: " + error.message);
    }
  }

  async getReviewByClassAndId(classId, revieweeId, revieweeType, date) {
    try {
      const reviews = await Review.findOne({
        classId,
        revieweeId,
        revieweeType,
      });
      return reviews;
    } catch (error) {
      throw new Error("Error fetching review: " + error.message);
    }
  }
  async getmoments(revieweeId) {
    try {
      const moments = await Review.find({ revieweeId: revieweeId }).select(
        "images date"
      );

      return moments;
    } catch (error) {
      throw Error("Error fetching review: " + error.message);
    }
  }

  async findReview(criteria) {
    try {
      // Construct the query object carefully
      const query = {};
      if (criteria.reviewerId)
        query.reviewerId = new mongoose.Types.ObjectId(criteria.reviewerId);
      if (criteria.revieweeId)
        query.revieweeId = new mongoose.Types.ObjectId(criteria.revieweeId);
      if (criteria.revieweeType) query.revieweeType = criteria.revieweeType;
      if (criteria.classId)
        query.classId = new mongoose.Types.ObjectId(criteria.classId);

      // For date matching, it's best to match a specific event or a date range (e.g., start of day to end of day)
      // If criteria.date is a specific ISODate, it should match directly.
      // If criteria.eventId is provided, that would be even better if reviews are linked to event IDs.
      if (criteria.date) {
        // Assuming criteria.date is a JS Date object or a string that can be parsed into one.
        // To match a specific day, you might need to query for a date range.
        const targetDate = new Date(criteria.date);
        const startDate = new Date(targetDate.setHours(0, 0, 0, 0));
        const endDate = new Date(targetDate.setHours(23, 59, 59, 999));
        query.date = { $gte: startDate, $lte: endDate };
      }
      // Add other criteria as needed, e.g., if you store eventId on the review model
      // if (criteria.eventId) query.eventId = new mongoose.Types.ObjectId(criteria.eventId);

      const review = await Review.findOne(query);
      return review; // Returns the review document or null
    } catch (error) {
      console.error("Error in ReviewRepository findReview:", error);
      throw new Error("Error finding review: " + error.message);
    }
  }

  async findReviewsByParentAndChildren({
    reviewerId,
    revieweeIds,
    revieweeType,
  }) {
    try {
      // Ensure revieweeIds is an array and not empty
      if (!Array.isArray(revieweeIds) || revieweeIds.length === 0) {
        return [];
      }

      const castReviewerId = new mongoose.Types.ObjectId(reviewerId);
      const castRevieweeIds = revieweeIds.map(
        (id) => new mongoose.Types.ObjectId(id)
      );

      const query = {
        reviewerId: castReviewerId, // Now explicitly casting to ObjectId
        revieweeId: { $in: castRevieweeIds },
        revieweeType: revieweeType,
      };
      // .lean() can be good for performance as it returns plain JS objects instead of Mongoose documents
      const reviews = await Review.find(query).lean();
      return reviews;
    } catch (error) {
      console.error(
        "[ReviewRepository] Error in findReviewsByParentAndChildren:",
        error
      );
      // It's often better to throw the error so the calling use case can handle it,
      // or decide to return an empty array if that's acceptable.
      throw error;
    }
  }
  async findByClassIdsAndParentId(classIds, parentId) {
    try {
      // Ensure parentId is a valid ObjectId
      const castParentId = new mongoose.Types.ObjectId(parentId);

      const reviews = await Review.find({
        //   classId: { $in: classIds },
        reviewerId: castParentId,
      }).populate({
        path: "classId",
        select: "classProviding",
      });

      // console.log(reviews);
      return reviews || [];
    } catch (error) {
      console.error("Error in findByClassIdsAndParentId:", error);
      throw new Error(`Failed to fetch reviews by class IDs and parent ID: ${error.message}`);
    }
  }

  async find(query) {
    try {
      return await Review.find(query).lean();
    } catch (error) {
      console.error("Error in ReviewRepository find:", error);
      throw new Error("Error finding reviews: " + error.message);
    }
  }
}

module.exports = ReviewRepository;
