const Manager = require("../models/managerModel");
const mongoose = require("mongoose");
class ManagerRepository {
  async save(managerData) {
    try {
      return await new Manager(managerData).save();
    } catch (error) {
      console.error("Error saving manager:", error);
      throw error;
    }
  }

  async update(managerId, managerUpdates, session = null) {
    try {
      const options = { new: true };
      if (session) {
        options.session = session;
      }
      return await Manager.findByIdAndUpdate(
        managerId,
        managerUpdates,
        options
      );
    } catch (error) {
      console.error("Error updating manager:", error);
      throw error;
    }
  }

  async delete(managerId, session) {
    try {
      return await Manager.findByIdAndDelete(managerId, { session });
    } catch (error) {
      console.error("Error deleting manager:", error);
      throw error;
    }
  }

  async findById(managerId) {
    try {
      return await Manager.findById(managerId);
    } catch (error) {
      console.error("Error finding manager by ID:", error);
      throw error;
    }
  }
  async findByCenter(centerId) {
    try {
      if (!mongoose.Types.ObjectId.isValid(centerId)) {
        throw new Error("Invalid manager ID");
      }

      return await Manager.findOne({ center: centerId });
    } catch (error) {
      throw error;
    }
  }
  async findByCoach(coachId) {
    try {
      return await Manager.findById({ manager: coachId });
    } catch (error) {
      throw error;
    }
  }
}

module.exports = ManagerRepository;
