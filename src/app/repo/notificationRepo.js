const INotificationRepository = require("../../interfaces/INotificationRepo");
const Token = require("../models/tokenModel");
const Notification = require("../models/notificationModel");
class NotificationRepository extends INotificationRepository {
  async saveOrUpdateToken(userId, token) {
    try {
      const existingToken = await Token.findOne({ userId });
      if (existingToken) {
        existingToken.token = token;
        await existingToken.save();
      } else {
        const newToken = new Token({ userId, token });
        await newToken.save();
      }
      return {
        message: existingToken
          ? "Token updated successfully"
          : "Token saved successfully",
      };
    } catch (error) {
      console.error("Error saving or updating token:", error);
      throw new Error(error.message);
    }
  }

  async getTokenById(userId) {
    try {
      const token = await Token.findOne({ userId });
      console.log(userId);
      return token;
    } catch (error) {
      console.error("Error getting token by ID:", error);
      throw new Error("Error getting token by ID");
    }
  }
  async getNotification(userId) {
    try {
      console.log("userId");
      return await Notification.find({ userId }).sort({ createdAt: -1 });
    } catch (error) {
      throw new Error(error.message);
    }
  }
  async saveNotification(data) {
    try {
      console.log("Here I am");

      const notification = new Notification(data);
      await notification.save();
      console.log("Notification saved successfully");

      return notification;
    } catch (error) {
      throw new Error(error.message);
    }
  }

  async deleteById(notificationId) {
    try {
      console.log("hi");
      const result = await Notification.deleteOne({ _id: notificationId });

      if (result.deletedCount === 0) {
        return {
          success: false,
          message: "Notification not found or already deleted.",
        };
      }

      return true;
    } catch (error) {
      console.error("Error deleting notification:", error);
      return { success: false, message: error.message };
    }
  }
  async findById(notificationId){
    return await Notification.findById(notificationId);
  }
}

module.exports = NotificationRepository;
