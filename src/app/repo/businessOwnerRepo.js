const { default: mongoose } = require("mongoose");
const BusinessOwnerModel = require("../models/businessOwnerModel");
const CenterMddel = require("../models/centerModel");

class BusinessOwnerRepository {
  async save(userData) {
    try {
      const newUser = new BusinessOwnerModel(userData);
      return await newUser.save();
    } catch (error) {
      throw error;
    }
  }

  async findById(id) {
    try {
      console.log(id);
      return await BusinessOwnerModel.findById(id);
    } catch (error) {
      throw error;
    }
  }
  async update(id, updateData) {
    try {
      return await BusinessOwnerModel.findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true,
      });
    } catch (error) {
      throw error;
    }
  }
  async findBranchesByOwner(ownerId) {
    try {
      // Query the database to find the branches by ownerId
      const branches = await CenterMddel.find({ owner: ownerId });

      return branches;
    } catch (error) {
      console.error("Error finding branches:", error);
      throw new Error("Could not find branches");
    }
  }
  async deleteBranch(branchId) {
    try {
      // Query the database to delete the branch by branchId
      const deleteBranch = await CenterMddel.findByIdAndDelete(branchId);
      return deleteBranch;
    } catch (error) {
      // console.error("Error deleting branch:", error);
      throw new Error("Could not delete branch");
    }
  }
  async findByEmail(email) {
    try {
      console.log("hi");
      return await BusinessOwnerModel.findOne({ email }); // Query the database to find a user by email
    } catch (error) {
      throw error;
    }
  }

  async startSession() {
    try {
      return await mongoose.startSession();
    } catch (e) {
      console.log(e);
      throw e;
    }
  }
}
module.exports = BusinessOwnerRepository;
