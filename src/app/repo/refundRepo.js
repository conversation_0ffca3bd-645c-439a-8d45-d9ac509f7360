const Refund = require('../models/refundModel');

class RefundRepo {
  async saveRefund(refundData) {
    const refund = new Refund(refundData);
    return await refund.save();
  }

  async getRefundsByStudent(studentId) {
    return await Refund.find({ studentId });
  }

  async getRefundsByClass(classId) {
    return await Refund.find({ classId });
  }

  async getRefundsByEvent(eventId) {
    return await Refund.find({ eventId });
  }

  async getRefundById(refundId) {
    return await Refund.findById(refundId);
  }

  async updateRefund(refundId, updateData) {
    return await Refund.findByIdAndUpdate(refundId, updateData, { new: true });
  }

  async deleteRefund(refundId) {
    return await Refund.findByIdAndDelete(refundId);
  }

  async find(filter) {
    return await Refund.find(filter);
  }
}

module.exports = RefundRepo; 