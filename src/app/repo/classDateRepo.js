const ClassDateModel = require("../models/classDateModel");
const { Types } = require("mongoose");
class ClassDateRepo {
  async getById(dateId) {
    const id = Types.ObjectId.isValid(dateId)
      ? new Types.ObjectId(dateId)
      : dateId;
    return await ClassDateModel.findOne({ _id: id });
  }

  async getDateById(dateId) {
    const id = Types.ObjectId.isValid(dateId)
      ? new Types.ObjectId(dateId)
      : dateId;
    return await ClassDateModel.findById(id).populate('students');
  }

  async update(dateId, data) {
    const id = Types.ObjectId.isValid(dateId)
      ? new Types.ObjectId(dateId)
      : dateId;

    try {
      const updatedDoc = await ClassDateModel.findByIdAndUpdate(
        id,
        { $set: data },
        { new: true } // return updated doc
      );

      if (!updatedDoc) {
        throw new Error("ClassDate not found");
      }

      return updatedDoc;
    } catch (error) {
      console.error("Failed to update ClassDate:", error);
      throw error;
    }
  }
  async getDateByClassIdAndDate(classId, date) {
    return await ClassDateModel.findOne({ classId, date });
  }
  async addStudentToDate(classId, date, studentId, options = {}) {
    return await ClassDateModel.findOneAndUpdate(
      {
        classId,
        date,
      },
      {
        $addToSet: {
          students: {
            $each: Array.isArray(studentId) ? studentId : [studentId],
          },
        },
      },
      { new: true, ...options }
    );
  }

  async removeStudentFromDate(classId, date, studentId) {
    return await ClassDateModel.findOneAndUpdate(
      {
        classId,
        date,
      },
      {
        $pull: { students: studentId },
      },
      { new: true }
    );
  }

  async getStudentsByDate(classId, date) {
    const result = await ClassDateModel.findOne({
      classId,
      date,
    }).populate("students");

    return result?.students || [];
  }
  async getStudents(slotId) {
    try {
      console.log(slotId);
      const classData = await ClassDateModel.findById(slotId)
        // .select("students")
        // .populate({
        //   path: "students",
        //   select: "fullname birthday rating phone mainImage parent",
        // });

      // If class not found
      if (!classData) {
        throw new Error("Class not found");
      }

      // Return just the student array
      return classData.students;
    } catch (error) {
      throw error;
    }
  }
  async updateDateDetails(classId, date, updateData) {
    return await ClassDateModel.findOneAndUpdate(
      {
        classId,
        date,
      },
      {
        $set: updateData,
      },
      { new: true }
    );
  }

  async getDatesInRange(classId, startDate, endDate) {
    return await ClassDateModel.find({
      classId,
      date: {
        $gte: startDate,
        $lte: endDate,
      },
    });
  }

  async createDate(dateData) {
    console.log("Creating class date with data:", dateData);
    return await ClassDateModel.create(dateData);
  }

  async createManyDates(datesData) {
    return await ClassDateModel.insertMany(datesData);
  }

  async removeDate(classId, date) {
    return await ClassDateModel.findOneAndDelete({
      classId,
      date,
    });
  }

  async getDateAttendance(classId, date) {
    const result = await ClassDateModel.findOne({
      classId,
      date,
    }).populate("students attendance.studentId");

    return {
      totalStudents: result?.students?.length || 0,
      students: result?.students || [],
      attendance: result?.attendance || [],
    };
  }

  async updateAttendance(classId, date, studentId, attendanceData) {
    return await ClassDateModel.findOneAndUpdate(
      {
        classId,
        date,
        "attendance.studentId": studentId,
      },
      {
        $set: {
          "attendance.$": {
            studentId,
            ...attendanceData,
          },
        },
      },
      {
        new: true,
        upsert: true, // Creates the attendance record if it doesn't exist
      }
    );
  }

  /**
   * Get all slots (dates) for a given class by classId
   * @param {string} classId
   * @returns {Promise<Array>} Array of slot documents
   * @throws {Error} If classId is missing
   */
  async getDatesByClassId(classId) {
    if (!classId) {
      throw new Error('Class ID is required');
    }
    return await ClassDateModel.find({ classId }).sort({ date: 1 });
  }

  async updateStatus(classId, date, status) {
    return await ClassDateModel.findOneAndUpdate(
      {
        classId,
        date,
      },
      {
        $set: { status },
      },
      { new: true }
    );
  }

  // Delete all class dates for a specific class
  async deleteByClassId(classId, options = {}) {
    try {
      const result = await ClassDateModel.deleteMany({ classId }, options);
      console.log(`Deleted ${result.deletedCount} class dates for class ${classId}`);
      return result;
    } catch (error) {
      console.error(`Error deleting class dates for class ${classId}:`, error);
      throw new Error(`Error deleting class dates: ${error.message}`);
    }
  }

  async getStudentsAttendanceStats(classId, startDate, endDate) {
    return await ClassDateModel.aggregate([
      {
        $match: {
          classId,
          date: { $gte: startDate, $lte: endDate },
        },
      },
      {
        $unwind: "$attendance",
      },
      {
        $group: {
          _id: "$attendance.studentId",
          totalClasses: { $sum: 1 },
          present: {
            $sum: { $cond: [{ $eq: ["$attendance.status", "present"] }, 1, 0] },
          },
          absent: {
            $sum: { $cond: [{ $eq: ["$attendance.status", "absent"] }, 1, 0] },
          },
          late: {
            $sum: { $cond: [{ $eq: ["$attendance.status", "late"] }, 1, 0] },
          },
        },
      },
    ]);
  }

  async getStudents(slotId) {
    try {
      const classData = await ClassDateModel.findById(slotId)
        .select("charge students")
        .populate({
          path: "students",
          select: "fullname birthday rating phone mainImage parent",
        });

      // If class not found
      if (!classData) {
        throw new Error("Class not found");
      }

      // Return just the student array
      return classData;
    } catch (error) {
      throw error;
    }
  }

  async getClassDatesByStudentIds(childIds) {
    return await ClassDateModel.find({ students: { $in: childIds } });
  }

  /**
   * Delete a slot (class date) by its slotId
   * @param {string} slotId
   * @returns {Promise<Object|null>} The deleted slot document or null if not found
   */
  async deleteBySlotId(slotId) {
    if (!slotId) {
      throw new Error('Slot ID is required');
    }
    return await ClassDateModel.findByIdAndDelete(slotId);
  }
}

module.exports = ClassDateRepo;
