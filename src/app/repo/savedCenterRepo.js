const SavedCenter = require("../models/savedCenterModel");
const mongoose = require("mongoose");

class SavedCenterRepository {
  constructor() {
    // Ensure indexes are created for optimal performance
    this.ensureIndexes();
  }

  // Create database indexes for performance optimization
  async ensureIndexes() {
    try {
      // Compound index for parentId + centerId (most common query)
      await SavedCenter.collection.createIndex(
        { parentId: 1, centerId: 1 }, 
        { unique: true, background: true }
      );
      
      // Index for parentId (for getSavedCenters)
      await SavedCenter.collection.createIndex(
        { parentId: 1, createdAt: -1 }, 
        { background: true }
      );
      
      console.log('SavedCenter indexes created successfully');
    } catch (error) {
      console.log('Indexes may already exist:', error.message);
    }
  }

  async saveCenter(parentId, centerId) {
    try {
      const savedCenter = new SavedCenter({
        parentId,
        centerId,
      });
      return await savedCenter.save();
    } catch (error) {
      if (error.code === 11000) {
        // Duplicate key error (center already saved)
        throw new Error("Center already saved");
      }
      console.error("Error saving center:", error);
      throw new Error("Failed to save center");
    }
  }

  async unsaveCenter(parentId, centerId) {
    try {
      const result = await SavedCenter.deleteOne({
        parentId,
        centerId,
      });
      
      // Don't throw error if center wasn't found - treat as successful unsave
      // This handles cases where the center was already unsaved or never saved
      return {
        success: true,
        wasFound: result.deletedCount > 0,
        deletedCount: result.deletedCount
      };
    } catch (error) {
      console.error("Error unsaving center:", error);
      throw new Error("Failed to unsave center");
    }
  }

  async getSavedCenters(parentId) {
    try {
      console.log("getting all saved centers for parent:", parentId);
      const centers = await SavedCenter.find({ parentId })
        .select("-__v") // Exclude version field instead of id
        .populate({
          path: "centerId",
          select: "-businessCertificate -hkidCard -__v",
        })
        .sort({ createdAt: -1 })
        .lean(); // Use lean() for better performance when no modification needed
      
      console.log(`Found ${centers.length} saved centers`);
      return centers;
    } catch (error) {
      console.error("Error getting saved centers:", error);
      throw new Error("Failed to get saved centers");
    }
  }

  async isCenterSaved(parentId, centerId) {
    try {
      // PERFORMANCE OPTIMIZATION: Use findOne instead of countDocuments
      // findOne with lean() is much faster than countDocuments
      const exists = await SavedCenter.findOne({
        parentId,
        centerId,
      }).lean().select('_id'); // Only select the _id field
      
      return !!exists; // Convert to boolean
    } catch (error) {
      console.error("Error checking if center is saved:", error);
      throw new Error("Failed to check if center is saved");
    }
  }

  // BATCH CHECK: Check multiple centers at once with single database query
  async batchCheckSavedCenters(parentId, centerIds) {
    try {
      // Single database query to check all centers at once
      const savedCenters = await SavedCenter.find({
        parentId,
        centerId: { $in: centerIds }
      }).lean().select('centerId'); // Only get centerId field

      // Convert results to object format
      const results = {};
      
      // Initialize all centers as not saved
      centerIds.forEach(centerId => {
        results[centerId] = false;
      });
      
      // Mark saved centers as true
      savedCenters.forEach(saved => {
        results[saved.centerId.toString()] = true;
      });

      return results;
    } catch (error) {
      console.error("Error batch checking saved centers:", error);
      throw new Error("Failed to batch check saved centers");
    }
  }
}

module.exports = SavedCenterRepository;
