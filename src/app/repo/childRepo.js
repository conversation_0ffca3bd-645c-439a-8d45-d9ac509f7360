const Child = require("../models/childModel");

class ChildRepo {
  async create(childData) {
    try {
      const child = new Child(childData);
      return await child.save();
    } catch (error) {
      console.error("Error creating child:", error);
      throw error;
    }
  }

  async getChildById(id) {
    try {
      return await Child.findById(id).populate("parent").exec();
    } catch (error) {
      console.error("Error getting child by ID:", error);
      throw new Error("Failed to get child by ID.");
    }
  }

  async getChildByParentId(parentId) {
    try {
      return await Child.find({ parent: parentId }).exec();
    } catch (error) {
      console.error("Error getting children by parent ID:", error);
      throw new Error("Failed to get children by parent ID.");
    }
  }
  async getParentByChildId(childId) {
    try {
      const child = await Child.findById(childId);
      console.log(child);
      if(child)
      return child.parent;
    else return null;
    } catch (error) {
      console.error("Error getting children by parent ID:", error);
      throw new Error("Failed to get children by parent ID.");
    }
  }
  async updateChildById(id, updateData) {
    try {
      return await Child.findByIdAndUpdate(id, updateData, {
        new: true,
      }).exec();
    } catch (error) {
      console.error("Error updating child by ID:", error);
      throw new Error("Failed to update child.");
    }
  }

  async deleteChildById(id) {
    try {
      return await Child.findByIdAndDelete(id).exec();
    } catch (error) {
      console.error("Error deleting child by ID:", error);
      throw new Error("Failed to delete child.");
    }
  }
}

module.exports = ChildRepo;
