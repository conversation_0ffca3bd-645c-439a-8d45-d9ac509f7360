class RequestRepository {
  constructor(requestModel) {
    this.requestModel = requestModel;
  }

  async create(requestData) {
    try {
      await this.requestModel.create(requestData);
      return true;
    } catch (error) {
      throw new Error("Error creating request: " + error.message);
    }
  }

  async findById(id) {
    return await this.requestModel.findById(id);
  }

  async findOne(filter) {
    const find = await this.requestModel.findOne(filter);

    return find;
  }

  async update(id, updateData) {
    return await this.requestModel.findByIdAndUpdate(id, updateData, {
      new: true,
    });
  }

  async findByCenter(centerId) {
    console.log(centerId);

    return await this.requestModel.find({ centerId: centerId }).populate({
      path: "coachId",
      select: "displayName mainImage rating address.city", // select only these fields from the coach document
    });
  }

  async findByCoach(coachId) {
    return await this.requestModel.find({ coachId: coachId });
  }
  async deleteById(requestId) {
    const request = await this.requestModel.findById(requestId);
    if (!request) {
      throw new Error("Request not found.");
    }

    await this.requestModel.deleteOne({ _id: requestId });
    return true;
  }
  async findRequestByCenterForManager(centerId, coachId) {
    return await this.requestModel.findOne({
      sender: centerId,
      coachId: coachId,
      centerId: centerId,
      message: "manager request",
    });
  }
  async findRequestByCenterForCoach(centerId, coachId) {
    return await this.requestModel.findOne({
      sender: centerId,
      coachId: coachId,
      centerId: centerId,
      message: "coach request",
    });
  }
  async getRequestByCenterForManager(centerId) {
    const requests = await this.requestModel
      .find({
        sender: centerId,
        message: "manager request",
      })
      .select("coachId");

    return requests.map((req) => req.coachId.toString()); // convert ObjectId to string
  }

  async deleteRequestByCenterForManager(centerId, coachId) {
    console.log(centerId, coachId);
    const request = await this.requestModel.findOne({
      sender: centerId,
      coachId,
      centerId,
      message: "manager request",
    });
    console.log(request);
    if (!request) {
      throw new Error("Request not found");
    }

    await request.deleteOne(); // cleaner than deleteOne({_id: ...})
  }
  async getRequestByCenterForCoach(centerId) {
    const requests = await this.requestModel
      .find({
        sender: centerId,
        message: "coach request",
      })
      .select("coachId");

    return requests.map((req) => req.coachId.toString());
  }
  async deleteRequestByCenterForCoach(centerId, coachId) {
    ///delete request by center for coach
    const request = await this.requestModel.findOne({
      sender: centerId,
      coachId,
      centerId,
      message: "coach request",
    });

    if (!request) {
      throw new Error("Request not found");
    }

    await request.deleteOne(); // cleaner than deleteOne({_id: ...})
  }
}

module.exports = RequestRepository;
