// data/repositories/centerRepository.js
const Center = require("../models/centerModel");
const BaseUser = require("../models/baseUserModel");
const mongoose = require("mongoose");

class CenterRepository {
  // Create a new center
  async save(centerData) {
    console.log("repo");
    console.log(centerData);
    const center = new Center(centerData);
    return center.save(); // Save the new Center
  }
  async findByLegalName(legalName) {
    return await Center.find({ legalName: legalName });
  }
  // Find a center by ID
  async findById(id, session = null) {
    if (session) {
      return Center.findById(id).session(session); // Chain session before await
    }
    return Center.findById(id);
  }

  async findByIdWithOwner(id) {
    return await Center.findById(id).populate({
      path: "owner",
      select: "isIndividualCreator"
    });
  }

  async findByEmail(email) {
    return await Center.findOne({ email });
  }

  async getCoachsByCenterId(centerId) {
    try {
      console.log("HI");
      const center = await Center.findById(centerId).populate("coachs");
      if (!center) {
        throw new Error("Center not found");
      }
      return center.coachs;
    } catch (error) {
      throw new Error(`Error finding coaches for center: ${error.message}`);
    }
  }
  async getManagersByCenterId(centerId) {
    try {
      console.log("HI");
      const center = await Center.findById(centerId).populate("managers");
      if (!center) {
        throw new Error("Center not found");
      }
      return center.managers;
    } catch (error) {
      throw new Error(`Error finding coaches for center: ${error.message}`);
    }
  }

  async findByBaseUserId(baseUserId) {
    return Center.findOne({ baseUser: baseUserId });
  }

  // Update center information (with session)
  async update(centerId, updateData, session = null) {
    if (session) {
      return Center.findByIdAndUpdate(centerId, updateData, {
        new: true,
        session: session, // Use session for the update
      });
    }
    return Center.findByIdAndUpdate(centerId, updateData, {
      new: true,
    });
  }

  // Delete a center by ID
  async deleteById(centerId) {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Find and delete the center
      const center = await Center.findByIdAndDelete(centerId).session(session);
      if (!center) {
        throw new Error("Center not found");
      }

      await session.commitTransaction();
      session.endSession();

      return center;
    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  }

  // Find all centers, optionally populating the BaseUser
  async findAll(query = {}, skip = 0, limit = 10) {
    console.log(`page ${skip} limit ${limit}`)
    console.log('Query filters:', query);
    return Center.find(query).select('-hkidCard -businessCertificate').skip(skip).limit(limit);
  }
  async countAll(query = {}) {
    console.log('Count query filters:', query);
    return Center.countDocuments(query);
  }

  async findCenterByManager(coachId) {
    // return await Center.findById("67190235d493e133bbdab127");
    console.log("coachId");
    return await Center.findOne({ manager: coachId });
  }

  // Start a session (utility function)
  async startSession() {
    try {
      return await mongoose.startSession();
    } catch (e) {
      console.log(e);
      throw e;
    }
  }

  // Find centers by SEN service status (for spotlight filtering)
  async findBySenStatus(senStatus) {
    try {
      console.log(`Finding centers with SEN status: ${senStatus}`);
      const centers = await Center.find({ sen: senStatus });
      console.log(`Found ${centers.length} centers with SEN status: ${senStatus}`);
      return centers;
    } catch (error) {
      console.error(`Error finding centers by SEN status: ${error.message}`);
      throw new Error(`Error finding centers by SEN status: ${error.message}`);
    }
  }

  // Count centers by SEN service status (for pagination)
  async countBySenStatus(senStatus) {
    try {
      console.log(`Counting centers with SEN status: ${senStatus}`);
      const count = await Center.countDocuments({ sen: senStatus });
      console.log(`Found ${count} centers with SEN status: ${senStatus}`);
      return count;
    } catch (error) {
      console.error(`Error counting centers by SEN status: ${error.message}`);
      throw new Error(`Error counting centers by SEN status: ${error.message}`);
    }
  }
}

module.exports = CenterRepository;
