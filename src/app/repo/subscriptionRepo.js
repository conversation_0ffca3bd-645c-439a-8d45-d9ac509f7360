const SubscriptionModel = require("../models/subscriptionsModel");

class SubscriptionRepo {
  async createSubscription(data) {
    try {
      const newSubscription = new SubscriptionModel(data);

      return await newSubscription.save();
    } catch (error) {
      throw new Error("Something went wrong creating subscriptions");
    }
  }
  async getSubscriptionByUserId(userId) {
    try {
      console.log(userId);
      return await SubscriptionModel.findOne({ userId: userId });
    } catch (error) {
      throw new Error("Something went wrong fetching subscriptions");
    }
  }
  async cancelSubscriptionByUserId(userId) {
    try {
      console.log("Cancelling subscription for user:", userId);
      const result = await SubscriptionModel.findOneAndDelete({ userId: userId });
      return result;
    } catch (error) {
      throw new Error("Something went wrong cancelling subscription");
    }
  }
}

module.exports = SubscriptionRepo;
