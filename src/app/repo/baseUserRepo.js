const BaseUser = require("../models/baseUserModel");
const mongoose = require("mongoose");
class BaseUserRepository {
  async findByEmail(email) {
    try {
      //  console.log(email)
      return await BaseUser.findOne({ email });
    } catch (error) {
      console.error("Error finding user by email:", error);
      throw error;
    }
  }

  async save(user) {
    try {
      const newUser = new BaseUser(user);
      return await newUser.save();
    } catch (error) {
      console.error("Error saving user:", error);
      throw error;
    }
  }

  async findById(id) {
    try {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        console.error("Invalid ID format:", id);
        return null;
      }

      // console.log("Querying for user with ID:", id);

      const user = await BaseUser.findById(id);

      //console.log("User fetched by ID:", user);

      return user;
    } catch (error) {
      console.error("Error finding user by ID:", error);
      throw error;
    }
  }

  async deleteById(id) {
    try {
      return await BaseUser.findByIdAndDelete(id);
    } catch (error) {
      console.error("Error deleting user by ID:", error);
      throw error;
    }
  }

  async update(id, updateData) {
    try {
      return await BaseUser.findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: true,
      });
    } catch (error) {
      console.error("Error updating user:", error);
      throw error;
    }
  }
  async updatePassword(id, password) {
    try {
      return await BaseUser.findByIdAndUpdate(
        id,
        { password },
        { new: true, runValidators: true }
      );
    } catch (error) {
      console.error("Error updating user password:", error);
      throw error;
    }
  }
}

module.exports = BaseUserRepository;
