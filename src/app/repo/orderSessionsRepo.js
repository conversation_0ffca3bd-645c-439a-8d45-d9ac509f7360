// src/infrastructure/repositories/orderSessionRepo.js
const OrderSessionModel = require("../models/orderSessionsModel");

class OrderSessionRepo {
  /**
   * Insert multiple order session documents.
   * @param {Array} sessions - Array of session objects to insert.
   * @returns {Promise<Array>} Inserted session documents.
   */
  async insertMany(sessions) {
    return await OrderSessionModel.insertMany(sessions);
  }

  /**
   * Find sessions by order ID.
   * @param {String|ObjectId} orderId
   * @returns {Promise<Array>}
   */
  async findByOrderId(orderId) {
    return await OrderSessionModel.find({ orderId });
  }

  /**
   * Find sessions by user ID.
   * @param {String|ObjectId} userId
   * @returns {Promise<Array>}
   */
  async getByUser(userId) {
    return await OrderSessionModel.find({ userId });
  }

  /**
   * Delete all sessions by order ID.
   * @param {String|ObjectId} orderId
   * @returns {Promise}
   */
  async deleteByOrderId(orderId) {
    return await OrderSessionModel.deleteMany({ orderId });
  }
}

module.exports = new OrderSessionRepo();
