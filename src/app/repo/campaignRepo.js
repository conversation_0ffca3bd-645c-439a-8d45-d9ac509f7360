class CampaignRepository {
  constructor(model) {
    this.model = model;
  }

  async createCampaign(campaignData) {
    try {
      const campaign = new this.model(campaignData);
      return await campaign.save();
    } catch (error) {
      throw new Error(`Failed to create campaign: ${error.message}`);
    }
  }

  async findActiveCampaigns(targetAudience = "all") {
    try {
      const currentDate = new Date();
      const query = {
        isActive: true,
        validFrom: { $lte: currentDate },
        validUntil: { $gte: currentDate },
        $or: [
          { targetAudience: "all" },
          { targetAudience: targetAudience }
        ]
      };

      return await this.model
        .find(query)
        .sort({ priority: -1, createdAt: -1 }) // High priority first, then newest
        .limit(10); // Limit to 10 active campaigns
    } catch (error) {
      throw new Error(`Failed to fetch active campaigns: ${error.message}`);
    }
  }

  async findAllCampaigns() {
    try {
      return await this.model
        .find()
        .sort({ createdAt: -1 });
    } catch (error) {
      throw new Error(`Failed to fetch all campaigns: ${error.message}`);
    }
  }

  async findCampaignById(campaignId) {
    try {
      return await this.model.findById(campaignId);
    } catch (error) {
      throw new Error(`Failed to find campaign: ${error.message}`);
    }
  }

  async updateCampaign(campaignId, updateData) {
    try {
      return await this.model.findByIdAndUpdate(
        campaignId,
        updateData,
        { new: true, runValidators: true }
      );
    } catch (error) {
      throw new Error(`Failed to update campaign: ${error.message}`);
    }
  }

  async deleteCampaign(campaignId) {
    try {
      return await this.model.findByIdAndDelete(campaignId);
    } catch (error) {
      throw new Error(`Failed to delete campaign: ${error.message}`);
    }
  }

  async incrementClickCount(campaignId) {
    try {
      return await this.model.findByIdAndUpdate(
        campaignId,
        { $inc: { clickCount: 1 } },
        { new: true }
      );
    } catch (error) {
      throw new Error(`Failed to increment click count: ${error.message}`);
    }
  }

  async incrementImpressionCount(campaignId) {
    try {
      return await this.model.findByIdAndUpdate(
        campaignId,
        { $inc: { impressionCount: 1 } },
        { new: true }
      );
    } catch (error) {
      throw new Error(`Failed to increment impression count: ${error.message}`);
    }
  }

  async deactivateExpiredCampaigns() {
    try {
      const currentDate = new Date();
      return await this.model.updateMany(
        { 
          validUntil: { $lt: currentDate },
          isActive: true
        },
        { $set: { isActive: false } }
      );
    } catch (error) {
      throw new Error(`Failed to deactivate expired campaigns: ${error.message}`);
    }
  }
}

module.exports = CampaignRepository;