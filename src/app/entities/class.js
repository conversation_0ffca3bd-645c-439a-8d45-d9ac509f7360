class Class {
  constructor({
    classProviding,
    level,
    description,
    mode,
    sen,
    initialCharge,
    mainImage,
    center,
    dates,         // Array of date objects with startTime, endTime, durationMinutes
    repeat         // Object containing interval and period
  }) {
    this.classProviding = classProviding;
    this.level = level;
    this.description = description;
    this.mode = mode;
    this.sen = sen;
    this.initialCharge = initialCharge;
    this.mainImage = mainImage;
    this.center = center;
    this.dates = dates;           // Array of date objects
    this.repeat = repeat;         // Repeat object with interval and period
  }
}


module.exports = Class;