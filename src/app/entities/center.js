// domain/entities/center.js
class Center {
  constructor({
    legalName,
    displayName,
    address,
    companyNumber,
    centerNumber,
    email,
    openingHour,
    businessNumber,
    businessCertificate,
    sexualConvictionRecord,
    isFreelanceEducator,
    hkidCard,
    languages,
    services,
    description,
    images,
    bankDetails,
    isComplete,
  }) {
    this.legalName = legalName;
    this.displayName = displayName;
    this.address = address;
    this.companyNumber = companyNumber;
    this.centerNumber = centerNumber;
    this.email = email;
    this.openingHour = openingHour;
    this.businessNumber = businessNumber;
    this.businessCertificate = businessCertificate;
    this.sexualConvictionRecord = sexualConvictionRecord;
    this.isFreelanceEducator = isFreelanceEducator;
    this.hkidCard = hkidCard;
    this.languages = languages;
    this.services = services;
    this.description = description;
    this.images = images;
    this.bankDetails = bankDetails;
    this.isComplete = isComplete;
  }
}

module.exports = Center;
