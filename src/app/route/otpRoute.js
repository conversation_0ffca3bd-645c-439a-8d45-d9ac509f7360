const express = require("express");
const router = express.Router();

const EmailService = require("../services/emailService");
const OtpService = require("../services/otpService");
const OtpRepo = require("../repo/otpRepo");
const OtpController = require("../controllers/otpController");
const BaseUserRepository = require("../repo/baseUserRepo");

const otpRepo = new OtpRepo();
const emailService = new EmailService();
const baseUserRepository = new BaseUserRepository();
const otpService = new OtpService(otpRepo, emailService, baseUserRepository);
const otpController = new OtpController({otpService});

router.get("", async (req, res) => otpController.requestOtp(req, res));
router.post("/verify", async (req, res) => otpController.verifyOtp(req, res));

module.exports = router;

