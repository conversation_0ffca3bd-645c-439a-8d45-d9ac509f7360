const express = require('express');
const mongoose = require('mongoose');
const Chat = require('../models/chatModel');
const User = require('../models/userModel');
const Center = require('../models/centerModel');
const Coach = require('../models/coachModel');
const router = express.Router();

// Debug route to check database connection
router.get('/db-status', async (req, res) => {
  try {
    const dbState = mongoose.connection.readyState;
    let status;
    
    switch (dbState) {
      case 0: status = 'Disconnected'; break;
      case 1: status = 'Connected'; break;
      case 2: status = 'Connecting'; break;
      case 3: status = 'Disconnecting'; break;
      default: status = 'Unknown';
    }
    
    res.json({
      status,
      dbState,
      connectionString: mongoose.connection.host,
      databaseName: mongoose.connection.name
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Debug route to check chat collections and counts
router.get('/chat-stats', async (req, res) => {
  try {
    const chatCount = await Chat.countDocuments();
    const userCount = await User.countDocuments();
    const centerCount = await Center.countDocuments();
    const coachCount = await Coach.countDocuments();
    
    const recentChats = await Chat.find().sort({ timestamp: -1 }).limit(5);
    
    res.json({
      collections: {
        chats: chatCount,
        users: userCount,
        centers: centerCount,
        coaches: coachCount
      },
      recentChats: recentChats.map(chat => ({
        id: chat._id,
        sender: chat.sender,
        recipient: chat.recipient,
        senderModel: chat.senderModel,
        recipientModel: chat.recipientModel,
        timestamp: chat.timestamp,
        status: chat.status || 'unknown'
      }))
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Check for specific user's chats
router.get('/user-chats/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    // Validate if the userId is a valid ObjectId
    const isValidObjectId = mongoose.isValidObjectId(userId);
    
    if (!isValidObjectId) {
      return res.status(200).json({
        message: "The provided userId is not a valid MongoDB ObjectId",
        userExists: false,
        isValidId: false,
        chatSummary: {
          sentCount: 0,
          receivedCount: 0,
          totalPartners: 0
        },
        recentSent: [],
        recentReceived: [],
        chatPartners: []
      });
    }
    
    // Check if user exists
    let userExists = false;
    let userType = null;
    
    const user = await User.findById(userId);
    if (user) {
      userExists = true;
      userType = 'user';
    } else {
      const center = await Center.findById(userId);
      if (center) {
        userExists = true;
        userType = 'center';
      } else {
        const coach = await Coach.findById(userId);
        if (coach) {
          userExists = true;
          userType = 'coach';
        }
      }
    }
    
    if (!userExists) {
      return res.status(200).json({
        message: "No user found with this ID in any collection",
        userExists: false,
        isValidId: true,
        chatSummary: {
          sentCount: 0,
          receivedCount: 0,
          totalPartners: 0
        },
        recentSent: [],
        recentReceived: [],
        chatPartners: []
      });
    }
    
    // Find chats where user is sender
    const sentChats = await Chat.find({ sender: userId }).sort({ timestamp: -1 }).limit(10);
    
    // Find chats where user is recipient
    const receivedChats = await Chat.find({ recipient: userId }).sort({ timestamp: -1 }).limit(10);
    
    // Using a safer approach for aggregation when the user might not exist
    let chatPartners = [];
    try {
      const objectId = new mongoose.Types.ObjectId(userId);
      chatPartners = await Chat.aggregate([
        {
          $match: {
            $or: [
              { sender: objectId },
              { recipient: objectId }
            ]
          }
        },
        {
          $group: {
            _id: {
              $cond: [
                { $eq: ["$sender", objectId] },
                "$recipient",
                "$sender"
              ]
            },
            count: { $sum: 1 },
            lastMessage: { $max: "$timestamp" }
          }
        },
        { $sort: { lastMessage: -1 } },
        { $limit: 10 }
      ]);
    } catch (error) {
      console.error("Error in chat partners aggregation:", error);
      // Return empty array if aggregation fails
      chatPartners = [];
    }
    
    res.json({
      userExists,
      userType,
      isValidId: true,
      chatSummary: {
        sentCount: await Chat.countDocuments({ sender: userId }),
        receivedCount: await Chat.countDocuments({ recipient: userId }),
        totalPartners: chatPartners.length
      },
      recentSent: sentChats.map(chat => ({
        id: chat._id,
        to: chat.recipient,
        recipientModel: chat.recipientModel,
        message: chat.message.substring(0, 30) + (chat.message.length > 30 ? '...' : ''),
        timestamp: chat.timestamp,
        status: chat.status || 'unknown'
      })),
      recentReceived: receivedChats.map(chat => ({
        id: chat._id,
        from: chat.sender,
        senderModel: chat.senderModel,
        message: chat.message.substring(0, 30) + (chat.message.length > 30 ? '...' : ''),
        timestamp: chat.timestamp,
        status: chat.status || 'unknown'
      })),
      chatPartners: chatPartners
    });
  } catch (error) {
    console.error("Error in user-chats route:", error);
    res.status(500).json({ 
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Simple endpoint to check for any chats in the database
router.get('/all-chats', async (req, res) => {
  try {
    // Get total count
    const totalCount = await Chat.countDocuments();
    
    // Get a few recent chats
    const recentChats = await Chat.find()
      .sort({ timestamp: -1 })
      .limit(20)
      .lean();
    
    // Get unique senders and recipients
    const uniqueUsers = new Set();
    recentChats.forEach(chat => {
      uniqueUsers.add(chat.sender.toString());
      uniqueUsers.add(chat.recipient.toString());
    });
    
    res.json({
      totalChats: totalCount,
      uniqueUsersCount: uniqueUsers.size,
      uniqueUsers: Array.from(uniqueUsers),
      recentChats: recentChats.map(chat => ({
        id: chat._id,
        message: chat.message.substring(0, 50) + (chat.message.length > 50 ? '...' : ''),
        sender: chat.sender,
        recipient: chat.recipient,
        senderModel: chat.senderModel,
        recipientModel: chat.recipientModel,
        timestamp: chat.timestamp,
        status: chat.status || 'unknown'
      }))
    });
  } catch (error) {
    console.error("Error retrieving all chats:", error);
    res.status(500).json({ error: error.message });
  }
});

// Debug endpoint to test with sample data
router.get('/test-chat', async (req, res) => {
  try {
    // Create a sample chat message for testing
    const testChat = new Chat({
      message: "This is a test message created via debug endpoint",
      sender: new mongoose.Types.ObjectId(),
      recipient: new mongoose.Types.ObjectId(),
      senderModel: "user",
      recipientModel: "user",
      status: "sent"
    });
    
    // Save the test chat
    await testChat.save();
    
    res.json({
      success: true,
      message: "Test chat created successfully",
      chatDetails: {
        id: testChat._id,
        message: testChat.message,
        sender: testChat.sender,
        recipient: testChat.recipient,
        timestamp: testChat.timestamp,
        status: testChat.status
      }
    });
  } catch (error) {
    console.error("Error creating test chat:", error);
    res.status(500).json({ 
      success: false,
      error: error.message 
    });
  }
});

// Create test users and conversations for debugging
router.get('/create-test-data', async (req, res) => {
  try {
    // Check if we have required models
    if (!User) {
      return res.status(500).json({
        success: false,
        error: "User model not available"
      });
    }
    
    const BaseUser = require('../models/baseUserModel');
    if (!BaseUser) {
      return res.status(500).json({
        success: false,
        error: "BaseUser model not available"
      });
    }

    // Check if we already have test users
    const existingUsers = await User.find({ 
      fullname: { $regex: /^Test User/ } 
    }).limit(5);

    let testUsers = [];
    
    // Create test users if they don't exist
    if (existingUsers.length < 2) {
      console.log('Creating new test users');
      
      // Create test users with proper BaseUser references
      for (let i = 1; i <= 3; i++) {
        try {
          // First create a BaseUser
          const baseUser = new BaseUser({
            email: `testuser${i}@example.com`,
            password: "password123", // In a real app, this would be hashed
            role: "user"
          });
          
          // Save the base user
          const savedBaseUser = await baseUser.save();
          console.log(`Created base user: ${savedBaseUser._id}`);
          
          // Now create the User with reference to the base user
          const testUser = new User({
            baseUser: savedBaseUser._id,
            fullname: `Test User ${i}`,
            displayName: `TestUser${i}`,
            mainImage: `https://via.placeholder.com/150?text=User${i}`
          });
          
          // Save the user
          const savedUser = await testUser.save();
          console.log(`Created test user: ${savedUser._id}`);
          
          testUsers.push(savedUser);
        } catch (error) {
          console.error(`Error creating test user ${i}:`, error);
        }
      }
      
      console.log(`Created ${testUsers.length} test users`);
    } else {
      testUsers = existingUsers;
      console.log(`Using ${testUsers.length} existing test users`);
    }
    
    // If no test users were created successfully, return an error
    if (testUsers.length === 0) {
      return res.status(500).json({
        success: false,
        error: "Failed to create or find test users"
      });
    }
    
    // Create test chats between users
    const chatPromises = [];
    
    // Create conversations between the first user and all others
    const firstUser = testUsers[0];
    
    for (let i = 1; i < testUsers.length; i++) {
      const recipient = testUsers[i];
      
      // Add a few messages in each conversation
      for (let msgNum = 1; msgNum <= 3; msgNum++) {
        // Messages from first user to recipient
        chatPromises.push(
          new Chat({
            message: `Hello from ${firstUser.fullname || 'User 1'} to ${recipient.fullname || 'User ' + (i+1)} - message ${msgNum}`,
            sender: firstUser._id,
            recipient: recipient._id,
            senderModel: "user",
            recipientModel: "user",
            status: "sent"
          }).save()
        );
        
        // Messages from recipient back to first user
        chatPromises.push(
          new Chat({
            message: `Reply from ${recipient.fullname || 'User ' + (i+1)} to ${firstUser.fullname || 'User 1'} - message ${msgNum}`,
            sender: recipient._id,
            recipient: firstUser._id,
            senderModel: "user",
            recipientModel: "user",
            status: "sent"
          }).save()
        );
      }
    }
    
    const savedChats = await Promise.all(chatPromises);
    
    res.json({
      success: true,
      message: "Test data created successfully",
      testUsers: testUsers.map(user => ({
        id: user._id,
        name: user.fullname || user.displayName,
        baseUser: user.baseUser,
        email: user.email
      })),
      testChatsCreated: savedChats.length,
      instructions: "Now you can use these user IDs to test the chat functionality"
    });
    
  } catch (error) {
    console.error("Error creating test data:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Quick check for any users in the system
router.get('/list-users', async (req, res) => {
  try {
    const users = await User.find()
      .select('_id fullname email displayName mainImage')
      .limit(20)
      .lean();
    
    const centers = await Center.find()
      .select('_id fullname displayName mainImage')
      .limit(10)
      .lean();
    
    const coaches = await Coach.find()
      .select('_id fullname displayName mainImage')
      .limit(10)
      .lean();
    
    res.json({
      totalUsers: {
        users: await User.countDocuments(),
        centers: await Center.countDocuments(),
        coaches: await Coach.countDocuments()
      },
      users: users.map(user => ({
        id: user._id,
        name: user.fullname || user.displayName,
        email: user.email,
        type: 'user'
      })),
      centers: centers.map(center => ({
        id: center._id,
        name: center.fullname || center.displayName,
        type: 'center'
      })),
      coaches: coaches.map(coach => ({
        id: coach._id,
        name: coach.fullname || coach.displayName,
        type: 'coach'
      }))
    });
  } catch (error) {
    console.error("Error listing users:", error);
    res.status(500).json({ error: error.message });
  }
});

// Create simple chat message between mock center and user without requiring real users
router.get('/create-mock-chat', async (req, res) => {
  try {
    // Generate random ObjectIds
    const mockCenterId = new mongoose.Types.ObjectId();
    const mockUserId = new mongoose.Types.ObjectId();
    
    // Create a chat message from mock center to mock user
    const centerToUserChat = new Chat({
      message: "Hello from mock center to mock user",
      sender: mockCenterId, 
      recipient: mockUserId,
      senderModel: "center",
      recipientModel: "user",
      status: "sent"
    });
    
    // Create a chat message from mock user to mock center
    const userToCenterChat = new Chat({
      message: "Hello from mock user to mock center",
      sender: mockUserId,
      recipient: mockCenterId,
      senderModel: "user", 
      recipientModel: "center",
      status: "sent"
    });
    
    // Save both chats
    const savedChat1 = await centerToUserChat.save();
    const savedChat2 = await userToCenterChat.save();
    
    res.json({
      success: true,
      message: "Mock chat messages created successfully",
      testChat1: {
        id: savedChat1._id,
        sender: savedChat1.sender,
        senderType: savedChat1.senderModel,
        recipient: savedChat1.recipient,
        recipientType: savedChat1.recipientModel,
        message: savedChat1.message,
        status: savedChat1.status
      },
      testChat2: {
        id: savedChat2._id,
        sender: savedChat2.sender,
        senderType: savedChat2.senderModel,
        recipient: savedChat2.recipient,
        recipientType: savedChat2.recipientModel,
        message: savedChat2.message,
        status: savedChat2.status
      },
      instructions: "You can use these IDs to test retrieving chats:",
      userChatEndpoint: `/api/chat/${mockUserId}`,
      centerChatEndpoint: `/api/chat/${mockCenterId}`,
      specificChatEndpoint: `/api/chat/${mockCenterId}/${mockUserId}`
    });
  } catch (error) {
    console.error("Error creating mock chat:", error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router; 