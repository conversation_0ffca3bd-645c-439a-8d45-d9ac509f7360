const express = require("express");
const router = express.Router();

const CampaignController = require("../controllers/campaignController");
const CampaignRepository = require("../repo/campaignRepo");
const CampaignService = require("../services/campaignService");
const CampaignModel = require("../models/campaignModel");

// Import discount dependencies for campaign discount integration
const DiscountRepository = require('../repo/discountRepo');
const DiscountModel = require('../models/discountModel');
const CampaignDiscountService = require('../services/campaignDiscountService');

// Initialize repositories
const campaignRepo = new CampaignRepository(CampaignModel);
const discountRepo = new DiscountRepository(DiscountModel);

// Initialize services
const campaignService = new CampaignService(campaignRepo);
const campaignDiscountService = new CampaignDiscountService(campaignRepo, discountRepo);

// Initialize controller with both services
const campaignController = new CampaignController(campaignService, campaignDiscountService);

// Public routes
router.get("/active", async (req, res) => {
  try {
    await campaignController.getActiveCampaigns(req, res);
  } catch (error) {
    console.error("Error getting active campaigns:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

router.post("/:id/click", async (req, res) => {
  try {
    await campaignController.recordCampaignClick(req, res);
  } catch (error) {
    console.error("Error recording campaign click:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

// Campaign discount routes
router.post("/sync-discounts", async (req, res) => {
  try {
    await campaignController.syncCampaignDiscounts(req, res);
  } catch (error) {
    console.error("Error syncing campaign discounts:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

router.get("/discounts/:userId", async (req, res) => {
  try {
    await campaignController.getAvailableCampaignDiscounts(req, res);
  } catch (error) {
    console.error("Error getting available campaign discounts:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

// Admin routes (these would need authentication middleware in production)
router.post("/", async (req, res) => {
  try {
    await campaignController.createCampaign(req, res);
  } catch (error) {
    console.error("Error creating campaign:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

router.get("/", async (req, res) => {
  try {
    await campaignController.getAllCampaigns(req, res);
  } catch (error) {
    console.error("Error getting all campaigns:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

router.get("/:campaignId", async (req, res) => {
  try {
    await campaignController.getCampaignById(req, res);
  } catch (error) {
    console.error("Error getting campaign by ID:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

router.put("/:id", async (req, res) => {
  try {
    await campaignController.updateCampaign(req, res);
  } catch (error) {
    console.error("Error updating campaign:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

router.delete("/:id", async (req, res) => {
  try {
    await campaignController.deleteCampaign(req, res);
  } catch (error) {
    console.error("Error deleting campaign:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

router.get("/:campaignId/analytics", async (req, res) => {
  try {
    await campaignController.getCampaignAnalytics(req, res);
  } catch (error) {
    console.error("Error getting campaign analytics:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

module.exports = router;