const express = require("express");
const router = express.Router();

const PurchasedHistoryRepo = require("../repo/purchaseHistoryRepo");
const UserRepo = require("../repo/userRepo");
const PurchaseHistoryUseCase = require("../use_case/purchasedHistoryUseCase");
const PurchasedHistoryController = require("../controllers/purchasedHistoryController");

const purchasedHistoryRepo = new PurchasedHistoryRepo();
const userRepo = new UserRepo();
const purchasedHistoryUseCase = new PurchaseHistoryUseCase(
  purchasedHistoryRepo,
  userRepo
);
const purchasedHistoryController = new PurchasedHistoryController(
  purchasedHistoryUseCase
);

router.get("/get/:userId", async (req, res) => {
  try {
    await purchasedHistoryController.get(req,res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
module.exports = router;
