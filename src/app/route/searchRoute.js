const express = require("express");
const router = express.Router();

const SearchRepository = require("../repo/searchRepo");
const SearchService = require("../services/searchService");
const SearchController = require("../controllers/searchController");
const searchRepo = new SearchRepository();
const searchService = new SearchService(searchRepo);
const searchController = new SearchController(searchService);

// General search endpoint
router.get("", async (req, res) => {
    await searchController.search(req, res);
});

// Center-specific filter endpoints
router.get("/centers/filter", async (req, res) => {
    await searchController.filterCenters(req, res);
});

// Class-specific filter endpoints
router.get("/classes/filter", async (req, res) => {
    await searchController.filterClasses(req, res);
});

// Nearby centers endpoint
router.get("/centers/nearby", async (req, res) => {
    await searchController.findNearbyCenters(req, res);
});

module.exports = router;
