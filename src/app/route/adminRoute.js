const express = require("express");
const router = express.Router();
const AdminController = require("../controllers/adminController");

// Initialize admin controller
const adminController = new AdminController();

// Dashboard Statistics
router.get("/dashboard/stats", (req, res) => 
  adminController.getDashboardStats(req, res)
);

// Rearrangement Management
router.get("/rearrangements/pending", (req, res) => 
  adminController.getPendingRearrangements(req, res)
);

router.put("/rearrangements/:eventId/process", (req, res) => 
  adminController.processRearrangement(req, res)
);

router.put("/rearrangements/:eventId/cancel", (req, res) => 
  adminController.cancelRearrangement(req, res)
);

// Refund Management
router.get("/refunds/history", (req, res) => 
  adminController.getRefundHistory(req, res)
);

router.post("/refunds/:eventId/retry", (req, res) => 
  adminController.retryRefund(req, res)
);

// Add route to fix refunds missing parentId
router.post("/refunds/fix-missing-parent", (req, res) => 
  adminController.fixRefundsMissingParentId(req, res)
);

module.exports = router; 