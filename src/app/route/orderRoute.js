// orderRoute.js
const express = require("express");

const router = express.Router();
const {orderController}= require("../../container/container")




// Define your routes here
router.post("/", (req, res) => orderController.create(req, res));
router.get("/:orderId", (req, res) => orderController.getById(req, res));
router.get("/", (req, res) => orderController.getAll(req, res));
router.put("/:orderId", (req, res) => orderController.update(req, res));
router.delete("/:orderId", (req, res) => orderController.delete(req, res));
router.get("/user/:userId", (req, res) => orderController.getByUser(req, res));

// NEW ROUTES: Duplicate booking prevention
router.get("/eligible-children/:userId/:classId", (req, res) => orderController.getEligibleChildren(req, res));
router.get("/can-book/:childId/:classId", (req, res) => orderController.canChildBookClass(req, res));

module.exports = router;
