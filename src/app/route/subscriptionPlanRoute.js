const express = require("express");
const router = express.Router();

const SubscriptionPlanRepo = require("../repo/subscriptionPlanRepo");
const SubscriptionPlanUseCase = require("../use_case/subscriptionPlanUseCase");
const SubscriptionPlanController = require("../controllers/subscriptionPlanController");
const GetSubscriptionUseCase = require("../use_case/getSubscriptionUseCase");
const CreateSubscriptionUseCase = require("../use_case/createSubscriptionUseCase");
const SubscriptionRepo = require("../repo/subscriptionRepo");
const subscriptionRepo = new SubscriptionRepo();
const createSubscription = new CreateSubscriptionUseCase(subscriptionRepo);
const getSubscription = new GetSubscriptionUseCase(subscriptionRepo);

const subscriptionPlanRepo = new SubscriptionPlanRepo();
const subscriptionPlanUseCase = new SubscriptionPlanUseCase(
  subscriptionPlanRepo
);
const subscriptionPlanController = new SubscriptionPlanController(
  subscriptionPlanUseCase,
  createSubscription,
  getSubscription
);

router.post("/new", async (req, res) => {
  try {
    await subscriptionPlanController.createNewPlan(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get("/", async (req, res) => {
  try {
    await subscriptionPlanController.getAllPlans(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
 router.get("/:userId",async(req,res)=>{
  try {
    await subscriptionPlanController.getSubscription(req,res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
 });
// router.get("/:planId", async (req, res) => {
//   try {
//     await subscriptionController.getPlanById(req, res);
//   } catch (error) {
//     res.status(500).json({ error: error.message });
//   }
// });

router.put("/:planId", async (req, res) => {
  try {
    await subscriptionPlanController.updatePlan(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.delete("/:planId", async (req, res) => {
  try {
    await subscriptionPlanController.deletePlan(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
module.exports = router;
