const express = require("express");
const router = express.Router();
const {
  upload,
  childController,
  childUseCase,
} = require("../../container/container");
// Define routes
router.post("/", upload, (req, res) => childController.createChild(req, res));
router.get("/:id", (req, res) => childController.getChildById(req, res));
router.get("/parent/:parentId", (req, res) =>
  childController.getChildByParentId(req, res)
);
router.put("/:id", upload, (req, res) => childController.updateChild(req, res));
router.delete("/:id", (req, res) => childController.deleteChildById(req, res));
// New endpoint for participation stats
router.get("/stats/:childId", (req, res) =>
  childController.getParticipationStats(req, res)
);
// New endpoint for performance metrics
router.get("/metrics/:childId", (req, res) =>
  childController.getPerformanceMetrics(req, res)
);

module.exports = { router, childUseCase };
