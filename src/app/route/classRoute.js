const express = require("express");
const router = express.Router();
const upload = require("../../config/multer");
const TokenService = require("../services/tokenService");
const ClassRepo = require("../repo/classRepo");
const ClassDateRepo = require("../repo/classDateRepo");
const EventRepository = require("../repo/eventRepo");
const ImageService = require("../services/imageService");
const CreateEventsFromClass = require("../use_case/createEventFromClass");
const ClassController = require("../controllers/classController");
const UploadFile = require("../services/uploadFile");
const tokenService = new TokenService();
const imageService = new ImageService();
const classRepo = new ClassRepo();
const classDateRepo = new ClassDateRepo();
const eventRepository = new EventRepository();
const createEventsFromClassUseCase = new CreateEventsFromClass(eventRepository);
const uploadFile = new UploadFile(imageService);
const { classUseCase } = require("../../container/container");

const classController = new ClassController({ classUseCase });

router.post("/addClass", upload, async (req, res) => {
  try {
    await classController.addClass(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get("/check-minimum-students/:classId/:dateId", async (req, res) => {
  try {
    await classController.checkMinimumStudents(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.post("/cancel-insufficient-students/:classId/:dateId", async (req, res) => {
  try {
    await classController.cancelClassForInsufficientStudents(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get("/", async (req, res) => {
  try {
    await classController.getAllClasses(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Specific routes should come before generic parameter routes
router.get("/center/:id", async (req, res) =>
  classController.getClassesByCenter(req, res)
);

// New endpoint for parents to view center classes
router.get("/parent/center/:centerId", async (req, res) =>
  classController.getClassesForParent(req, res)
);

router.get("/coach/:id", async (req, res) =>
  classController.getClassesByCoach(req, res)
);

// Student routes - these need to come before the generic /:id route
router.get("/:slotId/students", async (req, res) => {
  try {
    console.log('🎯 STUDENTS ROUTE HIT - Getting students for slot:', req.params.slotId);
    console.log('🎯 Full URL:', req.originalUrl);
    await classController.getStudents(req, res);
  } catch (error) {
    console.log('❌ Error in students route:', error.message);
    res.status(500).json({ error: error.message });
  }
});

// Generic class by ID route - this should come last among GET routes
router.get("/:id", async (req, res) => {
  try {
    console.log('🔍 GENERIC CLASS ROUTE HIT - Getting class by ID:', req.params.id);
    console.log('🔍 Full URL:', req.originalUrl);
    await classController.getClass(req, res);
  } catch (error) {
    console.log('❌ Error in generic class route:', error.message);
    res.status(500).json({ error: error.message });
  }
});


router.put("/:id", upload, async (req, res) => {
  try {
    await classController.updateClass(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.delete("/:id", async (req, res) => {
  try {
    await classController.deleteClass(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get("/:classId/coach-center", async (req, res) => {
  try {
    await classController.getCoachCenter(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.put("/:classId/coach", async (req, res) => {
  try {
    await classController.updateCoach(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route GET /:classId/slots
 * @desc Get all slots (dates) for a given class by classId
 * @access Public
 */
router.get("/:classId/slots", async (req, res) => {
  try {
    await classController.getClassSlots(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route DELETE /slot/:slotId
 * @desc Delete a slot (class date) by slotId
 * @access Public
 */
router.delete("/slot/:slotId", async (req, res) => {
  await classController.deleteSlotById(req, res);
});

module.exports = { router, classUseCase };
