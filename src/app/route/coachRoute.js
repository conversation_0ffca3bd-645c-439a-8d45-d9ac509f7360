const express = require("express");
const router = express.Router();
const {
  coachController,
  authMiddleware,
  coachUseCase,
  upload,
} = require("../../container/container");
router.post(
  "/create",
  authMiddleware.authenticate.bind(authMiddleware),
  upload,
  (req, res) => coachController.createCoach(req, res)
);
router.put(
  "/:id",
  authMiddleware.authenticate.bind(authMiddleware),
  upload,
  async (req, res) => {
    console.log("you hit it");
    coachController.updateCoach(req, res);
  }
);
router.get("/:id", (req, res) => coachController.getCoachById(req, res));
router.get;
router.delete("/:id", (req, res) => coachController.deleteCoachById(req, res));

router.get("/", (req, res) => coachController.getAllCoach(req, res));

router.post("/assign", async (req, res) => {
  try {
    console.log("assign");
    await coachController.assignCoach(req, res);
  } catch (error) {
    res.status(500).json({ error: "Something went wrong" });
  }
});
router.post("/requestCenter", async (req, res) => {
  try {
    await coachController.requestCenter(req, res);
  } catch (error) {
    res.status(500).json({ error: "Something went wrong" });
  }
});
router.post("/remove", async (req, res) => {
  try {
    console.log('remove');
    await coachController.removeCoach(req, res);
  } catch (error) {
    res.status(500).json({ error: "Something went wrong" });
  }
});

router.post("/assignManager", async (req, res) => {
  try {
    console.log("hi");
    await coachController.assignManager(req, res);
  } catch (error) {
    res.status(500).json({ error: "Something went wrong" });
  }
});
router.delete("/deleteNotification/:id", async (req, res) => {
  try {
    await coachController.deleteNotification(req, res);
  } catch (error) {
    res.status(500).json({ error: "Something went wrong" });
  }
});
router.get("/program/:id", async (req, res) => {
  try {
    await coachController.getProgramByCoachId(req, res);
  } catch (error) {
    res.status(500).json({ error: "Something went wrong" });
  }
});
module.exports = { router, coachUseCase };
