const express = require("express");
const router = express.Router();
const container = require("../../container/container");
const RefundController = require("../controllers/refundController");

const refundController = new RefundController(container.refundService);

// GET /parent/:parentId - Get all refunds for a parent
router.get("/parent/:parentId", (req, res) => refundController.getRefundsByParentId(req, res));

module.exports = router; 