const express = require("express");
const router = express.Router();
const { addressController } = require("../../container/container");

// Get all addresses for a user
router.get("/user/:userId", (req, res) =>
  addressController.getUserAddresses(req, res)
);

// Get default address for a user
router.get("/user/:userId/default", (req, res) =>
  addressController.getDefaultAddress(req, res)
);

// Add a new address
router.post("/user/:userId", (req, res) =>
  addressController.addAddress(req, res)
);

// Update an address
router.put("/user/:userId/:addressId", (req, res) =>
  addressController.updateAddress(req, res)
);

// Delete an address
router.delete("/user/:userId/:addressId", (req, res) =>
  addressController.deleteAddress(req, res)
);

// Set an address as default
router.put("/user/:userId/:addressId/default", (req, res) =>
  addressController.setDefaultAddress(req, res)
);

// Get address with map data
router.get("/user/:userId/map", (req, res) =>
  addressController.getAddressWithMap(req, res)
);

// Get specific address with map data
router.get("/user/:userId/:addressId/map", (req, res) =>
  addressController.getAddressWithMap(req, res)
);

module.exports = router;
