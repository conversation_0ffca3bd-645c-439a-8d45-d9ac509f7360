const express = require("express");
const router = express.Router();
const { requestController } = require("../../container/container");

router.post("/coach-to-center", async (req, res) => {
  requestController.sendJoinRequest(req, res);
});
router.patch("/:requestId/status", async (req, res) => {
  requestController.updateRequestStatus(req, res);
});
router.get("/center/:centerId", async (req, res) => {
  requestController.getRequestsByCenter(req, res);
});
router.get("/coach/:coachId", async (req, res) => {
  requestController.getRequestsByCoach(req, res);
});
router.delete("/:requestId", async (req, res) => {
  requestController.deleteRequest(req, res);
});

// Check if a request exists between coach and center
router.get("/check/:coachId/:centerId", async (req, res) => {
  requestController.checkExistingRequest(req, res);
});

// Cancel join request by coachId and centerId
router.delete("/coach-to-center/:coachId/:centerId", async (req, res) => {
  requestController.cancelJoinRequestByCoachAndCenter(req, res);
});

module.exports = router;
