const express = require("express");
const router = express.Router();

const BalanceRepo = require("../repo/balanceRepo");
const UserRepo = require("../repo/userRepo");
const BalanceUseCase = require("../use_case/balanceUseCase");
const BalanceController = require("../controllers/balanceController");

const balanceRepo = new BalanceRepo();
const userRepo = new UserRepo();
const balanceUseCase = new BalanceUseCase(balanceRepo, userRepo);
const balanceController = new BalanceController(balanceUseCase);

router.post("/", (req, res) => balanceController.createBalance(req, res));
router.get("/:userId", (req, res) => balanceController.getBalance(req, res));
router.put("/:userId", (req, res) => balanceController.updateBalance(req, res));
router.post("/add/:userId", (req, res) => balanceController.addBalance(req, res));

module.exports = { router, balanceUseCase };
