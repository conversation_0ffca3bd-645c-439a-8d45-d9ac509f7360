const express = require("express");
const router = express.Router();

const NotificationRepository = require("../repo/notificationRepo");
const NotificationService = require("../services/notificationService");
const NotificationController = require("../controllers/notificationController");

const notificationRepository = new NotificationRepository();
const notificationService = new NotificationService(notificationRepository);
const notificationController = new NotificationController(notificationService);

router.post("/token", async (req, res) => {
  notificationController.saveOrUpdateToken(req, res);
});

router.get("", async (req, res) => {
  notificationController.getTokenById(req, res);
});
router.get("/get", async (req, res) => {
  notificationController.getNotification(req, res);
});
router.delete("/:id", async (req, res) => {
  notificationController.deleteNotification(req, res);
});
module.exports = router;
