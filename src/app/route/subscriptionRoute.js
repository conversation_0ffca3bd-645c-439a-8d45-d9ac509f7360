const express = require("express");
const router = express.Router();

const SubscriptionController = require("../controllers/subscriptionController");
const GetSubscriptionUseCase = require("../use_case/getSubscriptionUseCase");
const CreateSubscriptionUseCase = require("../use_case/createSubscriptionUseCase");
const CancelSubscriptionUseCase = require("../use_case/cancelSubscriptionUseCase");
const SubscriptionRepo = require("../repo/subscriptionRepo");

const subscriptionRepo = new SubscriptionRepo();

const createSubscription = new CreateSubscriptionUseCase(subscriptionRepo);
const getSubscription = new GetSubscriptionUseCase(subscriptionRepo);
const cancelSubscription = new CancelSubscriptionUseCase(subscriptionRepo);
const subscriptionController = new SubscriptionController(
  createSubscription,
  getSubscription,
  cancelSubscription
);

router.post("/", async (req, res) => {
  try {
    subscriptionController.createSubscription(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get("/:userId", async (req, res) => {
  try {
    subscriptionController.getSubscription(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.delete("/:userId", async (req, res) => {
  try {
    subscriptionController.cancelSubscription(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
