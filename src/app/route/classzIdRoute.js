const express = require("express");
const router = express.Router();
const { generateEntityId } = require("../use_case/classzIdGenerate");

// Import models
const BaseUser = require("../models/baseUserModel");
const User = require("../models/userModel");
const BusinessOwner = require("../models/businessOwnerModel");
const Center = require("../models/centerModel");
const Coach = require("../models/coachModel");

router.get("/check-missing-ids", async (req, res) => {
  try {
    // Find all base users
    const baseUsers = await BaseUser.find({});
    
    const missingIds = {
      parent: [],
      owner: [],
      coach: [],
      center: []
    };

    const totals = {
      baseUsers: baseUsers.length,
      parent: 0,
      owner: 0,
      coach: 0,
      center: 0
    };

    // Process each base user
    for (const baseUser of baseUsers) {
      const roles = baseUser.roles;
      
      for (const role of roles) {
        let entity;
        
        switch(role) {
          case 'parent':
            entity = await User.findOne({ baseUser: baseUser._id });
            totals.parent++;
            if (!entity?.classzId) {
              missingIds.parent.push({
                baseUserId: baseUser._id,
                email: baseUser.email,
                entityId: entity?._id
              });
            }
            break;
            
          case 'owner':
            entity = await BusinessOwner.findOne({ baseUser: baseUser._id });
            totals.owner++;
            if (!entity?.classzId) {
              missingIds.owner.push({
                baseUserId: baseUser._id,
                email: baseUser.email,
                entityId: entity?._id
              });
            }
            break;
            
          case 'coach':
            entity = await Coach.findOne({ baseUser: baseUser._id });
            totals.coach++;
            if (!entity?.classzId) {
              missingIds.coach.push({
                baseUserId: baseUser._id,
                email: baseUser.email,
                entityId: entity?._id
              });
            }
            break;
            
          case 'center':
            entity = await Center.findOne({ baseUser: baseUser._id });
            totals.center++;
            if (!entity?.classzId) {
              missingIds.center.push({
                baseUserId: baseUser._id,
                email: baseUser.email,
                entityId: entity?._id,
                ownerId: entity?.owner
              });
            }
            break;
        }
      }
    }
    
    res.status(200).json({
      totals,
      missingIds,
      summary: {
        totalBaseUsers: baseUsers.length,
        totalMissing: Object.values(missingIds).reduce((acc, curr) => acc + curr.length, 0),
        missingByType: {
          parent: missingIds.parent.length,
          owner: missingIds.owner.length,
          coach: missingIds.coach.length,
          center: missingIds.center.length
        }
      }
    });
    
  } catch (error) {
    console.error("Error checking classz IDs:", error);
    res.status(500).json({
      message: "Error checking classz IDs",
      error: error.message
    });
  }
});

router.post("/generate-missing-ids", async (req, res) => {
  try {
    // Find all base users
    const baseUsers = await BaseUser.find({});
    
    const updatedIds = {
      parent: [],
      owner: [],
      coach: [],
      center: []
    };

    const failed = {
      parent: [],
      owner: [],
      coach: [],
      center: []
    };
    
    // Process each base user
    for (const baseUser of baseUsers) {
      // Get all roles for this user
      const roles = baseUser.roles;
      
      for (const role of roles) {
        let entity;
        let classzId;
        
        try {
          switch(role) {
            case 'parent':
              entity = await User.findOne({ baseUser: baseUser._id });
              if (entity && !entity.classzId) {
                classzId = generateEntityId('parent');
                await User.findByIdAndUpdate(entity._id, { classzId });
                updatedIds.parent.push({ id: entity._id, classzId });
              }
              break;
              
            case 'owner':
              entity = await BusinessOwner.findOne({ baseUser: baseUser._id });
              if (entity && !entity.classzId) {
                classzId = generateEntityId('business');
                await BusinessOwner.findByIdAndUpdate(entity._id, { classzId });
                updatedIds.owner.push({ id: entity._id, classzId });
              }
              break;
              
            case 'coach':
              entity = await Coach.findOne({ baseUser: baseUser._id });
              if (entity && !entity.classzId) {
                classzId = generateEntityId('coach');
                await Coach.findByIdAndUpdate(entity._id, { classzId });
                updatedIds.coach.push({ id: entity._id, classzId });
              }
              break;
              
            case 'center':
              entity = await Center.findOne({ baseUser: baseUser._id });
              if (entity && !entity.classzId) {
                // For center, we need to find its owner first
                const owner = await BusinessOwner.findById(entity.owner);
                if (owner && owner.classzId) {
                  // Get count of existing centers for this owner
                  const centerCount = await Center.countDocuments({ owner: owner._id });
                  classzId = generateEntityId('center', {
                    businessId: owner.classzId,
                    centreIndex: centerCount
                  });
                  await Center.findByIdAndUpdate(entity._id, { classzId });
                  updatedIds.center.push({ id: entity._id, classzId });
                } else {
                  failed.center.push({
                    id: entity._id,
                    reason: "Owner not found or missing classzId",
                    ownerId: entity.owner
                  });
                }
              }
              break;
          }
        } catch (error) {
          failed[role].push({
            baseUserId: baseUser._id,
            email: baseUser.email,
            error: error.message
          });
        }
      }
    }
    
    // Return summary of updated entities
    const summary = {
      updated: {
        parents: updatedIds.parent.length,
        owners: updatedIds.owner.length,
        centers: updatedIds.center.length,
        coaches: updatedIds.coach.length
      },
      failed: {
        parents: failed.parent.length,
        owners: failed.owner.length,
        centers: failed.center.length,
        coaches: failed.coach.length
      },
      total: {
        parents: await User.countDocuments({ classzId: { $exists: true, $ne: "" } }),
        owners: await BusinessOwner.countDocuments({ classzId: { $exists: true, $ne: "" } }),
        centers: await Center.countDocuments({ classzId: { $exists: true, $ne: "" } }),
        coaches: await Coach.countDocuments({ classzId: { $exists: true, $ne: "" } })
      }
    };
    
    res.status(200).json({
      message: "Completed generating missing classz IDs",
      summary,
      details: {
        updatedIds,
        failed
      }
    });
    
  } catch (error) {
    console.error("Error generating classz IDs:", error);
    res.status(500).json({
      message: "Error generating classz IDs",
      error: error.message
    });
  }
});

router.post("/update-all-entities", async (req, res) => {
  try {
    const results = {
      updated: { parents: [], owners: [], centers: [], coaches: [] },
      failed: { parents: [], owners: [], centers: [], coaches: [] },
      totals: { parents: 0, owners: 0, centers: 0, coaches: 0 }
    };

    // 1. Update all parents (users)
    const allParents = await User.find({});
    results.totals.parents = allParents.length;
    
    for (const parent of allParents) {
      try {
        if (!parent.classzId) {
          const classzId = generateEntityId('parent');
          await User.findByIdAndUpdate(parent._id, { classzId });
          results.updated.parents.push({ id: parent._id, email: parent.email, classzId });
        }
      } catch (error) {
        results.failed.parents.push({ id: parent._id, email: parent.email, error: error.message });
      }
    }

    // 2. Update all business owners
    const allOwners = await BusinessOwner.find({});
    results.totals.owners = allOwners.length;
    
    for (const owner of allOwners) {
      try {
        if (!owner.classzId) {
          const classzId = generateEntityId('business');
          await BusinessOwner.findByIdAndUpdate(owner._id, { classzId });
          results.updated.owners.push({ id: owner._id, email: owner.email, classzId });
        }
      } catch (error) {
        results.failed.owners.push({ id: owner._id, email: owner.email, error: error.message });
      }
    }

    // 3. Update all coaches
    const allCoaches = await Coach.find({});
    results.totals.coaches = allCoaches.length;
    
    for (const coach of allCoaches) {
      try {
        if (!coach.classzId) {
          const classzId = generateEntityId('coach');
          await Coach.findByIdAndUpdate(coach._id, { classzId });
          results.updated.coaches.push({ id: coach._id, email: coach.email, classzId });
        }
      } catch (error) {
        results.failed.coaches.push({ id: coach._id, email: coach.email, error: error.message });
      }
    }

    // 4. Update all centers (needs to be done after owners to ensure owner IDs exist)
    const allCenters = await Center.find({});
    results.totals.centers = allCenters.length;
    
    for (const center of allCenters) {
      try {
        if (!center.classzId) {
          const owner = await BusinessOwner.findById(center.owner);
          if (!owner || !owner.classzId) {
            throw new Error(`Owner not found or missing classzId for center ${center._id}`);
          }
          
          // Get count of existing centers for this owner
          const centerCount = await Center.countDocuments({ 
            owner: owner._id,
            _id: { $lt: center._id } // Count only centers created before this one
          });
          
          const classzId = generateEntityId('center', {
            businessId: owner.classzId,
            centreIndex: centerCount
          });
          
          await Center.findByIdAndUpdate(center._id, { classzId });
          results.updated.centers.push({ 
            id: center._id, 
            email: center.email, 
            classzId,
            ownerId: owner._id,
            ownerClasszId: owner.classzId 
          });
        }
      } catch (error) {
        results.failed.centers.push({ 
          id: center._id, 
          email: center.email, 
          ownerId: center.owner,
          error: error.message 
        });
      }
    }

    // Prepare summary
    const summary = {
      totalEntities: {
        parents: results.totals.parents,
        owners: results.totals.owners,
        centers: results.totals.centers,
        coaches: results.totals.coaches
      },
      updated: {
        parents: results.updated.parents.length,
        owners: results.updated.owners.length,
        centers: results.updated.centers.length,
        coaches: results.updated.coaches.length
      },
      failed: {
        parents: results.failed.parents.length,
        owners: results.failed.owners.length,
        centers: results.failed.centers.length,
        coaches: results.failed.coaches.length
      }
    };

    res.status(200).json({
      message: "Completed updating all entities",
      summary,
      details: results
    });

  } catch (error) {
    console.error("Error updating entities:", error);
    res.status(500).json({
      message: "Error updating entities",
      error: error.message
    });
  }
});

module.exports = router; 