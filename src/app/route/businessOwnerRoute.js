const express = require("express");
const router = express.Router();

const BusinessOwnerRepository = require("../repo/businessOwnerRepo");
const BusinessOwnerService = require("../services/businessOwnerService");
const BusinessOwnerController = require("../controllers/businessOwnerController");
const CenterRepository = require("../repo/centerRepo");
const CoachRepository = require("../repo/coachRepo");
const OrderRepository = require("../repo/orderRepo");
const ClassRepository = require("../repo/classRepo");
const TokenService = require("../services/tokenService");
const AuthMiddleware = require("../services/authMiddleware");
const upload = require("../../config/multer");

const centerRepository = new CenterRepository();
const coachRepository = new CoachRepository();
const orderRepository = new OrderRepository();
const classRepository = new ClassRepository();
const tokenService = new TokenService();
const authMiddleware = new AuthMiddleware(tokenService);
const businessOwnerRepository = new BusinessOwnerRepository();
const businessOwnerService = new BusinessOwnerService(
  businessOwnerRepository,
  centerRepository,
  coachRepository,
  orderRepository,
  classRepository
);
const businessOwnerController = new BusinessOwnerController(
  businessOwnerService
);

// Apply authentication middleware to all routes below
router.use((req, res, next) => authMiddleware.authenticate(req, res, next));

router.put(
  "/:id",
  upload, // Handle file upload, assuming the form field name is 'fileField'
  async (req, res) => {
    try {
      await businessOwnerController.updateOwner(req, res); // Controller to handle update
    } catch (error) {
      console.error("Error updating business owner:", error.message);
      res.status(500).json({ error: "Internal Server Error" });
    }
  }
);
router.get("/:id", async (req, res) => {
  try {
    await businessOwnerController.getOwnerById(req, res); // Controller to handle update
  } catch (error) {
    res.status(500).json({ error: "Internal Server Error" });
  }
});
router.get("/branch/:id", async (req, res) => {
  try {
    await businessOwnerController.getBranchByOwner(req, res);
  } catch (error) {
    res.status(500).json({ error: "Something went wrong" });
  }
});

router.delete("/branch/:id", async (req, res) => {
  try {
    await businessOwnerController.deleteBranch(req, res);
  } catch (error) {
    res.status(500).json({ error: "Something went wrong" });
  }
});
router.put("/branch/:id", async (req, res) => {
  try {
    await businessOwnerController.updateBranch(req, res);
  } catch (error) {
    res.status(500).json({ error: "Something went wrong" });
  }
});

// Get owner dashboard data
router.get("/:id/dashboard", async (req, res) => {
  try {
    await businessOwnerController.getOwnerDashboard(req, res);
  } catch (error) {
    console.error('Error in owner dashboard route:', error);
    res.status(500).json({ error: "Failed to fetch dashboard data" });
  }
});

router.post("/assignCoach/:ownerId/:coachId", async (req, res) => {
  try {
    await businessOwnerController.assignCoach(req, res);
  } catch (error) {
    res.status(500).json({ error: "Something went wrong" });
  }
});
module.exports = router;
