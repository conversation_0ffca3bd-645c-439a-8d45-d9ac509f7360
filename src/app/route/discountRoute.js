const express = require("express");
const router = express.Router();

const DiscountController = require("../controllers/discountController");
const DiscountRepository = require("../repo/discountRepo");
const DiscountService = require("../services/discountService");
const DiscountModel = require("../models/discountModel");

// Initialize instances
const discountRepo = new DiscountRepository(DiscountModel);
const discountService = new DiscountService(discountRepo);
const discountController = new DiscountController(discountService);

router.post("/", async (req, res) => {
  try {
    await discountController.create(req, res);
  } catch (error) {
    console.error("Error creating discount:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

router.get("/", async (req, res) => {
  try {
    await discountController.getAll(req, res);
  } catch (error) {
    console.error("Error fetching discounts:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

router.get("/:code", async (req, res) => {
  try {
    await discountController.getByCode(req, res);
  } catch (error) {
    console.error("Error fetching discount by code:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

router.delete("/:code", async (req, res) => {
  try {
    await discountController.delete(req, res);
  } catch (error) {
    console.error("Error deleting discount:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

router.get("/user/:userId", async (req, res) => {
  try {
    console.log("HI")
    await discountController.getById(req, res);
  } catch (error) {
    console.error("Error deleting discount:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});
module.exports = router;

module.exports = router;
