const express = require("express");
const router = express.Router();
const { attendanceController } = require("../../container/container");
router.post("/generate", async (req, res) => {
  console.log("Route: /generate hit");

  try {
    await attendanceController.generateCode(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.post("/mark", async (req, res) => {
  console.log("Route: /mark hit");
  try {
    await attendanceController.markAttendance(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get("/:classId", async (req, res) => {
  console.log("Route: /:classId hit");
  try {
    await attendanceController.getClassAttendance(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
router.get("/present/:classId/:classDate", async (req, res) => {
  console.log("Route: /:present hit");
  try {
    await attendanceController.getPresentAttendance(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get("/qr", async (req, res) => {
  try {
    await attendanceController.getExistingQRCode(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
router.post("/verify", async (req, res) => {
  console.log("Route: /verify hit");
  try {
    await attendanceController.verifyAttendance(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get("/child/:childId", async (req, res) => {
  try {
    await attendanceController.getHistoryBychildId(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
module.exports = router;
