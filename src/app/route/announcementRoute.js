const express = require("express");
const router = express.Router();
const {announcementController}=require("../../container/container")

router.post("/", async (req, res) => {
  try {
    await announcementController.save(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
router.get("/:announcementId", async (req, res) => {
  try {
    await announcementController.getAnnouncementByAnnouncementId(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
router.get("/class/:classId", async (req, res) => {
  try {
    await announcementController.getAnnouncementByClassId(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
router.get("/slot/:slotId", async (req, res) => {
  try {
    console.log('in slot');
    await announcementController.getAnnouncementBySlotId(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
