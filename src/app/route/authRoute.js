const express = require("express");
const router = express.Router();
const multer = require("multer");
const AuthController = require("../controllers/authController");
const SignUpUser = require("../use_case/signUpUser");
const SignInUser = require("../use_case/signInUser");
const UpdateUser = require("../use_case/updateUser");
const UpdateCenter = require("../use_case/updateCenter");
const ValidateToken = require("../use_case/validateToken");
const UserRepository = require("../repo/userRepo");
const BaseUserRepository = require("../repo/baseUserRepo");
const CenterRepository = require("../repo/centerRepo");
const ClassRepo = require("../repo/classRepo");
const HashService = require("../services/hashService");
const TokenService = require("../services/tokenService");
const AuthMiddleware = require("../services/authMiddleware");
const FileUploadService = require("../services/fileUploadService");
const ImageService = require("../services/imageService");
const CenterUseCase = require("../use_case/centerUseCase");
const upload = require("../../config/multer");
const CoachRepository = require("../repo/coachRepo");
const BusinessOwnerRepository = require("../repo/businessOwnerRepo");
const RoleService = require("../services/roleService");
const UploadFile = require("../services/uploadFile");
const EmailService = require("../services/emailService");
const OtpService = require("../services/otpService");
const OtpRepo = require("../repo/otpRepo");
const otpRepo = new OtpRepo();
const emailService = new EmailService();


// Initialize dependencies
const userRepository = new UserRepository();
const baseUserRepository = new BaseUserRepository();
const businessOwnerRepository = new BusinessOwnerRepository();
const centerRepository = new CenterRepository();
const classRepository = new ClassRepo();
const coachRepository = new CoachRepository();
const hashService = new HashService();
const tokenService = new TokenService();
const authMiddleware = new AuthMiddleware(tokenService);
const uploadFile = new UploadFile();
const otpService = new OtpService(otpRepo, emailService, baseUserRepository);
const validateToken = new ValidateToken({
  tokenService,
  baseUserRepository,
  userRepository,
});
const imageService = new ImageService();
const fileUploadService = new FileUploadService();
const roleService = new RoleService({
  userRepository,
  businessOwnerRepository,
  coachRepository,
  centerRepository,
});
const signUpUser = new SignUpUser({
  baseUserRepository,
  hashService,
  tokenService,
  roleService,
  otpService,
});
const signInUser = new SignInUser({
  baseUserRepository,
  hashService,
  tokenService,
  roleService,
});
const updateUser = new UpdateUser({
  baseUserRepository,
  userRepository,
  tokenService,
  uploadFile,
  hashService,
});
const updateCenter = new UpdateCenter({
  centerRepository,
  baseUserRepository,
  tokenService,
  imageService,
  classRepository,
});
const centerUseCase = new CenterUseCase({
  centerRepository,
  baseUserRepository,
  tokenService,
  imageService,
});
const authController = new AuthController({
  signUpUser,
  signInUser,
  updateUser,
  updateCenter,
  validateToken,
  tokenService,
  authMiddleware,
  fileUploadService,
  centerUseCase,
});

// Define routes
router.post("/signup", (req, res) => authController.signUp(req, res));
router.post("/signin", (req, res) => authController.signIn(req, res));
router.put("/newpassword", (req, res) => authController.newPassword(req, res));
router.put(
  "/update/:id",
  authMiddleware.authenticate.bind(authMiddleware),
  upload,
  (req, res) => authController.update(req, res)
);

router.post(
  "/istokenvalid",
  authMiddleware.authenticate.bind(authMiddleware),
  (req, res) => authController.isTokenValid(req, res)
);
router.post(
  "/user",
  authMiddleware.authenticate.bind(authMiddleware),
  (req, res) => authController.getUser(req, res)
);
router.get(
  "/center",
  //  authMiddleware.authenticate.bind(authMiddleware),
  (req, res) => authController.getAllCenters(req, res)
);
router.get("/getcenter/:id", (req, res) =>
  authController.getCenterById(req, res)
);
router.delete("/deleteAddress/:id/:addressId", (req, res) =>
  authController.deleteParentAddress(req, res)
);
router.put("/defaultAddress/:id/:addressId", (req, res) =>
  authController.defaultParentAddress(req, res)
);
// Error handling middleware
router.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: err.message });
});

module.exports = router;
