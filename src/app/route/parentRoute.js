const express = require("express");
const router = express.Router();

// Import controllers
const ParentController = require("../controllers/parentController");

// Import repositories
const SavedCenterRepository = require("../repo/savedCenterRepo");
const UserRepository = require("../repo/userRepo");
const CenterRepository = require("../repo/centerRepo");
const EmailService = require("../services/emailService");
// Import services
const SavedCenterService = require("../services/parentService");

// Import middleware
const { authenticateJWT } = require("../middleware/authMiddleware");
const emailService = new EmailService();
// Create instances
const savedCenterRepository = new SavedCenterRepository();
const userRepository = new UserRepository();
const centerRepository = new CenterRepository();
const savedCenterService = new SavedCenterService(
  savedCenterRepository,
  userRepository,
  centerRepository,
  emailService
);
const parentController = new ParentController(savedCenterService);

// Saved centers routes
router.post("/save-center", authenticateJWT, (req, res) =>
  parentController.saveCenter(req, res)
);

router.post("/unsave-center", authenticateJWT, (req, res) =>
  parentController.unsaveCenter(req, res)
);

router.get("/saved-centers/:parentId", authenticateJWT, (req, res) =>
  parentController.getSavedCenters(req, res)
);

router.get(
  "/is-center-saved/:parentId/:centerId",
  authenticateJWT,
  (req, res) => parentController.isCenterSaved(req, res)
);

// BATCH CHECK: Check multiple centers at once (NEW EFFICIENT ENDPOINT)
router.post(
  "/batch-check-saved/:parentId",
  authenticateJWT,
  (req, res) => parentController.batchCheckSavedCenters(req, res)
);

router.post("/contact-us", (req, res) => {
  parentController.contactUs(req, res);
});
module.exports = router;
