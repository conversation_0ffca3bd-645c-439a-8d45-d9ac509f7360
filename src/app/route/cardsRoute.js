const express = require("express");
const router = express.Router();
const UserRepository = require("../repo/userRepo");
const PaymentService = require("../services/paymentService");
const PaymentController = require("../controllers/paymentController");
const GetSavedCardUseCase = require("../use_case/getsavedCardUseCase");
const DeleteCardUseCase = require("../use_case/deleteCardUseCase");
const AuthMiddleware = require("../services/authMiddleware");
const TokenService = require("../services/tokenService");

const userRepository = new UserRepository();
const paymentService = new PaymentService();
const tokenService = new TokenService();
const authMiddleware = new AuthMiddleware(tokenService);

const getSavedCardUseCase = new GetSavedCardUseCase(
  userRepository,
  paymentService
);
const deleteCardUseCase = new DeleteCardUseCase(userRepository, paymentService);

const paymentController = new PaymentController(
  null, // saveCardUseCase not needed for these endpoints
  getSavedCardUseCase,
  null, // createCardTokenUseCase not needed
  null, // purchaseUseCase not needed
  null, // getPaymentMethodUseCase not needed
  null, // payWithSavedCardUseCase not needed
  deleteCardUseCase
);

// Apply authentication middleware to all routes
router.use((req, res, next) => authMiddleware.authenticate(req, res, next));

// GET /api/cards/ - Get user's saved card
router.get("/", async (req, res) => {
  try {
    // The auth middleware adds baseUserId to req.user
    const baseUserId = req.user;
    console.log("Invalid ID format:", baseUserId);
    
    // Find the parent user using the baseUser ID
    const parentUser = await userRepository.findByBaseUserId(baseUserId);
    if (!parentUser) {
      return res.status(400).json({ message: "User not found." });
    }
    
    req.params.userId = parentUser._id.toString();
    paymentController.getSavedCard(req, res);
  } catch (error) {
    console.error("Error in GET /api/cards/:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// DELETE /api/cards/ - Delete user's saved card
router.delete("/", async (req, res) => {
  try {
    // The auth middleware adds baseUserId to req.user
    const baseUserId = req.user;
    
    // Find the parent user using the baseUser ID
    const parentUser = await userRepository.findByBaseUserId(baseUserId);
    if (!parentUser) {
      return res.status(400).json({ message: "User not found." });
    }
    
    req.params.userId = parentUser._id.toString();
    paymentController.deleteCard(req, res);
  } catch (error) {
    console.error("Error in DELETE /api/cards/:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

module.exports = router; 