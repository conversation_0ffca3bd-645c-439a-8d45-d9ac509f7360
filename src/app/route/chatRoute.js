const express = require("express");
const router = express.Router();
const compression = require('compression');

const ChatRepository = require("../repo/chatRepo");
const ChatService = require("../services/chatService");
const ChatController = require("../controllers/chatController");

const chatRepository = new ChatRepository();
const chatService = new ChatService(chatRepository);
const chatController = new ChatController(chatService);

// Apply compression to all chat routes
router.use(compression({
  // Compress everything over 500 bytes
  threshold: 500,
  // Don't compress responses with this header
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  }
}));

// Get conversations for a user
router.get("/conversations/:userId([0-9a-fA-F]{24})", async (req, res) => {
  try {
    console.log("Getting conversations");
    
    // Add cache control headers
    // Allow caching for 5 minutes (300 seconds)
    res.set('Cache-Control', 'public, max-age=300');
    
    // Add ETag support for efficient caching
    res.set('ETag', `"conversations-${req.params.userId}-${Date.now()}"`);
    
    // Add performance timing header
    res.set('X-Response-Time', '0');
    const startTime = process.hrtime();
    
    // Add response handler to track performance
    const originalSend = res.send;
    res.send = function(body) {
      const diff = process.hrtime(startTime);
      const time = diff[0] * 1e3 + diff[1] * 1e-6;
      res.set('X-Response-Time', `${time.toFixed(2)}ms`);
      return originalSend.call(this, body);
    };
    
    chatController.getUserConversations(req, res);
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      message: error.message || "Failed to get conversations" 
    });
  }
});

router.get("/:senderId/:recipientId", async (req, res) => {
  try {
    // Add cache control headers for chats
    // Allow caching for 1 minute (60 seconds)
    res.set('Cache-Control', 'public, max-age=60');
    
    chatController.getChats(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.get("/:userId([0-9a-fA-F]{24})", async (req, res) => {
  try {    
    console.log("Getting chats");
    
    // Add cache control headers
    res.set('Cache-Control', 'public, max-age=60');
    
    chatController.getChatByUserId(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

router.post("/", async (req, res) => {
  try {
    // No caching for POST requests
    res.set('Cache-Control', 'no-store');
    
    chatController.createChats(req, res);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
