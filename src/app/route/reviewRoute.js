const express = require("express");
const router = express.Router();
const { reviewController, upload } = require("../../container/container");

// Submit a review
router.post("/", upload, (req, res) => {
  console.log("POST request to create a review");
  reviewController.createReview(req, res);
});
router.post("/parent", upload, (req, res) => {
  console.log("POST request to create a review");
  reviewController.postReviewByParent(req, res);
});

// Get reviews for a specific reviewee
router.get("/:revieweeId/:revieweeType", (req, res) => {
  console.log("GET request for reviews by reviewee:", req.params);
  reviewController.getReviews(req, res);
});

// Get reviews by a specific reviewer
router.get("/reviewer/:reviewerId/:reviewerType", (req, res) => {
  console.log("GET request for reviews by reviewer:", req.params);
  reviewController.getReviewsByReviewer(req, res);
});

// Get average rating for a reviewee
router.get("/average/:revieweeId/:revieweeType", (req, res) => {
  console.log("GET request for average rating:", req.params);
  reviewController.getAverageRating(req, res);
});

router.get("/student/pending/:centerId", (req, res) => {
  console.log("HI");
  reviewController.getPendingReview(req, res);
});
router.get("/student/pending/class/:classId", (req, res) => {
  console.log("Fetching pending reviews by classId");
  reviewController.getPendingReviewByClassId(req, res);
});
router.get("/student/pending/coach/:coachId", (req, res) => {
  console.log("Fetching pending reviews by coachId");
  reviewController.getPendingReviewByCoachId(req, res);
});

// New route for pending reviews by parentId
router.get("/parent/:parentId/pending", (req, res) => {
  console.log("Fetching pending reviews by parentId:", req.params.parentId);
  reviewController.getPendingReviewsByParentId(req, res);
});

// New route for parent review history
router.get("/parent/:parentId/history", (req, res) => {
  console.log("Fetching review history for parentId:", req.params.parentId);
  reviewController.getParentReviewHistory(req, res);
});

router.get("/:classId/:revieweeId/:revieweeType/:date", async (req, res) => {
  reviewController.getReviewByClassAndId(req, res);
});

router.get("/moments", async (req, res) => {
  console.log("HI");
  reviewController.getMoments(req, res);
});
module.exports = router;
console.log("Review routes initialized and exported");
