const express = require("express");
const router = express.Router();
const {
  centerController,
  authMiddleware,
  centerUseCase,
  upload,
} = require("../../container/container");
// Routes for CenterController
router.post(
  "/create",
  authMiddleware.authenticate.bind(authMiddleware), // Authentication middleware
  (req, res, next) => {
    upload(req, res, (error) => {
      if (error) {
        return res
          .status(400)
          .json({ message: `File upload error: ${error.message}` }); // Handle Multer errors
      }
      next(); // Proceed to the next middleware if no error
    });
  },
  centerController.createCenter.bind(centerController)
); // Controller logic

router.get(
  "/",
  //  authMiddleware.authenticate.bind(authMiddleware),
  centerController.getAllCenters.bind(centerController)
);

router.get(
  "/:id",

  centerController.getCenterById.bind(centerController)
);
router.get(
  "/getCoach/:id",

  (req, res) => {
    console.log("hi");
    centerController.getCoachesByCenterId(req, res);
  }
);
router.get(
  "/getManager/:id",

  (req, res) => {
    console.log("hi");
    centerController.getManagersByCenterId(req, res);
  }
);

router.put(
  "/:id",
  authMiddleware.authenticate.bind(authMiddleware),
  upload,
  centerController.updateCenter.bind(centerController) // Bind the controller method
);

// NEW: Add center verification/approval route
router.put(
  "/:id/verify",
  authMiddleware.authenticate.bind(authMiddleware),
  centerController.verifyCenter.bind(centerController)
);

router.delete(
  "/:id",
  authMiddleware.authenticate.bind(authMiddleware),
  centerController.deleteCenter.bind(centerController)
);

router.post(
  "/assignCoach",
  //  authMiddleware.authenticate.bind(authMiddleware),
  (req, res) => centerController.assignCoach(req, res)
);
router.post(
  "/removeCoach",
  //  authMiddleware.authenticate.bind(authMiddleware),
  (req, res) => centerController.removeCoach(req, res)
);
router.get("/request/:type/:id", (req, res) =>
  centerController.getRequestByType(req, res)
);
// Export the router
module.exports = { router, centerUseCase };
