class EventService {
  constructor({
    eventRepository,
    classRepository,
    cancellationNotificationService,
    classDateRepo,
    refundService,
    childRepo,
  }) {
    this.eventRepository = eventRepository;
    this.classRepository = classRepository;
    this.cancellationNotificationService = cancellationNotificationService;
    this.classDateRepo = classDateRepo;
    this.refundService = refundService;
    this.childRepo = childRepo;
  }

  async getEvents(filter) {
    console.log(filter);
    if (filter.date) return await this.eventRepository.getEvents(filter);
    else return await this.eventRepository.getEventDates(filter);
  }

  async getEventById(eventId) {
    console.log("geelo");
    return await this.eventRepository.getEventById(eventId);
  }
  async getEventByClassId(classId) {
    const classInfo = await this.classRepository.getByIdForCheck(classId);
    if (!classInfo) {
      throw new Error("Class not found");
    }

    const events = await this.eventRepository.getEventDatesByClassId(classId);
    console.log(events);
    // Group events by dateId
    const grouped = {};

    for (const event of events) {
      const key = event.dateId;
      if (!key) continue;

      if (!grouped[key]) {
        grouped[key] = {
          dateId: key,
          dates: [],
        };
      }

      grouped[key].dates.push(event.date);
    }

    return {
      eventDates: Object.values(grouped), // convert grouped object to array
    };
  }

  async getEventsByCenterIdAndDate(centerId, eventDate) {
    console.log("hello");
    return await this.eventRepository.getEventsByCenterIdAndDate(
      centerId,
      eventDate
    );
  }

  async deleteEventById(eventId) {
    try {
      // Fetch the event first
      const event = await this.eventRepository.getByIdWithoutPopulate(eventId);
      console.log('[deleteEventById] Event fetched:', event);
      if (!event) {
        throw new Error("Event not found");
      }
      // Delete the event
      const deletedEvent = await this.eventRepository.deleteEventById(eventId);
      console.log('[deleteEventById] Event deleted:', deletedEvent);
      // Decrement numberOfClass in the related classDate (dateId)
      if (event.dateId) {
        // Fetch the classDate
        const classDate = await this.classDateRepo.getById(event.dateId);
        console.log('[deleteEventById] ClassDate fetched:', classDate);
        if (classDate && typeof classDate.numberOfClass === 'number') {
          const newNumberOfClass = Math.max(0, classDate.numberOfClass - 1);
          console.log('[deleteEventById] Updating numberOfClass from', classDate.numberOfClass, 'to', newNumberOfClass);
          await this.classDateRepo.update(event.dateId, { numberOfClass: newNumberOfClass });
        }
      }
      return { deletedEvent, updatedDateId: event.dateId };
    } catch (error) {
      console.error('[deleteEventById] Error:', error);
      throw error;
    }
  }

  async deleteEvents(filter) {
    return await this.eventRepository.deleteEvents(filter);
  }
  async deleteEventsByClassId(classId, data) {
    try {
      console.log(
        `Attempting to ${
          data.cancelType || "delete"
        } events for class: ${classId}`
      );

      // Get class information including enrolled students
      const classs = await this.classRepository.getById(classId);
      console.log(`Found class with ${classs.student.length} students`);

      if (!classs) {
        throw new Error("Class not found");
      }
      console.log("hello");
      // Handle different cancellation types
      switch (data.cancelType) {
        case "refund":
          return await this.handleRefundCancellation(classs, data.eventId);

        case "rearrange":
          return await this.handleRearrangeCancellation(classs, data);

        default:
          // Original deletion logic for backward compatibility
          if (classs.student.length > 0) {
            throw new Error("You can not delete a slot after student enroll");
          }
          await this.eventRepository.deleteManyByClassId(classId);
          return { type: "deleted", message: "Events deleted successfully" };
      }
    } catch (error) {
      console.error("Error in deleteEventsByClassId:", error);
      throw error;
    }
  }

  async handleRefundCancellation(classs, eventId) {
    try {
      //  console.log(`hi ${classs}`);
      // 1. Fetch the event to get dateId
      const event = await this.eventRepository.findById(eventId);
      if (!event) {
        throw new Error("Event not found");
      }

      let classDate;
      if (event.dateId) {
        // 2. Fetch students from classDateRepo by dateId
        classDate = await this.classDateRepo.getStudents(event.dateId);
      }

      let students = [];
      students = classDate.students;
      const classCharge = classDate.charge;
console.log(classs);
      // 3. Use these students for refund processing
      const refundResults = [];

      if (students && students.length > 0) {
        for (const student of students) {
          const parentId = await this.childRepo.getParentByChildId(
            student._id || student
          );
          if (!parentId) throw new Error("Parent not found for student");
          const refundAmount = classCharge / classs.numberOfClass || 0;
          const refundData = {
            studentId: student._id || student,
            parentId: parentId,
            classId: classs._id,
            eventId: eventId,
            date: event.date,
            amount: refundAmount,
            status: "processed",
            processedAt: new Date(),
            notes: "",
          };
    //      console.log(refundData);
         
         // Save refund to database using refundService
              await this.refundService.saveRefund(parentId, refundData);

              // console.log(
              //   `Processed refund for student: ${
              //     student._id || student
              //   }, amount: ${refundAmount}`
              // );

              if (this.cancellationNotificationService) {
                console.log("helllllo");
                try {
                  console.log(`hi ${parentId}`);
                  await this.cancellationNotificationService.sendRefundNotifications(
                    refundData,
                    parentId,
                    classs
                  );
                  console.log("Refund notifications sent successfully");
                } catch (error) {
                  console.error("Failed to send refund notifications:", error);
                }
              }
            }
            // Send notifications to students about refunds
          }

          // Delete the event after processing refunds
          await this.eventRepository.deleteEventById(eventId);

          return {
            type: "refunded",
            message: `Slot cancelled and ${refundResults.length} students will be refunded`,
            refunds: refundResults,
            deletedEvents: true,
          };
        }
      
     catch (error) {
      console.error("Error processing refunds:", error);
      throw new Error(`Failed to process refunds: ${error.message}`);
    }
  }

  async handleRearrangeCancellation(classs, data) {
    try {
      console.log(`Marking class for rearrangement: ${classs._id}`);

      let event;
      event = await this.eventRepository.findById(data.eventId);

      if (!event) {
        throw new Error("Event not found");
      }
      console.log(data.date);
      //   const updatedEvent = {};
      // Update the event with new date and status
      const updatedEvent = await this.eventRepository.update(data.eventId, {
        date: data.date,
        status: "rearranged",
        "rearrangementInfo.originalDate": event.date,
        "rearrangementInfo.newDate": data.date,
        "rearrangementInfo.processedAt": new Date(),
        "rearrangementInfo.status": "completed",
      });

      // Find students from classDateRepo by event.dateId and notify their parents
      if (event.dateId) {
        console.log("check students");
        // Fetch classDate and populate students with parent info
        const classDate = await this.classDateRepo.getById(event.dateId);
        console.log(classDate);
        let students = [];
        if (classDate && classDate.students && classDate.students.length > 0) {
          // Populate students with parent info
          students = await this.classDateRepo.getStudents(event.dateId);
          console.log(` got ${students.length} students`);
          // // Extract unique parent IDs
          // const parentIds = [
          //   ...new Set(
          //     students.map((s) => s.parent && s.parent.toString()).filter(Boolean)
          //   ),
          // ];
          // console.log(parentIds);
          // Send notification to each parent
          for (const student of students) {
            //      console.log(`HI ${student}`);
            try {
              await this.cancellationNotificationService.sendRearrangementNotifications(
                student,
                event.date,
                data.date,
                classs
              );
            } catch (error) {
              console.error(
                `Failed to send rearrangement notification to parent ${student.parent}:`,
                error
              );
            }
          }
        }
      }

      const rearrangementData = {};
      console.log(
        `Event rearranged successfully, ${rearrangementData.originalStudentCount} students will be notified`
      );

      return {
        type: "rearranged",
        message: `Event rescheduled successfully, ${rearrangementData.originalStudentCount} students will be notified`,
        rearrangement: rearrangementData,
        updatedEvent: updatedEvent,
        deletedEvents: false, // Events are kept and updated
      };
    } catch (error) {
      console.error("Error processing rearrangement:", error);
      throw new Error(`Failed to process rearrangement: ${error.message}`);
    }
  }

  async getEventsByParentId(parentId) {
    // 1. Find all children for the parent
    const children = await this.childRepo.getChildByParentId(parentId);
    console.log(children);
    const childMap = new Map(
      children.map((child) => [String(child._id), child])
    );
    const childIds = children.map((child) => child._id);
    // 2. For each child, find all class dates (slots) where the child is a student
    const allClassDates = await this.classDateRepo.getClassDatesByStudentIds(
      childIds
    );
    const classDateIdToStudents = new Map();
    allClassDates.forEach((cd) => {
      classDateIdToStudents.set(
        String(cd._id),
        cd.students.map((s) => String(s))
      );
    });
    const classDateIds = allClassDates.map((cd) => cd._id);
    console.log(childIds);
    // 3. Find all events where dateId is in those classDateIds, and populate classId as specified
    const events = await this.eventRepository.findWithPopulate(
      { dateId: { $in: classDateIds } },
      [
        {
          path: "classId",
          select: "-student -dates",
          populate: [
            {
              path: "center",
              select: "displayName address",
            },
            {
              path: "coach",
              select: "displayName",
            },
          ],
        },
        {
          path: "dateId", // this will return the full date document
        },
      ]
    );
    console.log(events);
    // 4. For each event, determine which child(ren) it is for
    const result = [];
    for (const event of events) {
      const eventDateId = String(event.dateId._id || event.dateId);
      const studentIds = classDateIdToStudents.get(eventDateId) || [];
      for (const studentId of studentIds) {
        if (childMap.has(studentId)) {
          result.push({
            event: {
              ...event.toObject(),
              classId: event.classId, // already populated as specified
            },
            childId: {
              _id: studentId,
              fullname: childMap.get(studentId).fullname,
              sen: childMap.get(studentId).sen,
            },

            classDateId: eventDateId, // optional: include for clarity
          });
        }
      }
    }
    return result;
  }
}

module.exports = EventService;
