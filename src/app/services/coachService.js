const path = require("path");
class CoachService {
  constructor({
    coachRepository,
    baseUserRepository,
    tokenService,
    uploadFile,
  }) {
    this.coachRepository = coachRepository;
    this.baseUserRepository = baseUserRepository;
    this.tokenService = tokenService;
    this.uploadFile = uploadFile;
  }

  async create(coachData) {
    if (!coachData.baseUser) {
      throw new Error("Base user ID is required to create a coach.");
    }
    const baseUserId = coachData.baseUser;
    await this.uploadFile.uploadFile(
      coachData.mainImage,
      "mainImage",
      coachData,
      baseUserId
    );
    const imageData = { ...coachData };
    if (coachData.images && coachData.images.length > 0) {
      imageData.images = []; // Initialize an empty array for images
      // Iterate through each image and upload
      for (const image of coachData.images) {
        console.log("image");
        await this.uploadFile.uploadFile(
          image,
          "images",
          imageData,
          baseUserId
        );
      }
      coachData.images = imageData.images;
      console.log("All images uploaded:", coachData.images);
    } else {
      console.error("centerData.images is undefined or empty");
    }
    const coach = await this.coachRepository.create(coachData);
    const token = await this.tokenService.generate({
      id: coach._id,
      type: "coach",
    });
    return { token, coach };
  }
  async update(coachId, coachData) {
    console.log("coachUpdate");
    try {
      // Find the Coach by ID
      const coach = await this.coachRepository.findById(coachId);
      if (!coach) {
        throw new Error("Coach not found");
      }

      const updateData = { ...coachData };

      // Handle main image upload
      await this.uploadFile.uploadFile(
        coachData.mainImage,
        "mainImage",
        updateData,
        coachId
      );

      // Handle images upload
      if (coachData.images && coachData.images.length > 0) {
        updateData.images = []; // Initialize an empty array for images
        for (const image of coachData.images) {
          await this.uploadFile.uploadFile(
            image,
            "images",
            updateData,
            baseUserId
          );
        }
        console.log("All images uploaded:", updateData.images);
      } else {
        console.error("coachData.images is undefined or empty");
      }

      // Add new centerIds to existing centers in Coach
      if (coachData.newCenterIds && coachData.newCenterIds.length > 0) {
        coach.centers = coach.centers || [];
        coachData.newCenterIds.forEach((newCenterId) => {
          if (!coach.centers.includes(newCenterId)) {
            coach.centers.push(newCenterId);
          }
        });
        updateData.centers = coach.centers; // Ensure centers are included in the update
      }
      console.log("updateData", updateData);
      const updatedCoach = await this.coachRepository.update(
        coachId,
        updateData
      );
      console.log("Done");
      return updatedCoach;
    } catch (error) {
      console.error("Error updating coach:", error);
      throw error;
    }
  }

  async getCoachById(coachId) {
    try {
      const coach = await this.coachRepository.findById(coachId);
      return coach;
    } catch (error) {
      throw new Error("Error getting coach by ID: " + error.message);
    }
  }
  async deleteCoachById(coachId) {
    try {
      const coach = await this.coachRepository.delete(coachId);
      return coach;
    } catch (error) {
      throw new Error("Error deleting coach by ID: " + error.message);
    }
  }
  async getAllCoach() {
    try {
      const coach = await this.coachRepository.findAll();
      return coach;
    } catch (error) {
      throw new Error("Error fetching coachs: " + error.message);
    }
  }
  async getProgramByCoachId(coachId) {
    try {
      const coach = await this.coachRepository.findById(coachId);
      if (!coach) {
        throw new Error("Coach not found");
      }
      const program = await this.coachRepository.getProgramByCoachId(coachId);
      return program;
    } catch (error) {
      throw new Error("Error fetching program by coach ID: " + error.message);
    }
  }
}

module.exports = CoachService;
