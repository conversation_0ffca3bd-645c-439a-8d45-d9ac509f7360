class SearchService {
  constructor(searchRepo) {
    this.searchRepo = searchRepo;
  }

  async search(query, filters, sortBy, geoLocation, pagination) {
    try {
      // Get search results
      const classResults = await this.searchRepo.searchClasses(
        query,
        filters,
        sortBy,
        pagination
      );

      // If using distance sorting, use geolocation for centers
      let centerResults;
      if (
        sortBy === "distance" &&
        geoLocation.longitude &&
        geoLocation.latitude
      ) {
        centerResults = await this.searchRepo.findNearbyCenters(
          geoLocation,
          {
            ...filters,
            searchQuery: query,
          },
          sortBy,
          pagination
        );
      } else {
        centerResults = await this.searchRepo.searchCenters(
          query,
          filters,
          sortBy,
          pagination
        );
      }

      // Get coach results
      const coachResults = await this.searchRepo.searchCoaches(
        query,
        filters,
        sortBy,
        pagination
      );

      // Combine results
      const results = {
        classes: classResults.data,
        centers: centerResults.data,
        coaches: coachResults.data,
      };

      // Total count
      const totalCount =
        (classResults.totalCount || 0) +
        (centerResults.totalCount || 0) +
        (coachResults.totalCount || 0);

      return {
        results,
        totalCount,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method for filtering centers
  async filterCenters(filters, sortBy, geoLocation, pagination) {
    try {
      // If using distance sorting, use geolocation
      if (
        sortBy === "distance" &&
        geoLocation.longitude &&
        geoLocation.latitude
      ) {
        return await this.searchRepo.findNearbyCenters(
          geoLocation,
          filters,
          sortBy,
          pagination
        );
      } else {
        return await this.searchRepo.filterCenters(filters, sortBy, pagination);
      }
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method for filtering classes
  async filterClasses(filters, sortBy, pagination) {
    try {
      return await this.searchRepo.filterClasses(filters, sortBy, pagination);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method for finding nearby centers
  async findNearbyCenters(geoLocation, filters, sortBy, pagination) {
    try {
      return await this.searchRepo.findNearbyCenters(
        geoLocation,
        filters,
        sortBy,
        pagination
      );
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
  async getCoachesByQuery(query, pagination) {
    try {
      return await this.searchRepo.searchCoaches(query, pagination);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}

module.exports = SearchService;
