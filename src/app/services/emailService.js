const nodemailer = require("nodemailer");
const dotenv = require("dotenv");
dotenv.config();

class EmailService {
  async sendOtp(email, otp) {
    console.log(process.env.PASSWORD);
    try {
      // Create transporter
      const transporter = nodemailer.createTransport({
        service: "Gmail",
        auth: {
          user: process.env.EMAIL,
          pass: process.env.PASSWORD,
        },
      });

      // Define mail options
      const mailOptions = {
        from: process.env.EMAIL, // Sender email from environment
        to: email, // Recipient's email
        subject: "Your Verification Code", // Email subject
        text: `Your verification code is ${otp}. Please use this code to verify your account.`, // Email body
      };

      // Send email
      const info = await transporter.sendMail(mailOptions);

      console.log("Email sent successfully:", info.response);
      return { success: true, message: "OTP sent successfully." };
    } catch (error) {
      console.error("Error sending email:", error.message);
      return { success: false, message: error.message };
    }
  }
  async contactUs(email, subject, message) {
    try {
      // Create transporter
      const transporter = nodemailer.createTransport({
        service: "Gmail",
        auth: {
          user: process.env.EMAIL,
          pass: process.env.PASSWORD,
        },
      });
      const mailOptions = {
        from: `"Website Contact" <${process.env.SMTP_EMAIL}>`, // must match SMTP_EMAIL
        to: process.env.EMAIL, // your fixed email
        subject: subject,
        text: `Message from:<${email}>\n\n${message}`,
        replyTo: email, // allows you to reply directly to the user
      };

      console.log("Sending email to:", process.env.EMAIL);
      console.log("Email content:", mailOptions);
      const info = await transporter.sendMail(mailOptions);
      console.log("Email sent successfully:", info.response);
      return { success: true, message: "email sent successfully" };
    } catch (error) {
      console.error("Error sending email:", error.message);
      return { success: false, message: error.message };
    }
  }
}

module.exports = EmailService;
