const jwt = require("jsonwebtoken");
require("dotenv").config();
let secretKey = process.env.JWT_SECRET_KEY;

// Fallback to config file if environment variable is not set
if (!secretKey) {
  try {
    const config = require("../../../config");
    secretKey = config.jwt.secretKey;
    console.log("Using JWT key from config file");
  } catch (err) {
    console.error("Error loading JWT secret key:", err.message);
    secretKey = "default_unsafe_key";
  }
}

class TokenService {
  generate(payload) {
    return jwt.sign(payload, secretKey);
  }
  validate(token) {
    try {
      return jwt.verify(token, secretKey);
    } catch (error) {
      throw new Error("Invalid or expired token: " + error.message);
    }
  }
}

module.exports = TokenService;
