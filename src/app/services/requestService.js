const NotificationService = require("../services/notificationService");
class RequestService {
  constructor({ requestRepository, coachRepo, centerRepo }) {
    this.requestRepository = requestRepository;
    this.coachRepo = coachRepo;
    this.centerRepo = centerRepo;
  }
  _buildNotificationData(center, coach) {
    return {
      centerId: String(center.id),
      centerMainImage: String(center.mainImage?.url || ""),
      centerDisplayName: String(center.displayName),
      centerAddress: `${center.address.city}, ${center.address.region}`,
      coachId: String(coach.id),
      coachName: String(coach.displayName || coach.name || ""),
      coachProfileImage: String(coach.profileImage?.url || ""),
      coachBio: String(coach.bio || ""),
      url: "/request",
    };
  }

  _buildNotificationMessage(userId, data) {
    return {
      title: `Request for Coach Role`,
      body: `The above coach has requested for the role Coach with access permission`,
      data,
      userId,
    };
  }
  async sendJoinRequest(coachId, centerId) {
    const session = await this.coachRepo.startSession();

    try {
      session.startTransaction();
      const coach = await this.coachRepo.findByIdWithCenter(coachId);
      const center = await this.centerRepo.findById(centerId);

      if (!coach || !center) {
        throw new Error("Invalid coach or center.");
      }

      // Check if either the requesting coach or target center involves individual educators
      const coachCenter = coach.center;
      
      // Populate center owner to check individual educator status
      const centerWithOwner = await this.centerRepo.findByIdWithOwner(centerId);
      
      // Block connection if target center is an individual educator
      if (centerWithOwner?.isFreelanceEducator || centerWithOwner?.owner?.isIndividualCreator) {
        throw new Error("Cannot connect to individual educator centers.");
      }
      
      // Block connection if requesting coach is from an individual educator center
      if (coachCenter?.isFreelanceEducator || coachCenter?.owner?.isIndividualCreator) {
        throw new Error("Individual educator coaches cannot connect to other centers.");
      }

      const existingRequest = await this.requestRepository.findOne({
        coachId,
        centerId,
      });

      if (existingRequest) {
        // Return information about the existing request instead of throwing error
        await session.commitTransaction();
        return {
          success: false,
          message: "Request already exists.",
          requestExists: true,
          existingRequest: existingRequest,
          status: existingRequest.status || 'pending'
        };
      }

      const request = await this.requestRepository.create({
        coachId,
        centerId,
        status: 'pending'
      });
      
      const notificationData = this._buildNotificationData(center, coach);
      const message = this._buildNotificationMessage(
        center.owner,
        notificationData
      );
      
      await NotificationService.sendNotification(center.baseUser, message);
      await session.commitTransaction();
      
      return {
        success: true,
        message: "Request sent successfully.",
        requestExists: false,
        request: request,
        status: 'pending'
      };
    } catch (error) {
      await session.abortTransaction();
      console.log(error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  async updateRequestStatus(requestId, status) {
    try {
      const validStatuses = ["approved", "rejected", "pending"];
      if (!validStatuses.includes(status)) {
        throw new Error("Invalid status.");
      }

      const request = await this.requestRepository.findById(requestId);
      if (!request) {
        throw new Error("No request found.");
      }

      if (status === "rejected") {
        await this.requestRepository.deleteById(requestId);
        return true;
      }

      if (status === "approved") {
        const coach = await this.coachRepo.findById(request.coachId);
        if (!coach) throw new Error("Coach not found.");

        const center = await this.centerRepo.findById(request.centerId);
        if (!center) throw new Error("Center not found.");

        // Check if coach already linked to center
        if (center.coachs.includes(coach.id)) {
          throw new Error("Coach already assigned to this center.");
        }

        // Assign center to coach and update
        coach.center = request.centerId;
        await this.coachRepo.update(coach.id, coach);

        // Add coach to center and update
        center.coachs.push(coach.id);
        await this.centerRepo.update(center.id, center);
      }

      // Clean up the request after approval
      await this.requestRepository.deleteById(requestId);
      return true;
    } catch (error) {
      throw new Error(`Failed to update request status: ${error.message}`);
    }
  }

  async getRequestsByCenter(centerId) {
    return await this.requestRepository.findByCenter(centerId);
  }

  async getRequestsByCoach(coachId) {
    return await this.requestRepository.findByCoach(coachId);
  }

  async checkExistingRequest(coachId, centerId) {
    try {
      const existingRequest = await this.requestRepository.findOne({
        coachId,
        centerId,
      });

      if (existingRequest) {
        return {
          exists: true,
          request: existingRequest,
          status: existingRequest.status || 'pending'
        };
      }

      return {
        exists: false,
        request: null,
        status: null
      };
    } catch (error) {
      throw new Error(`Failed to check existing request: ${error.message}`);
    }
  }

  async cancelJoinRequestByCoachAndCenter(coachId, centerId) {
    // Find and delete the request with matching coachId and centerId
    const result = await this.requestRepository.requestModel.findOneAndDelete({
      coachId,
      centerId,
    });
    return result;
  }
}

module.exports = RequestService;
