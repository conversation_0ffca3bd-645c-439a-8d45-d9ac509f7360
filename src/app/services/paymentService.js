const Stripe = require("stripe");
let stripeKey = process.env.STRIPE_SECRET_KEY;

// Fallback to a config file if no environment variable is set
if (!stripeKey) {
  try {
    // Try to load from a config file
    const config = require("../../../config");
    stripeKey = config.stripe.secretKey;
    console.log("Loaded Stripe key from config file");
  } catch (err) {
    console.error("Error loading stripe key:", err.message);
    // Set a placeholder to avoid immediate crash, but service will not work
    stripeKey = "missing_stripe_key";
    console.error("WARNING: Using placeholder stripe key. Payment functions will fail!");
  }
}

const stripe = new Stripe(stripeKey);
const IPaymentService = require("../../interfaces/IPaymentService");

class StripePaymentService extends IPaymentService {
  constructor() {
    super();
    // Validate stripe key on initialization
    this.validateStripeKey();
  }

  // Validate stripe key
  async validateStripeKey() {
    if (stripeKey === "missing_stripe_key") {
      console.error("Invalid Stripe key configuration. Payment services will not work.");
      return false;
    }
    
    try {
      // Simple test to check if the key is valid by retrieving account info
      await stripe.account.retrieve();
      console.log("Stripe key is valid and connected to Stripe account");
      return true;
    } catch (error) {
      console.error("Stripe key validation failed:", error.message);
      return false;
    }
  }

  async createZCoinPaymentIntent(userId, amount, cardToken) {
    try {
      // Create and confirm payment intent in one step
      const paymentIntent = await stripe.paymentIntents.create({
        amount: amount * 100,
        currency: "usd",
        payment_method: cardToken,
        customer: userId,
        confirm: true,
        use_stripe_sdk: true,
        return_url: 'https://classz.app/payment-return',
      });
      
      return { 
        paymentIntent: paymentIntent, 
        transactionId: paymentIntent.id,
        client_secret: paymentIntent.client_secret
      };
    } catch (error) {
      throw new Error(error.message);
    }
  }

  async confirmZCoinPaymentIntent(paymentIntentId) {
    try {
      const paymentIntent = await stripe.paymentIntents.confirm(
        paymentIntentId
      );
      return paymentIntent;
    } catch (error) {
      throw new Error(error.message);
    }
  }

  async createCustomer(email, cardToken) {
    try {
      const customer = await stripe.customers.create({
        email: email,
        source: cardToken,
      });
      return customer;
    } catch (error) {
      throw new Error("Could not create customer");
    }
  }

  async addCardToCustomer(customerId, cardToken) {
    console.log(`addCardToCustomer called with customerId: ${customerId}, cardToken: ${cardToken?.substring(0, 8)}...`);
    
    try {
      console.log(`Creating source for customer ${customerId} with token ${cardToken?.substring(0, 8)}...`);
      const card = await stripe.customers.createSource(customerId, {
        source: cardToken,
      });
      console.log("Card source created successfully:", card.id);
      return card;
    } catch (error) {
      console.error("Error adding card to customer:", error.message);
      throw new Error("Could not add card to customer: " + error.message);
    }
  }

  async getSavedCard(customerId) {
    try {
      const response = await stripe.paymentMethods.list({
        customer: customerId,
        type: "card",
      });

      if (response.data.length > 0) {
        const card = response.data[0];
        const expYearLastTwo = card.card.exp_year.toString().slice(-2);
        return {
          name: card.billing_details.name,
          last4: card.card.last4,
          expiry_date: `${card.card.exp_month}/${expYearLastTwo}`,
          brand: card.card.brand,
        };
      } else {
        throw new Error("No cards found for this user");
      }
    } catch (error) {
      throw new Error("Could not retrieve saved cards");
    }
  }

  async deleteCustomerPaymentMethods(customerId) {
    try {
      // Get all payment methods for the customer
      const paymentMethods = await stripe.paymentMethods.list({
        customer: customerId,
        type: "card",
      });

      // Delete each payment method
      for (const paymentMethod of paymentMethods.data) {
        await stripe.paymentMethods.detach(paymentMethod.id);
      }

      // Also get and delete any legacy sources
      const customer = await stripe.customers.retrieve(customerId);
      if (customer.sources && customer.sources.data) {
        for (const source of customer.sources.data) {
          await stripe.customers.deleteSource(customerId, source.id);
        }
      }

      return { success: true, message: "All payment methods deleted successfully" };
    } catch (error) {
      console.error("Error deleting payment methods:", error);
      throw new Error("Could not delete payment methods: " + error.message);
    }
  }

  async createSubscription(customerId, priceId) {
    try {
      const subscription = await stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
      });
      return subscription;
    } catch (error) {
      throw new Error("Could not create subscription");
    }
  }

  async createToken(card) {
    const token = await stripe.tokens.create({
      card: {
        number: card.number,
        exp_month: card.expMonth,
        exp_year: card.expYear,
        cvc: card.cvc,
        name: card.name,
      },
    });
    return token.id;
  }

  async getPaymentMethod(customerId) {
    try {
      // Check if this is already a Stripe customer ID (starts with 'cus_')
      let stripeCustomerId;
      
      if (customerId.startsWith('cus_')) {
        stripeCustomerId = customerId;
      } else {
        // Get the user from the database to get their Stripe customer ID
        const User = require('../models/userModel');
        const user = await User.findById(customerId);
        
        if (!user || !user.stripeCustomerId) {
          throw new Error("User not found or no Stripe customer ID available");
        }
        
        stripeCustomerId = user.stripeCustomerId;
      }
      
      const paymentMethods = await stripe.paymentMethods.list({
        customer: stripeCustomerId,
        type: 'card',
      });
      
      if (paymentMethods.data.length === 0) {
        throw new Error('No payment methods found for this customer');
      }
      
      return {
        paymentMethodId: paymentMethods.data[0].id,
      };
    } catch (error) {
      console.error('Error retrieving payment method:', error);
      throw new Error('Could not retrieve payment method: ' + error.message);
    }
  }

  async payWithSavedCard(userId, amount, paymentMethodId) {
    try {
      // First get the user's Stripe customer ID
      let stripeCustomerId;
      
      // Check if this is already a Stripe customer ID (starts with 'cus_')
      if (userId.startsWith('cus_')) {
        stripeCustomerId = userId;
      } else {
        // Get the user from the database to get their Stripe customer ID
        const User = require('../models/userModel');
        const user = await User.findById(userId);
        
        if (!user || !user.stripeCustomerId) {
          throw new Error("User not found or no Stripe customer ID available");
        }
        
        stripeCustomerId = user.stripeCustomerId;
      }
      
      // Create and confirm the payment intent in one step
      // Using a simpler approach to avoid parameter conflicts
      const paymentIntent = await stripe.paymentIntents.create({
        amount: amount * 100, // Convert to cents
        currency: 'usd',
        customer: stripeCustomerId,
        payment_method: paymentMethodId,
        confirm: true, // Confirm immediately
        use_stripe_sdk: true, // This ensures proper authentication flow if needed
        return_url: 'https://classz.app/payment-return', // Fallback URL for 3D Secure if needed
      });
      
      return {
        success: true,
        paymentIntentId: paymentIntent.id,
        status: paymentIntent.status,
        client_secret: paymentIntent.client_secret
      };
    } catch (error) {
      console.error('Error processing payment with saved card:', error);
      throw new Error('Payment failed: ' + error.message);
    }
  }
}

module.exports = StripePaymentService;
