
class AuthMiddleware {
  constructor(tokenService) {
    this.tokenService = tokenService;
  }

  authenticate(req, res, next) {
    try {
      console.log("PSFLKHSLKFJHLKSJHFLKJSFLKJSLKFJlk")
      const token = req.header('auth-token');
      if (!token) return res.status(401).json({ message: 'Access Denied' });

      const verified = this.tokenService.validate(token);
      req.user = verified.id;
      next();
    } catch (error) {
      res.status(400).json({ message: 'Invalid Token' });
    }
  }
}

module.exports = AuthMiddleware;
