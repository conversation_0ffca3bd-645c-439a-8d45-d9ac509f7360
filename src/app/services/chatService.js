class ChatService {
  constructor(chatRepository) {
    this.chatRepository = chatRepository;
    this.cache = new Map();
    this.cacheTTL = 60000; // 1 minute in milliseconds
    
    // Periodically clean up expired cache entries
    setInterval(() => this.cleanupCache(), 300000); // Run every 5 minutes
  }

  /**
   * Get all chats between a sender and recipient
   */
  async getChats(senderId, recipientId, page, limit) {
    const cacheKey = `chats:${senderId}:${recipientId}:${page}:${limit}`;
    
    // Check cache first
    const cachedResult = this.getFromCache(cacheKey);
    if (cachedResult) {
      console.log(`Cache hit for ${cacheKey}`);
      return cachedResult;
    }
    
    // If cache miss, get from repository
    const result = await this.chatRepository.getAllChats(
      senderId,
      recipientId,
      page,
      limit
    );
    
    // Cache the result
    this.setInCache(cacheKey, result);
    
    return result;
  }

  /**
   * Get all chats for a specific user
   */
  async getUserChats(userId, page, limit) {
    const cacheKey = `userChats:${userId}:${page}:${limit}`;
    
    // Check cache first
    const cachedResult = this.getFromCache(cacheKey);
    if (cachedResult) {
      console.log(`Cache hit for ${cacheKey}`);
      return cachedResult;
    }
    
    // If cache miss, get from repository
    const result = await this.chatRepository.getUserChats(userId, page, limit);
    
    // Cache the result
    this.setInCache(cacheKey, result);
    
    return result;
  }

  /**
   * Get all conversations for a user
   */
  async getUserConversations(userId, limit, skip) {
    console.log(`Service: Getting conversations for user ${userId} (limit: ${limit}, skip: ${skip})`);
    
    if (!userId) {
      console.warn('Service: Empty userId provided to getUserConversations');
      return { conversations: [], total: 0 };
    }
    
    const cacheKey = `conversations:${userId}:${limit}:${skip}`;
    
    // Check cache first
    const cachedResult = this.getFromCache(cacheKey);
    if (cachedResult) {
      console.log(`Cache hit for ${cacheKey}`);
      // Before returning from cache, ensure we have broadcast conversations included
      // Check if any broadcast conversation exists in the returned list
      const hasBroadcast = cachedResult.conversations && 
                           cachedResult.conversations.some(conv => 
                             conv.isBroadcast === true || 
                             (conv.conversationId && conv.conversationId.startsWith('-')));
                             
      if (!hasBroadcast) {
        console.log('Cache hit, but no broadcast conversations found. Forcing database query.');
        // Invalidate this cache entry
        this.cache.delete(cacheKey);
      } else {
        return cachedResult;
      }
    }
    
    // If cache miss, get from repository
    try {
      const startTime = Date.now();
      
      // First, make sure we include a direct query for broadcast conversations
      // by checking for the special conversation ID format
      const broadcastConvId = `-${userId}`;
      
      console.log(`Looking for broadcast conversation with ID: ${broadcastConvId}`);
      
      // Get conversation list from repo
      const result = await this.chatRepository.getConversationsForUser(userId, limit, skip);
      const duration = Date.now() - startTime;
      
      // Log performance metrics
      console.log(`Repository call took ${duration}ms for ${cacheKey}`);
      
      // Check if we have a broadcast conversation in the result
      const hasBroadcast = result.conversations && 
                           result.conversations.some(conv => 
                             conv.isBroadcast === true || 
                             (conv.conversationId && conv.conversationId.startsWith('-')));
                             
      if (!hasBroadcast) {
        console.log('No broadcast conversation found, explicitly checking broadcast messages');
        
        try {
          // Look for any broadcast messages for this user
          const broadcastOptions = { limit: 1 };
          const broadcastMsgs = await this.getBroadcastMessages(userId, broadcastOptions);
          
          if (broadcastMsgs.messages && broadcastMsgs.messages.length > 0) {
            console.log('Found broadcast messages, creating synthetic conversation');
            
            // Get user details for a better display name
            const userDetails = await this.chatRepository.getUserDetails(userId);
            
            // Create a synthetic broadcast conversation
            const broadcastConv = {
              conversationId: broadcastConvId,
              recipientId: '', // Empty recipient ID signals broadcast
              recipientType: 'user',
              name: (userDetails?.displayName || userDetails?.fullname || 'Broadcast Messages') + ' (Broadcast)',
              mainImage: userDetails?.mainImage || '',
              lastMessage: broadcastMsgs.messages[0].message || '',
              lastMessageSender: broadcastMsgs.messages[0].sender || '',
              timestamp: broadcastMsgs.messages[0].timestamp || new Date(),
              messageCount: 1,
              isBroadcast: true
            };
            
            // Add this to the result
            result.conversations = [broadcastConv, ...result.conversations];
            result.total += 1;
            
            console.log('Added synthetic broadcast conversation to results');
          } else {
            console.log('No broadcast messages found for this user');
          }
        } catch (e) {
          console.warn(`Error checking for broadcast messages: ${e.message}`);
          // Continue with regular results
        }
      }
      
      // Cache the result if it's valid
      if (result && Array.isArray(result.conversations)) {
        this.setInCache(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      console.error(`Service error in getUserConversations: ${error.message}`);
      // Return a valid but empty result to prevent client errors
      return { conversations: [], total: 0 };
    }
  }

  /**
   * Add a new chat message
   */
  async addChat(message, sender, recipient, senderModel, recipientModel, chatOptions = {}) {
    // This operation invalidates several caches
    this.invalidateConversationCaches(sender);
    this.invalidateConversationCaches(recipient);
    this.invalidateChatCaches(sender, recipient);
    
    return await this.chatRepository.createChat(
      message,
      sender,
      recipient,
      senderModel,
      recipientModel,
      chatOptions
    );
  }

  /**
   * Get chats between two users based on their types
   */
  async getChatsBetweenUsers(user1, user2, type1, type2, options = {}) {
    console.log(`Getting chats between ${type1} (${user1}) and ${type2} (${user2})`);
    
    // Input validation
    if (!user1 || !user2) {
      console.warn(`Invalid user IDs: user1=${user1}, user2=${user2}`);
      return { messages: [], nextCursor: null };
    }
    
    const cacheKey = `chats:${user1}:${user2}:${type1}:${type2}:${JSON.stringify(options)}`;
    
    // Check cache first
    const cachedResult = this.getFromCache(cacheKey);
    if (cachedResult) {
      console.log(`Cache hit for ${cacheKey}`);
      return cachedResult;
    }
    
    // Route to appropriate repository method based on user types
    let result;
    try {
      // Handle specific user type combinations
      if ((type1 === "user" && type2 === "center") || (type1 === "center" && type2 === "user")) {
        // Handle specific case for parent and center 
        result = await this.chatRepository.getChatsBetweenParentAndCenter(user1, user2, options);
      } else if ((type1 === "user" && type2 === "coach") || (type1 === "coach" && type2 === "user")) {
        // Handle specific case for parent and coach 
        result = await this.chatRepository.getChatsBetweenParentAndCoach(user1, user2, options);
      } else if ((type1 === "center" && type2 === "coach") || (type1 === "coach" && type2 === "center")) {
        // Handle specific case for center and coach
        result = await this.chatRepository.getChatsBetweenCenterAndCoach(user1, user2, options);
      } else {
        // Default fallback for any other combination or missing types
        console.log("Using default chat retrieval method for these user types");
        result = await this.chatRepository.getChatsBetweenUsers(user1, user2, type1, type2, options);
      }
      
      // Cache the result if successful and not empty
      if (result && Array.isArray(result.messages)) {
        this.setInCache(cacheKey, result);
      }
      
      return result;
    } catch (error) {
      console.error(`Error getting chats between users: ${error.message}`);
      return { messages: [], nextCursor: null };
    }
  }
  
  /**
   * Get broadcast messages for a user
   */
  async getBroadcastMessages(userId, options = {}) {
    console.log(`Fetching broadcast messages for user: ${userId}`);
    
    // Check for valid userId
    if (!userId || userId === '') {
      console.warn('Missing userId for broadcast messages');
      return { messages: [], nextCursor: null };
    }
    
    try {
      // Build a more comprehensive query to capture broadcast messages
      // Look for messages with any of these conditions:
      // 1. Special conversationId format (-userId)
      // 2. Messages where sender and recipient are the same user (self-messages)
      // 3. Messages with isBroadcast flag set to true
      
      let query = {
        $or: [
          // Case 1: Special conversationId format
          { conversationId: `-${userId}` },
          
          // Case 2: Self messages (same sender and recipient)
          { sender: userId, recipient: userId },
          
          // Case 3: Messages with broadcast flag
          { isBroadcast: true, $or: [{ sender: userId }, { recipient: userId }] }
        ]
      };
      
      // Add timestamp filter if provided
      if (options.beforeTimestamp) {
        query.timestamp = { $lt: new Date(options.beforeTimestamp) };
      }
      
      console.log(`Broadcast message query: ${JSON.stringify(query)}`);
      
      // Fetch messages with limit and sort
      const messages = await this.chatRepository.findChatsByQuery(
        query,
        parseInt(options.limit || 30)
      );
      
      // Get the oldest message timestamp for next pagination request
      const nextCursor = messages.length > 0 ? 
        messages[messages.length - 1].timestamp.toISOString() : null;
      
      console.log(`Found ${messages.length} broadcast messages for user ${userId}`);
      
      return {
        messages: messages.reverse(), // Return in chronological order
        nextCursor
      };
    } catch (error) {
      console.error(`Error fetching broadcast messages: ${error.message}`);
      return { messages: [], nextCursor: null };
    }
  }
  
  // CACHE UTILITIES
  
  /**
   * Get a value from the cache
   */
  getFromCache(key) {
    const entry = this.cache.get(key);
    if (entry && entry.expires > Date.now()) {
      return entry.value;
    }
    return null;
  }
  
  /**
   * Store a value in the cache
   */
  setInCache(key, value, ttl = this.cacheTTL) {
    this.cache.set(key, {
      value,
      expires: Date.now() + ttl
    });
  }
  
  /**
   * Clean up expired cache entries
   */
  cleanupCache() {
    const now = Date.now();
    let expiredCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expires <= now) {
        this.cache.delete(key);
        expiredCount++;
      }
    }
    
    if (expiredCount > 0) {
      console.log(`Cleaned up ${expiredCount} expired cache entries`);
    }
  }
  
  /**
   * Invalidate all conversation caches for a user
   */
  invalidateConversationCaches(userId) {
    for (const key of this.cache.keys()) {
      if (key.startsWith(`conversations:${userId}:`)) {
        this.cache.delete(key);
      }
    }
  }
  
  /**
   * Invalidate all chat caches between two users
   */
  invalidateChatCaches(user1, user2) {
    // Invalidate in both directions
    for (const key of this.cache.keys()) {
      if (
        key.includes(`:${user1}:${user2}:`) || 
        key.includes(`:${user2}:${user1}:`) ||
        key.startsWith(`chats:${user1}:${user2}:`) ||
        key.startsWith(`chats:${user2}:${user1}:`)
      ) {
        this.cache.delete(key);
      }
    }
  }
}

module.exports = ChatService;
