const UploadFile = require("./uploadFile");
const uploadFile = new UploadFile();
class BusinessOwnerService {
  constructor(businessOwnerRepository, centerRepository, coachRepository, orderRepository, classRepository) {
    this.businessOwnerRepository = businessOwnerRepository;
    this.centerRepository = centerRepository;
    this.coachRepository = coachRepository;
    this.orderRepository = orderRepository;
    this.classRepository = classRepository;
  }
  async processUploadFields(centerData, baseUserId) {
    const fields = ["businessCertificate", "sexualConvictionRecord", "hkidCard", "mainImage", "images"];

    for (const fieldName of fields) {
      if (centerData[fieldName] && Array.isArray(centerData[fieldName])) {
        console.log(
          `Processing multiple files for ${fieldName}:`,
          centerData[fieldName]
        );
        for (const file of centerData[fieldName]) {
          if (!file.path) {
            console.error(`Missing path for file in ${fieldName}:`, file);
            continue; // Skip files without path
          }
          await uploadFile.uploadFile(file, fieldName, centerData, baseUserId);
        }
      } else if (centerData[fieldName]) {
        console.log(
          `Processing single file for ${fieldName}:`,
          centerData[fieldName]
        );
        if (!centerData[fieldName].path) {
          throw new Error(`Missing path for single file in ${fieldName}`);
        }
        await uploadFile.uploadFile(
          centerData[fieldName],
          fieldName,
          centerData,
          baseUserId
        );
      }
    }
  }
  async getOwnerById(id) {
    return await this.businessOwnerRepository.findById(id);
  }
  async update(userId, userData) {
    const user = await this.businessOwnerRepository.findById(userId);
    if (!user) {
      throw new Error("No user exists with this ID");
    }
    console.log(userData);

    if (userData.isCoach === "true") {
      console.log("hi");
      console.log(user);

      const coachData = {
        baseUser: user.baseUser,
        legalName: user.fullName || "",
        displayName: user.displayName || "",
        hkid: user.hkid || "",
        phone: user.phoneNumber || "",
        email: user.email || "",
        mainImage: user.mainImage || {},
      };

      const createdCoach = await this.coachRepository.create(coachData);
      console.log("Created Coach:", createdCoach);

      // Also update the owner to set `isCoach` true and maybe store reference
      user.isCoach = true;
      user.coach = createdCoach._id.toString(); // if you want to link coach back
    }

    console.log(user);
    console.log("updating");
    console.log(userData);
    const baseUserId = user.baseUser;
    await this.processUploadFields(userData, baseUserId);
    console.log("upload done");
    const updateData = {
      ...user._doc,
      ...userData,
    };
    console.log(updateData);
    return await this.businessOwnerRepository.update(userId, updateData);
  }
  async getBranchByOwner(ownerId) {
    return await this.businessOwnerRepository.findBranchesByOwner(ownerId);
  }
  async deleteBranch(branchId) {
    const session = await this.businessOwnerRepository.startSession();
    try {
      session.startTransaction();

      const existingBranch = await this.centerRepository.findById(branchId);

      if (!existingBranch) {
        throw new Error("There is no existing branch with this ID");
      }

      // Example: Clear branch from employees or other related models
      const ownerId = existingBranch.owner;
      const employees = await this.coachRepository.findByCenterId(branchId);
      console.log(employees);
      if (employees && employees.length > 0) {
        for (const emp of employees) {
          await this.coachRepository.update(emp.id, {
            center: null,
          });
        }
      }

      const manager = await this.coachRepository.findManagerByCenterId(
        branchId
      );
      console.log(manager);
      if (manager != null) {
        manager.manager = null;
        await this.coachRepository.update(manager.id, { manager: null });
      }
      // You can add more cleanup logic here if needed

      // Delete the branch

      //   await this.businessOwnerRepository.deleteBranch(branchId, session);
      const owner = await this.businessOwnerRepository.findById(ownerId);

      owner.branchs = owner.branchs.filter(
        (branch) => branch.toString() !== branchId.toString()
      );
      await this.businessOwnerRepository.update(owner.id, {
        branchs: owner.branchs,
      });
      await this.centerRepository.deleteById(branchId);
      await session.commitTransaction();
      session.endSession();
      return true;
    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      throw new Error("Error deleting branch: " + error.message);
    }
  }

  async getOwnerDashboardData(ownerId, startDate = null, endDate = null) {
    try {
      // Get owner's branches
      const branches = await this.businessOwnerRepository.findBranchesByOwner(ownerId);
      const branchIds = branches.map(branch => branch._id);

      // Get current date and calculate date ranges
      const now = new Date();
      const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const currentMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

      // Use provided date range or default to last 12 months
      const effectiveStartDate = startDate ? new Date(startDate) : new Date(now.getFullYear(), now.getMonth() - 11, 1);
      const effectiveEndDate = endDate ? new Date(endDate) : now;

      // Get all classes for the owner's centers
      let allClasses = [];
      for (const branchId of branchIds) {
        const branchClasses = await this.classRepository.getByCenter(branchId);
        allClasses = allClasses.concat(branchClasses);
      }
      
      // Extract class IDs
      const classIds = allClasses.map(cls => cls._id);

      // Get all orders for the owner's classes within the date range
      const allOrders = await this.orderRepository.getAll(0, 1000, { 
        paid: true,
        'classId': { $in: classIds },
        'createdAt': {
          $gte: effectiveStartDate,
          $lte: effectiveEndDate
        }
      });

      // Calculate total revenue from all paid orders
      const totalRevenue = allOrders.reduce((sum, order) => sum + (order.amount || 0), 0);

      // Calculate monthly revenue for the selected date range
      const monthlyRevenues = [];
      const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
      
      let currentDate = new Date(effectiveStartDate);
      while (currentDate <= effectiveEndDate) {
        const month = currentDate.getMonth();
        const year = currentDate.getFullYear();
        const monthStart = new Date(year, month, 1);
        const monthEnd = new Date(year, month + 1, 0);
        
        const monthlyOrders = allOrders.filter(order => {
          const orderDate = new Date(order.createdAt);
          return orderDate >= monthStart && orderDate <= monthEnd;
        });
        
        const monthlyRevenue = monthlyOrders.reduce((sum, order) => sum + (order.amount || 0), 0);
        
        monthlyRevenues.push({
          month: monthNames[month],
          revenue: monthlyRevenue
        });

        currentDate.setMonth(currentDate.getMonth() + 1);
      }

      // Calculate monthly income change (current month vs last month)
      const currentMonthOrders = allOrders.filter(order => {
        const orderDate = new Date(order.createdAt);
        return orderDate >= currentMonthStart && orderDate <= currentMonthEnd;
      });
      
      const lastMonthOrders = allOrders.filter(order => {
        const orderDate = new Date(order.createdAt);
        return orderDate >= lastMonthStart && orderDate <= lastMonthEnd;
      });
      
      const currentMonthRevenue = currentMonthOrders.reduce((sum, order) => sum + (order.amount || 0), 0);
      const lastMonthRevenue = lastMonthOrders.reduce((sum, order) => sum + (order.amount || 0), 0);
      
      const monthlyIncomeChange = lastMonthRevenue > 0 
        ? Math.round(((currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100) 
        : 0;

      // Get unique students who have enrolled in classes within the date range
      const uniqueStudents = [...new Set(allOrders.map(order => order.childId?.toString()))].filter(Boolean);
      const totalStudents = uniqueStudents.length;

      // Calculate monthly student activity
      const activeStudentsThisMonth = [...new Set(
        currentMonthOrders.map(order => order.childId?.toString())
      )].filter(Boolean).length;
      
      const activeStudentsLastMonth = [...new Set(
        lastMonthOrders.map(order => order.childId?.toString())
      )].filter(Boolean).length;
      
      const monthlyStudentActivity = totalStudents > 0 
        ? Math.round((activeStudentsThisMonth / totalStudents) * 100)
        : 0;
        
      // Calculate student activity change percentage
      const studentActivityChange = activeStudentsLastMonth > 0
        ? Math.round(((activeStudentsThisMonth - activeStudentsLastMonth) / activeStudentsLastMonth) * 100)
        : 0;

      // Get total active programs (classes) for the owner
      const totalPrograms = [...new Set(allOrders.map(order => order.classId?.toString()))].filter(Boolean).length;

      return {
        totalRevenue,
        monthlyIncomeChange,
        monthlyStudentActivity,
        studentActivityChange,
        totalPrograms,
        totalStudents,
        monthlyRevenues,
        startDate: effectiveStartDate,
        endDate: effectiveEndDate
      };
    } catch (error) {
      console.error('Error in getOwnerDashboardData:', error);
      throw new Error('Failed to fetch dashboard data');
    }
  }

  // async updateBranch(branchId, branchData) {
  //   // Step 1: Fetch existing branch
  //   if (branchData == null) throw new Error("Nothing to update");
  //   const branch = await this.centerRepository.findById(branchId);
  //   if (!branch) {
  //     throw new Error("Branch does not exist");
  //   }

  //   // Step 2: Merge existing data with new data
  //   const updatedData = {
  //     ...(branch.toObject?.() ?? branch), // convert Mongoose doc to plain object if needed
  //     ...branchData,
  //   };

  //   // Step 3: Update the branch in DB
  //   const updatedBranch = await this.centerRepository.update(
  //     branchId,
  //     updatedData
  //   );

  //   // Step 4: Return updated branch
  //   return updatedBranch;
  // }

  async assignCoach(ownerId, coachId) {
    return await this.businessOwnerRepository.assignCoach(ownerId, coachId);
  }
}
module.exports = BusinessOwnerService;
