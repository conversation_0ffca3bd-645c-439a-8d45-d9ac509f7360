const ImageService = require("../services/imageService");
const crypto = require("crypto");
const path = require("path");
const fs = require("fs");

class UploadFile {
  constructor(imageService) {
    this.imageService = imageService || new ImageService(); // Allow dependency injection for flexibility
  }

  async uploadFile(file, fieldName, updateData, id) {
    // More detailed logging to help with debugging
    console.log(`----------------------`);
    console.log(`Starting upload for ${fieldName}...`);

    try {
      // Handle null or undefined file
      if (!file) {
        console.error(`File is null or undefined for ${fieldName}`);
        throw new Error(`Missing file data for ${fieldName}`);
      }

      // Log file properties for debugging
      console.log(`File object type: ${typeof file}`);
      console.log(`File object properties:`, Object.keys(file || {}));

      // Special handling for Flutter launcher icon
      if (typeof file === "object" && file.originalname === "ic_launcher.png") {
        console.log(`Detected Flutter launcher icon. Using special handling.`);
        // The launcher icon just needs special processing - we'll create a local copy
      }

      let filePath;
      let tempCreated = false;

      // Case 1: File has a path property (most common for server-side files)
      if (file.path && typeof file.path === "string") {
        console.log(`Using existing path: ${file.path}`);
        filePath = file.path;

        // Verify the path exists
        if (!fs.existsSync(filePath)) {
          console.error(`Path does not exist: ${filePath}`);
          // If it's the launcher icon and path doesn't exist, we'll handle it differently
          if (file.originalname === "ic_launcher.png") {
            console.log(`Creating temporary file for launcher icon`);
            filePath = await this._createTempFileForIcon(file);
            tempCreated = true;
          } else {
            throw new Error(`File path does not exist: ${filePath}`);
          }
        }
      }
      // Case 2: File has a url property
      else if (file.url && typeof file.url === "string") {
        console.log(`Using URL as path: ${file.url}`);
        filePath = file.url;

        // Handle URLs that point to local files
        if (file.url.startsWith("/") && fs.existsSync(file.url)) {
          filePath = file.url;
        }
      }
      // Case 3: File has a buffer property (from multipart form)
      else if (file.buffer) {
        console.log(
          `Processing file from buffer, size: ${file.buffer.length} bytes`
        );
        filePath = await this._createTempFileFromBuffer(file);
        tempCreated = true;
      }
      // Case 4: File might be a direct file object from Flutter with uri or path in data
      else if (file.uri && typeof file.uri === "string") {
        console.log(`Using URI as path: ${file.uri}`);
        filePath = file.uri;
      }
      // Case 5: Flutter might send the file as base64 data
      else if (
        file.data &&
        typeof file.data === "string" &&
        file.data.startsWith("data:")
      ) {
        console.log(`Processing file from base64 data`);
        filePath = await this._createTempFileFromBase64(file.data);
        tempCreated = true;
      }
      // Case 6: Flutter might send file properties in a nested structure
      else if (
        file.image &&
        (file.image.path || file.image.uri || file.image.data)
      ) {
        console.log(`Using nested image object properties`);
        return this.uploadFile(file.image, fieldName, updateData, id);
      }
      // Case 7: Flutter sends a filename with no path or data (common with ic_launcher.png)
      else if (
        file.originalname === "ic_launcher.png" ||
        (typeof file === "string" && file.includes("ic_launcher.png"))
      ) {
        console.log(`Processing Flutter launcher icon`);
        filePath = await this._createTempFileForIcon(file);
        tempCreated = true;
      }
      // No recognizable file format
      else {
        console.error("Unrecognized file format:", file);
        throw new Error(
          `Invalid file format for ${fieldName}: ${JSON.stringify(
            Object.keys(file)
          )}`
        );
      }

      if (!filePath) {
        throw new Error(`Could not determine file path for ${fieldName}`);
      }

      console.log(`Final file path to use: ${filePath}`);

      // Generate a unique filename using id and timestamp
      const timestamp = Date.now();
      let fileExt = path.extname(filePath) || ".jpg";

      if (file.originalname) {
        fileExt = path.extname(file.originalname) || fileExt;
      }

      // Ensure the ID doesn't contain spaces by replacing them with dashes
      const sanitizedId = id ? id.toString().replace(/\s+/g, "-") : "";
      const filename = `${timestamp}-${fieldName}-${sanitizedId}${fileExt}`;
      console.log(`Generated filename: ${filename}`);

      // Upload the image
      console.log(`Calling imageService.uploadImage with path: ${filePath}`);
      const { url, contentType } = await this.imageService.uploadImage(
        { path: filePath },
        filename
      );
      console.log(`Image uploaded successfully to: ${url}`);

      // Handle different field types (arrays vs single objects)
      if (
        [
          "images",
          "businessCertificate",
          "sexualConvictionRecord",
          "hkidCard",
        ].includes(fieldName)
      ) {
        if (!updateData[fieldName]) updateData[fieldName] = [];
        const fileObject = {
          url: this.imageService.convertToUrlPath(url),
          contentType,
        };
        updateData[fieldName].push(fileObject);

        console.log(`Added to ${fieldName} array:`, fileObject);
      } else {
        const fileObject = {
          url: this.imageService.convertToUrlPath(url),
          contentType,
        };
        updateData[fieldName] = fileObject;
        console.log(`Set ${fieldName} object:`, fileObject);
      }

      // Clean up temp file if we created one
      if (
        tempCreated &&
        filePath.includes("temp_") &&
        fs.existsSync(filePath)
      ) {
        try {
          fs.unlinkSync(filePath);
          console.log(`Cleaned up temporary file: ${filePath}`);
        } catch (error) {
          console.warn(`Failed to clean up temp file ${filePath}:`, error);
        }
      }

      console.log(`Upload completed successfully for ${fieldName}`);
      return true;
    } catch (error) {
      console.error(`Error uploading file for ${fieldName}:`, error);

      // Clean up temp file if we created one and an error occurred
      if (
        tempCreated &&
        filePath &&
        filePath.includes("temp_") &&
        fs.existsSync(filePath)
      ) {
        try {
          fs.unlinkSync(filePath);
          console.log(`Cleaned up temporary file after error: ${filePath}`);
        } catch (cleanupError) {
          console.warn(
            `Failed to clean up temp file ${filePath}:`,
            cleanupError
          );
        }
      }

      throw new Error(`Failed to upload ${fieldName}: ${error.message}`);
    }
  }

  // Helper method to create a temporary file from a buffer
  async _createTempFileFromBuffer(file) {
    // Create a temporary file from the buffer
    const tempDir = path.join(__dirname, "../../temp");
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Get an appropriate filename and extension
    let fileExt = ".jpg"; // Default extension
    if (file.originalname) {
      fileExt = path.extname(file.originalname) || fileExt;
    } else if (file.mimetype) {
      // Get extension from mimetype
      const extensions = {
        "image/jpeg": ".jpg",
        "image/png": ".png",
        "image/gif": ".gif",
        "application/pdf": ".pdf",
      };
      fileExt = extensions[file.mimetype] || fileExt;
    }

    const tempFilename = `temp_${Date.now()}_${Math.round(
      Math.random() * 1e9
    )}${fileExt}`;
    const tempFilePath = path.join(tempDir, tempFilename);

    console.log(`Creating temporary file from buffer: ${tempFilePath}`);
    fs.writeFileSync(tempFilePath, file.buffer);
    return tempFilePath;
  }

  // Helper method to create a temporary file from base64 data
  async _createTempFileFromBase64(base64Data) {
    // Create a temporary file from the base64 data
    const tempDir = path.join(__dirname, "../../temp");
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Extract data from base64 data URL
    const matches = base64Data.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
    if (!matches || matches.length !== 3) {
      throw new Error("Invalid base64 data");
    }

    const mimeType = matches[1];
    const base64Content = matches[2];
    const buffer = Buffer.from(base64Content, "base64");

    // Get extension from mimetype
    const extensions = {
      "image/jpeg": ".jpg",
      "image/png": ".png",
      "image/gif": ".gif",
      "application/pdf": ".pdf",
    };
    const fileExt = extensions[mimeType] || ".bin";

    const tempFilename = `temp_${Date.now()}_${Math.round(
      Math.random() * 1e9
    )}${fileExt}`;
    const tempFilePath = path.join(tempDir, tempFilename);

    console.log(`Creating temporary file from base64 data: ${tempFilePath}`);
    fs.writeFileSync(tempFilePath, buffer);
    return tempFilePath;
  }

  // Special helper for handling the Flutter launcher icon
  async _createTempFileForIcon(file) {
    // For Flutter launcher icon or similar cases, create an empty PNG
    console.log("Creating a substitute file for Flutter launcher icon");

    // Create temp directory if it doesn't exist
    const tempDir = path.join(__dirname, "../../temp");
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const tempFilename = `temp_ic_launcher_${Date.now()}.png`;
    const tempFilePath = path.join(tempDir, tempFilename);

    // Create a minimal valid PNG file (1x1 transparent pixel)
    // This is a base64 encoded 1x1 transparent PNG
    const minimalPngBase64 =
      "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=";
    const buffer = Buffer.from(minimalPngBase64, "base64");

    fs.writeFileSync(tempFilePath, buffer);
    console.log(`Created temporary icon file: ${tempFilePath}`);
    return tempFilePath;
  }
}

module.exports = UploadFile;
