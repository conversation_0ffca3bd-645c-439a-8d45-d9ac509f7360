const { generateEntityId } = require("../use_case/classzIdGenerate");
class RoleService {
  constructor({
    userRepository,
    businessOwnerRepository,
    coachRepository,
    centerRepository,
  }) {
    this.repositories = {
      parent: userRepository,
      owner: businessOwnerRepository,
      center: centerRepository,
      coach: coachRepository,
      manager: centerRepository,
    };
  }

  async getUserRoles(user, email, type = null) {
    console.log(type);
    let data = { parent: {}, owner: {}, center: {}, coach: {} };
    console.log(user);
    if (type && type === "parent" && user.roles.includes(type)) {
      if (this.repositories[type]) {
        return {
          [type.toLowerCase()]: await this.findOrCreate({
            repository: this.repositories[type],
            email,
            userId: user.id,
          }),
        };
      }
    }

    for (const role of user.roles) {
      console.log(`role :${role}`);
      console.log(data);
      if (role == "parent") continue;
      if (role == "manager") {
        data["center"] = await this.repositories["manager"].findById(
          data["coach"].manager
        );
        // await this.findOrCreate({
        //   repository: this.repositories[role],
        //   centerId: data["coach"].manager || null,
        // });
        continue;
      }
      if (this.repositories[role]) {
        data[role.toLowerCase()] = await this.findOrCreate({
          repository: this.repositories[role],
          email,
          userId: user.id,
        });
      }
    }

    return data;
  }

  async assignRole(userId, email, type) {
    if (!this.repositories[type]) {
      throw new Error("Invalid role type");
    }
    console.log(`type: ${type}`);
    // Generate classz ID based on type
    let classzId;
    switch (type) {
      case "parent":
        classzId = generateEntityId("parent");
        break;
      case "owner":
        classzId = generateEntityId("business");
        break;
      case "coach":
        classzId = generateEntityId("coach");
        break;
      case "center":
        // For center, we'll need the business ID which will be handled in roleService
        classzId = generateEntityId("center");
        break;
      default:
        classzId = null;
    }
    console.log(classzId);
    return this.findOrCreate({
      repository: this.repositories[type],
      email,
      userId,
      classzId,
    });
  }

  async findOrCreate({
    repository,
    email,
    userId,
    centerId,
    coachId = null,
    classzId = null,
  }) {
    console.log(`hi ${coachId}`);
    if (coachId != null) {
      console.log("MANAGER");
      console.log(repository);
      // If coachId is provided, fetch the center by manager (coachId)
      return repository.findCenterByManager(coachId);
    }
    console.log("ADJlk");
    // If no coachId, search by email
    let entity = await repository.findByEmail(email);

    // If the entity doesn't exist, create and save a new one
    if (!entity) {
      entity = await repository.save({
        email,
        baseUser: userId,
        classzId: classzId,
      });
    }

    return entity;
  }
}

module.exports = RoleService;
