class DiscountService {
  constructor(discountRepo) {
    this.discountRepo = discountRepo;
  }

  async createDiscount(data) {
    console.log(data);
    return await this.discountRepo.createDiscount(data);
  }

  async getAllDiscounts() {
    return await this.discountRepo.getAllDiscounts();
  }

  async getDiscountByCode(code) {
    const discount = await this.discountRepo.getDiscountByCode(code);
    if (!discount) throw new Error("Discount code not found.");
    return discount;
  }

  async getUserSpecificDiscounts(userId) {
    return await this.discountRepo.getUserSpecificDiscounts(userId);
  }

  async getUniversalDiscounts() {
    return await this.discountRepo.getUniversalDiscounts();
  }
  async getById(userId) {
    return await this.discountRepo.getById(userId);
  }
  async deleteDiscount(code) {
    const deletedDiscount = await this.discountRepo.deleteDiscount(code);
    if (!deletedDiscount) throw new Error("Discount not found.");
    return { message: "Discount deleted successfully." };
  }
  async applyDiscount(code, userId, originalPrice) {
    const discount = await this.discountRepo.getDiscountByCode(code);
    if (!discount) throw new Error("Invalid discount code.");
    if (new Date() > discount.validUntil)
      throw new Error("Discount code has expired.");
    if (discount.userSpecific && !discount.allowedUsers.includes(userId)) {
      throw new Error("This discount is not applicable for you.");
    }
    let finalPrice = originalPrice;
    if (discount.discountType === "percentage") {
      finalPrice -= (originalPrice * discount.discountValue) / 100;
    } else if (discount.discountType === "fixed") {
      finalPrice -= discount.discountValue;
    }

    return { finalPrice: Math.max(finalPrice, 0) };
  }
}

module.exports = DiscountService;
