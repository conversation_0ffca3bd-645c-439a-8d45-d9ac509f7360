class CampaignService {
  constructor(campaignRepo) {
    this.campaignRepo = campaignRepo;
  }

  async createCampaign(campaignData) {
    try {
      // Validate date range
      const validFrom = new Date(campaignData.validFrom);
      const validUntil = new Date(campaignData.validUntil);
      
      if (validFrom >= validUntil) {
        throw new Error("Valid from date must be before valid until date");
      }

      // Validate discount percentage if provided
      if (campaignData.discountPercentage && (campaignData.discountPercentage < 0 || campaignData.discountPercentage > 100)) {
        throw new Error("Discount percentage must be between 0 and 100");
      }

      return await this.campaignRepo.createCampaign(campaignData);
    } catch (error) {
      throw new Error(`Campaign creation failed: ${error.message}`);
    }
  }

  async getActiveCampaigns(targetAudience = "all") {
    try {
      // First deactivate any expired campaigns
      await this.campaignRepo.deactivateExpiredCampaigns();
      
      // Then fetch active campaigns
      const campaigns = await this.campaignRepo.findActiveCampaigns(targetAudience);
      
      return campaigns;
    } catch (error) {
      throw new Error(`Failed to get active campaigns: ${error.message}`);
    }
  }

  async getAllCampaigns(page = 1, limit = 10) {
    try {
      return await this.campaignRepo.findAllCampaigns(page, limit);
    } catch (error) {
      throw new Error(`Failed to get all campaigns: ${error.message}`);
    }
  }

  async getCampaignById(campaignId) {
    try {
      const campaign = await this.campaignRepo.findCampaignById(campaignId);
      if (!campaign) {
        throw new Error("Campaign not found");
      }
      return campaign;
    } catch (error) {
      throw new Error(`Failed to get campaign: ${error.message}`);
    }
  }

  async updateCampaign(campaignId, updateData) {
    try {
      // Validate date range if dates are being updated
      if (updateData.validFrom || updateData.validUntil) {
        const campaign = await this.campaignRepo.findCampaignById(campaignId);
        if (!campaign) {
          throw new Error("Campaign not found");
        }

        const validFrom = new Date(updateData.validFrom || campaign.validFrom);
        const validUntil = new Date(updateData.validUntil || campaign.validUntil);
        
        if (validFrom >= validUntil) {
          throw new Error("Valid from date must be before valid until date");
        }
      }

      // Validate discount percentage if provided
      if (updateData.discountPercentage && (updateData.discountPercentage < 0 || updateData.discountPercentage > 100)) {
        throw new Error("Discount percentage must be between 0 and 100");
      }

      const updatedCampaign = await this.campaignRepo.updateCampaign(campaignId, updateData);
      if (!updatedCampaign) {
        throw new Error("Campaign not found");
      }
      return updatedCampaign;
    } catch (error) {
      throw new Error(`Campaign update failed: ${error.message}`);
    }
  }

  async deleteCampaign(campaignId) {
    try {
      const deletedCampaign = await this.campaignRepo.deleteCampaign(campaignId);
      if (!deletedCampaign) {
        throw new Error("Campaign not found");
      }
      return { message: "Campaign deleted successfully" };
    } catch (error) {
      throw new Error(`Campaign deletion failed: ${error.message}`);
    }
  }

  // Add missing methods that the controller needs
  async incrementImpressionCount(campaignId) {
    try {
      return await this.campaignRepo.incrementImpressionCount(campaignId);
    } catch (error) {
      throw new Error(`Failed to increment impression count: ${error.message}`);
    }
  }

  async recordCampaignClick(campaignId) {
    try {
      return await this.campaignRepo.incrementClickCount(campaignId);
    } catch (error) {
      throw new Error(`Failed to record campaign click: ${error.message}`);
    }
  }

  async handleCampaignClick(campaignId) {
    try {
      return await this.campaignRepo.incrementClickCount(campaignId);
    } catch (error) {
      throw new Error(`Failed to handle campaign click: ${error.message}`);
    }
  }

  async getCampaignAnalytics(campaignId) {
    try {
      const campaign = await this.campaignRepo.findCampaignById(campaignId);
      if (!campaign) {
        throw new Error("Campaign not found");
      }

      return {
        campaignId: campaign._id,
        title: campaign.title,
        impressions: campaign.impressionCount,
        clicks: campaign.clickCount,
        clickThroughRate: campaign.impressionCount > 0 ? 
          ((campaign.clickCount / campaign.impressionCount) * 100).toFixed(2) : 0,
        isActive: campaign.isActive,
        validFrom: campaign.validFrom,
        validUntil: campaign.validUntil
      };
    } catch (error) {
      throw new Error(`Failed to get campaign analytics: ${error.message}`);
    }
  }
}

module.exports = CampaignService;