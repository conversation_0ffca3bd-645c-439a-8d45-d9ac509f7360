const fs = require("fs");
const fsPromises = require("fs").promises;
const path = require("path");
const mime = require("mime-types");

class ImageService {
  async uploadImage(fileData, fileName) {
    console.log(`[ImageService] Starting uploadImage for file: ${fileName}`);
    
    // Sanitize the fileName to ensure no spaces or special characters
    fileName = fileName.replace(/\s+/g, '-').replace(/[^a-zA-Z0-9_.-]/g, '');
    console.log(`[ImageService] Sanitized fileName: ${fileName}`);
    
    // Ensure the upload directory structure exists
    const uploadDir = path.resolve(__dirname, "..", "..", "..", "uploads");
    console.log(`[ImageService] Upload directory: ${uploadDir}`);

    try {
      if (!fs.existsSync(uploadDir)) {
        console.log(`[ImageService] Creating upload directory: ${uploadDir}`);
        await fsPromises.mkdir(uploadDir, { recursive: true });
      }
    } catch (error) {
      console.error(`[ImageService] Error creating upload directory:`, error);
      throw new Error(`Failed to create upload directory: ${error.message}`);
    }

    // Get the source file path
    let oldPath = null;
    if (fileData.path) {
      oldPath = fileData.path;
    } else if (fileData.url) {
      oldPath = fileData.url;
    } else if (fileData.uri) {
      oldPath = fileData.uri;
    }
    
    console.log(`[ImageService] Source file path: ${oldPath}`);
    
    if (!oldPath) {
      console.error(`[ImageService] No source path found in fileData:`, fileData);
      throw new Error("No source file path provided");
    }
    
    if (!fs.existsSync(oldPath)) {
      console.error(`[ImageService] Source file does not exist at: ${oldPath}`);
      throw new Error(`Source file does not exist at the given path: ${oldPath}`);
    }

    // Check if file already exists in the target directory and create a unique name if needed
    let filePath = path.join(uploadDir, fileName);
    
    if (fs.existsSync(filePath)) {
      console.log(`[ImageService] File already exists, generating unique name`);
      
      // Add timestamp if not already in the filename
      const timestamp = Date.now();
      const uniqueFileName = `${timestamp}-${fileName}`;
      filePath = path.join(uploadDir, uniqueFileName);
      console.log(`[ImageService] New unique filePath: ${filePath}`);
    }

    // Move the file to the upload directory using a more reliable copy + delete approach
    try {
      console.log(`[ImageService] Copying from ${oldPath} to ${filePath}`);
      
      // Copy file contents instead of rename to avoid cross-device link errors
      await fsPromises.copyFile(oldPath, filePath);
      
      // Verify the file was copied successfully
      if (!fs.existsSync(filePath)) {
        throw new Error("File copy failed - destination file does not exist");
      }
      
      // Set proper permissions on the new file
      await fsPromises.chmod(filePath, 0o644);
      
      console.log(`[ImageService] File copied successfully to: ${filePath}`);
      
      // Don't delete source file if it's not a temp file (to prevent accidental data loss)
      if (oldPath.includes('temp_')) {
        try {
          await fsPromises.unlink(oldPath);
          console.log(`[ImageService] Removed source temp file: ${oldPath}`);
        } catch (unlinkError) {
          console.warn(`[ImageService] Warning: Could not remove source temp file:`, unlinkError);
          // Continue even if we can't delete the source file
        }
      }
    } catch (error) {
      console.error(`[ImageService] File copy error:`, error);
      throw new Error(`Failed to copy file: ${error.message}`);
    }

    // Get the MIME type of the file
    const contentType = mime.lookup(fileName) || "application/octet-stream";
    console.log(`[ImageService] Determined contentType: ${contentType}`);

    // Get the relative path for use in URLs
    const relativeFilePath = path.basename(filePath);
    const urlPath = `/uploads/${encodeURIComponent(relativeFilePath)}`;
    
    console.log(`[ImageService] Upload complete. URL path: ${urlPath}`);
    
    // Return the URL path and content type
    return {
      url: urlPath,
      contentType: contentType,
    };
  }

  convertToUrlPath(filePath) {
    // Convert any file path to the correct URL format
    if (!filePath) {
      console.error('[ImageService] Error: Null or undefined file path provided to convertToUrlPath');
      return '';
    }
    
    console.log(`[ImageService] Converting path to URL: ${filePath}`);
    
    if (filePath.startsWith('/uploads/')) {
      console.log(`[ImageService] Path already in correct URL format: ${filePath}`);
      return filePath; // Already in the right format
    }
    
    // Extract just the filename from the path
    const filename = path.basename(filePath);
    const urlPath = `/uploads/${encodeURIComponent(filename)}`;
    
    console.log(`[ImageService] Converted to URL path: ${urlPath}`);
    return urlPath;
  }
}

module.exports = ImageService;
