const NotificationService = require("./notificationService");

class CancellationNotificationService {
  constructor({ notificationRepository }) {
    this.notificationRepository = notificationRepository;
  }

  /**
   * Send refund notifications to students
   * @param {Array} refundResults - Array of refund result objects
   * @param {Object} classInfo - Class information
   */
  async sendRefundNotifications(refundResult, parentId, classInfo) {
    try {
      console.log(`Sending refund notifications to ${refundResult} students`);

      const notifications = [];

      const notificationData = {
        userId: parentId,
        title: "Class Cancelled - Refund Processed",
        body: `Your class "${classInfo.classProviding}" has been cancelled. A refund of $${refundResult.amount} has been processed and will be credited to your account within 3-5 business days.`,
        data: {
          type: "refund",
          classId: String(classInfo._id),
          className: String(classInfo.classProviding),
          refundAmount: String(refundResult.refundAmount),
          refundDate: String(refundResult.processedAt),
          status: "processed",
        },
      };

      // Create notification in database
      await NotificationService.sendNotification(parentId, notificationData);

      return {
        success: true,
        sent: 1,
        failed: 0,
        notifications: notifications,
      };
    } catch (error) {
      console.error("Error sending refund notifications:", error);
      throw new Error(`Failed to send refund notifications: ${error.message}`);
    }
  }

  /**
   * Send rearrangement notifications to students
   * @param {Array} students - Array of student IDs or objects
   * @param {Object} classInfo - Class information
   */
  async sendRearrangementNotifications(
    student,
    originalDate,
    newDate,
    classInfo
  ) {
    try {
      console.log(
        `Sending rearrangement notification to student: ${student.parent}`
      );

      const notificationData = {
        userId: student.parent,
        title: "Class Rescheduling Notice",
        body: `Your class "${classInfo.classProviding}" which was scheduled at ${originalDate} will rearranged to ${newDate}`,
        data: {
          type: "rearrangement",
          classId: String(classInfo._id),
          className: String(classInfo.classProviding),
          originalDate: String(classInfo.dates?.[0]?.date || "TBD"),
          status: "pending_rearrangement",
          rearrangedAt: new Date().toISOString(),
        },
      };

      // Save the notification
      await NotificationService.sendNotification(
        student.parent,
        notificationData
      );

      console.log(
        `Rearrangement notification sent to student: ${student.parent}`
      );

      return {
        success: true,
        sent: 1,
        failed: 0,
        //  notifications: [notification],
      };
    } catch (error) {
      console.error("Failed to send rearrangement notification:", error);
      throw new Error(
        `Failed to send rearrangement notification: ${error.message}`
      );
    }
  }

  /**
   * Send general class cancellation notification
   * @param {Array} students - Array of student IDs or objects
   * @param {Object} classInfo - Class information
   * @param {String} reason - Cancellation reason
   */
  async sendCancellationNotifications(
    students,
    classInfo,
    reason = "Class has been cancelled"
  ) {
    try {
      console.log(
        `Sending cancellation notifications to ${students.length} students`
      );

      const notifications = [];

      for (const student of students) {
        const studentId = student._id || student;

        const notificationData = {
          userId: studentId,
          title: "Class Cancelled",
          body: `Your class "${classInfo.classProviding}" has been cancelled. ${reason}`,
          data: {
            type: "cancellation",
            classId: classInfo._id,
            className: classInfo.classProviding,
            cancelledAt: new Date(),
            reason: reason,
          },
        };

        // Create notification in database
        try {
          const notification =
            await this.notificationRepository.saveNotification(
              notificationData
            );
          notifications.push(notification);
          console.log(
            `Cancellation notification sent to student: ${studentId}`
          );
        } catch (error) {
          console.error(
            `Failed to send cancellation notification to student ${studentId}:`,
            error
          );
        }
      }

      return {
        success: true,
        sent: notifications.length,
        failed: students.length - notifications.length,
        notifications: notifications,
      };
    } catch (error) {
      console.error("Error sending cancellation notifications:", error);
      throw new Error(
        `Failed to send cancellation notifications: ${error.message}`
      );
    }
  }
}

module.exports = CancellationNotificationService;
