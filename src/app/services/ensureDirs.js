/**
 * Utility script to ensure that all required directories exist
 * and have the proper permissions for file uploads
 */

const fs = require('fs');
const path = require('path');

// Directories that need to exist
const requiredDirs = [
  path.resolve(__dirname, '../../temp'),
  path.resolve(__dirname, '../../../uploads')
];

/**
 * Ensures all required directories exist and have proper permissions
 */
function ensureDirectories() {
  console.log('Ensuring required directories exist...');
  
  requiredDirs.forEach(dir => {
    try {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true, mode: 0o755 });
        console.log(`Created directory: ${dir}`);
      } else {
        // Update permissions if the directory already exists
        fs.chmodSync(dir, 0o755);
        console.log(`Updated permissions for existing directory: ${dir}`);
      }
    } catch (error) {
      console.error(`Error creating/updating directory ${dir}:`, error);
    }
  });
  
  console.log('Directory check complete.');
}

// Run when this file is executed directly
if (require.main === module) {
  ensureDirectories();
}

module.exports = ensureDirectories; 