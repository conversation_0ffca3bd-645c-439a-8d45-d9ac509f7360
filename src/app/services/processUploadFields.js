class UploadProcessor {
  constructor(uploadFileService) {
    this.uploadFileService = uploadFileService;
  }

  async processUploadFields(centerData, baseUserId) {
    const fields = ["businessCertificate", "sexualConvictionRecord", "hkidCard", "mainImage", "images"];
    console.log("Processing upload fields:", fields);
    for (const fieldName of fields) {
      if (centerData[fieldName] && Array.isArray(centerData[fieldName])) {
        console.log(
          `Processing multiple files for ${fieldName}:`,
          centerData[fieldName]
        );
        for (const file of centerData[fieldName]) {
          if (!file.path) {
            console.error(`Missing path for file in ${fieldName}:`, file);
            continue; // Skip files without path
          }
          await this.uploadFileService.uploadFile(
            file,
            fieldName,
            centerData,
            baseUserId
          );
        }
        centerData.images = centerData.images.filter(
          (img) => img.url && img.url.startsWith("/uploads/")
        );
        // centerData.businessCertificate = centerData.businessCertificate.filter(
        //   (img) => img.url && img.url.startsWith("/uploads/")
        // );
        // centerData.hkidCard = centerData.hkidCard.filter(
        //   (img) => img.url && img.url.startsWith("/uploads/")
        // );
      } else if (centerData[fieldName]) {
        console.log(
          `Processing single file for ${fieldName}:`,
          centerData[fieldName]
        );
        if (!centerData[fieldName].path) {
          throw new Error(`Missing path for single file in ${fieldName}`);
        }
        await this.uploadFileService.uploadFile(
          centerData[fieldName],
          fieldName,
          centerData,
          baseUserId
        );
      }
    }
  }
}

module.exports = UploadProcessor;
