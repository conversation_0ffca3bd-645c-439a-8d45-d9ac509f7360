class CampaignDiscountService {
  constructor(campaignRepo, discountRepo) {
    this.campaignRepo = campaignRepo;
    this.discountRepo = discountRepo;
  }

  /**
   * Get all active campaigns and create/update corresponding discount codes
   */
  async syncCampaignDiscounts() {
    try {
      const activeCampaigns = await this.campaignRepo.findActiveCampaigns();
      const synced = [];

      for (const campaign of activeCampaigns) {
        if (campaign.discountPercentage > 0 && campaign.discountCode) {
          const discount = await this.createOrUpdateDiscountFromCampaign(campaign);
          synced.push(discount);
        }
      }

      return synced;
    } catch (error) {
      throw new Error(`Failed to sync campaign discounts: ${error.message}`);
    }
  }

  /**
   * Create or update a discount code from a campaign
   */
  async createOrUpdateDiscountFromCampaign(campaign) {
    try {
      // Check if discount already exists
      const existingDiscount = await this.discountRepo.getDiscountByCode(campaign.discountCode);

      const discountData = {
        code: campaign.discountCode,
        discountPercentage: campaign.discountPercentage,
        discountType: 'percentage',
        validUntil: campaign.validUntil,
        userSpecific: false, // Universal discount for all users
        allowedUsers: [],
        isActive: campaign.isActive,
        maxUsage: 999999, // High number for campaign discounts
        usageCount: 0,
        // Add campaign reference
        campaignId: campaign._id,
        description: `${campaign.title} - ${campaign.subtitle}`
      };

      if (existingDiscount) {
        // Update existing discount
        return await this.discountRepo.updateDiscount(campaign.discountCode, discountData);
      } else {
        // Create new discount
        return await this.discountRepo.createDiscount(discountData);
      }
    } catch (error) {
      throw new Error(`Failed to create discount from campaign: ${error.message}`);
    }
  }

  /**
   * Get available campaign discounts for a user
   */
  async getAvailableCampaignDiscounts(userId) {
    try {
      const activeCampaigns = await this.campaignRepo.findActiveCampaigns();
      const availableDiscounts = [];

      for (const campaign of activeCampaigns) {
        if (campaign.discountPercentage > 0 && campaign.discountCode) {
          const discount = await this.discountRepo.getDiscountByCode(campaign.discountCode);
          if (discount && discount.isActive) {
            availableDiscounts.push({
              campaign: {
                id: campaign._id,
                title: campaign.title,
                subtitle: campaign.subtitle,
                backgroundColor: campaign.backgroundColor,
                textColor: campaign.textColor
              },
              discount: {
                code: discount.code,
                discountPercentage: discount.discountPercentage,
                discountType: discount.discountType,
                validUntil: discount.validUntil
              }
            });
          }
        }
      }

      return availableDiscounts;
    } catch (error) {
      throw new Error(`Failed to get campaign discounts: ${error.message}`);
    }
  }

  /**
   * Validate and apply campaign discount
   */
  async applyCampaignDiscount(discountCode, originalPrice) {
    try {
      const discount = await this.discountRepo.getDiscountByCode(discountCode);
      
      if (!discount) {
        throw new Error("Invalid discount code");
      }

      if (!discount.isActive) {
        throw new Error("Discount code is not active");
      }

      if (new Date() > new Date(discount.validUntil)) {
        throw new Error("Discount code has expired");
      }

      let finalPrice = originalPrice;
      if (discount.discountType === "percentage") {
        finalPrice = originalPrice - (originalPrice * discount.discountPercentage / 100);
      } else if (discount.discountType === "fixed") {
        finalPrice = originalPrice - discount.discountPercentage;
      }

      return {
        success: true,
        originalPrice,
        discountAmount: originalPrice - finalPrice,
        finalPrice: Math.max(finalPrice, 0),
        discountCode: discount.code,
        discountPercentage: discount.discountPercentage
      };
    } catch (error) {
      throw new Error(`Failed to apply campaign discount: ${error.message}`);
    }
  }
}

module.exports = CampaignDiscountService;