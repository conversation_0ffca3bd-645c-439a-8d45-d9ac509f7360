const INotificationService = require("../../interfaces/INotificationService");
const NotificationRepository = require("../repo/notificationRepo");
const ExpUpdateUseCase = require("../use_case/expUpdateUseCase");
const notificationRepository = new NotificationRepository();
const admin = require("./firebaseService");
const cron = require("node-cron");
class NotificationService extends INotificationService {
  constructor({ notificationRepository }) {
    super();
    this.notificationRepository = notificationRepository;
  }

  /**
   * Send refund notifications to students
   * @param {Array} refundResults - Array of refund result objects
   * @param {Object} classInfo - Class information
   */
  async sendRefundNotifications(refundResults, classInfo) {
    try {
      console.log(
        `Sending refund notifications to ${refundResults.length} students`
      );

      const notifications = [];

      for (const refund of refundResults) {
        const notificationData = {
          userId: refund.studentId,
          title: "Class Cancelled - Refund Processed",
          body: `Your class "${classInfo.classProviding}" has been cancelled. A refund of $${refund.refundAmount} has been processed and will be credited to your account within 3-5 business days.`,
          data: {
            type: "refund",
            classId: classInfo._id,
            className: classInfo.classProviding,
            refundAmount: refund.refundAmount,
            refundDate: refund.processedAt,
            status: "processed",
          },
        };

        // Create notification in database
        try {
          const notification =
            await this.notificationRepository.saveNotification(
              notificationData
            );
          notifications.push(notification);
          await NotificationService.sendNotification(
            refund.studentId,
            notificationData
          );
          console.log(
            `Refund notification sent to student: ${refund.studentId}`
          );
        } catch (error) {
          console.error(
            `Failed to send refund notification to student ${refund.studentId}:`,
            error
          );
        }
      }

      return {
        success: true,
        sent: notifications.length,
        failed: refundResults.length - notifications.length,
        notifications: notifications,
      };
    } catch (error) {
      console.error("Error sending refund notifications:", error);
      throw new Error(`Failed to send refund notifications: ${error.message}`);
    }
  }

  /**
   * Send rearrangement notifications to students
   * @param {Array} students - Array of student IDs or objects
   * @param {Object} classInfo - Class information
   */
  async sendRearrangementNotifications(student, classInfo) {
    try {
     

      console.log(
        `Sending rearrangement notification to student: ${student.parent}`
      );

      const notificationData = {
        userId: student.parent,
        title: "Class Rescheduling Notice",
        body: `Your class "${classInfo.classProviding}" needs to be rescheduled. We will notify you with the new date and time as soon as it's available. We apologize for any inconvenience.`,
        data: {
          type: "rearrangement",
          classId: classInfo._id,
          className: classInfo.classProviding,
          originalDate: classInfo.dates?.[0]?.date || "TBD",
          status: "pending_rearrangement",
          rearrangedAt: new Date(),
        },
      };

      // Save the notification
      await NotificationService.sendNotification(
        refund.studentId,
        notificationData
      );

      console.log(`Rearrangement notification sent to student: ${student.parent}`);

      return {
        success: true,
        sent: 1,
        failed: 0,
      //  notifications: [notification],
      };
    } catch (error) {
      console.error("Failed to send rearrangement notification:", error);
      throw new Error(
        `Failed to send rearrangement notification: ${error.message}`
      );
    }
  }

  /**
   * Send general class cancellation notification
   * @param {Array} students - Array of student IDs or objects
   * @param {Object} classInfo - Class information
   * @param {String} reason - Cancellation reason
   */
  async sendCancellationNotifications(
    students,
    classInfo,
    reason = "Class has been cancelled"
  ) {
    try {
      console.log(
        `Sending cancellation notifications to ${students.length} students`
      );

      const notifications = [];

      for (const student of students) {
        const studentId = student._id || student;

        const notificationData = {
          userId: studentId,
          title: "Class Cancelled",
          body: `Your class "${classInfo.classProviding}" has been cancelled. ${reason}`,
          data: {
            type: "cancellation",
            classId: classInfo._id,
            className: classInfo.classProviding,
            cancelledAt: new Date(),
            reason: reason,
          },
        };

        // Create notification in database
        try {
          const notification =
            await this.notificationRepository.saveNotification(
              notificationData
            );
          notifications.push(notification);
          console.log(
            `Cancellation notification sent to student: ${studentId}`
          );
        } catch (error) {
          console.error(
            `Failed to send cancellation notification to student ${studentId}:`,
            error
          );
        }
      }

      return {
        success: true,
        sent: notifications.length,
        failed: students.length - notifications.length,
        notifications: notifications,
      };
    } catch (error) {
      console.error("Error sending cancellation notifications:", error);
      throw new Error(
        `Failed to send cancellation notifications: ${error.message}`
      );
    }
  }

  // Static method to get token by userId
  static async getTokenById(userId) {
    try {
      // Use the repository directly (create a new instance if needed)
      const NotificationRepository = require("../repo/notificationRepo");
      const notificationRepository = new NotificationRepository();
      return await notificationRepository.getTokenById(userId);
    } catch (error) {
      console.error("Error in NotificationService.getTokenById:", error);
      throw error;
    }
  }
  async saveOrUpdateToken(userId, token) {
    console.log("Checking FCM token update for user:", userId);

    // Fetch the existing token
    const existingTokenData = await notificationRepository.getTokenById(userId);

    // Check if the token already exists and is the same
    if (existingTokenData && existingTokenData.token === token) {
      console.log("✅ Token is already up-to-date, no need to update.");
      return existingTokenData; // Return existing data without updating
    }

    // Update only if the token is different
    console.log("🔄 Updating FCM token...");
    const updatedToken = await notificationRepository.saveOrUpdateToken(
      userId,
      token
    );
    console.log("✅ Token updated:", updatedToken);

    return updatedToken;
  }

  static async getTokenById(userId) {
    console.log(userId);
    const token = await notificationRepository.getTokenById(userId);
    // if (token) {
    //   const payload = {
    //     title: "check",
    //     body: "how r u 3",
    //     url: "yourapp://notification",
    //   };
    //   const date = "44 11 * * *";
    //   await NotificationService.scheduleNotification(
    //     userId,
    //     token.token,
    //     payload,
    //     date
    //   );
    // }
    return token;
  }
  async getNotification(userId) {
    const notifications = await notificationRepository.getNotification(userId);
    return notifications;
  }
  static async scheduleNotification(userId, token, message, date) {
    console.log("Scheduling job with date:", date); // Debugging statement
    const job = cron.schedule(
      date,
      async () => {
        console.log("Cron job triggered at", new Date()); // Debugging statement
        await NotificationService.sendNotification(userId, token, message);
      },
      { scheduled: true, timezone: "Asia/Dhaka" }
    );
    console.log("Job scheduled"); // Debugging statement return job;
  }
  static async sendNotification(userId, message) {
    try {
      console.log("Sending notification...");
      console.log("User ID:", userId);
      console.log("Message:", message);

      // Step 1: Save the notification to the database
      const notification = await notificationRepository.saveNotification(
        message
      );
      console.log("Notification saved to the database. ID:", notification.id);

      // Step 2: Construct the payload
      const payload = {
        notification: {
          title: message.title,
          body: message.body,
        },
        data: {
          ...(message.data || {}),
          notificationId: notification.id.toString(), // Add the saved notification ID
        },
      };

      // Step 3: Fetch token data for the user
      const tokenData = await notificationRepository.getTokenById(userId);

      // Handle case where no token is found
      if (!tokenData || !tokenData.token) {
        console.log(`No FCM token found for user: ${userId}`);

        return {
          status: "saved",
          message: "Notification saved (no token available).",
        };
      }

      console.log("FCM Token for User:", tokenData.token);
      const token = tokenData.token;

      // Add the recipient's token to the payload
      payload.token = token;
      console.log("Payload for FCM:", payload);
      // Send the notification using Firebase Cloud Messaging
      try {
        const response = await admin.messaging().send(payload);
        console.log("Notification sent successfully:", response);

        return {
          status: "sent",
          message: "Notification sent successfully.",
          response,
        };
      } catch (error) {
        console.log(`Error:${error.message}`);
      }
    } catch (error) {
      console.error("Error handling notification:", error.message);
      throw new Error(`Error sending notification: ${error.message}`);
    }
  }
}

module.exports = NotificationService;
