const otpGenerator = require("../../utils/otpGenerator");
const dateUtils = require("../../utils/dateUtils");

class OtpService {
  constructor(otpRepo, emailService, baseUserRepository) {
    this.otpRepo = otpRepo;
    this.emailService = emailService;
    this.baseUserRepository = baseUserRepository;
  }

  // Generate OTP and send via email
  async getOtpAndSendEmail(email) {
    // First check if the email already exists in baseUser
    // const existingUser = await this.baseUserRepository.findByEmail(email);
    // if (existingUser) {
    //   throw new Error("User already exists with this email");
    // }

    const otp = otpGenerator.generateOtp();
    const expirationTime = dateUtils.addMinutes(5);  // 5-minute expiry
    await this.otpRepo.storeOtp(email, otp, expirationTime);
    await this.emailService.sendOtp(email, otp);

    console.log(`Generated OTP: ${otp}, Expiration Time: ${expirationTime}`);
  }

  // Verify OTP
  async verifyOtp(email, otp) {
    const storedOtp = await this.otpRepo.getOtp(email);

    if (!storedOtp) {
      throw new Error("No OTP found for this email");
    }

    const isValid = dateUtils.validateTimestamp(storedOtp.expirationTime);
    if (!isValid) {
      await this.otpRepo.deleteOtp(email);  // Delete expired OTP
      throw new Error("OTP expired");
    }
console.log(`Stored OTP: ${storedOtp.otp}, User OTP: ${otp}`);
    if (storedOtp.otp == otp) {
    //  await this.otpRepo.deleteOtp(email);  // Delete OTP after successful verification
      return true;
    } else {
      console.log(`OTP verification failed for email: ${email}`);
      return false;  // Incorrect OTP
    }
  }
}

module.exports = OtpService;
