const ftp = require("basic-ftp");

async function uploadToCPanel(filePath, remotePath) {
  const client = new ftp.Client();
  client.ftp.verbose = true;

  try {
    await client.access({
      host: "ftp.yourdomain.com", // Replace with your domain's FTP host
      user: "your-username", // Replace with your FTP username
      password: "your-password", // Replace with your FTP password
      secure: false, // Use true if your FTP requires secure connections
    });

    console.log("Connected to FTP server.");
    await client.uploadFrom(filePath, `/public_html/${remotePath}`); // Upload file to the specified directory
    console.log("File uploaded successfully:", remotePath);
  } catch (error) {
    console.error("FTP upload failed:", error.message);
  } finally {
    client.close();
  }
}

// Usage Example
uploadToCPanel("./local/image.jpg", "uploads/image.jpg"); // Adjust local and remote file paths
