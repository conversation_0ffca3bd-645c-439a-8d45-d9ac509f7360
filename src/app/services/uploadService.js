const fs = require("fs");
const path = require("path");
const axios = require("axios");
const FormData = require("form-data");

/**
 * Service for handling file uploads using the new user-specific approach
 */
class UploadService {
  constructor(apiBaseUrl = 'https://api.classz.co') {
    this.apiBaseUrl = apiBaseUrl;
  }

  /**
   * Upload a single image file
   * @param {Object} fileData - The file data object
   * @param {string} userId - The ID of the user who owns the file
   * @param {string} imageType - The type of image (e.g., profile, mainImage)
   * @param {string} token - Authentication token
   * @returns {Promise<Object>} - The upload result with URL
   */
  async uploadSingleImage(fileData, userId, imageType, token) {
    try {
      // Check if we have a file path (like for a file on disk)
      if (fileData.path && fs.existsSync(fileData.path)) {
        // Create form data
        const formData = new FormData();
        formData.append('image', fs.createReadStream(fileData.path));
        formData.append('imageType', imageType);
        
        // Send to our API endpoint
        const response = await axios.post(
          `${this.apiBaseUrl}/api/upload/image`, 
          formData, 
          {
            headers: {
              ...formData.getHeaders(),
              'Authorization': `Bearer ${token}`
            }
          }
        );
        
        return response.data;
      } 
      // For cases where we have a buffer or other data format
      else if (fileData.buffer) {
        // If we're implementing this, we'd need to handle buffer uploads
        throw new Error('Buffer uploads not yet implemented');
      }
      else {
        throw new Error('Invalid file data. Path does not exist.');
      }
    } catch (error) {
      console.error('Error in uploadSingleImage:', error);
      throw error;
    }
  }

  /**
   * Upload multiple image files
   * @param {Array<Object>} filesData - Array of file data objects
   * @param {string} userId - The ID of the user who owns the files
   * @param {string} imageType - The type of images
   * @param {string} token - Authentication token
   * @returns {Promise<Object>} - The upload results with URLs
   */
  async uploadMultipleImages(filesData, userId, imageType, token) {
    try {
      if (!Array.isArray(filesData) || filesData.length === 0) {
        throw new Error('No files provided for upload');
      }

      // Create form data
      const formData = new FormData();
      
      // Append each file to the form data
      filesData.forEach((fileData, index) => {
        if (fileData.path && fs.existsSync(fileData.path)) {
          formData.append('images', fs.createReadStream(fileData.path));
        } else {
          console.warn(`File at index ${index} does not exist, skipping`);
        }
      });
      
      formData.append('imageType', imageType);
      
      // Send to our API endpoint
      const response = await axios.post(
        `${this.apiBaseUrl}/api/upload/multiple`, 
        formData, 
        {
          headers: {
            ...formData.getHeaders(),
            'Authorization': `Bearer ${token}`
          }
        }
      );
      
      return response.data;
    } catch (error) {
      console.error('Error in uploadMultipleImages:', error);
      throw error;
    }
  }

  /**
   * Get the full URL for a file stored in the user's upload directory
   * @param {string} relativePath - The relative path to the file
   * @returns {string} - The full URL to the file
   */
  getFullUrl(relativePath) {
    if (!relativePath) return null;
    
    // If it's already a full URL, return it
    if (relativePath.startsWith('http')) {
      return relativePath;
    }
    
    // Otherwise, build a full URL
    return `${this.apiBaseUrl}${relativePath}`;
  }
}

module.exports = UploadService; 