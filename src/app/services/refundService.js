const { v4: uuidv4 } = require("uuid");
const TransactionModel = require("../models/transactionModel");
const BalanceUseCase = require("../use_case/balanceUseCase");

class RefundService {
  constructor({ refundRepo, childRepo, userRepo, balanceRepo, classRepo }) {
    this.refundRepo = refundRepo;
    this.childRepo = childRepo;
    this.userRepo = userRepo;
    this.classRepo = classRepo;
    this.balanceUseCase = new BalanceUseCase(balanceRepo, userRepo);
  }

  async saveRefund(parentId, refundData) {
    // 2. Add refund amount to parent balance
    await this.balanceUseCase.addBalance(parentId, refundData.amount);

    // 3. Save refund record (with parentId)
    const refundRecord = await this.refundRepo.saveRefund({
      refundData,
    });

    // 4. Save transaction record
    const transaction = new TransactionModel({
      transactionId: uuidv4(),
      userId: parentId,
      amount: refundData.amount,
      type: "refund",
      status: "Completed",
      createdAt: new Date(),
    });
    await transaction.save();

    return refundRecord;
  }

  async getRefundsByStudent(studentId) {
    return await this.refundRepo.getRefundsByStudent(studentId);
  }

  async getRefundsByClass(classId) {
    return await this.refundRepo.getRefundsByClass(classId);
  }

  async getRefundsByEvent(eventId) {
    return await this.refundRepo.getRefundsByEvent(eventId);
  }

  async getRefundsByParentId(parentId) {
    const refunds = await this.refundRepo.find({ parentId });
    // Fetch class info for each refund
    const refundsWithClass = await Promise.all(refunds.map(async (refund) => {
      let classInfo = null;
      if (refund.classId && this.classRepo) {
        try {
          classInfo = await this.classRepo.findByOnlyNameWithCenterAndCoach(refund.classId);
        } catch (e) {
          classInfo = null;
        }
      }
      return { ...refund.toObject(), classInfo };
    }));
    return refundsWithClass;
  }

  async getRefundById(refundId) {
    return await this.refundRepo.getRefundById(refundId);
  }

  async updateRefund(refundId, updateData) {
    return await this.refundRepo.updateRefund(refundId, updateData);
  }

  async deleteRefund(refundId) {
    return await this.refundRepo.deleteRefund(refundId);
  }
}

module.exports = RefundService;
