const multer = require('multer');
const path = require('path');

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, '../uploads'));
  },
  filename: (req, file, cb) => {
    // Use the original file name
    const filename = file.originalname;
    if (!req.fileMetaData) {
      req.fileMetaData = {};
    }
    req.fileMetaData[file.fieldname] = {
      originalname: file.originalname,
      filename: filename,
    };
    cb(null, filename);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5 MB limit
  fileFilter: (req, file, cb) => {
    const fileTypes = /jpeg|jpg|png|pdf/;
    const extname = fileTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = fileTypes.test(file.mimetype);
    if (mimetype && extname) {
      return cb(null, true);
    }
    cb(new Error('Only images and PDF files are allowed!'));
  }
}).fields([
  { name: 'businessCertificate', maxCount: 1 },
  { name: 'sexualConvictionRecord', maxCount: 1 },
  { name: 'hkidCard', maxCount: 1 },
  { name: 'mainImage', maxCount: 1 },
  { name: 'images', maxCount: 10 }
]);

class FileUploadService {
  constructor() {
    this.upload = upload;
  }

  singleFileUpload() {
    return this.upload.single('image');
  }

  multipleFilesUpload() {
    return this.upload.array('images', 10);
  }

  fieldsUpload() {
    return this.upload;
  }
}

module.exports = FileUploadService;
