class parentService {
  constructor(
    savedCenterRepository,
    userRepository,
    centerRepository,
    emailService
  ) {
    this.savedCenterRepository = savedCenterRepository;
    this.userRepository = userRepository;
    this.centerRepository = centerRepository;
    this.emailService = emailService;
    
    // Add simple cache for frequently checked saved statuses
    this.savedStatusCache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes cache
  }

  async saveCenter(parentId, centerId) {
    // Validate IDs
    await this.validateParentAndCenter(parentId, centerId);

    // Clear cache for this parent-center combination
    const cacheKey = `${parentId}-${centerId}`;
    this.savedStatusCache.delete(cacheKey);

    // Save the center
    return await this.savedCenterRepository.saveCenter(parentId, centerId);
  }

  async unsaveCenter(parentId, centerId) {
    // Skip validation for unsave operations to improve performance
    // If the center/parent doesn't exist, unsaving is still successful
    if (!this.isValidObjectId(parentId) || !this.isValidObjectId(centerId)) {
      return { success: true, wasFound: false };
    }

    // Clear cache for this parent-center combination
    const cacheKey = `${parentId}-${centerId}`;
    this.savedStatusCache.delete(cacheKey);

    // Unsave the center (now returns success info instead of throwing)
    const result = await this.savedCenterRepository.unsaveCenter(parentId, centerId);
    
    // Update cache to reflect the unsaved state
    this.savedStatusCache.set(cacheKey, {
      value: false, // Now it's definitely not saved
      timestamp: Date.now()
    });
    
    return result;
  }

  async getSavedCenters(parentId) {
    // Validate parent ID
    const parent = await this.userRepository.findById(parentId);
    if (!parent) {
      throw new Error(`Parent with ID ${parentId} not found`);
    }

    // Get saved centers
    const savedCenters = await this.savedCenterRepository.getSavedCenters(
      parentId
    );

    // Transform the data to match frontend requirements
    return savedCenters.map((saved) => {
      const center = saved.centerId;
      return center;
    });
  }

  async isCenterSaved(parentId, centerId) {
    // PERFORMANCE OPTIMIZATION: Skip validation for read-only operation
    // Only validate that the IDs are valid MongoDB ObjectIds, not that they exist
    if (!this.isValidObjectId(parentId) || !this.isValidObjectId(centerId)) {
      return false; // Invalid IDs = not saved
    }

    // Check cache first
    const cacheKey = `${parentId}-${centerId}`;
    const cached = this.savedStatusCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
      return cached.value;
    }

    // Check if center is saved (no validation needed for performance)
    const isSaved = await this.savedCenterRepository.isCenterSaved(parentId, centerId);
    
    // Cache the result
    this.savedStatusCache.set(cacheKey, {
      value: isSaved,
      timestamp: Date.now()
    });

    return isSaved;
  }

  // BATCH CHECK: Check multiple centers at once (SUPER EFFICIENT)
  async batchCheckSavedCenters(parentId, centerIds) {
    // Validate parent ID format only (no database lookup for performance)
    if (!this.isValidObjectId(parentId)) {
      throw new Error('Invalid parent ID format');
    }

    // Filter valid center IDs
    const validCenterIds = centerIds.filter(id => this.isValidObjectId(id));
    
    if (validCenterIds.length === 0) {
      return {};
    }

    // Check cache first for all centers
    const results = {};
    const uncachedCenterIds = [];

    for (const centerId of validCenterIds) {
      const cacheKey = `${parentId}-${centerId}`;
      const cached = this.savedStatusCache.get(cacheKey);
      
      if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
        results[centerId] = cached.value;
      } else {
        uncachedCenterIds.push(centerId);
      }
    }

    // Batch query database for uncached centers only
    if (uncachedCenterIds.length > 0) {
      const dbResults = await this.savedCenterRepository.batchCheckSavedCenters(parentId, uncachedCenterIds);
      
      // Cache and add results
      for (const centerId of uncachedCenterIds) {
        const isSaved = dbResults[centerId] || false;
        results[centerId] = isSaved;
        
        // Cache the result
        const cacheKey = `${parentId}-${centerId}`;
        this.savedStatusCache.set(cacheKey, {
          value: isSaved,
          timestamp: Date.now()
        });
      }
    }
    return results;
  }

  // Helper method to validate MongoDB ObjectIds
  isValidObjectId(id) {
    const mongoose = require('mongoose');
    return mongoose.Types.ObjectId.isValid(id);
  }

  // Helper method to validate parent and center IDs (only for write operations)
  async validateParentAndCenter(parentId, centerId) {
    // Validate parent ID
    const parent = await this.userRepository.findById(parentId);
    if (!parent) {
      throw new Error(`Parent with ID ${parentId} not found`);
    }

    // Validate center ID
    const center = await this.centerRepository.findById(centerId);
    if (!center) {
      throw new Error(`Center with ID ${centerId} not found`);
    }
  }
  
  async contactUs(email, subject, message) {
    try {
      // Validate email and message
      if (!email || !message) {
        throw new Error("Email and message are required");
      }

      // Send email
      const response = await this.emailService.contactUs(
        email,
        subject,
        message
      );
      return response;
    } catch (error) {
      console.error("Error in contactUs:", error);
      throw new Error("Failed to send contact us email");
    }
  }
}

module.exports = parentService;
