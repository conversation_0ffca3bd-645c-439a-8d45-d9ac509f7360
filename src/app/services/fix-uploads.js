/**
 * Fix uploads directory structure to organize files by user ID
 * 
 * This script scans the uploads directory for files using the pattern:
 * mainImage-USER_ID.jpg or 12345678-mainImage-USER_ID.jpg
 * and moves them into the proper user subdirectories.
 */

const fs = require('fs');
const path = require('path');

const uploadsDir = path.resolve(__dirname, '../../../uploads');

// Run this function from the command line:
// node src/app/services/fix-uploads.js
async function fixUploadsDirectory() {
  console.log('Starting uploads directory restructuring...');
  
  try {
    // Check if uploads directory exists
    if (!fs.existsSync(uploadsDir)) {
      console.error('Uploads directory does not exist!');
      return;
    }
    
    // Get all files in the uploads directory
    const files = fs.readdirSync(uploadsDir);
    console.log(`Found ${files.length} files in uploads directory`);
    
    // Stats for reporting
    let moved = 0;
    let skipped = 0;
    let errors = 0;
    
    // Process each file
    for (const file of files) {
      // Skip directories
      if (fs.statSync(path.join(uploadsDir, file)).isDirectory()) {
        console.log(`Skipping subdirectory: ${file}`);
        continue;
      }
      
      // Extract user ID from filename
      let userId = null;
      
      // Try different filename patterns
      // Pattern 1: mainImage-USER_ID.jpg
      const pattern1 = /^(.*)-([a-f0-9]{24})\.(jpg|png|gif|pdf)$/i;
      // Pattern 2: TIMESTAMP-mainImage-USER_ID.jpg
      const pattern2 = /^([0-9]+)-(.*)-([a-f0-9]{24})\.(jpg|png|gif|pdf)$/i;
      
      const match1 = file.match(pattern1);
      const match2 = file.match(pattern2);
      
      if (match1) {
        userId = match1[2];
      } else if (match2) {
        userId = match2[3];
      }
      
      if (!userId) {
        console.log(`Could not extract user ID from filename: ${file}`);
        skipped++;
        continue;
      }
      
      // Create user directory if it doesn't exist
      const userDir = path.join(uploadsDir, userId);
      if (!fs.existsSync(userDir)) {
        console.log(`Creating directory for user: ${userId}`);
        fs.mkdirSync(userDir, { recursive: true });
      }
      
      // Move file to user directory
      const sourcePath = path.join(uploadsDir, file);
      const destPath = path.join(userDir, file);
      
      try {
        // Skip if file already exists in destination
        if (fs.existsSync(destPath)) {
          console.log(`File already exists in destination: ${destPath}`);
          skipped++;
          continue;
        }
        
        // Copy first, then delete source to avoid cross-device issues
        console.log(`Moving ${sourcePath} to ${destPath}`);
        fs.copyFileSync(sourcePath, destPath);
        
        // Verify file was copied successfully
        if (fs.existsSync(destPath)) {
          fs.unlinkSync(sourcePath);
          moved++;
        } else {
          console.error(`Failed to copy file: ${file}`);
          errors++;
        }
      } catch (error) {
        console.error(`Error moving file ${file}:`, error);
        errors++;
      }
    }
    
    console.log('=== Restructuring Complete ===');
    console.log(`Moved: ${moved} files`);
    console.log(`Skipped: ${skipped} files`);
    console.log(`Errors: ${errors} files`);
    
  } catch (error) {
    console.error('Error restructuring uploads directory:', error);
  }
}

// Run the function if this script is executed directly
if (require.main === module) {
  fixUploadsDirectory().then(() => {
    console.log('Script completed');
  });
}

module.exports = { fixUploadsDirectory }; 