const mongoose = require("mongoose");

const SubscriptionModel = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "user",
  },
  planId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "subscriptionPlanModel",
  },
  startDate: {
    type: Date,
    default: Date.now,
  },
  endDate: {
    type: Date,
  },
});

const subscriptionModel = new mongoose.model(
  "subscription",
  SubscriptionModel
);
module.exports = subscriptionModel;
