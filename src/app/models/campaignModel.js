const mongoose = require("mongoose");

const campaignSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
    },
    subtitle: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      default: "",
    },
    imageUrl: {
      type: String,
      default: "",
    },
    backgroundColor: {
      type: String,
      default: "#FF6B6B", // Default gradient color
    },
    textColor: {
      type: String,
      default: "#FFFFFF", // Default white text
    },
    discountPercentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 0,
    },
    discountCode: {
      type: String,
      default: "", // Link to existing discount system
    },
    validFrom: {
      type: Date,
      required: true,
    },
    validUntil: {
      type: Date,
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    priority: {
      type: Number,
      default: 0, // Higher numbers = higher priority
    },
    targetAudience: {
      type: String,
      enum: ["all", "parent", "center", "coach", "owner"],
      default: "all",
    },
    actionType: {
      type: String,
      enum: ["none", "discount", "navigate", "external_link"],
      default: "none",
    },
    actionData: {
      type: mongoose.Schema.Types.Mixed, // Flexible data for different action types
      default: {},
    },
    clickCount: {
      type: Number,
      default: 0,
    },
    impressionCount: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
);

// Index for efficient queries
campaignSchema.index({ isActive: 1, validFrom: 1, validUntil: 1, priority: -1 });

const Campaign = mongoose.model("campaign", campaignSchema);
module.exports = Campaign;