const mongoose = require("mongoose");

const userAddressSchema = new mongoose.Schema(
  {
    flatFloorBlock: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Accept any non-empty string or "Not specified"
          return v !== null && v !== undefined && (v.trim() !== "" || v === "Not specified");
        },
        message: props => `${props.path} is required and cannot be empty`
      }
    },
    buildingEstate: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Accept any non-empty string or "Not specified"
          return v !== null && v !== undefined && (v.trim() !== "" || v === "Not specified");
        },
        message: props => `${props.path} is required and cannot be empty`
      }
    },
    district: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Accept any non-empty string or "Not specified"
          return v !== null && v !== undefined && (v.trim() !== "" || v === "Not specified");
        },
        message: props => `${props.path} is required and cannot be empty`
      }
    },
    region: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Accept any non-empty string or "Not specified"
          return v !== null && v !== undefined && (v.trim() !== "" || v === "Not specified");
        },
        message: props => `${props.path} is required and cannot be empty`
      }
    },
    country: {
      type: String,
      default: "Hong Kong",
    },
    coordinates: {
      lat: {
        type: Number,
        default: null
      },
      lng: {
        type: Number,
        default: null
      }
    },
    default: {
      type: Boolean,
      default: false,
    },
  },
  { _id: true }
); // optional, prevents auto _id in subdocs

// ✅ Export the schema, not the model
module.exports = userAddressSchema;
