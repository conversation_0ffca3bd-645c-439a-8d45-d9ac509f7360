const mongoose = require("mongoose");
const reviewSchema = require("./reviewModel");

const ChildSchema = new mongoose.Schema(
  {
    classzId: {
      type: String,
      required: true,
    },
    mainImage: {
      url: String,
      contentType: String,
    },
    fullname: {
      required: true,
      type: String,
    },
    idcard: {
      required: true,
      type: String,
    },
    birthday: {
      required: true,
      type: String,
    },
    school: {
      type: String,
    },
    sen: {
      type: Boolean,
    },
    phone: {
      type: String,
    },
    parent: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "user",
      required: true,
    },
    reviews: [{ type: mongoose.Schema.Types.ObjectId, ref: "review" }],
    rating: {
      type: Number,
      default: 0,
    },
    outstandingQuality: {
      type: Number,
      default: 0,
    },
    keyCompetency: {
      type: Number,
      default: 0,
    },
    distinctiveConduct: {
      type: String,
      default: "",
    },
    learningProgress: {
      type: String,
      default: "",
    },
    metricsLastUpdated: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);
const Child = mongoose.model("child", ChildSchema);
module.exports = Child;
