const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const conversationSchema = new mongoose.Schema({
  conversationId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  participants: [{
    userId: {
      type: Schema.Types.ObjectId,
      required: true
    },
    userType: {
      type: String,
      required: true,
      enum: ["user", "center", "coach"]
    }
  }],
  lastMessage: {
    text: String,
    sender: Schema.Types.ObjectId,
    timestamp: Date
  },
  messageCount: {
    type: Number,
    default: 0
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create indexes
conversationSchema.index({ "participants.userId": 1 });
conversationSchema.index({ updatedAt: -1 });

const ConversationModel = mongoose.model("conversation", conversationSchema);
module.exports = ConversationModel; 