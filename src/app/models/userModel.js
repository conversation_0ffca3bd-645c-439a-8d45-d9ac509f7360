const mongoose = require("mongoose");
const BaseUser = require("./baseUserModel");
const userAddressSchema = require("./userAddressModel");
const userSchema = new mongoose.Schema(
  {
    baseUser: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "baseUser",
      required: true,
    },
    email: {
      type: String,
    },
    classzId: {
      type: String,
      default: "",
    },
    mainImage: {
      url: String,
      contentType: String,
    },
    nickname: {
      type: String,
    },
    fullname: {
      type: String,
      // unique: true,
    },
    phone: {
      type: String,
      // unique: true,
    },
    location: {
      type: [userAddressSchema],
      default: [],
    },

    referal: {
      type: String,
    },
    stripeCustomerId: {
      type: String,
      default: "",
    },
    isCompleted: {
      type: Boolean,
      default: false,
    },
    balance: {
      type: Number,
      default: 0,
    },
    exp: {
      type: Number,
      default: 0,
    },
    activitiesNotification: {
      type: Boolean,
      default: true,
    },
    promotionNotification: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

const User = mongoose.model("user", userSchema);
module.exports = User;
