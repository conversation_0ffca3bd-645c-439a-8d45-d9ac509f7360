const mongoose = require("mongoose");

const eventSchema = new mongoose.Schema({
  date: Date,

  centerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "baseUser",
  },
  coachId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "coach",
  },
  classId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "class",
  },
  dateId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "classDate",
    required: true,
  },
  // Status tracking for cancellations and rearrangements
  status: {
    type: String,
    enum: ['active', 'cancelled', 'pending_rearrangement', 'rearranged'],
    default: 'active'
  },
  cancellationType: {
    type: String,
    enum: ['refund', 'rearrange', null],
    default: null
  },
  cancelledAt: {
    type: Date,
    default: null
  },
  rearrangementInfo: {
    originalDate: Date,
    newDate: Date,
    reason: String,
    processedAt: Date,
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed'],
      default: 'pending'
    }
  },
  refundInfo: {
    refundIds: [String], // Array of payment gateway refund IDs
    totalRefunded: Number,
    processedAt: Date,
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed'],
      default: 'pending'
    }
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// Add indexes to improve query performance
eventSchema.index({ classId: 1, date: 1 });
eventSchema.index({ coachId: 1 });
eventSchema.index({ centerId: 1, date: 1 });
eventSchema.index({ date: 1 });

const Event = mongoose.model("event", eventSchema);
module.exports = Event;
