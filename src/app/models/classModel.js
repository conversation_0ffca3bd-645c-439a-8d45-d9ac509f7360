const mongoose = require("mongoose");

const classDateSchema = new mongoose.Schema({
  date: {
    type: String, // Store the specific date
    required: true,
  },
  startTime: {
    type: String, // Format as 'HH:mm', e.g. '14:30'
    required: true,
  },
  endTime: {
    type: String, // Format as 'HH:mm', e.g. '15:15'
    required: true,
  },
  durationMinutes: {
    type: String, // e.g. 45 minutes
    required: true,
  },
  weekDay: {
    type: String,
  },
  repeat: {
    type: String,
  },
});
const classSchema = new mongoose.Schema(
  {
    center: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "center", // Refers to Base<PERSON>ser with role `center`
      required: true,
    },
    coach: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "coach", // Refers to Coach model
    },
    // student: {
    //   type: [mongoose.Schema.Types.ObjectId],
    //   ref: "child",
    //   default: [],
    // },
    category: {
      type: String,
      required: true,
      enum: [
        "Music",
        "Art",
        "Sports",
        "Science",
        "Technology",
        "Dance",
        "Math",
        "Language",
        "Coding",
        "Drama",
        "Health & Fitness",
        "Photography",
        "Cooking",
        "Engineering",
        "History",
        "Robotics",
        "Chess",
        "Public Speaking",
        "Writing",
        "Nature & Environment",
      ],
    },
    classProviding: {
      type: String,
      default: "",
    },
    level: {
      type: String,
    },
    description: {
      type: String,
      default: "",
    },

    mode: {
      type: Boolean, // Corrected from `bool` to `Boolean`
    },
    sen: {
      type: Boolean, // Corrected from `bool` to `Boolean`
    },
    // initialCharge: {
    //   // Fixed typo: 'initalCharge' to 'initialCharge'
    //   type: String,
    // },
    ageFrom: {
      type: Number,
    },
    ageTo: {
      type: Number,
    },
    mainImage: {
      url: String,
      contentType: String,
    },
    // course: {
    //   type: Boolean,
    // },
    newComer: {
      type: Boolean,
    },
    initialCharge: {
      type: String,
      default: '0',
    },
    // numberOfStudent: {
    //   type: Number,
    // },
    // minimumStudent: {
    //   type: Number,
    //   default: 1,
    // },
    // numberOfClass: {
    //   type: Number,
    //   default: 0,
    // },
    // languageOptions: {
    //   type: [String],
    //   default: [],
    // },

    // // Status tracking for class lifecycle
    // status: {
    //   type: String,
    //   enum: ['active', 'cancelled', 'pending_rearrangement', 'completed'],
    //   default: 'active'
    // },
    // cancellationInfo: {
    //   cancelledAt: Date,
    //   cancellationType: {
    //     type: String,
    //     enum: ['refund', 'rearrange'],
    //   },
    //   reason: String,
    //   refundTotal: Number,
    //   studentsAffected: Number
    // },
    // rearrangementInfo: {
    //   isRearranging: {
    //     type: Boolean,
    //     default: false
    //   },
    //   originalDates: [Date],
    //   newDates: [Date],
    //   reason: String,
    //   requestedAt: Date,
    //   completedAt: Date,
    //   studentsNotified: {
    //     type: Boolean,
    //     default: false
    //   }
    // }
  },
  {
    timestamps: true,
  }
);
classSchema.index({
  classProviding: "text",
  description: "text",
});
const Class = mongoose.model("class", classSchema);
module.exports = Class;
