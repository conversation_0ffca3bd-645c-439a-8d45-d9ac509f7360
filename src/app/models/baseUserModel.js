const mongoose = require("mongoose");
const userSchema = require("./userModel");
const centerSchema = require("./centerModel");
const coachSchema = require("./coachModel");
const baseUserSchema = new mongoose.Schema(
  {
    email: {
      required: true,
      type: String,
      unique: true,
      trim: true,
    },
    password: {
      required: true,
      type: String,
    },
    roles: [
      {
        type: String,
        enum: ["", "coach", "center", "owner","parent","manager"],
      },
    ],
  },
  {
    timestamps: true,
  }
);

const BaseUser = mongoose.model("baseUser", baseUserSchema);

module.exports = BaseUser;
