// models/bankSchema.js
const mongoose = require("mongoose");

const bankSchema = new mongoose.Schema({
  bankName: {
    type: String,
    default: "",
  },
  accountHolderName: {
    type: String,
    default: "",
  },
  bankCode: {
    type: String,
    default: "",
  },
  branchCode: {
    type: String,
    default: "",
  },
  accountNumber: {
    type: String,
    default: "",
  },
});

module.exports = bankSchema;
