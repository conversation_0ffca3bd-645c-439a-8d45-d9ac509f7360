const mongoose = require("mongoose");

const notificationModel = new mongoose.Schema(
  {
    title: { type: String, required: true },
    body: { type: String, required: true },
    data: { type: mongoose.Schema.Types.Mixed, default: {} }, // Flexible data field
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
    },
    isRead: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
  },
  { timestamps: true }
);

const NotificationModel = mongoose.model("notification", notificationModel);
module.exports = NotificationModel;
