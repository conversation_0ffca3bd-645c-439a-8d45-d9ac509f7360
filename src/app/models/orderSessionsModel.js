// src/models/orderSessionModel.js
const mongoose = require("mongoose");

const orderSessionSchema = new mongoose.Schema(
  {
    orderId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Order",
      required: true,
    },
    classId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Class",
      required: true,
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    childId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Child",
      required: true,
    },
    date: { type: Date, required: true },
    startTime: String,
    endTime: String,
    durationMinutes: Number,
    repeat: String,
    weekDay: String,
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("OrderSession", orderSessionSchema);
