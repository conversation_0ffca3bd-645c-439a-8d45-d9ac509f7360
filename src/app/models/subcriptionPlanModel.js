const mongoose = require("mongoose");

const SubscriptionPlanModel = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  amount: {
    type: Number,
    required: true,
  },
  zCoin: {
    type: Number,
    required: true,
  },
  interval:{
    type:String,
    required:true
  }
});
const subscriptionPlanModel= new mongoose.model("subscriptionPlanModel",SubscriptionPlanModel)
module.exports = subscriptionPlanModel;
