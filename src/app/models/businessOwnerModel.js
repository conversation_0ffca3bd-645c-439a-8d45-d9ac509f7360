const mongoose = require("mongoose");
const businessowner = new mongoose.Schema(
  {
    baseUser: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "baseUser",
      required: true,
    },
    classzId: {
      type: String,
      default: "",
    },
    fullName: {
      type: String,
    },
    isIndividualCreator: {
      type: Boolean,
      default: false,
    },
    displayName: {
      type: String,
    },
    email: {
      type: String,
    },
    phoneNumber: {
      type: String,
    },
    hkid: {
      type: String,
    },
    hkidCard: [
      {
        url: String,
        contentType: String,
      },
    ],
    mainImage: {
      url: String,
      contentType: String,
    },
    isCoach: {
      type: Boolean,
      default: false,
    },
    coach: {
      type: String,
      default: "",
    },
    referal_code: {
      type: String,
    },
    branchs: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "center",
        default: null,
      },
    ],
  },
  {
    timestamps: true,
  }
);

const Businessowner = mongoose.model("owner", businessowner);
module.exports = Businessowner;
