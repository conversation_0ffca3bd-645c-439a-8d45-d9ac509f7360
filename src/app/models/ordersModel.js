const mongoose = require("mongoose");

const orderSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "user",
      require: true,
    },
    classId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "class",
      require: true,
    },
    childId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "child",
      require: true,
    },
    dateId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "classDate",
      required: true,
    },
    course: {
      type: Boolean,
      require: true,
    },
    date: {
      type: [Date],
    },
    paid: {
      type: Boolean,
      default: false,
    },
    sen: {
      type: Boolean,
    },
    Discount: {
      type: Number,
    },
    amount: {
      type: Number,
    },
    quantity: {
      type: Number,
    },

    startTime: {
      type: String, // Format as 'HH:mm', e.g. '14:30'
      required: true,
    },
    endTime: {
      type: String, // Format as 'HH:mm', e.g. '15:15'
      required: true,
    },
    durationMinutes: {
      type: String, // e.g. 45 minutes
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

const Order = mongoose.model("order", orderSchema);
module.exports = Order;
