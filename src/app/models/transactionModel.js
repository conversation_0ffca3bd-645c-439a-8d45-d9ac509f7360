const mongoose = require("mongoose");

const transactionSchema = new mongoose.Schema({
  transactionId: { type: String, required: true, unique: true },
  userId: { type: mongoose.Schema.Types.ObjectId, ref: "user", required: true },
  amount: { type: Number, required: true },
  type: { type: String, enum: ["purchase", "redeem","refund"], required: true },
  status: {
    type: String,
    enum: ["pending", "Completed", "Failed"],
  },
  orderId: { type: mongoose.Schema.Types.ObjectId, ref: "order" },
  createdAt: { type: Date, default: Date.now },
});

module.exports = mongoose.model("transaction", transactionSchema);
