const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const AttendanceSchema = new Schema({
  classId: {
    type: Schema.Types.ObjectId,
    ref: "class",
    required: true,
  },
  studentId: {
    type: Schema.Types.ObjectId,
    ref: "child",
    required: true,
  },
  classDate: {
    type: Date,
    required: true,
  },
  code: {
    type: String,
    required: true,
  },
  qrCodeData: {
    type: String,
    required: true,
  },
  attendanceStatus: {
    type: String,
    enum: ["absent", "present", "late"],
    default: "absent",
  }, // Updated field
  createdAt: { type: Date, default: Date.now },
});

const Attendance = mongoose.model("Attendance", AttendanceSchema);
module.exports = Attendance;
