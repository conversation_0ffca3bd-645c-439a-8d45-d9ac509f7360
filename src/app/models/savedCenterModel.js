const mongoose = require("mongoose");

const savedCenterSchema = new mongoose.Schema(
  {
    parentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "user",
      required: true,
    },
    centerId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "center",
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Create a compound index to prevent duplicates
savedCenterSchema.index({ parentId: 1, centerId: 1 }, { unique: true });

const SavedCenter = mongoose.model("savedCenter", savedCenterSchema);
module.exports = SavedCenter; 