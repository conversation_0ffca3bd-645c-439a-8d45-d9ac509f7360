const mongoose = require("mongoose");

const reviewSchema = new mongoose.Schema(
  {
    reviewerId: { type: mongoose.Schema.Types.ObjectId, required: true },
    reviewerType: {
      type: String,
      enum: ["user", "center", "coach", "Parent"],
      required: true,
    },
    classId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "class",
    },
    revieweeId: { type: mongoose.Schema.Types.ObjectId, required: true },

    revieweeType: {
      type: String,
      enum: ["center", "coach", "child"],
      required: true,
    },
    centerName: {
      type: String,
      default: "",
    },
    coachName: {
      type: String,
      default: "",
    },
    rating: { type: Number, min: 1, max: 10 },
    title: {
      type: String,
      default: "",
    },
    comment: { type: String, maxlength: 100 },
    topic: { type: String, maxlength: 100 },
    questions: {
      q1: {
        type: Number,
      },
      q2: {
        type: Number,
      },
      q3: {
        type: Number,
      },
      q4: {
        type: Number,
      },
      q5: {
        type: Number,
      },
      q6: {
        type: Number,
      },
    },
    date: { type: Date, default: Date.now },
    images: [
      {
        url: String,
        contentType: String,
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Add indexes to improve query performance
reviewSchema.index({ revieweeId: 1, revieweeType: 1 });
reviewSchema.index({ reviewerId: 1, reviewerType: 1 });
reviewSchema.index({ classId: 1 });
reviewSchema.index({ date: 1 });
reviewSchema.index({ revieweeId: 1, classId: 1, date: 1 });

const Review = mongoose.model("review", reviewSchema);

module.exports = Review;
