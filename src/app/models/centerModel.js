const mongoose = require("mongoose");
const BaseUser = require("./baseUserModel");
const addressSchema = require("./addressUserModel");
const openingHourSchema = require("./openingHourUserModel");
const bankSchema = require("./bankUserModel");
const serviceSchema = require("./centerServiceUserModel");

const centerSchema = new mongoose.Schema({
  baseUser: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "baseUser",
    required: true,
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "owner",
    required: true,
  },
  classzId: {
    type: String,
    default: "",
  },
  managers: {
    type: [mongoose.Schema.Types.ObjectId],
    ref: "coach",
    default: [],
  },
  coachs: {
    type: [mongoose.Schema.Types.ObjectId],
    ref: "coach",
    default: [],
  },

  rating: {
    type: Number,
    default: 0.0,
  },
  reviewCount: {
    type: Number,
    default: 0,
  },
  legalName: {
    type: String,
    default: "",
  },
  displayName: {
    type: String,
    default: "",
  },
  startAge: {
    type: String,
    default: "",
  },
  sen: {
    type: Boolean,
    default: false,
  },
  promotion: {
    type: Boolean,
    default: false,
  },
  address: {
    type: addressSchema,
    required: true,
  },
  companyNumber: {
    type: String,
    default: "",
  },
  centerNumber: {
    type: String,
    default: "",
  },
  email: {
    type: String,
    default: "",
  },
  openingHour: [openingHourSchema],
  businessNumber: {
    type: String,
    default: "",
  },
  businessCertificate: [
    {
      url: String,
      contentType: String,
    },
  ],
  sexualConvictionRecord: [
    {
      url: String,
      contentType: String,
    },
  ],
  isFreelanceEducator: {
    type: Boolean,
    default: false,
  },
  hkidCard: [
    {
      url: String,
      contentType: String,
    },
  ],
  mainImage: {
    url: String,
    contentType: String,
  },
  languages: {
    type: [String],
    default: [],
  },
  services: {
    type: [String],
    default: [],
  },
  description: {
    type: String,
    default: "",
  },
  images: [
    {
      url: String,
      contentType: String,
    },
  ],
  bankDetails: {
    type: bankSchema,
  },
  verified: {
    type: Boolean,
    default: false,
  },
  // New fields for filtering
  price: {
    type: Number,
    default: 0,
  },
  priceFrom: {
    type: Number,
    default: null,
  },
  priceTo: {
    type: Number,
    default: null,
  },
  ageRangeFrom: {
    type: Number,
    default: 0,
  },
  ageRangeTo: {
    type: Number,
    default: 0,
  },
  // Geospatial location field
  location: {
    type: {
      type: String,
      enum: ["Point"],
      default: "Point",
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      default: [0, 0],
    },
  },
  categories: {
    type: [String],
    default: [],
  },
  // Timestamps will be automatically managed by Mongoose
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Add pre-save middleware to update location from address coordinates
centerSchema.pre("save", async function (next) {
  if (
    this.address &&
    this.address.coordinates &&
    this.address.coordinates.lat &&
    this.address.coordinates.lng
  ) {
    // Update location coordinates from address
    this.location.coordinates = [
      this.address.coordinates.lng, // MongoDB uses [longitude, latitude]
      this.address.coordinates.lat,
    ];
  }
  next();
});

// Add method to get full address details
centerSchema.methods.getAddressDetails = function () {
  if (!this.address) return null;

  return {
    formatted: this.address.getFormattedAddress(),
    coordinates: this.address.coordinates,
    googleMapsLink: this.address.address2,
    hasValidGoogleMapsLink: this.address.isValidGoogleMapsLink(),
  };
};

// Create a text index on the `displayName`, `legalName`, `description`, and `languages` fields
centerSchema.index({
  displayName: "text",
  legalName: "text",
  description: "text",
  languages: "text",
});

// Create a geospatial index on the location field
centerSchema.index({ location: "2dsphere" });

const Center = mongoose.model("center", centerSchema);
module.exports = Center;
