const mongoose = require("mongoose");

const classDateSchema = new mongoose.Schema(
  {
    classId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "class",
      required: true,
    },
    date: {
      type: String,
      required: true,
    },
    address: {
      type: String,
    },
    startTime: {
      type: String, // Format as 'HH:mm', e.g. '14:30'
      required: true,
    },
    endTime: {
      type: String, // Format as 'HH:mm', e.g. '15:15'
      required: true,
    },
    durationMinutes: {
      type: String,
      required: true,
    },
    weekDay: {
      type: String,
    },
    repeat: {
      type: String,
    },
    students: {
      type: [mongoose.Schema.Types.ObjectId],
      ref: "child",
      default: [],
    },
    minimumStudent: {
      type: Number,
      default: 1,
    },
    numberOfClass: {
      type: Number,
      default: null,
    },
    // Schedule-specific settings
    numberOfStudent: {
      type: Number,
      default: null, // If null, fall back to class-level setting
    },
    charge: {
      type: Number,
      default: null, // If null, fall back to class-level setting
    },
    languageOptions: {
      type: [String],
      default: [], // If empty, fall back to class-level setting
    },
    buyAll: {
      type: Boolean,
    },
    joinNew: {
      type: Boolean,
    },
    status: {
      type: String,
      enum: ["scheduled", "ongoing", "completed", "cancelled"],
      default: "scheduled",
    },
    cancellationReason: {
      type: String,
    },
    cancellationType: {
      type: String,
      enum: ["manual", "automatic_cancellation", null],
      default: null,
    },
    cancelledAt: {
      type: Date,
      default: null,
    },
  },
  {
    timestamps: true,
  }
);

// Index for faster queries
classDateSchema.index({ classId: 1, date: 1 });
classDateSchema.index({ date: 1 });
classDateSchema.index({ students: 1 });

const ClassDate = mongoose.model("classDate", classDateSchema);
module.exports = ClassDate;
