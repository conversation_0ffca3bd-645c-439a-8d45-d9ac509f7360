const mongoose = require("mongoose");

const DiscountSchema = new mongoose.Schema(
  {
    code: { type: String, required: true, unique: true, trim: true }, // Coupon code
    discountPercentage: { type: Number, required: true, min: 0, max: 100 }, // Discount percentage
    discountType: {
      type: String,
      enum: ["percentage", "fixed"],
      required: true,
    },
    validUntil: { type: Date, required: true }, // Expiry date
    userSpecific: { type: Boolean, default: false }, // If false, coupon is universal
    allowedUsers: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }], // User IDs for specific coupons
    isActive: { type: Boolean, default: true }, // To disable expired/inactive coupons
    maxUsage: { type: Number, required: true, default: 1 }, // Max number of uses for the coupon
    usageCount: { type: Number, default: 0 },
    createdAt: { type: Date, default: Date.now }, // Timestamp
    // New fields for campaign integration
    campaignId: { type: mongoose.Schema.Types.ObjectId, ref: "Campaign" }, // Reference to campaign
    description: { type: String }, // Description from campaign
  },
  { timestamps: true } // Automatically adds createdAt and updatedAt fields
);

module.exports = mongoose.model("Discount", DiscountSchema);
