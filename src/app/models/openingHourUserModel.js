const mongoose = require("mongoose");

const openingHourSchema = new mongoose.Schema({
  day: {
    type: String,
    default:"",
    enum: [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday",
    ],
  },
  openingTime: {
    type: String,  default:""

  },
  closingTime: {
    type: String,  default:""
  },
});
module.exports = openingHourSchema;
