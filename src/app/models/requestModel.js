const mongoose = require("mongoose");

const requestSchema = new mongoose.Schema(
  {
    centerId: { type: mongoose.Schema.Types.ObjectId, ref: "center" },
    coachId: { type: mongoose.Schema.Types.ObjectId, ref: "coach" },
    status: {
      type: String,
      enum: ["pending", "accepted", "rejected"],
      default: "pending",
    },
    sender: {
      type: String,
      default: "",
    },
    message: { type: String, default: "" },
    dateRequested: { type: Date, default: Date.now },
  },
  {
    timestamps: true,
  }
);
const Request = mongoose.model("request", requestSchema);
module.exports = Request;
