const mongoose = require("mongoose");
const addressSchema = require("./addressUserModel");
const BaseUser = require("./baseUserModel");
const experienceSchema = require("./experienceCoachModel");
const skillSchema = require("./skillCoachModel");
const accredationSchema = require("./accredationCoachModel");

const coachSchema = new mongoose.Schema(
  {
    baseUser: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "baseUser",
      required: true,
    },
    center: {
      type: mongoose.Schema.Types.ObjectId,

      ref: "center",
    },
    manager: {
      type: mongoose.Schema.Types.ObjectId,
      default: null,
      ref: "center",
    },
    classzId: {
      type: String,
      default: "",
    },
    legalName: {
      type: String,
      default: "",
    },
    displayName: {
      type: String,
      default: "",
    },
    hkid: {
      type: String,
    },
    phone: {
      type: String,
      default: "",
    },
    email: {
      type: String,
    },
    address: {
      type: addressSchema,
      default: {},
    },
    languages: {
      type: [String],
      default: [],
    },
    description: {
      type: String,
      default: "",
    },
    experience: {
      type: [experienceSchema],
      default: [],
    },
    skill: {
      type: [skillSchema],
      default: [],
    },
    ageFrom: {
      type: String,
      default: "",
    },
    ageTo: {
      type: String,
      default: "",
    },
    sen: {
      type: Boolean,
      default: true,
    },
    accredation: {
      type: [accredationSchema],
      default: [],
    },
    rating: {
      type: Number,
      default: 0.0,
    },
    reviewCount: {
      type: Number,
      default: 0,
    },
    mainImage: {
      url: String,
      contentType: String,
    },
    images: [
      {
        url: String,
        contentType: String,
      },
    ],
    programs: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "class", // Assuming your class model is named "class"
        default: [],
      },
    ],
  },
  {
    timestamps: true,
  }
);
coachSchema.index({
  displayName: "text",
  description: "text",
  languages: "text",
});
const Coach = mongoose.model("coach", coachSchema);
module.exports = Coach;
