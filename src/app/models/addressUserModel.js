// models/address.js
const mongoose = require("mongoose");

const addressSchema = new mongoose.Schema({
  address1: {
    type: String,
    default: "",
  },
  address2: {
    type: String,
    default: "",
    description: "Google Maps link for the location",
  },
  city: {
    type: String,
    default: "",
  },
  region: {
    type: String,
    default: "",
  },
  country: {
    type: String,
    default: "Hong Kong",
  },
  coordinates: {
    lat: {
      type: Number,
      default: null,
    },
    lng: {
      type: Number,
      default: null,
    },
  },
});

// Add a method to validate Google Maps link
addressSchema.methods.isValidGoogleMapsLink = function() {
  if (!this.address2) return true;
  return this.address2.includes('maps.google.com') || 
         this.address2.includes('goo.gl/maps') || 
         this.address2.includes('google.com/maps');
};

// Add a method to get formatted address
addressSchema.methods.getFormattedAddress = function() {
  const parts = [
    this.address1,
    this.city,
    this.region,
    this.country
  ].filter(part => part && part.trim());
  
  return parts.join(', ');
};

module.exports = addressSchema;
