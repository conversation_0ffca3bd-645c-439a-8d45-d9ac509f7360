class SubscriptionController {
  constructor(createSubscriptionUseCase, getSubscriptionUseCase, cancelSubscriptionUseCase) {
    this.createSubscriptionUseCase = createSubscriptionUseCase;
    this.getSubscriptionUseCase = getSubscriptionUseCase;
    this.cancelSubscriptionUseCase = cancelSubscriptionUseCase;
  }
  async createSubscription(req, res) {
    try {
      const data = req.body;
      const plan = await this.createSubscriptionUseCase.execute(data);
      res.status(200).json(plan);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
  async getSubscription(req, res) {
    const { userId } = req.params;
    try {
      const plan = await this.getSubscriptionUseCase.execute(userId);
      res.status(200).json(plan);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
  async cancelSubscription(req, res) {
    const { userId } = req.params;
    try {
      const result = await this.cancelSubscriptionUseCase.execute(userId);
      res.status(200).json(result);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
}

module.exports = SubscriptionController;
