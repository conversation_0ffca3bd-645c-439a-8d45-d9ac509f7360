class RefundController {
  constructor(refundService) {
    this.refundService = refundService;
  }

  async getRefundsByParentId(req, res) {
    try {
      const { parentId } = req.params;
      const refunds = await this.refundService.getRefundsByParentId(parentId);
      res.status(200).json({ success: true, refunds });
    } catch (error) {
      console.error("Error fetching refunds by parentId:", error);
      res.status(500).json({ success: false, message: error.message });
    }
  }
}

module.exports = RefundController; 