const OrderUseCase = require("../use_case/orderUseCase");

class OrderController {
  constructor({ orderUseCase }) {
    this.orderUseCase = orderUseCase;
  }

  // Create an order
  async create(req, res) {
    try {
      const orderData = req.body;
      console.log("Controller");
      const result = await this.orderUseCase.create(orderData);
      res.status(201).json(result);
    } catch (error) {
      res.status(500).json(error.message);
    }
  }

  // Get order by ID
  async getById(req, res) {
    try {
      const { orderId } = req.params;
      const order = await this.orderUseCase.getById(orderId);
      if (order) {
        res.status(200).json(order);
      } else {
        res.status(404).json({ message: "Order not found" });
      }
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  // Get all orders
  async getAll(req, res) {
    try {
      const { skip = 0, limit = 10, paid } = req.query; // Extract `skip`, `limit`, and `paid` from query
      const orders = await this.orderUseCase.getAll(
        parseInt(skip),
        parseInt(limit),
        paid ? JSON.parse(paid) : undefined // Convert 'paid' query to boolean if it exists
      );
      res.status(200).json(orders);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  // Update an order
  async update(req, res) {
    try {
      const { orderId } = req.params;
      const orderData = req.body;
      const updatedOrder = await this.orderUseCase.update(orderId, orderData);
      res.status(200).json(updatedOrder);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  // Delete an order
  async delete(req, res) {
    try {
      const { orderId } = req.params;
      await this.orderUseCase.delete(orderId);
      res.status(200).json({ message: "Order deleted" });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  // Get all orders by user ID
  async getByUser(req, res) {
    try {
      const { userId } = req.params;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      console.log(userId,page,
        limit
      )
      const orders = await this.orderUseCase.getByUser(userId, page, limit);
      res.status(200).json(orders);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }

  // NEW ENDPOINT: Get eligible children for a class
  async getEligibleChildren(req, res) {
    try {
      const { userId, classId } = req.params;
      const eligibilityInfo = await this.orderUseCase.getEligibleChildren(userId, classId);
      res.status(200).json(eligibilityInfo);
    } catch (error) {
      console.error("Error in getEligibleChildren controller:", error);
      res.status(500).json({ 
        message: error.message,
        error: "Failed to get eligible children" 
      });
    }
  }

  // NEW ENDPOINT: Check if a child can book a specific class
  async canChildBookClass(req, res) {
    try {
      const { childId, classId } = req.params;
      const canBook = await this.orderUseCase.canChildBookClass(childId, classId);
      res.status(200).json(canBook);
    } catch (error) {
      console.error("Error in canChildBookClass controller:", error);
      res.status(500).json({ 
        message: error.message,
        error: "Failed to check booking eligibility" 
      });
    }
  }
}

module.exports = OrderController;
