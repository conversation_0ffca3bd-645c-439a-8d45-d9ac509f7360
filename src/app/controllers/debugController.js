class DebugController {
  constructor({ debugUseCase }) {
    this.debugUseCase = debugUseCase;
  }

  async getDbStatus(req, res) {
    try {
      const status = await this.debugUseCase.getDbStatus();
      res.json(status);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getChatStats(req, res) {
    try {
      const stats = await this.debugUseCase.getChatStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getUserChats(req, res) {
    try {
      const { userId } = req.params;
      const chats = await this.debugUseCase.getUserChats(userId);
      res.json(chats);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getAllUsersByRole(req, res) {
    try {
      const { role } = req.params;
      const users = await this.debugUseCase.getAllUsersByRole(role);
      res.json(users);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async clearCollection(req, res) {
    try {
      const { modelName } = req.params;
      const result = await this.debugUseCase.clearCollection(modelName);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async removeUserFromCollection(req, res) {
    try {
      const { userId, modelName } = req.params;
      const result = await this.debugUseCase.removeUserFromCollection(userId, modelName);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async removeChatMessages(req, res) {
    try {
      const { senderId, recipientId } = req.params;
      const result = await this.debugUseCase.removeChatMessages(senderId, recipientId);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getPendingRequests(req, res) {
    try {
      const requests = await this.debugUseCase.getPendingRequests();
      res.json(requests);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getUnverifiedUsers(req, res) {
    try {
      const users = await this.debugUseCase.getUnverifiedUsers();
      res.json(users);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getEventsByDateRange(req, res) {
    try {
      const { startDate, endDate } = req.query;
      const events = await this.debugUseCase.getEventsByDateRange(startDate, endDate);
      res.json(events);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getClassesByDateRange(req, res) {
    try {
      const { startDate, endDate } = req.query;
      const classes = await this.debugUseCase.getClassesByDateRange(startDate, endDate);
      res.json(classes);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getOrdersByDateRange(req, res) {
    try {
      const { startDate, endDate } = req.query;
      const orders = await this.debugUseCase.getOrdersByDateRange(startDate, endDate);
      res.json(orders);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = DebugController; 