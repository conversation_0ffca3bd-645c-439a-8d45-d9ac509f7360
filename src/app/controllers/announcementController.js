class AnnouncementController {
  constructor(announcementService) {
    this.announcementService = announcementService;
  }
  async save(req, res) {
    try {
      const announcementData = req.body;

      const result = await this.announcementService.save(announcementData);
      res.status(200).json(result);
    } catch (error) {
      res.status(404).json({ error: error.message });
    }
  }
  async getAnnouncementByAnnouncementId(req, res) {
    try {
      const { announcementId } = req.params;
      const result =
        await this.announcementService.getAnnouncementByAnnouncementId(
          announcementId
        );
      res.status(200).json(result);
    } catch (error) {
      res.status(404).json({ error: error.message });
    }
  }
  async getAnnouncementByClassId(req, res) {
    try {
      const { classId } = req.params;
      const result =
        await this.announcementService.getAnnouncementByClassId(
          classId
        );
      res.status(200).json(result);
    } catch (error) {
      res.status(404).json({ error: error.message });
    }
  }
  async getAnnouncementBySlotId(req, res) {
    try {
      const { slotId } = req.params;
      const result = await this.announcementService.getAnnouncementBySlotId(slotId);
      res.status(200).json(result);
    } catch (error) {
      res.status(404).json({ error: error.message });
    }
  }
}

module.exports = AnnouncementController;
