const PaymentService = require("../services/paymentService");
const Card = require("../entities/card");
class PaymentController {
  constructor(
    saveCardUseCase,
    getSavedCardUseCase,
    createCardTokenUseCase,
    purchaseUseCase,
    getPaymentMethodUseCase,
    payWithSavedCardUseCase,
    deleteCardUseCase
  ) {
    this.saveCardUseCase = saveCardUseCase;
    this.getSavedCardUseCase = getSavedCardUseCase;
    this.createCardTokenUseCase = createCardTokenUseCase;
    this.purchaseUseCase = purchaseUseCase;
    this.getPaymentMethodUseCase = getPaymentMethodUseCase;
    this.payWithSavedCardUseCase = payWithSavedCardUseCase;
    this.deleteCardUseCase = deleteCardUseCase;
  }
  async purchase(req, res) {
    const { userId, amount, cardToken } = req.body;

    try {
      console.log(this.purchaseUseCase);
      const confirmedPaymentIntent = await this.purchaseUseCase.execute(
        userId,
        amount,
        cardToken
      );
      console.log("Payment successful:", confirmedPaymentIntent);

      res.status(201).json({
        charge: confirmedPaymentIntent,
        transaction: confirmedPaymentIntent.id,
      });
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async saveCard(req, res) {
    const { userId, cardToken } = req.body;
    console.log(`saveCard called with userId: ${userId}, cardToken: ${cardToken?.substring(0, 8)}...`);
    
    try {
      console.log("Executing saveCardUseCase...");
      const customer = await this.saveCardUseCase.execute(userId, cardToken);
      console.log("Card saved successfully, customer:", customer);
      res.status(201).json(customer);
    } catch (error) {
      console.error("Error in saveCard controller:", error);
      res.status(400).json({ message: error.message });
    }
  }
  async getSavedCard(req, res) {
    const { userId } = req.params;
    try {
      const card = await this.getSavedCardUseCase.execute(userId);
      res.status(200).json(card);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async createToken(req, res) {
    const { number, expMonth, expYear, cvc, name } = req.body;
    const card = new Card(number, expMonth, expYear, cvc, name);
    try {
      const token = await this.createCardTokenUseCase.execute(card);
      res.status(200).json({ token });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getPaymentMethod(req, res) {
    const { userId } = req.params;
    try {
      const paymentMethod = await this.getPaymentMethodUseCase.execute(userId);
      res.status(200).json(paymentMethod);
    } catch (error) {
      console.error("Error getting payment method:", error);
      res.status(400).json({ message: error.message });
    }
  }

  async payWithSavedCard(req, res) {
    const { userId, amount, paymentMethodId } = req.body;
    try {
      const result = await this.payWithSavedCardUseCase.execute(
        userId,
        amount,
        paymentMethodId
      );
      res.status(200).json(result);
    } catch (error) {
      console.error("Error processing payment with saved card:", error);
      res.status(400).json({ message: error.message });
    }
  }

  async deleteCard(req, res) {
    const { userId } = req.params;
    try {
      const result = await this.deleteCardUseCase.execute(userId);
      res.status(200).json(result);
    } catch (error) {
      console.error("Error deleting card:", error);
      res.status(400).json({ message: error.message });
    }
  }
}

module.exports = PaymentController;
