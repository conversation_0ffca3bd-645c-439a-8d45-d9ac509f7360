const e = require("cors");

class OtpController {
  constructor({ otpService }) {
    this.otpService = otpService;
  }

  async requestOtp(req, res) {
    try {
      const { email } = req.query;

      if (!email) {
        return res.status(400).json({ error: "Email is required." });
      }
      console.log(email);
      await this.otpService.getOtpAndSendEmail(email);

      return res.status(200).json(true);
    } catch (error) {
      console.error("Error in requestOtp:", error.message);
      return res
        .status(500)
        .json({ error: error.message || "Failed to send OTP." });
    }
  }

  async verifyOtp(req, res) {
    try {
      const data = req.body;
      console.log("came to verify");
      console.log(data);
      if (!data.email || !data.otp) {
        return res.status(400).json({ error: "Email and OTP are required." });
      }

      const isValid = await this.otpService.verifyOtp(data.email, data.otp);
      if (isValid) {
        console.log("verified");
        return res.status(200).json(true);
      } else {
        return res.status(400).json(false);
      }
    } catch (error) {
      console.error("Error in verifyOtp:", error.message);
      return res
        .status(500)
        .json({ error: error.message || "Failed to verify OTP." });
    }
  }
}

module.exports = OtpController;
