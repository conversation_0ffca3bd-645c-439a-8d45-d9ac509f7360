class SearchController {
  constructor(searchService) {
    this.searchService = searchService;
  }

  async search(req, res) {
    try {
      const query = req.query.q || "";
      const coachesQuery = req.query.coach || "";
      console.log("query", query);
      console.log("coachesQuery", coachesQuery);
      // Pagination parameters
      const pagination = {
        page: req.query.page ? parseInt(req.query.page) : 1,
        limit: req.query.limit ? parseInt(req.query.limit) : 50,
      };
      if (coachesQuery) {
        const results = await this.searchService.getCoachesByQuery(
          coachesQuery,
          pagination
        );
        console.log("coaches", results);
        res.status(200).json({
          success: true,
          pagination: {
            page: pagination.page,
            limit: pagination.limit,
            total: results.totalCount || 0,
          },
          results,
        });
      } else {
        // Extract all filter parameters
        const filters = {
          // Price range filters
          priceMin: req.query.minPrice
            ? parseFloat(req.query.minPrice)
            : undefined,
          priceMax: req.query.maxPrice
            ? parseFloat(req.query.maxPrice)
            : undefined,

          // Age range filters
          ageFrom: req.query.minAge ? parseInt(req.query.minAge) : undefined,
          ageTo: req.query.maxAge ? parseInt(req.query.maxAge) : undefined,

          // Location filter
          location: req.query.location,

          // Category filter
          category: req.query.category,

          // Rating filter
          rating: req.query.rating ? parseFloat(req.query.rating) : undefined,

          // SEN services filter
          senService: req.query.senService === "true",
        };

        // Extract sort parameter
        const sortBy = req.query.sortBy || "";

        // Extract geolocation parameters for distance sorting
        const geoLocation = {
          longitude: req.query.longitude
            ? parseFloat(req.query.longitude)
            : undefined,
          latitude: req.query.latitude
            ? parseFloat(req.query.latitude)
            : undefined,
          maxDistance: req.query.maxDistance
            ? parseInt(req.query.maxDistance)
            : 10000, // Default 10km
        };

        // Get search results
        const results = await this.searchService.search(
          query,
          filters,
          sortBy,
          geoLocation,
          pagination
        );

        res.status(200).json({
          success: true,
          pagination: {
            page: pagination.page,
            limit: pagination.limit,
            total: results.totalCount || 0,
          },
          results,
        });
      }
    } catch (error) {
      console.log(error);
      if (error.message === "No results found") {
        res.status(404).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: "Internal server error",
          error: error.message,
        });
      }
    }
  }

  // New method for filtering centers specifically
  async filterCenters(req, res) {
    try {
      // Check if user is a parent - if so, only show promoted centers
      const userType = req.user?.type;
      const promotionOnly = userType === 'parent';
      
      console.log(`filterCenters called by user type: ${userType}, promotionOnly: ${promotionOnly}`);
      
      // Extract all filter parameters
      const filters = {
        // Price range filters
        priceMin: req.query.minPrice
          ? parseFloat(req.query.minPrice)
          : undefined,
        priceMax: req.query.maxPrice
          ? parseFloat(req.query.maxPrice)
          : undefined,

        // Age range filters
        ageFrom: req.query.minAge ? parseInt(req.query.minAge) : undefined,
        ageTo: req.query.maxAge ? parseInt(req.query.maxAge) : undefined,

        // Location filter
        location: req.query.location,

        // Category filter
        category: req.query.category,

        // Rating filter
        rating: req.query.rating ? parseFloat(req.query.rating) : undefined,

        // SEN services filter
        senService: req.query.senService === "true",
        
        // Promotion filter for parent users
        promotion: promotionOnly ? true : undefined,
      };

      // Extract sort parameter
      const sortBy = req.query.sortBy || "";

      // Extract geolocation parameters for distance sorting
      const geoLocation = {
        longitude: req.query.longitude
          ? parseFloat(req.query.longitude)
          : undefined,
        latitude: req.query.latitude
          ? parseFloat(req.query.latitude)
          : undefined,
        maxDistance: req.query.maxDistance
          ? parseInt(req.query.maxDistance)
          : 10000, // Default 10km
      };

      // Pagination parameters
      const pagination = {
        page: req.query.page ? parseInt(req.query.page) : 1,
        limit: req.query.limit ? parseInt(req.query.limit) : 50,
      };

      // Get filtered centers
      const centers = await this.searchService.filterCenters(
        filters,
        sortBy,
        geoLocation,
        pagination
      );

      res.status(200).json({
        success: true,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total: centers.totalCount || 0,
        },
        data: centers.data,
      });
    } catch (error) {
      console.log(error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  // New method for filtering classes specifically
  async filterClasses(req, res) {
    try {
      // Extract all filter parameters
      const filters = {
        // Price range filters
        priceMin: req.query.minPrice
          ? parseFloat(req.query.minPrice)
          : undefined,
        priceMax: req.query.maxPrice
          ? parseFloat(req.query.maxPrice)
          : undefined,

        // Age range filters
        ageFrom: req.query.minAge ? parseInt(req.query.minAge) : undefined,
        ageTo: req.query.maxAge ? parseInt(req.query.maxAge) : undefined,

        // Location filter
        location: req.query.location,

        // Category filter
        category: req.query.category,

        // Rating filter
        rating: req.query.rating ? parseFloat(req.query.rating) : undefined,

        // SEN services filter
        senService: req.query.senService === "true",
      };

      // Extract sort parameter
      const sortBy = req.query.sortBy || "";

      // Pagination parameters
      const pagination = {
        page: req.query.page ? parseInt(req.query.page) : 1,
        limit: req.query.limit ? parseInt(req.query.limit) : 50,
      };

      // Get filtered classes
      const classes = await this.searchService.filterClasses(
        filters,
        sortBy,
        pagination
      );

      res.status(200).json({
        success: true,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total: classes.totalCount || 0,
        },
        data: classes.data,
      });
    } catch (error) {
      console.log(error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }

  // New method for nearby centers
  async findNearbyCenters(req, res) {
    try {
      const { longitude, latitude, maxDistance } = req.query;

      if (!longitude || !latitude) {
        return res.status(400).json({
          success: false,
          message: "Longitude and latitude are required",
        });
      }

      const geoLocation = {
        longitude: parseFloat(longitude),
        latitude: parseFloat(latitude),
        maxDistance: maxDistance ? parseInt(maxDistance) : 10000, // Default 10km
      };

      // Additional filters
      const filters = {
        category: req.query.category,
        rating: req.query.rating ? parseFloat(req.query.rating) : undefined,
        senService: req.query.senService === "true",
        priceMin: req.query.minPrice
          ? parseFloat(req.query.minPrice)
          : undefined,
        priceMax: req.query.maxPrice
          ? parseFloat(req.query.maxPrice)
          : undefined,
        ageFrom: req.query.minAge ? parseInt(req.query.minAge) : undefined,
        ageTo: req.query.maxAge ? parseInt(req.query.maxAge) : undefined,
      };

      // Extract sort parameter
      const sortBy = req.query.sortBy || "";

      // Pagination
      const pagination = {
        page: req.query.page ? parseInt(req.query.page) : 1,
        limit: req.query.limit ? parseInt(req.query.limit) : 50,
      };

      const centers = await this.searchService.findNearbyCenters(
        geoLocation,
        filters,
        sortBy,
        pagination
      );

      res.status(200).json({
        success: true,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total: centers.totalCount || 0,
        },
        data: centers.data,
      });
    } catch (error) {
      console.log(error);
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
      });
    }
  }
}

module.exports = SearchController;
