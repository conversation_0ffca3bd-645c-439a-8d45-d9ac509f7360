const path = require("path");
class ChildController {
  constructor({ childUseCase }) {
    this.childUseCase = childUseCase;
  }

  async createChild(req, res) {
    try {
      console.log("Request body:", req.body);
      console.log("Request files:", req.files);
      
      // Process files from the request
      const childData = this._processFiles(req);
      console.log("Processed child data:", JSON.stringify(childData, null, 2));

      // Validate required fields
      if (!childData.parent) {
        return res.status(400).json({ error: "Parent ID is required" });
      }
      
      if (!childData.fullname) {
        return res.status(400).json({ error: "Full name is required" });
      }
      
      if (!childData.idcard) {
        return res.status(400).json({ error: "ID card is required" });
      }
      
      if (!childData.birthday) {
        return res.status(400).json({ error: "Birthday is required" });
      }
      
      // Create the child record
      const result = await this.childUseCase.createChild(childData);
      res.status(201).json(result);
    } catch (error) {
      console.error("Error creating child:", error.message);
      res.status(500).json({ error: error.message });
    }
  }

  async getChildById(req, res) {
    try {
      const { id } = req.params;
      const child = await this.childUseCase.getChildById(id);
      if (!child) {
        return res.status(404).json({ message: "Child not found" });
      }
      res.status(200).json(child);
    } catch (error) {
      console.error("Error fetching child by ID:", error.message);
      res.status(500).json({ error: error.message });
    }
  }

  async getChildByParentId(req, res) {
    try {
      console.log(this.childUseCase)
      const { parentId } = req.params;
      const children = await this.childUseCase.getChildByParentId(parentId);
      res.status(200).json(children);
    } catch (error) {
      console.error("Error fetching children by parent ID:", error.message);
      res.status(500).json({ error: error.message });
    }
  }

  async updateChild(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      
      console.log("Update request for child ID:", id);
      console.log("Update data received:", updateData);
      
      // Validate basic requirements
      if (!id) {
        return res.status(400).json({ error: "Child ID is required" });
      }
      
      // Validate birthday format if provided
      if (updateData.birthday) {
        const birthdayPattern = /^\d{4}-\d{2}-\d{2}$/;
        if (!birthdayPattern.test(updateData.birthday) || updateData.birthday.includes('--')) {
          return res.status(400).json({ error: "Invalid birthday format. Expected YYYY-MM-DD" });
        }
      }
      
      // Handle file uploads
      if (req.files && req.files.mainImage && req.files.mainImage.length > 0) {
        console.log("Processing mainImage file upload");
        updateData.mainImage = {
          url: req.files.mainImage[0].path,
          contentType: req.files.mainImage[0].mimetype,
          originalname: req.files.mainImage[0].originalname,
        };
      }

      const updatedChild = await this.childUseCase.updateChildById(
        id,
        updateData
      );
      
      if (!updatedChild) {
        return res.status(404).json({ message: "Child not found" });
      }
      
      console.log("Child updated successfully");
      res.status(200).json(updatedChild);
    } catch (error) {
      console.error("Error updating child:", error.message);
      console.error("Error stack:", error.stack);
      res.status(500).json({ error: error.message });
    }
  }

  async deleteChildById(req, res) {
    try {
      const { id } = req.params;
      const deletedChild = await this.childUseCase.deleteChildById(id);
      if (!deletedChild) {
        return res.status(404).json({ message: "Child not found" });
      }
      res.status(200).json({ message: "Child deleted successfully" });
    } catch (error) {
      console.error("Error deleting child:", error.message);
      res.status(500).json({ error: error.message });
    }
  }
  
  async getParticipationStats(req, res) {
    try {
      const { childId } = req.params;
      const stats = await this.childUseCase.getParticipationStats(childId);
      res.status(200).json(stats);
    } catch (error) {
      console.error("Error fetching participation stats:", error.message);
      res.status(500).json({ error: error.message });
    }
  }
  
  // New method to get performance metrics for a child
  async getPerformanceMetrics(req, res) {
    try {
      const { childId } = req.params;
      const child = await this.childUseCase.getChildById(childId);
      
      if (!child) {
        return res.status(404).json({ message: "Child not found" });
      }
      
      // Extract performance metrics
      const metrics = {
        outstandingQuality: child.outstandingQuality || 0,
        keyCompetency: child.keyCompetency || 0,
        distinctiveConduct: child.distinctiveConduct || "",
        learningProgress: child.learningProgress || "",
        metricsLastUpdated: child.metricsLastUpdated || new Date()
      };
      
      res.status(200).json(metrics);
    } catch (error) {
      console.error("Error fetching performance metrics:", error.message);
      res.status(500).json({ error: error.message });
    }
  }
  
  _processFiles(req) {
    const childData = req.body;

    // Handle file uploads if present in the request
    if (req.files) {
      console.log("Processing files:", Object.keys(req.files));
      
      // Process main image
      if (req.files.mainImage && req.files.mainImage.length > 0) {
        console.log("Main Image file path:", req.files.mainImage[0].path);
        childData.mainImage = {
          path: req.files.mainImage[0].path,
          url: req.files.mainImage[0].path,
          contentType: req.files.mainImage[0].mimetype,
          originalname: req.files.mainImage[0].originalname,
        };
      } else {
        console.log("No mainImage file found in request");
      }
    } else {
      console.log("No files in request");
    }

    return childData;
  }
}

module.exports = ChildController;
