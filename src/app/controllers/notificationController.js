const NotificationService = require("../services/notificationService");

class NotificationController {
  constructor(notificationService) {
    this.notificationService = notificationService;
  }
  async saveOrUpdateToken(req, res) {
    try {
      const { token, userId } = req.body;
      const finalToken = await this.notificationService.saveOrUpdateToken(
        userId,
        token
      );
      res.status(201).json(finalToken);
    } catch (error) {
      res.status(400).json(error.message);
    }
  }
  async getTokenById(req, res) {
    try {
      const userId = req.query.id;
      const token = await NotificationService.getTokenById(userId);
      res.status(201).json({ token: token.token });
    } catch (error) {
      res.status(400).json(error.message);
    }
  }
  async getNotification(req, res) {
    try {
      const userId = req.query.id;
      console.log(userId);
      const notifications = await this.notificationService.getNotification(
        userId
      );
      res.status(201).json(notifications);
    } catch (error) {
      res.status(400).json(error.message);
    }
  }
  async deleteNotification(req, res) {
    const { id } = req.params.id;
    
  }
}

module.exports = NotificationController;
