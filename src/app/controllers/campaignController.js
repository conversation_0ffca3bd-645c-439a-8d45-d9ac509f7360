class CampaignController {
  constructor(campaignService, campaignDiscountService = null) {
    this.campaignService = campaignService;
    this.campaignDiscountService = campaignDiscountService;
  }

  async createCampaign(req, res) {
    try {
      const campaign = await this.campaignService.createCampaign(req.body);
      
      // Auto-sync discount codes if campaign discount service is available
      if (this.campaignDiscountService && campaign.discountCode) {
        try {
          await this.campaignDiscountService.createOrUpdateDiscountFromCampaign(campaign);
        } catch (discountError) {
          console.warn('Failed to create discount code for campaign:', discountError.message);
        }
      }
      
      res.status(201).json({
        success: true,
        data: campaign,
        message: "Campaign created successfully"
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async getActiveCampaigns(req, res) {
    try {
      const campaigns = await this.campaignService.getActiveCampaigns();
      
      // Increment impression count for each campaign
      for (const campaign of campaigns) {
        await this.campaignService.incrementImpressionCount(campaign._id);
      }
      
      res.status(200).json({
        success: true,
        data: campaigns
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async getAllCampaigns(req, res) {
    try {
      const { page = 1, limit = 10 } = req.query;
      const campaigns = await this.campaignService.getAllCampaigns(
        parseInt(page),
        parseInt(limit)
      );
      res.status(200).json({
        success: true,
        data: campaigns
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async getCampaignById(req, res) {
    try {
      const { campaignId } = req.params;
      const campaign = await this.campaignService.getCampaignById(campaignId);
      res.status(200).json({
        success: true,
        data: campaign
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: error.message
      });
    }
  }

  async updateCampaign(req, res) {
    try {
      const { id } = req.params;
      const campaign = await this.campaignService.updateCampaign(id, req.body);
      
      // Auto-sync discount codes if campaign discount service is available
      if (this.campaignDiscountService && campaign.discountCode) {
        try {
          await this.campaignDiscountService.createOrUpdateDiscountFromCampaign(campaign);
        } catch (discountError) {
          console.warn('Failed to update discount code for campaign:', discountError.message);
        }
      }
      
      res.status(200).json({
        success: true,
        data: campaign,
        message: "Campaign updated successfully"
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async deleteCampaign(req, res) {
    try {
      const { id } = req.params;
      await this.campaignService.deleteCampaign(id);
      res.status(200).json({
        success: true,
        message: 'Campaign deleted successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async handleCampaignClick(req, res) {
    try {
      const { campaignId } = req.params;
      const campaign = await this.campaignService.handleCampaignClick(campaignId);
      res.status(200).json({
        success: true,
        data: campaign,
        message: "Campaign click recorded"
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: error.message
      });
    }
  }

  async getCampaignAnalytics(req, res) {
    try {
      const { campaignId } = req.params;
      const analytics = await this.campaignService.getCampaignAnalytics(campaignId);
      res.status(200).json({
        success: true,
        data: analytics
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        error: error.message
      });
    }
  }

  // New methods for campaign discount integration
  async syncCampaignDiscounts(req, res) {
    try {
      if (!this.campaignDiscountService) {
        return res.status(503).json({
          success: false,
          message: 'Campaign discount service not available'
        });
      }

      const synced = await this.campaignDiscountService.syncCampaignDiscounts();
      res.status(200).json({
        success: true,
        data: synced,
        message: `Synced ${synced.length} campaign discounts`
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async getAvailableCampaignDiscounts(req, res) {
    try {
      if (!this.campaignDiscountService) {
        return res.status(503).json({
          success: false,
          message: 'Campaign discount service not available'
        });
      }

      const { userId } = req.params;
      const discounts = await this.campaignDiscountService.getAvailableCampaignDiscounts(userId);
      res.status(200).json({
        success: true,
        data: discounts
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
}

module.exports = CampaignController;