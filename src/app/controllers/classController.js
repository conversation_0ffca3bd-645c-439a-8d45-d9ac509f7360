class ClassController {
  constructor({ classUseCase }) {
    this.classUseCase = classUseCase;
  }

  async addClass(req, res) {
    try {
      const classData = this._processFiles(req);
      console.log("Controller");

      console.log("Class Data:", classData);

      const result = await this.classUseCase.create(classData);
      res.status(201).json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async checkMinimumStudents(req, res) {
    try {
      const { classId, dateId } = req.params;
      
      if (!classId || !dateId) {
        return res.status(400).json({ 
          success: false, 
          message: "Class ID and Date ID are required" 
        });
      }

      const result = await this.classUseCase.checkMinimumStudents(classId, dateId);
      
      return res.status(200).json({
        success: true,
        data: result,
        meetsMinimumRequirement: result.meetsMinimumRequirement,
        currentStudents: result.currentStudents,
        minimumRequired: result.minimumRequired
      });
    } catch (error) {
      console.error("Error checking minimum students:", error);
      return res.status(500).json({ 
        success: false, 
        message: error.message || "Failed to check minimum students requirement" 
      });
    }
  }

  async cancelClassForInsufficientStudents(req, res) {
    try {
      const { classId, dateId } = req.params;
      
      if (!classId || !dateId) {
        return res.status(400).json({ 
          success: false, 
          message: "Class ID and Date ID are required" 
        });
      }

      // First check if the class meets minimum requirements
      const checkResult = await this.classUseCase.checkMinimumStudents(classId, dateId);
      
      if (checkResult.meetsMinimumRequirement) {
        return res.status(400).json({
          success: false,
          message: "Cannot cancel class - minimum student requirement is met",
          currentStudents: checkResult.currentStudents,
          minimumRequired: checkResult.minimumRequired
        });
      }
      
      // If requirement is not met, proceed with cancellation
      const result = await this.classUseCase.cancelClassDate(
        classId, 
        dateId, 
        "Insufficient students enrolled", 
        "automatic_cancellation"
      );
      
      return res.status(200).json({
        success: true,
        message: "Class cancelled due to insufficient students",
        data: result
      });
    } catch (error) {
      console.error("Error cancelling class:", error);
      return res.status(500).json({ 
        success: false, 
        message: error.message || "Failed to cancel class" 
      });
    }
  }

  async getClass(req, res) {
    try {
      const classId = req.params.id;

      // Prevent common routing mistakes
      if (classId === 'students') {
        return res.status(400).json({
          error: 'Invalid class ID',
          message: 'It looks like you\'re trying to access students. Please use the correct endpoint: /api/class/{slotId}/students'
        });
      }

      const classs = await this.classUseCase.getById(classId);
      res.status(200).json(classs);
    } catch (error) {
      console.log('Error in getClass:', error.message);
      res.status(404).json({ error: error.message });
    }
  }

  // Get all classes
  async getAllClasses(req, res) {
    try {
      const category = req.query.category || "";
      console.log(category);
      const { skip, limit } = req.query;
      if (category) {
        console.log('in category');
        const classes = await this.classUseCase.getByCategory(category);console.log(`category ${classes}`);
        res.status(200).json(classes);
      } else {
        const classes = await this.classUseCase.getAll(
          Number(skip),
          Number(limit)
        );
        res.status(200).json(classes);
      }
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  // Update a class
  async updateClass(req, res) {
    try {
      const classId = req.params.id;
      const classData = this._processFiles(req);

      const updatedClass = await this.classUseCase.update(classId, classData);
      res.status(200).json(updatedClass);
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  // Delete a class
  async deleteClass(req, res) {
    try {
      const classId = req.params.id;
      const result = await this.classUseCase.delete(classId);

      if (result) {
        // If deletion is successful, send true
        res.status(200).json({ success: true });
      } else {
        // If no class was found with the given ID, send false
        res.status(404).json({ success: false, message: "Class not found" });
      }
    } catch (error) {
      res.status(500).json({ success: false, error: error.message });
    }
  }

  // Get all classes by center ID
  async getClassesByCenter(req, res) {
    try {
      console.log('req.params');
      const centerId = req.params.id;
      const classes = await this.classUseCase.getByCenter(centerId);
      res.status(200).json(classes);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async getClassesByCoach(req, res) {
    try {
      console.log(req.params);
      const coachId = req.params.id;
      const classes = await this.classUseCase.getClassesByCoach(coachId);
      res.status(200).json(classes);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async getStudents(req, res) {
    try {
      console.log('getStudents - req.params:', req.params);
      console.log('getStudents - req.query:', req.query);
      const slotId = req.params.slotId;

      // Validate slotId
      if (!slotId || slotId.trim() === '') {
        return res.status(400).json({
          error: 'Slot ID is required',
          message: 'Please provide a valid slot ID to fetch students'
        });
      }

      const students = await this.classUseCase.getStudents(slotId);
      res.status(200).json(students);
    } catch (error) {
      console.log('Error in getStudents:', error.message);
      res.status(500).json({ error: error.message });
    }
  }
  async getCoachCenter(req, res) {
    try {
      const classId = req.params.classId;
      const coachCenter = await this.classUseCase.getCoachCenter(classId);
      res.status(200).json(coachCenter);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  // Get classes for parent view of a center
  async getClassesForParent(req, res) {
    try {
      const centerId = req.params.centerId;
      
      // Get query parameters for filtering
      const { 
        minAge, 
        maxAge, 
        category,
        startDate,
        endDate,
        priceRange,
        language
      } = req.query;

      const filters = {
        minAge: minAge ? parseInt(minAge) : undefined,
        maxAge: maxAge ? parseInt(maxAge) : undefined,
        category,
        startDate,
        endDate,
        priceRange: priceRange ? JSON.parse(priceRange) : undefined,
        language
      };

      const classes = await this.classUseCase.getClassesForParent(centerId, filters);
      
      res.status(200).json(classes);
    } catch (error) {
      console.error("Error fetching classes for parent:", error);
      res.status(500).json({ 
        success: false,
        message: "Failed to fetch classes",
        error: error.message 
      });
    }
  }

  async updateCoach(req, res) {
    try {
      console.log(req.body);
      const classId = req.params.classId;
      const { coachId } = req.body;

      if (!coachId) {
        return res.status(400).json({ error: "Coach ID is required" });
      }

      const updatedClass = await this.classUseCase.updateCoach(classId, coachId);
      res.status(200).json(updatedClass);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getClassSlots(req, res) {
    try {
      const classId = req.params.classId;
      const slots = await this.classUseCase.getClassSlots(classId);
      res.status(200).json({ success: true, slots });
    } catch (error) {
      res.status(500).json({ success: false, message: error.message });
    }
  }

  /**
   * Delete a slot (class date) by its slotId
   * @param {import('express').Request} req
   * @param {import('express').Response} res
   * @returns {Promise<void>}
   */
  async deleteSlotById(req, res) {
    try {
      const slotId = req.params.slotId;
      if (!slotId) {
        return res.status(400).json({ success: false, message: 'Slot ID is required' });
      }
      const deletedSlot = await this.classUseCase.deleteSlotById(slotId);
      if (!deletedSlot) {
        return res.status(404).json({ success: false, message: 'Slot not found' });
      }
      res.status(200).json({ success: true, slot: deletedSlot });
    } catch (error) {
      res.status(500).json({ success: false, message: error.message });
    }
  }

  _processFiles(req) {
    const centerData = req.body;

    if (req.files) {
      if (req.files.hkidCard) {
        console.log(
          "HKID Card file paths:",
          req.files.hkidCard.map((file) => file.path)
        );
        centerData.hkidCard = req.files.hkidCard.map((file) => ({
          path: file.path, // Preserve path explicitly
          url: file.path || file.url,
          contentType: file.mimetype,
          originalname: file.originalname,
        }));
      }
      if (req.files.businessCertificate) {
        console.log(
          "Business Certificate file paths:",
          req.files.businessCertificate.map((file) => file.path)
        );
        centerData.businessCertificate = req.files.businessCertificate.map(
          (file) => ({
            path: file.path, // Preserve path explicitly
            url: file.path || file.url,
            contentType: file.mimetype,
            originalname: file.originalname,
          })
        );
      }
      if (req.files.mainImage) {
        console.log("Main Image file path:", req.files.mainImage[0].path);
        centerData.mainImage = {
          path: req.files.mainImage[0].path, // Preserve path explicitly
          url: req.files.mainImage[0].path || req.files.mainImage[0].url,
          contentType: req.files.mainImage[0].mimetype,
          originalname: req.files.mainImage[0].originalname,
        };
      }
      if (req.files.images) {
        console.log(
          "Images file paths:",
          req.files.images.map((file) => file.path)
        );
        centerData.images = req.files.images.map((file) => ({
          path: file.path, // Preserve path explicitly
          url: file.path || file.url,
          contentType: file.mimetype,
          originalname: file.originalname,
        }));
      }
    }

    return centerData;
  }
}

module.exports = ClassController;
