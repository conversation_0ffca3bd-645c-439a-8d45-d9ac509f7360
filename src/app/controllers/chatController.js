class ChatController {
  constructor(chatService) {
    this.chatService = chatService;
  }

  async getChats(req, res) {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const { senderId, recipientId } = req.params;

    try {
      console.log(`Controller: Getting chats between ${senderId} and ${recipientId}`);
      const chat = await this.chatService.getChats(
        senderId,
        recipientId,
        page,
        limit
      );
      
      return res.status(200).json({
        success: true,
        count: chat.length,
        data: chat
      });
    } catch (error) {
      console.error(`Error getting chats between ${senderId} and ${recipientId}:`, error);
      return res.status(500).json({ 
        success: false, 
        message: error.message || "Failed to retrieve chat messages"
      });
    }
  }

  async getChatByUserId(req, res) {
    try {
      const { userId } = req.params;
      console.log(`Controller: Getting chats for user ${userId}`);
      
      if (!userId) {
        return res.status(400).json({
          success: false,
          message: "User ID is required"
        });
      }

      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      
      const chat = await this.chatService.getUserChats(userId, page, limit);
      
      // Always return consistent structure with empty array for no chats
      return res.status(200).json({
        success: true,
        count: chat.length,
        data: chat
      });
    } catch (error) {
      console.error(`Error getting chats for user ${req.params.userId}:`, error);
      return res.status(500).json({ 
        success: false, 
        message: error.message || "Failed to retrieve user chat history"
      });
    }
  }

  async createChats(req, res) {
    const { message, sender, recipient, senderModel, recipientModel } = req.body;
    
    // Validate required fields
    if (!message || !sender || !recipient || !senderModel || !recipientModel) {
      return res.status(400).json({
        success: false,
        message: "Missing required fields: message, sender, recipient, senderModel, and recipientModel are all required"
      });
    }

    try {
      console.log(`Controller: Creating new chat message from ${sender} to ${recipient}`);
      const chat = await this.chatService.addChat(
        message,
        sender,
        recipient,
        senderModel,
        recipientModel
      );
      
      return res.status(201).json({
        success: true,
        data: chat,
        status: chat.status || "sent"
      });
    } catch (error) {
      console.error(`Error creating chat message:`, error);
      return res.status(500).json({ 
        success: false, 
        message: error.message || "Failed to create chat message"
      });
    }
  }

  async getUserConversations(req, res) {
    try {
      const { userId } = req.params;
      console.log(`Controller: Getting conversations for user ${userId}`);
      
      // Early validation - return fast for invalid input
      if (!userId) {
        return res.status(400).json({
          success: false,
          message: "User ID is required",
          data: {
            conversations: []
          }
        });
      }

      // Parse query parameters with defaults
      const limit = parseInt(req.query.limit) || 20;
      const skip = parseInt(req.query.skip) || 0;
      
      // Check for conditional requests using If-None-Match header
      const clientETag = req.headers['if-none-match'];
      const serverETag = `"conversations-${userId}-${Math.floor(Date.now() / (1000 * 300))}"`; // ETag valid for 5 minutes
      
      // If client has current version, return 304 Not Modified
      if (clientETag && clientETag === serverETag) {
        return res.status(304).end();
      }
      
      // Set ETag header for caching
      res.set('ETag', serverETag);
      
      // Get conversations with optimized error handling
      let result;
      try {
        // Set a timeout for the service call to prevent long-running queries
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Service timeout')), 5000);
        });
        
        // Race between actual query and timeout
        result = await Promise.race([
          this.chatService.getUserConversations(userId, limit, skip),
          timeoutPromise
        ]);
      } catch (error) {
        console.error(`Error in service layer: ${error.message}`);
        // If service error, return an empty but valid response
        return res.status(200).json({
          success: true,
          count: 0,
          total: 0,
          data: {
            conversations: []
          }
        });
      }
      
      // Ensure the conversations property exists and is an array
      if (!result || !result.conversations) {
        console.warn(`Invalid result structure returned for user ${userId}, providing empty response`);
        return res.status(200).json({
          success: true,
          count: 0,
          total: 0,
          data: {
            conversations: []
          }
        });
      }
      
      // Normalize the response structure to match what the client expects
      // Use a more efficient approach to build the response
      const responseData = {
        success: true,
        count: Array.isArray(result.conversations) ? result.conversations.length : 0,
        total: typeof result.total === 'number' ? result.total : 0,
        data: {
          conversations: Array.isArray(result.conversations) ? result.conversations : []
        }
      };
      
      // Log performance metrics
      console.log(`Returning ${responseData.count} conversations for user ${userId} (total: ${responseData.total})`);
      
      // Return the response
      return res.status(200).json(responseData);
    } catch (error) {
      console.error(`Error getting conversations for user ${req.params.userId}:`, error);
      // Even on error, return a valid structure to prevent client crashes
      return res.status(500).json({ 
        success: false, 
        message: error.message || "Failed to retrieve user conversations",
        data: {
          conversations: []
        }
      });
    }
  }
}

module.exports = ChatController;
