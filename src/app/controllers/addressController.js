const User = require('../models/userModel');
const mongoose = require('mongoose');

class AddressController {
  constructor({ updateUser }) {
    this.updateUser = updateUser;
  }

  // Get all addresses for a user
  async getUserAddresses(req, res) {
    try {
      const userId = req.params.userId;
      
      if (!userId) {
        return res.status(400).json({ success: false, message: 'User ID is required' });
      }

      const user = await User.findById(userId);
      
      if (!user) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      return res.status(200).json({ 
        success: true, 
        addresses: user.location || [] 
      });
    } catch (error) {
      console.error('Error fetching user addresses:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'An error occurred while fetching addresses',
        error: error.message 
      });
    }
  }

  // Get default address for a user
  async getDefaultAddress(req, res) {
    try {
      const userId = req.params.userId;
      
      if (!userId) {
        return res.status(400).json({ success: false, message: 'User ID is required' });
      }

      const user = await User.findById(userId);
      
      if (!user) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      const defaultAddress = user.location && user.location.find(addr => addr.default === true);
      
      if (!defaultAddress) {
        return res.status(404).json({ 
          success: false, 
          message: 'No default address found for this user' 
        });
      }

      return res.status(200).json({ 
        success: true, 
        address: defaultAddress 
      });
    } catch (error) {
      console.error('Error fetching default address:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'An error occurred while fetching the default address',
        error: error.message 
      });
    }
  }

  // Add a new address for a user
  async addAddress(req, res) {
    try {
      const userId = req.params.userId;
      const addressData = req.body;
      
      if (!userId) {
        return res.status(400).json({ success: false, message: 'User ID is required' });
      }

      if (!addressData || !addressData.flatFloorBlock || !addressData.buildingEstate || 
          !addressData.district || !addressData.region) {
        return res.status(400).json({ 
          success: false, 
          message: 'Address data is incomplete. Please provide flatFloorBlock, buildingEstate, district, and region' 
        });
      }

      const user = await User.findById(userId);
      
      if (!user) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      // Set up coordinates, to be used for mapping
      const coordinates = addressData.coordinates || { lat: null, lng: null };
      
      // Create new address object with mapping data
      const newAddress = {
        flatFloorBlock: addressData.flatFloorBlock,
        buildingEstate: addressData.buildingEstate,
        district: addressData.district,
        region: addressData.region,
        country: addressData.country || "Hong Kong",
        coordinates: coordinates,
        default: addressData.default || false
      };

      // If this is the first address or marked as default, update existing addresses
      if ((!user.location || user.location.length === 0) || addressData.default === true) {
        // If this is set as default, set all others to non-default
        if (user.location && user.location.length > 0) {
          user.location.forEach(addr => {
            addr.default = false;
          });
        }
        newAddress.default = true;
      }

      // Add the new address to the user's locations
      const updatedUser = await User.findByIdAndUpdate(
        userId,
        { $push: { location: newAddress } },
        { new: true, runValidators: true }
      );

      return res.status(201).json({ 
        success: true, 
        message: 'Address added successfully',
        user: updatedUser
      });
    } catch (error) {
      console.error('Error adding address:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'An error occurred while adding the address',
        error: error.message 
      });
    }
  }

  // Update an address
  async updateAddress(req, res) {
    try {
      const userId = req.params.userId;
      const addressId = req.params.addressId;
      const updateData = req.body;
      
      if (!userId || !addressId) {
        return res.status(400).json({ 
          success: false, 
          message: 'User ID and Address ID are required' 
        });
      }

      if (!updateData) {
        return res.status(400).json({ 
          success: false, 
          message: 'Update data is required' 
        });
      }

      const user = await User.findById(userId);
      
      if (!user) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      // Find the address to update
      const addressIndex = user.location.findIndex(
        addr => addr._id.toString() === addressId
      );

      if (addressIndex === -1) {
        return res.status(404).json({ 
          success: false, 
          message: 'Address not found' 
        });
      }

      // Update the address with the new data
      Object.keys(updateData).forEach(key => {
        if (key !== '_id' && key !== 'default') { // Don't update _id directly and handle default separately
          user.location[addressIndex][key] = updateData[key];
        }
      });

      // If setting this address as default, update all others
      if (updateData.default === true) {
        user.location.forEach((addr, index) => {
          addr.default = index === addressIndex;
        });
      }

      // Save the updated user
      await user.save();

      return res.status(200).json({ 
        success: true, 
        message: 'Address updated successfully',
        user: user
      });
    } catch (error) {
      console.error('Error updating address:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'An error occurred while updating the address',
        error: error.message 
      });
    }
  }

  // Delete an address
  async deleteAddress(req, res) {
    try {
      const userId = req.params.userId;
      const addressId = req.params.addressId;
      
      if (!userId || !addressId) {
        return res.status(400).json({ 
          success: false, 
          message: 'User ID and Address ID are required' 
        });
      }

      // Use the updateUser service method to delete the address
      const result = await this.updateUser.deleteAddress(userId, addressId);

      return res.status(200).json({ 
        success: true, 
        message: 'Address deleted successfully',
        user: result.data.parent
      });
    } catch (error) {
      console.error('Error deleting address:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'An error occurred while deleting the address',
        error: error.message 
      });
    }
  }

  // Set an address as default
  async setDefaultAddress(req, res) {
    try {
      const userId = req.params.userId;
      const addressId = req.params.addressId;
      
      if (!userId || !addressId) {
        return res.status(400).json({ 
          success: false, 
          message: 'User ID and Address ID are required' 
        });
      }

      // Use the updateUser service method to set default address
      const result = await this.updateUser.changeDefaultAddress(userId, addressId);

      return res.status(200).json({ 
        success: true, 
        message: 'Default address updated successfully',
        user: result.data.parent
      });
    } catch (error) {
      console.error('Error setting default address:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'An error occurred while setting the default address',
        error: error.message 
      });
    }
  }

  // Get an address with map display data
  async getAddressWithMap(req, res) {
    try {
      const userId = req.params.userId;
      const addressId = req.params.addressId;
      
      if (!userId) {
        return res.status(400).json({ success: false, message: 'User ID is required' });
      }

      const user = await User.findById(userId);
      
      if (!user) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      let address;
      
      if (addressId) {
        // Find specific address
        address = user.location.find(addr => addr._id.toString() === addressId);
        
        if (!address) {
          return res.status(404).json({ 
            success: false, 
            message: 'Address not found' 
          });
        }
      } else {
        // Find default address if no specific address ID provided
        address = user.location.find(addr => addr.default === true);
        
        if (!address) {
          // If no default, return the first address if available
          address = user.location[0];
          
          if (!address) {
            return res.status(404).json({ 
              success: false, 
              message: 'No addresses found for this user' 
            });
          }
        }
      }

      // Format the address for display and mapping
      const formattedAddress = {
        id: address._id,
        fullAddress: `${address.flatFloorBlock}, ${address.buildingEstate}, ${address.district}, ${address.region}, ${address.country || 'Hong Kong'}`,
        flatFloorBlock: address.flatFloorBlock,
        buildingEstate: address.buildingEstate,
        district: address.district,
        region: address.region,
        country: address.country || 'Hong Kong',
        default: address.default,
        coordinates: address.coordinates || { lat: null, lng: null }
      };

      return res.status(200).json({ 
        success: true, 
        address: formattedAddress 
      });
    } catch (error) {
      console.error('Error fetching address with map:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'An error occurred while fetching the address with map',
        error: error.message 
      });
    }
  }
}

module.exports = AddressController; 