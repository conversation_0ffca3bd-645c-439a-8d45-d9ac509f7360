class AttendanceController {
  constructor({ attendanceUseCase, attendanceRepo }) {
    this.attendanceUseCase = attendanceUseCase;
    this.attendanceRepo = attendanceRepo;
  }

  async generateCode(req, res) {
    try {
      const { classId, studentId, classDate } = req.body;
      const result = await this.attendanceUseCase.create(
        classId,
        studentId,
        classDate
      );
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async markAttendance(req, res) {
    try {
      const { classId, studentId, status } = req.body;
      await this.attendanceUseCase.update(classId, studentId, status);
      res.status(200).json({ message: "Attendance marked" });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getClassAttendance(req, res) {
    try {
      const { classId } = req.params;
      const attendanceRecords = await this.attendanceRepo.getClassAttendance(
        classId
      );
      res.status(200).json(attendanceRecords);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async getPresentAttendance(req, res) {
    try {
      const { classId } = req.params;
      const { classDate } = req.params;
      console.log(classDate);
      const attendanceRecords = await this.attendanceRepo.getPresentAttendance(
        classId,
        classDate
      );
      res.status(200).json(attendanceRecords);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async getExistingQRCode(req, res) {
    try {
      const { classId, studentId } = req.body;
      const result = await this.attendanceUseCase.getExistingQRCode(
        classId,
        studentId
      );
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async verifyAttendance(req, res) {
    console.log("Controller verifyAttendance method called");
    try {
      console.log("Request body:", req.body);
      const { qrCodeData, classId, code, classDate } = req.body;

      if (!qrCodeData && (!classId || !code)) {
        console.log("Missing qrCodeData or classId and code in request body");
        return res.status(400).json({ message: "Missing data" });
      }

      let isVerified;

      if (qrCodeData) {
        console.log("qrCodeData received:", qrCodeData);
        isVerified = await this.attendanceUseCase.verify(qrCodeData, classDate);
      } else if (classId && code) {
        console.log("classId and code received:", classId, code);
        isVerified = await this.attendanceUseCase.verifyByClassIdAndCode(
          classId,
          code,
          classDate
        );
      }

      console.log("Verification result:", isVerified);

      if (isVerified) {
        res.status(200).json({ isVerified });
      } else {
        res.status(400).json({ message: "Invalid code or classId" });
      }
    } catch (error) {
      console.error("Error in verifyAttendance method:", error);
      res.status(500).json({ error: error.message });
    }
  }
  async getHistoryBychildId(req, res) {
    const { childId } = req.params;
    try {
      const history = await this.attendanceUseCase.getHistoryBychildId(childId);
      res.status(200).json(history);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = AttendanceController;
