class ClasszIdController {
  constructor({ classzIdUseCase }) {
    this.classzIdUseCase = classzIdUseCase;
  }

  async checkMissingIds(req, res) {
    try {
      const result = await this.classzIdUseCase.checkMissingIds();
      res.status(200).json(result);
    } catch (error) {
      console.error("Error in ClasszIdController.checkMissingIds:", error);
      res.status(500).json({ message: "Error checking missing IDs", error: error.message });
    }
  }

  async generateMissingIds(req, res) {
    try {
      const result = await this.classzIdUseCase.generateMissingIds();
      res.status(200).json(result);
    } catch (error) {
      console.error("Error in ClasszIdController.generateMissingIds:", error);
      res.status(500).json({ message: "Error generating missing IDs", error: error.message });
    }
  }
}

module.exports = ClasszIdController; 