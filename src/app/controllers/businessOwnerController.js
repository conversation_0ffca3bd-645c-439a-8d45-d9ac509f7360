class BusinessOwnerController {
  constructor(businessOwnerService) {
    this.businessOwnerService = businessOwnerService;
  }
  _processFiles(req) {
    const centerData = req.body;

    if (req.files) {
      if (req.files.hkidCard) {
        console.log(
          "HKID Card file paths:",
          req.files.hkidCard.map((file) => file.path)
        );
        centerData.hkidCard = req.files.hkidCard.map((file) => ({
          path: file.path, // Preserve path explicitly
          url: file.path || file.url,
          contentType: file.mimetype,
          originalname: file.originalname,
        }));
      }
      if (req.files.businessCertificate) {
        console.log(
          "Business Certificate file paths:",
          req.files.businessCertificate.map((file) => file.path)
        );
        centerData.businessCertificate = req.files.businessCertificate.map(
          (file) => ({
            path: file.path, // Preserve path explicitly
            url: file.path || file.url,
            contentType: file.mimetype,
            originalname: file.originalname,
          })
        );
      }
      if (req.files.sexualConvictionRecord) {
        console.log(
          "Sexual Conviction Record file paths:",
          req.files.sexualConvictionRecord.map((file) => file.path)
        );
        centerData.sexualConvictionRecord = req.files.sexualConvictionRecord.map(
          (file) => ({
            path: file.path, // Preserve path explicitly
            url: file.path || file.url,
            contentType: file.mimetype,
            originalname: file.originalname,
          })
        );
      }
      if (req.files.mainImage) {
        console.log("Main Image file path:", req.files.mainImage[0].path);
        centerData.mainImage = {
          path: req.files.mainImage[0].path, // Preserve path explicitly
          url: req.files.mainImage[0].path || req.files.mainImage[0].url,
          contentType: req.files.mainImage[0].mimetype,
          originalname: req.files.mainImage[0].originalname,
        };
      }
      if (req.files.images) {
        console.log(
          "Images file paths:",
          req.files.images.map((file) => file.path)
        );
        centerData.images = req.files.images.map((file) => ({
          path: file.path, // Preserve path explicitly
          url: file.path || file.url,
          contentType: file.mimetype,
          originalname: file.originalname,
        }));
      }
    }

    return centerData;
  }
  async getOwnerById(req, res) {
    const id = req.params.id;
    try {
      const getData = await this.businessOwnerService.getOwnerById(id);
      res.status(200).json(getData);
    } catch (error) {
      res.status(404).json(error.message);
    }
  }
  async updateOwner(req, res) {
    const id = req.params.id;

    const data = this._processFiles(req);
    try {
      const updateData = await this.businessOwnerService.update(id, data);
     console.log('update done');
     console.log(updateData);
      res.status(200).json(updateData);
    } catch (error) {
      res.status(500).json(error.message);
    }
  }
  async getBranchByOwner(req, res) {
    const id = req.params.id;
    try {
      const branches = await this.businessOwnerService.getBranchByOwner(id);
      res.status(200).json(branches);
    } catch (error) {
      res.status(404).json(error.message);
    }
  }
  async deleteBranch(req, res) {
    const id = req.params.id;
    try {
      const deleteData = await this.businessOwnerService.deleteBranch(id);
      res.status(200).json(deleteData);
    } catch (error) {
      res.status(404).json(error.message);
    }
  }
  async updateBranch(req, res) {
    const id = req.params.id;
    const data = req.body;
    try {
      const updateData = await this.businessOwnerService.updateBranch(id, data);
      res.status(200).json(updateData);
    } catch (error) {
      res.status(500).json(error.message);
    }
  }

  async getOwnerDashboard(req, res) {
    const ownerId = req.params.id;
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;
    
    try {
      const dashboardData = await this.businessOwnerService.getOwnerDashboardData(
        ownerId,
        startDate,
        endDate
      );
      res.status(200).json(dashboardData);
    } catch (error) {
      console.error('Error in getOwnerDashboard:', error);
      res.status(500).json({ error: 'Failed to fetch dashboard data' });
    }
  }
}
module.exports = BusinessOwnerController;
