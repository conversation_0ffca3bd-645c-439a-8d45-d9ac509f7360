class ParentController {
  constructor(parentService) {
    this.parentService = parentService;
  }

  // Save a center
  async saveCenter(req, res) {
    try {
      const { parentId, centerId } = req.body;

      // Validate input
      if (!parentId || !centerId) {
        return res.status(400).json({
          success: false,
          message: "Parent ID and Center ID are required",
        });
      }

      // Save center
      await this.parentService.saveCenter(parentId, centerId);

      res.json({
        success: true,
        message: "Center saved successfully",
      });
    } catch (error) {
      if (error.message === "Center already saved") {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
      console.error("Error saving center:", error);
      res.status(500).json({
        success: false,
        message: "Failed to save center",
        error: error.message,
      });
    }
  }

  // Unsave a center
  async unsaveCenter(req, res) {
    try {
      const { parentId, centerId } = req.body;

      // Validate input
      if (!parentId || !centerId) {
        return res.status(400).json({
          success: false,
          message: "Parent ID and Center ID are required",
        });
      }

      // Unsave center (now returns detailed result)
      const result = await this.parentService.unsaveCenter(parentId, centerId);

      res.json({
        success: true,
        message: result.wasFound 
          ? "Center removed from saved list" 
          : "Center was not in saved list (already removed)",
        wasFound: result.wasFound,
      });
    } catch (error) {
      console.error("Error unsaving center:", error);
      res.status(500).json({
        success: false,
        message: "Failed to remove center from saved list",
        error: error.message,
      });
    }
  }

  // Get saved centers
  async getSavedCenters(req, res) {
    try {
      const { parentId } = req.params;

      // Validate input
      if (!parentId) {
        return res.status(400).json({
          success: false,
          message: "Parent ID is required",
        });
      }

      // Get saved centers
      const centers = await this.parentService.getSavedCenters(parentId);

      res.json({
        success: true,
        data: centers,
      });
    } catch (error) {
      console.error("Error getting saved centers:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get saved centers",
        error: error.message,
      });
    }
  }

  // Check if center is saved (REDIRECTS TO BATCH ENDPOINT)
  async isCenterSaved(req, res) {
    try {
      const { parentId, centerId } = req.params;

      // Validate input
      if (!parentId || !centerId) {
        return res.status(400).json({
          success: false,
          message: "Parent ID and Center ID are required",
        });
      }

      // FORCE USE OF BATCH ENDPOINT - Call batch method with single center
      const results = await this.parentService.batchCheckSavedCenters(parentId, [centerId]);
      const isSaved = results[centerId] || false;

      res.json({
        success: true,
        isSaved,
      });
    } catch (error) {
      console.error("Error checking if center is saved:", error);
      res.status(500).json({
        success: false,
        message: "Failed to check if center is saved",
        error: error.message,
      });
    }
  }

  // BATCH CHECK: Check multiple centers at once (NEW EFFICIENT ENDPOINT)
  async batchCheckSavedCenters(req, res) {
    try {
      const { parentId } = req.params;
      const { centerIds } = req.body; // Array of center IDs

      // Validate input
      if (!parentId || !centerIds || !Array.isArray(centerIds)) {
        return res.status(400).json({
          success: false,
          message: "Parent ID and array of Center IDs are required",
        });
      }

      // Limit batch size to prevent abuse
      if (centerIds.length > 50) {
        return res.status(400).json({
          success: false,
          message: "Maximum 50 centers can be checked at once",
        });
      }

      // Batch check all centers
      const results = await this.parentService.batchCheckSavedCenters(parentId, centerIds);

      res.json({
        success: true,
        data: results, // Returns object like { "centerId1": true, "centerId2": false, ... }
      });
    } catch (error) {
      console.error("Error batch checking saved centers:", error);
      res.status(500).json({
        success: false,
        message: "Failed to batch check saved centers",
        error: error.message,
      });
    }
  }

  async contactUs(req, res) {
    try {
      console.log("Contact Us Request Body:", req.body);
      const { email, message, subject } = req.body;

      // Validate input
      if (!email || !message) {
        return res.status(400).json({
          success: false,
          message: "Email and message are required",
        });
      }

      // Send email
      const response = await this.parentService.contactUs(
        email,
        subject,
        message
        
      );

      res.json({
        success: true,
        message: response.message,
      });
    } catch (error) {
      console.error("Error sending contact us email:", error);
      res.status(500).json({
        success: false,
        message: "Failed to send contact us email",
        error: error.message,
      });
    }
  }
}

module.exports = ParentController;
