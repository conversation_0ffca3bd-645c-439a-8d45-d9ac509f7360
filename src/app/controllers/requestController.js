class RequestController {
  constructor({ requestService }) {
    this.requestService = requestService;
  }

  async sendJoinRequest(req, res) {
    console.log("hi");
    const data = req.body;
    console.log(data);
    try {
      const result = await this.requestService.sendJoinRequest(
        data.coachId,
        data.centerId
      );
      
      if (result.requestExists) {
        // Request already exists - return 409 Conflict with details
        res.status(409).json({
          error: result.message,
          requestExists: true,
          status: result.status,
          existingRequest: result.existingRequest
        });
      } else {
        // New request created successfully
        res.status(201).json({
          success: true,
          message: result.message,
          request: result.request,
          status: result.status
        });
      }
    } catch (err) {
      res.status(400).json({ error: err.message });
    }
  }

  async updateRequestStatus(req, res) {
    console.log(req.body);
    const { requestId } = req.params;
    const { status } = req.body;
    try {
      const updated = await this.requestService.updateRequestStatus(
        requestId,
        status
      );
      res.json(updated);
    } catch (err) {
      res.status(400).json({ error: err.message });
    }
  }

  async getRequestsByCenter(req, res) {
    try {
      const requests = await this.requestService.getRequestsByCenter(
        req.params.centerId
      );
      res.json(requests);
    } catch (err) {
      res.status(400).json({ error: err.message });
    }
  }

  async getRequestsByCoach(req, res) {
    try {
      const requests = await this.requestService.getRequestsByCoach(
        req.params.coachId
      );
      res.json(requests);
    } catch (err) {
      res.status(400).json({ error: err.message });
    }
  }

  async checkExistingRequest(req, res) {
    try {
      const { coachId, centerId } = req.params;
      const result = await this.requestService.checkExistingRequest(coachId, centerId);
      
      res.status(200).json({
        success: true,
        exists: result.exists,
        request: result.request,
        status: result.status
      });
    } catch (err) {
      res.status(500).json({ success: false, message: err.message });
    }
  }

  async cancelJoinRequestByCoachAndCenter(req, res) {
    try {
      const { coachId, centerId } = req.params;
      const result = await this.requestService.cancelJoinRequestByCoachAndCenter(coachId, centerId);
      if (result) {
        res.status(200).json({ success: true, message: "Request cancelled" });
      } else {
        res.status(404).json({ success: false, message: "Request not found" });
      }
    } catch (err) {
      res.status(500).json({ success: false, message: err.message });
    }
  }
}

module.exports = RequestController;
