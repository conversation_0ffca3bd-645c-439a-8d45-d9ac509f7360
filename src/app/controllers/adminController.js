const EventRepository = require("../repo/eventRepo");
const ClassRepository = require("../repo/classRepo");
const NotificationRepository = require("../repo/notificationRepo");
const RefundService = require("../services/refundService");
const RefundModel = require("../models/refundModel");

class AdminController {
  constructor() {
    this.eventRepository = new EventRepository();
    this.classRepository = new ClassRepository();
    this.notificationRepository = new NotificationRepository();
    //   this.refundService = new RefundService();
  }

  /**
   * Get dashboard statistics
   */
  async getDashboardStats(req, res) {
    try {
      const stats = {
        pendingRearrangements: await this.eventRepository.count({
          status: "pending_rearrangement",
        }),
        cancelledClasses: await this.eventRepository.count({
          status: "cancelled",
        }),
        pendingRefunds: await this.eventRepository.count({
          "refundInfo.status": "pending",
        }),
        totalRefundsToday: await this.getTotalRefundsToday(),
        activeClasses: await this.classRepository.count({
          status: "active",
        }),
      };

      res.status(200).json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error("Error getting dashboard stats:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get dashboard statistics",
        error: error.message,
      });
    }
  }

  /**
   * Get all pending rearrangements
   */
  async getPendingRearrangements(req, res) {
    try {
      const { page = 1, limit = 10 } = req.query;
      const skip = (page - 1) * limit;

      const pendingRearrangements =
        await this.eventRepository.findWithPagination(
          { status: "pending_rearrangement" },
          {
            populate: [
              {
                path: "classId",
                select: "classProviding category student",
              },
              { path: "centerId", select: "name email" },
              { path: "coachId", select: "firstName lastName email" },
            ],
          },
          skip,
          parseInt(limit)
        );

      const total = await this.eventRepository.count({
        status: "pending_rearrangement",
      });

      res.status(200).json({
        success: true,
        data: {
          rearrangements: pendingRearrangements,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit),
          },
        },
      });
    } catch (error) {
      console.error("Error getting pending rearrangements:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get pending rearrangements",
        error: error.message,
      });
    }
  }

  /**
   * Process a rearrangement
   */
  async processRearrangement(req, res) {
    try {
      const { eventId } = req.params;
      const { newDate, newStartTime, newEndTime, reason } = req.body;

      // Find the event
      const event = await this.eventRepository.findById(eventId);
      if (!event) {
        return res.status(404).json({
          success: false,
          message: "Event not found",
        });
      }

      // Update event with new schedule
      const updatedEvent = await this.eventRepository.update(eventId, {
        date: new Date(newDate),
        status: "rearranged",
        "rearrangementInfo.newDate": new Date(newDate),
        "rearrangementInfo.reason": reason,
        "rearrangementInfo.processedAt": new Date(),
        "rearrangementInfo.status": "completed",
      });

      // Update class status
      await this.classRepository.update(event.classId, {
        "rearrangementInfo.completedAt": new Date(),
        "rearrangementInfo.newDates": [new Date(newDate)],
      });

      // Send notification to students
      const classInfo = await this.classRepository.findById(event.classId);
      if (classInfo && classInfo.student && classInfo.student.length > 0) {
        for (const studentId of classInfo.student) {
          await this.notificationRepository.saveNotification({
            userId: studentId,
            title: "Class Rescheduled",
            body: `Your class "${classInfo.classProviding}" has been rescheduled to ${newDate} at ${newStartTime}-${newEndTime}. ${reason}`,
            data: {
              type: "rearrangement_completed",
              classId: classInfo._id,
              eventId: eventId,
              newDate,
              newStartTime,
              newEndTime,
              reason,
            },
          });
        }
      }

      res.status(200).json({
        success: true,
        message: "Rearrangement processed successfully",
        data: {
          event: updatedEvent,
          studentsNotified: classInfo?.student?.length || 0,
        },
      });
    } catch (error) {
      console.error("Error processing rearrangement:", error);
      res.status(500).json({
        success: false,
        message: "Failed to process rearrangement",
        error: error.message,
      });
    }
  }

  /**
   * Get refund history
   */
  async getRefundHistory(req, res) {
    try {
      const { page = 1, limit = 10, status, dateFrom, dateTo } = req.query;
      const skip = (page - 1) * limit;

      let filter = { "refundInfo.refundIds": { $exists: true, $ne: [] } };

      if (status) {
        filter["refundInfo.status"] = status;
      }

      if (dateFrom || dateTo) {
        filter["refundInfo.processedAt"] = {};
        if (dateFrom)
          filter["refundInfo.processedAt"].$gte = new Date(dateFrom);
        if (dateTo) filter["refundInfo.processedAt"].$lte = new Date(dateTo);
      }

      const refunds = await this.eventRepository.findWithPagination(
        filter,
        {
          populate: [
            {
              path: "classId",
              select: "classProviding category student",
            },
            { path: "centerId", select: "name email" },
          ],
          sort: { "refundInfo.processedAt": -1 },
        },
        skip,
        parseInt(limit)
      );

      const total = await this.eventRepository.count(filter);

      res.status(200).json({
        success: true,
        data: {
          refunds,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit),
          },
        },
      });
    } catch (error) {
      console.error("Error getting refund history:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get refund history",
        error: error.message,
      });
    }
  }

  /**
   * Retry failed refund
   */
  async retryRefund(req, res) {
    try {
      const { eventId } = req.params;
      const { studentId, amount, originalPaymentId } = req.body;

      const refundResult = await this.refundService.processRefund(
        studentId,
        amount,
        originalPaymentId,
        "Manual retry by admin"
      );

      if (refundResult.success) {
        // Update event with new refund info
        await this.eventRepository.update(eventId, {
          $push: {
            "refundInfo.refundIds": refundResult.refundId,
          },
          "refundInfo.status": "completed",
          "refundInfo.processedAt": new Date(),
        });

        // Send success notification
        await this.notificationRepository.saveNotification({
          userId: studentId,
          title: "Refund Processed",
          body: `Your refund of $${amount} has been successfully processed.`,
          data: {
            type: "refund_success",
            refundId: refundResult.refundId,
            amount: amount,
          },
        });
      }

      res.status(200).json({
        success: true,
        message: refundResult.success
          ? "Refund processed successfully"
          : "Refund failed",
        data: refundResult,
      });
    } catch (error) {
      console.error("Error retrying refund:", error);
      res.status(500).json({
        success: false,
        message: "Failed to retry refund",
        error: error.message,
      });
    }
  }

  /**
   * Cancel a pending rearrangement (convert to refund)
   */
  async cancelRearrangement(req, res) {
    try {
      const { eventId } = req.params;
      const { reason = "Rearrangement cancelled by admin" } = req.body;

      const event = await this.eventRepository.findById(eventId);
      if (!event) {
        return res.status(404).json({
          success: false,
          message: "Event not found",
        });
      }

      const classInfo = await this.classRepository.findById(event.classId);
      if (!classInfo) {
        return res.status(404).json({
          success: false,
          message: "Class not found",
        });
      }

      // Process refunds for all students
      const refundResults = await this.refundService.processBulkRefunds(
        classInfo.student,
        classInfo.charge,
        reason
      );

      // Update event status
      await this.eventRepository.update(eventId, {
        status: "cancelled",
        cancellationType: "refund",
        cancelledAt: new Date(),
        "refundInfo.refundIds": refundResults
          .map((r) => r.refundId)
          .filter(Boolean),
        "refundInfo.totalRefunded": refundResults.reduce(
          (sum, r) => sum + (r.amount || 0),
          0
        ),
        "refundInfo.status": "completed",
        "refundInfo.processedAt": new Date(),
      });

      // Send notifications
      for (const studentId of classInfo.student) {
        await this.notificationRepository.saveNotification({
          userId: studentId,
          title: "Class Cancelled - Refund Processed",
          body: `Your class "${classInfo.classProviding}" rearrangement has been cancelled. A refund of $${classInfo.charge} has been processed.`,
          data: {
            type: "rearrangement_cancelled",
            classId: classInfo._id,
            refundAmount: classInfo.charge,
            reason,
          },
        });
      }

      res.status(200).json({
        success: true,
        message: "Rearrangement cancelled and refunds processed",
        data: {
          refundResults,
          studentsRefunded: refundResults.filter((r) => r.success).length,
        },
      });
    } catch (error) {
      console.error("Error cancelling rearrangement:", error);
      res.status(500).json({
        success: false,
        message: "Failed to cancel rearrangement",
        error: error.message,
      });
    }
  }

  /**
   * Helper method to get total refunds for today
   */
  async getTotalRefundsToday() {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const events = await this.eventRepository.find({
        "refundInfo.processedAt": {
          $gte: today,
          $lt: tomorrow,
        },
      });

      return events.reduce((total, event) => {
        return total + (event.refundInfo?.totalRefunded || 0);
      }, 0);
    } catch (error) {
      console.error("Error calculating total refunds:", error);
      return 0;
    }
  }

  async fixRefundsMissingParentId(req, res) {
    try {
    
      const container = require("../../container/container");
      const refundRepo = container.refundRepo;
      const childRepo = container.childRepo;
      // 1. Find all refunds with missing parentId
      const refunds = await require("../models/refundModel").find(
      //   {
      //   $or: [{ parentId: { $exists: false } }, { parentId: null }],
      // }
    );
      let updated = 0;
      for (const refund of refunds) {
        console.log(refund)
        // 2. Get parent from studentId
        const parentId = await childRepo.getParentByChildId(refund.studentId);
        if (parentId) {
          // 3. Update refund record
          refund.parentId = parentId;

          // If no date, set date to createdAt

          await refund.save();
          updated++;
        }
        console.log(refund.date);
        if (!refund.date) {
          refund.date = refund.createdAt;
          await refund.save();
          updated++;
        }
      }
      res.status(200).json({ success: true, updated, total: refunds.length });
    } catch (error) {
      console.error("Error fixing refunds missing parentId:", error);
      res.status(500).json({ success: false, message: error.message });
    }
  }
}

module.exports = AdminController;
