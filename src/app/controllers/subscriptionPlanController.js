class SubscriptionPlanController {
  constructor(
    subscriptionPlanUseCase,
    createSubscriptionUseCase,
    getSubscriptionUseCase
  ) {
    this.subscriptionPlanUseCase = subscriptionPlanUseCase;
    this.createSubscriptionUseCase = createSubscriptionUseCase;
    this.getSubscriptionUseCase = getSubscriptionUseCase;
  }

  async getAllPlans(req, res) {
    try {
      const userId = req.query.userId
      const currentPlan = await this.getSubscriptionUseCase.execute(userId);
      console.log(currentPlan);
      const plans = await this.subscriptionPlanUseCase.getAllPlans();
      res.status(200).json({
        current: currentPlan || null,
        plans,
      });
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
  async createNewPlan(req, res) {
    const subscriptionPlan = req.body;
    try {
      const plans = await this.subscriptionPlanUseCase.createNewPlan(
        subscriptionPlan
      );
      res.status(200).json(plans);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
  async deletePlan(req, res) {
    const { planId } = req.params;
    try {
      const plan = await this.subscriptionPlanUseCase.deletePlan(planId);
      res.status(200).json(true);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
  async updatePlan(req, res) {
    const subscriptionPlan = req.body;
    const { planId } = req.params;
    try {
      const plans = await this.subscriptionPlanUseCase.updatePlan(
        planId,
        subscriptionPlan
      );
      res.status(200).json(plans);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
  async createSubscription(req, res) {
    try {
      const data = req.body;
      const plan = await this.createSubscriptionUseCase.execute(data);
      res.status(200).json(plan);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
  async getSubscription(req, res) {
    const { userId } = req.params;
    try {
      const plan = await this.getSubscriptionUseCase.execute(userId);
      res.status(200).json(plan);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
}

module.exports = SubscriptionPlanController;
