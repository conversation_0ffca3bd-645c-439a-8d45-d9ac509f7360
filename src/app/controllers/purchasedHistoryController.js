class PurchasedHistoryController {
  constructor(purchasedHistoryuseCase) {
    this.purchasedHistoryuseCase = purchasedHistoryuseCase;
  }
  async create() {}
  async get(req, res) {
    try {
      const {userId} = req.params;
      const history =await this.purchasedHistoryuseCase.get(userId);
      console.log(history);
      res.status(200).json(history);
    } catch (error) {
      throw error("Filed to Find purchased History: ", error.message);
    }
  }
}

module.exports = PurchasedHistoryController;
