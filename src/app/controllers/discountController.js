class DiscountController {
  constructor(discountService) {
    this.discountService = discountService;
  }

  async create(req, res) {
    try {
      const discount = await this.discountService.createDiscount(req.body);
      res.status(201).json(discount);
    } catch (err) {
      res.status(400).json({ error: err.message });
    }
  }

  async getAll(req, res) {
    try {
      const discounts = await this.discountService.getAllDiscounts();
      res.status(200).json(discounts);
    } catch (err) {
      res.status(400).json({ error: err.message });
    }
  }
  async getById(req, res) {
    try {console.log("HI")
      const { userId } = req.params;
      const discounts = await this.discountService.getById(userId);
      res.status(200).json(discounts);
    } catch (error) {
      res.status(400).json({ error: err.message });
    }
  }
  async getByCode(req, res) {
    try {
      const { code } = req.params;
      const discount = await this.discountService.getDiscountByCode(code);
      res.status(200).json(discount);
    } catch (err) {
      res.status(400).json({ error: err.message });
    }
  }

  async delete(req, res) {
    try {
      const { code } = req.params;
      const result = await this.discountService.deleteDiscount(code);
      res.status(200).json(result);
    } catch (err) {
      res.status(400).json({ error: err.message });
    }
  }
}

module.exports = DiscountController;
