class CenterController {
  constructor({
    centerUseCase,
    sendNotificationToAssignCoach,
    sendNotificationToRemoveCoach,
  }) {
    this.centerUseCase = centerUseCase;
    this.sendNotificationToAssignCoach = sendNotificationToAssignCoach;
    this.sendNotificationToRemoveCoach = sendNotificationToRemoveCoach;
  }

  async createCenter(req, res) {
    try {
      const centerData = this._processFiles(req);
      const result = await this.centerUseCase.create(centerData);
      res.status(201).json(result);
    } catch (error) {
      this._handleError(res, error, "Error creating center");
    }
  }

  async getAllCenters(req, res) {
    try {
      // Check if user is a parent - if so, only show promoted centers
      const userType = req.user?.type;
      const promotionOnly = userType === 'parent';
      
      console.log(`getAllCenters called by user type: ${userType}, promotionOnly: ${promotionOnly}`);
      
      const centers = await this.centerUseCase.getAllCenters(promotionOnly);
      
      console.log(`Returning ${centers.length} centers to ${userType} user`);
      if (promotionOnly) {
        console.log('Centers returned (promotion=true only):', centers.map(c => ({ id: c._id, displayName: c.displayName, promotion: c.promotion })));
      }
      
      res.status(200).json(centers);
    } catch (error) {
      this._handleError(res, error, "Error fetching all centers");
    }
  }

  async getCenterById(req, res) {
    try {
      const centerId = req.params.id;
      const center = await this.centerUseCase.getCenterById(centerId);
      res.status(200).json(center);
    } catch (error) {
      this._handleError(res, error, "Error getting center by ID");
    }
  }

  async getCoachesByCenterId(req, res) {
    try {
      const centerId = req.params.id;
      console.log(centerId);
      const coaches = await this.centerUseCase.getCoachsByCenterId(centerId);
      res.status(200).json(coaches);
    } catch (error) {
      this._handleError(res, error, "Error fetching coaches");
    }
  }
  async getManagersByCenterId(req, res) {
    try {
      const centerId = req.params.id;
      console.log(centerId);
      const coaches = await this.centerUseCase.getManagersByCenterId(centerId);
      res.status(200).json(coaches);
    } catch (error) {
      this._handleError(res, error, "Error fetching coaches");
    }
  }

  async updateCenter(req, res) {
    try {
      const centerId = req.params.id;
      const centerData = this._processFiles(req);
      const updatedCenter = await this.centerUseCase.update(
        centerId,
        centerData
      );
      res.status(200).json(updatedCenter);
    } catch (error) {
      this._handleError(res, error, "Error updating center");
    }
  }

  async deleteCenter(req, res) {
    try {
      const centerId = req.params.id;
      await this.centerUseCase.deleteCenterById(centerId);
      res.status(204).send();
    } catch (error) {
      this._handleError(res, error, "Error deleting center");
    }
  }

  async assignCoach(req, res) {
    try {
      const data = req.body;
      console.log(data);
      const assignData = await this.sendNotificationToAssignCoach.execute(
        data.centerId,
        data.coachId,
        data.status
      );
      res.status(200).json(assignData);
    } catch (error) {
      this._handleError(res, error, "Error assigning coach");
    }
  }
  async removeCoach(req, res) {
    try {
      console.log("hi");
      const data = req.body;

      const assignData = await this.sendNotificationToRemoveCoach.execute(
        data.centerId,
        data.coachId,
        data.status
      );
      res.status(200).json(assignData);
    } catch (error) {
      this._handleError(res, error, "Error assigning coach");
    }
  }
  async getRequestByType(req, res) {
    try {
      console.log('req')
      const { type, id } = req.params;
      const request = await this.centerUseCase.getRequestByType(type, id);
res.status(200).json(request);
    } catch (error) {
      this._handleError(res, error, "Error fetching request by type");
    }
  }
  // New method for center verification
  async verifyCenter(req, res) {
    try {
      const centerId = req.params.id;
      const { verified } = req.body;

      if (verified === undefined) {
        return res.status(400).json({
          success: false,
          message: "Verification status is required",
        });
      }

      const updatedCenter = await this.centerUseCase.verifyCenterById(
        centerId,
        verified
      );

      res.status(200).json({
        success: true,
        message: verified
          ? "Center has been approved successfully"
          : "Center approval has been revoked",
        data: updatedCenter,
      });
    } catch (error) {
      this._handleError(
        res,
        error,
        "Error updating center verification status"
      );
    }
  }

  _processFiles(req) {
    const centerData = req.body;

    if (req.files) {
      if (req.files.hkidCard) {
        console.log(
          "HKID Card file paths:",
          req.files.hkidCard.map((file) => file.path)
        );
        centerData.hkidCard = req.files.hkidCard.map((file) => ({
          path: file.path, // Preserve path explicitly
          url: file.path || file.url,
          contentType: file.mimetype,
          originalname: file.originalname,
        }));
      }
      if (req.files.businessCertificate) {
        console.log(
          "Business Certificate file paths:",
          req.files.businessCertificate.map((file) => file.path)
        );
        centerData.businessCertificate = req.files.businessCertificate.map(
          (file) => ({
            path: file.path, // Preserve path explicitly
            url: file.path || file.url,
            contentType: file.mimetype,
            originalname: file.originalname,
          })
        );
      }
      if (req.files.sexualConvictionRecord) {
        console.log(
          "Sexual Conviction Record file paths:",
          req.files.sexualConvictionRecord.map((file) => file.path)
        );
        centerData.sexualConvictionRecord = req.files.sexualConvictionRecord.map(
          (file) => ({
            path: file.path, // Preserve path explicitly
            url: file.path || file.url,
            contentType: file.mimetype,
            originalname: file.originalname,
          })
        );
      }
      if (req.files.mainImage) {
        console.log("Main Image file path:", req.files.mainImage[0].path);
        centerData.mainImage = {
          path: req.files.mainImage[0].path, // Preserve path explicitly
          url: req.files.mainImage[0].path || req.files.mainImage[0].url,
          contentType: req.files.mainImage[0].mimetype,
          originalname: req.files.mainImage[0].originalname,
        };
      }
      if (req.files.images) {
        console.log(
          "Images file paths:",
          req.files.images.map((file) => file.path)
        );
        centerData.images = req.files.images.map((file) => ({
          path: file.path, // Preserve path explicitly
          url: file.path || file.url,
          contentType: file.mimetype,
          originalname: file.originalname,
        }));
      }
    }

    return centerData;
  }

  _handleError(res, error, message) {
    console.error(`${message}:`, error.message);
    res.status(500).json({ error: error.message });
  }
}

module.exports = CenterController;
