const path = require("path");
class AuthController {
  constructor({
    signUpUser,
    signInUser,
    updateUser,
    updateCenter,
    validateToken,
    tokenService,
    authMiddleware,
    fileUploadService,
    centerUseCase,
  }) {
    // console.log("Constructor Params:", {
    //   signUpUser,
    //   signInUser,
    //   updateUser,
    //   updateCenter,
    //   validateToken,
    //   tokenService,
    //   authMiddleware,
    //   fileUploadService,
    //   centerUseCase,
    // });
    this.signUpUser = signUpUser;
    this.signInUser = signInUser;
    this.updateUser = updateUser;
    this.updateCenter = updateCenter;
    this.validateToken = validateToken;
    this.tokenService = tokenService;
    this.authMiddleware = authMiddleware;
    this.fileUploadService = fileUploadService;
    this.centerUseCase = centerUseCase;
  }

  async signUp(req, res) {
    const { email, password, type, otp } = req.body;
    try {
      console.log(req.body);
      const user = await this.signUpUser.execute(email, password, type, otp);
      console.log("done");
      res.status(200).json(user);
    } catch (error) {
      console.log(error.message);
      res.status(400).json({ message: error.message });
    }
  }

  async signIn(req, res) {
    const { email, password, type } = req.body;
    console.log(req.body);
    try {
      const user = await this.signInUser.execute(email, password, type);
      res.status(200).json(user);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
  async newPassword(req, res) {
    const { email, password } = req.body;
    try {
      const user = await this.updateUser.newPassword(email, password);
      res.status(200).json(user);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
  async update(req, res) {
    try {
      // Extract user ID and body type
      const id = req.params.id;
      console.log("Starting profile update for user ID:", id);
      console.log("Request body:", JSON.stringify(req.body, null, 2));
      console.log(
        "Request files:",
        req.files ? Object.keys(req.files) : "None"
      );

      if (!id) {
        return res.status(400).json({ message: "Missing user ID" });
      }

      // Process the files in the request
      const data = this._processFiles(req);
      console.log("Processed data:", JSON.stringify(data, null, 2));

      // Execute the update with the processed data
      const updatedData = await this.updateUser.execute(id, data);
      console.log("Update successful, returning data");

      // Send success response
      res.status(200).json(updatedData);
    } catch (error) {
      console.error("Update error:", error);
      res.status(500).json({ message: error.message });
    }
  }
  async deleteParentAddress(req, res) {
    try {
      const id = req.params.id;
      const addressId = req.params.addressId;
      if (!id || !addressId) {
        return res
          .status(400)
          .json({ message: "Missing user ID or address ID" });
      }
      const updatedData = await this.updateUser.deleteAddress(id, addressId);
      res.status(200).json(updatedData);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }
  async defaultParentAddress(req, res) {
    try {
      const id = req.params.id;
      const addressId = req.params.addressId;
      if (!id || !addressId) {
        return res
          .status(400)
          .json({ message: "Missing user ID or address ID" });
      }
      const updatedData = await this.updateUser.changeDefaultAddress(
        id,
        addressId
      );
      res.status(200).json(updatedData);
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }
  async isTokenValid(req, res) {
    const token = req.header("auth-token");
    try {
      const isValid = await this.validateToken.execute(token);
      res.json(isValid);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getUser(req, res) {
    try {
      const user = await this.signInUser.userRepository.findById(req.user.id);
      res.json(user);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getAllCenters(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const skip = (page - 1) * limit;

      // Check if user is authenticated and get user type
      let userType = null;
      let promotionOnly = false;
      
      const token = req.header("auth-token");
      if (token) {
        try {
          // Validate the token and get user info
          const decodedToken = this.tokenService.validate(token);
          const userId = decodedToken.id;
          
          // Check if user is a parent by checking userRepository (parents are stored there)
          // Since userRepository doesn't have findOne, we'll check if the user exists as a parent
          // by trying to find them in the User collection with baseUser field
          let parentUser = null;
          try {
            // Use the same approach as in validateToken.js - check if user exists as parent
            parentUser = await this.signInUser.roleService.repositories.parent.findByBaseUserId(userId);
          } catch (error) {
            // If findByBaseUserId fails, the user is not a parent, which is fine
            console.log('Error checking parent user:', error.message);
            parentUser = null;
          }
          
          if (parentUser) {
            userType = 'parent';
            promotionOnly = true;
            console.log('Parent user detected - applying promotion filter');
          } else {
            userType = 'other'; // coach, center, owner etc.
            console.log(`Non-parent user detected, showing all centers`);
          }
          
          console.log(`getAllCenters (auth) called by user type: ${userType}`);
        } catch (tokenError) {
          console.log('Invalid or expired token, showing all centers:', tokenError.message);
          // Don't throw error, just proceed without filtering
        }
      } else {
        console.log('No auth token provided, showing all centers');
      }

      const [centers, total] = await Promise.all([
        this.updateCenter.getAllCenter(skip, limit, promotionOnly), // Pass promotionOnly parameter
        this.updateCenter.countCenters(promotionOnly), // Pass promotionOnly parameter for accurate count
      ]);

      console.log(`Returning ${centers.length} centers to ${userType || 'anonymous'} user`);
      if (promotionOnly) {
        console.log('Centers returned (promotion=true only):', centers.map(c => ({ id: c._id, displayName: c.displayName, promotion: c.promotion })));
      }

      res.json({
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        data: centers,
      });
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }

  async getCenterById(req, res) {
    try {
      const { id } = req.params;
      const center = await this.centerUseCase.getCenterById(id);
      res.json(center);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
  _processFiles(req) {
    try {
      const userData = { ...req.body };
      console.log(
        "ProcessFiles - Request body:",
        JSON.stringify(req.body, null, 2)
      );

      // Process location data if present
      if (userData.location) {
        // Handle location array or object
        const locations = Array.isArray(userData.location)
          ? userData.location
          : [userData.location];

        // Process each location object
        userData.location = locations.map((location) => {
          if (typeof location === "string") {
            try {
              // Try to parse if it's a JSON string
              return JSON.parse(location);
            } catch (e) {
              console.error("Failed to parse location string:", e);
              return {
                flatFloorBlock: location,
                buildingEstate: location,
                district: location,
                region: location,
              };
            }
          }
          return location;
        });

        // Further process location fields from form data format
        if (req.body["location[0][flatFloorBlock]"] !== undefined) {
          console.log("Processing location from form data format");
          const formLocation = {
            flatFloorBlock: req.body["location[0][flatFloorBlock]"] || "",
            buildingEstate: req.body["location[0][buildingEstate]"] || "",
            district: req.body["location[0][district]"] || "",
            region: req.body["location[0][region]"] || "",
            country: req.body["location[0][country]"] || "Hong Kong",
            default: req.body["location[0][default]"] === "true" || false,
          };

          // Don't accept "Not specified" literally in form fields
          Object.keys(formLocation).forEach((key) => {
            if (formLocation[key] === "Not specified") {
              console.log(
                `Converting 'Not specified' in ${key} to empty string`
              );
              formLocation[key] = ""; // Convert "Not specified" to empty string
            }
          });

          userData.location = [formLocation];
        }
      }

      if (req.files) {
        console.log("ProcessFiles - Files in request:", Object.keys(req.files));

        // Process mainImage file
        if (req.files.mainImage && req.files.mainImage.length > 0) {
          console.log(
            "ProcessFiles - Main Image found:",
            req.files.mainImage[0].originalname
          );
          const mainImageFile = req.files.mainImage[0];
          userData.mainImage = {
            path: mainImageFile.path,
            buffer: mainImageFile.buffer,
            url: mainImageFile.path,
            contentType: mainImageFile.mimetype,
            originalname: mainImageFile.originalname,
          };
          console.log(
            "ProcessFiles - Processed mainImage:",
            JSON.stringify(userData.mainImage, null, 2)
          );
        }

        // Process other file types if needed...
        // Additional file processing can be added here
      }
      // Handle Flutter-specific case where file data might be in the request body
      else if (req.body.mainImage) {
        console.log("ProcessFiles - mainImage data found in request body");

        // Handle different formats the Flutter app might send
        if (typeof req.body.mainImage === "string") {
          // If mainImage is a string, it's likely a file path or base64 data
          if (req.body.mainImage.startsWith("data:")) {
            // It's base64 data
            console.log("ProcessFiles - mainImage is base64 data");
            userData.mainImage = {
              data: req.body.mainImage,
              contentType: req.body.mainImage.split(";")[0].split(":")[1],
              originalname: "image.jpg",
            };
          } else {
            // It's a file path or URL
            console.log("ProcessFiles - mainImage is a path or URL");
            userData.mainImage = {
              url: req.body.mainImage,
              contentType: "image/jpeg",
              originalname: path.basename(req.body.mainImage) || "image.jpg",
            };
          }
        } else if (typeof req.body.mainImage === "object") {
          // If it's an object, keep it as is but ensure it has minimal required properties
          console.log("ProcessFiles - mainImage is an object");
          userData.mainImage = {
            ...req.body.mainImage,
            originalname: req.body.mainImage.originalname || "image.jpg",
            contentType: req.body.mainImage.contentType || "image/jpeg",
          };
        }

        console.log(
          "ProcessFiles - Processed mainImage from body:",
          JSON.stringify(userData.mainImage, null, 2)
        );
      }

      return userData;
    } catch (error) {
      console.error("Error in _processFiles:", error);
      // Don't throw here, just return what we have and let the next stage handle it
      return req.body;
    }
  }
}

module.exports = AuthController;
