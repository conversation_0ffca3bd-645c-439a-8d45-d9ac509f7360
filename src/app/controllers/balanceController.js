class BalanceController {
  constructor(balanceUseCase) {
    this.balanceUseCase = balanceUseCase;
  }
  async createBalance(req, res) {
    try {
      const data = req.body;
      const balance = await this.balanceUseCase.createBalance(data);
      res.status(200).json(balance);
    } catch (error) {
      res.status(500).json(error.message);
    }
  }
  async getBalance(req, res) {
    try {
      const { userId } = req.params;
      const balance = await this.balanceUseCase.getBalance(userId);
      res.status(200).json(balance);
    } catch (error) {
      res.status(500).json(error.message);
    }
  }
  async updateBalance(req, res) {
    try {
      const { userId } = req.params;
      const data = req.body;
      const balance = await this.balanceUseCase.updateBalance(userId, data);
      res.status(200).json(balance);
    } catch (error) {
      res.status(500).json(error.message);
    }
  }
  
  async addBalance(req, res) {
    try {
      const { userId } = req.params;
      const { amount } = req.body;
      
      if (!amount || isNaN(amount)) {
        return res.status(400).json({ message: "Valid amount is required" });
      }
      
      const balance = await this.balanceUseCase.addBalance(userId, amount);
      res.status(200).json(balance);
    } catch (error) {
      console.error("Error adding balance:", error);
      res.status(500).json({ message: error.message });
    }
  }
}

module.exports = BalanceController;
