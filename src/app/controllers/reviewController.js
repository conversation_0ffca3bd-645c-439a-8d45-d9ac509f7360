const { processFiles } = require("../../infrustructure/processfile");

class ReviewController {
  constructor(reviewUseCase, getMomentsUseCase) {
    this.reviewUseCase = reviewUseCase;
    this.getMomentsUseCase = getMomentsUseCase;
  }

  async createReview(req, res) {
    console.log("Creating review...");
    const data = processFiles(req); // processed body + files

    try {
      // Validate revieweeId is a valid MongoDB ObjectId
      const mongoose = require("mongoose");
      if (!data.revieweeId || data.revieweeId === "null" || data.revieweeId === "") {
        return res.status(400).json({ error: "Invalid revieweeId. Student ID is required." });
      }
      
      // Ensure revieweeId is a valid ObjectId
      if (!mongoose.Types.ObjectId.isValid(data.revieweeId)) {
        return res.status(400).json({ error: "Invalid revieweeId format. Must be a valid MongoDB ObjectId." });
      }
      
      const review = await this.reviewUseCase.createReview(data);
      res.status(201).json(review);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async postReviewByParent(req, res) {
    const data = processFiles(req);
    try {
      console.log("data", data);
      const review = await this.reviewUseCase.postReviewByParent(data);
      return res.status(200).json(review);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async getReviews(req, res) {
    const data = req.params;

    try {
      const reviews = await this.reviewUseCase.getReviews(
        data.revieweeId,
        data.revieweeType
      );
      res.status(200).json(reviews);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getReviewsByReviewer(req, res) {
    const data = req.params;

    try {
      const reviews = await this.reviewUseCase.getReviewsByReviewer(
        data.reviewerId,
        data.reviewerType
      );
      res.status(200).json(reviews);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getAverageRating(req, res) {
    const data = req.params;

    try {
      const avgRating = await this.reviewUseCase.getAverageRating(
        data.revieweeId,
        data.revieweeType
      );
      res.status(200).json({ averageRating: avgRating });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getPendingReview(req, res) {
    const data = req.params;

    try {
      const pending = await this.reviewUseCase.pendingReview(data.centerId);
      res.status(200).json(pending);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async getPendingReviewByClassId(req, res) {
    const data = req.params;
    try {
      const pendings = await this.reviewUseCase.getPendingReviewByClassId(
        data.classId
      );
      res.status(200).json(pendings);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async getPendingReviewByCoachId(req, res) {
    const data = req.params;
    // Get page and limit from query parameters, with defaults
    const limit = parseInt(req.query.limit, 10) || 10; // Default limit to 10 items
    const skip = parseInt(req.query.skip, 10) || 0;
    
    try {
      console.time("getPendingReviewByCoachId");
      const pendings = await this.reviewUseCase.getPendingReviewByCoachId(
        data.coachId,
        limit,
        skip
      );
      console.timeEnd("getPendingReviewByCoachId");
      res.status(200).json(pendings);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getReviewByClassAndId(req, res) {
    const data = req.params;

    try {
      const review = await this.reviewUseCase.getReviewByClassAndId(
        data.classId,
        data.revieweeId,
        data.revieweeType,
        data.date
      );
      res.status(200).json(review);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getMoments(req, res) {
    const data = req.query;
    console.log(data);
    try {
      const moments = await this.getMomentsUseCase.execute(data.reviewId);
      res.status(200).json(moments);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getPendingReviewsByParentId(req, res) {
    const { parentId } = req.params;
    // Get page and limit from query parameters, with defaults
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10; // Default limit to 10 items
    const skip = (page - 1) * limit;

    try {
      const result = await this.reviewUseCase.getPendingReviewsByParentId(
        parentId
      );
      res.status(200).json({
        message: "Reviews and pending reviews fetched successfully",
        allReviews: result
      });
      // res.status(200).json({
      //   message: "Reviews and pending reviews fetched successfully",
      //   allReviews: {
      //     reviews: result.reviews,
      //     pendingReviews: result.pendingReviews,
      //   },
      // });
    } catch (error) {
      console.error(
        "Error in ReviewController getPendingReviewsByParentId:",
        error
      );
      res.status(500).json({
        error: "Failed to fetch pending reviews for parent: " + error.message,
      });
    }
  }

  async getParentReviewHistory(req, res) {
    const { parentId } = req.params;
    const { childId } = req.query; // Get childId from query parameters
    try {
      const history = await this.reviewUseCase.getParentReviewHistory(
        parentId,
        childId
      );
      res.status(200).json(history);
    } catch (error) {
      console.error("Error in ReviewController getParentReviewHistory:", error);
      res.status(500).json({
        error: "Failed to fetch review history for parent: " + error.message,
      });
    }
  }
}

module.exports = ReviewController;
