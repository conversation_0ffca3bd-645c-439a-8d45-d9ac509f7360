const path = require("path");

class coachController {
  constructor({
    coachUseCase,
    assignCoachTo<PERSON>enter,
    removeCoach<PERSON><PERSON><PERSON>enter,
    removeManagerFromCenter,
    assignManagertoCenter,
    requestCenterUseCase,
    rejectRequestCoach,
  }) {
    this.coachUseCase = coachUseCase;
    this.assignCoachtoCenterUseCase = assignCoachToCenter;
    this.removeManagerFromCenter = removeManagerFromCenter;
    this.removeCoachFromCenter = removeCoachFromCenter;
    this.assignManagertoCenter = assignManagertoCenter;
    this.requestCenterUseCase = requestCenterUseCase;
    this.rejectRequestCoach = rejectRequestCoach;
  }
  async createCoach(req, res) {
    try {
      console.log("controller");
      const coachData = this._processFiles(req);

      const result = await this.coachUseCase.create(coachData);
      res.status(201).json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async updateCoach(req, res) {
    try {
      const id = req.params.id;
      if (!id) {
        return res.status(400).json({ message: "Missing user ID or type" });
      }

      const coachData = this._processFiles(req);
      console.log(coachData);
      const updatedData = await this.coachUseCase.update(id, coachData);
      res.status(200).json(updatedData);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getCoachById(req, res) {
    try {
      const { id } = req.params;
      const coach = await this.coachUseCase.getCoachByIdWithCenter(id);
      if (!coach) {
        return res.status(404).json({ message: "Coach not found" });
      }
      res.status(200).json(coach);
    } catch (error) {}
  }

  async deleteCoachById(req, res) {
    try {
      const { id } = req.params;
      const coach = await this.coachUseCase.deleteCoachById(id);
      if (!coach) {
        res.status(404).json({ message: "Coach not found" });
      }
      res.status(201).json({ message: "Coach delete" });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async getAllCoach(req, res) {
    try {
      const coachs = await this.coachUseCase.getAllCoach();
      res.json(coachs);
    } catch (error) {
      res.status(400).json({ message: error.message });
    }
  }
  async assignCoach(req, res) {
    try {
      const data = req.body;

      let assignCoach;

      if (data.type == "coach")
        assignCoach = await this.assignCoachtoCenterUseCase.execute(
          data.centerId,
          data.coachId,
          data.notificationId
        );
      else if (data.type == "manager") {
        console.log("manager");
        assignCoach = await this.assignManagertoCenter.execute(
          data.centerId,
          data.coachId,
          data.notificationId
        );
      }
      res.status(200).json(assignCoach);
    } catch (error) {
      res.status(400).json(error.message);
    }
  }
  async removeCoach(req, res) {
    try {
      const data = req.body;
      console.log(data);
      let remove;
      console.log(data.type);
      if (data.type == "coach") {
        remove = await this.removeCoachFromCenter.execute(
          data.centerId,
          data.coachId,
          data.type
        );
      } else {
        remove = await this.removeManagerFromCenter.execute(
          data.centerId,
          data.coachId,
          data.type
        );
      }
      console.log(`controller ${remove}`);
      res.status(200).json(remove);
    } catch (error) {
      res.status(400).json(error.message);
    }
  }
  async requestCenter(req, res) {
    try {
      const data = req.body;
      const requestCenter = await this.requestCenterUseCase.requestCenter(
        data.centerId,
        data.coachId
      );
      res.status(200).json(requestCenter);
    } catch (error) {
      res.status(400).json(error.message);
    }
  }
  async assignManager(req, res) {
    try {
      const data = req.body;
      const assignManager = await this.assignManagertoCenter.execute(
        data.centerId,
        data.coachId,
        data.notificationId
      );
      res.status(200).json(assignManager);
    } catch (error) {
      res.status(400).json(error.message);
    }
  }
  async getProgramByCoachId(req, res) {
    try {
      const { id } = req.params;
      const programs = await this.coachUseCase.getProgramByCoachId(id);
      res.status(200).json(programs);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  async deleteNotification(req, res) {
    try {
      const { id } = req.params;
      console.log(id);
      const deleteNotification = await this.rejectRequestCoach.execution(id);
      res.status(200).json(deleteNotification);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
  _processFiles(req) {
    const centerData = req.body;

    if (req.files) {
      if (req.files.hkidCard) {
        console.log(
          "HKID Card file paths:",
          req.files.hkidCard.map((file) => file.path)
        );
        centerData.hkidCard = req.files.hkidCard.map((file) => ({
          path: file.path, // Preserve path explicitly
          url: file.path || file.url,
          contentType: file.mimetype,
          originalname: file.originalname,
        }));
      }
      if (req.files.businessCertificate) {
        console.log(
          "Business Certificate file paths:",
          req.files.businessCertificate.map((file) => file.path)
        );
        centerData.businessCertificate = req.files.businessCertificate.map(
          (file) => ({
            path: file.path, // Preserve path explicitly
            url: file.path || file.url,
            contentType: file.mimetype,
            originalname: file.originalname,
          })
        );
      }
      if (req.files.mainImage) {
        console.log("Main Image file path:", req.files.mainImage[0].path);
        centerData.mainImage = {
          path: req.files.mainImage[0].path, // Preserve path explicitly
          url: req.files.mainImage[0].path || req.files.mainImage[0].url,
          contentType: req.files.mainImage[0].mimetype,
          originalname: req.files.mainImage[0].originalname,
        };
      }
      if (req.files.images) {
        console.log(
          "Images file paths:",
          req.files.images.map((file) => file.path)
        );
        centerData.images = req.files.images.map((file) => ({
          path: file.path, // Preserve path explicitly
          url: file.path || file.url,
          contentType: file.mimetype,
          originalname: file.originalname,
        }));
      }
    }

    return centerData;
  }
}

module.exports = coachController;
