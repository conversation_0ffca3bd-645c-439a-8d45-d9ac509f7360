const express = require("express");
const router = express.Router();

// Import models and dependencies
const Campaign = require("../models/campaignModel");
const CampaignRepository = require("../repo/campaignRepo");
const CampaignService = require("../services/campaignService");
const CampaignController = require("../controllers/campaignController");

// Import discount dependencies for campaign discount integration
const DiscountRepository = require('../repo/discountRepo');
const DiscountModel = require('../models/discountModel');
const CampaignDiscountService = require('../services/campaignDiscountService');

// Initialize repositories
const campaignRepo = new CampaignRepository(Campaign);
const discountRepo = new DiscountRepository(DiscountModel);

// Initialize services
const campaignService = new CampaignService(campaignRepo);
const campaignDiscountService = new CampaignDiscountService(campaignRepo, discountRepo);

// Initialize controller with both services
const campaignController = new CampaignController(campaignService, campaignDiscountService);

// Public routes (no authentication required)
router.get("/active", (req, res) => campaignController.getActiveCampaigns(req, res));
router.post("/:id/click", (req, res) => campaignController.recordCampaignClick(req, res));

// Campaign discount routes
router.post("/sync-discounts", (req, res) => campaignController.syncCampaignDiscounts(req, res));
router.get("/discounts/:userId", (req, res) => campaignController.getAvailableCampaignDiscounts(req, res));

// Admin routes (authentication required - add middleware as needed)
router.post("/", (req, res) => campaignController.createCampaign(req, res));
router.get("/", (req, res) => campaignController.getAllCampaigns(req, res));
router.get("/:campaignId", (req, res) => campaignController.getCampaignById(req, res));
router.put("/:campaignId", (req, res) => campaignController.updateCampaign(req, res));
router.delete("/:campaignId", (req, res) => campaignController.deleteCampaign(req, res));
router.get("/:campaignId/analytics", (req, res) => campaignController.getCampaignAnalytics(req, res));

module.exports = router; 