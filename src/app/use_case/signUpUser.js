class SignUpUser {
  constructor({
    baseUserRepository,
    hashService,
    tokenService,
    roleService,
    otpService,
  }) {
    this.baseUserRepository = baseUserRepository;
    this.hashService = hashService;
    this.tokenService = tokenService;
    this.roleService = roleService;
    this.otpService = otpService;
  }

  async execute(email, password, type, otp) {
    if (otp) {
      const validate = await this.otpService.verifyOtp(email, otp);
      
      if (!validate) throw new Error("Not verified");
      let user = await this.baseUserRepository.findByEmail(email);
      console.log(user);
      if (user) {
        // Check for conflicting roles
        const hasCoachRole = user.roles.includes("coach");
        const hasOwnerRole = user.roles.includes("owner");
        console.log(hasOwnerRole);
        if (
          (hasCoachRole && type === "owner") ||
          (hasOwnerRole && type === "coach")
        ) {
          throw new Error(
            "This email is already registered with a conflicting role. Please use a different email."
          );
        }

        if (user.roles.includes(type)) {
          throw Error("User already exists with this role");
        }
        user.roles.push(type);
        user = await this.baseUserRepository.update(user.id, user);
      } else {
        const hashedPassword = await this.hashService.hash(password);
        if(validate) console.log(type);
        user = await this.baseUserRepository.save({
          email,
          password: hashedPassword,
          roles: [type],
        });
      } console.log('its done');
      // Handle role-specific logic
      await this.roleService.assignRole(user.id, email, type);
      const token = this.tokenService.generate({ id: user.id });
      return { token, user };
    } else throw Error("Please send otp");
  }
}

module.exports = SignUpUser;
