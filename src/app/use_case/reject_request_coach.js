class RejectRequestCoach {
  constructor({ notificationRepository, requestRepository }) {
    this.notificationRepository = notificationRepository;
    this.requestRepository = requestRepository;
  }

  async execution(notificationId) {
    const mongoose = require("mongoose");
    const session = await mongoose.startSession();

    try {
      session.startTransaction();

      const notification = await this.notificationRepository.findById(
        notificationId
      );

      if (!notification) {
        throw new Error("Notification not found");
      }

      const type = notification.data.status;

      if (type === "manager") {
        await this.requestRepository.deleteRequestByCenterForManager(
          notification.data.centerId,
          notification.userId
        );
      } else if (type === "coach") {
        await this.requestRepository.deleteRequestByCenterForCoach(
          notification.data.centerId,
          notification.userId
        );
      }

      // Optionally delete the notification too
      await this.notificationRepository.deleteById(notificationId, session);

      await session.commitTransaction();
      console.log("deleted request & notification");
      return true;
    } catch (err) {
      await session.abortTransaction();
      console.error("Transaction failed:", err);
      throw err;
    } finally {
      session.endSession();
    }
  }
}

module.exports = RejectRequestCoach;
