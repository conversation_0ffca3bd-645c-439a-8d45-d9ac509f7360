const { generateEntityId } = require("./classzIdGenerate");

class ClasszIdUseCase {
  constructor({ BaseUser, User, BusinessOwner, Center, Coach }) {
    this.BaseUser = BaseUser;
    this.User = User;
    this.BusinessOwner = BusinessOwner;
    this.Center = Center;
    this.Coach = Coach;
  }

  async checkMissingIds() {
    const baseUsers = await this.BaseUser.find({});
    
    const missingIds = {
      parent: [],
      owner: [],
      coach: [],
      center: []
    };

    const totals = {
      baseUsers: baseUsers.length,
      parent: 0,
      owner: 0,
      coach: 0,
      center: 0
    };

    for (const baseUser of baseUsers) {
      const roles = baseUser.roles;
      
      for (const role of roles) {
        let entity;
        
        switch(role) {
          case 'parent':
            entity = await this.User.findOne({ baseUser: baseUser._id });
            totals.parent++;
            if (!entity?.classzId) {
              missingIds.parent.push({
                baseUserId: baseUser._id,
                email: baseUser.email,
                entityId: entity?._id
              });
            }
            break;
            
          case 'owner':
            entity = await this.BusinessOwner.findOne({ baseUser: baseUser._id });
            totals.owner++;
            if (!entity?.classzId) {
              missingIds.owner.push({
                baseUserId: baseUser._id,
                email: baseUser.email,
                entityId: entity?._id
              });
            }
            break;
            
          case 'coach':
            entity = await this.Coach.findOne({ baseUser: baseUser._id });
            totals.coach++;
            if (!entity?.classzId) {
              missingIds.coach.push({
                baseUserId: baseUser._id,
                email: baseUser.email,
                entityId: entity?._id
              });
            }
            break;
            
          case 'center':
            entity = await this.Center.findOne({ baseUser: baseUser._id });
            totals.center++;
            if (!entity?.classzId) {
              missingIds.center.push({
                baseUserId: baseUser._id,
                email: baseUser.email,
                entityId: entity?._id,
                ownerId: entity?.owner
              });
            }
            break;
        }
      }
    }
    
    return {
      totals,
      missingIds,
      summary: {
        totalBaseUsers: baseUsers.length,
        totalMissing: Object.values(missingIds).reduce((acc, curr) => acc + curr.length, 0),
        missingByType: {
          parent: missingIds.parent.length,
          owner: missingIds.owner.length,
          coach: missingIds.coach.length,
          center: missingIds.center.length
        }
      }
    };
  }

  async generateMissingIds() {
    const baseUsers = await this.BaseUser.find({});
    
    const updatedIds = {
      parent: [],
      owner: [],
      coach: [],
      center: []
    };

    const failed = {
      parent: [],
      owner: [],
      coach: [],
      center: []
    };
    
    for (const baseUser of baseUsers) {
      const roles = baseUser.roles;
      
      for (const role of roles) {
        let entity;
        let classzId;
        
        try {
          switch(role) {
            case 'parent':
              entity = await this.User.findOne({ baseUser: baseUser._id });
              if (entity && !entity.classzId) {
                classzId = generateEntityId('parent');
                await this.User.findByIdAndUpdate(entity._id, { classzId });
                updatedIds.parent.push({ id: entity._id, classzId });
              }
              break;
              
            case 'owner':
              entity = await this.BusinessOwner.findOne({ baseUser: baseUser._id });
              if (entity && !entity.classzId) {
                classzId = generateEntityId('business');
                await this.BusinessOwner.findByIdAndUpdate(entity._id, { classzId });
                updatedIds.owner.push({ id: entity._id, classzId });
              }
              break;
              
            case 'coach':
              entity = await this.Coach.findOne({ baseUser: baseUser._id });
              if (entity && !entity.classzId) {
                classzId = generateEntityId('coach');
                await this.Coach.findByIdAndUpdate(entity._id, { classzId });
                updatedIds.coach.push({ id: entity._id, classzId });
              }
              break;
              
            case 'center':
              entity = await this.Center.findOne({ baseUser: baseUser._id });
              if (entity && !entity.classzId) {
                const owner = await this.BusinessOwner.findById(entity.owner);
                if (owner && owner.classzId) {
                  const centerCount = await this.Center.countDocuments({ owner: owner._id });
                  classzId = generateEntityId('center', {
                    businessId: owner.classzId,
                    centreIndex: centerCount
                  });
                  await this.Center.findByIdAndUpdate(entity._id, { classzId });
                  updatedIds.center.push({ id: entity._id, classzId });
                } else {
                  failed.center.push({
                    id: entity._id,
                    reason: "Owner not found or missing classzId",
                    ownerId: entity.owner
                  });
                }
              }
              break;
          }
        } catch (error) {
          failed[role].push({
            baseUserId: baseUser._id,
            email: baseUser.email,
            error: error.message,
            entityId: entity?._id
          });
        }
      }
    }
    
    return { updatedIds, failed };
  }
}

module.exports = ClasszIdUseCase; 