// src/use_case/updateCenter.js
const path = require("path");
const { getSafeTimestamp } = require("../../utils/dateUtils");

class UpdateCenter {
  constructor({
    centerRepository,
    baseUserRepository,
    tokenService,
    imageService,
    centerValidationService,
    classRepository,
  }) {
    this.centerRepository = centerRepository;
    this.baseUserRepository = baseUserRepository;
    this.tokenService = tokenService;
    this.imageService = imageService;
    this.centerValidationService = centerValidationService;
    this.classRepository = classRepository;
  }

  async execute(centerId, centerData) {
    try {
      const user = await this.baseUserRepository.findById(centerId);
      console.log(user);
      if (!user) {
        throw new Error("User not found");
      }
      const updateData = {
        ...user.centerData,
        ...centerData,
        type: "center", // Set the type to "parent"
      };
      const uploadFile = async (file, fieldName) => {
        if (file) {
          console.log(file);
          console.log(fieldName);
          const filename = `${centerId}-${getSafeTimestamp()}${path.extname(
            file.originalname
          )}`;
          console.log(filename);
          try {
            const { url, contentType } = await this.imageService.uploadImage(
              file,
              filename
            );
            updateData[fieldName] = {
              url: this.imageService.convertToUrlPath(url),
              contentType,
            };
            console.log(`${fieldName} details set:`, updateData[fieldName]);
          } catch (error) {
            throw new Error(`${fieldName} upload failed: ` + error.message);
          }
        }
      };

      // Handle businessCertificate image
      await uploadFile(centerData.businessCertificate, "businessCertificate");

      // Handle sexual conviction record
      await uploadFile(centerData.sexualConvictionRecord, "sexualConvictionRecord");

      // Handle hkidCard image
      await uploadFile(centerData.hkidCard, "hkidCard");

      // Handle mainImage
      await uploadFile(centerData.mainImage, "mainImage");

      // Handle multiple images
      if (centerData.images && centerData.images.length > 0) {
        try {
          updateData.images = await Promise.all(
            centerData.images.map(async (image) => {
              const filename = `${centerId}-${getSafeTimestamp()}${path.extname(
                image.originalname
              )}`;
              const { url, contentType } = await this.imageService.uploadImage(
                image,
                filename
              );
              return { url, contentType };
            })
          );
          console.log("Images details set:", updateData.images);
        } catch (error) {
          throw new Error("Images upload failed: " + error.message);
        }
      }

      // Add 'coach' role to roles array if not already present
      if (!user.roles.includes("center")) {
        user.roles.push("center");
      }

      // Update the coachData in the BaseUser schema
      user.centerData = updateData;

      // Save the updated user
      await user.save();

      const token = this.tokenService.generate({
        id: centerId,
        type: "center",
      });

      return { token, user };
    } catch (error) {
      console.error("Error executing update center:", error);
      throw error;
    }
  }

  async getAllCenter(skip = 0, limit = 10, promotionOnly = false) {
    try {
      // Build query filter based on promotionOnly parameter
      const queryFilter = {};
      if (promotionOnly) {
        queryFilter.promotion = true;
        console.log('Filtering centers with promotion=true');
      }
      
      // Get centers with appropriate filtering and pagination
      const allCenters = await this.centerRepository.findAll(queryFilter, skip, limit);
      
      // We need classRepository to calculate pricing - check if it's available
      if (!this.classRepository) {
        console.warn("classRepository not available in updateCenter - pricing calculation skipped");
        return allCenters;
      }
      
      // Calculate pricing for each center from their classes
      const centersWithPricing = await Promise.all(allCenters.map(async (center) => {
        try {
          const classes = await this.classRepository.getByCenter(center._id);
          
          let priceFrom = null;
          let priceTo = null;

          if (classes && classes.length > 0) {
            // Filter out SEN classes (they should be free)
            const nonSenClasses = classes.filter(c => !c.sen);
            const prices = nonSenClasses.map((p) => p.classDate && p.classDate.charge ? p.classDate.charge : p.charge).filter((p) => p != null && p > 0);
            
            if (prices.length > 0) {
              priceFrom = Math.min(...prices);
              priceTo = Math.max(...prices);
            }
          }

          const centerObject = center.toObject ? center.toObject() : center;
          return {
            ...centerObject,
            priceFrom,
            priceTo,
          };
        } catch (classError) {
          // If there's an error fetching classes, just return center without pricing
          console.warn(`Error fetching classes for center ${center._id}:`, classError.message);
          const centerObject = center.toObject ? center.toObject() : center;
          return {
            ...centerObject,
            priceFrom: null,
            priceTo: null,
          };
        }
      }));
      
      return centersWithPricing;
    } catch (error) {
      console.error("Error fetching all centers with pagination:", error);
      throw new Error("Error fetching centers");
    }
  }
  
  async countCenters(promotionOnly = false) {
    try {
      // Build query filter based on promotionOnly parameter
      const queryFilter = {};
      if (promotionOnly) {
        queryFilter.promotion = true;
      }
      
      // Count centers with appropriate filtering
      const total = await this.centerRepository.countAll(queryFilter);
      return total;
    } catch (error) {
      console.error("Error counting all centers:", error);
      throw new Error("Error counting centers");
    }
  }
}

module.exports = UpdateCenter;
