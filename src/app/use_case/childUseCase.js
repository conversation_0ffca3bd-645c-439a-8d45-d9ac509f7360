const path = require("path");
const AttendanceRepo = require("../repo/attendanceRepo");
const { generateEntityId } = require("../use_case/classzIdGenerate");
class ChildUseCases {
  constructor({ childRepo, userRepository, tokenService, uploadFile }) {
    this.childRepo = childRepo;
    this.userRepository = userRepository;
    this.tokenService = tokenService;
    this.uploadFile = uploadFile;
    this.attendanceRepo = new AttendanceRepo(); // Add the attendance repository
  }

  async getParticipationStats(childId) {
    try {
      // Get attendance records for the child
      const attendanceRecords = await this.attendanceRepo.getHistoryBychildId(
        childId
      );

      // Calculate total classes attended
      const classesAttended = attendanceRecords.length;

      // Calculate total hours participated
      let totalMinutes = 0;
      let firstClassDate = null;

      for (const record of attendanceRecords) {
        // If the class has duration information, add it to the total
        if (record.classId && record.classId.durationMinutes) {
          totalMinutes += record.classId.durationMinutes;
        }

        // Track the earliest class date
        const recordDate = new Date(record.createdAt);
        if (!firstClassDate || recordDate < firstClassDate) {
          firstClassDate = recordDate;
        }
      }

      // Convert minutes to hours (rounded to nearest hour)
      const hoursParticipated = Math.round(totalMinutes / 60);

      // If no classes attended, use current date as the start date
      if (!firstClassDate) {
        firstClassDate = new Date();
      }

      return {
        classesAttended,
        hoursParticipated,
        firstClassDate: firstClassDate.toISOString(),
      };
    } catch (error) {
      throw new Error("Error fetching participation stats: " + error.message);
    }
  }

  async processUploadFields(childData, baseUserId) {
    const fields = ["mainImage"]; // Focusing only on required fields for child creation

    try {
      for (const fieldName of fields) {
        if (!childData[fieldName]) {
          continue; // Skip if no file data
        }

        if (Array.isArray(childData[fieldName])) {
          for (const file of childData[fieldName]) {
            if (!file || !file.path) {
              console.error(`Missing path for file in ${fieldName}:`, file);
              continue;
            }
            await this.uploadFile.uploadFile(
              file,
              fieldName,
              childData,
              baseUserId
            );
          }
        } else if (childData[fieldName]) {
          // If file is null or undefined, skip upload
          if (!childData[fieldName].path) {
            continue;
          }
          await this.uploadFile.uploadFile(
            childData[fieldName],
            fieldName,
            childData,
            baseUserId
          );
        }
      }
    } catch (error) {
      console.error("Error in processUploadFields:", error);
      throw new Error(`File upload processing error: ${error.message}`);
    }
  }

  async createChild(childData) {
    try {
      // First check if the parent exists
      const parent = await this.userRepository.findById(childData.parent);
      if (!parent) {
        throw new Error(`Parent with ID ${childData.parent} not found`);
      }

      // Process file uploads if present
      if (childData.mainImage) {
        await this.processUploadFields(childData, parent.id);
      }

      // Create the child entity in the repository
      const classzId = generateEntityId("child", {
        parentId: parent.classzId,
      });
      childData.classzId = classzId;
      const child = await this.childRepo.create(childData);

      // Generate a token for the new child
      const token = this.tokenService.generate({
        id: child.id,
        type: "child",
      });

      return { token, child };
    } catch (error) {
      console.error("Error in createChild:", error);
      throw new Error(`Child creation failed: ${error.message}`);
    }
  }

  async getChildById(id) {
    try {
      return await this.childRepo.getChildById(id);
    } catch (error) {
      throw new Error("Error fetching child by ID: " + error.message);
    }
  }

  async updateChildById(id, updateData) {
    try {
      // Fetch the existing child by ID
      const existingChild = await this.childRepo.getChildById(id);
      
      // Check if the child exists
      if (!existingChild) {
        throw new Error("Child not found");
      }

      // Merge existing child data with incoming data
      const mergedData = {
        ...existingChild.toObject(), // Convert Mongoose document to plain object if using Mongoose
        ...updateData,
      };

      // Handle file uploads if present
      if (updateData.mainImage) {
        await this.uploadFile.uploadFile(
          updateData.mainImage,
          "mainImage",
          mergedData,
          id
        );
      }

      // Update the child in the repository
      return await this.childRepo.updateChildById(id, mergedData);
    } catch (error) {
      console.error("Error in updateChildById:", error);
      throw new Error("Error updating child by ID: " + error.message);
    }
  }

  async getChildByParentId(parentId) {
    try {
      return await this.childRepo.getChildByParentId(parentId);
    } catch (error) {
      throw new Error("Error fetching child by parent ID: " + error.message);
    }
  }

  async deleteChildById(id) {
    try {
      return await this.childRepo.deleteChildById(id);
    } catch (error) {
      throw new Error("Error deleting child by ID: " + error.message);
    }
  }
}

module.exports = ChildUseCases;
