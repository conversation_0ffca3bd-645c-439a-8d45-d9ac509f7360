class BusinessOwnerUseCase {
  constructor({
    businessOwnerRepository,
    centerRepository,
    coachRepository,
    orderRepository,
    classRepository,
    sendNotificationToAssignCoach,
    sendNotificationToRemoveCoach,
  }) {
    this.businessOwnerRepository = businessOwnerRepository;
    this.centerRepository = centerRepository;
    this.coachRepository = coachRepository;
    this.orderRepository = orderRepository;
    this.classRepository = classRepository;
    this.sendNotificationToAssignCoach = sendNotificationToAssignCoach;
    this.sendNotificationToRemoveCoach = sendNotificationToRemoveCoach;
  }

  async updateOwner(ownerId, updateData) {
    return this.businessOwnerRepository.update(ownerId, updateData);
  }

  async getOwnerById(ownerId) {
    return this.businessOwnerRepository.findById(ownerId);
  }

  async getBranchByOwner(ownerId) {
    return this.centerRepository.findByOwnerId(ownerId);
  }

  async deleteBranch(branchId) {
    return this.centerRepository.delete(branchId);
  }

  async updateBranch(branchId, updateData) {
    return this.centerRepository.update(branchId, updateData);
  }

  async getOwnerDashboard(ownerId) {
    // Implement logic to fetch dashboard data
    // This might involve querying multiple repositories
    const owner = await this.businessOwnerRepository.findById(ownerId);
    if (!owner) {
      throw new Error("Business Owner not found");
    }

    const centers = await this.centerRepository.findByOwnerId(ownerId);
    const coaches = await this.coachRepository.findByOwnerId(ownerId);
    const classes = await this.classRepository.findByOwnerId(ownerId);
    const orders = await this.orderRepository.findByOwnerId(ownerId);

    return {
      ownerInfo: owner,
      centers: centers.length,
      coaches: coaches.length,
      classes: classes.length,
      orders: orders.length,
      // Add more dashboard specific data as needed
    };
  }

  async assignCoach(ownerId, coachId) {
    // Logic for assigning coach to owner (or their center)
    const owner = await this.businessOwnerRepository.findById(ownerId);
    const coach = await this.coachRepository.findById(coachId);

    if (!owner) throw new Error("Owner not found");
    if (!coach) throw new Error("Coach not found");

    // Example: send notification to coach
    await this.sendNotificationToAssignCoach.execute(coachId, ownerId);
    return { message: "Coach assignment process initiated" };
  }

  async removeCoach(ownerId, coachId) {
    // Logic for removing coach from owner (or their center)
    const owner = await this.businessOwnerRepository.findById(ownerId);
    const coach = await this.coachRepository.findById(coachId);

    if (!owner) throw new Error("Owner not found");
    if (!coach) throw new Error("Coach not found");

    // Example: send notification to coach
    await this.sendNotificationToRemoveCoach.execute(coachId, ownerId);
    return { message: "Coach removal process initiated" };
  }
}

module.exports = BusinessOwnerUseCase; 