class AssignCoachtoCenter {
  constructor({ centerRepository, coachRepository, notificationRepository }) {
    this.centerRepository = centerRepository;
    this.coachRepository = coachRepository;
    this.notificationRepository = notificationRepository;
  }

  async execute(centerId, coachId,
    notificationId
  ) {
    const session = await this.centerRepository.startSession();

    try {
      session.startTransaction();

      const center = await this.centerRepository.findById(centerId, session);
      if (!center) throw new Error("Center not found");
      const coach = await this.coachRepository.findById(coachId, session);

      if (!coach) throw new Error("Coach not found");

      // Check if the coach is already assigned to the center
      const isAlreadyAssigned = center.coachs.includes(coachId.toString());

      if (isAlreadyAssigned) {
        // If the coach is already assigned, abort the transaction and end the session
        throw new Error("Coach already assigned to this center.");
      }

      // Assign coach to center
      center.coachs.push(coachId);
      coach.center = centerId;

      // Save the updated center and coach
      await this.centerRepository.update(
        centerId,
        { coachs: center.coachs },
        session
      );
      const newCoach = await this.coachRepository.update(
        coachId,
        { center: coach.center },
        session
      );

      let data = { coach: newCoach };

      try {
        await this.notificationRepository.deleteById(notificationId);
      } catch (error) {
        // It's not critical if notification deletion fails, so we just log it.
        console.error(`Could not delete notification ${notificationId}:`, error);
      }
      await session.commitTransaction();
      await session.endSession();
      return data;
    } catch (error) {
      // If an error occurs, abort the transaction and end the session
      await session.abortTransaction();
      session.endSession();
      console.error("Transaction failed:", error.message);
      throw error;
    }
  }
}

module.exports = AssignCoachtoCenter;
