class RemoveManagerFromCenter {
  constructor({ coachRepository, centerRepository, baseUserRepository }) {
    this.coachRepository = coachRepository;
    this.centerRepository = centerRepository;
    this.baseUserRepository = baseUserRepository;
  }

  async execute(centerId, coachId) {
    const session = await this.centerRepository.startSession();
    session.startTransaction();

    try {
      const center = await this.centerRepository.findById(centerId, session);
      const coach = await this.coachRepository.findById(coachId, session);

      if (!center) throw new Error("Center not found");
      if (!coach) throw new Error("Coach not found");

      if (!center.managers.includes(coachId)) {
        throw new Error("This coach is not a manager of this center");
      }

      // Remove coachId from center.managers
      center.managers = center.managers.filter(
        (id) => id.toString() !== coachId.toString()
      );
      await this.centerRepository.update(centerId, center, session);

      // Remove manager reference from coach
      coach.manager = null;
      await this.coachRepository.update(coachId, coach, session);

      // Remove 'manager' role if it exists
      const baseUserId = coach.baseUser;
      const baseUserData = await this.baseUserRepository.findById(
        baseUserId,
        session
      );

      if (baseUserData.roles.includes("manager")) {
        baseUserData.roles = baseUserData.roles.filter(
          (role) => role !== "manager"
        );
      }

      await this.baseUserRepository.update(baseUserId, baseUserData, session);
      let data = { center: center, coach: coach };
      console.log(data);
      await session.commitTransaction();
      await session.endSession();
      return data;
    } catch (error) {
      await session.abortTransaction();
      await session.endSession();
      throw new Error("Remove manager failed, rolled back: " + error.message);
    }
  }
}

module.exports = RemoveManagerFromCenter;
