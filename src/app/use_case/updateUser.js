const path = require("path");

class UpdateUser {
  constructor({
    baseUserRepository,
    userRepository,
    tokenService,
    uploadFile,
    hashService,
  }) {
    this.baseUserRepository = baseUserRepository;
    this.userRepository = userRepository;
    this.tokenService = tokenService;
    this.uploadFile = uploadFile;
    this.hashService = hashService;
  }

  async processUploadFields(userData, userId) {
    console.log(`Starting processUploadFields for user ${userId}`);
    const fields = ["businessCertificate", "sexualConvictionRecord", "hkidCard", "mainImage", "images"];

    try {
      // Process each field that might contain file data
      for (const fieldName of fields) {
        // Skip if the field doesn't exist in the userData
        if (!userData[fieldName]) {
          console.log(`Field ${fieldName} not found in request data, skipping`);
          continue;
        }

        console.log(`Processing ${fieldName} field`);

        // Handle array of files (like images)
        if (Array.isArray(userData[fieldName])) {
          console.log(
            `Processing array of files for ${fieldName}, count: ${userData[fieldName].length}`
          );

          for (const file of userData[fieldName]) {
            // Skip invalid files
            if (
              !file ||
              (typeof file === "object" &&
                !file.path &&
                !file.buffer &&
                !file.url &&
                !file.uri &&
                !file.data)
            ) {
              console.error(`Invalid file data in ${fieldName} array:`, file);
              continue;
            }

            try {
              console.log(`Uploading file from ${fieldName} array`);
              await this.uploadFile.uploadFile(
                file,
                fieldName,
                userData,
                userId
              );
              console.log(`Successfully uploaded file from ${fieldName} array`);
            } catch (fileError) {
              console.error(
                `Error uploading file from ${fieldName} array:`,
                fileError
              );
              // Continue with other files in the array even if one fails
            }
          }
        }
        // Handle single file
        else {
          const file = userData[fieldName];
          console.log(
            `Processing single file for ${fieldName}, type: ${typeof file}`
          );

          // Log the file properties to help with debugging
          if (typeof file === "object") {
            console.log(`${fieldName} properties:`, Object.keys(file));
          } else {
            console.log(`${fieldName} is not an object but ${typeof file}`);
          }

          try {
            // Upload the file
            await this.uploadFile.uploadFile(file, fieldName, userData, userId);
            console.log(`Successfully uploaded file for ${fieldName}`);
          } catch (error) {
            console.error(`Error uploading ${fieldName}:`, error);
            // For mainImage which is critical for profile, throw the error
            if (fieldName === "mainImage") {
              throw new Error(`Profile image upload failed: ${error.message}`);
            }
            // For other fields, we can continue
          }
        }
      }

      console.log(`Completed processUploadFields for user ${userId}`);
    } catch (error) {
      console.error(`Error in processUploadFields:`, error);
      throw error; // Re-throw to be handled by the caller
    }
  }

  async execute(userId, data) {
    try {
      console.log(`Starting update for user ${userId}`);
      console.log("Received data:", JSON.stringify(data, null, 2));

      // Find the user
      const user = await this.userRepository.findById(userId);
      if (!user) {
        console.error(`User not found with ID: ${userId}`);
        throw new Error("User not found");
      }
      console.log(`Found user: ${user.id}`);

      // Process any upload fields
      await this.processUploadFields(data, userId);
      console.log("File uploads processed successfully");

      // Merge the user data with the new data
      let updateData = {
        ...user._doc, // Ensure we use the document data
        ...data,
      };

      // Handle location data properly
      if (data.location) {
        const newLocation = Array.isArray(data.location)
          ? data.location
          : [data.location];

        updateData.location = [...(user.location || []), ...newLocation];

        if (data.location[0].default == true) {
          // console.log(
          //   "Setting default address to true for the last added location"
          // );
          // Reset all locations' default to false first
          updateData.location = updateData.location.map((address) => {
            address.default = false; // Set all to false first

            return address;
          });
          // Now set the last added location's default to true
          updateData.location[updateData.location.length - 1].default = true;
        }
      }

      //  Update the user data in the repository
      const updatedUser = await this.userRepository.update(userId, updateData);
   //   console.log(`User ${userId} updated successfully`);

      // Generate a new token for the updated user
      const token = this.tokenService.generate({ id: updatedUser.id });

      return {
        token,
        data: {
          parent: updatedUser,
        },
      };
    } catch (error) {
      console.error(`Error updating user ${userId}:`, error);
      throw new Error(`Update failed: ${error.message}`);
    }
  }
  async newPassword(email, password) {
    try {
      console.log("Starting new password process");
      console.log("Email:", email);
      // Find the user by email
      const user = await this.baseUserRepository.findByEmail(email);
      if (!user) {
        throw new Error("User not found");
      }
console.log("User found:", user);
      const isMatch = await this.hashService.compare(password, user.password);
      console.log("Is match:", isMatch);
      if (isMatch) throw new Error("You cannot use the same password");
      const hashedPassword = await this.hashService.hash(password);
       // Update the password in the repository
      const updatedUser = await this.baseUserRepository.updatePassword(
        user.id,
        hashedPassword
      );
      return true;
    } catch (error) {
      throw new Error(`Update failed: ${error.message}`);
    }
  }
  async deleteAddress(userId, addressId) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new Error("User not found");
      }
      // Filter out the address to be deleted
      const updatedLocations = user.location.filter(
        (address) => address._id.toString() !== addressId
      );
      // Update the user with the new locations
      const updatedUser = await this.userRepository.update(userId, {
        location: updatedLocations,
      });
      console.log("Address deleted successfully:", updatedUser);
      return {
        data: {
          parent: updatedUser,
        },
      };
    } catch (error) {
      throw new Error(`Delete failed: ${error.message}`);
    }
  }

  async changeDefaultAddress(userId, addressId) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new Error("User not found");
      }
      // Find the address to be set as default
      user.location.forEach((address) => {
        address.default = address._id.toString() === addressId;
      });

      const updatedUser = await this.userRepository.update(userId, {
        location: user.location,
      });
      return {
        data: {
          parent: updatedUser,
        },
      };
    } catch (error) {
      throw new Error(`Change Default Address Failed: ${error.message}`);
    }
  }
}

module.exports = UpdateUser;
