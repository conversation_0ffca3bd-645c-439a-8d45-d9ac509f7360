class GetSavedCardUseCase {
  constructor(userRepository, paymentService) {
    this.userRepository = userRepository;
    this.paymentService = paymentService;
  }

  async execute(userId) {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new Error("User not found.");
    }
    console.log(user.stripeCustomerId + "HI");
    const cards = await this.paymentService.getSavedCard(user.stripeCustomerId);
    return cards;
  }
}

module.exports = GetSavedCardUseCase;
