class SignInUser {
  constructor({ baseUserRepository, hashService, tokenService, roleService }) {
    this.baseUserRepository = baseUserRepository;
    this.hashService = hashService;
    this.tokenService = tokenService;
    this.roleService = roleService; // New role service
  }

  async execute(email, password, type) {
    const user = await this.baseUserRepository.findByEmail(email);
    if (!user) throw new Error("User not found");
console.log(`User found: ${user.password},
  ${password}`);
    const isMatch = await this.hashService.compare(password, user.password);
    if (!isMatch) throw new Error("Password is incorrect");
    console.log(type);
    // Get user data based on roles
    const roleData = await this.roleService.getUserRoles(user, email, type);

    // Generate token
    const token = this.tokenService.generate({ id: user.id });

    return { token, data: roleData };
  }
}

module.exports = SignInUser;
