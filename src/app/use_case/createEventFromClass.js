const moment = require("moment-timezone");
const Event = require("../models/eventModel");
const NotificationService = require("../services/notificationService");
const { convertToCron } = require("../../utils/cronConverter");

class CreateEventsFromClass {
  constructor(eventRepository, classRepo, classDateRepo) {
    this.eventRepository = eventRepository;
    this.classRepo = classRepo;
    this.classDateRepo = classDateRepo;
  }

  async create(classDetails, dateDetails, options = {}) {
    console.log("📅 Generating events from class...");
console.log(classDetails);
console.log(dateDetails);
    const {
      classProviding,
      classId,
      coachId,
      centerId,
      numberOfClass = 1, // fallback if not provided
    } = classDetails;

    const {
      date,
      repeat = "None",
      _id: dateId,
      startTime,
    } = dateDetails;

    if (!date || !startTime || !classId || !centerId || !dateId) {
      throw new Error("Missing required event or class details");
    }

    const timeZone = "UTC";
    const initialDate = moment.tz(date, "DD/MM/YYYY", timeZone);

    if (!initialDate.isValid()) {
      throw new Error("Invalid date format. Expected 'DD/MM/YYYY'");
    }

    const token = await NotificationService.getTokenById(centerId) || null;

    const events = [];

    for (let i = 0; i < numberOfClass; i++) {
      let eventDate = initialDate.clone();

      switch (repeat) {
        case "Every day":
          eventDate.add(i, "days");
          break;
        case "Every week":
          eventDate.add(i, "weeks");
          break;
        case "Every month":
          eventDate.add(i, "months");
          break;
        case "Every year":
          eventDate.add(i, "years");
          break;
        case "Custom":
          // TODO: Add custom repeat handling
          break;
        case "None":
        default:
          if (i > 0) continue; // only one-time event
          break;
      }

      const event = new Event({
        date: eventDate.toDate(),
        classId,
        dateId,
        centerId,
        coachId
      });
      events.push(event);

      // Schedule notification
      try {
        const cronExpression = convertToCron(eventDate, startTime);

        const message = {
          title: "Class Reminder",
          body: `${classProviding} class starts in 10 minutes.`,
          url: "", // add redirect if needed
        };

        await NotificationService.scheduleNotification(
          centerId,
          token,
          message,
          cronExpression
        );
      } catch (error) {
        console.error("❌ Failed to schedule notification:", error.message);
        // Continue without throwing; log for visibility
      }
    }

    // Save events
    try {
      await this.eventRepository.saveMany(events, options);
      console.log(`✅ ${events.length} events saved for classId: ${classId}`);
    } catch (error) {
      console.error("❌ Error saving events:", error.message);
      throw new Error("Failed to save events to repository");
    }
  }
}

module.exports = CreateEventsFromClass;
