class CoachUseCase {
  constructor({
    coachRepository,
    baseUserRepository,
    tokenService,
    uploadFile,
  }) {
    this.coachRepository = coachRepository;
    this.baseUserRepository = baseUserRepository;
    this.tokenService = tokenService;
    this.uploadFile = uploadFile;
  }
  async processUploadFields(centerData, baseUserId) {
    const fields = [
      "businessCertificate",
      "sexualConvictionRecord",
      "hkidCard",
      "mainImage",
      "images",
    ];

    for (const fieldName of fields) {
      if (centerData[fieldName] && Array.isArray(centerData[fieldName])) {
        console.log(
          `Processing multiple files for ${fieldName}:`,
          centerData[fieldName]
        );
        for (const file of centerData[fieldName]) {
          if (!file.path) {
            console.error(`Missing path for file in ${fieldName}:`, file);
            continue; // Skip files without path
          }
          await this.uploadFile.uploadFile(
            file,
            fieldName,
            centerData,
            baseUserId
          );
        }
      } else if (centerData[fieldName]) {
        console.log(
          `Processing single file for ${fieldName}:`,
          centerData[fieldName]
        );
        if (!centerData[fieldName].path) {
          throw new Error(`Missing path for single file in ${fieldName}`);
        }
        await this.uploadFile.uploadFile(
          centerData[fieldName],
          fieldName,
          centerData,
          baseUserId
        );
      }
    }
  }
  async create(coachData) {
    if (!coachData.baseUser) {
      throw new Error("Base user ID is required to create a coach.");
    }
    const baseUserId = coachData.baseUser;
    await this.processUploadFields(coachData, baseUserId);

    const coach = await this.coachRepository.create(coachData);
    const token = await this.tokenService.generate({
      id: coach._id,
      type: "coach",
    });
    return { token, coach };
  }
  async update(coachId, coachData) {
    console.log("coachUpdate");
    try {
      // Find the Coach by ID
      const coach = await this.coachRepository.findById(coachId);
      if (!coach) {
        throw new Error("Coach not found");
      }
      const baseUserId = coach.baseUser;

      // Process upload fields (images, files, etc.)
      await this.processUploadFields(coachData, baseUserId);

      // Prepare the update data
      const updateData = { ...coachData };

      // Check if skills are provided and add them to the existing skills (if any)
      if (coachData.skill && coachData.skill.length > 0) {
        // Add the new skills to the existing skills array
        updateData.skill = [...coach.skill, ...coachData.skill];
      }

      // Check if experience data is provided and add it to the existing experience (if any)
      if (coachData.experience && coachData.experience.length > 0) {
        // Add the new experience data to the existing experience array
        updateData.experience = [...coach.experience, ...coachData.experience];
      }

      // Check if accreditation data is provided and add it to the existing accreditation (if any)
      if (coachData.accredation && coachData.accredation.length > 0) {
        console.log(updateData.accredation);
        // Add the new accreditation to the existing accreditation array
        updateData.accredation = [
          ...coach.accredation,
          ...coachData.accredation,
        ];
        console.log(updateData.accredation);
      }

      // Check if images are provided and add them to the existing images (if any)
      if (coachData.images && coachData.images.length > 0) {
        // Add the new images to the existing images array
        updateData.images = [...coach.images, ...coachData.images];
      }

      console.log("updateData", updateData);

      //  Update the coach with the new data
      const updatedCoach = await this.coachRepository.update(
        coachId,
        updateData
      );
      console.log("Done");

      return updatedCoach;
    } catch (error) {
      console.error("Error updating coach:", error);
      throw error;
    }
  }
  async getCoachByIdWithCenter(coachId) {
    try {
      const coach = await this.coachRepository.findByIdWithCenter(coachId);
      return coach;
    } catch (error) {
      throw new Error("Error getting coach by ID: " + error.message);
    }
  }
  async getCoachById(coachId) {
    try {
      const coach = await this.coachRepository.findById(coachId);
      return coach;
    } catch (error) {
      throw new Error("Error getting coach by ID: " + error.message);
    }
  }
  async deleteCoachById(coachId) {
    try {
      const coach = await this.coachRepository.delete(coachId);
      return coach;
    } catch (error) {
      throw new Error("Error deleting coach by ID: " + error.message);
    }
  }
  async getAllCoach() {
    try {
      const coach = await this.coachRepository.findAllExcludingIndividualEducators();
      return coach;
    } catch (error) {
      throw new Error("Error fetching coachs: " + error.message);
    }
  }
  async getAllCoachIncludingIndividualEducators() {
    try {
      const coach = await this.coachRepository.findAll();
      return coach;
    } catch (error) {
      throw new Error("Error fetching coachs: " + error.message);
    }
  }
  async getProgramByCoachId(coachId) {
    try {
      const coach = await this.coachRepository.findById(coachId);
      if (!coach) {
        throw new Error("Coach not found");
      }
      const program = await this.coachRepository.getProgramByCoachId(coachId);
      return program;
    } catch (error) {
      throw new Error("Error fetching program by coach ID: " + error.message);
    }
  }
}

module.exports = CoachUseCase;
