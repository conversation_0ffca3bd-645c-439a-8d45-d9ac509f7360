class RequestCenterUseCase {
  constructor({ coachRepository, centerRepository,
    reqestRepository
   }) {
    this.coachRepository = coachRepository;
    this.centerRepository = centerRepository;
    this.reqestRepository=reqestRepository;

  }

  async exrequestCenterecute(centerId, coachId) {
    try {
      const center = await this.centerRepository.findById(centerId);
      const coach = await this.coachRepository.findById(coachId);
      if (!center) {
        throw new Error("Center not found");
      }
      if (!coach) {
        throw new Error("Coach not found");
      }
    } catch (error) {
      throw new Error(error.message);
    }
  }
}

module.exports = RequestCenterUseCase;
