const { v4: uuidv4 } = require("uuid");
const Transaction = require("../entities/transaction");

class PurchaseZCoin {
  constructor(walletRepo, transactionRepo, paymentProcessor) {
    this.walletRepo = walletRepo;
    this.transactionRepo = transactionRepo;
    this.paymentProcessor = paymentProcessor;
  }

  async execute(userId, amount, cardToken) {
    // Generate transaction ID at the beginning
    const transactionId = uuidv4();
    console.log("Transaction ID Generated:", transactionId);

    const session = await mongoose.startSession();
    session.startTransaction();
    try {
      const transaction = new Transaction(
        transactionId,
        userId,
        amount,
        "purchase",
        new Date()
      );

      const wallet = await this.walletRepo.getWalletByUserId(userId);
      if (!wallet) throw new Error("Wallet not found");

      // Process payment
      await this.paymentProcessor.processPayment(amount, cardToken);

      wallet.balance += amount;
      await this.walletRepo.updateWallet(wallet);

      transaction.status = "completed";
      await this.transactionRepo.saveTransaction(transaction);

      await session.commitTransaction();
      return transaction;
    } catch (error) {
      await session.abortTransaction();
      console.error("Transaction Error:", error);

      // Handle error, ensuring the transaction is logged as failed
      const failedTransaction = new Transaction(
        transactionId,
        userId,
        amount,
        "purchase",
        new Date()
      );
      failedTransaction.status = "failed";
      await this.transactionRepo.saveTransaction(failedTransaction);

      throw new Error(`Failed to complete purchase: ${error.message}`);
    } finally {
      session.endSession();
    }
  }
}

module.exports = PurchaseZCoin;
