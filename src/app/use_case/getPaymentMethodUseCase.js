class GetPaymentMethodUseCase {
  constructor(userRepository, paymentService) {
    this.userRepository = userRepository;
    this.paymentService = paymentService;
  }

  async execute(userId) {
    try {
      // Get the user's customer ID
      const user = await this.userRepository.findById(userId);
      
      if (!user || !user.stripeCustomerId) {
        throw new Error("User not found or no Stripe customer ID available");
      }
      
      // Get the payment method from Stripe
      const paymentMethod = await this.paymentService.getPaymentMethod(user.stripeCustomerId);
      
      return paymentMethod;
    } catch (error) {
      console.error("Failed to get payment method:", error.message);
      throw new Error(error.message);
    }
  }
}

module.exports = GetPaymentMethodUseCase; 