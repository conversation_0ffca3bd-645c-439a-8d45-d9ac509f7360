class BalanceUseCase {
  constructor(balanceRepo, userRepo) {
    this.balanceRepo = balanceRepo;
    this.userRepo = userRepo;
  }
  async createBalance(data) {
    try {
      return await this.balanceRepo.createBalance(data);
    } catch (error) {
      throw new Error(error.message);
    }
  }
  async getBalance(userId) {
    try {
      return await this.balanceRepo.getBalance(userId);
    } catch (error) {
      throw new Error(error.message);
    }
  }
  async updateBalance(userId, data) {
    try {
      const existingUser = await this.userRepo.exists(userId);
      if (!existingUser) {
        throw new Error("Child not found");
      }
      return await this.balanceRepo.update(userId, data);
    } catch (error) {
      throw new Error(error.message);
    }
  }
  
  async addBalance(userId, amount) {
    try {
      console.log(`BalanceUseCase: Adding ${amount} ZCoins to user ${userId}`);
      
      // Check if user exists
      const existingUser = await this.userRepo.exists(userId);
      if (!existingUser) {
        throw new Error("User not found");
      }
      
      // Update both the user model and the balance model
      console.log("Updating user model balance...");
      const updatedUser = await this.userRepo.addBalance(userId, amount);
      
      console.log("Updating balance model...");
      const updatedBalance = await this.balanceRepo.addBalance(userId, amount);
      
      console.log(`Balance updated successfully in both models:`);
      console.log(`- User model balance: ${updatedUser.balance}`);
      console.log(`- Balance model balance: ${updatedBalance.balance}`);
      
      return updatedBalance;
    } catch (error) {
      console.error(`Failed to add balance: ${error.message}`);
      throw new Error(`Failed to add balance: ${error.message}`);
    }
  }
}

module.exports = BalanceUseCase;
