class AssignManagerToCenterUseCase {
  constructor({
    centerRepository,
    coachRepository,
    baseUserRepository,
    notificationRepository,
    requestRepository,
  }) {
    this.centerRepository = centerRepository;
    this.coachRepository = coachRepository;
    this.baseUserRepository = baseUserRepository;
    this.notificationRepository = notificationRepository;
    this.requestRepository = requestRepository;
  }

  async execute(centerId, coachId, notificationId) {
    const session = await this.centerRepository.startSession();
    session.startTransaction();

    try {
      console.log("add manager");

      const center = await this.centerRepository.findById(centerId, session);
      if (!center) throw new Error("No center exists at this ID.");

      const coach = await this.coachRepository.findById(coachId, session);
      if (!coach) throw new Error("No coach exists at this ID.");

      // Avoid duplicate assignment
      if (center.managers.includes(coachId)) {
        throw new Error("Coach already assigned to this center.");
      }

      // Assign coach to center
      center.managers.push(coachId);
      await this.centerRepository.update(centerId, center, session);

      // Assign center to coach
      coach.manager = centerId;
      await this.coachRepository.update(coachId, coach, session);

      // Update base user role
      const baseUserId = coach.baseUser;
      let baseUserData = await this.baseUserRepository.findById(
        baseUserId,
        session
      );

      if (!baseUserData.roles.includes("manager")) {
        baseUserData.roles.push("manager");
      }

      await this.baseUserRepository.update(baseUserId, baseUserData, session);
      await this.requestRepository.deleteRequestByCenterForManager(
        centerId,
        coachId
      );
      let data = { center: center, coach: coach };
      console.log(data);
      try {
        console.log(`hi ${notificationId}`);
        await this.notificationRepository.deleteById(notificationId);
        console.log("deleted");
      } catch (error) {
        console.log(`cant delete ${notificationId}`);
      }
      await session.commitTransaction();
      await session.endSession();
      return data;
    } catch (error) {
      await session.abortTransaction();
      await session.endSession();
      throw new Error("Assign coach failed, rolled back: " + error.message);
    }
  }
}

module.exports = AssignManagerToCenterUseCase;
