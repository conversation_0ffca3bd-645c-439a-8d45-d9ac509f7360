const crypto = require("crypto");

/**
 * Generate a random 4-character alphanumeric string
 */
function generateRandomCode(length = 4) {
  return crypto
    .randomBytes(length)
    .toString("base64")
    .replace(/[^A-Z0-9]/gi, "")
    .substring(0, length)
    .toUpperCase();
}

/**
 * Format date to YYMMDD
 */
function getDateCode(date = new Date()) {
  const yy = String(date.getFullYear()).slice(-2);
  const mm = String(date.getMonth() + 1).padStart(2, "0");
  const dd = String(date.getDate()).padStart(2, "0");
  return `${yy}${mm}${dd}`;
}

/**
 * Generate ID based on entity type
 */
function generateEntityId(type, options = {}) {
  const dateCode = getDateCode();
  const randomCode = generateRandomCode();
  console.log(`in generate: ${type} `);
  switch (type) {
    case "parent":
      return `PAR-${dateCode}-${randomCode}`;
    case "child":
      if (!options.parentId) {
        throw new Error("Child ID requires parentId");
      }
      const randomChildCode = generateRandomCode(); // e.g., A4B9
      return `${options.parentId}-CHD-${randomChildCode}`;

    case "business":
      return `BIZ-${dateCode}-${randomCode}`;
    case "center":
      if (!options.businessId || typeof options.centreIndex !== "number") {
        throw new Error("Centre ID requires businessId and centreIndex");
      }
      const centreNum = String(options.centreIndex).padStart(2, "0");
      return `${options.businessId}-CTR${centreNum}`;
    case "coach":
      return `COA-${dateCode}-${randomCode}`;
    default:
      throw new Error(
        "Unknown type. Use: parent, child, business, centre, coach."
      );
  }
}

module.exports = { generateEntityId };
