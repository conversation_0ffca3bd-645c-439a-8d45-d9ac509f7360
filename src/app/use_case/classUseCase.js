const path = require("path");
const mongoose = require("mongoose");

class ClassUseCase {
  constructor({
    classRepo,
    tokenService,
    uploadFile,
    createEventsFromClass,
    classDateRepo,
    eventRepository,
  }) {
    console.log("ClassUseCase constructor called with:", {
      hasClassRepo: !!classRepo,
      hasTokenService: !!tokenService,
      hasUploadFile: !!uploadFile,
      hasCreateEventsFromClass: !!createEventsFromClass,
      hasClassDateRepo: !!classDateRepo,
      hasEventRepository: !!eventRepository,
    });

    if (!classDateRepo) {
      console.error("classDateRepo is undefined in ClassUseCase constructor");
      throw new Error("classDateRepo is required");
    }

    if (!eventRepository) {
      console.error("eventRepository is undefined in ClassUseCase constructor");
      throw new Error("eventRepository is required");
    }

    this.classRepo = classRepo;
    this.tokenService = tokenService;
    this.uploadFile = uploadFile;
    this.createEventsFromClass = createEventsFromClass;
    this.classDateRepo = classDateRepo;
    this.eventRepository = eventRepository;
  }
  async processUploadFields(centerData, baseUserId) {
    const fields = ["businessCertificate", "hkidCard", "mainImage", "images"];

    for (const fieldName of fields) {
      if (centerData[fieldName] && Array.isArray(centerData[fieldName])) {
        console.log(
          `Processing multiple files for ${fieldName}:`,
          centerData[fieldName]
        );
        for (const file of centerData[fieldName]) {
          if (!file.path) {
            console.error(`Missing path for file in ${fieldName}:`, file);
            continue; // Skip files without path
          }
          await this.uploadFile.uploadFile(
            file,
            fieldName,
            centerData,
            baseUserId
          );
        }
      } else if (centerData[fieldName]) {
        console.log(
          `Processing single file for ${fieldName}:`,
          centerData[fieldName]
        );
        if (!centerData[fieldName].path) {
          throw new Error(`Missing path for single file in ${fieldName}`);
        }
        await this.uploadFile.uploadFile(
          centerData[fieldName],
          fieldName,
          centerData,
          baseUserId
        );
      }
    }
  }

  async create(classData) {
    console.log("Creating class with data:", classData);

    await this.processUploadFields(classData, classData.center);

    // Generate a token for the new class

    const classs = await this.classRepo.create(classData);
    const token = this.tokenService.generate({
      id: classs.id,
      type: "class",
    });
    // Add the classId to the coach's programs array
    // if (classData.coachId) {
    //   await this.coachRepo.update(classData.coachId, {
    //     $addToSet: { programs: classs._id }, // Prevents duplicate entries
    //   });
    // }

    return { token, classs };
  }

  async getById(classId) {
    return await this.classRepo.getById(classId);
  }

  async getByIds(classIds) {
    return await this.classRepo.findByIds(classIds);
  }

  async getByCategory(category) {
    return await this.classRepo.getByCategory(category);
  }

  // Get all classes with optional paginatio
  async getAll(skip, limit) {
    return await this.classRepo.getAll(skip, limit);
  }

  // Update a class by ID
  async update(classId, classData) {
    console.log("Updating class with ID:", classId);
    console.log(classData);
    // Start MongoDB session for transaction
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Fetch existing class data from the repository
      const existingClass = await this.classRepo.getByIdWithoutPopulate(
        classId
      );
      if (!existingClass) {
        return existing;
      }

      // Validate numberOfStudent if provided
      if (
        classData.numberOfStudent &&
        typeof classData.numberOfStudent !== "number"
      ) {
        throw new Error("numberOfStudent must be a number");
      }

      // Update basic class information
      // If this is just adding schedules, don't update class-level fields except for charge
      let updatedClassInfo;
      if (classData.addScheduleOnly) {
        // For schedule-only updates, preserve existing class data but update charge and coach if provided
        updatedClassInfo = existingClass;
        console.log(
          "🔍 DEBUG: Schedule-only update, preserving existing class data"
        );
        console.log(
          "🔍 DEBUG: Existing class languageOptions:",
          existingClass.languageOptions
        );
        console.log(
          "🔍 DEBUG: Existing class numberOfStudent:",
          existingClass.numberOfStudent
        );
        console.log("🔍 DEBUG: Existing class charge:", existingClass.charge); // CHANGE to classDate.charge if available
        console.log("🔍 DEBUG: Existing class coach:", existingClass.coach);

        // Prepare update fields
        let updateFields = {};

        // Update the main class charge if provided in schedule data
        // This ensures UI components reading class.charge show the correct price
        // CHANGE LOGIC: Use classDate.charge instead of class.charge
        if (classData.charge !== undefined && classData.charge !== null) {
          console.log(
            "🔍 DEBUG: Updating class charge from schedule data:",
            classData.charge
          ); // CHANGE to classDate.charge if available
          updateFields.charge = classData.charge;
        }

        // Update the coach if provided in schedule data
        // This ensures the class record has the correct coach assignment
        if (classData.coach !== undefined && classData.coach !== null) {
          console.log(
            "🔍 DEBUG: Updating class coach from schedule data:",
            classData.coach
          );
          updateFields.coach = classData.coach;
        }

        // Only update if there are fields to update
        if (Object.keys(updateFields).length > 0) {
          console.log("🔍 DEBUG: Updating class with fields:", updateFields);
          updatedClassInfo = await this.classRepo.update(
            classId,
            updateFields,
            { session }
          );
        }
      } else {
        // For full class updates, update all provided fields
        console.log("🔍 DEBUG: Full class update with data:", classData);
        updatedClassInfo = await this.classRepo.update(
          classId,
          {
            ...classData,
          },
          { session }
        );
      }

      // Handle dates if provided
      if (classData.dates && Array.isArray(classData.dates)) {
        try {
          // Create class dates in parallel
          const datePromises = classData.dates.map(async (date) => {
            const existing = await this.classDateRepo.getDateByClassIdAndDate(
              classId,
              date.date
            );

            // // If it exists, skip or return existing
            // if (existing) {
            //   return existing;
            // }
            const dateData = {
              classId,
              date: date.date,
              address: date.address || classData.address,
              startTime: date.startTime,
              endTime: date.endTime,
              durationMinutes: date.durationMinutes || "0",
              weekDay: date.weekDay,
              repeat: date.repeat,
              students: date.students || [],
              minimumStudent:
                date.minimumStudent || classData.minimumStudent || 1,
              numberOfStudent:
                date.numberOfStudent || classData.numberOfStudent || null,
              numberOfClass:
                date.numberOfClass || classData.numberOfClass || null,
              charge: date.charge || classData.charge || null,
              languageOptions:
                date.languageOptions || classData.languageOptions || [],
              buyAll:
                date.buyAll !== undefined ? date.buyAll : classData.buyAll,
              joinNew:
                date.joinNew !== undefined ? date.joinNew : classData.joinNew,
              status: date.status || "scheduled",
              cancellationReason: date.cancellationReason,
              cancellationType: date.cancellationType,
              cancelledAt: date.cancelledAt,
            };

            const classDate = await this.classDateRepo.createDate(dateData);
            console.log(classData.event);
            // Create corresponding event
            if (classData.event !== false) {
              await this.createEventsFromClass.create(
                {
                  classProviding: existingClass.classProviding,
                  numberOfClass: classData.numberOfClass,
                  centerId: existingClass.center,
                  coachId: classData.coach || existingClass.coach, // Use updated coach from classData if available
                  classId: classId,
                },
                {
                  date: classDate.date,
                  startTime: classDate.startTime,
                  endTime: classDate.endTime,
                  durationMinutes: classDate.durationMinutes,
                  repeat: classDate.repeat,
                  _id: classDate._id,
                },
                { session }
              );
            }

            return classDate;
          });

          await Promise.all(datePromises);
        } catch (error) {
          console.error("Error creating class dates:", error);
          throw error;
        }
      }

      await session.commitTransaction();
      return updatedClassInfo;
    } catch (error) {
      await session.abortTransaction();
      console.error("Error in class update:", error);
      throw new Error(`Failed to update class: ${error.message}`);
    } finally {
      session.endSession();
    }
  }

  // Delete a class by ID
  async delete(classId) {
    console.log(`Deleting class with ID: ${classId}`);

    // Start a transaction to ensure all related data is cleaned up
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // First, check if the class exists
      const existingClass = await this.classRepo.getByIdWithoutPopulate(
        classId
      );
      if (!existingClass) {
        throw new Error("Class not found");
      }

      // Check if the class has enrolled students - prevent deletion if students are enrolled
      if (existingClass.student && existingClass.student.length > 0) {
        throw new Error(
          "Cannot delete class with enrolled students. Please remove students first or use cancellation options."
        );
      }

      console.log(
        `Class found: ${existingClass.classProviding}. Proceeding with cleanup...`
      );

      // Delete all related events for this class
      try {
        const deletedEvents = await this.eventRepository.deleteManyByClassId(
          classId
        );
        console.log(
          `Deleted ${deletedEvents.deletedCount || 0} related events`
        );
      } catch (error) {
        console.log(
          `Warning: Could not delete events for class ${classId}: ${error.message}`
        );
      }

      // Delete all related class dates for this class
      try {
        const deletedClassDates = await this.classDateRepo.deleteByClassId(
          classId
        );
        console.log(`Deleted related class dates`);
      } catch (error) {
        console.log(
          `Warning: Could not delete class dates for class ${classId}: ${error.message}`
        );
      }

      // Finally, delete the class itself
      const deletedClass = await this.classRepo.delete(classId);

      // Commit the transaction
      await session.commitTransaction();
      console.log(`Successfully deleted class ${classId} and all related data`);

      return deletedClass;
    } catch (error) {
      // Rollback on error
      await session.abortTransaction();
      console.error(`Error deleting class ${classId}:`, error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  // Get all classes by center ID
  async getByCenter(centerId) {
    console.log("centerId");
    return await this.classRepo.getByCenter(centerId);
  }
  async getClassesByCoach(coachId) {
    return await this.classRepo.getClassesByCoach(coachId);
  }
  async getStudents(slotId) {
    return await this.classDateRepo.getStudents(slotId);
  }
  async getCoachCenter(classId) {
    return await this.classRepo.getCoachCenter(classId);
  }

  // Get filtered classes for parent view of a center
  async getClassesForParent(centerId, filters) {
    try {
      const classes = await this.classRepo.getByCenter(centerId);
      const today = new Date();

      // const filtered = classes.filter((cls) => {
      //   // Skip classes with no dates
      //   if (!cls.dates || cls.dates.length === 0) {
      //     return false;
      //   }

      //   if (cls.joinNew === false) {
      //     // Parse dates from the class
      //     const upcomingDates = cls.dates
      //       .map((d) => {
      //         const [day, month, year] = d.date.split("/").map(Number);
      //         return new Date(`20${year}`, month - 1, day); // Assuming '25' → 2025
      //       })
      //       .filter((date) => date >= today); // Keep only future dates

      //     // If there are no upcoming dates, skip this class
      //     if (upcomingDates.length === 0) {
      //       return false;
      //     }
      //   }

      //   // If newComer is true, or there are future dates
      //   return true;
      // });

      return classes;
    } catch (error) {
      console.error("Error in getClassesForParent:", error);
      throw error;
    }
  }

  // Helper method to get earliest class date
  getEarliestClassDate(classItem) {
    if (!classItem.dates || classItem.dates.length === 0) {
      return new Date(0); // Return earliest possible date if no dates
    }
    return classItem.dates
      .map((date) => this.parseDate(date.date))
      .reduce((earliest, current) => (current < earliest ? current : earliest));
  }

  // Helper method to get latest class date
  getLatestClassDate(classItem) {
    if (!classItem.dates || classItem.dates.length === 0) {
      return new Date(); // Return current date if no dates
    }
    return classItem.dates
      .map((date) => this.parseDate(date.date))
      .reduce((latest, current) => (current > latest ? current : latest));
  }

  // Helper method to parse date string in DD/MM/YYYY format
  parseDate(dateString) {
    const [day, month, year] = dateString.split("/").map(Number);
    return new Date(year, month - 1, day); // month is 0-based in JavaScript
  }

  async updateCoach(classId, coachId) {
    try {
      console.log(classId, coachId);
      // Validate inputs
      if (!classId || !coachId) {
        throw new Error("Class ID and Coach ID are required");
      }

      // Get the existing class to verify it exists
      const existingClass = await this.classRepo.getById(classId);
      if (!existingClass) {
        throw new Error("Class not found");
      }
      console.log("received");
      // Update the coach
      const updatedClass = await this.classRepo.updateCoach(classId, coachId);

      // Update existing events with the new coach
      try {
        await this.eventRepository.updateCoachForClass(classId, coachId);
        console.log("✅ Successfully updated events with new coach");
      } catch (error) {
        console.log(
          "⚠️ Warning: Error updating events with new coach:",
          error.message
        );
        // Don't throw here as the coach update was successful
      }

      return true;
    } catch (error) {
      console.error("Error in updateCoach use case:", error);
      throw error;
    }
  }

  async removeCoachFromAllClasses(coachId) {
    try {
      if (!coachId) {
        throw new Error("Coach ID is required");
      }

      // Remove coach from all their classes
      const result = await this.classRepo.removeCoachFromAllClasses(coachId);

      // Update events for the affected classes
      // Note: We're not throwing if event update fails since the main operation succeeded
      try {
        // Get all classes that were affected
        const affectedClasses = await this.classRepo.getClassesByCoach(coachId);

        // Update events for each class
        // for (const classItem of affectedClasses) {
        //   const eventData = {
        //     classProviding: classItem.classProviding,
        //     description: classItem.description,
        //     coachId: null, // Coach is being removed
        //     centerId: classItem.center,
        //     dates: classItem.dates,
        //     _id: classItem._id,
        //     numberOfClass: classItem.numberOfClass,
        //   };
        //   await this.createEventsFromClass.execute(eventData);
        // }
      } catch (error) {
        console.log("Warning: Error updating events:", error.message);
      }

      return result;
    } catch (error) {
      console.error("Error in removeCoachFromAllClasses use case:", error);
      throw error;
    }
  }

  async checkMinimumStudents(classId, dateId) {
    try {
      // Get the class date
      const classDate = await this.classDateRepo.getDateById(dateId);

      if (!classDate) {
        throw new Error(`Class date with ID ${dateId} not found`);
      }

      // Get the class
      const classData = await this.classRepo.findById(classId);

      if (!classData) {
        throw new Error(`Class with ID ${classId} not found`);
      }

      // Get the minimum student requirement
      const minimumRequired =
        classDate.minimumStudent || classData.minimumStudent || 1;

      // Get the current number of students enrolled
      const currentStudents = classDate.students
        ? classDate.students.length
        : 0;

      // Check if the minimum requirement is met
      const meetsMinimumRequirement = currentStudents >= minimumRequired;

      return {
        meetsMinimumRequirement,
        currentStudents,
        minimumRequired,
        classDate,
        classData,
      };
    } catch (error) {
      console.error(`Error checking minimum students: ${error.message}`);
      throw new Error(`Failed to check minimum students: ${error.message}`);
    }
  }

  async cancelClassDate(
    classId,
    dateId,
    reason,
    cancellationType = "automatic_cancellation"
  ) {
    try {
      // Get the class date
      const classDate = await this.classDateRepo.getDateById(dateId);

      if (!classDate) {
        throw new Error(`Class date with ID ${dateId} not found`);
      }

      // Update the class date status to cancelled
      const updatedClassDate = await this.classDateRepo.update(dateId, {
        status: "cancelled",
        cancellationReason: reason,
        cancellationType: cancellationType,
        cancelledAt: new Date(),
      });

      // Update any associated events
      // This would depend on your event model and repository structure
      // For example:
      // await this.eventRepository.updateEventStatus(dateId, "cancelled", reason);

      return {
        message: "Class date cancelled successfully",
        classDate: updatedClassDate,
      };
    } catch (error) {
      console.error(`Error cancelling class date: ${error.message}`);
      throw new Error(`Failed to cancel class date: ${error.message}`);
    }
  }

  async getClassSlots(classId) {
    return await this.classDateRepo.getDatesByClassId(classId);
  }

  /**
   * Delete a slot (class date) by its slotId and all related events
   * @param {string} slotId
   * @returns {Promise<boolean>} true if successful, otherwise throws
   * @throws {Error} If slotId is missing or deletion fails
   */
  async deleteSlotById(slotId) {
    if (!slotId) {
      throw new Error("Slot ID is required");
    }
    // Delete all events with this slotId as dateId
    await this.eventRepository.deleteManyByDateId(slotId);
    // Delete the slot itself
    const deletedSlot = await this.classDateRepo.deleteBySlotId(slotId);
    if (!deletedSlot) {
      throw new Error("Slot not found or could not be deleted");
    }
    return true;
  }
}

module.exports = ClassUseCase;
