const ExpUpdateUseCase = require("./expUpdateUseCase");
const mongoose = require("mongoose");

class ReviewUseCase {
  constructor(
    reviewRepository,
    centerUseCase,
    childUseCases,
    coachUseCase,
    attendanceRepository,
    eventRepository,
    uploadProcessor,
    classUseCase,
    orderRepository
  ) {
    this.reviewRepository = reviewRepository;
    this.centerUseCase = centerUseCase;
    this.childUseCases = childUseCases;
    this.coachUseCase = coachUseCase;
    this.attendanceRepository = attendanceRepository;
    this.eventRepository = eventRepository;
    this.uploadProcessor = uploadProcessor;
    this.classUseCase = classUseCase;
    this.orderRepository = orderRepository;
  }

  async createReview(reviewData) {
    try {
      console.log("Creating review...Await for upload processor");
      console.log(reviewData);
      await this.uploadProcessor.processUploadFields(
        reviewData,
        reviewData.reviewerId
      );
      console.log("Processing upload fields...");

      // const review = await this.reviewRepository.createReview(reviewData);

      await this.updateAverageRating(
        reviewData.revieweeId,
        reviewData.revieweeType
      );

      // Update performance metrics if this is a review for a child
      if (reviewData.revieweeType === "child") {
        await this.updateChildPerformanceMetrics(reviewData.revieweeId);
      }

      if (review.rating > 8.5) {
        ExpUpdateUseCase.execute(review.revieweeId, 10);
      }
      return review;
    } catch (error) {
      throw new Error("Error in review service: " + error.message);
    }
  }
  async postReviewByParent(reviewData) {
    try {
      console.log("📥 Incoming review data:", reviewData);

      const { centerReview, coachReview } = reviewData;

      // Validate both reviews before proceeding (optional but safer)
      if (!centerReview || !coachReview) {
        throw new Error("Missing center or coach review in request.");
      }

      // Save reviews
      await this.reviewRepository.createReview(centerReview);
      await this.reviewRepository.createReview(coachReview);

      // Update ratings
      await Promise.all([
        this.updateAverageRating(
          centerReview.revieweeId,
          centerReview.revieweeType
        ),
        this.updateAverageRating(
          coachReview.revieweeId,
          coachReview.revieweeType
        ),
      ]);

      return true;
    } catch (error) {
      console.error("❌ Error in postReviewByParent:", error);
      throw new Error(`Error in posting review: ${error.message}`);
    }
  }

  // New method to calculate and update child performance metrics
  async updateChildPerformanceMetrics(childId) {
    try {
      // Get all reviews for this child
      const reviews = await this.reviewRepository.getReviewsByRevieweeIdAndType(
        childId,
        "child"
      );

      if (!reviews || reviews.length === 0) {
        console.log(`No reviews found for child ${childId}`);
        return;
      }

      // Calculate metrics based on review questions
      // Assuming:
      // q1 & q2 -> Outstanding Quality (e.g., participation and engagement)
      // q3 & q4 -> Key Competency (e.g., skill mastery and application)

      let outstandingQualitySum = 0;
      let keyCompetencySum = 0;
      let validReviewCount = 0;

      // Find the most common topics/subjects for distinctive conduct and learning progress
      const topicFrequency = {};

      for (const review of reviews) {
        if (review.questions) {
          // Only count reviews with question data
          validReviewCount++;

          // Outstanding Quality calculation (average of q1 and q2)
          const outstandingQualityScore =
            ((review.questions.q1 || 0) + (review.questions.q2 || 0)) / 2;
          outstandingQualitySum += outstandingQualityScore;

          // Key Competency calculation (average of q3 and q4)
          const keyCompetencyScore =
            ((review.questions.q3 || 0) + (review.questions.q4 || 0)) / 2;
          keyCompetencySum += keyCompetencyScore;

          // Track topics for distinctive conduct and learning progress
          if (review.topic) {
            topicFrequency[review.topic] =
              (topicFrequency[review.topic] || 0) + 1;
          }
        }
      }

      // Calculate final metrics
      const outstandingQuality =
        validReviewCount > 0 ? outstandingQualitySum / validReviewCount : 0;
      const keyCompetency =
        validReviewCount > 0 ? keyCompetencySum / validReviewCount : 0;

      // Find the most common topic (if any)
      let distinctiveConduct = "";
      let learningProgress = "";

      if (Object.keys(topicFrequency).length > 0) {
        const sortedTopics = Object.entries(topicFrequency).sort(
          (a, b) => b[1] - a[1]
        );
        distinctiveConduct = sortedTopics[0][0];
        learningProgress =
          sortedTopics.length > 1 ? sortedTopics[1][0] : sortedTopics[0][0];
      }

      // Update child record with calculated metrics
      await this.childUseCases.updateChildById(childId, {
        outstandingQuality,
        keyCompetency,
        distinctiveConduct,
        learningProgress,
        metricsLastUpdated: new Date(),
      });

      console.log(`Updated performance metrics for child ${childId}`);
      return {
        outstandingQuality,
        keyCompetency,
        distinctiveConduct,
        learningProgress,
      };
    } catch (error) {
      console.error("Error updating child performance metrics:", error);
      throw new Error(
        "Error updating child performance metrics: " + error.message
      );
    }
  }

  // New method to get child performance metrics
  async getChildPerformanceMetrics(childId) {
    try {
      const child = await this.childUseCases.getChildById(childId);
      if (!child) {
        throw new Error(`Child with ID ${childId} not found`);
      }

      return {
        outstandingQuality: child.outstandingQuality || 0,
        keyCompetency: child.keyCompetency || 0,
        distinctiveConduct: child.distinctiveConduct || "",
        learningProgress: child.learningProgress || "",
      };
    } catch (error) {
      console.error("Error getting child performance metrics:", error);
      throw new Error(
        "Error getting child performance metrics: " + error.message
      );
    }
  }

  async getReviews(revieweeId, revieweeType) {
    try {
      const reviews = await this.reviewRepository.getReviewsByRevieweeIdAndType(
        revieweeId,
        revieweeType
      );

      if (revieweeType === "child") {
        const { averages, bestQuestion, worstQuestion } =
          this.processChildReviewStats(reviews);
        return {
          reviews,

          bestQuestion,
          worstQuestion,
        };
      }

      return { reviews };
    } catch (error) {
      throw new Error("Error in review service: " + error.message);
    }
  }

  processChildReviewStats(reviews) {
    const questionStats = {};

    for (const review of reviews) {
      if (review.questions && typeof review.questions === "object") {
        for (const [question, score] of Object.entries(review.questions)) {
          if (typeof score === "number") {
            if (!questionStats[question]) {
              questionStats[question] = { total: 0, count: 0 };
            }
            questionStats[question].total += score;
            questionStats[question].count += 1;
          }
        }
      }
    }

    const averages = {};
    let bestQuestion = null;
    let worstQuestion = null;
    let highestAvg = -Infinity;
    let lowestAvg = Infinity;

    for (const [question, data] of Object.entries(questionStats)) {
      if (data.count > 0) {
        const avg = data.total / data.count;
        averages[question] = avg;

        if (avg > highestAvg) {
          highestAvg = avg;
          bestQuestion = question;
        }

        if (avg < lowestAvg) {
          lowestAvg = avg;
          worstQuestion = question;
        }
      } else {
        averages[question] = null;
      }
    }

    return { averages, bestQuestion, worstQuestion };
  }

  async getReviewsByReviewer(reviewerId, reviewerType) {
    try {
      console.log(`reviewerId: ${reviewerId}, reviewerType: ${reviewerType}`);
      const reviews = await this.reviewRepository.getReviewsByReviewerIdAndType(
        reviewerId,
        reviewerType
      );
      return reviews;
    } catch (error) {
      throw new Error("Error in review service: " + error.message);
    }
  }

  async updateAverageRating(revieweeId, revieweeType) {
    try {
      const { avgRating, reviewCount } =
        await this.reviewRepository.getAverageRatingByRevieweeId(
          revieweeId,
          revieweeType
        );
      console.log("avgRating", avgRating);
      if (revieweeType === "center") {
        await this.centerUseCase.update(revieweeId, {
          rating: avgRating,
          reviewCount: reviewCount,
        });
      }
      if (revieweeType === "child") {
        await this.childUseCases.updateChildById(revieweeId, {
          rating: avgRating,
        });
      }
      if (revieweeType === "coach") {
        await this.coachUseCase.update(revieweeId, {
          rating: avgRating,
        });
      }
      return { avgRating, reviewCount };
    } catch (error) {
      throw new Error("Error updating average rating: " + error.message);
    }
  }

  async getAverageRating(revieweeId, revieweeType) {
    try {
      const avgRating =
        await this.reviewRepository.getAverageRatingByRevieweeId(
          revieweeId,
          revieweeType
        );
      return avgRating;
    } catch (error) {
      throw new Error("Error in review service: " + error.message);
    }
  }
  async pendingReview(centerId) {
    try {
      console.time(`pendingReview-${centerId}`);
      const studentsWithoutReview = [];

      // Step 1: Fetch all attendance records for the center.
      const attendanceRecords =
        await this.attendanceRepository.getAttendanceByCenterId(centerId);

      if (!attendanceRecords || attendanceRecords.length === 0) {
        console.timeEnd(`pendingReview-${centerId}`);
        return [];
      }

      const classStudentMap = new Map(); // Stores classId -> { details, students: Map<studentId, studentObject> }
      const allClassIds = new Set();
      const allStudentIds = new Set();

      for (const attendance of attendanceRecords) {
        if (!attendance.classId || !attendance.classId._id) continue;

        const classIdString = attendance.classId._id.toString();
        allClassIds.add(classIdString);

        if (!classStudentMap.has(classIdString)) {
          const plainClassDetails = attendance.classId.toObject
            ? attendance.classId.toObject()
            : JSON.parse(JSON.stringify(attendance.classId));
          delete plainClassDetails.student;

          classStudentMap.set(classIdString, {
            details: plainClassDetails,
            students: new Map(),
          });
        }

        const students = attendance.classId.student;
        if (students && students.length > 0) {
          const classEntry = classStudentMap.get(classIdString);
          for (const student of students) {
            if (student && student._id) {
              const studentIdString = student._id.toString();
              allStudentIds.add(studentIdString);
              if (!classEntry.students.has(studentIdString)) {
                classEntry.students.set(
                  studentIdString,
                  student.toObject
                    ? student.toObject()
                    : JSON.parse(JSON.stringify(student))
                );
              }
            }
          }
        }
      }

      if (allClassIds.size === 0) {
        console.timeEnd(`pendingReview-${centerId}`);
        return [];
      }

      const today = new Date();
      const pastEvents = await this.eventRepository.find({
        classId: {
          $in: Array.from(allClassIds).map(
            (id) => new mongoose.Types.ObjectId(id)
          ),
        },
        date: { $lt: today },
      });

      if (!pastEvents || pastEvents.length === 0) {
        console.timeEnd(`pendingReview-${centerId}`);
        return [];
      }

      const existingReviews = await this.reviewRepository.find({
        revieweeId: {
          $in: Array.from(allStudentIds).map(
            (id) => new mongoose.Types.ObjectId(id)
          ),
        },
        revieweeType: "child",
        classId: {
          $in: Array.from(allClassIds).map(
            (id) => new mongoose.Types.ObjectId(id)
          ),
        },
      });

      const existingReviewKeys = new Set();
      for (const review of existingReviews) {
        if (review.revieweeId && review.classId && review.date) {
          const key = `${review.revieweeId.toString()}_${review.classId.toString()}_${new Date(
            review.date
          ).toDateString()}`;
          existingReviewKeys.add(key);
        }
      }

      for (const event of pastEvents) {
        const eventClassIdString = event.classId
          ? event.classId.toString()
          : null;
        if (!eventClassIdString || !classStudentMap.has(eventClassIdString)) {
          continue;
        }

        const classEntry = classStudentMap.get(eventClassIdString);
        const eventDate = new Date(event.date);
        const eventObject = event.toObject
          ? event.toObject()
          : JSON.parse(JSON.stringify(event));

        for (const [studentIdString, studentObject] of classEntry.students) {
          const reviewKey = `${studentIdString}_${eventClassIdString}_${eventDate.toDateString()}`;

          if (!existingReviewKeys.has(reviewKey)) {
            studentsWithoutReview.push({
              student: studentObject,
              studentId: studentObject,
              plainClassDetails: classEntry.details,
              event: eventObject,
            });
          }
        }
      }
      console.timeEnd(`pendingReview-${centerId}`);
      return studentsWithoutReview;
    } catch (error) {
      console.error("Error fetching pending reviews:", error);
      throw new Error("Error fetching pending reviews: " + error.message);
    }
  }
  async getPendingReviewByClassId(classId) {
    try {
      const studentsWithoutReview = [];

      // Step 1: Fetch attendance record(s) for the given classId
      const attendanceRecords =
        await this.attendanceRepository.getAttendanceByClassId(classId);

      const today = new Date(); // You can also use new Date() for real-time

      for (const attendance of attendanceRecords) {
        const classDetails = attendance.classId;
        const students = classDetails?.student || [];

        // Step 2: Get all event dates for the class
        const allDates = await this.eventRepository.getEventDatesByClassId(
          classId
        );
        const pastEvents = allDates.filter(
          (event) => new Date(event.date) < today
        );

        for (const student of students) {
          const studentId = student._id;

          // Step 3: Get reviews written by this student for this class
          const reviews =
            await this.reviewRepository.getReviewsByRevieweeIdAndType(
              studentId,
              "child"
            );

          for (const event of pastEvents) {
            const classDate = new Date(event.date);

            const classReview = reviews.find(
              (review) =>
                review.classId &&
                review.classId._id && // Ensure classId and _id exist
                review.classId._id.toString() === classId.toString() &&
                new Date(review.date).toDateString() ===
                  classDate.toDateString()
            );

            // Step 4: If review missing, add to result
            if (!classReview) {
              const plainClassDetails = classDetails.toObject();
              delete plainClassDetails.student;

              studentsWithoutReview.push({
                student,
                plainClassDetails,
                event,
              });
            }
          }
        }
      }

      return studentsWithoutReview;
    } catch (error) {
      console.error("Error fetching pending reviews by classId:", error);
      throw new Error(
        "Error fetching pending reviews by classId: " + error.message
      );
    }
  }
  async getPendingReviewByCoachId(coachId, limit = 10, skip = 0) {
    try {
      console.time("getPendingReviewByCoachId-internal");
      const studentsWithoutReview = [];

      // Step 1: Fetch attendance records for the given coachId with pagination
      // This is more efficient than fetching all records
      const attendanceRecords =
        await this.attendanceRepository.getAttendanceByCoachId(coachId);

      if (!attendanceRecords || attendanceRecords.length === 0) {
        console.timeEnd("getPendingReviewByCoachId-internal");
        return [];
      }

      // Extract all class IDs from attendance records
      const classIds = [
        ...new Set(
          attendanceRecords
            .map((record) => record.classId?._id?.toString())
            .filter(Boolean)
        ),
      ];

      // Extract all student IDs from the classes
      const studentIds = [];
      const classStudentMap = new Map(); // Map to track which students belong to which class

      for (const record of attendanceRecords) {
        const students = record.classId?.student || [];
        const classId = record.classId?._id?.toString();

        if (classId) {
          for (const student of students) {
            const studentId = student._id.toString();
            studentIds.push(studentId);

            // Track which students belong to which class
            if (!classStudentMap.has(classId)) {
              classStudentMap.set(classId, new Map());
            }
            classStudentMap.get(classId).set(studentId, student);
          }
        }
      }

      // Get all past events for these classes in one query
      const today = new Date();
      const allPastEvents = await this.eventRepository.find({
        classId: { $in: classIds },
        date: { $lt: today },
      });

      // Group events by class ID for faster lookup
      const eventsByClass = {};
      for (const event of allPastEvents) {
        const classId = event.classId.toString();
        if (!eventsByClass[classId]) {
          eventsByClass[classId] = [];
        }
        eventsByClass[classId].push(event);
      }

      // Get all existing reviews for these students in one query
      const existingReviews = await this.reviewRepository.find({
        revieweeId: { $in: studentIds },
        revieweeType: "child",
        classId: { $in: classIds },
      });

      // Create a map of existing reviews for fast lookup
      const reviewMap = new Map();
      for (const review of existingReviews) {
        const key = `${review.classId.toString()}_${review.revieweeId.toString()}_${new Date(
          review.date
        ).toDateString()}`;
        reviewMap.set(key, review);
      }

      // Now determine which students need reviews
      let pendingCount = 0;

      for (const classId of classIds) {
        const events = eventsByClass[classId] || [];
        const studentMap = classStudentMap.get(classId);

        if (!studentMap || events.length === 0) continue;

        for (const [studentId, student] of studentMap.entries()) {
          for (const event of events) {
            const classDate = new Date(event.date);
            const reviewKey = `${classId}_${studentId}_${classDate.toDateString()}`;

            if (!reviewMap.has(reviewKey)) {
              // This student needs a review for this class on this date
              // Skip entries if we're past the limit
              if (pendingCount < skip) {
                pendingCount++;
                continue;
              }

              // Stop adding if we've reached the limit
              if (studentsWithoutReview.length >= limit) {
                break;
              }

              // Find the class details
              const classDetails = attendanceRecords.find(
                (record) => record.classId?._id?.toString() === classId
              )?.classId;

              if (classDetails) {
                const plainClassDetails = classDetails.toObject
                  ? classDetails.toObject()
                  : JSON.parse(JSON.stringify(classDetails));

                // Don't include the full student list in the response
                delete plainClassDetails.student;

                studentsWithoutReview.push({
                  student: student,
                  studentId: student,
                  plainClassDetails,
                  event,
                });

                pendingCount++;
              }
            }
          }

          // Stop processing if we've reached the limit
          if (studentsWithoutReview.length >= limit) {
            break;
          }
        }

        // Stop processing classes if we've reached the limit
        if (studentsWithoutReview.length >= limit) {
          break;
        }
      }

      console.timeEnd("getPendingReviewByCoachId-internal");
      return studentsWithoutReview;
    } catch (error) {
      console.error("Error fetching pending reviews by coachId:", error);
      throw new Error(
        "Error fetching pending reviews by coachId: " + error.message
      );
    }
  }

  async getReviewByClassAndId(classId, revieweeId, revieweeType, date) {
    try {
      const reviews = await this.reviewRepository.getReviewByClassAndId(
        classId,
        revieweeId,
        revieweeType,
        date
      );
      return reviews;
    } catch (error) {
      throw new Error("Error in review service: " + error.message);
    }
  }

  // New method to get pending reviews for all children of a parent
  async getPendingReviewsByParentId(parentId) {
    try {
      // Fetch orders for the parent
      const orders = await this.orderRepository.getByUserForPending(parentId);
      if (!orders || orders.length === 0) {
        // Return empty arrays instead of throwing an error
        return {
          reviews: [],
          pendingReviews: [],
        };
      }

      // Extract class IDs from orders, filtering out any null/undefined values
      const classIds = orders
        .map((order) => order.classId?._id)
        .filter(Boolean);

      if (classIds.length === 0) {
        return {
          reviews: [],
          pendingReviews: [],
        };
      }

      // Fetch all reviews for these class IDs and parent ID in one query
      const reviews = await this.reviewRepository.findByClassIdsAndParentId(
        classIds,
        parentId
      );

      // Create a set of class IDs that have reviews
      const reviewedClassIds = new Set(
        (reviews || [])
          .map((review) => review.classId?._id?.toString())
          .filter(Boolean)
      );

      // Filter orders that don't have reviews
      const pendingReviews = orders.filter(
        (order) => order.classId?._id && !reviewedClassIds.has(order.classId._id.toString())
      );

      // Add childInfo to each review where classId matches
      const reviewsWithChildInfo = (reviews || []).map((review) => {
        const matchingOrder = orders.find(
          (order) =>
            order.classId?._id?.toString() === review.classId?._id?.toString()
        );
        return {
          ...(review.toObject?.() ?? review),
          childInfo: matchingOrder ? matchingOrder.childId : null,
        };
      });

      // 🔽 Sort by createdAt (or change to another date field if needed)
      reviewsWithChildInfo.sort(
        (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
      );

      return {
        reviews: reviewsWithChildInfo,
        pendingReviews,
      };
    } catch (error) {
      console.error(
        "[ReviewUseCase] Error fetching pending reviews:",
        error.message
      );
      throw new Error("Failed to fetch pending reviews: " + error.message);
    }
  }

  async getParentReviewHistory(parentId, childId = null) {
    console.log(
      `[ReviewUseCase] Getting review history for parent: ${parentId}${
        childId ? `, filtered by childId: ${childId}` : ""
      }`
    );
    try {
      // Assuming reviewerType for a parent is "Parent". Adjust if different.
      let history = await this.reviewRepository.getReviewsByReviewerIdAndType(
        parentId,
        "Parent"
      );
      console.log(`[ReviewUseCase] Raw history items count: ${history.length}`);

      if (history.length > 0) {
        console.log(
          `[ReviewUseCase] First history item sample:`,
          JSON.stringify(history[0], null, 2)
        );
      }

      // Ensure each history item has unique data by adding a timestamp to the review
      history = history.map((item, index) => {
        // Make sure we have a valid date
        if (!item.date) {
          item.date = item.createdAt || new Date();
        }

        // Log details about each item
        console.log(`[ReviewUseCase] History item ${index}:
          - revieweeType: ${item.revieweeType}
          - revieweeId: ${item.revieweeId}
          - rating: ${item.rating}
          - date: ${item.date}
          - classId: ${
            item.classId
              ? typeof item.classId === "object"
                ? item.classId._id
                : item.classId
              : "null"
          }
        `);

        return item;
      });

      // Filter by childId if provided
      if (childId) {
        history = history.filter(
          (review) =>
            review.revieweeId &&
            (review.revieweeId.toString() === childId ||
              (review.revieweeId._id &&
                review.revieweeId._id.toString() === childId))
        );
        console.log(
          `[ReviewUseCase] Filtered to ${history.length} items for childId: ${childId}`
        );
      }

      // Populate class details if they exist
      for (let i = 0; i < history.length; i++) {
        try {
          if (history[i].classId) {
            // If classId is a string (ObjectId), try to populate it with class details
            if (
              typeof history[i].classId === "string" ||
              history[i].classId instanceof mongoose.Types.ObjectId
            ) {
              const classDetails = await this.classRepository.getById(
                history[i].classId
              );
              if (classDetails) {
                history[i].classId = classDetails;
                console.log(
                  `[ReviewUseCase] Populated class details for item ${i}: ${classDetails.classProviding}`
                );
              }
            }
          }
        } catch (err) {
          console.error(
            `[ReviewUseCase] Error populating class details for item ${i}:`,
            err
          );
        }
      }

      console.log(
        `[ReviewUseCase] Found ${history.length} items in review history for parent ${parentId}`
      );
      return history;
    } catch (error) {
      console.error("[ReviewUseCase] Error in getParentReviewHistory:", error);
      throw new Error(
        "Error fetching review history for parent: " + error.message
      );
    }
  }
}

module.exports = ReviewUseCase;
