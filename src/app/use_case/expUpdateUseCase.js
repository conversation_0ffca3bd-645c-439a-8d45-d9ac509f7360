const UserRepository = require("../repo/userRepo");
const userRepository = new UserRepository();
const ChildRepository = require("../repo/childRepo");
const childRepository = new ChildRepository();
class ExpUpdateUseCase {
  static async execute(id, exp) {
    console.log(id);

    let parentId = await childRepository.getParentByChildId(id);
    if (parentId == null) {
      parentId = id;
    }
    const user = await userRepository.findById(parentId);

    if (user) {
      user.exp = user.exp + exp;
console.log(user);
      const updatedUser = await userRepository.update(parentId, user);
      console.log(updatedUser);
      return true;
    }
    return false;
  }
}

module.exports = ExpUpdateUseCase;
