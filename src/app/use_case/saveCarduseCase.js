class SaveCardUseCase {
  constructor(userRepository, paymentService) {
    this.userRepository = userRepository;
    this.paymentService = paymentService;
  }
  async execute(userId, cardToken) {
    console.log(`SaveCardUseCase.execute called with userId: ${userId}, cardToken: ${cardToken?.substring(0, 8)}...`);
    
    try {
      console.log(`Finding user with ID: ${userId}`);
      const user = await this.userRepository.findById(userId);
      
      if (!user) {
        console.error(`User not found with ID: ${userId}`);
        throw new Error("User Not found");
      }
      
      console.log(`User found: ${user.email}, stripeCustomerId: ${user.stripeCustomerId || 'none'}`);
      let customer;
      
      if (!user.stripeCustomerId) {
        console.log("No Stripe customer ID found, creating new customer");
        customer = await this.paymentService.createCustomer(
          user.email,
          cardToken
        );
        console.log("New customer created:", customer);
        user.stripeCustomerId = customer.id;
        console.log(`Updating user with new stripeCustomerId: ${customer.id}`);
        await this.userRepository.update(userId, {
          stripeCustomerId: customer.id,
        });
      } else {
        console.log(`Adding card to existing customer: ${user.stripeCustomerId}`);
        customer = await this.paymentService.addCardToCustomer(
          user.stripeCustomerId,
          cardToken
        );
        console.log("Card added to customer:", customer);
      }
      
      return customer;
    } catch (error) {
      console.error("Save card error:", error.message);
      throw new Error("could not execute save card: " + error.message);
    }
  }
}

module.exports = SaveCardUseCase;
