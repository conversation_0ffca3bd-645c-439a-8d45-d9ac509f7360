const path = require("path");
const mongoose = require("mongoose");
const ExpUpdateUseCase = require("./expUpdateUseCase");
const OrderSession = require("../models/orderSessionsModel");
const { v4: uuidv4 } = require("uuid");

class OrderUseCase {
  constructor({
    orderRepository,
    classUseCase,
    balanceUseCase,
    tokenService,
    purchasedHistoryRepo,
    orderSessionRepo,
    classDateRepo,
  }) {
    this.orderRepo = orderRepository;
    this.classUseCase = classUseCase;

    this.balanceUseCase = balanceUseCase;
    this.tokenService = tokenService;
    this.purchasedHistoryRepo = purchasedHistoryRepo;
    this.orderSessionRepo = orderSessionRepo;
    this.classDateRepo = classDateRepo;
    console.log("OrderUseCase initialized with:", {
      orderRepo: orderRepository,
      classUseCase,
      balanceUseCase,
      tokenService,
      orderSessionRepo,
      classDateRepo,
    });
  }

  // Create an order

  async create(orderData) {
    //   console.log("Order Data Received:", orderData);

    const session = await mongoose.startSession();
    session.startTransaction();
    console.log("Transaction Started");

    try {
      const childId = orderData.childId[0]; // Extract first child ID from array
      //  console.log(orderData);

      // NEW: Check for duplicate booking before processing

      const alreadyBooked = await this.orderRepo.checkChildAlreadyBooked(
        childId,
        orderData.classId
      );

      if (alreadyBooked) {
        throw new Error(
          "This child has already booked this class. Each child can only book a program once."
        );
      }

      // Check balance
      const available = await this.balanceUseCase.getBalance(orderData.userId);
      console.log("Available Balance:", available);
      if (!available || available.balance < orderData.amount) {
        console.log("Insufficient Balance");
        throw new Error("Not enough balance");
      }

      // Update balance
      await this.balanceUseCase.updateBalance(orderData.userId, {
        balance: available.balance - orderData.amount,
      });

      // Create order
      const order = await this.orderRepo.create(orderData, { session });
      console.log("Order Created:", order);

      // Create transaction record
      const TransactionModel = require("../models/transactionModel");
      const transaction = new TransactionModel({
        transactionId: uuidv4(),
        userId: orderData.userId,
        amount: orderData.amount,
        type: "purchase",
        status: "Completed",
        orderId: order._id, // Link to the order
      });

      await transaction.save({ session });
      console.log("Transaction record created:", transaction);

      // Update class with user ID and student ID
      const existingDate = await this.classDateRepo.getById(orderData.dateId);
      console.log(existingDate);
      if (!existingDate.students.includes(childId)) {
        existingDate.students.push(childId); // add it
        await existingDate.save(); // save the document
        console.log(`✅ Child ID ${childId} added to students.`);
      } else {
        console.log(`ℹ️ Child ID ${childId} is already in students.`);
      }
      console.log(existingDate.id);
      await this.classDateRepo.update(existingDate.id, {
        students: existingDate.students,
      });

      // Generate token for the order
      const token = this.tokenService.generate({ id: order.id, type: "order" });
      console.log("Token Generated:", token);

      // Create purchase history
      const purchaseHistory = {
        user: orderData.userId,
        order: order._id,
        classId: orderData.classId,
      };
      await this.purchasedHistoryRepo.create(purchaseHistory, { session });

      // Exp points
      ExpUpdateUseCase.execute(orderData.userId, 10);

      // Commit transaction
      await session.commitTransaction();
      console.log("Transaction Committed");

      // Create Order Sessions after commit
      await this.createSessionsFromOrder(order);
      console.log("Order Sessions Created");

      return { token, order };
    } catch (error) {
      console.error("Transaction Error:", error);
      if (session.inTransaction()) {
        await session.abortTransaction();
        console.error("Transaction Aborted");
      }
      throw new Error(`Failed to create order: ${error.message}`);
    } finally {
      session.endSession();
    }
  }
  async createSessionsFromOrder(order) {
    // Fetch full class data
    const classData = await this.classUseCase.getById(order.classId);

    if (
      !classData ||
      !Array.isArray(classData.dates) ||
      classData.dates.length === 0
    ) {
      console.error("No class dates found for classId:", order.classId);
      return;
    }

    const baseDate = classData.dates[0];

    const sessions = order.date.map((date) => ({
      orderId: order._id,
      classId: order.classId,
      userId: order.userId,
      childId: order.childId,
      date: new Date(date),
      startTime: baseDate.startTime,
      endTime: baseDate.endTime,
      durationMinutes: parseInt(baseDate.durationMinutes),
      repeat: baseDate.repeat,
      weekDay: baseDate.weekDay,
    }));

    try {
      await this.orderSessionRepo.insertMany(sessions);
      console.log("Sessions created successfully!");
    } catch (error) {
      console.error("Error inserting sessions:", error);
    }
  }

  // Get order by ID
  async getById(orderId) {
    return await this.orderRepo.getById(orderId);
  }

  // Get all orders with optional pagination
  async getAll(skip, limit, paid) {
    const filter = paid !== undefined ? { paid: Boolean(paid) } : {}; // Apply `paid` filter if provided
    return await this.orderRepo.getAll(skip, limit, filter); // Pass the filter to the repository
  }

  // Update an order by ID
  async update(orderId, orderData) {
    const existingOrder = await this.orderRepo.getById(orderId);
    if (!existingOrder) {
      throw new Error("Order not found");
    }
    const updateData = {
      ...existingOrder.toObject(),
      ...orderData,
    };
    return await this.orderRepo.update(orderId, updateData);
  }

  // Delete an order by ID
  async delete(orderId) {
    return await this.orderRepo.delete(orderId);
  }

  // Get all orders by user ID
  async getByUser(userId, page, limit) {
    try {
      console.log('here')
      // Fetch orders from the repository
      const orders = await this.orderRepo.getByUser(userId);

      // Handle case where no orders are found
      if (!orders || orders.length === 0) {
        return [];
      }

      // Get current date dynamically (midnight of current day)
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to start of day for consistent comparison

      // Transform orders: create an entry for each date, include today and future dates
      const transformedOrders = orders.flatMap(
        (order) =>
          order.date
            .map((date) => {
              const parsedDate = new Date(date);
              // Include dates from today onwards (changed from 'after today')
              if (parsedDate < today) {
                return null;
              }
              return {
                date: parsedDate,
                order: order,
                class: order.classId,
                child: order.childId,
              };
            })
            .filter((item) => item !== null) // Remove null entries
      );

      // Sort by date in ascending order (oldest to newest)
      transformedOrders.sort((a, b) => a.date - b.date);

      // Format dates as ISO strings for output
      return transformedOrders.map((order) => ({
        ...order,
        date: order.date.toISOString(),
      }));
    } catch (error) {
      throw new Error(
        `Failed to fetch orders for user ${userId}: ${error.message}`
      );
    }
  }

  // NEW METHOD: Get eligible children for a class (children who haven't booked it yet)
  async getEligibleChildren(userId, classId) {
    try {
      // First, get all children for the user
      const ChildModel = require("../models/childModel");
      const allChildren = await ChildModel.find({ parent: userId }).lean();

      if (!allChildren || allChildren.length === 0) {
        return {
          eligibleChildren: [],
          bookedChildren: [],
          totalChildren: 0,
          eligibleCount: 0,
          bookedCount: 0,
          message: "No children found for this parent",
        };
      }

      // Use the repository method to get eligibility info
      const eligibilityInfo = await this.orderRepo.getEligibleChildrenForClass(
        userId,
        classId,
        allChildren
      );

      return {
        ...eligibilityInfo,
        message:
          eligibilityInfo.eligibleCount === 0
            ? "All children have already booked this program"
            : `${eligibilityInfo.eligibleCount} children available to book this program`,
      };
    } catch (error) {
      console.error("Error in OrderUseCase.getEligibleChildren:", error);
      throw new Error(`Failed to get eligible children: ${error.message}`);
    }
  }

  // NEW METHOD: Check if a specific child can book a class
  async canChildBookClass(childId, classId) {
    try {
      const alreadyBooked = await this.orderRepo.checkChildAlreadyBooked(
        childId,
        classId
      );
      return {
        canBook: !alreadyBooked,
        alreadyBooked: alreadyBooked,
        message: alreadyBooked
          ? "This child has already booked this program"
          : "This child can book this program",
      };
    } catch (error) {
      console.error("Error checking if child can book class:", error);
      throw new Error(`Failed to check booking eligibility: ${error.message}`);
    }
  }
}

module.exports = OrderUseCase;
