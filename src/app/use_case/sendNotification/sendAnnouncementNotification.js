const NotificationService = require("../../services/notificationService");
const UserRepo = require("../../repo/userRepo");

class SendAnnouncementNotification {
  constructor() {
    this.userRepo = new UserRepo();
  }
  async execute(announcement, parentIds) {
    console.log("Sending announcement notification...");
    console.log("Announcement:", announcement);
    console.log("Parent IDs:", parentIds);
    // Use for...of loop to handle async/await correctly
    for (const parentId of parentIds) {
      
      const data = {
        announcementId: String(announcement._id || ""),
        classId: String((announcement.classId && (announcement.classId._id || announcement.classId)) || ""),
        slotId: String((announcement.slotId && (announcement.slotId._id || announcement.slotId)) || ""),
        url: "/announcement",
        title: String(`Announcement for ${announcement.title}`),
        mainImage: String((announcement.mainImage && (announcement.mainImage.url || announcement.mainImage)) || ""),
        senderName: String(announcement.senderName || ""),
        senderImage: String((announcement.senderImage && (announcement.senderImage.url || announcement.senderImage)) || ""),
      };
      const message = {
        title: `Announcement of ${announcement.title}`,
        body: `You have a new announcement from ${announcement.senderName}`,
         data,
        userId: parentId,
      };
      await NotificationService.sendNotification(parentId, message);
      console.log(
        `Notification sent to parent: ${parentId}`
      );
    }
    return true;
  }
}

module.exports = SendAnnouncementNotification;
