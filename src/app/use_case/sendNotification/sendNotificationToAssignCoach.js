const NotificationService = require("../../services/notificationService");

class SendNotificationToAssignCoach {
  constructor({ coachRepository, centerRepository, requestRepository }) {
    this.coachRepository = coachRepository;
    this.centerRepository = centerRepository;
    this.requestRepository = requestRepository;
  }

  async execute(centerId, coachId, status) {
    const session = await this.centerRepository.startSession();

    try {
      console.log("Starting notification process...");

      session.startTransaction();

      const coach = await this.coachRepository.findById(coachId, session);
      const center = await this.centerRepository.findById(centerId, session);

      if (!coach || !center) {
        throw new Error("Coach or center not found");
      }
      let existingRequest = null;
      if (status == "manager") {
        existingRequest =
          await this.requestRepository.findRequestByCenterForManager(
            centerId,
            coachId
          );
      } else if (status == "coach") {
        existingRequest =
          await this.requestRepository.findRequestByCenterForCoach(
            centerId,
            coachId
          );
      }
      if (existingRequest) {
        throw new Error("Request already exists for this coach and center");
      }

      // Prepare notification
      const notificationData = this._buildNotificationData(center, status);
      const message = this._buildNotificationMessage(
        coach.id.toString(),
        center.displayName,
        notificationData,
        status
      );

      // Send notification (outside transaction)
      await NotificationService.sendNotification(coach.id, message);

      // Create the request only after notification is successful
      await this.requestRepository.create({
        centerId: center.id,
        coachId: coach.id,
        status: "pending",
        sender: center.id,
        message: status === "manager" ? "manager request" : "coach request",
      });
      // Commit transaction early if data checks are valid
      await session.commitTransaction();
      await session.endSession();
      return true;
    } catch (error) {
      await session.abortTransaction();
      await session.endSession();
      console.error(
        "Failed to assign coach and send notification:",
        error.message
      );
      throw error;
    }
  }

  _buildNotificationData(center, status) {
    return {
      centerId: center.id.toString(),
      mainImage: center.mainImage?.url || "",
      displayName: center.displayName,
      address: `${center.address.city}, ${center.address.region}`,
      url: "/assign",
      status,
    };
  }

  _buildNotificationMessage(userId, centerName, data, status) {
    const type = status === "manager" ? "manager" : "coach";
    return {
      title: `You have been assigned as a ${type}`,
      body: `You have been assigned as a ${type} for ${centerName}`,
      data,
      userId,
    };
  }
}

module.exports = SendNotificationToAssignCoach;
