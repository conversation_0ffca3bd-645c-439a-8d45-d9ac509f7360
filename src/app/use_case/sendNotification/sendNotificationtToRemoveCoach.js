const NotificationService = require("../../services/notificationService");

class SendNotificationToRemoveCoach {
  constructor({ coachRepository, centerRepository }) {
    this.coachRepository = coachRepository;
    this.centerRepository = centerRepository;
  }

  async execute(centerId, coachId, status) {
    const session = await this.centerRepository.startSession();

    try {
      session.startTransaction();

      // Fetch coach and center details within the transaction
      const coach = await this.coachRepository.findById(coachId, session);
      const center = await this.centerRepository.findById(centerId, session);

      if (!coach || !center) {
        throw new Error("Coach or center not found");
      }
      console.log(center.coachs);
      center.coachs.remove(coachId);
      console.log(center.coachs);
      await this.centerRepository.update(centerId, center);
      coach.center = null;
      await this.coachRepository.update(coachId, coach);
      //   // Prepare notification data
      const notificationData = this._buildNotificationData(center, status);
      const message = this._buildNotificationMessage(
        coach.id,
        center.displayName,
        status,
        notificationData
      );

      // Commit the transaction before sending the notification
      await session.commitTransaction();

      // Send notification
      await NotificationService.sendNotification(coach.baseUser, message);

      return true;
    } catch (error) {
      await session.abortTransaction();
      console.error("Failed to send notification:", error.message);
      throw error;
    } finally {
      await session.endSession(); // Ensure session is always ended
    }
  }

  _buildNotificationData(center, status) {
    return {
      centerId: String(center.id),
      mainImage: String(center.mainImage?.url || ""),
      displayName: String(center.displayName),
      address: `${center.address.city}, ${center.address.region}`,
      url: "/remove",
      status,
    };
  }

  _buildNotificationMessage(userId, centerName, status, data) {
    return {
      title: `You have been removed as a ${status}`,
      body: `You have been removed as a coach for ${centerName}`,
      data,
      userId,
    };
  }
}

module.exports = SendNotificationToRemoveCoach;
