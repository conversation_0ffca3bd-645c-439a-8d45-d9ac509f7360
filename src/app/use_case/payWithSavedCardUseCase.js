class PayWithSavedCardUseCase {
  constructor(paymentService) {
    this.paymentService = paymentService;
  }

  async execute(userId, amount, paymentMethodId) {
    try {
      console.log(`Processing payment with saved card for user ${userId}, amount: ${amount}`);
      
      // Process the payment using the saved card
      const result = await this.paymentService.payWithSavedCard(
        userId,
        amount,
        paymentMethodId
      );
      
      console.log("Payment with saved card successful:", result.paymentIntentId);
      
      return {
        success: true,
        paymentIntentId: result.paymentIntentId,
        status: result.status,
        client_secret: result.client_secret
      };
    } catch (error) {
      console.error("Payment with saved card failed:", error.message);
      throw new Error(error.message);
    }
  }
}

module.exports = PayWithSavedCardUseCase; 