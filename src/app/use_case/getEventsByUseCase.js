// class GetEventsByCenterId {
//   constructor(eventRepository) {
//     this.eventRepository = eventRepository;
//   }

//   async execute(centerId) {
//     return await this.eventRepository.getEventsByCenterId(centerId);
//   }
// }

// class GetEventsByClassId {
//   constructor(eventRepository) {
//     this.eventRepository = eventRepository;
//   }

//   async execute(classId) {
//     return await this.eventRepository.getEventsByClassId(classId);
//   }
// }

// class GetEventById {
//   constructor(eventRepository) {
//     this.eventRepository = eventRepository;
//   }

//   async execute(eventId) {
//     return await this.eventRepository.getEventById(eventId);
//   }
// }

// class GetEventsByCenterIdAndDate {
//   constructor(eventRepository) {
//     this.eventRepository = eventRepository;
//   }
//   async execute(centerId, eventDate) {
//     return await this.eventRepository.getEventsByCenterIdAndDate(
//       centerId,
//       eventDate
//     );
//   }
// }

// module.exports = {
//   GetEventsByCenterId,
//   GetEventsByClassId,
//   GetEventById,
//   GetEventsByCenterIdAndDate,
// };
