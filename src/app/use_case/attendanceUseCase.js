const CryptoJS = require("crypto-js");

class AttendanceUseCase {
  constructor({ codeGenerator, qrCodeGenerator, attendanceRepo, secretKey }) {
    this.codeGenerator = codeGenerator;
    this.qrCodeGenerator = qrCodeGenerator;
    this.attendanceRepo = attendanceRepo;
    this.secretKey = secretKey;
  }

  async create(classId, studentId, classDate) {
    const existingAttendance = await this.attendanceRepo.getAttendanceRecord(
      classId,
      studentId,
      classDate
    );

    if (existingAttendance) {
      console.log("QR Code already exists for this student and class.");
      console.log("Existing data:", existingAttendance);
      
      // Check if existing QR code has proper encryption by trying to decrypt
      let hasValidQRCode = false;
      try {
        const decryptedData = CryptoJS.AES.decrypt(
          existingAttendance.qrCodeData.split(',').pop(), // Remove data:image/png;base64, prefix if present
          this.secretKey
        ).toString(CryptoJS.enc.Utf8);
        const qrData = JSON.parse(decryptedData);
        hasValidQRCode = qrData.classId && qrData.studentId && qrData.code;
      } catch (err) {
        console.log("Existing QR code needs regeneration with proper encryption");
        hasValidQRCode = false;
      }
      
      if (hasValidQRCode) {
        return {
          code: existingAttendance.code,
          qrCodeData: existingAttendance.qrCodeData,
        };
      }
      
      // Regenerate QR code with proper encryption
      const qrData = {
        classId,
        studentId,
        code: existingAttendance.code,
        timestamp: Date.now()
      };
      
      const encryptedData = CryptoJS.AES.encrypt(
        JSON.stringify(qrData),
        this.secretKey
      ).toString();
      
      const newQrCodeData = await this.qrCodeGenerator.generateQRCode(encryptedData);
      
      // Update the existing record with new QR code
      await this.attendanceRepo.updateQRCode(
        classId,
        studentId,
        classDate,
        newQrCodeData
      );
      
      return {
        code: existingAttendance.code,
        qrCodeData: newQrCodeData,
      };
    }
    const code = this.codeGenerator.generateCode();
    
    // Create data object for QR code encryption
    const qrData = {
      classId,
      studentId,
      code,
      timestamp: Date.now()
    };
    
    // Encrypt the data for QR code
    const encryptedData = CryptoJS.AES.encrypt(
      JSON.stringify(qrData),
      this.secretKey
    ).toString();
    
    const qrCodeData = await this.qrCodeGenerator.generateQRCode(encryptedData);
    await this.attendanceRepo.saveCode(
      classId,
      studentId,
      classDate,
      code,
      qrCodeData
    );
    return { code, qrCodeData };
  }
  async getPresentAttendance(classId) {
    return await this.attendanceRepo.getPresentAttendance(classId);
  }
  async update(classId, studentId, status) {
    console.log("update");
    console.log(classId);
    console.log(studentId);
    console.log(status);
    return await this.attendanceRepo.updateAttendance(
      classId,
      studentId,
      status
    );
  }

  async verify(qrCodeData, classDate) {
    try {
      console.log("QR Code Data for Verification:", qrCodeData);
      
      // The QR scanner might return the encrypted string directly
      // or it might be scanning from a base64 image, so we need to handle both
      let encryptedString = qrCodeData;
      
      // If this looks like base64 image data, we can't process it here
      // The QR scanner should be reading the encrypted text content, not the image
      if (qrCodeData.startsWith('data:image/')) {
        console.error("Received image data instead of encrypted text content");
        return false;
      }
      
      const decryptedData = CryptoJS.AES.decrypt(
        encryptedString,
        this.secretKey
      ).toString(CryptoJS.enc.Utf8);
      
      if (!decryptedData) {
        console.error("Failed to decrypt QR code data");
        return false;
      }
      
      const { classId, studentId, code } = JSON.parse(decryptedData);
      const attendance = await this.attendanceRepo.getAttendanceRecord(
        classId,
        studentId,
        classDate
      );
      if (attendance && attendance.code === code) {
        console.log("Verification successful");
        return await this.update(classId, studentId, "present");
      }
      console.log("Verification failed: code mismatch or attendance not found");
      return false;
    } catch (err) {
      console.error("Decryption Error:", err);
      return false;
    }
  }

  async verifyByClassIdAndCode(classId, code, classDate) {
    try {
      console.log("JIO");
      const attendance = await this.attendanceRepo.getAttendanceRecordByClassId(
        classId,
        code,
        classDate
      );

      if (attendance && attendance.code === code) {
        return await this.attendanceRepo.updateAttendance(
          classId,
          attendance.studentId.toString(),
          classDate,
          "present"
        );
      }
      return false;
    } catch (err) {
      console.error("Error in verifyByClassIdAndCode method:", err);
      return false;
    }
  }
  async getHistoryBychildId(childId) {
    return await this.attendanceRepo.getHistoryBychildId(childId);
  }
}

module.exports = AttendanceUseCase;
