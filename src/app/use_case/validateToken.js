class ValidateToken {
  constructor({ tokenService, baseUserRepository, userRepository }) {
    this.tokenService = tokenService;
    this.baseUserRepository = baseUserRepository;
    this.userRepository = userRepository;
  }

  async execute(token) {
    try {
      // Validate the token and get the decoded payload
      const valid = this.tokenService.validate(token);

      if (!valid) {
        // Token is invalid or could not be parsed
        return false;
      }
      console.log(valid);
      // Fetch the user from the repository
      const user = await this.baseUserRepository.findById(valid.id);
      const parent = await this.userRepository.findById(valid.id);

      // Return true if the user exists, otherwise false
      return !!user || !!parent;
    } catch (error) {
      // Log or handle unexpected errors
      console.error("Error validating token:", error.message);
      // Consider whether you want to throw an error or return false
      return false;
    }
  }
}

module.exports = ValidateToken;
