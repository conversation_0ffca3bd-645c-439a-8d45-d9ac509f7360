const path = require("path");
const { v4: uuidv4 } = require("uuid");
class CenterUseCase {
  constructor({
    centerRepository,
    coachRepository,
    baseUserRepository,
    ownerRepository,
    tokenService,
    uploadFile,
    requestRepository,
    classRepository,
  }) {
    this.centerRepository = centerRepository;
    this.coachRepository = coachRepository;
    this.baseUserRepository = baseUserRepository;
    this.ownerRepository = ownerRepository;
    this.tokenService = tokenService;
    this.uploadFile = uploadFile;
    this.requestRepository = requestRepository;
    this.classRepository = classRepository;
  }
  async processUploadFields(centerData, baseUserId) {
    const fields = [
      "businessCertificate",
      "sexualConvictionRecord",
      "hkidCard",
      "mainImage",
      "images",
    ];

    for (const fieldName of fields) {
      if (centerData[fieldName] && Array.isArray(centerData[fieldName])) {
        console.log(
          `Processing multiple files for ${fieldName}:`,
          centerData[fieldName]
        );
        for (const file of centerData[fieldName]) {
          if (!file.path) {
            console.error(`Missing path for file in ${fieldName}:`, file);
            continue; // Skip files without path
          }
          await this.uploadFile.uploadFile(
            file,
            fieldName,
            centerData,
            baseUserId
          );
        }
      } else if (centerData[fieldName]) {
        console.log(
          `Processing single file for ${fieldName}:`,
          centerData[fieldName]
        );
        if (!centerData[fieldName].path) {
          throw new Error(`Missing path for single file in ${fieldName}`);
        }
        await this.uploadFile.uploadFile(
          centerData[fieldName],
          fieldName,
          centerData,
          baseUserId
        );
      }
    }
  }

  async create(centerData) {
    try {
      console.log("Creating center...");

      // Validate base user existence
      //console.log(centerData);
      let existingUser = await this.baseUserRepository.findById(
        centerData.baseUser
      );
      if (!existingUser) {
        throw new Error("Base user ID is required to create a center.");
      }

      // Validate owner existence
      existingUser = await this.ownerRepository.findById(centerData.owner);
      if (!existingUser) {
        throw new Error("Owner ID is required to create a center.");
      }

      // Check if this is an individual educator trying to create multiple branches
      if (centerData.isFreelanceEducator || existingUser.isIndividualCreator) {
        const existingBranches = await this.ownerRepository.findById(centerData.owner);
        if (existingBranches.length > 0) {
          throw new Error("Individual educators can only create one branch. You already have an existing center.");
        }
        // Mark the owner as individual creator if creating a freelance educator center
        if (centerData.isFreelanceEducator && !existingUser.isIndividualCreator) {
          existingUser.isIndividualCreator = true;
          await this.ownerRepository.update(existingUser.id, existingUser);
        }
      }

      // Validate center legal name
      let existingCenter = await this.centerRepository.findByLegalName(
        centerData.legalName
      );

      if (existingCenter.length > 0) {
        throw new Error("Center exists with this legal name.");
      }

      const baseUserId = centerData.baseUser;

      // Process upload fields dynamically
      await this.processUploadFields(centerData, baseUserId);
      //  console.log(centerData);
      // Simulate saving center to repository
      const center = await this.centerRepository.save(centerData);
      console.log("done");
      //  console.log(existingUser);

      // Update existing user's branches
      existingUser.branchs.push(center.id);
      await this.ownerRepository.update(existingUser.id, existingUser);

      // Generate token
      const token = this.tokenService.generate({
        id: center._id,
        type: "center",
      });

      return { token, center };
    } catch (error) {
      console.error("Error creating center:", error.message);
      throw error;
    }
  }

  async update(centerId, centerData) {
    try {
      console.log("use case");
      console.log(centerData);

      // Convert string boolean values to actual booleans
      if (centerData.promotion !== undefined) {
        const originalPromotion = centerData.promotion;
        centerData.promotion =
          centerData.promotion === "true" || centerData.promotion === true;
        console.log(
          `Promotion conversion: "${originalPromotion}" (${typeof originalPromotion}) -> ${
            centerData.promotion
          } (${typeof centerData.promotion})`
        );
      }
      if (centerData.sen !== undefined) {
        const originalSen = centerData.sen;
        centerData.sen = centerData.sen === "true" || centerData.sen === true;
        console.log(
          `SEN conversion: "${originalSen}" (${typeof originalSen}) -> ${
            centerData.sen
          } (${typeof centerData.sen})`
        );
      }

      // Check if center exists
      const existingCenter = await this.centerRepository.findById(centerId);
      if (!existingCenter) {
        throw new Error("There is no existing center by this ID");
      }

      // Process file uploads first (this mutates centerData in-place)
      await this.processUploadFields(centerData, existingCenter.baseUser);

      console.log("Updated centerData with uploaded file URLs:", centerData);

      // Update center data with the modified centerData object
      const updatedCenter = await this.centerRepository.update(
        centerId,
        centerData
      );

      console.log(updatedCenter);
      console.log("DONE");
      return updatedCenter;
    } catch (error) {
      throw new Error("Error updating center: " + error.message);
    }
  }

  async getAllCenters(promotionOnly = false) {
    try {
      // Get centers with optional promotion filtering
      let query = {};
      if (promotionOnly) {
        query = { promotion: true };
        console.log(
          "Filtering centers by promotion: true (Display on ClassZ enabled only)"
        );
      } else {
        console.log("Getting all centers (no promotion filtering)");
      }

      const allCenters = await this.centerRepository.findAll(query);
      console.log(`Found ${allCenters.length} centers after filtering`);

      // Calculate pricing for each center from their classes
      const centersWithPricing = await Promise.all(
        allCenters.map(async (center) => {
          try {
            const classes = await this.classRepository.getByCenter(center._id);

            let priceFrom = null;
            let priceTo = null;

            if (classes && classes.length > 0) {
              // Filter out SEN classes (they should be free)
              const nonSenClasses = classes.filter((c) => !c.sen);
              const prices = nonSenClasses
                .map((p) => p.charge || (p.classDate && p.classDate.charge))
                .filter((p) => p != null && p > 0);

              if (prices.length > 0) {
                priceFrom = Math.min(...prices);
                priceTo = Math.max(...prices);
              }
            }

            const centerObject = center.toObject ? center.toObject() : center;
            return {
              ...centerObject,
              priceFrom,
              priceTo,
            };
          } catch (classError) {
            // If there's an error fetching classes, just return center without pricing
            console.warn(
              `Error fetching classes for center ${center._id}:`,
              classError.message
            );
            const centerObject = center.toObject ? center.toObject() : center;
            return {
              ...centerObject,
              priceFrom: null,
              priceTo: null,
            };
          }
        })
      );

      return centersWithPricing;
    } catch (error) {
      console.error("Error fetching all centers:", error);
      throw new Error("Error fetching centers: " + error.message);
    }
  }

  async getCenterById(centerId) {
    try {
      const center = await this.centerRepository.findById(centerId);
      if (!center) {
        return null;
      }

      let priceFrom = null;
      let priceTo = null;

      try {
        const classes = await this.classRepository.getByCenter(centerId);

        if (classes && classes.length > 0) {
          // Filter out SEN classes (they should be free)
          const nonSenClasses = classes.filter((c) => !c.sen);
          const prices = nonSenClasses
            .map((p) => p.charge || (p.classDate && p.classDate.charge))
            .filter((p) => p != null && p > 0);

          if (prices.length > 0) {
            priceFrom = Math.min(...prices);
            priceTo = Math.max(...prices);
          }
        }
      } catch (error) {
        // Log the error but don't crash; just return the center without pricing.
        console.error(
          `Failed to get class pricing for center ${centerId}:`,
          error.message
        );
      }

      const centerObject = center.toObject ? center.toObject() : center;

      return {
        ...centerObject,
        priceFrom,
        priceTo,
      };
    } catch (error) {
      throw new Error("Error getting center by ID: " + error.message);
    }
  }
  async getCoachsByCenterId(centerId) {
    try {
      console.log("Fetching coaches for center ID:", centerId);
      return await this.centerRepository.getCoachsByCenterId(centerId);
    } catch (error) {
      throw new Error("Error getting coaches by center ID: " + error.message);
    }
  }
  async getManagersByCenterId(centerId) {
    try {
      console.log("Fetching coaches for center ID:", centerId);
      return await this.centerRepository.getManagersByCenterId(centerId);
    } catch (error) {
      throw new Error("Error getting coaches by center ID: " + error.message);
    }
  }

  async deleteCenterById(centerId) {
    console.log(`hi ${centerId}`);
    const session = await this.centerRepository.startSession(); // make sure your repository exposes this

    try {
      session.startTransaction();

      const existingCenter = await this.centerRepository.findById(centerId);
      if (!existingCenter) {
        throw new Error("There is no existing center by this ID");
      }

      // Find and update all coaches related to the center
      const coaches = await this.coachRepository.findByCenterId(centerId);
      if (coaches && coaches.length > 0) {
        for (const coach of coaches) {
          coach.center = null;
          await coach.save({ session });
        }
      }

      // Update manager if any
      const manager = await this.coachRepository.findManagerByCenterId(
        centerId
      );
      if (manager) {
        manager.center = null;
        await manager.save({ session });
      }

      // Delete the center
      await this.centerRepository.delete(centerId, session); // pass session into your delete method

      await session.commitTransaction();
      session.endSession();
      return {
        success: true,
        message: "Center and related data updated successfully",
      };
    } catch (error) {
      await session.abortTransaction();
      session.endSession();
      throw new Error("Error deleting center by ID: " + error.message);
    }
  }

  // New method for center verification
  async verifyCenterById(centerId, verified) {
    try {
      console.log(`Verifying center with ID: ${centerId}, status: ${verified}`);

      const existingCenter = await this.centerRepository.findById(centerId);
      if (!existingCenter) {
        throw new Error("Center not found");
      }

      const updateData = { verified };
      const updatedCenter = await this.centerRepository.update(
        centerId,
        updateData
      );

      console.log(
        `Center ${centerId} verification status updated to: ${verified}`
      );

      return updatedCenter;
    } catch (error) {
      console.error(`Error verifying center: ${error.message}`);
      throw new Error(`Error verifying center: ${error.message}`);
    }
  }
  async getRequestByType(type, id) {
    try {
      let request;
      if (type == "coach") {
        request = await this.requestRepository.getRequestByCenterForCoach(id);
      } else if (type == "manager") {
        request = await this.requestRepository.getRequestByCenterForManager(id);
      }

      // Return empty array if no requests found - this is a normal scenario
      if (!request) {
        return [];
      }

      return request;
    } catch (error) {
      // Only catch actual database/system errors, not "no results found"
      console.error("Error getting request by type:", error);
      throw new Error("Error getting request by type: " + error.message);
    }
  }
}

module.exports = CenterUseCase;
