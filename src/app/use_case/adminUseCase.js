const mongoose = require("mongoose");

class AdminUseCase {
  constructor({ 
    classRepo, 
    classDateRepo, 
    eventRepo,
    migrationStatusRepo 
  }) {
    this.classRepo = classRepo;
    this.classDateRepo = classDateRepo;
    this.eventRepo = eventRepo;
    this.migrationStatusRepo = migrationStatusRepo;
  }

  async migrateClassesToNewModel() {
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get all existing classes
      const classes = await this.classRepo.getAll();
      console.log(`Found ${classes.length} classes to migrate`);

      const migrationResults = {
        totalClasses: classes.length,
        processedClasses: 0,
        createdDates: 0,
        createdEvents: 0,
        errors: []
      };

      // Process each class
      for (const classData of classes) {
        try {
          if (!classData.dates || !Array.isArray(classData.dates)) {
            migrationResults.errors.push({
              classId: classData._id,
              error: "No dates array found"
            });
            continue;
          }

          // Create ClassDate documents for each date
          const classDatePromises = classData.dates.map(async (date) => {
            const classDateData = {
              classId: classData._id,
              date: date.date,
              startTime: date.startTime,
              endTime: date.endTime,
              durationMinutes: date.durationMinutes,
              weekDay: date.weekDay,
              repeat: date.repeat,
              students: date.students || [],
              status: "scheduled"
            };

            const classDate = await this.classDateRepo.createDate(classDateData, { session });
            migrationResults.createdDates++;

            // Create corresponding event
            await this.eventRepo.create({
              classId: classData._id,
              dateId: classDate._id
            }, { session });
            migrationResults.createdEvents++;

            return classDate;
          });

          await Promise.all(classDatePromises);
          migrationResults.processedClasses++;

        } catch (error) {
          migrationResults.errors.push({
            classId: classData._id,
            error: error.message
          });
        }
      }

      // Save migration status
      await this.migrationStatusRepo.create({
        type: 'class_date_migration',
        status: 'completed',
        details: migrationResults
      }, { session });

      await session.commitTransaction();
      console.log("Migration completed successfully");
      return migrationResults;

    } catch (error) {
      await session.abortTransaction();
      throw new Error(`Migration failed: ${error.message}`);
    } finally {
      session.endSession();
    }
  }

  async getMigrationStatus() {
    return await this.migrationStatusRepo.getLatestByType('class_date_migration');
  }
}

module.exports = AdminUseCase; 