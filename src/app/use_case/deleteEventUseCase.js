// usecases/DeleteEvents.js
class DeleteEventById {
  constructor(eventRepository) {
    this.eventRepository = eventRepository;
  }

  async execute(eventId) {
    // Attempt to delete the event by eventId
    return await this.eventRepository.deleteEventById(eventId);
  }
}

class DeleteEventsByClassId {
  constructor(eventRepository) {
    this.eventRepository = eventRepository;
  }

  async execute(classId) {
    // Delete events by classId
    return await this.eventRepository.deleteManyByClassId(classId);
  }
}

class DeleteEventsByCenterId {
  constructor(eventRepository) {
    this.eventRepository = eventRepository;
  }

  async execute(centerId) {
    // Delete events by centerId
    return await this.eventRepository.deleteManyByCenterId(centerId);
  }
}

// Export all classes from a single file
module.exports = {
  DeleteEventById,
  DeleteEventsByClassId,
  DeleteEventsByCenterId,
};
