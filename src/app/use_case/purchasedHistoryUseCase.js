class PurchaseHistoryUseCase {
  constructor(purchasedHistoryRepo, userRepo) {
    this.purchasedHistoryRepo = purchasedHistoryRepo;
    this.userRepo = userRepo;
  }
  async create() {}
  async get(userId) {
    try {
      const userExists = await this.userRepo.exists(userId);
      if (!userExists) {
        throw new Error("No user found");
      }
      return await this.purchasedHistoryRepo.get(userId);
    } catch (error) {
      throw new Error(`Failed to find purchase history: ${error.message}`);
    }
  }
}

module.exports = PurchaseHistoryUseCase;
