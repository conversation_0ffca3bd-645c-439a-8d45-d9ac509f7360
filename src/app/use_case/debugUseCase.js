const mongoose = require('mongoose');

class DebugUseCase {
  constructor({ Cha<PERSON>, User, Center, Coach, BaseUser }) {
    this.Chat = Chat;
    this.User = User;
    this.Center = Center;
    this.Coach = Coach;
    this.BaseUser = BaseUser;
  }

  async getDbStatus() {
    const dbState = mongoose.connection.readyState;
    let status;
    
    switch (dbState) {
      case 0: status = 'Disconnected'; break;
      case 1: status = 'Connected'; break;
      case 2: status = 'Connecting'; break;
      case 3: status = 'Disconnecting'; break;
      default: status = 'Unknown';
    }
    
    return {
      status,
      dbState,
      connectionString: mongoose.connection.host,
      databaseName: mongoose.connection.name
    };
  }

  async getChatStats() {
    const chatCount = await this.Chat.countDocuments();
    const userCount = await this.User.countDocuments();
    const centerCount = await this.Center.countDocuments();
    const coachCount = await this.Coach.countDocuments();
    
    const recentChats = await this.Chat.find().sort({ timestamp: -1 }).limit(5);
    
    return {
      collections: {
        chats: chatCount,
        users: userCount,
        centers: centerCount,
        coaches: coachCount
      },
      recentChats: recentChats.map(chat => ({
        id: chat._id,
        sender: chat.sender,
        recipient: chat.recipient,
        senderModel: chat.senderModel,
        recipientModel: chat.recipientModel,
        timestamp: chat.timestamp,
        status: chat.status || 'unknown'
      }))
    };
  }

  async getUserChats(userId) {
    const isValidObjectId = mongoose.isValidObjectId(userId);
    
    if (!isValidObjectId) {
      return {
        message: "The provided userId is not a valid MongoDB ObjectId",
        userExists: false,
        isValidId: false,
        chatSummary: {
          sentCount: 0,
          receivedCount: 0,
          totalPartners: 0
        },
        recentSent: [],
        recentReceived: [],
        chatPartners: []
      };
    }
    
    let userExists = false;
    let userType = null;
    
    const user = await this.User.findById(userId);
    if (user) {
      userExists = true;
      userType = 'user';
    } else {
      const center = await this.Center.findById(userId);
      if (center) {
        userExists = true;
        userType = 'center';
      } else {
        const coach = await this.Coach.findById(userId);
        if (coach) {
          userExists = true;
          userType = 'coach';
        }
      }
    }
    
    if (!userExists) {
      return {
        message: "No user found with this ID in any collection",
        userExists: false,
        isValidId: true,
        chatSummary: {
          sentCount: 0,
          receivedCount: 0,
          totalPartners: 0
        },
        recentSent: [],
        recentReceived: [],
        chatPartners: []
      };
    }
    
    const sentChats = await this.Chat.find({ sender: userId }).sort({ timestamp: -1 }).limit(10);
    
    const receivedChats = await this.Chat.find({ recipient: userId }).sort({ timestamp: -1 }).limit(10);
    
    let chatPartners = [];
    try {
      const objectId = new mongoose.Types.ObjectId(userId);
      chatPartners = await this.Chat.aggregate([
        {
          $match: {
            $or: [
              { sender: objectId },
              { recipient: objectId }
            ]
          }
        },
        {
          $group: {
            _id: {
              $cond: [
                { $eq: ["$sender", objectId] },
                "$recipient",
                "$sender"
              ]
            },
            count: { $sum: 1 },
            lastMessage: { $max: "$timestamp" }
          }
        },
        { $sort: { lastMessage: -1 } },
        { $limit: 10 }
      ]);
    } catch (error) {
      console.error("Error in chat partners aggregation:", error);
      chatPartners = [];
    }
    
    return {
      userExists,
      userType,
      isValidId: true,
      chatSummary: {
        sentCount: await this.Chat.countDocuments({ sender: userId }),
        receivedCount: await this.Chat.countDocuments({ recipient: userId }),
        totalPartners: chatPartners.length
      },
      recentSent: sentChats.map(chat => ({
        id: chat._id,
        to: chat.recipient,
        recipientModel: chat.recipientModel,
        message: chat.message.substring(0, 30) + (chat.message.length > 30 ? '...' : ''),
        timestamp: chat.timestamp,
        status: chat.status || 'unknown'
      })),
      recentReceived: receivedChats.map(chat => ({
        id: chat._id,
        from: chat.sender,
        senderModel: chat.senderModel,
        message: chat.message.substring(0, 30) + (chat.message.length > 30 ? '...' : ''),
        timestamp: chat.timestamp,
        status: chat.status || 'unknown'
      })),
      chatPartners: chatPartners
    };
  }

  async getAllUsersByRole(role) {
    let users = [];
    switch(role) {
      case 'parent':
        users = await this.User.find({});
        break;
      case 'owner':
        users = await this.BusinessOwner.find({});
        break;
      case 'coach':
        users = await this.Coach.find({});
        break;
      default:
        throw new Error("Invalid role specified");
    }
    return users.map(user => ({ id: user._id, email: user.email, classzId: user.classzId }));
  }

  async clearCollection(modelName) {
    let Model;
    switch(modelName) {
      case 'Chat': Model = this.Chat; break;
      case 'User': Model = this.User; break;
      case 'Center': Model = this.Center; break;
      case 'Coach': Model = this.Coach; break;
      case 'BaseUser': Model = this.BaseUser; break;
      default:
        throw new Error(`Invalid model name: ${modelName}`);
    }
    const result = await Model.deleteMany({});
    return { message: `Cleared ${result.deletedCount} documents from ${modelName} collection.` };
  }

  async removeUserFromCollection(userId, modelName) {
    let Model;
    switch(modelName) {
      case 'User': Model = this.User; break;
      case 'Center': Model = this.Center; break;
      case 'Coach': Model = this.Coach; break;
      case 'BaseUser': Model = this.BaseUser; break;
      default:
        throw new Error(`Invalid model name: ${modelName}`);
    }
    const result = await Model.findByIdAndDelete(userId);
    if (!result) {
      throw new Error(`${modelName} with ID ${userId} not found.`);
    }
    return { message: `Removed user ${userId} from ${modelName} collection.` };
  }

  async removeChatMessages(senderId, recipientId) {
    const result = await this.Chat.deleteMany({ 
      $or: [
        { sender: senderId, recipient: recipientId },
        { sender: recipientId, recipient: senderId }
      ]
    });
    return { message: `Removed ${result.deletedCount} chat messages between ${senderId} and ${recipientId}.` };
  }

  async getPendingRequests() {
    const Request = require('../models/requestModel'); // Directly import if not provided via constructor for now
    const requests = await Request.find({ status: 'pending' });
    return requests.map(req => ({
      id: req._id,
      coachId: req.coachId,
      centerId: req.centerId,
      status: req.status
    }));
  }

  async getUnverifiedUsers() {
    const BaseUser = require('../models/baseUserModel');
    const users = await BaseUser.find({ isVerified: false });
    return users.map(user => ({ id: user._id, email: user.email, roles: user.roles }));
  }

  async getEventsByDateRange(startDate, endDate) {
    const Event = require('../models/eventModel');
    const events = await Event.find({
      date: { $gte: new Date(startDate), $lte: new Date(endDate) }
    });
    return events;
  }

  async getClassesByDateRange(startDate, endDate) {
    const Class = require('../models/classModel');
    const classes = await Class.find({
      startDate: { $lte: new Date(endDate) },
      endDate: { $gte: new Date(startDate) }
    });
    return classes;
  }

  async getOrdersByDateRange(startDate, endDate) {
    const Order = require('../models/orderModel');
    const orders = await Order.find({
      orderDate: { $gte: new Date(startDate), $lte: new Date(endDate) }
    });
    return orders;
  }
}

module.exports = DebugUseCase; 