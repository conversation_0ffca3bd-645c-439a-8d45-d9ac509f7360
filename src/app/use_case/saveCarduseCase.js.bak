class SaveCardUseCase {
  constructor(userRepository, paymentService) {
    this.userRepository = userRepository;
    this.paymentService = paymentService;
  }
  async execute(userId, cardToken) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new Error("User Not found");
      }
      let customer;
      if (!user.stripeCustomerId) {
        console.log("JI");
        const customer = await this.paymentService.createCustomer(
          user.email,
          cardToken
        );
        console.log("JI");
        console.log(customer);
        user.stripeCustomerId = customer.id;
        await this.userRepository.update(userId, {
          stripeCustomerId: customer.id,
        });
      } else {
        console.log("hi");
        customer = await this.paymentService.addCardToCustomer(
          userId,
          cardToken
        );
      }
      return customer;
    } catch (error) {
      throw new Error("could not execute save card");
    }
  }
}

module.exports = SaveCardUseCase;
