class DeleteCardUseCase {
  constructor(userRepository, paymentService) {
    this.userRepository = userRepository;
    this.paymentService = paymentService;
  }

  async execute(userId) {
    try {
      // Get user data to find the customer ID
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new Error("User not found");
      }

      // Delete the saved payment method from Strip<PERSON>
      if (user.stripeCustomerId) {
        await this.paymentService.deleteCustomerPaymentMethods(user.stripeCustomerId);
      }

      // Update user record to remove card information
      await this.userRepository.update(userId, {
        stripeCustomerId: null,
        cardLast4: null,
        cardBrand: null,
        cardExpiryMonth: null,
        cardExpiryYear: null,
        hasCard: false
      });

      return { success: true, message: "Card deleted successfully" };
    } catch (error) {
      console.error("Error deleting card:", error);
      throw error;
    }
  }
}

module.exports = DeleteCardUseCase; 