class CancelSubscriptionUseCase {
  constructor(subscriptionRepo) {
    this.subscriptionRepo = subscriptionRepo;
  }
  async execute(userId) {
    try {
      // Find the subscription first to verify it exists
      const existingSubscription = await this.subscriptionRepo.getSubscriptionByUserId(userId);
      if (!existingSubscription) {
        throw new Error("No active subscription found for this user");
      }
      
      // Cancel the subscription
      const result = await this.subscriptionRepo.cancelSubscriptionByUserId(userId);
      if (result) {
        return { success: true, message: "Subscription cancelled successfully" };
      } else {
        throw new Error("Failed to cancel subscription");
      }
    } catch (error) {
      throw error;
    }
  }
}

module.exports = CancelSubscriptionUseCase; 