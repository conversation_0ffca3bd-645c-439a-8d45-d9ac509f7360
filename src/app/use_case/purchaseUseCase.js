class PurchaseUseCase {
  constructor(paymentService) {
    this.paymentService = paymentService;
  }
  async execute(userId, amount, cardToken) {
    try {
      // Process the payment with our updated method that handles confirmation
      const { paymentIntent, transactionId, client_secret } =
        await this.paymentService.createZCoinPaymentIntent(
          userId,
          amount,
          cardToken
        );
      console.log("Payment successful:", transactionId);

      // Return the already confirmed payment intent
      return {
        charge: paymentIntent,
        transaction: transactionId,
        client_secret: client_secret,
      };
    } catch (error) {
      throw new Error(error.message);
    }
  }
}

module.exports = PurchaseUseCase;
