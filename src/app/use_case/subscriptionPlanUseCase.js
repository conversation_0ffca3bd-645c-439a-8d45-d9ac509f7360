class SubcriptionPlanUseCase {
  constructor(subscriptionPlanRepo) {
    this.subscriptionPlanRepo = subscriptionPlanRepo;
  }
  async getAllPlans() {
    return await this.subscriptionPlanRepo.getAllPlans();
  }
  async createNewPlan(planData) {
    return await this.subscriptionPlanRepo.createNewPlan(planData);
  }
  async deletePlan(planId) {
    return await this.subscriptionPlanRepo.deletePlan(planId);
  }
  async updatePlan(planId, planData) {
    try {
      const plan = await this.subscriptionPlanRepo.findPlanById(planId);
      if (!plan) throw new Error("No plan found");
      return this.subscriptionPlanRepo.updatePlan(planId, planData);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = SubcriptionPlanUseCase;
