class RemoveCoachFromCenter {
  constructor({ centerRepository, coachRepository, classRepo }) {
    this.centerRepository = centerRepository;
    this.coachRepository = coachRepository;
    this.classRepo = classRepo;
  }

  async execute(centerId, coachId, type) {
    const session = await this.centerRepository.startSession();
    session.startTransaction();

    try {
      // Fetch the center and coach by their IDs within the session
      let center = await this.centerRepository.findById(centerId, session);
      let coach = await this.coachRepository.findById(coachId, session);

      // Check if center and coach exist
      if (!center) {
        throw new Error("Center not found");
      }
      if (!coach) {
        throw new Error("Coach not found");
      }

      // Check if coach is assigned to the center
      if (!center.coachs || !center.coachs.includes(coachId)) {
        throw new Error("Coach not assigned to this center");
      }

      // Remove the coach from the center's coach list
      center.coachs = center.coachs.filter(
        (coach) => coach.toString() !== coachId.toString()
      );
      coach.center = null;

      // Update the center and the coach
      const [_, newCoach] = await Promise.all([
        this.centerRepository.update(centerId, center, session),
        this.coachRepository.update(coachId, coach, session),
      ]);

      // Remove coach from all classes in this center
      try {
        await this.classRepo.updateMany(
          { 
            center: centerId,
            coach: coachId 
          },
          { 
            $unset: { coach: "" }
          },
          { session }
        );
      } catch (error) {
        console.error("❌ Error removing coach from classes:", error);
        throw new Error("Failed to remove coach from classes: " + error.message);
      }

      let data = { coach: newCoach };
      console.log(data);
      await session.commitTransaction();
      await session.endSession();
      return data;
    } catch (error) {
      // Abort the transaction if any error occurs
      await session.abortTransaction();
      session.endSession();

      // Log and throw the error
      console.error("Error removing coach from center:", error.message);
      throw new Error(`Failed to remove coach from center: ${error.message}`);
    }
  }
}

module.exports = RemoveCoachFromCenter;
