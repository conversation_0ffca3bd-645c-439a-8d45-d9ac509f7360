/**
 * Test script for the filter API endpoints
 * 
 * Run with: node src/utils/testFilterAPI.js
 */

const axios = require('axios');
const dotenv = require('dotenv');
dotenv.config();

// Base URL for API requests
const API_URL = process.env.API_URL || 'http://localhost:3000/api';

// Test cases
const tests = [
  {
    name: 'Filter centers by price range',
    endpoint: '/search/centers/filter',
    params: { minPrice: 15, maxPrice: 30, sortBy: 'price' }
  },
  {
    name: 'Filter centers by age range',
    endpoint: '/search/centers/filter',
    params: { minAge: 3, maxAge: 6 }
  },
  {
    name: 'Filter centers by location',
    endpoint: '/search/centers/filter',
    params: { location: 'Central' }
  },
  {
    name: 'Filter centers with SEN services',
    endpoint: '/search/centers/filter',
    params: { senService: true }
  },
  {
    name: 'Filter centers by category',
    endpoint: '/search/centers/filter',
    params: { category: 'Music' }
  },
  {
    name: 'Filter centers by rating',
    endpoint: '/search/centers/filter',
    params: { rating: 4, sortBy: 'rating' }
  },
  {
    name: 'Find nearby centers',
    endpoint: '/search/centers/nearby',
    params: { longitude: 114.1694, latitude: 22.3193, maxDistance: 5000 }
  },
  {
    name: 'Filter classes by price and category',
    endpoint: '/search/classes/filter',
    params: { minPrice: 20, maxPrice: 40, category: 'Sports' }
  },
  {
    name: 'General search with filters',
    endpoint: '/search',
    params: { q: 'music', minPrice: 20, maxPrice: 50, sortBy: 'rating' }
  }
];

/**
 * Run all test cases
 */
async function runTests() {
  console.log('Running filter API tests...\n');
  
  for (const test of tests) {
    await runTest(test);
  }
  
  console.log('\nAll tests completed.');
}

/**
 * Run a single test case
 */
async function runTest(test) {
  console.log(`Testing: ${test.name}`);
  console.log(`Endpoint: ${test.endpoint}`);
  console.log(`Parameters: ${JSON.stringify(test.params)}`);
  
  try {
    // Build URL with query parameters
    const url = `${API_URL}${test.endpoint}`;
    
    // Make the request
    const response = await axios.get(url, { params: test.params });
    
    // Log results
    console.log(`Status: ${response.status}`);
    
    if (response.data.success) {
      const totalCount = response.data.pagination?.total || 
                        (response.data.results ? 
                          Object.values(response.data.results)
                            .reduce((sum, arr) => sum + arr.length, 0) : 0);
      
      console.log(`Results: ${totalCount} items found`);
      
      // Log sample results
      if (response.data.data) {
        logSampleResults(response.data.data);
      } else if (response.data.results) {
        for (const [key, value] of Object.entries(response.data.results)) {
          console.log(`- ${key}: ${value.length} items`);
        }
      }
    } else {
      console.log(`Error: ${response.data.message}`);
    }
  } catch (error) {
    console.error(`Test failed: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Error data: ${JSON.stringify(error.response.data)}`);
    }
  }
  
  console.log('-------------------');
}

/**
 * Log a sample of results
 */
function logSampleResults(data, limit = 3) {
  if (!Array.isArray(data) || data.length === 0) {
    console.log('No results to display');
    return;
  }
  
  const sample = data.slice(0, limit);
  
  sample.forEach((item, index) => {
    const name = item.displayName || item.legalName || item.classProviding || 'Unnamed';
    const price = item.price || (item.classDate && item.classDate.charge) || item.charge || 'Unknown';
    console.log(`- [${index + 1}] ${name} (Price: ${price})`);
  });
  
  if (data.length > limit) {
    console.log(`... and ${data.length - limit} more results`);
  }
}

// Run the tests
runTests(); 