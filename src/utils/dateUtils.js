// Add minutes to current time
function addMinutes(minutes) {
  console.log(`Adding ${minutes} minutes to current ${Date.now()}.`);
  return Date.now() + minutes * 60 * 1000;
}

// Check if token is expired
function isExpired(expires) {
  return Date.now() > expires;
}

// Get safe timestamp (prevents future timestamps)
function getSafeTimestamp() {
  const now = Date.now();
  // If system clock is incorrectly set to future, this will still work
  // as we compare to a reference date (Jan 1, 2024)
  const referenceDate = new Date().getTime();
  const oneYearMs = 365 * 24 * 60 * 60 * 1000;

  if (now > referenceDate + oneYearMs) {
    console.warn(
      `Detected possibly incorrect future timestamp: ${now}. Using adjusted time.`
    );
    return referenceDate + (now % oneYearMs);
  }

  return now;
}

// Validate a timestamp to ensure it's not unreasonably in the future
function validateTimestamp(timestamp) {
  const now = Date.now();
  console.log(
    `Validating timestamp: ${timestamp} against current time: ${now}`
  );

  // Allow up to 1 minute in the future to account for slight clock differences
  return timestamp >= now + 60000;
}

module.exports = {
  addMinutes,
  isExpired,
  getSafeTimestamp,
  validateTimestamp,
};
