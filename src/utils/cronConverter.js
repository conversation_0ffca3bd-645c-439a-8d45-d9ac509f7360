// cronUtils.js
const moment = require('moment');

/**
 * Converts event date and start time to a cron expression.
 * 
 * @param {string} eventDate - The event date in ISO format (e.g., '2025-01-13T00:00:00.000+00:00').
 * @param {string} startTime - The event start time in 12-hour format with AM/PM (e.g., '10:00 AM').
 * @returns {string} - The corresponding cron expression.
 */
function convertToCron(eventDate, startTime) {
  // Parse the eventDate and startTime
  const eventMoment = moment(eventDate); // Parsing the event date (e.g., '2025-01-13T00:00:00.000+00:00')
  const startMoment = moment(startTime, 'h:mm A'); // Parsing the start time (e.g., '10:00 AM')

  // Extract date and time values for the cron expression
  const day = eventMoment.date();
  const month = eventMoment.month() + 1; // month is 0-indexed in Moment.js
  const hour = startMoment.hour(); // Get the hour from the startTime
  const minute = startMoment.minute(); // Get the minute from the startTime

  // Construct the cron expression
  const cronExpression = `${minute} ${hour} ${day} ${month} *`;
  return cronExpression;
}

module.exports = { convertToCron };
