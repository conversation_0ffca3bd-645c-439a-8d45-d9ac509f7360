const { ObjectId } = require("mongodb");

// Main function to build a comprehensive filter object
function buildFilters(filters) {
  const queryFilters = {};

  // Handle price range
  if (filters.priceMin !== undefined && filters.priceMax !== undefined) {
    // This is handled post-query now, but leaving structure in case needed later
  }

  // Handle age range
  if (filters.ageFrom !== undefined && filters.ageTo !== undefined) {
    queryFilters.ageFrom = { $lte: parseInt(filters.ageTo, 10) };
    queryFilters.ageTo = { $gte: parseInt(filters.ageFrom, 10) };
  }

  // Handle location (region/district) - direct match
  if (filters.location && filters.location !== 'Your location') {
    queryFilters.location = { $regex: new RegExp(filters.location, 'i') };
  }

  // Handle rating (greater than or equal to)
  if (filters.rating !== undefined && filters.rating > 0) {
    queryFilters.rating = { $gte: parseFloat(filters.rating) };
  }

  // Handle SEN service availability (boolean)
  if (filters.senService === true) {
    queryFilters.sen = true;
  }

  // Handle category filtering
  if (filters.category) {
    queryFilters["$or"] = [
      { category: filters.category },
      { categories: filters.category },
    ];
  }
  
  return queryFilters;
}

// Applies sorting to a Mongoose query
function applySorting(query, sortBy) {
  switch (sortBy) {
    case 'rating':
      return query.sort({ rating: -1 });
    case 'price':
      // Price sorting on calculated field is now handled post-query in the repo
      return query;
    case 'distance':
       // Distance sorting is handled by $geoNear, no explicit sort needed here
      return query;
    default:
      return query;
  }
}

// Builds a $geoNear aggregation stage for geospatial searches
function buildGeoNearStage(longitude, latitude, maxDistanceMeters) {
  return {
    $geoNear: {
      near: {
        type: "Point",
        coordinates: [parseFloat(longitude), parseFloat(latitude)],
      },
      distanceField: "distance", // This adds a 'distance' field to each document
      maxDistance: parseInt(maxDistanceMeters, 10),
      spherical: true,
      key: "location"
    },
  };
}

module.exports = {
  buildFilters,
  applySorting,
  buildGeoNearStage
};
