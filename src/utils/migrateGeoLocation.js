/**
 * Migration script to add geolocation and other filter-related fields to existing centers
 * 
 * Run with: node src/utils/migrateGeoLocation.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Center = require('../app/models/centerModel');
const Class = require('../app/models/classModel');

// Load environment variables
dotenv.config();

// Connect to database
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
}).then(() => {
  console.log('MongoDB connected');
  migrateData();
}).catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});

/**
 * Main migration function
 */
async function migrateData() {
  try {
    console.log('Starting migration...');
    
    // 1. Update centers with geolocation data
    await updateCentersWithGeoData();
    
    // 2. Update centers with price and age range data from classes
    await updateCentersWithClassData();
    
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

/**
 * Updates centers with geolocation data
 * Note: In a real-world scenario, you would use a geocoding service like Google Maps API
 * to convert addresses to coordinates. This is a simplified example.
 */
async function updateCentersWithGeoData() {
  console.log('Updating centers with geolocation data...');
  
  const centers = await Center.find({});
  console.log(`Found ${centers.length} centers to update`);
  
  // Sample regions with coordinates (for demonstration)
  const regionCoordinates = {
    'Central': [114.1585, 22.2826],
    'Wan Chai': [114.1733, 22.2793],
    'Tsim Sha Tsui': [114.1724, 22.2953],
    'Mong Kok': [114.1687, 22.3193],
    'Causeway Bay': [114.1853, 22.2812],
    'North Point': [114.1994, 22.2915],
    // Add more regions as needed
  };
  
  // Default coordinates for unknown regions
  const defaultCoordinates = [114.1694, 22.3193]; // Hong Kong center
  
  let updated = 0;
  
  for (const center of centers) {
    // Skip centers that already have geolocation data
    if (center.location && 
        center.location.coordinates && 
        center.location.coordinates[0] !== 0 && 
        center.location.coordinates[1] !== 0) {
      continue;
    }
    
    // Try to get coordinates based on region
    let coordinates = defaultCoordinates;
    if (center.address && center.address.region) {
      const region = center.address.region;
      if (regionCoordinates[region]) {
        coordinates = regionCoordinates[region];
      }
    }
    
    // Add slight randomization to avoid all centers in the same region having identical coordinates
    const jitter = 0.005; // Approximately 500m
    const randomizedCoordinates = [
      coordinates[0] + (Math.random() - 0.5) * jitter,
      coordinates[1] + (Math.random() - 0.5) * jitter
    ];
    
    // Update the center with geolocation data
    center.location = {
      type: 'Point',
      coordinates: randomizedCoordinates
    };
    
    await center.save();
    updated++;
  }
  
  console.log(`Updated ${updated} centers with geolocation data`);
}

/**
 * Updates centers with price and age range data from their classes
 */
async function updateCentersWithClassData() {
  console.log('Updating centers with price and age range data from classes...');
  
  const centers = await Center.find({});
  console.log(`Found ${centers.length} centers to update`);
  
  let updated = 0;
  
  for (const center of centers) {
    // Find all classes for this center
    const classes = await Class.find({ center: center._id });
    
    if (classes.length === 0) {
      // If center has no classes, set default values
      if (!center.price || center.price === 0) {
        center.price = Math.floor(Math.random() * 25) + 10; // Random price between 10-35
      }
      
      if (!center.ageRangeFrom || center.ageRangeFrom === 0) {
        center.ageRangeFrom = Math.floor(Math.random() * 3) + 2; // Random min age between 2-4
      }
      
      if (!center.ageRangeTo || center.ageRangeTo === 0) {
        center.ageRangeTo = center.ageRangeFrom + Math.floor(Math.random() * 4) + 2; // Random max age
      }
    } else {
      // Calculate average price from classes
      const prices = classes.map(c => c.classDate && c.classDate.charge ? c.classDate.charge : c.charge).filter(p => p !== undefined && p !== null);
      if (prices.length > 0) {
        const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
        center.price = Math.round(avgPrice);
      }
      
      // Find min and max age from classes
      const ageFromValues = classes.map(c => c.ageFrom).filter(a => a !== undefined && a !== null);
      const ageToValues = classes.map(c => c.ageTo).filter(a => a !== undefined && a !== null);
      
      if (ageFromValues.length > 0) {
        center.ageRangeFrom = Math.min(...ageFromValues);
      }
      
      if (ageToValues.length > 0) {
        center.ageRangeTo = Math.max(...ageToValues);
      }
    }
    
    // Set default categories if not set
    if (!center.categories || center.categories.length === 0) {
      const classCategories = [...new Set(classes.map(c => c.category))].filter(c => c);
      center.categories = classCategories.length > 0 ? classCategories : ['General'];
    }
    
    await center.save();
    updated++;
  }
  
  console.log(`Updated ${updated} centers with price and age range data`);
} 