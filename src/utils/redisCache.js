const redis = require('redis');
let redisClient;

// Connection options with sensible defaults
const REDIS_CONFIG = {
  socket: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379
  },
  username: process.env.REDIS_USERNAME,
  password: process.env.REDIS_PASSWORD,
  retry_strategy: (options) => {
    // Retry connection every 5 seconds up to 10 times
    if (options.attempt > 10) {
      console.error('Redis connection failed 10 times, giving up');
      return new Error('Redis connection failed too many times');
    }
    return Math.min(options.attempt * 1000, 5000);
  }
};

// Function to initialize Redis client
const initRedis = async () => {
  try {
    if (process.env.ENABLE_REDIS_CACHE !== 'true') {
      return null;
    }
    
    redisClient = redis.createClient(REDIS_CONFIG);
    
    // Event handlers for connection monitoring
    redisClient.on('error', (err) => {
      console.error('Redis error:', err);
    });
    
    // Connect to Redis server
    await redisClient.connect();
    
    return redisClient;
  } catch (error) {
    console.error('Redis initialization error:', error);
    return null;
  }
};

// Safe JSON parse with error handling
const safeJsonParse = (data) => {
  try {
    return JSON.parse(data);
  } catch (error) {
    console.error('Error parsing cached JSON data:', error);
    return null;
  }
};

// Cache recent messages for a conversation
const cacheMessages = async (conversationId, messages, ttl = 300) => {
  if (!redisClient || !redisClient.isReady) return;
  
  try {
    const key = `messages:${conversationId}`;
    // Ensure messages data is consistently structured
    const dataToCache = {
      messages: Array.isArray(messages.messages) ? messages.messages : [],
      nextCursor: messages.nextCursor || null
    };
    await redisClient.setEx(key, ttl, JSON.stringify(dataToCache));
    
    // Only access array length if it's actually an array
    const messageCount = Array.isArray(dataToCache.messages) ? dataToCache.messages.length : 0;
  } catch (error) {
    console.error(`Error caching messages for ${conversationId}:`, error);
  }
};

// Get cached messages for a conversation
const getCachedMessages = async (conversationId) => {
  if (!redisClient || !redisClient.isReady) return null;
  
  try {
    const key = `messages:${conversationId}`;
    const cachedData = await redisClient.get(key);
    
    if (cachedData) {
      const parsedData = safeJsonParse(cachedData);
      if (parsedData) {
        return parsedData;
      }
    }
    
    return null;
  } catch (error) {
    console.error(`Error getting cached messages for ${conversationId}:`, error);
    return null;
  }
};

// Cache conversations for a user
const cacheConversations = async (userId, conversations, ttl = 300) => {
  if (!redisClient || !redisClient.isReady) return;
  
  try {
    const key = `conversations:${userId}`;
    
    // Ensure the conversations object has the expected structure
    const dataToCache = {
      conversations: Array.isArray(conversations.conversations) ? conversations.conversations : [],
      total: typeof conversations.total === 'number' ? conversations.total : 0
    };
    
    await redisClient.setEx(key, ttl, JSON.stringify(dataToCache));
    
    // Only access array length if it's definitely an array
    const convCount = Array.isArray(dataToCache.conversations) ? dataToCache.conversations.length : 0;
  } catch (error) {
    console.error(`Error caching conversations for ${userId}:`, error);
  }
};

// Get cached conversations for a user
const getCachedConversations = async (userId) => {
  if (!redisClient || !redisClient.isReady) return null;
  
  try {
    const key = `conversations:${userId}`;
    const cachedData = await redisClient.get(key);
    
    if (cachedData) {
      const parsedData = safeJsonParse(cachedData);
      if (parsedData) {
        return parsedData;
      }
    }
    
    return null;
  } catch (error) {
    console.error(`Error getting cached conversations for ${userId}:`, error);
    return null;
  }
};

// Invalidate cache when a new message is sent
const invalidateConversationCache = async (conversationId) => {
  if (!redisClient || !redisClient.isReady) return;
  
  try {
    const key = `messages:${conversationId}`;
    await redisClient.del(key);
  } catch (error) {
    console.error(`Error invalidating cache for ${conversationId}:`, error);
  }
};

// Invalidate user's conversations cache when a new message is sent
const invalidateUserConversationsCache = async (userId) => {
  if (!redisClient || !redisClient.isReady) return;
  
  try {
    const key = `conversations:${userId}`;
    await redisClient.del(key);
  } catch (error) {
    console.error(`Error invalidating conversations cache for ${userId}:`, error);
  }
};

module.exports = {
  initRedis,
  cacheMessages,
  getCachedMessages,
  cacheConversations,
  getCachedConversations,
  invalidateConversationCache,
  invalidateUserConversationsCache
}; 