const jwt = require("jsonwebtoken");
require('dotenv').config();

const secretKey = process.env.JWT_SECRET_KEY;

const auth = async (req, res, next) => {
  try {
    // Extract token from the header and remove "Bearer " prefix if present
    const token = req.header("auth-token")?.replace("Bearer ", "");

    if (!token) return res.status(401).json({ message: "No auth token provided" });

    // Verify token with explicit expiration check
    const decoded = jwt.verify(token, secretKey, { ignoreExpiration: false });

    // Log the valid token payload
    console.log("Token payload:", decoded);

    // Attach user and token to the request object
    req.user = { id: decoded.id, type: decoded.type };
    req.token = token;

    next();
  } catch (e) {
    console.error('JWT Verification Error:', e);
    
    // Handle different types of errors with more detailed messages
    if (e.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        success: false,
        message: `Invalid token: ${e.message}`,
        errorType: 'INVALID_TOKEN'
      });
    } else if (e.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        success: false,
        message: `Token expired at ${new Date(e.expiredAt).toISOString()}`,
        errorType: 'TOKEN_EXPIRED',
        expiredAt: e.expiredAt
      });
    } else {
      return res.status(500).json({ 
        success: false,
        message: `Authentication error: ${e.message}`,
        errorType: 'AUTH_ERROR'
      });
    }
  }
};

module.exports = auth;
