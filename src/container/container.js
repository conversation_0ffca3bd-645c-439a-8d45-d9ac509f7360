const upload = require("../config/multer");

// === MODELS ===
const requestModel = require("../app/models/requestModel");
const announcementModel = require("../app/models/announecementModel");

// === REPOSITORIES ===
const UserRepository = require("../app/repo/userRepo");
const BaseUserRepository = require("../app/repo/baseUserRepo");
const OwnerRepository = require("../app/repo/businessOwnerRepo");
const CoachRepository = require("../app/repo/coachRepo");
const CenterRepository = require("../app/repo/centerRepo");
const RequestRepository = require("../app/repo/requestRepo");
const ManagerRepository = require("../app/repo/managerRepo");
const NotificationRepository = require("../app/repo/notificationRepo");
const ReviewRepository = require("../app/repo/reviewRepo");
const AttendanceRepository = require("../app/repo/attendanceRepo");
const EventRepository = require("../app/repo/eventRepo");
const ClassRepository = require("../app/repo/classRepo");
const OrderRepository = require("../app/repo/orderRepo");
const PurchasedHistoryRepo = require("../app/repo/purchaseHistoryRepo");
const orderSessionRepo = require("../app/repo/orderSessionsRepo");
const AnnouncementRepository = require("../app/repo/announcementRepo");
const ChildRepo = require("../app/repo/childRepo");
const BalanceRepo = require("../app/repo/balanceRepo");
const ClassDateRepo = require("../app/repo/classDateRepo");
const RefundRepo = require('../app/repo/refundRepo');

// === SERVICES ===
const TokenService = require("../app/services/tokenService");
const ImageService = require("../app/services/imageService");
const UploadFile = require("../app/services/uploadFile");
const UploadProcessor = require("../app/services/processUploadFields");
const HashService = require("../app/services/hashService");
const NotificationService = require("../app/services/notificationService");
const AuthMiddleware = require("../app/services/authMiddleware");
const RequestService = require("../app/services/requestService");
const AnnouncementService = require("../app/services/announcemetnService");
const CodeGenerator = require("../app/services/codeGenerator");
const QRCodeGenerator = require("../app/services/qrCodeGenerator");
const RefundService = require('../app/services/refundService');
const CancellationNotificationService = require('../app/services/cancellationNotificationService');
const EventService = require('../app/services/eventService');

// === USE CASES ===
const UpdateUser = require("../app/use_case/updateUser");
const CenterUseCase = require("../app/use_case/centerUseCase");
const SendNotificationToAssignCoach = require("../app/use_case/sendNotification/sendNotificationToAssignCoach");
const SendNotificationToRemoveCoach = require("../app/use_case/sendNotification/sendNotificationtToRemoveCoach");
const AssignCoachToCenter = require("../app/use_case/assignCoachtoCenter");
const AssignManagerToCenterUseCase = require("../app/use_case/assignManagertoCenter");
const RemoveCoachFromCenter = require("../app/use_case/removeCoachFromCenter");
const RemoveManagerFromCenter = require("../app/use_case/removeManagerToCenter");
const CoachUseCase = require("../app/use_case/coachUseCase");
const RejectRequestCoach = require("../app/use_case/reject_request_coach");
const ReviewUseCase = require("../app/use_case/reviewUseCase");
const GetMomentsUseCase = require("../app/use_case/getmomentsUseCase");
const ClassUseCase = require("../app/use_case/classUseCase");
const OrderUseCase = require("../app/use_case/orderUseCase");
const AttendanceUseCase = require("../app/use_case/attendanceUseCase");
const AnnouncementNotificationUseCase = require("../app/use_case/sendNotification/sendAnnouncementNotification");
const ChildUseCase = require("../app/use_case/childUseCase");
const BalanceUseCase = require("../app/use_case/balanceUseCase");
const CreateEventsFromClass = require("../app/use_case/createEventFromClass");

// === CONTROLLERS ===
const AddressController = require("../app/controllers/addressController");
const CenterController = require("../app/controllers/centerController");
const RequestController = require("../app/controllers/requestController");
const CoachController = require("../app/controllers/coachController");
const ReviewController = require("../app/controllers/reviewController");
const OrderController = require("../app/controllers/orderController");
const AnnouncementController = require("../app/controllers/announcementController");
const AttendanceController = require("../app/controllers/attendanceController");
const ChildController = require("../app/controllers/childController");

// === INITIALIZE REPOSITORIES ===
const userRepository = new UserRepository();
const baseUserRepository = new BaseUserRepository();
const ownerRepository = new OwnerRepository();
const coachRepository = new CoachRepository();
const centerRepository = new CenterRepository();
const requestRepository = new RequestRepository(requestModel);
const notificationRepository = new NotificationRepository();
const managerRepository = new ManagerRepository();
const reviewRepository = new ReviewRepository();
const attendanceRepository = new AttendanceRepository();
const eventRepository = new EventRepository();
const classRepository = new ClassRepository();
const purchasedHistoryRepo = new PurchasedHistoryRepo();
const orderRepository = new OrderRepository();
const classDateRepo = new ClassDateRepo();
const announcementRepository = new AnnouncementRepository(announcementModel);
const childRepo = new ChildRepo();
const balanceRepo = new BalanceRepo();
const refundRepo = new RefundRepo();

// === INITIALIZE SERVICES ===
const tokenService = new TokenService();
const imageService = new ImageService();
const uploadFile = new UploadFile(imageService);
const uploadProcessor = new UploadProcessor(uploadFile);
const hashService = new HashService();
const notificationService = new NotificationService({ notificationRepository });
const authMiddleware = new AuthMiddleware(tokenService);
const requestService = new RequestService({
  requestRepository,
  coachRepo: coachRepository,
  centerRepo: centerRepository,
});
const announcementNotificationUseCase = new AnnouncementNotificationUseCase();
const announcementService = new AnnouncementService(
  announcementRepository,
  classDateRepo,
  childRepo,
  announcementNotificationUseCase
);
const codeGenerator = new CodeGenerator();
const qrCodeGenerator = new QRCodeGenerator();
const refundService = new RefundService({
  refundRepo,
  childRepo,
  userRepo: userRepository,
  balanceRepo,
  classRepo: classRepository,
});
const cancellationNotificationService = new CancellationNotificationService({ notificationRepository });
const eventService = new EventService({
  eventRepository,
  classRepository,
  cancellationNotificationService,
  classDateRepo,
  refundService,
  childRepo
});

// === INITIALIZE USE CASES ===
const updateUser = new UpdateUser({
  userRepository,
  tokenService,
  uploadFile,
  hashService,
});
const centerUseCase = new CenterUseCase({
  centerRepository,
  coachRepository,
  baseUserRepository,
  ownerRepository,
  tokenService,
  uploadFile,
  requestRepository,
});
const sendNotificationToAssignCoach = new SendNotificationToAssignCoach({
  coachRepository,
  centerRepository,
  requestRepository,
  notificationService,
});
const sendNotificationToRemoveCoach = new SendNotificationToRemoveCoach({
  coachRepository,
  centerRepository,
  requestRepository,
  notificationService,
});
const assignCoachToCenter = new AssignCoachToCenter({
  centerRepository,
  coachRepository,
  notificationRepository,
});
const assignManagertoCenter = new AssignManagerToCenterUseCase({
  centerRepository,
  coachRepository,
  baseUserRepository,
  notificationRepository,
  requestRepository,
});
const removeCoachFromCenter = new RemoveCoachFromCenter({
  centerRepository,
  coachRepository,
  classRepo: classRepository
});
const removeManagerFromCenter = new RemoveManagerFromCenter({
  centerRepository,
  coachRepository,
  baseUserRepository,
  requestRepository,
});
const coachUseCase = new CoachUseCase({
  coachRepository,
  baseUserRepository,
  tokenService,
  uploadFile,
});
const rejectRequestCoach = new RejectRequestCoach({
  notificationRepository,
  requestRepository,
});
const createEventsFromClassUseCase = new CreateEventsFromClass(eventRepository);
const classUseCase = new ClassUseCase({
  classRepo: classRepository,
  tokenService,
  uploadFile,
  createEventsFromClass: createEventsFromClassUseCase,
  classDateRepo,
  eventRepository,
});
const getMomentsUseCase = new GetMomentsUseCase(reviewRepository);
const balanceUseCase = new BalanceUseCase(balanceRepo, userRepository);
const orderUseCase = new OrderUseCase({
  orderRepository,
  classUseCase,
  balanceUseCase,
  tokenService,
  purchasedHistoryRepo,
  orderSessionRepo,
  classDateRepo
});
const childUseCase = new ChildUseCase({
  childRepo,
  userRepository,
  tokenService,
  uploadFile,
});
const reviewUseCase = new ReviewUseCase(
  reviewRepository,
  centerUseCase,
  childUseCase,
  coachUseCase,
  attendanceRepository,
  eventRepository,
  uploadProcessor,
  classUseCase,
  orderRepository
);

const attendanceUseCase = new AttendanceUseCase({
  codeGenerator,
  qrCodeGenerator,
  attendanceRepo: attendanceRepository,
  secretKey: "very-secure-secret-key",
});

// === INITIALIZE CONTROLLERS ===
const addressController = new AddressController({ updateUser });
const centerController = new CenterController({
  centerUseCase,
  sendNotificationToAssignCoach,
  sendNotificationToRemoveCoach,
});
const requestController = new RequestController({ requestService });
const coachController = new CoachController({
  coachUseCase,
  assignCoachToCenter,
  removeCoachFromCenter,
  removeManagerFromCenter,
  assignManagertoCenter,
  rejectRequestCoach,
});
const reviewController = new ReviewController(reviewUseCase, getMomentsUseCase);
const orderController = new OrderController({ orderUseCase });
const announcementController = new AnnouncementController(announcementService);
const attendanceController = new AttendanceController({
  attendanceUseCase,
  attendanceRepo: attendanceRepository,
});
const childController = new ChildController({ childUseCase });

// === EXPORT EVERYTHING ===
module.exports = {
  // Controllers
  addressController,
  centerController,
  requestController,
  coachController,
  reviewController,
  orderController,
  announcementController,
  attendanceController,
  childController,

  // Repositories
  userRepository,
  baseUserRepository,
  ownerRepository,
  coachRepository,
  centerRepository,
  requestRepository,
  notificationRepository,
  managerRepository,
  reviewRepository,
  attendanceRepository,
  eventRepository,
  classRepository,
  purchasedHistoryRepo,
  orderRepository,
  orderSessionRepo,
  announcementRepository,
  childRepo,
  balanceRepo,
  refundRepo,

  // Models
  requestModel,
  announcementModel,

  // Use Cases
  updateUser,
  centerUseCase,
  sendNotificationToAssignCoach,
  sendNotificationToRemoveCoach,
  assignCoachToCenter,
  assignManagertoCenter,
  removeCoachFromCenter,
  removeManagerFromCenter,
  coachUseCase,
  rejectRequestCoach,
  reviewUseCase,
  getMomentsUseCase,
  classUseCase,
  orderUseCase,
  announcementNotificationUseCase,
  attendanceUseCase,
  childUseCase,
  balanceUseCase,

  // Services
  tokenService,
  imageService,
  uploadFile,
  uploadProcessor,
  hashService,
  notificationService,
  authMiddleware,
  requestService,
  announcementService,
  codeGenerator,
  qrCodeGenerator,
  refundService,
  cancellationNotificationService,
  eventService,

  // Config
  upload,
};
