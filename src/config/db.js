const mongoose = require("mongoose");
const Class = require('../app/models/classModel');
const Center = require('../app/models/centerModel');
const Coach = require('../app/models/coachModel');

const connectDB = async () => {
  try {
    let connectionString = process.env.DB_CONNECTION_STRING;
    
    // Fallback to config file if environment variable is not set
    if (!connectionString) {
      try {
        const config = require("../../config");
        connectionString = config.database.connectionString;
        console.log("Using database connection string from config file");
      } catch (err) {
        console.error("Error loading database connection string:", err.message);
        throw new Error("No database connection string available");
      }
    }
    
    // MongoDB connection options for better performance and reliability
    const mongooseOptions = {
      connectTimeoutMS: 30000, // 30 seconds timeout
      socketTimeoutMS: 45000,  // 45 seconds timeout
      serverSelectionTimeoutMS: 60000, // 60 seconds timeout
      maxPoolSize: 10, // Maximum pool size
      minPoolSize: 2,  // Minimum pool size
      heartbeatFrequencyMS: 10000, // Check connection every 10 seconds
    };
    
    await mongoose.connect(connectionString, mongooseOptions);
    console.log("Connected to the database");

    // Ensure indexes are created
    await Class.createIndexes();
    await Center.createIndexes();
    await Coach.createIndexes();
  } catch (err) {
    console.error("Error connecting to the database:", err.message);
    process.exit(1);
  }
};

module.exports = connectDB;
