// const express = require("express");
// const http = require("http");
// const dotenv = require("dotenv");
// const cors = require("cors");
// const path = require("path");
// const connectDB = require("../src/config/db");
// const expressRoutes = require("../src/framework/expressRoute");

// // Load environment variables
// dotenv.config();

// const app = express();
// // const server = http.createServer(app); // Create HTTP server
// // const io = require("socket.io")(server); // Attach Socket.io to the HTTP server

// connectDB();

// app.use(express.json());
// app.use(express.urlencoded({ extended: true }));
// app.use(cors());
// app.use(expressRoutes);

// app.use("/uploads", express.static(path.join(__dirname, "uploads")));

// const users = {};

// // io.on("connection", (socket) => {
// //   console.log("AABB")
// //   console.log("New WebSocket connectionAAAAAAAAAAAAAAAA");

// //   socket.on("register", (username) => {
// //     if (!username) {
// //       console.error("Username is required for registration");
// //       return;
// //     }
// //     users[username] = socket.id;
// //     console.log(`User registered: ${username}`);
// //   });

// //   socket.on("sendMessage", async (messageData) => {
// //     const { message, sender, recipient, senderModel, recipientModel } = messageData;

// //     if (!message || !sender || !recipient) {
// //       console.error("Message, sender, and recipient are required");
// //       return;
// //     }

// //     const recipientSocketId = users[recipient];
// //     if (recipientSocketId) {
// //       io.to(recipientSocketId).emit("message", { message, sender, recipient });
// //       try {
// //         await chatService.addChat(message, sender, recipient, senderModel, recipientModel);
// //         console.log("Message saved");
// //       } catch (error) {
// //         console.error("Error saving message:", error);
// //       }
// //     } else {
// //       console.error("Recipient not connected");
// //     }
// //   });

// //   socket.on("disconnect", () => {
// //     for (let username in users) {
// //       if (users[username] === socket.id) {
// //         console.log(`User disconnected: ${username}`);
// //         delete users[username];
// //         break;
// //       }
// //     }
// //   });
// // });

// const port = process.env.PORT || 3000;
// server.listen(port, "0.0.0.0", () => {
//   console.log(`Server is running on port ${port}`);
// });
