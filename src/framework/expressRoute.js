const express = require("express");
const authRoutes = require("../app/route/authRoute");
const { router: childRoutes } = require("../app/route/childRoute");
const { router: centerRoutes } = require("../app/route/centerRoute");
const { router: coachRoutes } = require("../app/route/coachRoute");
const { router: classRouter } = require("../app/route/classRoute"); // Properly destructure the router
const EventRoutes = require("../app/route/eventRoute");
const orderRoutes = require("../app/route/orderRoute");
const attendanceRoutes = require("../app/route/attendanceRoute");
const reviewRoute = require("../app/route/reviewRoute");
const purchasedHistoryRoute = require("../app/route/purchasedHistoryRoute");
const { router: balanceRoute } = require("../app/route/balanceRoute");
const paymentRoute = require("../app/route/paymentRoute");
const subscriptionPlanRouter = require("../app/route/subscriptionPlanRoute");
const subscriptionRouter = require("../app/route/subscriptionRoute");
const chatRouter = require("../app/route/chatRoute");
const searchRouter = require("../app/route/searchRoute");
const notificationRouter = require("../app/route/notificationRoute");
const otpRouter = require("../app/route/otpRoute");
const BusinessOwnerRouter = require("../app/route/businessOwnerRoute");
const DiscountRouter = require("../app/route/discountRoute");
const CampaignRouter = require("../app/route/campaignRoute");
const AnnouncementRouter = require("../app/route/announcementRoute");
const RequestRouter = require("../app/route/requestRoute");
const uploadRouter = require("../app/route/uploadRoute");
const parentRouter = require("../app/route/parentRoute");
const debugRouter = require("../app/route/debugRoute");
const addressRouter = require("../app/route/addressRoute"); // Add address router
const transactionRouter = require("../app/routes/transactionRoutes"); // Add transaction router
const cardsRouter = require("../app/route/cardsRoute"); // Add cards router
const adminRouter = require("../app/route/adminRoute"); // Add admin router
const refundRouter=require("../app/route/refundRoute");
//const { router: reviewRoute } = require("./reviewRoute");
const router = express.Router();

router.use("/api/auth", authRoutes);
router.use("/api/children", childRoutes);
router.use("/api/center", centerRoutes);
router.use("/api/owner", BusinessOwnerRouter);
router.use("/api/coach", coachRoutes);
router.use("/api/class", classRouter); // Use the router part
router.use("/api/events", EventRoutes);
router.use("/api/orders", orderRoutes);
router.use("/api/attendance", attendanceRoutes);
router.use("/api/review", reviewRoute);
router.use("/api/purchasedHistory", purchasedHistoryRoute);
router.use("/api/balance", balanceRoute);
router.use("/api/payment", paymentRoute);
router.use("/api/cards", cardsRouter); // Add cards routes
router.use("/api/subscriptionPlan", subscriptionPlanRouter);
router.use("/api/subscription", subscriptionRouter);
router.use("/api/chat", chatRouter);
router.use("/api/search", searchRouter);
router.use("/api/notification", notificationRouter);
router.use("/api/otp", otpRouter);
router.use("/api/discount", DiscountRouter);
router.use("/api/campaigns", CampaignRouter);
router.use("/api/announcement", AnnouncementRouter);
router.use("/api/request", RequestRouter);
router.use("/api/upload", uploadRouter);
router.use("/api/parent", parentRouter);
router.use("/api/address", addressRouter); // Add address routes
router.use("/api/transactions", transactionRouter);
router.use("/api/admin", adminRouter); // Add admin routes
router.use("/api/debug", debugRouter);
router.use("/api/refund",refundRouter);

module.exports = router;
