const express = require("express");
const router = express.Router();
const UserRepository = require("../../repo/userRepo");
const SubscriptionRepo = require("../../repo/subscriptionPlanRepo");
const PaymentService = require("../../services/paymentService");
const PaymentController = require("../controllers/paymentController");
const GetSavedCardUseCase = require("../../use_case/getsavedCardUseCase");
const SaveCardUseCase = require("../../use_case/saveCarduseCase");
const CreateCardTokenUseCase = require("../../use_case/createCardToken");
const PurchaseUseCase = require("../../use_case/purchaseUseCase");
const GetPaymentMethodUseCase = require("../../use_case/getPaymentMethodUseCase");
const PayWithSavedCardUseCase = require("../../use_case/payWithSavedCardUseCase");

const userRepository = new UserRepository();

const paymentService = new PaymentService();
const getSavedCardUseCase = new GetSavedCardUseCase(
  userRepository,
  paymentService
);
const purchaseUseCase = new PurchaseUseCase(paymentService);
const saveCardUseCase = new SaveCardUseCase(userRepository, paymentService);
const createCardTokenUseCase = new CreateCardTokenUseCase(paymentService);
const getPaymentMethodUseCase = new GetPaymentMethodUseCase(
  userRepository,
  paymentService
);
const payWithSavedCardUseCase = new PayWithSavedCardUseCase(paymentService);

const paymentController = new PaymentController(
  saveCardUseCase,
  getSavedCardUseCase,
  createCardTokenUseCase,
  purchaseUseCase,
  getPaymentMethodUseCase,
  payWithSavedCardUseCase
);

router.post("/savecard", (req, res) => {
  paymentController.saveCard(req, res);
});

router.get("/getcard/:userId", (req, res) => {
  paymentController.getSavedCard(req, res);
});

router.get("/getpaymentmethod/:userId", (req, res) => {
  paymentController.getPaymentMethod(req, res);
});

router.post("/createToken", (req, res) => {
  paymentController.createToken(req, res);
});

router.post("/paywithsavedcard", (req, res) => {
  paymentController.payWithSavedCard(req, res);
});

router.post("/purchase", (req, res) => {
  paymentController.purchase(req, res);
});

router.get("/getplans", (req, res) => {
  paymentController.getAllPlans(req, res);
});
module.exports = router;
