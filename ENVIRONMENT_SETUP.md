# Environment Setup Guide

This guide explains how to set up environment variables for the Class Cancellation System with payment integration.

## Required Environment Variables

Create a `.env` file in the root of your `classZ_Backend` directory with the following variables:

### Database Configuration
```env
MONGODB_URI=mongodb://localhost:27017/classz_app
```

### JWT Configuration
```env
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d
```

### Stripe Payment Gateway Configuration
To enable real refund processing, you need to set up Stripe:

1. **Create a Stripe Account**
   - Go to [https://dashboard.stripe.com](https://dashboard.stripe.com)
   - Create an account or sign in
   - Complete the verification process

2. **Get API Keys**
   - Navigate to **Developers > API keys**
   - Copy your **Secret key** (starts with `sk_test_` for testing)
   - Copy your **Publishable key** (starts with `pk_test_` for testing)

3. **Set Environment Variables**
```env
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
```

4. **Webhook Configuration (Optional)**
```env
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

### Server Configuration
```env
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:3001
```

## Complete .env File Template

```env
# Database
MONGODB_URI=mongodb://localhost:27017/classz_app

# JWT
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# Stripe (Required for real refunds)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Server
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:3001

# Optional: Email notifications
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Optional: SMS notifications
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********
```

## Testing the Setup

1. **Test Without Stripe (Mock Mode)**
   - Don't set the `STRIPE_SECRET_KEY` variable
   - The system will automatically use mock refunds
   - Perfect for development and testing

2. **Test With Stripe (Real Mode)**
   - Set up your Stripe test keys
   - Use Stripe's test card numbers
   - Monitor refunds in your Stripe dashboard

## Stripe Test Cards

For testing refunds, use these test card numbers:

- **Successful Payment**: `****************`
- **Declined Payment**: `****************`
- **Insufficient Funds**: `****************`

## Security Best Practices

1. **Never commit `.env` files to version control**
2. **Use test keys for development**
3. **Rotate keys regularly in production**
4. **Use environment-specific configurations**

## Production Considerations

When deploying to production:

1. **Use Production Stripe Keys**
   - Replace test keys with live keys
   - Enable webhook endpoints

2. **Secure Environment Variables**
   - Use your hosting platform's secret management
   - Encrypt sensitive values

3. **Monitor Transactions**
   - Set up Stripe webhooks
   - Implement proper logging
   - Monitor refund success rates

## Troubleshooting

### Common Issues

1. **"STRIPE_SECRET_KEY not found" Warning**
   - This is normal if you haven't set up Stripe yet
   - The system will use mock refunds

2. **"Invalid API Key" Error**
   - Check your Stripe key format
   - Ensure you're using the correct test/live keys
   - Verify the key hasn't been rotated

3. **Refunds Not Processing**
   - Check your Stripe dashboard for errors
   - Verify the payment ID format
   - Ensure the original payment exists

### Debug Mode

Enable debug logging by setting:
```env
DEBUG_MODE=true
LOG_LEVEL=debug
```

## Getting Help

- **Stripe Documentation**: [https://stripe.com/docs](https://stripe.com/docs)
- **Stripe Test Data**: [https://stripe.com/docs/testing](https://stripe.com/docs/testing)
- **MongoDB Connection**: [https://docs.mongodb.com/manual/reference/connection-string/](https://docs.mongodb.com/manual/reference/connection-string/)