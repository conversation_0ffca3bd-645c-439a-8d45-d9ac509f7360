# Class Cancellation Implementation

This document explains the implementation of the "Refund Students" and "Rearrange Slot" functionality for class cancellations.

## Overview

The system now supports three types of class cancellations:
1. **Normal Deletion** - Deletes empty classes (existing behavior)
2. **Refund Students** - Processes refunds and deletes the class
3. **Rearrange Slot** - Marks class for rearrangement and notifies students

## API Changes

### Frontend → Backend Request
```http
DELETE /api/events/class/{classId}
Content-Type: application/json

{
  "cancelType": "refund" | "rearrange" | null
}
```

### Response Format
```json
{
  "success": true,
  "message": "Slot cancelled and students will be refunded",
  "result": {
    "type": "refunded",
    "refunds": [...],
    "deletedEvents": true
  }
}
```

## Backend Implementation

### 1. Updated Controller (`eventController.js`)
- Now reads `cancelType` from request body
- Passes it to the service layer
- Returns appropriate success messages

### 2. Enhanced Service (`eventService.js`)
- Handles three different cancellation types
- Integrates with notification service
- Processes refunds for students

### 3. New Services Created

#### CancellationNotificationService
```javascript
// Sends notifications for:
- Refund confirmations
- Rearrangement notices
- General cancellation alerts
```

#### RefundService (Optional)
```javascript
// Handles:
- Individual refund processing
- Bulk refunds for class cancellations
- Payment gateway integration
```

## Cancellation Types Explained

### 1. Refund Students (`cancelType: "refund"`)

**What it does:**
- Calculates refund amounts for each enrolled student
- Processes refunds through payment gateway (mocked currently)
- Sends refund notifications to students
- Deletes the class and all related events

**Flow:**
1. Validates class exists and has enrolled students
2. Calculates refund amount per student (currently uses class charge)
3. Processes refunds for each student
4. Sends notifications about refunds
5. Deletes events from database
6. Returns refund summary

**Example Response:**
```json
{
  "type": "refunded",
  "message": "Slot cancelled and 3 students will be refunded",
  "refunds": [
    {
      "studentId": "student123",
      "refundAmount": 50,
      "status": "processed",
      "processedAt": "2024-01-15T10:30:00Z"
    }
  ],
  "deletedEvents": true
}
```

### 2. Rearrange Slot (`cancelType: "rearrange"`)

**What it does:**
- Marks the class for rearrangement (doesn't delete immediately)
- Notifies students about the rescheduling
- Keeps events for future rearrangement
- Creates administrative tasks for rescheduling

**Flow:**
1. Validates class exists
2. Marks class status as "pending_rearrangement"
3. Sends rearrangement notifications to students
4. Creates rearrangement tracking data
5. Keeps events in database for later rescheduling

**Example Response:**
```json
{
  "type": "rearranged",
  "message": "Slot marked for rearrangement, 3 students will be notified",
  "rearrangement": {
    "classId": "class123",
    "status": "pending_rearrangement",
    "originalStudentCount": 3,
    "markedAt": "2024-01-15T10:30:00Z"
  },
  "deletedEvents": false
}
```

### 3. Normal Deletion (default behavior)
- Only allows deletion of classes with no enrolled students
- Throws error if students are enrolled
- Maintains backward compatibility

## Frontend Integration

The frontend already sends the correct `cancelType` parameter:

```javascript
// Refund Students button
context.read<CenterBloc>().add(
  ClassSlotDeleteEvent(
    event.classId?.id ?? '',
    cancelType: 'refund',
  ),
);

// Rearrange Slot button  
context.read<CenterBloc>().add(
  ClassSlotDeleteEvent(
    event.classId?.id ?? '',
    cancelType: 'rearrange',
  ),
);
```

## Setup Instructions

### 1. Install Dependencies
```bash
cd classZ_Backend
npm install
```

### 2. Update Route Configuration
The route setup is already updated in `eventRoute.js` with mock notification service.

### 3. Configure Real Notification Service (Optional)
Replace the mock notification repository with your actual implementation:

```javascript
// In eventRoute.js
const NotificationRepository = require("../repo/notificationRepo");
const notificationRepository = new NotificationRepository();

const cancellationNotificationService = new CancellationNotificationService({
  notificationRepository: notificationRepository
});
```

### 4. Configure Payment Gateway (For Refunds)
To enable real refund processing, integrate with your payment provider:

```javascript
// Example for Stripe
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// In RefundService.js
const refund = await stripe.refunds.create({
  payment_intent: originalPaymentId,
  amount: Math.round(refundAmount * 100),
  reason: 'requested_by_customer'
});
```

## Testing

### Test Refund Flow
```bash
curl -X DELETE http://localhost:3000/api/events/class/CLASS_ID \
  -H "Content-Type: application/json" \
  -d '{"cancelType": "refund"}'
```

### Test Rearrangement Flow
```bash
curl -X DELETE http://localhost:3000/api/events/class/CLASS_ID \
  -H "Content-Type: application/json" \
  -d '{"cancelType": "rearrange"}'
```

## Production Considerations

### 1. Database Updates Needed
- Add `status` field to Event model for tracking rearrangements
- Add `pendingRearrangement` field to Class model
- Create refund tracking tables

### 2. Payment Integration
- Configure payment gateway credentials
- Add webhook handling for refund status updates
- Implement refund reconciliation

### 3. Notification Enhancements
- Integrate with email service (SendGrid, etc.)
- Add SMS notifications
- Implement push notifications

### 4. Admin Tools
- Create admin dashboard for managing rearrangements
- Add refund monitoring and reporting
- Implement manual refund processing tools

## Error Handling

The implementation includes comprehensive error handling:
- Payment gateway failures
- Notification delivery failures
- Database transaction failures
- Network connectivity issues

Failed operations are logged but don't prevent the core cancellation from completing.

## Security Considerations

- Validate user permissions before processing cancellations
- Audit log all refund operations
- Implement rate limiting for cancellation requests
- Encrypt sensitive payment information

## Future Enhancements

1. **Automatic Rescheduling**: AI-powered slot rearrangement
2. **Partial Refunds**: Support for percentage-based refunds
3. **Credit System**: Issue credits instead of refunds
4. **Batch Operations**: Handle multiple class cancellations
5. **Analytics**: Track cancellation patterns and reasons