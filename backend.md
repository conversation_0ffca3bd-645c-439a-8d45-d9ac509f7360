req.params
centerId
HELLO: 682e257644f4dce64bfee3f6
[
  {
    mainImage: {
      url: '/uploads/1750848746993-mainImage-682e257644f4dce64bfee3f6.jpg',
      contentType: 'image/jpeg'
    },
    rearrangementInfo: {
      isRearranging: false,
      originalDates: [],
      newDates: [],
      studentsNotified: false
    },
    status: 'active',
    _id: new ObjectId('685bd4ebe5a8b81476b7cde2'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    student: [
      new ObjectId('6720d4b771fd2c5f3e236499'),
      new ObjectId('6856fb1faf9353edfad95d86'),
      new ObjectId('685b01bda328bf6181ef26fa'),
      new ObjectId('685cdf9806d03eb93fa65eab'),
      new ObjectId('6720cf643e2668d169c0094d'),
      new ObjectId('6863c1d34dc83cc84e865560')
    ],
    category: 'Language',
    classProviding: 'test',
    level: 'begineer',
    description: 'testing',
    mode: false,
    sen: false,
    initialCharge: '0',
    charge: 5,
    ageFrom: 9,
    ageTo: 14,
    numberOfClass: 2,
    languageOptions: [ 'Spanish', 'Cantonese' ],
    dates: [ [Object] ],
    createdAt: 2025-06-25T10:52:27.004Z,
    updatedAt: 2025-07-16T02:15:12.798Z,
    __v: 0,
    address: 'center',
    buyAll: true,
    course: false,
    joinNew: false,
    newComer: false,
    numberOfStudent: 5,
    coach: {
      mainImage: [Object],
      _id: new ObjectId('67e7af4322a791ee0bc40fa9'),
      baseUser: new ObjectId('67e7af4322a791ee0bc40fa5'),
      email: '<EMAIL>',
      displayName: 'Coach Three',
      address: [Object],
      languages: [Array],
      description: 'Certified swimmingxzxzc coach',
      ageFrom: '5',
      ageTo: '15',
      sen: false,
      experience: [Array],
      skill: [Array],
      accredation: [Array],
      images: [Array],
      createdAt: 2025-03-29T08:28:51.701Z,
      updatedAt: 2025-07-01T02:53:34.375Z,
      __v: 0,
      legalName: 'N N Dipu',
      manager: new ObjectId('682e257644f4dce64bfee3f6'),
      phone: '',
      programs: [],
      rating: 3,
      reviewCount: 0,
      center: new ObjectId('682e257644f4dce64bfee3f6'),
      classzId: 'COA-250619-D69K'
    },
    minimumStudent: 1
  },
  {
    rearrangementInfo: {
      isRearranging: false,
      originalDates: [],
      newDates: [],
      studentsNotified: false
    },
    _id: new ObjectId('6877f55558313742b2099661'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    student: [],
    category: 'Language',
    classProviding: 'Brian',
    level: 'beginer',
    description: 'good course',
    mode: false,
    sen: false,
    initialCharge: '0',
    charge: 1,
    ageFrom: 11,
    ageTo: 12,
    minimumStudent: 1,
    numberOfClass: 0,
    languageOptions: [],
    status: 'active',
    createdAt: 2025-07-16T18:54:13.837Z,
    updatedAt: 2025-07-16T19:37:57.116Z,
    __v: 0,
    coach: {
      mainImage: [Object],
      _id: new ObjectId('67e7af4322a791ee0bc40fa9'),
      baseUser: new ObjectId('67e7af4322a791ee0bc40fa5'),
      email: '<EMAIL>',
      displayName: 'Coach Three',
      address: [Object],
      languages: [Array],
      description: 'Certified swimmingxzxzc coach',
      ageFrom: '5',
      ageTo: '15',
      sen: false,
      experience: [Array],
      skill: [Array],
      accredation: [Array],
      images: [Array],
      createdAt: 2025-03-29T08:28:51.701Z,
      updatedAt: 2025-07-01T02:53:34.375Z,
      __v: 0,
      legalName: 'N N Dipu',
      manager: new ObjectId('682e257644f4dce64bfee3f6'),
      phone: '',
      programs: [],
      rating: 3,
      reviewCount: 0,
      center: new ObjectId('682e257644f4dce64bfee3f6'),
      classzId: 'COA-250619-D69K'
    }
  },
  {
    rearrangementInfo: {
      isRearranging: false,
      originalDates: [],
      newDates: [],
      studentsNotified: false
    },
    _id: new ObjectId('687802dc44de1e6f89803c19'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    student: [],
    category: 'Art',
    classProviding: 'I do not know',
    level: 'advance',
    description: '1234567',
    mode: false,
    sen: false,
    initialCharge: '0',
    charge: 134,
    ageFrom: 5,
    ageTo: 15,
    minimumStudent: 1,
    numberOfClass: 0,
    languageOptions: [],
    status: 'active',
    createdAt: 2025-07-16T19:51:56.288Z,
    updatedAt: 2025-07-16T20:10:51.688Z,
    __v: 0
  }
]
