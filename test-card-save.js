const axios = require('axios');

async function testSaveCard() {
  try {
    console.log('Testing card saving API...');
    
    // First, create a new token
    console.log('Creating a new Stripe token...');
    const tokenResponse = await axios.post('http://localhost:3000/api/payment/createToken', {
      number: '****************',
      expMonth: 12,
      expYear: 2025,
      cvc: '123',
      name: 'Test User'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'auth-token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MTVkNDhmM2NhYzk0NTQ1NzdmNmFmOSIsImlhdCI6MTc0NzY2NzA3NH0._A_tp5Zdfqc_7OhzKWRJMOBRpZfj1A-sHdKNTwsAyMY'
      }
    });
    
    console.log('Token created:', tokenResponse.data.token);
    
    // Then, save the card with the new token
    const response = await axios.post('http://localhost:3000/api/payment/savecard', {
      userId: '6824d948ece27184f5f08fba',
      cardToken: tokenResponse.data.token
    }, {
      headers: {
        'Content-Type': 'application/json',
        'auth-token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MTVkNDhmM2NhYzk0NTQ1NzdmNmFmOSIsImlhdCI6MTc0NzY2NzA3NH0._A_tp5Zdfqc_7OhzKWRJMOBRpZfj1A-sHdKNTwsAyMY'
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testSaveCard(); 