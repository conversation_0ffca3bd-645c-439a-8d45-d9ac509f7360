I'll create a documentation for the backend API endpoints needed for the saved centers feature. Based on the frontend implementation, here's what you'll need:

# Saved Centers API Documentation

## Base URL
`/api/parent`

## Database Schema

### SavedCenter Model
```javascript
{
  _id: ObjectId,
  parentId: ObjectId,  // Reference to Parent
  centerId: ObjectId,  // Reference to Center
  createdAt: Date,
  updatedAt: Date
}
```

## API Endpoints

### 1. Save a Center
Save a center to parent's saved centers list.

- **Endpoint**: `POST /api/parent/save-center`
- **Authentication**: Required (JWT)
- **Request Body**:
```json
{
  "parentId": "string",
  "centerId": "string"
}
```
- **Response (200)**:
```json
{
  "success": true,
  "message": "Center saved successfully"
}
```
- **Response (400)**:
```json
{
  "success": false,
  "message": "Invalid request parameters"
}
```
- **Response (401)**:
```json
{
  "success": false,
  "message": "Unauthorized"
}
```

### 2. Unsave a Center
Remove a center from parent's saved centers list.

- **Endpoint**: `POST /api/parent/unsave-center`
- **Authentication**: Required (JWT)
- **Request Body**:
```json
{
  "parentId": "string",
  "centerId": "string"
}
```
- **Response (200)**:
```json
{
  "success": true,
  "message": "Center removed from saved list"
}
```

### 3. Get Saved Centers
Get all saved centers for a parent.

- **Endpoint**: `GET /api/parent/saved-centers/:parentId`
- **Authentication**: Required (JWT)
- **Parameters**: 
  - `parentId`: Parent's ID (in URL)
- **Response (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "legalName": "string",
      "startAge": "string",
      "address": {
        "address1": "string",
        // ... other address fields
      },
      "mainImage": {
        "url": "string"
        // ... other image fields
      },
      "rating": number
      // ... other center fields
    }
  ]
}
```

### 4. Check if Center is Saved
Check if a specific center is saved by the parent.

- **Endpoint**: `GET /api/parent/is-center-saved/:parentId/:centerId`
- **Authentication**: Required (JWT)
- **Parameters**:
  - `parentId`: Parent's ID (in URL)
  - `centerId`: Center's ID (in URL)
- **Response (200)**:
```json
{
  "success": true,
  "isSaved": boolean
}
```

## Implementation Notes

### Authentication Middleware
```javascript
const authenticateJWT = (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({
      success: false,
      message: 'No authorization token provided'
    });
  }

  const token = authHeader.split(' ')[1];
  // Verify JWT token
  // Add user to request object
  next();
};
```

### Example Controller Implementation
```javascript
const SavedCenterController = {
  async saveCenter(req, res) {
    try {
      const { parentId, centerId } = req.body;
      
      // Validate IDs
      if (!parentId || !centerId) {
        return res.status(400).json({
          success: false,
          message: 'Parent ID and Center ID are required'
        });
      }

      // Check if already saved
      const existing = await SavedCenter.findOne({ parentId, centerId });
      if (existing) {
        return res.status(400).json({
          success: false,
          message: 'Center already saved'
        });
      }

      // Save center
      await SavedCenter.create({ parentId, centerId });

      res.json({
        success: true,
        message: 'Center saved successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to save center',
        error: error.message
      });
    }
  },

  async getSavedCenters(req, res) {
    try {
      const { parentId } = req.params;

      // Get saved centers with populated center data
      const savedCenters = await SavedCenter.find({ parentId })
        .populate('centerId')
        .sort({ createdAt: -1 });

      // Transform data to match frontend requirements
      const centers = savedCenters.map(saved => ({
        id: saved.centerId._id,
        legalName: saved.centerId.legalName,
        startAge: saved.centerId.startAge,
        address: saved.centerId.address,
        mainImage: saved.centerId.mainImage,
        rating: saved.centerId.rating
      }));

      res.json({
        success: true,
        data: centers
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to get saved centers',
        error: error.message
      });
    }
  }
};
```

### Routes Setup
```javascript
const router = express.Router();

router.post('/save-center', authenticateJWT, SavedCenterController.saveCenter);
router.post('/unsave-center', authenticateJWT, SavedCenterController.unsaveCenter);
router.get('/saved-centers/:parentId', authenticateJWT, SavedCenterController.getSavedCenters);
router.get('/is-center-saved/:parentId/:centerId', authenticateJWT, SavedCenterController.isCenterSaved);

module.exports = router;
```

## Error Handling
- Implement proper error handling for:
  - Invalid ObjectIds
  - Non-existent parents/centers
  - Database connection errors
  - Authentication failures
  - Validation errors

## Security Considerations
1. Validate that the authenticated user has permission to access/modify the specified parent's data
2. Implement rate limiting
3. Sanitize input data
4. Use HTTPS
5. Implement proper CORS settings


