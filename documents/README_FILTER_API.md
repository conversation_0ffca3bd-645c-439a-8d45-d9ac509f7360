# ClassZ Filter API Documentation

This document provides documentation for the filter APIs implemented to support the `filter_screen.dart` functionality in the ClassZ application.

## API Endpoints

### 1. General Search

Search across centers, classes, and coaches with filters.

```
GET /api/search?q={searchQuery}&{filterParameters}
```

### 2. Filter Centers

Filter centers based on various criteria.

```
GET /api/search/centers/filter?{filterParameters}
```

### 3. Filter Classes

Filter classes based on various criteria.

```
GET /api/search/classes/filter?{filterParameters}
```

### 4. Find Nearby Centers

Find centers near a specific location.

```
GET /api/search/centers/nearby?longitude={longitude}&latitude={latitude}&{additionalParameters}
```

## Filter Parameters

All filter endpoints support the following query parameters:

| Parameter   | Type    | Description                                        | Example               |
|-------------|---------|----------------------------------------------------|-----------------------|
| minPrice    | Number  | Minimum price for filtering                        | minPrice=12           |
| maxPrice    | Number  | Maximum price for filtering                        | maxPrice=36           |
| minAge      | Number  | Minimum age for filtering                          | minAge=3              |
| maxAge      | Number  | Maximum age for filtering                          | maxAge=6              |
| location    | String  | Location in format "region,district"               | location=Central,Wan%20Chai |
| sortBy      | String  | Sort results by: "price", "rating", or "distance"  | sortBy=price          |
| rating      | Number  | Minimum rating filter                              | rating=4              |
| senService  | Boolean | Filter for special educational needs services      | senService=true       |
| category    | String  | Filter by category                                | category=Music        |
| page        | Number  | Page number for pagination                         | page=1                |
| limit       | Number  | Results per page                                   | limit=20              |

### Additional Parameters for Nearby Search

| Parameter   | Type    | Description                                        | Example               |
|-------------|---------|----------------------------------------------------|-----------------------|
| longitude   | Number  | Longitude coordinate (required)                    | longitude=114.1694    |
| latitude    | Number  | Latitude coordinate (required)                     | latitude=22.3193      |
| maxDistance | Number  | Maximum distance in meters (optional)              | maxDistance=5000      |

## Response Format

All endpoints return responses in the following format:

```json
{
  "success": true,
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 120
  },
  "data": [...] // Array of results
}
```

For the general search endpoint, the response format is:

```json
{
  "success": true,
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 120
  },
  "results": {
    "centers": [...],
    "classes": [...],
    "coaches": [...]
  }
}
```

## Error Handling

In case of errors, the API returns:

```json
{
  "success": false,
  "message": "Error message",
  "error": "Detailed error description"
}
```

## Usage Examples

### Example 1: Filter Centers by Price Range and Category

```
GET /api/search/centers/filter?minPrice=10&maxPrice=30&category=Music&sortBy=price
```

### Example 2: Find Nearby Centers with Additional Filters

```
GET /api/search/centers/nearby?longitude=114.1694&latitude=22.3193&maxDistance=5000&senService=true&rating=4
```

### Example 3: Search for Classes by Age Range

```
GET /api/search/classes/filter?minAge=3&maxAge=6&sortBy=rating
```

### Example 4: General Search with Filters

```
GET /api/search?q=piano&minPrice=20&maxPrice=50&location=Central
```

## Integration with Flutter

For integrating these APIs with the Flutter app, use the HTTP package to make requests:

```dart
// Example code for filtering centers
Future<List<CenterData>> getFilteredCenters({
  int? minPrice,
  int? maxPrice,
  int? minAge,
  int? maxAge,
  String? location,
  String? sortBy,
  double? rating,
  bool? senService,
}) async {
  // Build query parameters
  Map<String, String> queryParams = {};
  
  if (minPrice != null) queryParams['minPrice'] = minPrice.toString();
  if (maxPrice != null) queryParams['maxPrice'] = maxPrice.toString();
  if (minAge != null) queryParams['minAge'] = minAge.toString();
  if (maxAge != null) queryParams['maxAge'] = maxAge.toString();
  if (location != null) queryParams['location'] = location;
  if (sortBy != null) queryParams['sortBy'] = sortBy;
  if (rating != null) queryParams['rating'] = rating.toString();
  if (senService != null) queryParams['senService'] = senService.toString();
  
  // Create URL with query parameters
  final uri = Uri.parse('$baseUrl/search/centers/filter').replace(queryParameters: queryParams);
  
  final response = await http.get(uri);
  
  if (response.statusCode == 200) {
    final Map<String, dynamic> data = json.decode(response.body);
    
    if (data['success']) {
      final List<dynamic> centersList = data['data'];
      return centersList.map((center) => CenterData.fromJson(center)).toList();
    } else {
      throw Exception(data['message'] ?? 'Unknown error');
    }
  } else {
    throw Exception('Failed to load centers: ${response.statusCode}');
  }
}
```

## Testing with Postman

1. Set up a Postman collection for testing these filter APIs
2. Create request templates for each endpoint
3. Test various filter combinations to ensure they work correctly
4. Verify pagination works as expected
5. Test error scenarios by providing invalid parameters 