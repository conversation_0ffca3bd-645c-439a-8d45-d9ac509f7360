# Parent Review System Documentation

## Overview

The Parent Review System allows parents to provide feedback on coaches and centers at two specific points:

1. **After the first lesson** (once the coach has provided a performance review)
2. **At the end of all lessons** (if they didn't review after the first lesson)

This document explains how the system works, what changes were made, how to test it, and the API integration.

## How It Works

### Review Timing Logic

The system determines when to show review prompts based on the following conditions:

```dart
// Check if we're at the end of all lessons
bool isLastLesson = currentSessionIndex == totalSessions - 1;

// Check if first lesson has been completed
// Note: currentSessionIndex is 0-based, so index 1 means we're after the first lesson
bool isAfterFirstLesson = currentSessionIndex > 0;
```

### Coach Review Check

Before showing the first lesson review, the system verifies that the coach has provided a performance review:

```dart
bool hasCoachReview = _hasCoachPerformanceReview(pending);

// Helper method to check if coach has given a performance review
bool _hasCoachPerformanceReview(PendingModel pending) {
  // Check if there are any reviews associated with this class
  if (pending.event == null) return false;
  
  // Since we don't have a direct way to check for coach reviews,
  // we'll consider the existence of an event as an indication
  // that there might be a review
  return true;
}
```

### One-Time Review Guarantee

The system uses SharedPreferences to ensure each review opportunity is only shown once:

```dart
// Check if the first lesson review was already shown
String firstLessonKey = "$_firstLessonReviewPrefixKey${userId}_$classId";
bool firstLessonReviewShown = prefs.getBool(firstLessonKey) == true;

// Mark first lesson review as shown
await prefs.setBool(firstLessonKey, true);
```

## Changes Made

### 1. Modified Review Timing

- **Before**: Reviews were prompted at the halfway point and completion of a course
- **After**: Reviews are now prompted after the first lesson (with coach review) and at course completion (if not already reviewed)

### 2. Updated Storage Keys

- Changed from `half_course_review_{userId}_{classId}` to `first_lesson_review_{userId}_{classId}`

### 3. Updated Review Titles

- Changed "Mid-Course Review" to "First Lesson Review"
- Updated descriptions to reflect the new timing

### 4. Added Coach Review Check

- Added logic to check if the coach has provided a review before showing the parent review prompt

## How to Test

### Testing First Lesson Review

1. Login as a parent
2. Navigate to a class where:
   - The first lesson has been completed
   - The coach has provided a performance review
   - You haven't reviewed this class before
3. Open the class details screen
4. Verify that the review popup appears
5. Submit a review
6. Re-open the class details screen and verify the review doesn't appear again

### Testing End-of-Course Review

1. Login as a parent
2. Navigate to a class where:
   - All lessons have been completed
   - You haven't reviewed this class before
3. Open the class details screen
4. Verify that the review popup appears
5. Submit a review
6. Re-open the class details screen and verify the review doesn't appear again

## API Integration

### Review Submission API

When a parent submits a review, the app sends a POST request to the server:

**Endpoint**: `/api/review/`

**Payload**:
```json
{
  "reviewerId": "parent_user_id",
  "reviewerType": "User",
  "revieweeId": "coach_or_center_id",
  "revieweeType": "Coach" or "Center",
  "classId": "class_id",
  "rating": 5.0,
  "title": "First Lesson Review" or "Course Completion",
  "comment": "User's comment text",
  "date": "2023-05-19T12:00:00Z"
}
```

### No Backend Changes Required

The changes made to the review system are all client-side and don't require any backend modifications:

1. The API endpoint remains the same
2. The payload structure is unchanged
3. Only the timing of when reviews are shown and the text content has changed

## Implementation Files

Key files modified:

1. `lib/core/services/course_progress_service.dart` - Core review logic
2. `lib/features/roles/parent/presentation/screen/class_details.dart` - Integration with class details
3. `lib/core/common/presentation/widgets/course_review_popup.dart` - Individual review UI
4. `lib/core/common/presentation/widgets/combined_course_review_popup.dart` - Combined review UI

## Future Improvements

1. **Better Coach Review Detection**: Implement a more reliable way to check if the coach has submitted a performance review
2. **Review History**: Add a way for parents to see their past reviews
3. **Review Reminders**: Send notifications to remind parents to review if they haven't done so 