# Campaign Management System Documentation

## Overview

The Campaign Management System allows you to create and manage promotional campaign highlights that appear on the main page of the ClassZ app. This system replaces the hardcoded "NEW YEAR NEW CLASSES" widget with dynamic, configurable campaign cards.

## Features

- Dynamic campaign highlights with customizable content
- Horizontal scrolling campaign cards
- View-only display (clicking disabled for better UX)
- Gradient background colors
- Responsive design
- Real-time campaign management via backend API

## How to Adjust Campaign Content

### 1. Backend Campaign Management

#### Creating a New Campaign

To create a new campaign, you can use the backend API or create a script similar to the sample data script:

**API Endpoint:**
```
POST http://*************:3000/api/campaigns
```

**Campaign Data Structure:**
```javascript
{
  "title": "Campaign Title",                    // Main headline (e.g., "LUNAR NEW YEAR SPECIAL")
  "subtitle": "Campaign Subtitle",              // Secondary text (e.g., "20% OFF ALL CLASSES")
  "description": "Detailed description",        // Full description for analytics
  "imageUrl": "",                               // Optional image URL
  "backgroundColor": "#FF6B6B",                 // Hex color for gradient start
  "textColor": "#FFFFFF",                       // Text color (hex)
  "discountPercentage": 20,                     // Numeric discount value
  "discountCode": "PROMO2024",                  // Discount code users can use
  "validFrom": "2025-01-01T00:00:00.000Z",      // Start date (ISO format)
  "validUntil": "2025-01-31T23:59:59.000Z",     // End date (ISO format)
  "isActive": true,                             // Whether campaign is visible
  "priority": 10,                               // Display order (higher = first)
  "targetAudience": "all",                      // Target audience
  "actionType": "discount"                      // Type of campaign action
}
```

#### Example Script to Create Campaign

Create a file `create_campaign.js` in the backend `scripts` folder:

```javascript
const mongoose = require('mongoose');
const CampaignModel = require('../src/app/models/campaignModel');

const campaigns = [
  {
    title: "Spring Special",
    subtitle: "25% Off All Classes",
    description: "Welcome spring with amazing discounts on all our classes!",
    backgroundColor: "#4ECDC4",
    textColor: "#FFFFFF",
    discountPercentage: 25,
    discountCode: "SPRING25",
    validFrom: new Date("2025-03-01"),
    validUntil: new Date("2025-03-31"),
    isActive: true,
    priority: 10,
    targetAudience: "all",
    actionType: "discount"
  }
];

async function createCampaigns() {
  try {
    await mongoose.connect('your-mongodb-connection-string');
    
    for (const campaign of campaigns) {
      const newCampaign = new CampaignModel(campaign);
      await newCampaign.save();
      console.log(`Created campaign: ${campaign.title}`);
    }
    
    console.log('All campaigns created successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error creating campaigns:', error);
    process.exit(1);
  }
}

createCampaigns();
```

Run the script:
```bash
cd classZ_Backend
node scripts/create_campaign.js
```

### 2. Managing Existing Campaigns

#### Update Campaign Content

**API Endpoint:**
```
PUT http://*************:3000/api/campaigns/{campaignId}
```

**Example Update:**
```javascript
{
  "title": "Updated Campaign Title",
  "subtitle": "New Subtitle",
  "discountPercentage": 30,
  "backgroundColor": "#FF9800"
}
```

#### Deactivate Campaign

```javascript
{
  "isActive": false
}
```

#### Get All Active Campaigns

```
GET http://*************:3000/api/campaigns/active
```

### 3. Campaign Display Customization

#### Color Schemes

The system supports gradient backgrounds. You can use these color combinations:

```javascript
// Warm colors
backgroundColor: "#FF6B6B"  // Red gradient
backgroundColor: "#FF9800"  // Orange gradient
backgroundColor: "#FFC107"  // Yellow gradient

// Cool colors
backgroundColor: "#2196F3"  // Blue gradient
backgroundColor: "#4CAF50"  // Green gradient
backgroundColor: "#9C27B0"  // Purple gradient

// Text colors
textColor: "#FFFFFF"        // White text
textColor: "#333333"        // Dark text
textColor: "#000000"        // Black text
```

#### Date Formatting

The system automatically formats date ranges:
- Same month: "1 - 15 Mar"
- Different months: "25 Feb - 5 Mar"

## File Structure & Components

### Backend Components

```
classZ_Backend/src/app/
├── models/campaignModel.js           # MongoDB schema
├── repo/campaignRepo.js              # Database operations
├── services/campaignService.js       # Business logic
├── controllers/campaignController.js # HTTP endpoints
└── routes/campaignRoute.js           # API routes
```

### Frontend Components

```
lib/core/common/
├── domain/
│   ├── entities/campaign_entity.dart    # Domain entity
│   └── usecase/campaign_use_case.dart   # Use cases
├── data/
│   ├── models/campaign_model.dart       # Data model with JSON mapping
│   ├── data_sources/campaign_data_source.dart  # API communication
│   └── repositories/campaign_repo.dart  # Repository implementation
└── presentation/
    ├── blocs/campaignBloc/             # State management
    │   ├── campaign_bloc.dart
    │   ├── campaign_event.dart
    │   └── campaign_state.dart
    └── widgets/campaign_highlight_widget.dart  # UI widget
```

### Integration Points

```
lib/
├── dependency_injection/injection.dart  # Dependency registration
├── main.dart                           # BlocProvider registration
└── features/roles/parent/presentation/screen/user_main_page.dart  # Usage
```

## API Endpoints

### Public Endpoints

- `GET /api/campaigns/active` - Get all active campaigns
- `POST /api/campaigns/{id}/click` - Record campaign click

### Admin Endpoints

- `POST /api/campaigns` - Create new campaign
- `PUT /api/campaigns/{id}` - Update campaign
- `DELETE /api/campaigns/{id}` - Delete campaign
- `GET /api/campaigns` - Get all campaigns (with pagination)

## Troubleshooting

### Common Issues

1. **Campaigns not showing**
   - Check if campaigns are active (`isActive: true`)
   - Verify campaign dates are current
   - Check backend connection

2. **Styling issues**
   - Verify hex color format (#RRGGBB)
   - Check text color contrast
   - Ensure valid date formats

3. **Campaigns not displaying properly**
   - Check network connectivity
   - Verify backend server is running
   - Check Flutter console for errors

### Debug Tips

Enable debug logging by checking Flutter console for:
- `🎯 Dispatching GetActiveCampaignsEvent...`
- `📡 CampaignDataSource: Response received`
- `🔍 CampaignBloc: Use case response`

## Analytics

Campaign performance is tracked with:
- `clickCount` - Number of times campaign was clicked
- `impressionCount` - Number of times campaign was viewed
- Created/updated timestamps

Access analytics via backend API:
```
GET /api/campaigns/{id}
```

## Best Practices

1. **Content Guidelines**
   - Keep titles under 3 words for better display
   - Use subtitles for discount information
   - Ensure text is readable on background colors

2. **Technical Guidelines**
   - Set appropriate priority values (1-10)
   - Use ISO date formats
   - Test campaigns in staging environment first

3. **Design Guidelines**
   - Use high contrast color combinations
   - Test on different screen sizes
   - Keep discount percentages realistic

## Quick Reference

### Sample Campaign JSON
```json
{
  "title": "Holiday Special",
  "subtitle": "30% Off Winter Classes",
  "description": "Get ready for winter with discounted classes!",
  "backgroundColor": "#2E7D32",
  "textColor": "#FFFFFF",
  "discountPercentage": 30,
  "discountCode": "WINTER30",
  "validFrom": "2025-12-01T00:00:00.000Z",
  "validUntil": "2025-12-31T23:59:59.000Z",
  "isActive": true,
  "priority": 8,
  "targetAudience": "all",
  "actionType": "discount"
}
```

### cURL Example
```bash
curl -X POST http://*************:3000/api/campaigns \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Summer Camp",
    "subtitle": "Early Bird 40% Off",
    "backgroundColor": "#FF9800",
    "textColor": "#FFFFFF",
    "discountPercentage": 40,
    "validFrom": "2025-06-01T00:00:00.000Z",
    "validUntil": "2025-08-31T23:59:59.000Z",
    "isActive": true,
    "priority": 9
  }'
```

## Support

For technical issues or questions:
1. Check backend logs in `classZ_Backend`
2. Verify Flutter console output
3. Test API endpoints with Postman or curl
4. Check MongoDB for data consistency 