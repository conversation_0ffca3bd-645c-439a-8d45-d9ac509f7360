# ClassZ App Review System Documentation

This document outlines the functionality and architecture of the parent review system within the ClassZ application.

## 1. Overview

The review system allows parents to provide feedback on class sessions their children have attended. Reviews can be initiated in two main ways:
1.  **Automatic Popups:** Triggered by the system at specific points in a course progression.
2.  **"My Reviews" Screen:** A dedicated section in the app where parents can see sessions pending review and view their review history.

## 2. Review Initiation

### 2.1. Automatic Popups

-   **Trigger Logic:** The `CourseProgressService` (specifically the `shouldShowReview` method) determines if a review popup should be displayed based on the current class session.
-   **Triggers & Conditions:**
    -   **First Lesson Review:**
        -   The current session is *after* the first lesson of the class (`currentSessionIndex > 0`).
        -   The first lesson review for this class and user has *not* already been shown (checked via `SharedPreferences` flag `first_lesson_review_{userId}_{classId}`).
        -   The system detects that the coach has potentially provided a performance review for the session (`_hasCoachPerformanceReview(pending)` returns true. Currently, this checks if `pending.event` exists).
        -   If all conditions met, the `first_lesson_review_{userId}_{classId}` flag is set to true in `SharedPreferences`, and the method returns `true` to show the popup.
    -   **Course Completion Review:**
        -   The current session is the *last lesson* of the class.
        -   The *first lesson review* for this class and user was *not* shown (i.e., the conditions for the first lesson review popup were not met earlier, or it's a single-session class considered as "last lesson").
        -   If conditions met, the `completion_review_{userId}_{classId}` flag is set to true in `SharedPreferences`, and the method returns `true` to show the popup.
-   **Tracking:** `SharedPreferences` is used to keep track of whether these milestone reviews have been prompted.
    -   `first_lesson_review_{userId}_{classId}`
    -   `completion_review_{userId}_{classId}`
-   **Display:** If `shouldShowReview` returns `true`, `CourseProgressService.showReviewPopup` is called.
    -   This method takes `BuildContext`, `ClassModel`, a boolean `isHalfCourse` (true for first lesson type review, false for course completion type), and an optional `DateTime? sessionDate`.
    -   It then calls either `showCombinedCourseReviewBottomSheet` (if both center and coach are present in `ClassModel`) or `showCourseReviewBottomSheet` (for individual center or coach review), passing along the parameters including `sessionDate`.
-   **Note:** There is no explicit debug flag like `_forceShowReviewDebug` mentioned in the current `CourseProgressService` code.

### 2.2. "My Reviews" Screen

-   A dedicated screen (`MyReviewsScreen`) accessible via the "My Reviews" item in the user's profile navigation.
-   This screen provides a centralized place for parents to manage reviews.

## 3. "My Reviews" Screen Details

The screen features a `TabBar` interface with two main sections:

### 3.1. "To Review" Tab

-   **Purpose:** Lists all class sessions that are identified as pending review by the parent.
-   **Data Fetching:**
    1.  The screen dispatches a `GetParentPendingReviewsEvent` to the `UserBloc` with the `parentId`.
    2.  `GetParentPendingReviewsUseCase` is invoked, which calls `UserRepository`.
    3.  `UserRepository` calls `UserDataSource`, which makes an HTTP GET request to the backend API: `/api/review/parent/:parentId/pending`.
    4.  The API returns a list of `PendingModel` objects representing potentially reviewable attended sessions.
-   **Processing & Display:**
    1.  Upon receiving `ParentPendingReviewsSuccessState` (containing `List<PendingModel>`), the `MyReviewsScreen` calls `_courseProgressService.getReviewableItemsForParent`.
    2.  The `getReviewableItemsForParent` method processes the `PendingModel` list:
        -   For each `PendingModel`, it extracts `classDetails` and `sessionEvent`.
        -   It determines the `currentSessionIndex` within the class's schedule and the `totalSessions`.
        -   It checks `SharedPreferences` flags (`first_lesson_review_{userId}_{classId}` and `completion_review_{userId}_{classId}`) to see if a review popup for these milestones was already considered shown (as per the automatic popup logic).
        -   **Conditions for adding a "First Lesson Review" `ReviewableItem`**:
            -   `currentSessionIndex > 0` (is after the first lesson).
            -   The `first_lesson_review_{userId}_{classId}` flag is *not* set in `SharedPreferences`.
            -   `_hasCoachPerformanceReview(pendingModel)` returns true.
        -   **Conditions for adding a "Course Completion Review" `ReviewableItem`**:
            -   It's the `isLastLesson`.
            -   The `completion_review_{userId}_{classId}` flag is *not* set in `SharedPreferences`.
            -   It avoids adding a duplicate if a "First Lesson Review" for this same last session was already added to the list (e.g. if a multi-session course's first lesson review conditions were met on its final session).
        -   If conditions are met, a `ReviewableItem` model is created and added to the list.
    3.  The UI then displays a `ListView` of these `ReviewableItem` objects. Each item typically shows:
        -   Class Name (e.g., `item.classModel.classProviding`)
        -   Session Date & Time (derived from `item.sessionEvent.date` and `item.sessionEvent.startTime`)
        -   Review Type (e.g., `item.reviewType` which is "First Lesson Review" or "Course Completion Review")
        -   A "Review Now" button.
-   **Action ("Review Now"):**
    1.  Pressing "Review Now" for an item calls `_courseProgressService.showReviewPopup`.
        -   `isHalfCourse` is set to `item.isFirstLessonReview`.
        -   `sessionDate` is set to `item.sessionEvent.date`.
    2.  This presents the standard review submission dialog.
    3.  After the popup is dismissed (regardless of whether a review was submitted), the screen re-dispatches `GetParentPendingReviewsEvent` to refresh the list of pending reviews.

### 3.2. "History" Tab

-   **Purpose:** Displays a list of all reviews previously submitted by the parent.
-   **Data Fetching:**
    1.  When the "History" tab becomes active (or on initial load if conditions are met after "To Review" data processing), the screen dispatches a `GetParentReviewHistoryEvent` to the `UserBloc` with the `parentId`.
    2.  `GetParentReviewHistoryUseCase` is invoked, calling `UserRepository`.
    3.  `UserRepository` calls `UserDataSource`, which makes an HTTP GET request to the backend API: `/api/review/parent/:parentId/history`.
    4.  The API returns a list of `ReviewOfChildModel` objects.
-   **Display:**
    1.  Upon receiving `ParentReviewHistorySuccessState`, the `MyReviewsScreen` updates its state with the list of `ReviewOfChildModel`.
    2.  The UI then displays each historical review, showing:
        -   Class Name (e.g., `historyItem.classId?.classProviding`)
        -   Review Submission Date (formatted `historyItem.date`)
        -   Rating (e.g., `historyItem.rating?.toStringAsFixed(1) ?? 'N/A'`, displayed as "Rating: X / 10")

## 4. Backend System

### 4.1. Key API Endpoints

-   **`POST /api/review`**:
    -   Used by the review popup (`showCombinedCourseReviewBottomSheet` or `showCourseReviewBottomSheet` via `CourseProgressService.showReviewPopup`) to submit a new review.
    -   Controller: `ReviewController.createReview`
    -   UseCase: `ReviewUseCase.createReview`
-   **`GET /api/review/parent/:parentId/pending`**:
    -   Fetches a list of `PendingModel` objects representing class sessions that the specified parent *might* need to review. The frontend service (`CourseProgressService`) then filters these further based on `SharedPreferences` flags.
    -   Controller: `ReviewController.getPendingReviewsByParentId`
    -   UseCase: `ReviewUseCase.getPendingReviewsByParentId`
-   **`GET /api/review/parent/:parentId/history`**:
    -   Fetches all reviews previously submitted by the specified parent.
    -   Controller: `ReviewController.getParentReviewHistory`
    -   UseCase: `ReviewUseCase.getParentReviewHistory`
-   **`GET /api/review/:classId/:revieweeId/:revieweeType/:date`**:
    -   (Existing endpoint) Checks if a specific review already exists. This might be used by the popup system to prevent duplicate prompts if a review was already submitted, although the primary client-side duplicate prevention for popups relies on `SharedPreferences`.

### 4.2. Backend Logic for Pending Reviews (`ReviewUseCase.getPendingReviewsByParentId`)

1.  Fetch Children for the `parentId`.
2.  For each child, find all unique `classId`s they have attended.
3.  For each `classId`, get all past sessions/events.
4.  For each child + past session, check if the parent has already reviewed it.
5.  Return a list of `PendingModel`-like objects for sessions not yet reviewed by the parent.

### 4.3. Backend Logic for Review History (`ReviewUseCase.getParentReviewHistory`)

1.  Queries `ReviewRepository` for reviews matching `reviewerId` (parentId) and `reviewerType` ("Parent").
2.  Returns the list.

### 4.4. Key Backend Repositories

-   `ReviewRepository`
-   `AttendanceRepository`
-   `EventRepository`
-   `ChildRepository`

## 5. Data Models (Frontend)

-   **`ReviewableItem`**: UI-specific model for the "To Review" tab. Created by `CourseProgressService.getReviewableItemsForParent`. It contains:
    -   `classModel: ClassModel`
    -   `sessionEvent: EventModel` (from the `PendingModel`)
    -   `currentSessionIndex: int`
    -   `totalSessions: int`
    -   `isFirstLessonReview: bool` (determines if it's a first lesson type or completion type)
    -   `reviewType: String` (e.g., "First Lesson Review", "Course Completion Review")
-   **`PendingModel`**: Represents data from `/api/review/parent/:parentId/pending`. Contains `plainClassDetails` (a `ClassModel`), `event` (an `EventModel`), `childId`, etc. It's the raw data indicating a past session attendance.
-   **`ReviewOfChildModel`**: Represents a submitted review from the `/api/review/parent/:parentId/history` endpoint. Includes `classId` (populated `ClassModel`), `childId`, `rating`, `comment`, `date` of review submission.

## 6. Frontend Data Flow (Simplified)

### 6.1. "To Review" Tab Flow:

`MyReviewsScreen` -> `UserBloc.add(GetParentPendingReviewsEvent)` -> `GetParentPendingReviewsUseCase` -> `UserRepository` -> `UserDataSource` -> **Backend API (`/api/review/parent/:parentId/pending`)** -> `UserDataSource` returns `List<PendingModel>` -> `UserRepository` -> `GetParentPendingReviewsUseCase` -> `UserBloc` emits `ParentPendingReviewsSuccessState` -> `MyReviewsScreen` calls `CourseProgressService.getReviewableItemsForParent` with `List<PendingModel>` -> `CourseProgressService` returns `List<ReviewableItem>` -> UI Update with reviewable items.

### 6.2. "History" Tab Flow:

`MyReviewsScreen` -> `UserBloc.add(GetParentReviewHistoryEvent)` -> `GetParentReviewHistoryUseCase` -> `UserRepository` -> `UserDataSource` -> **Backend API (`/api/review/parent/:parentId/history`)** -> `UserDataSource` returns `List<ReviewOfChildModel>` -> `UserRepository` -> `GetParentReviewHistoryUseCase` -> `UserBloc` emits `ParentReviewHistorySuccessState` -> `MyReviewsScreen` -> UI Update with review history.

---

This documentation should provide an updated overview of the parent review system. 