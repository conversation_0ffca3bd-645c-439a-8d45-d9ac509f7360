# Address API Documentation

This documentation outlines the Address API endpoints for managing user addresses with mapping functionality.

## Base URL

```
/api/address
```

## Authentication

Most endpoints require authentication. Include an Authorization header with a Bearer token:

```
Authorization: Bearer <token>
```

## Data Models

### Address Object

```json
{
  "_id": "string",
  "flatFloorBlock": "string",
  "buildingEstate": "string",
  "district": "string",
  "region": "string",
  "country": "string",
  "coordinates": {
    "lat": "number or null",
    "lng": "number or null"
  },
  "default": "boolean"
}
```

### Formatted Address Object (Map View)

```json
{
  "id": "string",
  "fullAddress": "string",
  "flatFloorBlock": "string",
  "buildingEstate": "string",
  "district": "string",
  "region": "string",
  "country": "string",
  "default": "boolean",
  "coordinates": {
    "lat": "number or null",
    "lng": "number or null"
  }
}
```

## Endpoints

### 1. Get All Addresses for a User

Retrieves all addresses associated with a specific user.

- **URL**: `/user/:userId`
- **Method**: `GET`
- **URL Parameters**: 
  - `userId` (required): The ID of the user

#### Success Response

- **Code**: `200 OK`
- **Content**:

```json
{
  "success": true,
  "addresses": [
    {
      "_id": "address_id_1",
      "flatFloorBlock": "G032, G/F",
      "buildingEstate": "ABC Mall",
      "district": "Causeway Bay",
      "region": "Hong Kong",
      "country": "Hong Kong",
      "coordinates": {
        "lat": 22.2799,
        "lng": 114.1880
      },
      "default": true
    },
    {
      "_id": "address_id_2",
      "flatFloorBlock": "Flat B, 7/F",
      "buildingEstate": "Ocean View",
      "district": "North Point",
      "region": "Hong Kong",
      "country": "Hong Kong",
      "coordinates": {
        "lat": 22.2913,
        "lng": 114.1950
      },
      "default": false
    }
  ]
}
```

#### Error Responses

- **Code**: `400 Bad Request`
  - **Content**: `{ "success": false, "message": "User ID is required" }`

- **Code**: `404 Not Found`
  - **Content**: `{ "success": false, "message": "User not found" }`

- **Code**: `500 Internal Server Error`
  - **Content**: `{ "success": false, "message": "An error occurred while fetching addresses", "error": "error_message" }`

---

### 2. Get Default Address for a User

Retrieves the default address for a specific user.

- **URL**: `/user/:userId/default`
- **Method**: `GET`
- **URL Parameters**: 
  - `userId` (required): The ID of the user

#### Success Response

- **Code**: `200 OK`
- **Content**:

```json
{
  "success": true,
  "address": {
    "_id": "address_id_1",
    "flatFloorBlock": "G032, G/F",
    "buildingEstate": "ABC Mall",
    "district": "Causeway Bay",
    "region": "Hong Kong",
    "country": "Hong Kong",
    "coordinates": {
      "lat": 22.2799,
      "lng": 114.1880
    },
    "default": true
  }
}
```

#### Error Responses

- **Code**: `400 Bad Request`
  - **Content**: `{ "success": false, "message": "User ID is required" }`

- **Code**: `404 Not Found`
  - **Content**: `{ "success": false, "message": "User not found" }` or 
  - **Content**: `{ "success": false, "message": "No default address found for this user" }`

- **Code**: `500 Internal Server Error`
  - **Content**: `{ "success": false, "message": "An error occurred while fetching the default address", "error": "error_message" }`

---

### 3. Add a New Address

Adds a new address for a specific user.

- **URL**: `/user/:userId`
- **Method**: `POST`
- **URL Parameters**: 
  - `userId` (required): The ID of the user
- **Request Body**:

```json
{
  "flatFloorBlock": "G032, G/F",
  "buildingEstate": "ABC Mall",
  "district": "Causeway Bay",
  "region": "Hong Kong",
  "country": "Hong Kong",
  "coordinates": {
    "lat": 22.2799,
    "lng": 114.1880
  },
  "default": true
}
```

#### Success Response

- **Code**: `201 Created`
- **Content**:

```json
{
  "success": true,
  "message": "Address added successfully",
  "user": {
    "_id": "user_id",
    "email": "<EMAIL>",
    "location": [
      {
        "_id": "address_id_1",
        "flatFloorBlock": "G032, G/F",
        "buildingEstate": "ABC Mall",
        "district": "Causeway Bay",
        "region": "Hong Kong",
        "country": "Hong Kong",
        "coordinates": {
          "lat": 22.2799,
          "lng": 114.1880
        },
        "default": true
      }
    ],
    "other_user_fields": "..."
  }
}
```

#### Error Responses

- **Code**: `400 Bad Request`
  - **Content**: `{ "success": false, "message": "User ID is required" }` or
  - **Content**: `{ "success": false, "message": "Address data is incomplete. Please provide flatFloorBlock, buildingEstate, district, and region" }`

- **Code**: `404 Not Found`
  - **Content**: `{ "success": false, "message": "User not found" }`

- **Code**: `500 Internal Server Error`
  - **Content**: `{ "success": false, "message": "An error occurred while adding the address", "error": "error_message" }`

---

### 4. Update an Address

Updates an existing address for a specific user.

- **URL**: `/user/:userId/:addressId`
- **Method**: `PUT`
- **URL Parameters**: 
  - `userId` (required): The ID of the user
  - `addressId` (required): The ID of the address to update
- **Request Body**:

```json
{
  "flatFloorBlock": "G032, G/F (Updated)",
  "buildingEstate": "ABC Mall (Updated)",
  "district": "Causeway Bay",
  "region": "Hong Kong",
  "coordinates": {
    "lat": 22.2799,
    "lng": 114.1880
  },
  "default": true
}
```

#### Success Response

- **Code**: `200 OK`
- **Content**:

```json
{
  "success": true,
  "message": "Address updated successfully",
  "user": {
    "_id": "user_id",
    "email": "<EMAIL>",
    "location": [
      {
        "_id": "address_id_1",
        "flatFloorBlock": "G032, G/F (Updated)",
        "buildingEstate": "ABC Mall (Updated)",
        "district": "Causeway Bay",
        "region": "Hong Kong",
        "country": "Hong Kong",
        "coordinates": {
          "lat": 22.2799,
          "lng": 114.1880
        },
        "default": true
      }
    ],
    "other_user_fields": "..."
  }
}
```

#### Error Responses

- **Code**: `400 Bad Request`
  - **Content**: `{ "success": false, "message": "User ID and Address ID are required" }` or
  - **Content**: `{ "success": false, "message": "Update data is required" }`

- **Code**: `404 Not Found`
  - **Content**: `{ "success": false, "message": "User not found" }` or
  - **Content**: `{ "success": false, "message": "Address not found" }`

- **Code**: `500 Internal Server Error`
  - **Content**: `{ "success": false, "message": "An error occurred while updating the address", "error": "error_message" }`

---

### 5. Delete an Address

Deletes an address for a specific user.

- **URL**: `/user/:userId/:addressId`
- **Method**: `DELETE`
- **URL Parameters**: 
  - `userId` (required): The ID of the user
  - `addressId` (required): The ID of the address to delete

#### Success Response

- **Code**: `200 OK`
- **Content**:

```json
{
  "success": true,
  "message": "Address deleted successfully",
  "user": {
    "_id": "user_id",
    "email": "<EMAIL>",
    "location": [],
    "other_user_fields": "..."
  }
}
```

#### Error Responses

- **Code**: `400 Bad Request`
  - **Content**: `{ "success": false, "message": "User ID and Address ID are required" }`

- **Code**: `404 Not Found`
  - **Content**: `{ "success": false, "message": "User not found" }` or
  - **Content**: `{ "success": false, "message": "Address not found" }`

- **Code**: `500 Internal Server Error`
  - **Content**: `{ "success": false, "message": "An error occurred while deleting the address", "error": "error_message" }`

---

### 6. Set an Address as Default

Sets a specific address as the default address for a user.

- **URL**: `/user/:userId/:addressId/default`
- **Method**: `PUT`
- **URL Parameters**: 
  - `userId` (required): The ID of the user
  - `addressId` (required): The ID of the address to set as default

#### Success Response

- **Code**: `200 OK`
- **Content**:

```json
{
  "success": true,
  "message": "Default address updated successfully",
  "user": {
    "_id": "user_id",
    "email": "<EMAIL>",
    "location": [
      {
        "_id": "address_id_1",
        "flatFloorBlock": "G032, G/F",
        "buildingEstate": "ABC Mall",
        "district": "Causeway Bay",
        "region": "Hong Kong",
        "country": "Hong Kong",
        "coordinates": {
          "lat": 22.2799,
          "lng": 114.1880
        },
        "default": true
      },
      {
        "_id": "address_id_2",
        "flatFloorBlock": "Flat B, 7/F",
        "buildingEstate": "Ocean View",
        "district": "North Point",
        "region": "Hong Kong",
        "country": "Hong Kong",
        "coordinates": {
          "lat": 22.2913,
          "lng": 114.1950
        },
        "default": false
      }
    ],
    "other_user_fields": "..."
  }
}
```

#### Error Responses

- **Code**: `400 Bad Request`
  - **Content**: `{ "success": false, "message": "User ID and Address ID are required" }`

- **Code**: `404 Not Found`
  - **Content**: `{ "success": false, "message": "User not found" }` or
  - **Content**: `{ "success": false, "message": "Address not found" }`

- **Code**: `500 Internal Server Error`
  - **Content**: `{ "success": false, "message": "An error occurred while setting the default address", "error": "error_message" }`

---

### 7. Get Address with Map Data

Retrieves a formatted address with map data for a specific user. If no addressId is provided, returns the default address or the first available address.

- **URL**: `/user/:userId/map`
- **Method**: `GET`
- **URL Parameters**: 
  - `userId` (required): The ID of the user

#### Success Response

- **Code**: `200 OK`
- **Content**:

```json
{
  "success": true,
  "address": {
    "id": "address_id_1",
    "fullAddress": "G032, G/F, ABC Mall, Causeway Bay, Hong Kong, Hong Kong",
    "flatFloorBlock": "G032, G/F",
    "buildingEstate": "ABC Mall",
    "district": "Causeway Bay",
    "region": "Hong Kong",
    "country": "Hong Kong",
    "default": true,
    "coordinates": {
      "lat": 22.2799,
      "lng": 114.1880
    }
  }
}
```

#### Error Responses

- **Code**: `400 Bad Request`
  - **Content**: `{ "success": false, "message": "User ID is required" }`

- **Code**: `404 Not Found`
  - **Content**: `{ "success": false, "message": "User not found" }` or
  - **Content**: `{ "success": false, "message": "No addresses found for this user" }`

- **Code**: `500 Internal Server Error`
  - **Content**: `{ "success": false, "message": "An error occurred while fetching the address with map", "error": "error_message" }`

---

### 8. Get Specific Address with Map Data

Retrieves a specific address with map data for a specific user.

- **URL**: `/user/:userId/:addressId/map`
- **Method**: `GET`
- **URL Parameters**: 
  - `userId` (required): The ID of the user
  - `addressId` (required): The ID of the address to retrieve

#### Success Response

- **Code**: `200 OK`
- **Content**:

```json
{
  "success": true,
  "address": {
    "id": "address_id_1",
    "fullAddress": "G032, G/F, ABC Mall, Causeway Bay, Hong Kong, Hong Kong",
    "flatFloorBlock": "G032, G/F",
    "buildingEstate": "ABC Mall",
    "district": "Causeway Bay",
    "region": "Hong Kong",
    "country": "Hong Kong",
    "default": true,
    "coordinates": {
      "lat": 22.2799,
      "lng": 114.1880
    }
  }
}
```

#### Error Responses

- **Code**: `400 Bad Request`
  - **Content**: `{ "success": false, "message": "User ID is required" }`

- **Code**: `404 Not Found`
  - **Content**: `{ "success": false, "message": "User not found" }` or
  - **Content**: `{ "success": false, "message": "Address not found" }`

- **Code**: `500 Internal Server Error`
  - **Content**: `{ "success": false, "message": "An error occurred while fetching the address with map", "error": "error_message" }`

## Integration with Frontend

### Flutter Integration

To integrate with a Flutter frontend, you would need to:

1. Create API service classes to handle HTTP requests to these endpoints
2. Set up models that match the JSON response structure
3. Use a mapping library like Google Maps for Flutter to display the address coordinates

Example Flutter code for fetching and displaying an address with map:

```dart
import 'package:http/http.dart' as http;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:convert';

class AddressService {
  final String baseUrl = 'http://localhost:3000/api/address';
  final String token;

  AddressService({required this.token});

  Future<Map<String, dynamic>> getAddressWithMap(String userId) async {
    final response = await http.get(
      Uri.parse('$baseUrl/user/$userId/map'),
      headers: {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to load address: ${response.body}');
    }
  }
}

// In your widget
Future<void> loadAndDisplayAddress() async {
  try {
    final addressService = AddressService(token: 'your_auth_token');
    final result = await addressService.getAddressWithMap('user_id');
    
    if (result['success'] && result['address'] != null) {
      final address = result['address'];
      final lat = address['coordinates']['lat'];
      final lng = address['coordinates']['lng'];
      
      // If coordinates exist, show on map
      if (lat != null && lng != null) {
        // Show Google Map with marker at these coordinates
        final mapController = GoogleMapController(...);
        final position = LatLng(lat, lng);
        
        mapController.animateCamera(CameraUpdate.newLatLngZoom(position, 15));
        
        // Add marker
        setState(() {
          markers.add(
            Marker(
              markerId: MarkerId(address['id']),
              position: position,
              infoWindow: InfoWindow(
                title: address['buildingEstate'],
                snippet: address['fullAddress'],
              ),
            ),
          );
        });
      } else {
        // Handle case where coordinates are not available
        displayAddressText(address['fullAddress']);
      }
    }
  } catch (e) {
    // Handle error
    print('Error loading address: $e');
  }
}
``` 