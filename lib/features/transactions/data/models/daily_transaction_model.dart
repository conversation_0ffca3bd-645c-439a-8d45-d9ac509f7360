// lib/features/transactions/data/models/daily_transaction_model.dart

class DailyTransactionModel {
  final String id;
  final double amount;
  final String status; // This corresponds to 'paymentStatus' from backend
  final String type;
  final DateTime createdAt;
  final String userId; // Coach's baseUser ID

  final String coachDisplayName;
  final Map<String, dynamic>?
      coachMainImage; // Backend returns an object like { url: String, contentType: String }

  // Placeholders that will be populated from backend in the future, or if backend provides them
  final String studentName;
  final String className;
  final String location;
  final bool isSen;

  DailyTransactionModel({
    required this.id,
    required this.amount,
    required this.status,
    required this.type,
    required this.createdAt,
    required this.userId,
    required this.coachDisplayName,
    this.coachMainImage,
    required this.studentName,
    required this.className,
    required this.location,
    required this.isSen,
  });

  factory DailyTransactionModel.fromJson(Map<String, dynamic> json) {
    return DailyTransactionModel(
      id: json['_id'] as String? ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      status:
          json['paymentStatus'] as String? ?? json['status'] as String? ?? '',
      type: json['type'] as String? ?? '',
      createdAt: DateTime.tryParse(json['createdAt'] as String? ?? '') ??
          DateTime.now(),
      userId: json['userId'] as String? ??
          '', // This is the coach's baseUser ID from the transaction record
      coachDisplayName: json['coachDisplayName'] as String? ?? 'Unknown Coach',
      coachMainImage: json['coachMainImage'] is Map<String, dynamic>
          ? json['coachMainImage'] as Map<String, dynamic>
          : null,
      studentName: json['studentName'] as String? ?? 'N/A',
      className: json['className'] as String? ?? 'N/A',
      location: json['location'] as String? ?? 'N/A',
      isSen: json['isSen'] as bool? ?? false,
    );
  }

  // Method to get coach's main image URL if available
  String? get coachMainImageUrl => coachMainImage?['url'] as String?;
}
