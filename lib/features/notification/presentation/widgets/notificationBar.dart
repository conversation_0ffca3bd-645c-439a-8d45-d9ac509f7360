import 'package:class_z/core/imports.dart';

Widget notificationBar({
  required BuildContext context,
  NotificationEntity? notification,
  VoidCallback? onTap,
  VoidCallback? onAccept,
  VoidCallback? onDecline,
}) {
  bool isRequest = onAccept != null && onDecline != null;
  final coachImage = notification?.data?['coachProfileImage'] as String?;
  final centerImage = notification?.data?['centerMainImage'] as String?;
  final mainImage = notification?.data?['mainImage'] as String?;
  final senderImage = notification?.data?['senderImage']?['url'] as String?;

  String finalImage = '';
  if (senderImage != null && senderImage.isNotEmpty) {
    finalImage = senderImage;
  } else if (coachImage != null && coachImage.isNotEmpty) {
    finalImage = coachImage;
  } else if (centerImage != null && centerImage.isNotEmpty) {
    finalImage = centerImage;
  } else if (mainImage != null && mainImage.isNotEmpty) {
    finalImage = mainImage;
  }

  return Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 14.w),
        child: Column(
          children: [
            Row(
              children: [
                CustomImageBuilder(
                  imagePath: imageStringGenerator(imagePath: finalImage),
                  height: 60.w,
                  width: 60.w,
                  borderRadius: 60.w,
                ),
                SizedBox(
                  width: 16.w,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: customtext(
                              context: context,
                              newYear: notification?.title ??
                                  notification?.data?['title'] ??
                                  '',
                              font: 15.sp,
                              weight: FontWeight.w500,
                            ),
                          ),
                          customtext(
                            context: context,
                            newYear: timeAgo(notification?.createdAt),
                            font: 12.sp,
                            weight: FontWeight.w400,
                          ),
                        ],
                      ),
                      customtext(
                        context: context,
                        newYear: notification?.body ?? '',
                        maxLines: 3,
                        font: 12.sp,
                        weight: FontWeight.w400,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (isRequest)
              Padding(
                padding: EdgeInsets.only(top: 12.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Button(
                      onPressed: onDecline,
                      buttonText: "Decline",
                      color: AppPallete.whiteColor,
                      textColorFinal: AppPallete.red,
                      width: 100.w,
                      height: 35.h,
                      border: Border.all(color: AppPallete.red),
                    ),
                    SizedBox(width: 16.w),
                    Button(
                      onPressed: onAccept,
                      buttonText: "Accept",
                      color: AppPallete.secondaryColor,
                      width: 100.w,
                      height: 35.h,
                    ),
                  ],
                ),
              ),
            SizedBox(
              height: 8.h,
            ),
            customDivider(),
            SizedBox(
              height: 8.h,
            ),
          ],
        ),
      ),
    ),
  );
}
