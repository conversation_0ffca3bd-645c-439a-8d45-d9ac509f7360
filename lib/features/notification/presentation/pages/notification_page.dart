import 'package:class_z/core/imports.dart';

class NotificationPage extends StatefulWidget {
  final String userId;
  const NotificationPage({super.key, required this.userId});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> {
  final notificationService = locator<Notificationservice>();
  bool _isPermissionGranted = true;
  List<NotificationEntity> notifications = [];

  @override
  void initState() {
    super.initState();

    final bloc = context.read<NotificationBloc>();

    if (bloc.cachedNotifications.isEmpty) {
      bloc.add(GetNotificationsEvent(userId: widget.userId));
    } else {
      notifications = bloc.cachedNotifications;
    }

    _checkNotificationPermission();
    notificationService.FirebaseInit(context);
    notificationService.handleTerminateMessage(context);

    // Delay device token registration to allow Firebase to fully initialize
    Future.delayed(Duration(seconds: 2), () {
      if (mounted) {
        _getAndRegisterDeviceTokenWithRetry();
      }
    });
  }

  void _checkNotificationPermission() async {
    final settings = await FirebaseMessaging.instance.getNotificationSettings();
    setState(() {
      _isPermissionGranted =
          settings.authorizationStatus == AuthorizationStatus.authorized;
      if (!_isPermissionGranted) {
        notificationService.requestNotificationPermission(context);
      }
    });
  }

  Future<void> _refreshNotifications() async {
    print('hitting');
    context
        .read<NotificationBloc>()
        .add(GetNotificationsEvent(userId: widget.userId));
  }

  @override
  Widget build(BuildContext context) {
    if (!_isPermissionGranted) return _buildPermissionDeniedMessage(context);

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _refreshNotifications,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomAppBar(title: "Notification", leading: customBackButton()),
              SizedBox(height: 19.h),
              Padding(
                padding: EdgeInsets.only(left: 25.w),
                child: customtext(
                  context: context,
                  newYear: "Notifications",
                  font: 17.sp,
                  weight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 22.h),
              BlocConsumer<NotificationBloc, NotificationState>(
                listener: (context, state) {
                  if (state is NotificationLoading) {
                    loadingState(context: context);
                  } else {
                    hideLoadingDialog(context);
                  }

                  if (state is NotificationError) {
                    errorState(context: context, error: state.message);
                  }
                },
                builder: (context, state) {
                  if (state is NotificationLoaded) {
                    notifications = state.notifications;
                  } else {
                    // Fallback to cached if not loaded state
                    notifications =
                        context.read<NotificationBloc>().cachedNotifications;
                  }

                  if (notifications.isEmpty) {
                    return SizedBox(
                      height: MediaQuery.of(context).size.height * 0.5,
                      child: Center(
                        child: customtext(
                          context: context,
                          newYear: 'No new notification',
                          font: 20,
                          weight: FontWeight.w600,
                        ),
                      ),
                    );
                  }

                  return ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: notifications.length,
                    itemBuilder: (context, index) {
                      final notification = notifications[index];
                      final isRequest = notification.data?['url'] == '/assign';
                      return notificationBar(
                        context: context,
                        notification: notification,
                        onTap: () => _handleNotificationTap(notification),
                        onAccept: isRequest
                            ? () {
                                final requestId =
                                    notification.data?['requestId'];
                                if (requestId != null) {
                                  context.read<RequestBloc>().add(
                                        UpdateRequestStatusEvent(
                                          requestId: requestId,
                                          status: 'approved',
                                        ),
                                      );
                                }
                              }
                            : null,
                        onDecline: isRequest
                            ? () {
                                final requestId =
                                    notification.data?['requestId'];
                                if (requestId != null) {
                                  context.read<RequestBloc>().add(
                                        UpdateRequestStatusEvent(
                                          requestId: requestId,
                                          status: 'rejected',
                                        ),
                                      );
                                }
                              }
                            : null,
                      );
                    },
                  );
                },
              ),
              BlocListener<RequestBloc, RequestState>(
                listener: (context, state) {
                  if (state is RequestLoading) {
                    loadingState(context: context);
                  } else {
                    hideLoadingDialog(context);
                  }

                  if (state is RequestStatusUpdatedSuccessState) {
                    successState(
                        context: context, title: 'Request status updated');
                    _refreshNotifications();
                  }

                  if (state is RequestError) {
                    errorState(context: context, error: state.message);
                  }
                },
                child: const SizedBox.shrink(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleNotificationTap(NotificationEntity notification) {
    final data = notification.data;

    // Add this line for debugging
    print('Tapped notification data: ${notification.data}');

    if (data?['url'] == '/request' || data?['url'] == '/assign') {
      showAlertDialogWithAcceptReject(
        context,
        data!,
        id: notification.id ?? '',
      );
    } else if (data?['url'] == '/announcement') {
      print(data);
      NavigatorService.pushNamed(
        AppRoutes.announcementMessage,
        arguments: {
          "id": data?['announcementId'],
          "className": notification.title,
        },
      );
    }
  }

  Widget _buildPermissionDeniedMessage(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "Notification permissions are denied.",
              style: TextStyle(fontSize: 18),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                AppSettings.openAppSettings();
              },
              child: Text("Enable Notifications"),
            ),
          ],
        ),
      ),
    );
  }

  // Get device token and register it with the backend with enhanced retry logic
  Future<void> _getAndRegisterDeviceTokenWithRetry() async {
    const maxAttempts = 3;
    const delay = Duration(seconds: 5);

    for (int attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        print('Attempting to get device token (attempt ${attempt + 1})...');
        final token = await notificationService.getDeviceToken();
        print('Device Token: $token');

        if (token.isNotEmpty) {
          // Register the token with the backend
          if (mounted) {
            context.read<NotificationBloc>().add(
                  CheckDeviceTokenEvent(
                    deviceToken: token,
                    id: widget.userId,
                  ),
                );
          }
          print('Device token successfully registered');
          return; // Success, exit the retry loop
        } else {
          print('Device token is empty on attempt ${attempt + 1}');
          if (attempt < maxAttempts - 1) {
            print('Retrying in ${delay.inSeconds} seconds...');
            await Future.delayed(delay);
          }
        }
      } catch (e) {
        print(
            'Error getting or registering device token on attempt ${attempt + 1}: $e');
        if (attempt < maxAttempts - 1) {
          print('Retrying in ${delay.inSeconds} seconds...');
          await Future.delayed(delay);
        }
      }
    }

    print('Failed to get device token after $maxAttempts attempts');
  }
}
