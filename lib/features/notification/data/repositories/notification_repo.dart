// Implement the interface in the NotificationRepository class

import 'package:class_z/core/error/exception.dart';
import 'package:class_z/core/error/failure.dart';
import 'package:class_z/features/notification/data/data_source/notification_data_source.dart';
import 'package:class_z/features/notification/data/models/notificationModel.dart';
import 'package:class_z/features/notification/domain/entity/notification.dart';
import 'package:class_z/features/notification/domain/repositories/notification_repo_domain.dart';
import 'package:dartz/dartz.dart';

class NotificationRepository extends INotificationRepository {
  final NotificationRemoteDataSource remoteDataSource;

  NotificationRepository(this.remoteDataSource);
  Future<Either<Failure, List<NotificationEntity>>> getNotifications(
      String userId) async {
    try {
      final List<NotificationModel> notifications =
          await remoteDataSource.getNotifications(userId);

      // Ensure NotificationModel is converted to NotificationEntity
      final List<NotificationEntity> notificationEntities = notifications;

      return Right(notificationEntities);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> readNotification(String notificationId) async {
    try {
      await remoteDataSource.readNotification(notificationId);
      return Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteNotification(
      String notificationId) async {
    try {
      var response = await remoteDataSource.deleteNotification(notificationId);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> checkDeviceToken(
      String deviceToken, String id) async {
    try {
      await remoteDataSource.checkDeviceToken(deviceToken, id);
      return Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
