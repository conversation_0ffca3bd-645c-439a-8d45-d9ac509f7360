import 'package:class_z/core/error/exception.dart';
import 'package:class_z/core/network/api_service.dart';
import 'package:class_z/features/notification/data/models/notificationModel.dart';

abstract class NotificationRemoteDataSource {
  Future<void> checkDeviceToken(String deviceToken, String id);
  Future<List<NotificationModel>> getNotifications(String userId);
  Future<void> readNotification(String notificationId);
  Future<bool> deleteNotification(String notificationId);
}

class NotificationRemoteDataSourceImpl implements NotificationRemoteDataSource {
  final ApiService apiService;

  NotificationRemoteDataSourceImpl({required this.apiService});

  @override
  Future<List<NotificationModel>> getNotifications(String userId) async {
    print('hitting again');
    //   UserModel? user = locator<SharedRepository>().getUserData();
    // String? id;
    // if (userType == 'coach') {
    //   id = user?.data?.coach?.id;
    // } else if (userType == 'center') {
    //   id = user?.data?.center?.id;
    // } else if (userType == 'parent') {
    //   id = user?.data?.parent?.id;
    // } else {
    //   id = user?.data?.owner?.id;
    // }
    // print(id);
    final response = await apiService.get('/api/notification/get?id=$userId');
    if (response is List) {
      return response.map((json) => NotificationModel.fromJson(json)).toList();
    } else {
      throw ServerException();
    }
  }

  @override
  Future<void> readNotification(String notificationId) async {
    await apiService.post('/api/notification/$notificationId', {});
  }

  @override
  Future<bool> deleteNotification(String notificationId) async {
    print(notificationId);
    final response = await apiService
        .delete('/api/coach/deleteNotification/$notificationId');
    if (response == true)
      return true;
    else {
      throw ServerException();
    }
  }

  @override
  Future<void> checkDeviceToken(String deviceToken, String id) async {
    // Validate token and userId before sending to backend
    if (deviceToken.isEmpty) {
      print(
          'Warning: Attempted to send empty device token to backend. Request skipped.');
      print(
          'This usually indicates APNS token retrieval failed. Check Firebase configuration and iOS permissions.');
      return; // Skip the API call if token is empty
    }

    if (id.isEmpty) {
      print(
          'Warning: Attempted to send device token with empty userId. Request skipped.');
      print('User ID is required for device token registration.');
      return; // Skip the API call if userId is empty
    }

    try {
      print('Registering device token for user $id...');
      await apiService.post(
          '/api/notification/token', {'token': deviceToken, 'userId': id});
      print('Successfully updated device token for user $id');
    } catch (e) {
      print('Error updating device token for user $id: $e');
      print('Device token registration failed, but app will continue normally');
      // Don't rethrow - we don't want app to crash due to notification token issues
    }
  }
}
