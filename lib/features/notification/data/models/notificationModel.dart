import 'dart:convert';

import 'package:class_z/features/notification/domain/entity/notification.dart';

List<NotificationModel> notificationModelFromJson(String str) =>
    List<NotificationModel>.from(
        json.decode(str).map((x) => NotificationModel.fromJson(x)));

String notificationModelToJson(List<NotificationModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class NotificationModel extends NotificationEntity {
  NotificationModel({
    String? id,
    String? title,
    String? body,
    Map<String, dynamic>? data,
    String? userId,
    bool? isRead,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? v,
  }) : super(
          id: id,
          title: title,
          body: body,
          data: data,
          userId: userId,
          isRead: isRead,
          createdAt: createdAt,
          updatedAt: updatedAt,
          v: v,
        );

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      NotificationModel(
        id: json["_id"],
        title: json["title"],
        body: json["body"],
        data: json["data"] == null
            ? null
            : Map<String, dynamic>.from(
                json["data"]), // Parse data as Map<String, dynamic>
        userId: json["userId"],
        isRead: json["isRead"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "title": title,
        "body": body,
        "data": data, // Use DataModel's toJson()
        "userId": userId,
        "isRead": isRead,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
      };
  @override
  String toString() {
    return 'NotificationModel{id: $id, title: $title, body: $body, data: $data, userId: $userId, isRead: $isRead, createdAt: $createdAt, updatedAt: $updatedAt}';
  }
}
