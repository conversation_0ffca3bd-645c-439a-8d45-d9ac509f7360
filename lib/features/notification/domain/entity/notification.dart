// lib/domain/entities/notification_entity.dart
class NotificationEntity {
  String? id;
  String? title;
  String? body;
  Map<String, dynamic>? data;
  String? userId;
  bool? isRead;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? v;

  NotificationEntity({
    this.id,
    this.title,
    this.body,
    this.data,
    this.userId,
    this.isRead,
    this.createdAt,
    this.updatedAt,
    this.v,
  });
}
