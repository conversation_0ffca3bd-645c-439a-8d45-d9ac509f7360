import 'package:class_z/core/error/failure.dart';
import 'package:class_z/features/notification/domain/entity/notification.dart';
import 'package:dartz/dartz.dart';

abstract class INotificationRepository {
  Future<Either<Failure, void>> checkDeviceToken(String deviceToken,String id);
  Future<Either<Failure, List<NotificationEntity>>> getNotifications(
      String userId);
  Future<Either<Failure, void>> readNotification(String notificationId);
  Future<Either<Failure, bool>> deleteNotification(String notificationId);
}
