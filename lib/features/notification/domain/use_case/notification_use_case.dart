import 'package:class_z/features/notification/data/repositories/notification_repo.dart';
import 'package:class_z/features/notification/domain/entity/notification.dart';
import 'package:dartz/dartz.dart';
import 'package:class_z/core/error/failure.dart';
import 'package:class_z/core/use_cases/use_case.dart';

// 🔹 1. Get All Notifications
class GetNotifications extends UseCase<List<NotificationEntity>, String> {
  final NotificationRepository repository;

  GetNotifications(this.repository);

  @override
  Future<Either<Failure, List<NotificationEntity>>> call(String userId) async {
    return await repository.getNotifications(userId);
  }
}

// 🔹 3. Mark Notification as Read
class ReadNotification extends UseCase<void, String> {
  final NotificationRepository repository;

  ReadNotification(this.repository);

  @override
  Future<Either<Failure, void>> call(String notificationId) {
    return repository.readNotification(notificationId);
  }
}

// 🔹 4. Delete Notification
class DeleteNotification extends UseCase<bool, String> {
  final NotificationRepository repository;

  DeleteNotification(this.repository);

  @override
  Future<Either<Failure, bool>> call(String notificationId) async {
    print(notificationId);
    return await repository.deleteNotification(notificationId);
  }
}

class CheckDeviceToken {
  final NotificationRepository repository;

  CheckDeviceToken(this.repository);

  Future<Either<Failure, void>> call(String deviceToken, String id) async {
    return await repository.checkDeviceToken(deviceToken, id);
  }
}
