// lib/auth/data/repositories/auth_repository_impl.dart

import 'package:class_z/features/authentications/data/dataSources/auth_data_sources.dart';
import 'package:class_z/core/common/data/models/user_model.dart';

import 'package:class_z/features/authentications/domain/repositories/auth_repo_domain.dart';
import 'package:dartz/dartz.dart';

class AuthRepositoryImpl implements AuthRepositoryDomain {
  final AuthDataSource dataSource;

  AuthRepositoryImpl(this.dataSource);

  @override
  Future<Either<String, UserModel>> signIn(
      String email, String password, String? type) async {
    try {
      final userModel = await dataSource.signin(email, password, type);

      return Right(userModel); // Return the user on success
    } catch (e) {
      return Left(e.toString()); // Return a failure on error
    }
  }

  @override
  Future<bool> signUp(
      String email, String password, String type, String otp) async {
    try {
      final userModel = await dataSource.signup(email, password, type, otp);

      return userModel;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<UserModel> parentInfoComplete(
      {required Map<String, dynamic> updateData}) async {
    try {
      return await dataSource.parentInfoComplete(updateData: updateData);
    } catch (e) {
      rethrow;
    }
  }

  // @override
  // Future<User> parentInfoComplete(
  //     {String? email,
  //     String? password,
  //     required String type,
  //     required String nickname,
  //     required String fullname,
  //     required String phone,
  //     required String location,
  //     File? image,
  //     String? referal}) async {
  //   try {
  //     final userModel = await dataSource.userInfoComplete(email, password,
  //         nickname, fullname, phone, location, type, image, referal);
  //     final imageUrl = userModel.user.parentData?.image!.url;
  //     final imageContentType = userModel.user.parentData?.image?.contentType;
  //     return User(
  //         email: userModel.user.email,
  //         password: userModel.user.password,
  //         nickname: userModel.user.parentData?.nickname,
  //         fullname: userModel.user.parentData?.fullname,
  //         phone: userModel.user.parentData?.phone,
  //         location: userModel.user.parentData?.location,
  //         type: userModel.user.parentData?.type,
  //         image: UserImage(url: imageUrl, contentType: imageContentType),
  //         referal: userModel.user..parentData?.referal);
  //   } catch (e) {
  //     rethrow;
  //   }
  // }

  @override
  Future<bool> tokenValidation(String token) async {
    try {
      if (token.isEmpty) {
        print("Empty token in auth repository");
        return false;
      }
      final auth = await dataSource.tokenvValidity(token);
      return auth;
    } catch (e) {
      print("Error in auth repository tokenValidation: ${e.toString()}");
      return false;
    }
  }

  @override
  Future<bool> sendOTP({required String email}) async {
    try {
      return await dataSource.sendOTP(email: email);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> verifyOTP({required String email, required String otp}) async {
    try {
      return await dataSource.verifyOTP(email: email, otp: otp);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> resetPassword(
      {required String email, required String password}) {
    try {
      return dataSource.resetPassword(email: email, password: password);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<Either<String, UserModel>> updateUser(
      String id, Map<String, dynamic> data) async {
    try {
      final userModel = await dataSource.updateUser(id, data);
      return Right(userModel);
    } catch (e) {
      return Left(e.toString());
    }
  }

  // @override
  // Future<User> coachInfoComplete(
  //     {required String type,
  //     required String nickname,
  //     required String fullname}) async {
  //   try {
  //     final CoachModel =
  //         await dataSource.coachInfoComplete(nickname, fullname, type);
  //     return Coach(
  //         id: CoachModel.coach.id,
  //         email: CoachModel.coach.email,
  //         password: CoachModel.coach.password,
  //         type: type,
  //         nickname: nickname,
  //         isComplete: CoachModel.coach.isComplete,
  //         createdAt: CoachModel.coach.createdAt,
  //         updatedAt: CoachModel.coach.updatedAt,
  //         v: CoachModel.coach.v);
  //   } catch (e) {
  //     rethrow;
  //   }
  // }
}
