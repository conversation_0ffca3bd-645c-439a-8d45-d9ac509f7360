import 'dart:io';
import 'dart:convert';

import 'package:class_z/core/widgets/form_data.dart';
import 'package:class_z/features/roles/coach/data/models/coach_model.dart';
import 'package:class_z/core/constants/string.dart';
import 'package:class_z/core/utils/shared_repo.dart';
import 'package:http/http.dart' as http;
import 'package:class_z/core/common/data/models/user_model.dart';
import 'package:dio/dio.dart' as dio;

abstract class AuthDataSource {
  Future<UserModel> signin(String email, String password, String? type);
  Future<bool> signup(String email, String password, String type, String otp);
  Future<UserModel> parentInfoComplete(
      {required Map<String, dynamic> updateData});
  Future<CoachModel> coachInfoComplete(
    String nickname,
    String fullname,
    String type,
  );
  Future<bool> tokenvValidity(String token);
  Future<bool> sendOTP({required String email});
  Future<bool> verifyOTP({required String email, required String otp});
  Future<bool> resetPassword({required String email, required String password});
  Future<UserModel> updateUser(String id, Map<String, dynamic> data);
}

class AuthDataSourceImpl implements AuthDataSource {
  final http.Client client;
  final dio.Dio dioClient;
  final SharedRepository sharedRepository;
  final String device = AppText.device;

  AuthDataSourceImpl(
      {required this.client,
      required this.sharedRepository,
      required this.dioClient});

  @override
  Future<UserModel> signin(String email, String password, String? type) async {
    try {
      final uri = Uri.parse("$device/api/auth/signin");
      print(email + password);
      print(type);
      final response = await http.post(
        uri,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'email': email, 'password': password, 'type': type}),
      );
      print(response);
      // Check if the response status is successful
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        final jsonData = json.decode(response.body);
        print("Sign in successful:");

        // Create UserModel from response
        final user = UserModel.fromJson(jsonData as Map<String, dynamic>);
        // Store user data and token
        print(user.data?.parent?.location);
        await sharedRepository.storeUserData(user);
        await sharedRepository.storeToken(user.token);
        return user;
      } else {
        // Log and throw a more descriptive error
        print("Sign in failed: ${response.body}");
        throw ('${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print("Error during sign in: ${e.toString()}");
      throw Exception(
          e.toString()); // Rethrow the exception for higher-level handling
    }
  }

  @override
  Future<bool> signup(
      String email, String password, String type, String otp) async {
    try {
      String uri = "$device/api/auth/signup";
      print("Attempting signup with:");
      print("Email: $email");
      print("Password length: ${password.length}");
      print("Type: $type");
      print("OTP length: ${otp.length}");
      print("URI: $uri");

      final response = await client.post(
        Uri.parse(uri),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(
            {'email': email, 'password': password, 'type': type, 'otp': otp}),
      );

      print("Signup response status: ${response.statusCode}");
      print("Signup response body: ${response.body}");

      if (response.statusCode >= 200 && response.statusCode < 300) {
        print("Signup successful!");
        return true;
      } else {
        print("Signup failed with status code: ${response.statusCode}");
        print("Response body: ${response.body}");
        throw Exception(
            'Failed to sign up: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print("Exception during signup: $e");
      throw Exception('Failed to sign up: $e');
    }
  }

  Future<UserModel> parentInfoComplete(
      {required Map<String, dynamic> updateData}) async {
    try {
      // Debug info
      print("Update data keys: ${updateData.keys.toList()}");

      // Check if image file exists and is valid
      if (updateData.containsKey('mainImage') &&
          updateData['mainImage'] is File) {
        final File imageFile = updateData['mainImage'];
        if (!imageFile.existsSync()) {
          throw Exception('Image file does not exist: ${imageFile.path}');
        }

        // Check file size
        final fileSize = imageFile.lengthSync();
        if (fileSize > 5 * 1024 * 1024) {
          throw Exception(
              'Image file is too large (${fileSize ~/ 1024 ~/ 1024}MB). Maximum size is 5MB.');
        }

        print("Image file size: ${fileSize ~/ 1024}KB");
      }

      final formData = await buildFormData(updateData);

      // Retrieve user token and data
      String? token = sharedRepository.getToken();
      UserModel? user = sharedRepository.getUserData();
      String? id = user?.data?.parent?.id;

      if (id == null || id.isEmpty) {
        throw Exception('User ID is missing or invalid');
      }

      String uri = "$device/api/auth/update/$id";
      print("Sending request to: $uri");

      // Add onSendProgress to track upload progress
      var response = await dioClient.put(
        uri,
        data: formData,
        options: dio.Options(
          headers: {
            'auth-token': token,
            'Content-Type': 'multipart/form-data', // Ensure Content-Type is set
          },
          // Add timeout for the request
          sendTimeout: const Duration(minutes: 2),
          receiveTimeout: const Duration(minutes: 2),
        ),
        onSendProgress: (sent, total) {
          final progress = (sent / total * 100).toStringAsFixed(2);
          print('Upload progress: $progress% ($sent/$total bytes)');
        },
      );

      if (response.statusCode! >= 200 && response.statusCode! <= 300) {
        print("Update successful. Response data: ${response.data}");

        // Parse the response into UserModel
        UserModel updatedUser = UserModel.fromJson(response.data);
        await sharedRepository.storeToken(updatedUser.token);
        await sharedRepository.storeUserData(updatedUser);

        return updatedUser;
      } else {
        print("Update failed with status code: ${response.statusCode}");
        print("Response data: ${response.data}");
        throw Exception('Failed to update user info: ${response.statusCode}');
      }
    } catch (e) {
      if (e is dio.DioException) {
        print("DioError during parent info update: ${e.message}");
        print("DioError type: ${e.type}");
        if (e.response != null) {
          print("Response status code: ${e.response?.statusCode}");
          print("Response data: ${e.response?.data}");
        }
        if (e.type == dio.DioExceptionType.badResponse &&
            e.response?.statusCode == 500) {
          throw Exception(
              'Server error (500). The image may be too large or in an unsupported format.');
        }
      } else {
        print("Error during parent info update: $e");
      }
      throw Exception('Failed to complete parent info update: $e');
    }
  }

  @override
  Future<CoachModel> coachInfoComplete(
    String nickname,
    String fullname,
    String type,
  ) async {
    try {
      String? token = sharedRepository.getToken();
      String uri = "$device/api/auth/update";
      var request = http.MultipartRequest('POST', Uri.parse(uri))
        ..headers['auth-token'] = token ?? "";
      request.fields['nickname'] = nickname;
      request.fields['type'] = type;

      // Send request
      var response = await request.send();

      // Handle response
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        var responseBody = await response.stream.bytesToString();
        final jsonData = json.decode(responseBody);
        return CoachModel.fromJson(jsonData);
      } else {
        var responseBody = await response.stream.bytesToString();
        throw Exception('Failed to update coach info: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<bool> tokenvValidity(String token) async {
    try {
      // Check if token is empty or null
      if (token.isEmpty) {
        print("Token is empty or null");
        return false;
      }

      print("Checking token: $token");

      String uri = '$device/api/auth/istokenvalid';
      final response = await http.post(
        Uri.parse(uri),
        headers: {'Content-Type': 'application/json', 'auth-token': token},
      );

      print("Response Code: ${response.statusCode}");
      print("Response Body: ${response.body}");

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final jsonData = json.decode(response.body);

        // Ensure the response is a boolean
        if (jsonData is bool) {
          return jsonData;
        } else {
          print("Unexpected response format: $jsonData");
          return false;
        }
      } else {
        print("Token validation failed: ${response.statusCode}");
        return false;
      }
    } catch (e) {
      print("Error in token validation: ${e.toString()}");
      return false; // Return false if any error occurs
    }
  }

  @override
  Future<bool> sendOTP({required String email}) async {
    try {
      String uri = '$device/api/otp?email=$email';
      print("Sending OTP to email: $email");
      print("OTP request URL: $uri");

      final response = await client.get(Uri.parse(uri));

      print("OTP request status code: ${response.statusCode}");
      print("OTP request response body: ${response.body}");

      if (response.statusCode >= 200 && response.statusCode <= 300) {
        print("OTP sent successfully!");
        return true;
      } else {
        print("Failed to send OTP. Status code: ${response.statusCode}");
        print("Response body: ${response.body}");
        return false;
      }
    } catch (e) {
      print("Exception during OTP sending: $e");
      throw Exception('Sending OTP failed: $e');
    }
  }

  @override
  Future<bool> verifyOTP({required String email, required String otp}) async {
    try {
      print('here is verify');
      print({'email': email, 'otp': otp});
      String uri = '$device/api/otp';
      final response = await client.post(Uri.parse(uri),
          headers: {'Content-Type': 'application/json'},
          body: json.encode({'email': email, 'otp': otp}));
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        print(response.body);
        return true;
      } else
        return false;
    } catch (e) {
      throw Exception('sending otp failed');
    }
  }

  @override
  Future<bool> resetPassword(
      {required String email, required String password}) async {
    try {
      String uri = '$device/api/auth/newpassword';
      print(" email: $email");
      print(" password: $password");
      final response = await client.put(Uri.parse(uri),
          headers: {'Content-Type': 'application/json'},
          body: json.encode({'email': email, 'password': password}));
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        return true;
      } else {
        throw Exception(
            'Failed to reset password: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Password reset failed: $e');
    }
  }

  Future<UserModel> updateUser(String id, Map<String, dynamic> data) async {
    try {
      String? token = sharedRepository.getToken();
      String uri = "$device/api/auth/update/$id";

      final response = await client.put(
        Uri.parse(uri),
        headers: {
          'Content-Type': 'application/json',
          'auth-token': token ?? '',
        },
        body: json.encode(data),
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final jsonData = json.decode(response.body);
        final user = UserModel.fromJson(jsonData as Map<String, dynamic>);
        await sharedRepository.storeUserData(user);
        await sharedRepository.storeToken(user.token);
        return user;
      } else {
        throw ('${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }
}
