import 'package:class_z/core/imports.dart';

class FirstTimeLogIn extends StatelessWidget {
  final String? portalType;
  const FirstTimeLogIn({this.portalType, super.key});

  @override
  Widget build(BuildContext context) {
    if (portalType == 'parent') {
      Future.microtask(() => NavigatorService.popAndPushNamed(AppRoutes.signUp,
          arguments: 'parent'));
      return const Scaffold(backgroundColor: AppPallete.secondaryColor);
    }
    return Scaffold(
      backgroundColor: AppPallete.secondaryColor,
      body: Padding(
        padding: EdgeInsets.only(left: 21.w, top: 122.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            customtext(
                context: context,
                newYear: "Are You a",
                font: 30.sp,
                weight: FontWeight.w500,
                color: Colors.white),
            SizedBox(
              height: 25.h,
            ),
            if (portalType != 'coach') ...[
              _container(
                  context: context,
                  title: "Parent",
                  text:
                      "For individuals seeking coaching in interest-based classes",
                  onTap: () {
                    NavigatorService.popAndPushNamed(AppRoutes.signUp,
                        arguments: 'parent');
                  }),
              SizedBox(
                height: 25.h,
              ),
            ],
            _container(
                context: context,
                title: "Coach",
                text:
                    "For individuals aiming to provide flexible student coaching",
                onTap: () {
                  NavigatorService.popAndPushNamed(AppRoutes.signUp,
                      arguments: 'coach');
                }),
            SizedBox(
              height: 25.h,
            ),
            _container(
                context: context,
                title: "Centre Owner",
                text: "For centre owners seeking expansion opportunities",
                onTap: () {
                  NavigatorService.popAndPushNamed(AppRoutes.signUp,
                      arguments: 'owner');
                })
          ],
        ),
      ),
    );
  }

  Widget _container(
      {required BuildContext context,
      required String title,
      required String text,
      required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 388.w,
        height: 139.h,
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
        child: Padding(
          padding: EdgeInsets.only(top: 35.h, left: 18.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              customtext(
                  context: context,
                  newYear: title,
                  font: 20.sp,
                  weight: FontWeight.w700,
                  color: AppPallete.secondaryColor),
              SizedBox(
                height: 12.h,
              ),
              SizedBox(
                width: 354.w,
                child: customtext(
                  context: context,
                  newYear: text,
                  font: 15.sp,
                  weight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
