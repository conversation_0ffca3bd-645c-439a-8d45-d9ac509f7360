// ignore_for_file: file_names

import 'package:class_z/core/imports.dart';

class LogIn extends StatefulWidget {
  const LogIn({super.key});

  @override
  State<LogIn> createState() => _LogInState();
}

class _LogInState extends State<LogIn> {
  late final TextEditingController emailController;
  late final TextEditingController passwordController;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  bool parent = true;

  @override
  void initState() {
    super.initState();
    emailController = TextEditingController();
    passwordController = TextEditingController();
  }

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  void _handleLogin(BuildContext context) {
    if (formKey.currentState!.validate()) {
      final email = emailController.text.trim();
      final password = passwordController.text.trim();
      final type = parent ? 'parent' : 'other';
      print(type);
      BlocProvider.of<AuthBloc>(context).add(
        SignInEvent(
          email: email,
          password: password,
          type: type,
        ),
      );
    }
  }

  void _handleAuthState(BuildContext context, AuthState state) {
    if (state is AuthLoading) {
      Future.delayed(Duration.zero, () {
        loadingState(context: context);
      });
    } else {
      hideLoadingDialog(context);
    }

    if (state is AuthError) {
      errorState(context: context, error: state.message);
    }

    if (state is AuthSignInSuccess) {
      final user = state.user.data;
      if (user != null) {
        // Fetch latest user data
        BlocProvider.of<AuthBloc>(context).add(GetUserEvent());

        if (user.parent != null) {
          if (user.parent?.fullname == null)
            NavigatorService.pushNamed(AppRoutes.userProfile);
          else
            NavigatorService.pushNamed(AppRoutes.homePage);
        } else if (user.owner != null) {
          if (user.owner?.displayName == null) {
            NavigatorService.pushNamed(AppRoutes.ownerRegistration);
          } else
            NavigatorService.pushNamed(AppRoutes.ownerMain);
        } else if (user.center != null || user.coach != null) {
          print("coachName ${user.coach?.displayName}");
          if (user.coach?.displayName == null ||
              user.coach?.displayName == "") {
            NavigatorService.pushNamed(AppRoutes.coachRegistration);
          } else
            NavigatorService.pushNamed(AppRoutes.openProfile);
        }
      } else {
        NavigatorService.pushNamed(AppRoutes.logIn);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: BlocListener<AuthBloc, AuthState>(
          listener: _handleAuthState,
          child: Container(
            decoration: BoxDecoration(
              gradient: GradientProvider.getLinearGradient(),
            ),
            child: Stack(
              children: [
                PositionedItemWidget(
                  top: 50.h,
                  left: 0,
                  right: 0,
                  opacity: 1,
                  child: Align(
                    alignment: Alignment.center,
                    child: Image.asset(
                      ImagePath.logo,
                      height: 391.h,
                      width: 614.w,
                    ),
                  ),
                ),
                PositionedItemWidget(
                  top: 300.h,
                  left: 0,
                  right: 31.w,
                  opacity: 1,
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 31.h, vertical: 94.w),
                    child: Form(
                      key: formKey,
                      child: Column(
                        children: [
                          if (!parent) ...[
                            Align(
                              alignment: Alignment.centerLeft,
                              child: customtext(
                                context: context,
                                newYear: 'Coach/ Centre Login',
                                font: 20.sp,
                                weight: FontWeight.w400,
                                color: AppPallete.darkGrey,
                              ),
                            ),
                            SizedBox(height: 8.h),
                          ],
                          AuthField(
                            controller: emailController,
                            hintText: 'Enter your email',
                            isObsecureText: false,
                            keyboard: TextInputType.emailAddress,
                            onTap: () {},
                            validator: (value) => value == null || value.isEmpty
                                ? 'Please enter your email'
                                : null,
                            suffixIcon: const Icon(Icons.email),
                          ),
                          SizedBox(height: 8.h),
                          AuthField(
                            controller: passwordController,
                            hintText: "Enter your password",
                            maxline: 1,
                            keyboard: TextInputType.visiblePassword,
                            isObsecureText: true,
                            validator: (value) => value == null || value.isEmpty
                                ? 'Password is required'
                                : null,
                          ),
                          SizedBox(height: 17.h),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              GestureDetector(
                                onTap: () => setState(() => parent = !parent),
                                child: customtext(
                                  context: context,
                                  newYear: parent
                                      ? "I'm a coach/ centre"
                                      : "I'm a parent",
                                  font: 13.sp,
                                  weight: FontWeight.w400,
                                  color: AppPallete.secondaryColor,
                                ),
                              ),
                              InkWell(
                                onTap: () => NavigatorService.pushNamed(
                                    AppRoutes.forgetPassword),
                                child: customtext(
                                  context: context,
                                  newYear: "Forget Password",
                                  font: 13.sp,
                                  weight: FontWeight.w400,
                                  color: AppPallete.greyWord,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 67.h),
                          Button(
                            buttonText: "Sign In",
                            color: AppPallete.buttonColor,
                            onPressed: () => _handleLogin(context),
                          ),
                          SizedBox(height: 71.h),
                          RichText(
                            text: TextSpan(
                              text: "Don't have an account? ",
                              style: const TextStyle(
                                color: AppPallete.greyWord,
                                fontWeight: FontWeight.w400,
                              ),
                              children: [
                                TextSpan(
                                  text: "Sign Up",
                                  style: const TextStyle(
                                    color: AppPallete.secondaryColor,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = () {
                                      NavigatorService.pushNamed(
                                        AppRoutes.firstTimelogIn,
                                        arguments: parent ? 'parent' : 'coach',
                                      );
                                    },
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
