import 'package:class_z/core/imports.dart';

class ResetPassword extends StatefulWidget {
  final String email;
  const ResetPassword({
    required this.email,
    super.key,
  });

  @override
  State<ResetPassword> createState() => _ResetPasswordState();
}

class _ResetPasswordState extends State<ResetPassword> {
  final formKey = GlobalKey<FormState>();
  final confirmPasswordController = TextEditingController();
  final passwordController = TextEditingController();
  bool isLength = false;
  bool isNumber = false;
  bool isUpperCase = false;
  @override
  void initState() {
    // TODO: implement initState
    passwordController.addListener(checkPassword);
    super.initState();
  }

  void checkPassword() {
    final password = passwordController.text;
    setState(() {
      isLength = password.length >= 8;
      isNumber = password.contains(RegExp(r'[0-9]'));
      isUpperCase = password.contains(RegExp(r'[A-Z]'));
    });
  }

  @override
  void dispose() {
    confirmPasswordController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  Widget _buildPasswordRequirement(String text, bool isMet) {
    return customtext(
        context: context,
        newYear: text,
        font: 13.sp,
        color: isMet ? AppPallete.green : AppPallete.change,
        weight: FontWeight.w400);
  }

  @override
  Widget build(BuildContext context) {
    final isPasswordValid = isLength && isNumber && isUpperCase;
    return Container(
      color: AppPallete.white,
      child: SafeArea(
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: AppPallete.white,
          appBar: CustomAppBar(
              title: "Reset Password",
              leading: CustomIconButton(
                icon: Icons.arrow_back_ios,
                onPressed: () {
                  NavigatorService.goBack();
                },
              )),
          body: BlocListener<AuthBloc, AuthState>(
            listener: (context, state) {
              if (state is AuthLoading)
                loadingState(context: context);
              else
                hideLoadingDialog(context);
              if (state is AuthError) {
                errorState(context: context, error: state.message);
              }
              if (state is ResetPasswordSuccessState) {
                if (state.success == true) {
                  NavigatorService.pushNamed(AppRoutes.logIn);
                }
              }
            },
            child: Form(
              key: formKey,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: Column(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 44.h), // Adjusted top padding
                          customtext(
                              context: context,
                              newYear: "Reset password",
                              font: 24.sp,
                              weight: FontWeight.bold),
                          SizedBox(
                            height: 20.h,
                          ),
                          AuthField(
                            hintText: "Input your password",
                            controller: passwordController,
                            isObsecureText: true,
                          ),
                          SizedBox(height: 16.h),
                          AuthField(
                            hintText: "Confirm your password",
                            controller: confirmPasswordController,
                            isObsecureText: true,
                          ),
                          SizedBox(height: 24.h),
                          _buildPasswordRequirement(
                              'Your password must be at least 8 characters long.',
                              isLength),
                          SizedBox(height: 12.h),
                          _buildPasswordRequirement(
                              'Your password must contain at least 1 number.',
                              isNumber),
                          SizedBox(height: 12.h),
                          _buildPasswordRequirement(
                              'Your password must contain at least 1 uppercase letter.',
                              isUpperCase),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(bottom: 16.h),
                      child: Center(
                        child: Button(
                          buttonText: 'Confirm',
                          color: AppPallete.buttonColor,
                          onPressed: () {
                            if (formKey.currentState!.validate()) {
                              if (!isPasswordValid) {
                                errorState(
                                    context: context,
                                    error:
                                        'Password does not meet all requirements.');
                                return;
                              }
                              if (passwordController.text ==
                                  confirmPasswordController.text) {
                                context.read<AuthBloc>().add(ResetPasswordEvent(
                                    email: widget.email,
                                    password: passwordController.text));
                              } else {
                                errorState(
                                    context: context,
                                    error: "Password does not match");
                              }
                            } else {
                              errorState(
                                  context: context,
                                  error: "Please fill all fields");
                            }
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
