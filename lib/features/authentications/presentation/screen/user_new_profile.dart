import 'package:class_z/core/imports.dart';

class UserNewProfile extends StatefulWidget {
  const UserNewProfile({super.key});

  @override
  State<UserNewProfile> createState() => _UserProfileState();
}

class _UserProfileState extends State<UserNewProfile> {
  final formKey = GlobalKey<FormState>();
  final nameController = TextEditingController();
  final fullnameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final referalController = TextEditingController();
  final locationController = TextEditingController();
  final labelController = TextEditingController();

  final ImagePicker _picker = ImagePicker();
  XFile? _image;

  @override
  void dispose() {
    nameController.dispose();
    fullnameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    referalController.dispose();
    locationController.dispose();
    labelController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final XFile? pickedImage =
        await _picker.pickImage(source: ImageSource.gallery);
    if (pickedImage != null) {
      setState(() {
        _image = pickedImage;
      });
    }
  }

  List<String> countryCodes = ['+852', '+99'];
  List<String> hongKongDistricts = [
    'Central and Western District',
    'Eastern District',
    'Southern District',
    'Wan Chai District',
    'Kowloon City District',
    'Kwun Tong District',
    'Sham Shui Po District',
    'Wong Tai Sin District',
    'Yau Tsim Mong District',
    'Tsuen Wan District',
    'Tuen Mun District',
    'Sai Kung District',
    'Sha Tin District',
    'Kwai Tsing District',
    'North District',
    'Yuen Long District',
    'Islands District'
  ];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "My Profile",
        leading: CustomIconButton(
          icon: Icons.arrow_back_ios,
          onPressed: () => NavigatorService.goBack(),
        ),
      ),
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthLoading) {
            loadingState(context: context);
          } else {
            hideLoadingDialog(context);
          }
          if (state is ParnetAccountInfoSuccessState) {
            NavigatorService.pushNamed(AppRoutes.homePage);
          }
          if (state is AuthError) {
            errorState(context: context, error: state.message);
          }
          if (state is UserLoaded) {
            loadingState(context: context);
          }
        },
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProfileImageSection(),
              _form(),
              const SizedBox(height: 70),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 70.0),
                child: _buildCreateButton(),
              ),
              const SizedBox(
                height: 30,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _form() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 21.w),
      child: Form(
        key: formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoSection('Your Information', 17.sp),
            _buildTextField('Profile Name', nameController, "Nickname"),
            _buildTextField('Full Name', fullnameController, "Fullname"),
            buildStaticEmail(context,
                locator<SharedRepository>().getParentData()?.email ?? ''),
            _buildTextField('Phone Number', phoneController, "Phone Number",
                isPhone: true),
            _buildInfoSection('Your location', 17.sp),
            _buildLocationDropdown(hongKongDistricts),
            _buildTextField('Referral Code', referalController, "Referral Code",
                optional: true),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileImageSection() {
    return SizedBox(
      height: 125.h,
      child: Stack(
        children: [
          PositionedItemWidget(
              top: 60.h, left: 0, right: 0, child: customDivider()),
          PositionedItemWidget(
            top: 0.h,
            left: 0,
            right: 0.w,
            child: Center(
              child: Container(
                height: 125.h,
                width: 125.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(125.w),
                  color: AppPallete.paleGrey,
                ),
                child: _image != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(125.w),
                        child: Image.file(
                          File(_image!.path),
                          height: 125.h,
                          width: 125.w,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Container(),
              ),
            ),
          ),
          PositionedItemWidget(
            left: 90.w,
            right: 0.w,
            bottom: 0.h,
            child: CustomIconButton(
              color: AppPallete.greyColor,
              icon: Icons.camera_alt,
              height: 29.h,
              width: 33.w,
              onPressed: _pickImage,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(String text, double fontSize) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customtext(
          context: context,
          newYear: text,
          font: fontSize,
          weight: FontWeight.w500,
        ),
        SizedBox(height: 22.h),
      ],
    );
  }

  Widget _buildTextField(
      String label, TextEditingController controller, String hintText,
      {bool isPhone = false, bool optional = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        optional == false
            ? customRequiredText(
                context: context,
                title: label,
                font: 17.sp,
                weight: FontWeight.w500,
              )
            : customtext(
                context: context,
                newYear: label,
                font: 17.sp,
                weight: FontWeight.w500),
        const SizedBox(height: 20),
        isPhone
            ? Row(
                children: [
                  SizedBox(
                    width: 65,
                    child: DropDown(
                      label: "",
                      times: ['+852', '+99'],
                      color: AppPallete.paleGrey,
                      controller: labelController,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Country Code is required';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(width: 6.w),
                  Expanded(
                    child: AuthField(
                      hintText: hintText,
                      controller: controller,
                      keyboard: TextInputType.number,
                      height: 30.h,
                      validator: optional == false
                          ? (value) {
                              if (value == null || value.isEmpty) {
                                return '$label is required';
                              }
                              return null;
                            }
                          : (value) {
                              return null; // No validation needed if optional is true
                            },
                    ),
                  ),
                ],
              )
            : AuthField(
                hintText: hintText,
                controller: controller,
                width: 387.w,
                height: 30.h,
                validator: optional == false
                    ? (value) {
                        if (value == null || value.isEmpty) {
                          return '$label is required';
                        }
                        return null;
                      }
                    : (value) {
                        return null; // No validation needed if optional is true
                      },
              ),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildLocationDropdown(List<String> locations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DropDown(
          label: "Select Location",
          times: locations,
          color: AppPallete.paleGrey,
          width: 316.w,
          height: 30.h,
          controller: locationController,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Location is required';
            }
            return null;
          },
        ),
        const SizedBox(
          height: 20,
        )
      ],
    );
  }

  Widget _buildCreateButton() {
    return Button(
      buttonText: 'Create',
      color: AppPallete.buttonColor,
      onPressed: () async {
        if (formKey.currentState!.validate()) {
          final String nickname = nameController.text;
          final String fullname = fullnameController.text;
          final String phone = "${labelController.text}${phoneController.text}";
          final String location = locationController.text;
          final String? referal =
              referalController.text.isEmpty ? null : referalController.text;
          final File? imageFile = _image != null ? File(_image!.path) : null;

          Map<String, dynamic> updateData = {
            'nickname': nickname,
            'fullname': fullname,
            'phone': phone,
            'location': location,
            'isCompleted': true,
            if (referal != null) 'referal': referal,
            if (imageFile != null) 'mainImage': imageFile,
          };

          context
              .read<AuthBloc>()
              .add(ParentInfoCompleteEvent(updateData: updateData));
        }
      },
    );
  }
}
