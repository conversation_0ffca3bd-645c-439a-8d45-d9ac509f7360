import 'package:class_z/core/imports.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    SizeConfig.init(context);
    return SafeArea(
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: GradientProvider.getLinearGradient(),
          ),
          child: Padding(
            padding: EdgeInsets.fromLTRB(18.w, 48.h, 18.w, 48.h),
            child: Column(
              children: [
                Image.asset(
                  ImagePath.logo,
                  height: 449.h,
                ),
                const Spacer(),
                <PERSON><PERSON>(
                  buttonText: "Sign In",
                  color: AppPallete.splashButtonColor,
                  onPressed: () {
                    NavigatorService.pushNamed(AppRoutes.logIn);
                  },
                ),
                <PERSON><PERSON><PERSON><PERSON>(
                  height: 18.h,
                ),
                <PERSON><PERSON>(
                  buttonText: "Sign Up",
                  textColorFinal: AppPallete.secondaryColor,
                  color: Colors.white,
                  onPressed: () {
                    NavigatorService.pushNamed(AppRoutes.firstTimelogIn);
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
