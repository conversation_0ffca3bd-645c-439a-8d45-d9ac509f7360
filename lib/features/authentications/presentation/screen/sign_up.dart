// lib/presentation/screens/sign_up.dar
import 'package:class_z/core/imports.dart';

class SignUp extends StatefulWidget {
  final String? userType;
  const SignUp({this.userType, Key? key}) : super(key: key);

  @override
  State<SignUp> createState() => _SignUpState();
}

class _SignUpState extends State<SignUp> {
  final formKey = GlobalKey<FormState>();
  final emailController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final passwordController = TextEditingController();
  bool isLength = false;
  bool isNumber = false;
  bool isUpperCase = false;
  @override
  void initState() {
    passwordController.addListener(checkPassword);
    super.initState();
  }

  void checkPassword() {
    final password = passwordController.text;
    setState(() {
      isLength = password.length >= 8;
      isNumber = password.contains(RegExp(r'[0-9]'));
      isUpperCase = password.contains(RegExp(r'[A-Z]'));
    });
  }

  @override
  void dispose() {
    emailController.dispose();
    confirmPasswordController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  Widget _buildPasswordRequirement({required String text, required bool met}) {
    return Row(
      children: [
        Icon(
          met ? Icons.check_circle : Icons.remove_circle_outline,
          color: met ? AppPallete.green : AppPallete.redWordColor,
          size: 16.sp,
        ),
        SizedBox(width: 8.w),
        customtext(
          context: context,
          newYear: text,
          font: 13.w,
          color: met ? AppPallete.green : AppPallete.redWordColor,
          weight: FontWeight.w400,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: const Text('Registration'),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
        titleTextStyle: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w700,
          color:
              Colors.white, // Changed to white for better contrast on gradient
        ),
        leading: CustomIconButton(
          icon: Icons.arrow_back_ios,
          color: Colors.white, // Changed to white for better contrast
          onPressed: () {
            NavigatorService.goBack();
          },
        ),
      ),
      body: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthLoading) {
            loadingState(context: context);
          } else {
            hideLoadingDialog(context);
          }
          if (state is AuthError) {
            errorState(context: context, error: state.message);
          }
          if (state is AuthSignUpSuccess) {
            NavigatorService.pushNamed(AppRoutes.logIn);
          }
        },
        builder: (context, state) {
          if (state is AuthSignUpLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          return Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: GradientProvider.getLinearGradient(),
            ),
            child: SafeArea(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return SingleChildScrollView(
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        minHeight: constraints.maxHeight,
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 24.w),
                        child: Form(
                          key: formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(height: 60.h), // Space from top
                                  const Text(
                                    'Create Account',
                                    style: TextStyle(
                                      fontSize: 28,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  SizedBox(height: 8.h),
                                  const Text(
                                    'Start your journey with us.',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.white70,
                                    ),
                                  ),
                                  SizedBox(height: 40.h),
                                  const Text(
                                    'Email',
                                    style: TextStyle(
                                      fontSize: 17,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white,
                                    ),
                                  ),
                                  SizedBox(height: 8.h),
                                  AuthField(
                                    hintText: "Email address",
                                    controller: emailController,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Email is required';
                                      }
                                      final emailRegex = RegExp(
                                        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
                                      );
                                      if (!emailRegex.hasMatch(value)) {
                                        return 'Enter a valid email address';
                                      }
                                      return null;
                                    },
                                  ),
                                  SizedBox(height: 20.h),
                                  const Text(
                                    'Password',
                                    style: TextStyle(
                                      fontSize: 17,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white,
                                    ),
                                  ),
                                  SizedBox(height: 8.h),
                                  AuthField(
                                    hintText: "Input your password",
                                    controller: passwordController,
                                    isObsecureText: true,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Password is required';
                                      }
                                      return null;
                                    },
                                  ),
                                  SizedBox(height: 8.h),
                                  AuthField(
                                    hintText: "Confirm your password",
                                    controller: confirmPasswordController,
                                    isObsecureText: true,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Confirmed Password is required';
                                      }
                                      if (value != passwordController.text) {
                                        return 'Passwords do not match';
                                      }
                                      return null;
                                    },
                                  ),
                                  SizedBox(height: 20.h),
                                  _buildPasswordRequirement(
                                    text: 'At least 8 characters long.',
                                    met: isLength,
                                  ),
                                  SizedBox(height: 8.h),
                                  _buildPasswordRequirement(
                                    text: 'Contains at least 1 number.',
                                    met: isNumber,
                                  ),
                                  SizedBox(height: 8.h),
                                  _buildPasswordRequirement(
                                    text:
                                        'Contains at least 1 uppercase letter.',
                                    met: isUpperCase,
                                  ),
                                ],
                              ),
                              Column(
                                children: [
                                  SizedBox(height: 20.h),
                                  Center(
                                    child: Button(
                                      buttonText: 'Register',
                                      color: AppPallete.buttonColor,
                                      onPressed: () {
                                        if (formKey.currentState!.validate()) {
                                          final email = emailController.text;
                                          final password =
                                              passwordController.text;

                                          loadingState(context: context);

                                          context
                                              .read<AuthBloc>()
                                              .add(SendOTPEvent(email: email));

                                          Future.delayed(
                                              const Duration(seconds: 1), () {
                                            hideLoadingDialog(context);

                                            NavigatorService.pushNamed(
                                                AppRoutes.verify,
                                                arguments: {
                                                  'email': email,
                                                  'password': password,
                                                  'type': widget.userType
                                                });
                                          });
                                        }
                                      },
                                    ),
                                  ),
                                  SizedBox(
                                      height: 20.h), // Padding at the bottom
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }
}
