import 'package:class_z/core/imports.dart';

class ForgetPassword extends StatefulWidget {
  const ForgetPassword({super.key});

  @override
  State<ForgetPassword> createState() => _ForgetPassWordState();
}

class _ForgetPassWordState extends State<ForgetPassword> {
  final emailController = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  bool isEmailValid = false;

  @override
  void initState() {
    super.initState();
    emailController.addListener(_validateEmail);
  }

  @override
  void dispose() {
    emailController.removeListener(_validateEmail);
    emailController.dispose();
    super.dispose();
  }

  void _validateEmail() {
    final value = emailController.text;
    final emailRegex =
        RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
    final valid = value.isNotEmpty && emailRegex.hasMatch(value);
    if (valid != isEmailValid) {
      setState(() {
        isEmailValid = valid;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: CustomAppBar(
          title: "Forget Password",
          leading: CustomIconButton(
            icon: Icons.arrow_back_ios,
            onPressed: () {
              NavigatorService.goBack();
            },
          ),
        ),
        body: BlocConsumer<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is AuthError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(state.message)),
              );
            }
          },
          builder: (context, state) {
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: 18.w),
              child: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 58.h),
                    Padding(
                      padding: EdgeInsets.only(left: 14.w),
                      child: customtext(
                        context: context,
                        newYear: 'Email',
                        font: 17.sp,
                        weight: FontWeight.w700,
                      ),
                    ),
                    SizedBox(height: 10.h),
                    AuthField(
                      hintText: "Email address",
                      controller: emailController,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return "Email address cannot be empty.";
                        }
                        final emailRegex = RegExp(
                            r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
                        if (!emailRegex.hasMatch(value)) {
                          return "Please enter a valid email address.";
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 10.h),
                    customtext(
                      context: context,
                      textAlign: TextAlign.center,
                      height: 15.51.w / 13.w,
                      newYear:
                          'Please enter your registered email address, we will get back to you with the reset password link and confirmation OTP thanks',
                      font: 13.w,
                      weight: FontWeight.w400,
                    ),
                    const Spacer(),
                    Center(
                      child: Button(
                        buttonText: "submit",
                        color: isEmailValid
                            ? AppPallete.secondaryColor
                            : AppPallete.forgetButton,
                        onPressed: isEmailValid
                            ? () {
                                if (formKey.currentState!.validate()) {
                                  context.read<AuthBloc>().add(
                                        SendOTPEvent(
                                          email: emailController.text,
                                        ),
                                      );
                                  NavigatorService.pushNamed(
                                    AppRoutes.resetPasswordVerify,
                                    arguments: emailController.text,
                                  );
                                }
                              }
                            : null, // disables button when email is invalid
                      ),
                    ),
                    SizedBox(height: 84.h),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
