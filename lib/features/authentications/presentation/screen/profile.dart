import 'package:class_z/core/imports.dart';
import 'package:class_z/features/authentications/presentation/screen/disconnect_center.dart';

class UserProfile extends StatefulWidget {
  const UserProfile({super.key});

  @override
  State<UserProfile> createState() => _UserProfileState();
}

class _UserProfileState extends State<UserProfile> {
  final notificationservices = locator<Notificationservice>();
  UserModel? userData;
  bool coach = false;
  bool center = false;
  String coachImage = '';
  String centerImage = '';
  bool _isPermissionGranted = true;
  double width = 0.0;
  bool isLoading = true;
  String errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _initializeNotifications();

    // Initialize notifications if user ID is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final userId = locator<SharedRepository>().getUserId();
      if (userId.isNotEmpty) {
        context
            .read<NotificationBloc>()
            .add(GetNotificationsEvent(userId: userId));
      }
    });
  }

  void _initializeNotifications() async {
    await notificationservices.initialize(context);
    _checkNotificationPermission();
    await notificationservices.getDeviceToken();
  }

  void _loadUserData() {
    try {
      setState(() {
        isLoading = true;
        errorMessage = '';
      });

      final sharedRepository = locator<SharedRepository>();
      userData = sharedRepository.getUserData();

      if (userData == null) {
        setState(() {
          isLoading = false;
          errorMessage = 'User data not available. Please log in.';
        });
        return;
      }

      // Check specifically for coach data
      if (userData?.data?.coach == null) {
        setState(() {
          isLoading = false;
          errorMessage = 'Coach profile not available for this user type';
        });
        return;
      }

      // Safely access properties with null checks
      coachImage = userData?.data?.coach?.mainImage?.url != null
          ? "${AppText.device}${userData?.data?.coach?.mainImage?.url}"
          : '';
      centerImage = userData?.data?.center?.mainImage?.url != null
          ? "${AppText.device}${userData?.data?.center?.mainImage?.url}"
          : '';

      // Safely check for coach center properties
      coach = userData?.data?.coach?.center?.id != null;
      center = userData?.data?.coach?.managerId != null;

      // Log successful data load
      print(
          "Coach profile data loaded successfully: ${userData?.data?.coach?.displayName}");

      setState(() {
        isLoading = false;
      });

      // Initialize only after successful data loading
      _initialize();
    } catch (e) {
      print("Error loading user data: $e");
      setState(() {
        isLoading = false;
        errorMessage = 'Error loading profile data: $e';
      });
    }
  }

  void _checkNotificationPermission() async {
    try {
      NotificationSettings settings =
          await FirebaseMessaging.instance.getNotificationSettings();
      setState(() {
        _isPermissionGranted =
            settings.authorizationStatus == AuthorizationStatus.authorized;
        if (!_isPermissionGranted) {
          notificationservices.requestNotificationPermission(context);
        }
      });
    } catch (e) {
      print("Error checking notification permission: $e");
    }
  }

  Future<void> _initialize() async {
    try {
      String? deviceToken = await locator<FirebaseService>().getDeviceToken();
      print('Device Token: $deviceToken');

      if (userData?.data?.coach?.baseUser != null) {
        context.read<NotificationBloc>().add(CheckDeviceTokenEvent(
            deviceToken: deviceToken ?? '',
            id: userData?.data?.coach?.baseUser ?? ''));
      }
    } catch (e) {
      print("Error initializing: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    width = getawidth(context: context);

    if (isLoading) {
      return WillPopScope(
        onWillPop: () async => false,
        child: Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    if (errorMessage.isNotEmpty) {
      return WillPopScope(
        onWillPop: () async => false,
        child: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Error',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 10),
                Text(errorMessage),
                SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      isLoading = true;
                      errorMessage = '';
                    });
                    _loadUserData();
                  },
                  child: Text('Retry'),
                )
              ],
            ),
          ),
        ),
      );
    }

    if (userData == null || userData?.data == null) {
      return WillPopScope(
        onWillPop: () async => false,
        child: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Profile Not Available',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pushNamed(context, AppRoutes.logIn);
                  },
                  child: Text('Login'),
                )
              ],
            ),
          ),
        ),
      );
    }

    if (userData?.data?.coach == null) {
      return WillPopScope(
        onWillPop: () async => false,
        child: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Coach Profile Not Available',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 10),
                Text('This section is only available for coach users.'),
                SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text('Go Back'),
                )
              ],
            ),
          ),
        ),
      );
    }

    return WillPopScope(
      onWillPop: () async => false,
      child: Scaffold(
        body: RefreshIndicator(
          onRefresh: _refreshData,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 21),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // SizedBox(height: 30.h),
                  BlocBuilder<NotificationBloc, NotificationState>(
                    builder: (context, state) {
                      int unreadCount = 0;

                      if (state is NotificationLoaded) {
                        unreadCount = state.notifications
                            .where((n) => n.isRead != true)
                            .length;
                      } else if (state is NotificationError) {
                        debugPrint(
                            'Error loading notifications: ${state.message}');
                      }

                      return customTopBarOnlyIcon(
                        context: context,
                        badgeCount1: unreadCount,
                        badgeCount2: 0,
                        icon2: Icons.settings,
                        onTap1: () {
                          Navigator.pushNamed(
                            context,
                            AppRoutes.notification,
                            arguments: userData?.data?.coach?.id,
                          );
                        },
                        onTap2: () {
                          Navigator.pushNamed(
                            context,
                            AppRoutes.coachSettings,
                          );
                        },
                      );
                    },
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  customtext(
                      context: context,
                      newYear:
                          'Hello ${userData?.data?.coach?.displayName ?? "User"}!',
                      font: 24,
                      weight: FontWeight.w600),
                  customtext(
                      context: context,
                      newYear: 'Nice to have you back!',
                      font: 16,
                      weight: FontWeight.w300,
                      color: AppPallete.greyWord),
                  const SizedBox(height: 24),
                  _myProfile(),
                  const SizedBox(height: 12),
                  InkWell(
                    onTap: () {
                      if (center) {
                        print('done');
                        Navigator.pushNamed(context, AppRoutes.centerMain,
                            arguments: userData?.data?.center);
                      } else {}
                    },
                    onLongPress: () {
                      if (center) {
                        print({
                          'coachId': userData?.data?.coach?.id,
                          'centerId': userData?.data?.coach?.center,
                          'type': 'manager'
                        });
                        disconnectCenterAlertDialog(context: context, data: {
                          'coachId': userData?.data?.coach?.id,
                          'centerId': userData?.data?.coach?.managerId,
                          'type': 'manager'
                        });
                      }
                    },
                    child: CustomProfileTileWidget(
                      imagePath: centerImage,
                      title: "Centre Manager",
                      exist: center,
                      connectedCenter: userData?.data?.center?.displayName ??
                          'Unknown Center',
                    ),
                  ),
                  const SizedBox(
                    height: 29,
                  ),
                  InkWell(
                    onTap: () {
                      if (coach) {
                        Navigator.pushNamed(context, AppRoutes.coachMain,
                            arguments: userData?.data?.coach);
                      } else {
                        Navigator.pushNamed(
                            context, AppRoutes.connectToYourCenter);
                      }
                    },
                    onLongPress: () {
                      if (coach) {
                        disconnectCenterAlertDialog(context: context, data: {
                          'coachId': userData?.data?.coach?.id,
                          'centerId': userData?.data?.coach?.center?.id,
                          'type': 'coach'
                        });
                      } else {
                        Navigator.pushNamed(
                            context, AppRoutes.connectToYourCenter);
                      }
                    },
                    child: CustomProfileTileWidget(
                        imagePath: coachImage,
                        title: "Coach",
                        exist: coach,
                        connectedCenter:
                            userData?.data?.coach?.center?.displayName ??
                                'Unknown Center'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _myProfile() {
    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, AppRoutes.ownerCoachProfile,
            arguments: {'coach': userData?.data?.coach});
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 21),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            color: AppPallete.white,
            boxShadow: [shadow(blurRadius: 15, xoffset: 0, yoffset: 3)]),
        child: Row(
          children: [
            CustomImageBuilder(
              borderRadius: 50,
              imagePath: coachImage,
              height: 50,
              width: 50,
            ),
            const SizedBox(width: 20),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                customtext(
                    context: context,
                    newYear: 'Coach Profile',
                    font: 20,
                    weight: FontWeight.w700),
                customtext(
                    context: context,
                    newYear: 'Manage your display profile',
                    font: 15,
                    weight: FontWeight.w400),
              ],
            )
          ],
        ),
      ),
    );
  }

  Future<void> _refreshData() async {
    try {
      setState(() {
        isLoading = true;
        errorMessage = '';
      });

      final sharedRepository = locator<SharedRepository>();
      userData = sharedRepository.getUserData();

      if (userData == null) {
        setState(() {
          isLoading = false;
          errorMessage = 'User data not available. Please log in.';
        });
        return;
      }

      // Check specifically for coach data
      if (userData?.data?.coach == null) {
        setState(() {
          isLoading = false;
          errorMessage = 'Coach profile not available for this user type';
        });
        return;
      }

      // Safely access properties with null checks
      coachImage = userData?.data?.coach?.mainImage?.url != null
          ? "${AppText.device}${userData?.data?.coach?.mainImage?.url}"
          : '';
      centerImage = userData?.data?.center?.mainImage?.url != null
          ? "${AppText.device}${userData?.data?.center?.mainImage?.url}"
          : '';

      // Safely check for coach center properties
      coach = userData?.data?.coach?.center?.id != null;
      center = userData?.data?.coach?.managerId != null;

      setState(() {
        isLoading = false;
      });

      // Re-initialize after refresh
      _initialize();
    } catch (e) {
      print("Error refreshing data: $e");
      setState(() {
        isLoading = false;
        errorMessage = 'Error refreshing profile data: $e';
      });
    }
  }
}
