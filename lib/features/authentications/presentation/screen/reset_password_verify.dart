import 'package:class_z/core/imports.dart';

class ResetPasswordVerify extends StatefulWidget {
  final String email;
  const ResetPasswordVerify({required this.email, super.key});

  @override
  State<ResetPasswordVerify> createState() => _ResetPasswordVerifyState();
}

class _ResetPasswordVerifyState extends State<ResetPasswordVerify> {
  Duration _countDuration = Duration(minutes: 5);
  late Timer _timer;
  final otpController = TextEditingController();
  
  @override
  void initState() {
    startCountdown();
    super.initState();
  }

  void _sendOTP() {
    context.read<AuthBloc>().add(SendOTPEvent(email: widget.email));
    // Reset and start countdown when manually sending OTP
    setState(() {
      _countDuration = const Duration(minutes: 5);
      startCountdown();
    });
  }

  void startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countDuration.inSeconds == 0) {
        timer.cancel();
      } else {
        setState(() {
          _countDuration -= const Duration(seconds: 1);
        });
      }
    });
  }

  String formatDuration(Duration duration) {
    String minutes = duration.inMinutes.toString().padLeft(2, '0');
    String seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        title: "Reset Password",
        subtitle: 'Enter the OTP from the mail   ',
        leading: CustomIconButton(
          icon: Icons.arrow_back_ios,
          onPressed: () {
            NavigatorService.goBack();
          },
        ),
      ),
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          print('current state $state');
          // Show loading dialog for loading states
          if (state is AuthLoading || state is VerifyLoading) {
            loadingState(context: context);
          } else {
            hideLoadingDialog(context);
          }

          // Handle other states
          if (state is AuthError) {
            errorState(context: context, error: state.message);
          }
          if (state is AuthSignUpSuccess) {
            NavigatorService.popAndPushNamed(AppRoutes.logIn);
          }
          if (state is OTPVerifiedState) {
            if (state.success == true) {
              // OTP verified successfully
              NavigatorService.popAndPushNamed(AppRoutes.resetPassword,
                  arguments: widget.email);
            } else {
              // OTP verification failed
              errorState(context: context, error: 'Invalid OTP');
            }
          }
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 31.h),
          child: Column(
            children: [
              SizedBox(
                height: 60.h,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: CustomPinCodeTextField(
                  context: context,
                  codeController: otpController,
                ),
              ),
              Center(
                child: Text(
                  "Code Expire in ${formatDuration(_countDuration)}",
                  style: TextStyle(fontSize: 15, fontWeight: FontWeight.w400),
                ),
              ),
              const Spacer(),
              Button(
                  buttonText: "Send again",
                  color: AppPallete.forgetButton,
                  onPressed: _sendOTP),
              SizedBox(
                height: 18.h,
              ),
              Button(
                buttonText: "verify",
                color: AppPallete.buttonColor,
                onPressed: () {
                  context.read<AuthBloc>().add(VerifyOTPEvent(
                      email: widget.email, otp: otpController.text));
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
