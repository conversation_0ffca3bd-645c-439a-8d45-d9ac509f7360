import 'package:class_z/core/imports.dart';

void disconnectCenterAlertDialog({
  required BuildContext context,
  required Map<String, dynamic> data,
}) {
  showDialog(
    context: context,
    builder: (context) {
      return BlocListener<CoachBloc, CoachState>(
        listener: (context, state) {
          if (state is LoadingCoachState) {
            loadingState(context: context);
          } else {
            hideLoadingDialog(context);
          }
          if (state is ErrorCoachState) {
            NavigatorService.goBack();
            errorState(context: context, error: state.message);
          }
          if (state is RemoveCenterSuccessState) {
            errorState(context: context, error: 'You have been removed');
            NavigatorService.goBack();
          }
        },
        child: AlertDialog(
          backgroundColor: Colors.white,
          actions: <Widget>[_disconnectCenterCard(context, data)],
        ),
      );
    },
  );
}

Widget _disconnectCenterCard(BuildContext context, Map<String, dynamic> data) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      const SizedBox(height: 50),
      customtext(
          context: context,
          newYear: 'Are you sure you want to disconnect?',
          font: 16,
          weight: FontWeight.w600),
      customtext(
          context: context,
          newYear: 'Permission will be revoked immediately',
          font: 14,
          weight: FontWeight.w300,
          color: AppPallete.greyWord),
      const SizedBox(height: 20),
      Row(
        children: [
          Expanded(
            child: Button(
                height: 40,
                buttonText: "Disconnect",
                color: AppPallete.greyWord,
                onPressed: () {
                  print('coming');
                  context.read<CoachBloc>().add(RemoveCenterEvent(
                      centerId: data['centerId'],
                      coachId: data['coachId'],
                      type: data['type'],
                      saveData: true));
                  // Navigator.of(context).pop();
                }),
          ),
          const SizedBox(
            width: 16,
          ),
          Expanded(
            child: Button(
                height: 40,
                buttonText: "Back",
                color: AppPallete.secondaryColor,
                textColorFinal: AppPallete.white,
                onPressed: () {
                  Navigator.of(context).pop();
                }),
          ),
        ],
      ),
    ],
  );
}
