import 'package:class_z/core/imports.dart';

class ConnectToYour<PERSON>enter extends StatefulWidget {
  const ConnectToYourCenter({super.key});

  @override
  State<ConnectToYourCenter> createState() => _ConnectToYourCenterState();
}

class _ConnectToYourCenterState extends State<ConnectToYourCenter> {
  TextEditingController searchController = TextEditingController();
  bool isIndividualEducatorCoach = false;
  bool isCheckingCoachStatus = true;

  @override
  void initState() {
    super.initState();
    _checkCoachStatus();
  }

  Future<void> _checkCoachStatus() async {
    try {
      final coachId = locator<SharedRepository>().getCoachId();
      if (coachId.isNotEmpty) {
        // Get coach info to check if they're from an individual educator center
        context.read<CoachBloc>().add(GetCoachInfoByIdEvent(coachId: coachId));
      } else {
        setState(() {
          isCheckingCoachStatus = false;
        });
      }
    } catch (e) {
      print("Error checking coach status: $e");
      setState(() {
        isCheckingCoachStatus = false;
      });
    }
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocListener<CoachBloc, CoachState>(
        listener: (context, state) {
          if (state is GetCoachInfoByIdSuccessState) {
            final coach = state.coach;
            final isFromIndividualEducator =
                coach?.center?.isFreelanceEducator ?? false;
            setState(() {
              isIndividualEducatorCoach = isFromIndividualEducator;
              isCheckingCoachStatus = false;
            });
          } else if (state is ErrorCoachState) {
            setState(() {
              isCheckingCoachStatus = false;
            });
          }
        },
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomAppBar(
                title: 'Connect to your centre',
                leading: customBackButton(),
              ),
              const SizedBox(height: 50),

              // Show loading while checking coach status
              if (isCheckingCoachStatus)
                const Center(child: CircularProgressIndicator())

              // Show restriction message for individual educators
              else if (isIndividualEducatorCoach)
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppPallete.paleGrey,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppPallete.darkGrey),
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 48,
                          color: AppPallete.darkGrey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          "Connection Not Available",
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: AppPallete.black,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 12),
                        Text(
                          "Individual educator coaches cannot connect to other centers. You are already associated with an individual educator center.",
                          style: TextStyle(
                            fontSize: 14,
                            color: AppPallete.darkGrey,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                )

              // Show normal connection interface for regular coaches
              else ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Center(
                    child: customtext(
                        context: context,
                        newYear:
                            'Link your account to your working centre to manage its operations.',
                        font: 14,
                        weight: FontWeight.w300,
                        color: AppPallete.darkGrey),
                  ),
                ),
                const SizedBox(height: 26),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: AuthField(
                    controller: searchController,
                    hintText: 'Search for your centre',
                    height: 30,
                    suffixIcon: const Icon(
                      Icons.search,
                      color: AppPallete.darkGrey,
                    ),
                    onTap: () {
                      context
                          .read<SearchBloc>()
                          .add(SearchQueryEvent(query: searchController.text));
                    },
                  ),
                ),
                const SizedBox(height: 26),
                BlocConsumer<SearchBloc, SearchState>(
                  listener: (context, state) {
                    if (state is SearchLoading)
                      loadingState(context: context);
                    else
                      hideLoadingDialog(context);
                    if (state is SearchError) {
                      errorState(context: context, error: state.error);
                    }
                  },
                  builder: (context, state) {
                    if (state is SearchSuccessState) {
                      return _showSearchData(
                          context: context, search: state.search);
                    }
                    return const Center(
                      child: Text('No data found'),
                    );
                  },
                )
              ]
            ],
          ),
        ),
      ),
    );
  }

  Widget _showSearchData({
    required BuildContext context,
    required SearchModel search,
  }) {
    if (search.centers == null || search.centers!.isEmpty) {
      return const Center(child: Text('No data found'));
    }

    return ListView.builder(
      padding: EdgeInsets.zero,
      itemCount: search.centers!.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final center = search.centers![index];
        return _CenterCard(center: center);
      },
    );
  }
}

class _CenterCard extends StatelessWidget {
  final CenterData center;

  const _CenterCard({required this.center});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 11.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CustomImageBuilder(
                borderRadius: 20,
                height: 100,
                width: 100,
                imagePath: imageStringGenerator(
                  imagePath: center.mainImage?.url ?? '',
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _CenterDetails(
                  center: center,
                  connected: false,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          customDivider(),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

class _CenterDetails extends StatefulWidget {
  final CenterData center;
  final bool connected;
  const _CenterDetails({required this.center, required this.connected});

  @override
  State<_CenterDetails> createState() => _CenterDetailsState();
}

class _CenterDetailsState extends State<_CenterDetails> {
  RequestStatus _requestStatus = RequestStatus.initial;
  bool _hasShownExistsMessage = false;
  bool _hasShownSentMessage = false;
  bool _isCurrentlyPerformingAction = false;

  @override
  void initState() {
    super.initState();
    // Defer the check with a small delay to ensure context is ready
    Future.delayed(Duration(milliseconds: 100), () {
      if (mounted) {
        _checkExistingRequest();
      }
    });
  }

  void _checkExistingRequest() {
    final coachId = locator<SharedRepository>().getCoachId();
    final centerId = widget.center.id ?? '';

    print(
        "_checkExistingRequest: coachId=$coachId, centerId=$centerId, centerName=${widget.center.displayName}");

    if (coachId.isNotEmpty && centerId.isNotEmpty) {
      try {
        context.read<RequestBloc>().add(
              CheckExistingRequestEvent(
                coachId: coachId,
                centerId: centerId,
              ),
            );
        print("CheckExistingRequestEvent dispatched successfully");
      } catch (e) {
        print("Error dispatching CheckExistingRequestEvent: $e");
      }
    } else {
      print("Skipping check: coachId or centerId is empty");
    }
  }

  String get buttonText {
    switch (_requestStatus) {
      case RequestStatus.initial:
        return "Connect";
      case RequestStatus.pending:
        return "Cancel Request";
      case RequestStatus.exists:
        return "Cancel Request";
      case RequestStatus.error:
        return "Try Again";
    }
  }

  Color get buttonColor {
    switch (_requestStatus) {
      case RequestStatus.initial:
        return AppPallete.secondaryColor;
      case RequestStatus.pending:
      case RequestStatus.exists:
        return Colors.grey[600]!;
      case RequestStatus.error:
        return Colors.red[400]!;
    }
  }

  IconData? get buttonIcon {
    switch (_requestStatus) {
      case RequestStatus.pending:
      case RequestStatus.exists:
        return Icons.close;
      case RequestStatus.error:
        return Icons.error_outline;
      default:
        return null;
    }
  }

  void _showExistsMessage() {
    if (!_hasShownExistsMessage) {
      _hasShownExistsMessage = true;
    }
  }

  void _handleRequestAction() {
    final coachId = locator<SharedRepository>().getCoachId();
    final centerId = widget.center.id ?? '';

    // Set flag to indicate this centre is performing an action
    _isCurrentlyPerformingAction = true;

    // If there's a pending request, cancel it
    if (_requestStatus == RequestStatus.exists ||
        _requestStatus == RequestStatus.pending) {
      context.read<RequestBloc>().add(
            CancelJoinRequestEvent(
              coachId: coachId,
              centerId: centerId,
            ),
          );
    }
    // Otherwise, send a new request
    else if (_requestStatus == RequestStatus.initial ||
        _requestStatus == RequestStatus.error) {
      context.read<RequestBloc>().add(
            SendJoinRequestEvent(
              coachId: coachId,
              centerId: centerId,
            ),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if this center is an individual educator center
    final isIndividualEducatorCenter =
        widget.center.isFreelanceEducator ?? false;

    if (isIndividualEducatorCenter) {
      // Show restricted message instead of connection button
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.center.displayName ?? 'Unknown Center',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              addressGenerator(address: widget.center.address),
              style: const TextStyle(
                fontSize: 12,
                color: AppPallete.darkGrey,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppPallete.paleGrey,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                "Individual Educator - Not Available for Connection",
                style: TextStyle(
                  fontSize: 12,
                  color: AppPallete.darkGrey,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }

    // Normal connection interface for regular centers
    return BlocListener<RequestBloc, RequestState>(
      listener: (context, state) {
        // Handle RequestExistenceCheckedState regardless of _isCurrentlyPerformingAction
        if (state is RequestExistenceCheckedState) {
          final currentCoachId = locator<SharedRepository>().getCoachId();
          final currentCenterId = widget.center.id ?? '';

          // Only handle this state if it's for the current centre
          if (state.coachId == currentCoachId &&
              state.centerId == currentCenterId) {
            print(
                "RequestExistenceCheckedState received for ${widget.center.displayName}: exists=${state.exists}, status=${state.status}");

            setState(() {
              if (state.exists) {
                if (state.status == 'pending') {
                  _requestStatus = RequestStatus.pending;
                } else {
                  _requestStatus = RequestStatus.exists;
                }
              } else {
                _requestStatus = RequestStatus.initial;
              }
            });
          }
          return;
        }

        // Only handle other states if this centre is currently performing an action
        if (!_isCurrentlyPerformingAction) return;

        if (state is RequestLoading) {
          loadingState(context: context);
        } else {
          hideLoadingDialog(context);

          // Reset the flag since the action is complete
          _isCurrentlyPerformingAction = false;

          if (state is RequestError) {
            if (state.message.contains("already exists")) {
              setState(() => _requestStatus = RequestStatus.exists);
              _showExistsMessage();
            } else {
              setState(() => _requestStatus = RequestStatus.error);
              errorState(context: context, error: state.message);
            }
          } else if (state is RequestSentSuccessState) {
            if (state.success == true && !_hasShownSentMessage) {
              setState(() {
                _requestStatus = RequestStatus.pending;
                _hasShownSentMessage = true;
              });
            }
          } else if (state is RequestCancelledSuccessState) {
            if (state.success == true) {
              setState(() {
                _requestStatus = RequestStatus.initial;
                _hasShownExistsMessage = false;
                _hasShownSentMessage = false;
              });
            }
          }
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: customtext(
                  context: context,
                  newYear: widget.center.displayName ?? 'Unknown Name',
                  font: 18,
                  weight: FontWeight.w700,
                  color: AppPallete.darkGrey,
                ),
              ),
              widget.connected == true
                  ? Row(
                      children: [
                        const Icon(Icons.check),
                        const SizedBox(width: 4),
                        customtext(
                          context: context,
                          newYear: 'Connected',
                          font: 15,
                          weight: FontWeight.w500,
                        ),
                      ],
                    )
                  : SizedBox(),
            ],
          ),
          textWithSvg(
            context: context,
            imagePath: ImagePath.locationSvg,
            title: addressGenerator(
              address: widget.center.address,
              condition: 'city',
            ),
            font: 15,
            weight: FontWeight.w400,
            fontColor: AppPallete.darkGrey,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              SizedBox(
                width: 100.w,
                child: customtext(
                  context: context,
                  newYear: widget.center.classZId ?? 'invalid id',
                  font: 15,
                  weight: FontWeight.w400,
                  color: AppPallete.darkGrey,
                ),
              ),
              const SizedBox(width: 50),
              Expanded(
                child: InkWell(
                  onTap: _handleRequestAction,
                  child: Container(
                    height: 30.h,
                    decoration: BoxDecoration(
                      color: buttonColor,
                      borderRadius: BorderRadius.circular(5.r),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (buttonIcon != null) ...[
                          Icon(
                            buttonIcon,
                            color: Colors.white,
                            size: 16,
                          ),
                          SizedBox(width: 6.w),
                        ],
                        Flexible(
                          child: Text(
                            buttonText,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 15.sp,
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                            softWrap: false,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

enum RequestStatus {
  initial,
  pending,
  exists,
  error,
}
