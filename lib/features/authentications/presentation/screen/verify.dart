import 'package:class_z/core/imports.dart';

class Verify extends StatefulWidget {
  final Map<String, dynamic> data;
  const Verify({required this.data, super.key});

  @override
  State<Verify> createState() => _VerifyState();
}

class _VerifyState extends State<Verify> {
  Duration _countDuration = Duration(minutes: 5);
  late Timer _timer;
  final otpController = TextEditingController();
  @override
  void initState() {
    // Removing the SendOTPEvent to prevent duplicate OTP sending
    // OTP is already sent from the sign-up screen
    // context.read<AuthBloc>().add(SendOTPEvent(email: widget.data['email']));
    startCountdown();
    super.initState();
  }

  void startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countDuration.inSeconds == 0) {
        timer.cancel();
      } else {
        setState(() {
          _countDuration -= const Duration(seconds: 1);
        });
      }
    });
  }

  String formatDuration(Duration duration) {
    String minutes = duration.inMinutes.toString().padLeft(2, '0');
    String seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        title: "Verify Account",
        subtitle: 'Enter the OTP sent to your email',
        leading: CustomIconButton(
          icon: Icons.arrow_back_ios,
          onPressed: () {
            NavigatorService.goBack();
          },
        ),
      ),
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          print('current state $state');
          // Show loading dialog for loading states
          if (state is AuthLoading || state is VerifyLoading) {
            loadingState(context: context);
          } else {
            hideLoadingDialog(context);
          }

          // Handle other states
          if (state is AuthError) {
            errorState(context: context, error: state.message);
          }
          if (state is AuthSignUpSuccess) {
            NavigatorService.popAndPushNamed(AppRoutes.logIn);
          }
          if (state is OTPVerifiedState) {
            NavigatorService.popAndPushNamed(AppRoutes.logIn);
          }
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 31.h),
          child: Column(
            children: [
              SizedBox(
                height: 60.h,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: CustomPinCodeTextField(
                  context: context,
                  codeController: otpController,
                ),
              ),
              Center(
                child: Text(
                  "Code Expire in ${formatDuration(_countDuration)}",
                  style: TextStyle(fontSize: 15, fontWeight: FontWeight.w400),
                ),
              ),
              const Spacer(),
              Button(
                buttonText: "Send again",
                color: AppPallete.forgetButton,
                onPressed: () {
                  print("Resending OTP to: ${widget.data['email']}");
                  // Reset the countdown timer
                  setState(() {
                    _countDuration = Duration(minutes: 5);
                  });
                  // Send a new OTP
                  context
                      .read<AuthBloc>()
                      .add(SendOTPEvent(email: widget.data['email']));
                  // Show feedback to user
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                        content:
                            Text("OTP sent again to ${widget.data['email']}")),
                  );
                },
              ),
              SizedBox(
                height: 18.h,
              ),
              Button(
                buttonText: "verify",
                color: AppPallete.buttonColor,
                onPressed: () {
                  // Add debug logs
                  print("Verify button pressed");
                  print("Email: ${widget.data['email']}");
                  print(
                      "Password: ${widget.data['password']?.length} characters");
                  print("Type: ${widget.data['type']}");
                  print("OTP: ${otpController.text}");

                  // Show loading state
                  loadingState(context: context);

                  // Dispatch SignUpEvent with all required data
                  context.read<AuthBloc>().add(SignUpEvent(
                      email: widget.data['email'],
                      password: widget.data['password'],
                      type: widget.data['type'],
                      otp: otpController.text));
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
