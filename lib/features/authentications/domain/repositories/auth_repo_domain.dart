// lib/auth/domain/repositories/auth_repository_domain.dart

import 'package:class_z/core/common/data/models/user_model.dart';

import 'package:dartz/dartz.dart';

abstract class AuthRepositoryDomain {
  Future<Either<String, UserModel>> signIn(
      String email, String password, String? type);
  Future<bool> signUp(String email, String password, String type, String otp);
  Future<bool> tokenValidation(String token);
  Future<UserModel> parentInfoComplete(
      {required Map<String, dynamic> updateData});
  Future<Either<String, UserModel>> updateUser(
      String id, Map<String, dynamic> data);
  Future<bool> sendOTP({required String email});
  Future<bool> verifyOTP({required String email, required String otp});
  Future<bool> resetPassword({required String email, required String password});
  // Future<User> coachInfoComplete({
  //   required String type,
  //   required String nickname,
  //   required String fullname,
  // });
}
