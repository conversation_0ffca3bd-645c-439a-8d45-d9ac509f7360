// lib/auth/domain/usecases/login_usecase.dart

import 'package:class_z/core/common/data/models/user_model.dart';

import 'package:class_z/features/authentications/domain/repositories/auth_repo_domain.dart';
import 'package:class_z/core/utils/shared_repo.dart';
import 'package:dartz/dartz.dart';

class SignInUseCase {
  final AuthRepositoryDomain repository;

  SignInUseCase(this.repository);

  Future<Either<String, UserModel>> call(
      {required String email, required String password, String? type}) async {
    return await repository.signIn(email, password, type);
  }
}

class SignUpUseCase {
  final AuthRepositoryDomain repository;

  SignUpUseCase(this.repository);

  Future<bool> call(
      {required String email,
      required String password,
      required String type,
      required String otp}) async {
    return await repository.signUp(email, password, type, otp);
  }
}

class GetUserUseCase {
  final SharedRepository repository;

  GetUserUseCase(this.repository);

  Future<UserModel?> execute() async {
    return repository.getUserData();
  }
}

class TokenValidationUseCase {
  final AuthRepositoryDomain repository;

  TokenValidationUseCase(this.repository);
  Future<bool> call({required String token}) async {
    try {
      if (token.isEmpty) {
        print("Empty token in TokenValidationUseCase");
        return false;
      }
      return await repository.tokenValidation(token);
    } catch (e) {
      print("Error in TokenValidationUseCase: ${e.toString()}");
      return false;
    }
  }
}

class ParentInfoCompleterUseCase {
  final AuthRepositoryDomain repository;

  ParentInfoCompleterUseCase({required this.repository});
  Future<UserModel> call({required Map<String, dynamic> updateData}) async {
    return await repository.parentInfoComplete(updateData: updateData);
  }
}

// class CoachInfoCompleterUseCase {
//   final AuthRepositoryDomain repository;

//   CoachInfoCompleterUseCase({required this.repository});
//   Future<Coach> call({
//     required String type,
//     required String nickname,
//     required String fullname,
//   }) async {
//     return await repository.coachInfoComplete(
//       type: type,
//       nickname: nickname,
//       fullname: fullname,
//     );
//   }
// }

class SendOTPUseCase {
  final AuthRepositoryDomain authRepositoryDomain;

  SendOTPUseCase({required this.authRepositoryDomain});
  Future<bool> call({required String email}) async {
    return await authRepositoryDomain.sendOTP(email: email);
  }
}

class VerifyOTPUseCase {
  final AuthRepositoryDomain authRepositoryDomain;

  VerifyOTPUseCase({required this.authRepositoryDomain});
  Future<bool> call({required String email, required String otp}) async {
    return await authRepositoryDomain.verifyOTP(email: email, otp: otp);
  }
}

class ResetPasswordUseCase {
  final AuthRepositoryDomain authRepositoryDomain;

  ResetPasswordUseCase({required this.authRepositoryDomain});
  Future<bool> call({required String email, required String password}) async {
    return await authRepositoryDomain.resetPassword(
        email: email, password: password);
  }
}
