// To parse this JSON data, do
//
//     final lastMessageModel = lastMessageModelFromJson(jsonString);

import 'dart:convert';

List<LastMessageModel> lastMessageModelFromJson(String str) =>
    List<LastMessageModel>.from(
        json.decode(str).map((x) => LastMessageModel.fromJson(x)));

String lastMessageModelToJson(List<LastMessageModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class LastMessageModel {
  String? oppositeId;
  String? oppositeModel;
  String? name;
  MainImage? mainImage;
  String? lastMessage;
  DateTime? lastMessageTime;
  String? status;
  String? conversationId;
  bool? isBroadcast;

  LastMessageModel({
    this.oppositeId,
    this.oppositeModel,
    this.name,
    this.mainImage,
    this.lastMessage,
    this.lastMessageTime,
    this.status,
    this.conversationId,
    this.isBroadcast,
  });

  factory LastMessageModel.fromJson(Map<String, dynamic> json) {
    final bool isBroadcast = json["isBroadcast"] == true || 
                           (json["conversationId"] != null && 
                            json["conversationId"].toString().startsWith('-'));
    
    final oppositeId = json["recipientId"] != null && json["recipientId"].toString().isNotEmpty 
      ? json["recipientId"] 
      : json["oppositeId"];
      
    return LastMessageModel(
      oppositeId: oppositeId,
      oppositeModel: json['oppositeModel'] ?? json['recipientType'],
      name: json["name"],
      mainImage: json["mainImage"] == null
          ? null
          : MainImage.fromJson(json["mainImage"]),
      lastMessage: json["lastMessage"],
      lastMessageTime: json["lastMessageTime"] != null
          ? DateTime.parse(json["lastMessageTime"])
          : (json["timestamp"] != null 
              ? DateTime.parse(json["timestamp"]) 
              : null),
      status: json["status"] == null ? null : json["status"],
      conversationId: json["conversationId"],
      isBroadcast: isBroadcast,
    );
  }

  Map<String, dynamic> toJson() => {
        "oppositeId": oppositeId,
        "oppositeModel": oppositeModel,
        "name": name,
        "mainImage": mainImage?.toJson(),
        "lastMessage": lastMessage,
        "lastMessageTime": lastMessageTime?.toIso8601String(),
        "status": status,
        "conversationId": conversationId,
        "isBroadcast": isBroadcast,
      };
      
  bool get isBroadcastConversation => isBroadcast == true || 
                                     (conversationId != null && 
                                      conversationId!.startsWith('-'));
                                      
  Map<String, dynamic> toNavigationData() {
    final bool isBroadcastChat = isBroadcastConversation;
    
    return {
      'id': isBroadcastChat ? '' : oppositeId,
      'title': name ?? 'Chat',
      'imagePath': mainImage?.url ?? '',
      'senderId': '',
      'senderType': 'user',
      'oppositeModel': oppositeModel ?? 'user',
      'isBroadcast': isBroadcastChat,
      'conversationId': conversationId,
    };
  }
}

class MainImage {
  String? url;
  String? contentType;

  MainImage({
    this.url,
    this.contentType,
  });

  factory MainImage.fromJson(Map<String, dynamic> json) => MainImage(
        url: json["url"],
        contentType: json["contentType"],
      );

  Map<String, dynamic> toJson() => {
        "url": url,
        "contentType": contentType,
      };
}
