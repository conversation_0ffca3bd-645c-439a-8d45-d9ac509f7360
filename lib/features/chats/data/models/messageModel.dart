import 'package:class_z/features/chats/domain/entity/message_entity.dart';

class MessageModel extends MessageEntity {
  MessageModel({
    String? message,
    DateTime? createdAt,
    String? id,
  }) : super(
          message: message,
          createdAt: createdAt,
          id: id,
        );

  factory MessageModel.fromJson(Map<String, dynamic> json) => MessageModel(
        message: json["message"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "createdAt": createdAt?.toIso8601String(),
        "_id": id,
      };
}
