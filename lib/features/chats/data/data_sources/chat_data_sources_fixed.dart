import 'dart:async';
import 'dart:convert';

import 'package:class_z/features/chats/data/models/chatModel.dart';
import 'package:class_z/features/chats/data/models/lastMessageModel.dart';
import 'package:socket_io_client/socket_io_client.dart' as socket_io;

abstract class ChatRepository {
  Future<void> connectSocket(String userId);
  Future<void> disconnectSocket();
  Future<void> sendMessage(ChatModel message);
  Future<List<ChatModel>> fetchMessages(Map<String, dynamic> payload);
  Stream<ChatModel> receiveMessage();
  Future<List<LastMessageModel>> getLastMessages(String id);
}

// Data Layer: Socket Data Source
class SocketDataSource {
  final String serverUrl;
  socket_io.Socket? socket;

  // Use a new stream controller for each connection to prevent "already closed" errors
  StreamController<ChatModel>? _messageController;
  bool _isDisconnecting = false;
  bool _isConnected = false;

  // Add more efficient timeouts
  final Duration connectionTimeout = const Duration(seconds: 8);
  final Duration operationTimeout = const Duration(seconds: 5);

  // Add cursor tracking for pagination
  String? _lastCursor;

  SocketDataSource(this.serverUrl);

  // Create or reset the message controller
  StreamController<ChatModel> _getMessageController() {
    if (_messageController == null || _messageController!.isClosed) {
      _messageController = StreamController<ChatModel>.broadcast();
    }
    return _messageController!;
  }

  // Safely add a message to the controller
  void _safeAddToController(ChatModel message) {
    try {
      if (!(_messageController?.isClosed ?? true) && !_isDisconnecting) {
        _messageController?.add(message);
      } else {
        print('Cannot add message to closed controller or during disconnect');
      }
    } catch (e) {
      print('Error adding message to controller: $e');
    }
  }

  Future<void> connect(String userId) async {
    // If already connected with same user, don't reconnect
    if (_isConnected && socket != null) {
      print('Already connected, skipping reconnection');
      return;
    }

    // Reset disconnecting flag when connecting
    _isDisconnecting = false;
    _isConnected = false;

    // Create a new message controller if needed
    _getMessageController();

    // Create a completer to handle connection state
    final Completer<void> connectionCompleter = Completer<void>();

    // Setup connection timeout
    Timer? connectionTimer;
    connectionTimer = Timer(connectionTimeout, () {
      if (!connectionCompleter.isCompleted) {
        print('Socket connection timeout');
        connectionCompleter.completeError(
            'Connection timeout: Could not connect to chat server');
        _safeDisconnect();
      }
    });

    // Dispose of existing socket if needed
    if (socket != null) {
      try {
        socket!.dispose();
      } catch (e) {
        print('Error disposing old socket: $e');
      }
      socket = null;
    }

    // Fix the API URL if needed
    String apiUrl = serverUrl;
    if (apiUrl.contains('classz.cod')) {
      apiUrl = apiUrl.replaceAll('classz.cod', 'classz.co');
      print('Corrected API URL typo: $apiUrl');
    }

    socket = socket_io.io(apiUrl, <String, dynamic>{
      'transports': ['websocket'],
      'query': {'userId': userId},
      'reconnection': true,
      'reconnectionDelay': 1000,
      'reconnectionAttempts': 3,
      'timeout': 8000 // 8 seconds timeout
    });

    socket?.onConnect((_) {
      print('Socket connected successfully');
      _isConnected = true;
      if (!connectionCompleter.isCompleted) {
        connectionCompleter.complete();
      }
      // Cancel timeout timer
      connectionTimer?.cancel();
    });

    socket?.onConnectError((error) {
      print('Socket connection error: $error');
      _isConnected = false;

      // Check for potential Redis-related errors
      if (error.toString().contains('Redis') ||
          error.toString().contains('cache')) {
        print(
            'Potential Redis-related error, server might be operating without caching');
        // Connection can still work without Redis, just slower
        // Don't treat this as a fatal connection error if we can establish the connection
      } else {
        // Handle as normal connection error
        if (!connectionCompleter.isCompleted) {
          connectionCompleter.completeError('Failed to connect: $error');
        }
      }

      // Cancel timeout timer
      connectionTimer?.cancel();
    });

    socket?.onError((error) {
      print('Socket error: $error');
      // Only complete if not already completed
      if (!connectionCompleter.isCompleted) {
        connectionCompleter.completeError('Socket error: $error');
      }
    });

    socket?.on('message', (data) {
      if (_isDisconnecting) {
        print('Skipping message because disconnecting');
        return;
      }

      try {
        // Safely parse and add the message
        if (data is Map<String, dynamic>) {
          final message = ChatModel.fromJson(data);
          _safeAddToController(message);
        } else if (data is Map) {
          // Convert dynamic map to string-keyed map
          final jsonMap = Map<String, dynamic>.from(data);
          final message = ChatModel.fromJson(jsonMap);
          _safeAddToController(message);
        } else {
          print('Received message in unexpected format: $data');
        }
      } catch (e) {
        print('Error processing incoming message: $e');
      }
    });

    // Add message status update handler
    socket?.on('messageStatusUpdate', (data) {
      _handleMessageStatusUpdate(data);
    });

    // Add read receipts handler
    socket?.on('messagesRead', (data) {
      _handleMessagesRead(data);
    });

    // Update messageConfirmation handler to handle duplicate detection
    socket?.on('messageConfirmation', (data) {
      if (data is Map) {
        final messageId = data['messageId'];
        final success = data['success'] ?? false;
        final status = data['status'] ?? 'sent';
        final duplicateDetected = data['duplicateDetected'] ?? false;

        if (duplicateDetected) {
          print('Server detected duplicate message: $messageId');
        }

        print(
            'Message confirmation - ID: $messageId, Status: $status, Success: $success');
      }
    });

    socket?.onDisconnect((_) {
      print('Socket disconnected');
      _isConnected = false;
      // Don't close the controller on disconnect to allow for reconnection
      // Only close it when explicitly called from disconnect()
    });

    socket?.connect();

    // Return the future to allow proper error handling
    return connectionCompleter.future;
  }

  // Add method for sending read receipts
  void markMessagesAsRead(List<String> messageIds) {
    if (!_isConnected || socket == null) {
      print('Cannot mark messages as read: Socket not connected');
      return;
    }

    socket!.emit('messageRead', {'messageIds': messageIds});
    print('Sending read receipts for ${messageIds.length} messages');
  }

  // Add method to handle message status updates
  void _handleMessageStatusUpdate(Map<String, dynamic> data) {
    final messageId = data['messageId'];
    final status = data['status'];

    if (messageId != null && status != null) {
      print('Message status update - ID: $messageId, Status: $status');
      // You could emit an event to notify listeners of the status change
    }
  }

  // Add method to handle messages read notification
  void _handleMessagesRead(Map<String, dynamic> data) {
    final messageIds = data['messageIds'] as List<dynamic>?;

    if (messageIds != null && messageIds.isNotEmpty) {
      print('Messages marked as read: ${messageIds.length} messages');
      // You could emit an event to notify listeners of the read status
    }
  }

  // Safely disconnect without causing exceptions
  void _safeDisconnect() {
    try {
      if (socket != null) {
        socket!.disconnect();
        socket!.close();
        socket!.dispose();
        socket = null;
      }
    } catch (e) {
      print('Error in safe disconnect: $e');
    }
    _isConnected = false;
  }

  Future<void> disconnect() async {
    if (_isDisconnecting) {
      print('Already disconnecting, skipping duplicate call');
      return;
    }

    // Set the disconnecting flag to prevent new messages
    _isDisconnecting = true;

    // Add a timeout to ensure disconnect completes
    final disconnectCompleter = Completer<void>();
    Timer(const Duration(seconds: 3), () {
      if (!disconnectCompleter.isCompleted) {
        disconnectCompleter.complete();
      }
    });

    try {
      // Disconnect socket first
      _safeDisconnect();

      // Then close the controller
      await Future.delayed(const Duration(milliseconds: 100));
      await _messageController?.close();
      _messageController = null;

      disconnectCompleter.complete();
    } catch (e) {
      print('Error disconnecting: $e');
      if (!disconnectCompleter.isCompleted) {
        disconnectCompleter.complete();
      }
    }

    _isDisconnecting = false;
    return disconnectCompleter.future;
  }

  void sendMessage(ChatModel message) {
    try {
      if (_isDisconnecting) {
        throw Exception('Cannot send message while disconnecting');
      }

      if (!_isConnected || socket == null) {
        throw Exception('Cannot send message: Socket not connected');
      }

      socket!.emit('sendMessage', message.toJson());
    } catch (e) {
      print('Error sending message: $e');
      throw Exception('Failed to send message: $e');
    }
  }

  Future<List<ChatModel>> fetchMessages(Map<String, dynamic> payload) async {
    final completer = Completer<List<ChatModel>>();

    // Return immediately if socket is not connected or we're disconnecting
    if (!_isConnected || _isDisconnecting || socket == null) {
      print('Cannot fetch messages: Socket not connected or disconnecting');
      return [];
    }

    // Add support for cursor-based pagination
    if (payload.containsKey('beforeTimestamp')) {
      // Use the provided timestamp for pagination
      print(
          'Fetching messages before timestamp: ${payload['beforeTimestamp']}');
    } else if (_lastCursor != null &&
        payload.containsKey('useCursor') &&
        payload['useCursor'] == true) {
      // Use the stored cursor for pagination if requested
      payload['beforeTimestamp'] = _lastCursor;
      print('Using stored cursor for pagination: $_lastCursor');
    }

    // Add timeout for fetch messages - shorter for better UX
    Timer? fetchTimer;
    fetchTimer = Timer(const Duration(seconds: 5), () {
      if (!completer.isCompleted) {
        print('Fetch messages timeout - returning empty list');
        completer.complete([]);
      }
    });

    try {
      print('Fetching messages with payload: $payload');

      // Register once handlers with proper syntax
      socket!.once('fetchedMessage', (data) {
        fetchTimer?.cancel();
        try {
          print('Received messages data type: ${data.runtimeType}');

          // CRITICAL: Return empty list for all null/empty cases to avoid crashes
          if (data == null) {
            print('Server returned null for messages, returning empty list');
            completer.complete([]);
            return;
          }

          // Additional safety - directly handle specific error cases
          if (data is String &&
              (data.contains('error') || data.contains('not found'))) {
            print('Server returned error string: $data');
            completer.complete([]);
            return;
          }

          // Handle the new response format with cursor
          if (data is Map &&
              data.containsKey('messages') &&
              data.containsKey('nextCursor')) {
            final List<dynamic>? messages = data['messages'] as List<dynamic>?;
            final String? nextCursor = data['nextCursor'] as String?;

            // Store the cursor for potential future pagination
            if (nextCursor != null) {
              _lastCursor = nextCursor;
              print('Stored cursor for pagination: $nextCursor');
            }

            if (messages == null || messages.isEmpty) {
              print(
                  'No messages in cursor-based response, returning empty list');
              completer.complete([]);
              return;
            }

            // Process the messages list
            final chatMessages = <ChatModel>[];
            for (var item in messages) {
              try {
                if (item == null) continue;

                // Convert item to a map that ChatModel can use
                Map<String, dynamic> jsonMap;
                if (item is Map<String, dynamic>) {
                  jsonMap = item;
                } else if (item is Map) {
                  jsonMap = Map<String, dynamic>.from(item);
                } else {
                  // Skip non-map items
                  continue;
                }

                // Create a ChatModel from the processed data
                chatMessages.add(ChatModel.fromJson(jsonMap));
              } catch (e) {
                print('Error parsing individual message: $e');
                // Continue with other messages
              }
            }

            print(
                'Successfully parsed ${chatMessages.length} messages from cursor-based response');
            completer.complete(chatMessages);
            return;
          }

          // Extract messages array from whatever format the server provides
          List<dynamic>? messagesList;

          // Explicitly handle every possible type with strict type checking
          if (data is List) {
            messagesList = data;
            print('Data is directly a List with ${messagesList.length} items');
          } else if (data is Map) {
            final Map<dynamic, dynamic> dataMap = data;
            // Check common patterns for message lists
            if (dataMap.containsKey('messages') &&
                dataMap['messages'] is List) {
              messagesList = dataMap['messages'] as List;
            } else if (dataMap.containsKey('data') && dataMap['data'] is List) {
              messagesList = dataMap['data'] as List;
            } else if (dataMap.containsKey('result') &&
                dataMap['result'] is List) {
              messagesList = dataMap['result'] as List;
            } else if (dataMap.containsKey('items') &&
                dataMap['items'] is List) {
              messagesList = dataMap['items'] as List;
            } else {
              // Single message case
              messagesList = [dataMap];
            }
          } else {
            // Handle any other type safely
            print('Server returned unexpected data type: ${data.runtimeType}');
            completer.complete([]);
            return;
          }

          // If we still couldn't extract a list or it's empty, return empty safely
          if (messagesList.isEmpty) {
            print('No messages extracted from response, returning empty list');
            completer.complete([]);
            return;
          }

          // Now process the messages list
          final messages = <ChatModel>[];
          for (var item in messagesList) {
            try {
              if (item == null) continue;

              // Convert item to a map that ChatModel can use
              Map<String, dynamic> jsonMap;
              if (item is Map<String, dynamic>) {
                jsonMap = item;
              } else if (item is Map) {
                jsonMap = Map<String, dynamic>.from(item);
              } else {
                // Skip non-map items
                continue;
              }

              // Create a ChatModel from the processed data
              messages.add(ChatModel.fromJson(jsonMap));
            } catch (e) {
              print('Error parsing individual message: $e');
              // Continue with other messages
            }
          }

          print('Successfully parsed ${messages.length} messages');
          completer.complete(messages);
        } catch (e) {
          print('Error parsing messages: $e, data type: ${data?.runtimeType}');
          // Return empty list instead of failing to prevent UI errors
          completer.complete([]);
        }
      });

      socket!.once('FetchedMessageError', (data) {
        fetchTimer?.cancel();
        final errorMessage = data is Map
            ? (data['error'] as String? ?? 'Unknown error')
            : 'Unknown error';
        print('Fetch message error: $errorMessage');

        // If error contains Redis-related information, try to use local storage
        if (errorMessage.contains('Redis') || errorMessage.contains('cache')) {
          print('Redis-related error detected in fetch response');
        }

        // Return empty list even on explicit errors to prevent UI crashes
        completer.complete([]);
      });

      // Emit the fetch message event
      socket!.emit('fetchMessage', payload);
    } catch (e) {
      fetchTimer.cancel();
      print('Error in fetchMessages: $e');
      // Return empty list instead of failing to prevent UI crashes
      if (!completer.isCompleted) {
        completer.complete([]);
      }
    }

    return completer.future;
  }

  Stream<ChatModel> receiveMessage() {
    // Always return a valid stream, creating a new controller if needed
    return _getMessageController().stream;
  }
}
