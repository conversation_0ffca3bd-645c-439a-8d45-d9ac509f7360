import 'dart:async';
import 'dart:convert';

import 'package:class_z/features/chats/data/models/chatModel.dart';
import 'package:class_z/features/chats/data/models/lastMessageModel.dart';
import 'package:socket_io_client/socket_io_client.dart' as socket_io;
// Redis performance monitor removed
// import 'package:class_z/features/chats/utils/redis_performance_monitor.dart';

abstract class ChatRepository {
  Future<void> connectSocket(String userId);
  Future<void> disconnectSocket();
  Future<void> sendMessage(ChatModel message);
  Future<List<ChatModel>> fetchMessages(Map<String, dynamic> payload);
  Stream<ChatModel> receiveMessage();
  Future<List<LastMessageModel>> getLastMessages(String id);
  Future<bool> markMessagesAsRead(List<String> messageIds);
}

// Data Layer: Socket Data Source
class SocketDataSource {
  final String serverUrl;
  socket_io.Socket? socket;

  // Use a new stream controller for each connection to prevent "already closed" errors
  StreamController<ChatModel>? _messageController;
  bool _isDisconnecting = false;
  bool _isConnected = false;

  // Add more efficient timeouts
  final Duration connectionTimeout = const Duration(seconds: 8);
  final Duration operationTimeout = const Duration(seconds: 5);

  SocketDataSource(this.serverUrl);

  // Create or reset the message controller
  StreamController<ChatModel> _getMessageController() {
    if (_messageController == null || _messageController!.isClosed) {
      _messageController = StreamController<ChatModel>.broadcast();
    }
    return _messageController!;
  }

  // Safely add a message to the controller
  void _safeAddToController(ChatModel message) {
    try {
      if (!(_messageController?.isClosed ?? true) && !_isDisconnecting) {
        _messageController?.add(message);
      } else {
        print('Cannot add message to closed controller or during disconnect');
      }
    } catch (e) {
      print('Error adding message to controller: $e');
    }
  }

  Future<void> connect(String userId) async {
    // If already connected with same user, don't reconnect
    if (_isConnected && socket != null) {
      print('Already connected, skipping reconnection');
      return;
    }

    // Start performance monitoring
    // RedisPerformanceMonitor.startOperation('socket_connect');
    print('Starting socket connection...');

    // Reset disconnecting flag when connecting
    _isDisconnecting = false;
    _isConnected = false;

    // Create a new message controller if needed
    _getMessageController();

    // Create a completer to handle connection state
    final Completer<void> connectionCompleter = Completer<void>();

    // Setup connection timeout
    Timer? connectionTimer;
    connectionTimer = Timer(connectionTimeout, () {
      if (!connectionCompleter.isCompleted) {
        print('Socket connection timeout');
        // RedisPerformanceMonitor.logRedisEvent('socket_timeout', 'Connection timeout after ${connectionTimeout.inSeconds} seconds');
        print(
            'ERROR: Connection timeout after ${connectionTimeout.inSeconds} seconds');
        connectionCompleter.completeError(
            'Connection timeout: Could not connect to chat server');
        _safeDisconnect();
      }
    });

    // Dispose of existing socket if needed
    if (socket != null) {
      try {
        socket!.dispose();
      } catch (e) {
        print('Error disposing old socket: $e');
      }
      socket = null;
    }

    // Fix the API URL if needed
    String apiUrl = serverUrl;
    if (apiUrl.contains('classz.cod')) {
      apiUrl = apiUrl.replaceAll('classz.cod', 'classz.co');
      print('Corrected API URL typo: $apiUrl');
    }

    // Add diagnostics for connection debugging
    print('Connecting to chat server at: $apiUrl');

    // Create socket with optimized options for better network behavior
    socket = socket_io.io(apiUrl, <String, dynamic>{
      'transports': ['websocket'],
      'query': {'userId': userId},
      'reconnection': true,
      'reconnectionDelay': 1000,
      'reconnectionAttempts': 5,
      'timeout': 10000, // Increase to 10 seconds for better reliability
      'forceNew': true, // Force a new connection
      'pingInterval':
          5000, // Send ping every 5 seconds for more reliable connection
      'pingTimeout': 10000, // Wait 10 seconds for pong response
      'autoConnect': false, // Don't connect until we're ready
    });

    // Add ping/pong debugging
    socket?.onPing((_) {
      print('Ping sent to server');
    });

    socket?.onPong((_) {
      print('Pong received from server');
    });

    socket?.onConnect((data) {
      print('Socket connected successfully');
      _isConnected = true;
      // RedisPerformanceMonitor.logRedisEvent('socket_connected', 'Successfully connected to chat server');
      // RedisPerformanceMonitor.endOperation('socket_connect');
      print('SUCCESS: Successfully connected to chat server');
      if (!connectionCompleter.isCompleted) {
        connectionCompleter.complete();
      }
      // Cancel timeout timer
      connectionTimer?.cancel();
    });

    socket?.onConnectError((error) {
      print('Socket connection error: $error');
      _isConnected = false;
      // RedisPerformanceMonitor.logRedisEvent('socket_connect_error', 'Connection error: $error');
      // RedisPerformanceMonitor.endOperation('socket_connect');
      print('ERROR: Connection error: $error');
      if (!connectionCompleter.isCompleted) {
        connectionCompleter.completeError('Failed to connect: $error');
      }
      // Cancel timeout timer
      connectionTimer?.cancel();
    });

    socket?.onError((error) {
      print('Socket error: $error');
      // Only complete if not already completed
      if (!connectionCompleter.isCompleted) {
        connectionCompleter.completeError('Socket error: $error');
      }
    });

    socket?.on('message', (data) {
      if (_isDisconnecting) {
        print('Skipping message because disconnecting');
        return;
      }

      // RedisPerformanceMonitor.startOperation('process_incoming_message');
      try {
        // Safely parse and add the message
        if (data is Map<String, dynamic>) {
          final message = ChatModel.fromJson(data);
          _safeAddToController(message);
          // RedisPerformanceMonitor.logRedisEvent('message_received', 'Processed message from: ${message.sender}');
        } else if (data is Map) {
          // Convert dynamic map to string-keyed map
          final jsonMap = Map<String, dynamic>.from(data);
          final message = ChatModel.fromJson(jsonMap);
          _safeAddToController(message);
          // RedisPerformanceMonitor.logRedisEvent('message_received', 'Processed message from: ${message.sender}');
        } else {
          print('Received message in unexpected format: $data');
          // RedisPerformanceMonitor.logRedisEvent('message_error', 'Received message in unexpected format');
        }
      } catch (e) {
        print('Error processing incoming message: $e');
        // RedisPerformanceMonitor.logRedisEvent('message_error', 'Error processing incoming message: $e');
      } finally {
        // RedisPerformanceMonitor.endOperation('process_incoming_message');
      }
    });

    socket?.onDisconnect((_) {
      print('Socket disconnected');
      _isConnected = false;
      // Don't close the controller on disconnect to allow for reconnection
      // Only close it when explicitly called from disconnect()
    });

    socket?.on('messageConfirmation', (data) {
      if (_isDisconnecting) return;

      // RedisPerformanceMonitor.startOperation('process_confirmation');
      try {
        print('Message confirmation received: $data');

        // Handle different confirmation formats
        if (data is Map) {
          final Map<String, dynamic> confirmData = data is Map<String, dynamic>
              ? data
              : Map<String, dynamic>.from(data);

          final messageId = confirmData['messageId'];
          final dbMessageId = confirmData[
              'dbMessageId']; // This is important for tracking message status
          final status = confirmData['status'] ?? 'sent';
          final duplicateDetected = confirmData['duplicateDetected'] ?? false;
          final timestamp = confirmData['timestamp'];

          print(
              'Message confirmation - ID: $messageId, DB ID: $dbMessageId, Status: $status, Duplicate: $duplicateDetected');

          if (duplicateDetected) {
            // RedisPerformanceMonitor.logRedisEvent('duplicate_message', 'Server detected duplicate message: $messageId');
            return; // No need to update UI for duplicates
          }

          // Update local message with server ID for future status updates
          if (messageId != null && dbMessageId != null) {
            print(
                'Updating message with client ID $messageId to use server ID $dbMessageId');
            _updateLocalMessageWithServerInfo(
                messageId, dbMessageId, status, timestamp);

            // Emit a special update for this specific ID mapping
            // This allows UI to find messages with client tempId and update them
            _safeAddToController(ChatModel(
              id: dbMessageId,
              tempId: messageId,
              status: status,
              timestamp: timestamp != null
                  ? DateTime.parse(timestamp)
                  : DateTime.now(),
            ));
          }
        }
      } catch (e) {
        print('Error processing message confirmation: $e');
        // RedisPerformanceMonitor.logRedisEvent('confirmation_error', 'Error: $e');
      } finally {
        // RedisPerformanceMonitor.endOperation('process_confirmation');
      }
    });

    // New handler for message status updates
    socket?.on('messageStatusUpdate', (data) {
      if (_isDisconnecting) return;

      try {
        print('Message status update received: $data');

        if (data is Map) {
          final Map<String, dynamic> statusData = data is Map<String, dynamic>
              ? data
              : Map<String, dynamic>.from(data);

          final messageId = statusData['messageId'];
          final status = statusData['status'];

          if (messageId != null && status != null) {
            print('Message status update - ID: $messageId, Status: $status');
            _updateMessageStatus(messageId, status);
          }
        }
      } catch (e) {
        print('Error processing message status update: $e');
      }
    });

    // New handler for messages read notifications
    socket?.on('messagesRead', (data) {
      if (_isDisconnecting) return;

      try {
        print('Messages read notification received: $data');

        if (data is Map && data.containsKey('messageIds')) {
          final messageIds = data['messageIds'] as List<dynamic>;

          if (messageIds.isNotEmpty) {
            print('Messages marked as read: ${messageIds.length} messages');

            // Update local status for each message
            for (final id in messageIds) {
              _updateMessageStatus(id.toString(), 'read');
            }
          }
        }
      } catch (e) {
        print('Error processing messages read notification: $e');
      }
    });

    // Now actually connect
    socket?.connect();

    // Return the future to allow proper error handling
    return connectionCompleter.future;
  }

  // Safely disconnect without causing exceptions
  void _safeDisconnect() {
    try {
      if (socket != null) {
        socket!.disconnect();
        socket!.close();
        socket!.dispose();
        socket = null;
      }
    } catch (e) {
      print('Error in safe disconnect: $e');
    }
    _isConnected = false;
  }

  Future<void> disconnect() async {
    if (_isDisconnecting) {
      print('Already disconnecting, skipping duplicate call');
      return;
    }

    // CRITICAL: We must immediately return a completed future to prevent UI blocking
    // Set the disconnecting flag to prevent new messages
    _isDisconnecting = true;

    // Perform actual disconnection in a separate isolate/thread
    // to avoid blocking the UI thread
    Future.microtask(() async {
      try {
        print("Performing socket disconnect in background");

        // Disconnect socket first - don't wait more than 1 second
        _safeDisconnect();

        // Then try to close the controller
        try {
          await Future.delayed(const Duration(milliseconds: 100));
          await _messageController?.close();
          _messageController = null;
        } catch (e) {
          print('Error closing message controller: $e');
        }

        print("Socket disconnection completed in background");
      } catch (e) {
        print('Error in background disconnect: $e');
      } finally {
        _isDisconnecting = false;
      }
    });

    // Return immediately to prevent UI blocking
    return Future.value();
  }

  void sendMessage(ChatModel message) {
    // RedisPerformanceMonitor.startOperation('send_message');
    try {
      if (_isDisconnecting) {
        // RedisPerformanceMonitor.logRedisEvent('send_message_error', 'Cannot send while disconnecting');
        // RedisPerformanceMonitor.endOperation('send_message');
        throw Exception('Cannot send message while disconnecting');
      }

      if (!_isConnected || socket == null) {
        // RedisPerformanceMonitor.logRedisEvent('send_message_error', 'Socket not connected');
        // RedisPerformanceMonitor.endOperation('send_message');
        throw Exception('Cannot send message: Socket not connected');
      }

      // Log the message being sent
      print('Sending message: ${message.message}');

      // Create a copy with timestamp to ensure it's fresh
      final messageToSend = ChatModel(
        id: message.id,
        tempId:
            message.tempId ?? DateTime.now().millisecondsSinceEpoch.toString(),
        message: message.message,
        sender: message.sender,
        recipient: message.recipient,
        senderModel: message.senderModel,
        recipientModel: message.recipientModel,
        timestamp: DateTime.now(), // Use a fresh timestamp
        conversationId: message.conversationId,
      );

      // Add the message to our local controller immediately
      // This ensures the message appears in the UI even if the server doesn't respond
      _safeAddToController(messageToSend);

      // Prepare payload and ensure it's serialized correctly
      final payload = messageToSend.toJson();

      // Emit the message to the server
      socket!.emit('sendMessage', payload);
      // RedisPerformanceMonitor.logRedisEvent('message_sent', 'Sent message to: ${message.recipient}');
      // RedisPerformanceMonitor.endOperation('send_message');

      // Set up a confirmation handler with timeout
      final messageId = messageToSend.tempId ??
          DateTime.now().millisecondsSinceEpoch.toString();
      final confirmationTimeout = Timer(const Duration(seconds: 5), () {
        // If we don't get confirmation, try resending once
        if (_isConnected && socket != null && !_isDisconnecting) {
          // RedisPerformanceMonitor.logRedisEvent('message_retry', 'No confirmation for message $messageId, retrying');
          print('No confirmation for message $messageId, attempting one retry');
          socket!.emit('sendMessage', payload);
        } else {
          // RedisPerformanceMonitor.logRedisEvent('message_no_confirm', 'No confirmation for message $messageId');
          print('No confirmation for message $messageId, but keeping in UI');
        }
      });

      // Listen for confirmation (one-time)
      socket!.once('messageSent', (data) {
        confirmationTimeout.cancel();
        print('Message confirmed by server: $data');
        // RedisPerformanceMonitor.logRedisEvent('message_confirmed', 'Message confirmed by server');

        // If the server returns an updated message with an ID, add it to the controller
        if (data != null && data is Map) {
          try {
            Map<String, dynamic> responseData;
            if (data is Map<String, dynamic>) {
              responseData = data;
            } else {
              responseData = Map<String, dynamic>.from(data);
            }

            // If this is our message with a server ID, update it
            if (responseData.containsKey('tempId') &&
                responseData['tempId'] == messageToSend.tempId) {
              final updatedMessage = ChatModel.fromJson(responseData);
              _safeAddToController(updatedMessage);
            }
          } catch (e) {
            print('Error processing message confirmation: $e');
            // RedisPerformanceMonitor.logRedisEvent('confirmation_error', 'Error processing message confirmation: $e');
          }
        }
      });
    } catch (e) {
      print('Error sending message: $e');
      // RedisPerformanceMonitor.logRedisEvent('send_message_error', 'Error sending message: $e');
      // RedisPerformanceMonitor.endOperation('send_message');
      throw Exception('Failed to send message: $e');
    }
  }

  Future<List<ChatModel>> fetchMessages(Map<String, dynamic> payload) async {
    // RedisPerformanceMonitor.startOperation('fetch_messages');
    final completer = Completer<List<ChatModel>>();

    // Return immediately if socket is not connected or we're disconnecting
    if (!_isConnected || _isDisconnecting || socket == null) {
      print('Cannot fetch messages: Socket not connected or disconnecting');
      // RedisPerformanceMonitor.logRedisEvent('fetch_error', 'Socket not connected or disconnecting');
      // RedisPerformanceMonitor.endOperation('fetch_messages');
      return [];
    }

    // Add timeout for fetch messages - give more time for history
    Timer? fetchTimer;
    fetchTimer = Timer(const Duration(seconds: 15), () {
      // Increase timeout to 15 seconds
      if (!completer.isCompleted) {
        print('Fetch messages timeout - returning empty list');
        // RedisPerformanceMonitor.logRedisEvent('fetch_timeout', 'Timeout after 15 seconds');
        // RedisPerformanceMonitor.endOperation('fetch_messages');
        completer.complete([]);
      }
    });

    try {
      // Extract sender and recipient for logging
      final String? user1 = payload['user1'] as String?;
      final String? user2 = payload['user2'] as String?;
      // RedisPerformanceMonitor.logRedisEvent('fetch_request', 'Fetching messages between $user1 and $user2');

      // Create conversation ID if not provided
      if (payload['conversationId'] == null && user1 != null && user2 != null) {
        // Create a consistent conversation ID regardless of who is user1/user2
        final List<String> users = [user1, user2];
        users.sort(); // Sort to ensure consistent ID regardless of order
        payload['conversationId'] = users.join('-');
      }

      // Add cursor-based pagination support
      if (payload['beforeTimestamp'] == null && payload['nextCursor'] != null) {
        payload['beforeTimestamp'] = payload['nextCursor'];
      }

      // Register an event handler for messages data with proper format handling
      socket!.once('fetchedMessage', (data) {
        fetchTimer?.cancel();

        try {
          print('Received messages data: $data');

          // Initialize an empty list for storing messages
          final List<ChatModel> messages = [];

          // Handle the new structure with messages and nextCursor
          if (data is Map &&
              data.containsKey('messages') &&
              data['messages'] is List) {
            final messagesList = data['messages'] as List;
            final nextCursor = data['nextCursor'];

            // Store nextCursor for pagination if available
            if (nextCursor != null) {
              print('Next cursor available: $nextCursor');
              // You could store this for later pagination
            }

            // Convert each message to a ChatModel
            for (var item in messagesList) {
              try {
                if (item == null) continue;

                // Convert item to a map that ChatModel can use
                Map<String, dynamic> jsonMap;
                if (item is Map<String, dynamic>) {
                  jsonMap = item;
                } else if (item is Map) {
                  jsonMap = Map<String, dynamic>.from(item);
                } else {
                  // Skip non-map items
                  continue;
                }

                // Create a ChatModel from the processed data
                final message = ChatModel.fromJson(jsonMap);
                messages.add(message);
              } catch (e) {
                print('Error parsing individual message: $e');
                // Continue with other messages
              }
            }
          } else if (data is List) {
            // Handle case where server returns a direct list (legacy format)
            for (var item in data) {
              try {
                if (item == null) continue;

                // Convert item to a map that ChatModel can use
                Map<String, dynamic> jsonMap;
                if (item is Map<String, dynamic>) {
                  jsonMap = item;
                } else if (item is Map) {
                  jsonMap = Map<String, dynamic>.from(item);
                } else {
                  // Skip non-map items
                  continue;
                }

                // Create a ChatModel from the processed data
                final message = ChatModel.fromJson(jsonMap);
                messages.add(message);
              } catch (e) {
                print('Error parsing individual message: $e');
                // Continue with other messages
              }
            }
          }

          print('Successfully parsed ${messages.length} messages');

          // Mark unread messages as read
          _markMessagesAsRead(messages, user1);

          // RedisPerformanceMonitor.logRedisEvent('fetch_success', 'Retrieved ${messages.length} messages');
          // RedisPerformanceMonitor.endOperation('fetch_messages');
          completer.complete(messages);
        } catch (e) {
          print('Error parsing messages: $e');
          // RedisPerformanceMonitor.logRedisEvent('parse_error', 'Error parsing fetched messages: $e');
          // RedisPerformanceMonitor.endOperation('fetch_messages');
          // Return empty list instead of failing to prevent UI errors
          completer.complete([]);
        }
      });

      // Emit the fetch message event
      socket!.emit('fetchMessage', payload);
    } catch (e) {
      fetchTimer.cancel();
      print('Error in fetchMessages: $e');
      // RedisPerformanceMonitor.logRedisEvent('fetch_exception', 'Exception in fetchMessages: $e');
      // RedisPerformanceMonitor.endOperation('fetch_messages');
      // Return empty list instead of failing to prevent UI crashes
      if (!completer.isCompleted) {
        completer.complete([]);
      }
    }

    return completer.future;
  }

  Stream<ChatModel> receiveMessage() {
    // Always return a valid stream, creating a new controller if needed
    return _getMessageController().stream;
  }

  // New function to mark messages as read
  Future<bool> markMessagesAsRead(List<String> messageIds) async {
    if (messageIds.isEmpty) {
      return true; // Nothing to mark as read
    }

    // RedisPerformanceMonitor.startOperation('mark_messages_read');

    try {
      if (!_isConnected || socket == null) {
        // RedisPerformanceMonitor.logRedisEvent('read_receipt_error', 'Socket not connected');
        // RedisPerformanceMonitor.endOperation('mark_messages_read');
        return false;
      }

      // Create a completer to handle success/failure
      final completer = Completer<bool>();

      // Set up timeout
      Timer? timeoutTimer;
      timeoutTimer = Timer(const Duration(seconds: 5), () {
        if (!completer.isCompleted) {
          // RedisPerformanceMonitor.logRedisEvent('read_receipt_timeout', 'No response after 5 seconds');
          completer.complete(false); // Consider it failed if no response
        }
      });

      // Prepare payload
      final payload = {
        'messageIds': messageIds,
      };

      // Listen for confirmation
      socket!.once('messagesMarkedAsRead', (data) {
        timeoutTimer?.cancel();

        // RedisPerformanceMonitor.logRedisEvent(
        //   'read_receipt_success',
        //   'Marked ${messageIds.length} messages as read'
        // );

        if (!completer.isCompleted) {
          completer.complete(true);
        }
      });

      // Listen for error
      socket!.once('markAsReadError', (error) {
        timeoutTimer?.cancel();

        final errorMsg = error is Map
            ? error['message']?.toString() ?? 'Unknown error'
            : 'Unknown error';
        // RedisPerformanceMonitor.logRedisEvent('read_receipt_error', errorMsg);

        if (!completer.isCompleted) {
          completer.complete(false);
        }
      });

      // Emit the event to mark messages as read
      socket!.emit('markAsRead', payload);
      // RedisPerformanceMonitor.logRedisEvent(
      //   'read_receipt_request',
      //   'Requesting to mark ${messageIds.length} messages as read'
      // );

      final result = await completer.future;
      // RedisPerformanceMonitor.endOperation('mark_messages_read');
      return result;
    } catch (e) {
      // RedisPerformanceMonitor.logRedisEvent('read_receipt_exception', 'Error: $e');
      // RedisPerformanceMonitor.endOperation('mark_messages_read');
      return false;
    }
  }

  // New helper method to update local message with server info
  void _updateLocalMessageWithServerInfo(
      String clientId, String dbId, String status, dynamic timestamp) {
    try {
      // Get a timestamp from the data or use now
      final messageTime =
          timestamp != null ? DateTime.parse(timestamp) : DateTime.now();

      print(
          'Updating message: clientId=$clientId to serverDbId=$dbId with status=$status');

      // Create a special update message that contains both IDs for proper mapping
      final updatedMessage = ChatModel(
        id: dbId, // Server's MongoDB ID
        tempId: clientId, // Original client-generated ID
        status: status,
        timestamp: messageTime,
      );

      // This will be caught by the messageController stream
      _safeAddToController(updatedMessage);

      // RedisPerformanceMonitor.logRedisEvent(
      //   'message_updated',
      //   'Updated local message $clientId with server ID $dbId'
      // );
    } catch (e) {
      print('Error updating message with server info: $e');
    }
  }

  // Helper method to update message status
  void _updateMessageStatus(String messageId, String status) {
    try {
      // Emit a status update message to the controller
      final statusUpdateMessage = ChatModel(
        id: messageId,
        status: status,
      );

      _safeAddToController(statusUpdateMessage);

      // RedisPerformanceMonitor.logRedisEvent(
      //   'status_updated',
      //   'Updated message $messageId status to $status'
      // );
    } catch (e) {
      print('Error updating message status: $e');
    }
  }

  // Helper to automatically mark messages as read
  void _markMessagesAsRead(List<ChatModel> messages, String? currentUserId) {
    if (currentUserId == null) return;

    try {
      // Find messages that aren't from the current user and aren't marked as read
      final unreadMessages = messages
          .where((msg) =>
              msg.sender != currentUserId &&
              msg.status != 'read' &&
              msg.id != null)
          .toList();

      if (unreadMessages.isEmpty) return;

      // Get the IDs of these messages
      final messageIds = unreadMessages.map((msg) => msg.id!).toList();

      // Emit the event to mark messages as read
      socket?.emit('messageRead', {'messageIds': messageIds});

      print('Marked ${messageIds.length} messages as read');
    } catch (e) {
      print('Error marking messages as read: $e');
    }
  }
}
