import 'package:class_z/core/imports.dart';
// import '../../utils/redis_performance_monitor.dart';

class HttpDataSource {
  @override
  Future<List<LastMessageModel>> getLastMessages(
      String id, String token) async {
    // RedisPerformanceMonitor.startOperation('redis_getLastMessages');
    print('Fetching last messages for user $id');

    try {
      String uri = "${AppText.device}/api/chat/conversations/$id";
      HttpClient client = HttpClient();
      HttpClientRequest request = await client.getUrl(Uri.parse(uri));
      request.headers.set('Content-Type', 'application/json');
      request.headers.set('Authorization', token);

      HttpClientResponse response = await request.close();
      String responseBody = await response.transform(utf8.decoder).join();

      if (response.statusCode == 200) {
        try {
          // Parse the response body
          final Map<String, dynamic> jsonResponse = json.decode(responseBody);

          // Check if response has required structure
          if (!jsonResponse.containsKey('data') ||
              !jsonResponse['data'].containsKey('conversations')) {
            // RedisPerformanceMonitor.logRedisEvent('structure_error', 'Redis endpoint returned invalid structure (missing data.conversations)');
            print(
                'ERROR: Redis endpoint returned invalid structure (missing data.conversations)');
            return await _getLegacyLastMessages(id, token);
          }

          // Safely extract conversations list
          final dynamic conversationsData =
              jsonResponse['data']['conversations'];

          // Ensure conversations is actually a List
          if (conversationsData == null || conversationsData is! List) {
            // RedisPerformanceMonitor.logRedisEvent('type_error', 'Redis endpoint conversations is not a List: ${conversationsData.runtimeType}');
            print(
                'ERROR: Redis endpoint conversations is not a List: ${conversationsData.runtimeType}');
            return await _getLegacyLastMessages(id, token);
          }

          // Convert to strongly typed list and map to models
          final List<dynamic> conversations = conversationsData;
          // RedisPerformanceMonitor.logRedisEvent('conversation_fetch', 'Redis endpoint: ${conversations.length} conversations fetched');
          print(
              'SUCCESS: Redis endpoint: ${conversations.length} conversations fetched');

          // Check if broadcast conversation exists
          bool hasBroadcastConversation = conversations.any((item) =>
              (item["conversationId"] != null &&
                  item["conversationId"].toString().startsWith('-')) ||
              (item["isBroadcast"] == true));

          print(
              'Has broadcast conversation in response: $hasBroadcastConversation');

          // Map to LastMessageModel objects with safety checks
          List<LastMessageModel> result = conversations
              .map((item) {
                try {
                  // Ensure the item has isBroadcast property if it's a broadcast conversation
                  if ((item["conversationId"] != null &&
                          item["conversationId"].toString().startsWith('-')) &&
                      item["isBroadcast"] != true) {
                    item["isBroadcast"] = true;
                  }

                  return LastMessageModel.fromJson(item);
                } catch (e) {
                  // Log parsing error but continue with other items
                  // RedisPerformanceMonitor.logRedisEvent('item_parse_error', 'Error parsing conversation item: $e');
                  print('ERROR: Error parsing conversation item: $e');
                  return null;
                }
              })
              .where((item) => item != null)
              .cast<LastMessageModel>()
              .toList();

          // If there's no broadcast conversation, create a synthetic one
          if (!hasBroadcastConversation) {
            try {
              // Get broadcast messages to check if they exist
              final broadcastMessages =
                  await _checkForBroadcastMessages(id, token);

              if (broadcastMessages &&
                  !result.any((conv) => conv.isBroadcastConversation)) {
                print('Adding synthetic broadcast conversation for user $id');

                // Create synthetic broadcast conversation
                final broadcastConv = LastMessageModel(
                  oppositeId: '',
                  oppositeModel: 'user',
                  name: 'Broadcast Messages',
                  lastMessage: 'Broadcast messages',
                  lastMessageTime: DateTime.now(),
                  status: 'read',
                  conversationId: '-$id',
                  isBroadcast: true,
                );

                result.add(broadcastConv);
              }
            } catch (e) {
              print('Error checking for broadcast messages: $e');
            }
          }

          // RedisPerformanceMonitor.endOperation('redis_getLastMessages');
          return result;
        } catch (e) {
          // RedisPerformanceMonitor.logRedisEvent('parse_error', 'Error parsing Redis endpoint response: $e');
          print('ERROR: Error parsing Redis endpoint response: $e');
          // Fall back to legacy endpoint
          return await _getLegacyLastMessages(id, token);
        }
      } else {
        // RedisPerformanceMonitor.logRedisEvent('status_error', 'Redis endpoint returned status ${response.statusCode}');
        print('ERROR: Redis endpoint returned status ${response.statusCode}');
        // Fall back to legacy endpoint
        return await _getLegacyLastMessages(id, token);
      }
    } catch (e) {
      // RedisPerformanceMonitor.logRedisEvent('request_error', 'Error in GetLastMessages with Redis endpoint: $e');
      print('ERROR: Error in GetLastMessages with Redis endpoint: $e');
      // Fall back to legacy endpoint
      return await _getLegacyLastMessages(id, token);
    }
  }

  // Helper method to check if broadcast messages exist for a user
  Future<bool> _checkForBroadcastMessages(String userId, String token) async {
    try {
      // Use the conversationId format for broadcast messages: -userId
      String conversationId = '-$userId';
      String uri =
          "${AppText.device}/api/chat/messages?conversationId=$conversationId&limit=1";

      HttpClient client = HttpClient();
      HttpClientRequest request = await client.getUrl(Uri.parse(uri));
      request.headers.set('Content-Type', 'application/json');
      request.headers.set('Authorization', token);

      HttpClientResponse response = await request.close();
      String responseBody = await response.transform(utf8.decoder).join();

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(responseBody);
        if (jsonResponse.containsKey('data') &&
            jsonResponse['data'] is List &&
            (jsonResponse['data'] as List).isNotEmpty) {
          return true;
        }
      }
      return false;
    } catch (e) {
      print('Error checking for broadcast messages: $e');
      return false;
    }
  }

  Future<List<LastMessageModel>> _getLegacyLastMessages(
      String id, String token) async {
    // RedisPerformanceMonitor.startOperation('legacy_getLastMessages');
    print('Falling back to legacy endpoint for user $id');

    try {
      String uri = "${AppText.device}/api/chat/$id";
      HttpClient client = HttpClient();
      HttpClientRequest request = await client.getUrl(Uri.parse(uri));
      request.headers.set('Content-Type', 'application/json');
      request.headers.set('Authorization', token);

      HttpClientResponse response = await request.close();
      String responseBody = await response.transform(utf8.decoder).join();

      if (response.statusCode == 200) {
        try {
          final jsonResponse = json.decode(responseBody);

          if (!jsonResponse.containsKey('data')) {
            // RedisPerformanceMonitor.logRedisEvent('legacy_structure_error', 'Legacy endpoint missing data field');
            // RedisPerformanceMonitor.endOperation('legacy_getLastMessages');
            print('ERROR: Legacy endpoint missing data field');
            return [];
          }

          final dynamic data = jsonResponse['data'];
          if (data == null || data is! List) {
            // RedisPerformanceMonitor.logRedisEvent('legacy_type_error', 'Legacy endpoint data is not a List');
            // RedisPerformanceMonitor.endOperation('legacy_getLastMessages');
            print('ERROR: Legacy endpoint data is not a List');
            return [];
          }

          final List<dynamic> messages = data;
          // RedisPerformanceMonitor.logRedisEvent('legacy_fetch', 'Legacy endpoint: ${messages.length} messages fetched');
          print(
              'SUCCESS: Legacy endpoint: ${messages.length} messages fetched');

          // Check if any message is a broadcast
          bool hasBroadcast = messages.any((item) =>
              (item["conversationId"] != null &&
                  item["conversationId"].toString().startsWith('-')) ||
              (item["isBroadcast"] == true));

          List<LastMessageModel> result = messages
              .map((item) {
                try {
                  // Ensure the item has isBroadcast property if it's a broadcast conversation
                  if ((item["conversationId"] != null &&
                          item["conversationId"].toString().startsWith('-')) &&
                      item["isBroadcast"] != true) {
                    item["isBroadcast"] = true;
                  }

                  return LastMessageModel.fromJson(item);
                } catch (e) {
                  // RedisPerformanceMonitor.logRedisEvent('legacy_item_parse_error', 'Error parsing legacy item: $e');
                  print('ERROR: Error parsing legacy item: $e');
                  return null;
                }
              })
              .where((item) => item != null)
              .cast<LastMessageModel>()
              .toList();

          // If no broadcast found, check separately for broadcasts
          if (!hasBroadcast) {
            try {
              final hasBroadcastMessages =
                  await _checkForBroadcastMessages(id, token);

              if (hasBroadcastMessages &&
                  !result.any((conv) => conv.isBroadcastConversation)) {
                print(
                    'Adding synthetic broadcast conversation for user $id (legacy endpoint)');

                // Create synthetic broadcast conversation
                final broadcastConv = LastMessageModel(
                  oppositeId: '',
                  oppositeModel: 'user',
                  name: 'Broadcast Messages',
                  lastMessage: 'Broadcast messages',
                  lastMessageTime: DateTime.now(),
                  status: 'read',
                  conversationId: '-$id',
                  isBroadcast: true,
                );

                result.add(broadcastConv);
              }
            } catch (e) {
              print('Error checking for broadcast messages in legacy flow: $e');
            }
          }

          // RedisPerformanceMonitor.endOperation('legacy_getLastMessages');
          return result;
        } catch (e) {
          // RedisPerformanceMonitor.logRedisEvent('legacy_parse_error', 'Error parsing legacy response: $e');
          print('ERROR: Error parsing legacy response: $e');
          // RedisPerformanceMonitor.endOperation('legacy_getLastMessages');
          return [];
        }
      } else {
        // RedisPerformanceMonitor.logRedisEvent('legacy_status_error', 'Legacy endpoint returned status ${response.statusCode}');
        print('ERROR: Legacy endpoint returned status ${response.statusCode}');
        // RedisPerformanceMonitor.endOperation('legacy_getLastMessages');
        return [];
      }
    } catch (e) {
      // RedisPerformanceMonitor.logRedisEvent('legacy_request_error', 'Error in legacy GetLastMessages: $e');
      print('ERROR: Error in legacy GetLastMessages: $e');
      // RedisPerformanceMonitor.endOperation('legacy_getLastMessages');
      return [];
    }
  }
}
