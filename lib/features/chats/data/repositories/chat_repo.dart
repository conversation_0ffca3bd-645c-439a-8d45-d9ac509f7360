// Data Layer: Repository Implementation
import 'package:class_z/core/imports.dart';


class ChatRepositoryImpl implements ChatRepository {
  final SocketDataSource socketDataSource;
  final HttpDataSource httpDataSource;

  ChatRepositoryImpl(this.socketDataSource, this.httpDataSource);

  @override
  Future<void> connectSocket(String userId) async {
    await socketDataSource.connect(userId);
  }

  @override
  Future<void> disconnectSocket() async {
    await socketDataSource.disconnect();
  }

  @override
  Future<void> sendMessage(ChatModel message) async {
    socketDataSource.sendMessage(message);
  }

  @override
  Future<List<ChatModel>> fetchMessages(Map<String, dynamic> payload) async {
    return await socketDataSource.fetchMessages(payload);
  }

  @override
  Stream<ChatModel> receiveMessage() {
    return socketDataSource.receiveMessage();
  }

  @override
  Future<List<LastMessageModel>> getLastMessages(String id) async {
    // Get the token from SharedRepository
    final sharedRepository = GetIt.instance.get<SharedRepository>();
    final token = await sharedRepository.getToken() ?? '';
    return await httpDataSource.getLastMessages(id, token);
  }
  
  @override
  Future<bool> markMessagesAsRead(List<String> messageIds) async {
    return await socketDataSource.markMessagesAsRead(messageIds);
  }
}