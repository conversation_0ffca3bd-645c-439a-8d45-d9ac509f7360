import 'package:class_z/features/chats/data/models/chatModel.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'dart:math' as math;

class ChatLocalRepository {
  static const String _chatBoxName = 'chatMessages';
  static ChatLocalRepository? _instance;
  Box<ChatModel>? _box;
  bool _initialized = false;

  // Singleton pattern
  static ChatLocalRepository get instance {
    _instance ??= ChatLocalRepository._();
    return _instance!;
  }

  ChatLocalRepository._();

  // Initialize the repository
  Future<void> initialize() async {
    if (_initialized) {
      print('ChatLocalRepository already initialized');
      return;
    }

    try {
      print('Initializing ChatLocalRepository...');

      // Make sure Hive is initialized - use a safer approach
      try {
        print('Ensuring Hive is initialized...');
        await Hive.initFlutter();
      } catch (e) {
        // Ignore errors if Hive is already initialized
        print('Hive initialization note: $e');
      }

      // Register the adapter if not already registered
      if (!Hive.isAdapterRegistered(50)) {
        print('Registering ChatModelAdapter with typeId 50');
        Hive.registerAdapter(ChatModelAdapter());
      } else {
        print('ChatModelAdapter already registered');
      }

      // Open the box if not already open
      if (!Hive.isBoxOpen(_chatBoxName)) {
        print('Opening Hive box: $_chatBoxName');
        try {
          _box = await Hive.openBox<ChatModel>(_chatBoxName);
          print('Hive box opened successfully with ${_box?.length} messages');
        } catch (e) {
          // If there's an error opening the box, try to delete it and recreate
          print('Error opening box: $e');
          print('Attempting to delete and recreate box');
          await Hive.deleteBoxFromDisk(_chatBoxName);
          _box = await Hive.openBox<ChatModel>(_chatBoxName);
        }
      } else {
        print('Hive box already open');
        _box = Hive.box<ChatModel>(_chatBoxName);
      }

      // Debug: List all messages in the box
      if (_box != null) {
        print('Current messages in box: ${_box!.length}');
        for (var key in _box!.keys) {
          final message = _box!.get(key);
          print(
              'Message: ${message?.message}, Sender: ${message?.sender}, Recipient: ${message?.recipient}');
        }
      }

      _initialized = true;
      print('ChatLocalRepository initialized successfully');
    } catch (e) {
      print('Error initializing ChatLocalRepository: $e');
      // If there's an error, try to delete and recreate the box
      try {
        print('Attempting to recover by deleting and recreating the box');
        await Hive.deleteBoxFromDisk(_chatBoxName);
        _box = await Hive.openBox<ChatModel>(_chatBoxName);
        _initialized = true;
        print('Recovery successful');
      } catch (e) {
        print('Failed to recover from initialization error: $e');
        _initialized = false;
      }
    }
  }

  // Save a message to local storage
  Future<bool> saveMessage(ChatModel message) async {
    if (message.message == null || message.message!.isEmpty) {
      print('Skipping save of empty message');
      return false;
    }

    try {
      final box = await _getBox();

      // Generate a key for the message
      final key = message.id ??
          message.tempId ??
          '${message.sender}-${message.recipient}-${message.timestamp?.millisecondsSinceEpoch}';

      // Ensure the message has a conversationId
      ChatModel messageToSave = message;
      if (message.conversationId == null &&
          message.sender != null &&
          message.recipient != null) {
        // Create a consistent conversation ID
        final List<String> users = [message.sender!, message.recipient!];
        users.sort();
        final conversationId = users.join('-');

        // Create a new message with the conversation ID
        messageToSave = ChatModel(
          id: message.id,
          tempId: message.tempId,
          message: message.message,
          sender: message.sender,
          recipient: message.recipient,
          senderModel: message.senderModel,
          recipientModel: message.recipientModel,
          timestamp: message.timestamp,
          conversationId: conversationId,
        );
      }

      // Save the message
      await box.put(key, messageToSave);

      // Force flush to disk
      try {
        await box.flush();
      } catch (e) {
        print('Non-fatal error flushing to disk: $e');
      }

      print(
          'Message saved to local storage: $key, Content: ${messageToSave.message}, ConversationId: ${messageToSave.conversationId}');

      // Verify the message was saved
      try {
        final savedMessage = box.get(key);
        if (savedMessage != null) {
          print('Verified message saved: ${savedMessage.message}');
          return true;
        } else {
          print(
              'WARNING: Message verification failed - not found in box after save');
          return false;
        }
      } catch (e) {
        print('Error verifying message save: $e');
        return false;
      }
    } catch (e) {
      print('Error saving message: $e');
      return false;
    }
  }

  // Save multiple messages at once
  Future<bool> saveMessages(List<ChatModel> messages) async {
    if (messages.isEmpty) {
      print('No messages to save');
      return false;
    }

    try {
      final box = await _getBox();

      // Create a map of keys to messages
      final Map<String, ChatModel> entries = {};
      for (final message in messages) {
        // Skip empty messages
        if (message.message == null || message.message!.isEmpty) {
          continue;
        }

        // Generate a key for the message
        final key = message.id ??
            message.tempId ??
            '${message.sender}-${message.recipient}-${message.timestamp?.millisecondsSinceEpoch}';

        // Ensure the message has a conversationId
        ChatModel messageToSave = message;
        if (message.conversationId == null &&
            message.sender != null &&
            message.recipient != null) {
          // Create a consistent conversation ID
          final List<String> users = [message.sender!, message.recipient!];
          users.sort();
          final conversationId = users.join('-');

          // Create a new message with the conversation ID
          messageToSave = ChatModel(
            id: message.id,
            tempId: message.tempId,
            message: message.message,
            sender: message.sender,
            recipient: message.recipient,
            senderModel: message.senderModel,
            recipientModel: message.recipientModel,
            timestamp: message.timestamp,
            conversationId: conversationId,
          );
        }

        entries[key] = messageToSave;
      }

      if (entries.isEmpty) {
        print('No valid messages to save after filtering');
        return false;
      }

      // Save all messages at once
      await box.putAll(entries);

      // Force flush to disk
      try {
        await box.flush();
      } catch (e) {
        print('Non-fatal error flushing to disk: $e');
      }

      print('Saved ${entries.length} messages to local storage');

      // Verify at least one message was saved
      if (entries.isNotEmpty) {
        try {
          final firstKey = entries.keys.first;
          final savedMessage = box.get(firstKey);
          if (savedMessage != null) {
            print(
                'Verified at least one message saved: ${savedMessage.message}');
            return true;
          } else {
            print(
                'WARNING: Message verification failed - first message not found in box after save');
            return false;
          }
        } catch (e) {
          print('Error verifying message save: $e');
          return false;
        }
      }

      return true;
    } catch (e) {
      print('Error saving messages batch: $e');
      return false;
    }
  }

  // Get all messages for a specific conversation
  Future<List<ChatModel>> getMessagesForConversation(
      String senderId, String recipientId) async {
    try {
      final box = await _getBox();

      // Check if this is a broadcast conversation (empty recipient)
      final bool isBroadcast = recipientId.isEmpty;
      String conversationId;

      if (isBroadcast) {
        // For broadcast messages, use the special format
        conversationId = '-$senderId';
        print(
            'Getting broadcast messages with conversationId: $conversationId');
      } else {
        // Create a consistent conversation ID for regular conversations
        final List<String> users = [senderId, recipientId];
        users.sort();
        conversationId = users.join('-');
      }

      print(
          'Getting messages for conversation: $conversationId, Box has ${box.length} total messages');

      // Debug: List all messages in the box
      print('All message keys in box:');
      for (var key in box.keys.take(10)) {
        // Limit to first 10 to avoid log spam
        final msg = box.get(key);
        print(
            'Key: $key, ConvId: ${msg?.conversationId}, Sender: ${msg?.sender}, Recipient: ${msg?.recipient}, isBroadcast: ${msg?.isBroadcast}');
      }
      if (box.length > 10) {
        print('... (${box.length - 10} more messages)');
      }

      // Filter messages for this conversation
      final messages = box.values.where((message) {
        // For broadcast messages, use special filtering
        if (isBroadcast) {
          // Check several conditions for a broadcast message:
          // 1. Has the special conversationId format
          final hasSpecialId = message.conversationId == conversationId;

          // 2. Is a broadcast message to/from this user
          final isBroadcastMessage = message.isBroadcast == true &&
              (message.sender == senderId || message.recipient == senderId);

          // 3. Sender and recipient are the same (self-messages)
          final isSelfMessage =
              message.sender == senderId && message.recipient == senderId;

          final isRelevant =
              hasSpecialId || isBroadcastMessage || isSelfMessage;

          if (isRelevant) {
            print(
                'Found broadcast message: ${message.message?.substring(0, math.min(20, message.message?.length ?? 0))}...');
          }

          return isRelevant;
        }

        // Regular message filtering
        // Check if the message has the correct conversation ID
        if (message.conversationId != null) {
          final match = message.conversationId == conversationId;
          if (match) {
            print(
                'Found message by conversationId: ${message.message?.substring(0, math.min(20, message.message?.length ?? 0))}...');
          }
          return match;
        }

        // Fall back to checking sender and recipient
        final senderRecipientMatch =
            (message.sender == senderId && message.recipient == recipientId);
        final recipientSenderMatch =
            (message.sender == recipientId && message.recipient == senderId);
        final match = senderRecipientMatch || recipientSenderMatch;

        if (match) {
          print(
              'Found message by sender/recipient: ${message.message?.substring(0, math.min(20, message.message?.length ?? 0))}...');
        }

        return match;
      }).toList();

      // Sort by timestamp
      messages.sort((a, b) => (a.timestamp ?? DateTime.now())
          .compareTo(b.timestamp ?? DateTime.now()));

      print(
          'Found ${messages.length} messages for conversation $conversationId');

      // Print all found messages
      for (int i = 0; i < math.min(5, messages.length); i++) {
        final msg = messages[i];
        print(
            'Message $i: ${msg.message?.substring(0, math.min(20, msg.message?.length ?? 0))}... from ${msg.sender} to ${msg.recipient}, isBroadcast: ${msg.isBroadcast}');
      }
      if (messages.length > 5) {
        print('... (${messages.length - 5} more messages)');
      }

      return messages;
    } catch (e) {
      print('Error getting messages for conversation: $e');
      return [];
    }
  }

  // Load messages from local storage
  Future<List<ChatModel>> loadMessages(
      String senderId, String recipientId) async {
    try {
      print(
          'Loading messages for conversation between $senderId and $recipientId');

      // Get messages using the existing method
      final messages = await getMessagesForConversation(senderId, recipientId);

      print(
          'Found ${messages.length} messages in local storage for conversation between $senderId and $recipientId');

      // Ensure all messages have a conversationId
      final List<String> users = [senderId, recipientId];
      users.sort();
      final conversationId = users.join('-');

      print('Using conversationId: $conversationId');

      // Update any messages that don't have a conversationId
      final List<ChatModel> updatedMessages = [];
      bool needsUpdate = false;

      for (final message in messages) {
        if (message.conversationId == null) {
          needsUpdate = true;
          print(
              'Message without conversationId: ${message.message} from ${message.sender} to ${message.recipient}');
          final updatedMessage = ChatModel(
            id: message.id,
            tempId: message.tempId,
            message: message.message,
            sender: message.sender,
            recipient: message.recipient,
            senderModel: message.senderModel,
            recipientModel: message.recipientModel,
            timestamp: message.timestamp,
            conversationId: conversationId,
          );
          updatedMessages.add(updatedMessage);
        } else {
          updatedMessages.add(message);
        }
      }

      // Save updated messages if needed
      if (needsUpdate) {
        print(
            'Updating ${updatedMessages.length} messages with conversationId');
        await saveMessages(updatedMessages);
        print('Messages updated with conversationId');
      } else {
        print('All messages already have conversationId');
      }

      print('Loaded ${updatedMessages.length} messages from local storage');

      // Print first and last message for debugging
      if (updatedMessages.isNotEmpty) {
        print(
            'First message: ${updatedMessages.first.message} from ${updatedMessages.first.sender} to ${updatedMessages.first.recipient}');
        print(
            'Last message: ${updatedMessages.last.message} from ${updatedMessages.last.sender} to ${updatedMessages.last.recipient}');
      }

      return updatedMessages;
    } catch (e) {
      print('Error loading messages: $e');
      return [];
    }
  }

  // Delete all messages for a conversation
  Future<void> deleteConversation(String senderId, String recipientId) async {
    try {
      final box = await _getBox();

      // Create a consistent conversation ID
      final List<String> users = [senderId, recipientId];
      users.sort();
      final conversationId = users.join('-');

      // Find all keys for this conversation
      final keysToDelete = <String>[];

      for (final entry in box.toMap().entries) {
        final message = entry.value;
        final messageConversationId = message.conversationId;

        if (messageConversationId == conversationId ||
            ((message.sender == senderId && message.recipient == recipientId) ||
                (message.sender == recipientId &&
                    message.recipient == senderId))) {
          keysToDelete.add(entry.key.toString());
        }
      }

      // Delete all messages for this conversation
      await box.deleteAll(keysToDelete);
      print(
          'Deleted ${keysToDelete.length} messages for conversation $conversationId');
    } catch (e) {
      print('Error deleting conversation: $e');
    }
  }

  // Clear all messages
  Future<void> clearAllMessages() async {
    try {
      final box = await _getBox();
      await box.clear();
      print('Cleared all messages from local storage');
    } catch (e) {
      print('Error clearing messages: $e');
    }
  }

  // Get the Hive box
  Future<Box<ChatModel>> _getBox() async {
    if (_box == null || !_box!.isOpen) {
      print('Box not available, initializing...');
      await initialize();
      if (_box == null || !_box!.isOpen) {
        throw Exception('Failed to initialize Hive box for chat messages');
      }
    }
    return _box!;
  }

  // Debug method to list all messages in storage
  Future<void> debugListAllMessages() async {
    try {
      final box = await _getBox();

      print('===== DEBUG: LISTING ALL MESSAGES IN STORAGE =====');
      print('Total messages in box: ${box.length}');

      // Group messages by conversation ID
      final Map<String, List<ChatModel>> messagesByConversation = {};

      for (final message in box.values) {
        final conversationId = message.conversationId ?? 'unknown';

        if (!messagesByConversation.containsKey(conversationId)) {
          messagesByConversation[conversationId] = [];
        }

        messagesByConversation[conversationId]!.add(message);
      }

      // Print messages grouped by conversation
      messagesByConversation.forEach((conversationId, messages) {
        print('Conversation: $conversationId - ${messages.length} messages');

        // Sort messages by timestamp
        messages.sort((a, b) => (a.timestamp ?? DateTime.now())
            .compareTo(b.timestamp ?? DateTime.now()));

        // Print first and last message if there are any
        if (messages.isNotEmpty) {
          final firstMessage = messages.first;
          final lastMessage = messages.last;

          print(
              '  First: ${firstMessage.message} from ${firstMessage.sender} to ${firstMessage.recipient} at ${firstMessage.timestamp}');

          if (messages.length > 1) {
            print(
                '  Last: ${lastMessage.message} from ${lastMessage.sender} to ${lastMessage.recipient} at ${lastMessage.timestamp}');
          }
        }
      });

      print('===== END DEBUG LISTING =====');
    } catch (e) {
      print('Error listing all messages: $e');
    }
  }

  // Check if a user has any messages (sent or received)
  Future<bool> hasAnyMessages(String userId) async {
    try {
      print('Checking if user $userId has any messages in local storage');
      final box = await _getBox();

      // Get all messages
      final allMessages = box.values.toList();

      // Check if any message involves this user as sender or recipient
      final hasMessages = allMessages.any(
          (message) => message.sender == userId || message.recipient == userId);

      print(
          'User $userId ${hasMessages ? "has" : "does not have"} messages in local storage');
      return hasMessages;
    } catch (e) {
      print('Error checking if user has messages: $e');
      return false; // Assume no messages on error
    }
  }

  // Clear all messages for a specific conversation ID
  Future<void> clearConversationMessages(String conversationId) async {
    try {
      final box = await _getBox();

      print('Clearing messages for conversation ID: $conversationId');

      // Find all keys for this conversation
      final keysToDelete = <String>[];

      for (final entry in box.toMap().entries) {
        final message = entry.value;

        // Check if the message belongs to this conversation
        if (message.conversationId == conversationId) {
          keysToDelete.add(entry.key.toString());
        }
      }

      if (keysToDelete.isEmpty) {
        print('No messages found for conversation ID: $conversationId');
        return;
      }

      // Delete all messages for this conversation
      await box.deleteAll(keysToDelete);
      print(
          'Deleted ${keysToDelete.length} messages for conversation ID: $conversationId');

      // Verify deletion
      int remainingMessages = 0;
      for (final message in box.values) {
        if (message.conversationId == conversationId) {
          remainingMessages++;
        }
      }

      if (remainingMessages > 0) {
        print(
            'WARNING: After deletion, still found $remainingMessages messages for conversation ID: $conversationId');
      } else {
        print(
            'Successfully cleared all messages for conversation ID: $conversationId');
      }
    } catch (e) {
      print('Error clearing messages for conversation: $e');
    }
  }
}
