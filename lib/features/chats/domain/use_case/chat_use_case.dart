// Domain Layer: Use Cases
import 'package:class_z/core/imports.dart';

class ConnectSocket {
  final ChatRepository repository;

  ConnectSocket(this.repository);

  Future<void> call(String userId) async {
    await repository.connectSocket(userId);
  }
}

class DisconnectSocket {
  final ChatRepository repository;

  DisconnectSocket(this.repository);

  Future<void> call() async {
    await repository.disconnectSocket();
  }
}

class SendMessage {
  final ChatRepository repository;

  SendMessage(this.repository);

  Future<void> call(ChatModel message) async {
    await repository.sendMessage(message);
  }
}

class FetchMessages {
  final ChatRepository repository;

  FetchMessages(this.repository);

  Future<List<ChatModel>> call(Map<String, dynamic> payload) async {
    return await repository.fetchMessages(payload);
  }
}

class ReceiveMessage {
  final ChatRepository repository;

  ReceiveMessage(this.repository);

  Stream<ChatModel> call() {
    return repository.receiveMessage();
  }
}

class GetLastMessages {
  final ChatRepository repository;

  GetLastMessages(this.repository);

  Future<List<LastMessageModel>> call(String userId) async {
    try {
      final result = await repository.getLastMessages(userId);
      return result;
    } catch (e) {
      print('Error in GetLastMessages use case: $e');
      // Return an empty list instead of throwing to prevent crashes
      return [];
    }
  }
}

class MarkMessagesAsRead {
  final ChatRepository repository;

  MarkMessagesAsRead(this.repository);

  Future<bool> call(List<String> messageIds) async {
    try {
      final result = await repository.markMessagesAsRead(messageIds);
      return result;
    } catch (e) {
      print('Error in MarkMessagesAsRead use case: $e');
      // Return false instead of throwing to prevent crashes
      return false;
    }
  }
}