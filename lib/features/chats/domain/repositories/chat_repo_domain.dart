import 'package:class_z/features/chats/data/models/chatModel.dart';
import 'package:class_z/features/chats/data/models/lastMessageModel.dart';

abstract class ChatRepoDomain {
  Future<List<LastMessageModel>> getLastMessage(String id);
  // Stream<List<ChatModel>> fetchMessage(Map<String, dynamic> message);
  Stream<ChatModel>? getMessageStream(Map<String, dynamic> payload);
  Future<void> connect();
  Future<void> disconnect();
  Future<void> sendMessage(Map<String, dynamic> message);
  Future<void> onMessageReceived(Function(String));
  Future<void> onConnect(Function() onConnect);
  Future<void> onDisconnect(Function() onDisconnect);
  Future<void> fetchMessage(Map<String, dynamic> payload);
  Future<void> register(String id);
}
