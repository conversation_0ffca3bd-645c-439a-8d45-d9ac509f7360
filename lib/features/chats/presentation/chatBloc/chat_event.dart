part of 'chat_bloc.dart';

abstract class ChatEvent extends Equatable {
  const ChatEvent();

  @override
  List<Object?> get props => [];
}

class ChatConnectEvent extends ChatEvent {
  final String userId;

  const ChatConnectEvent(this.userId);

  @override
  List<Object?> get props => [userId];
}

class ChatDisconnectEvent extends ChatEvent {
  @override
  List<Object?> get props => [];
}

class ChatReconnectEvent extends ChatEvent {
  final String? userId;

  const ChatReconnectEvent([this.userId]);

  @override
  List<Object?> get props => [userId];
}

class ChatSendMessageEvent extends ChatEvent {
  final Map<String, dynamic> message;

  const ChatSendMessageEvent(this.message);

  @override
  List<Object?> get props => [message];
}

class ChatFetchMessagesEvent extends ChatEvent {
  final Map<String, dynamic> payload;

  const ChatFetchMessagesEvent(this.payload);

  @override
  List<Object?> get props => [payload];
}

class ChatGetLastMessagesEvent extends ChatEvent {
  final String userId;

  const ChatGetLastMessagesEvent(this.userId);

  @override
  List<Object?> get props => [userId];
}

class ChatMarkMessagesAsReadEvent extends ChatEvent {
  final List<String> messageIds;

  const ChatMarkMessagesAsReadEvent(this.messageIds);

  @override
  List<Object?> get props => [messageIds];
}

class ChatMessageReceivedEvent extends ChatEvent {
  final ChatModel message;

  const ChatMessageReceivedEvent(this.message);

  @override
  List<Object?> get props => [message];
}

class ChatMessageStatusUpdateEvent extends ChatEvent {
  final String messageId;
  final String status;

  const ChatMessageStatusUpdateEvent(this.messageId, this.status);

  @override
  List<Object?> get props => [messageId, status];
}

class ChatEmptyMessagesEvent extends ChatEvent {
  // Event to signal that no messages were loaded and UI should transition
  const ChatEmptyMessagesEvent();

  @override
  List<Object?> get props => [];
}
