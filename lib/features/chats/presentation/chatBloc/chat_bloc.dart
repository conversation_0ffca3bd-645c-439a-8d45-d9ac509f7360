// Presentation Layer: BLoC
import 'package:class_z/core/imports.dart';
import 'dart:math' as math;

part 'chat_event.dart';
part 'chat_state.dart';

class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final ConnectSocket connectSocket;
  final DisconnectSocket disconnectSocket;
  final SendMessage sendMessage;
  final FetchMessages fetchMessages;
  final ReceiveMessage receiveMessage;
  final GetLastMessages getLastMessages;
  final MarkMessagesAsRead markMessagesAsRead;

  // Add subscription management
  StreamSubscription? _messageSubscription;
  Timer? _reconnectTimer;
  bool _isReconnecting = false;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 3;

  // Message cache to manage message states
  final Map<String, ChatModel> _messageCache = {};

  ChatBloc({
    required this.connectSocket,
    required this.disconnectSocket,
    required this.sendMessage,
    required this.fetchMessages,
    required this.receiveMessage,
    required this.getLastMessages,
    required this.markMessagesAsRead,
  }) : super(ChatInitial()) {
    on<ChatConnectEvent>(_onConnectSocket);
    on<ChatDisconnectEvent>(_onDisconnectSocket);
    on<ChatSendMessageEvent>(_onSendMessage);
    on<ChatFetchMessagesEvent>(_onFetchMessages);
    on<ChatGetLastMessagesEvent>(_onGetLastMessages);
    on<ChatMarkMessagesAsReadEvent>(_onMarkMessagesAsRead);
    on<ChatMessageReceivedEvent>(_onMessageReceived);
    on<ChatMessageStatusUpdateEvent>(_onMessageStatusUpdate);
    on<ChatReconnectEvent>(_onReconnect);
    on<ChatEmptyMessagesEvent>((event, emit) {
      // Handle the empty messages event by emitting a loaded state with empty list
      print('Handling empty messages event - transitioning to empty state');
      emit(ChatMessagesLoaded([]));
    });

    // Listen to incoming messages from the repository stream with error handling
    _subscribeToMessages();
  }

  // Add a new method to handle incoming messages and status updates
  Future<void> _onMessageReceived(
      ChatMessageReceivedEvent event, Emitter<ChatState> emit) async {
    final message = event.message;

    // Check if this is a status update (has ID and status but might be missing other fields)
    if (message.id != null &&
        message.status != null &&
        (message.message == null || message.message!.isEmpty)) {
      print(
          'Received status update for message ${message.id}: ${message.status}');

      // Update the message cache with the new status
      _updateMessageStatus(message.id!, message.status!);

      // Forward as status update event
      add(ChatMessageStatusUpdateEvent(message.id!, message.status!));
      return;
    }

    // If message has a tempId, it might be a confirmation of a message we sent
    if (message.tempId != null && message.id != null) {
      // This is a confirmation - map the temporary ID to the server ID
      print(
          'Received confirmation for message with tempId ${message.tempId} -> serverId ${message.id}');

      // Store the message in our cache
      _messageCache[message.id!] = message;
    }

    // This is a normal message, cache it
    if (message.id != null) {
      _messageCache[message.id!] = message;
    }

    // Emit the message received state
    emit(ChatMessageReceived(message));
  }

  // Add a method to handle message status updates
  Future<void> _onMessageStatusUpdate(
      ChatMessageStatusUpdateEvent event, Emitter<ChatState> emit) async {
    // Get the current message from cache if available
    final messageId = event.messageId;
    final status = event.status;

    print('Handling status update for message $messageId: $status');

    // Update message in cache with new status
    _updateMessageStatus(messageId, status);

    // Emit status update state with the updated message
    if (_messageCache.containsKey(messageId)) {
      emit(ChatMessageStatusUpdated(
          messageId, status, _messageCache[messageId]));
    } else {
      // If the message isn't in our cache yet, emit with a minimal message object
      final dummyMessage = ChatModel(
        id: messageId,
        status: status,
      );
      emit(ChatMessageStatusUpdated(messageId, status, dummyMessage));
    }
  }

  // Helper method to update a message's status in the cache
  void _updateMessageStatus(String messageId, String status) {
    if (_messageCache.containsKey(messageId)) {
      // Create a copy of the existing message with updated status
      final existingMessage = _messageCache[messageId]!;
      final updatedMessage = ChatModel(
        id: existingMessage.id,
        tempId: existingMessage.tempId,
        message: existingMessage.message,
        sender: existingMessage.sender,
        recipient: existingMessage.recipient,
        senderModel: existingMessage.senderModel,
        recipientModel: existingMessage.recipientModel,
        timestamp: existingMessage.timestamp,
        status: status, // Update the status
        conversationId: existingMessage.conversationId,
      );
      _messageCache[messageId] = updatedMessage;
      print('Updated message $messageId status to $status in cache');
    } else {
      // If the message isn't in our cache yet, add a minimal entry
      _messageCache[messageId] = ChatModel(
        id: messageId,
        status: status,
      );
      print(
          'Added message $messageId with status $status to cache (minimal entry)');
    }
  }

  void _subscribeToMessages() {
    try {
      // Cancel existing subscription first if any
      _messageSubscription?.cancel();

      // Subscribe to the message stream from the repository
      _messageSubscription = receiveMessage().listen(
        (message) {
          // Add message received event to the bloc
          if (!isClosed) {
            add(ChatMessageReceivedEvent(message));
          }
        },
        onError: (error) {
          print('Error in message subscription: $error');
          // Add error event if not closed
          if (!isClosed) {
            add(ChatReconnectEvent());
          }
        },
        onDone: () {
          print('Message stream closed, attempting to reconnect');
          // Attempt to reconnect if the stream closes unexpectedly
          if (!isClosed) {
            add(ChatReconnectEvent());
          }
        },
      );

      print('Successfully subscribed to message stream');
    } catch (e) {
      print('Error subscribing to messages: $e');
      // If subscription fails, try to reconnect
      if (!isClosed) {
        add(ChatReconnectEvent());
      }
    }
  }

  void _scheduleReconnect() {
    if (_reconnectTimer != null && _reconnectTimer!.isActive) {
      _reconnectTimer!.cancel();
    }

    _reconnectAttempts++;
    _isReconnecting = true;

    // Use exponential backoff with a maximum delay
    final delay = _calculateBackoff(_reconnectAttempts);
    print(
        'Scheduling reconnect attempt $_reconnectAttempts in ${delay.inSeconds} seconds');

    _reconnectTimer = Timer(delay, () {
      if (!isClosed) {
        print('Attempting reconnect #$_reconnectAttempts');
        add(ChatReconnectEvent());
      }
    });
  }

  Duration _calculateBackoff(int attempt) {
    // Exponential backoff: 2^n seconds with a maximum of 30 seconds
    // 1st attempt: 2 seconds
    // 2nd attempt: 4 seconds
    // 3rd attempt: 8 seconds
    // etc.
    final seconds = math.min(math.pow(2, attempt).toInt(), 30);
    return Duration(seconds: seconds);
  }

  List<LastMessageModel>? _lastmessaged = [];
  List<LastMessageModel>? get lastMessage => _lastmessaged;

  Future<void> _onConnectSocket(
      ChatConnectEvent event, Emitter<ChatState> emit) async {
    // Don't emit loading state to prevent loading dialog
    // emit(ChatLoading());
    try {
      print('Connecting to socket with user ID: ${event.userId}');

      // Add timeout to connection attempt
      final result = await connectSocket(event.userId).timeout(
          const Duration(seconds: 10),
          onTimeout: () =>
              throw TimeoutException('Connection timed out after 10 seconds'));

      // Reset reconnect attempts on successful connection
      _reconnectAttempts = 0;
      _isReconnecting = false;

      emit(ChatConnected());

      // Resubscribe to messages after a successful connection
      _subscribeToMessages();
    } on TimeoutException catch (_) {
      emit(ChatError(
          'Connection timed out. Please check your internet connection and try again.'));

      // Attempt to reconnect automatically
      _scheduleReconnect();
    } catch (e) {
      emit(ChatError('Failed to connect: ${e.toString().split('\n').first}'));

      // Attempt to reconnect automatically if not manually triggered
      if (!_isReconnecting && _reconnectAttempts < _maxReconnectAttempts) {
        _scheduleReconnect();
      }
    }
  }

  Future<void> _onReconnect(
      ChatReconnectEvent event, Emitter<ChatState> emit) async {
    if (_isReconnecting && _reconnectAttempts >= _maxReconnectAttempts) {
      emit(ChatError(
          'Maximum reconnection attempts reached. Please try again later.'));
      return;
    }

    try {
      // If user ID is provided, use it, otherwise use the cached one from previous connect
      final userId = event.userId ?? '';
      if (userId.isNotEmpty) {
        await connectSocket(userId).timeout(const Duration(seconds: 5),
            onTimeout: () => throw TimeoutException('Reconnection timed out'));

        // Reset reconnect attempts on successful connection
        _reconnectAttempts = 0;
        _isReconnecting = false;

        // Resubscribe to messages
        _subscribeToMessages();

        emit(ChatConnected());
      } else {
        throw Exception('Cannot reconnect: No user ID available');
      }
    } on TimeoutException catch (_) {
      // Schedule another reconnect attempt with exponential backoff
      _scheduleReconnect();
      emit(ChatError('Reconnection timed out. Retrying...'));
    } catch (e) {
      // Schedule another reconnect attempt with exponential backoff
      _scheduleReconnect();
      emit(ChatError('Reconnection failed: ${e.toString().split('\n').first}'));
    }
  }

  Future<void> _onDisconnectSocket(
      ChatDisconnectEvent event, Emitter<ChatState> emit) async {
    // Cancel any pending reconnect attempts
    _reconnectTimer?.cancel();
    _isReconnecting = false;

    // First emit disconnecting state to show progress
    emit(ChatDisconnecting());

    // Cancel message subscription immediately to stop receiving messages
    await _messageSubscription?.cancel();
    _messageSubscription = null;

    // CRITICAL: The most important change is to emit the disconnected state IMMEDIATELY
    // rather than waiting for the actual socket to disconnect
    emit(ChatDisconnected());

    try {
      // Run the actual disconnect in the background with a very short timeout
      // This prevents the UI from freezing while waiting for socket operations
      unawaited(disconnectSocket().timeout(
        const Duration(milliseconds: 200), // Much shorter timeout
        onTimeout: () {
          print('Disconnect operation timed out, but UI has already moved on');
          return;
        },
      ).catchError((e) {
        print('Background disconnect error (non-blocking): $e');
      }));
    } catch (e) {
      print('Disconnect error in event handler: $e');
      // Even if there's an error, we've already emitted ChatDisconnected,
      // so the UI will continue to work properly
    }
  }

  Future<void> _onSendMessage(
      ChatSendMessageEvent event, Emitter<ChatState> emit) async {
    final currentState = state;

    try {
      // Log sending attempt
      print('Attempting to send message: ${event.message['message']}');

      // Create the message model from the event data
      final message = ChatModel.fromJson(event.message);

      // IMPORTANT: We should NOT emit ChatMessageReceived here because:
      // 1. The UI already has local optimistic updates from _sendMessage
      // 2. This causes duplicate processing of the same message
      // 3. The message will come back from the server anyway via the socket stream

      // Instead, we'll emit a temporary state to acknowledge the send attempt
      emit(ChatMessageSending(message));

      // Check connection status before sending
      if (currentState is! ChatConnected) {
        // Try reconnecting first
        try {
          print(
              'Not connected, attempting to reconnect before sending message');
          if (message.sender != null) {
            await connectSocket(message.sender!).timeout(
                const Duration(seconds: 5),
                onTimeout: () => throw TimeoutException(
                    'Reconnection before send timed out'));

            // Reset reconnect attempts on successful connection
            _reconnectAttempts = 0;
            _isReconnecting = false;

            // Resubscribe to messages
            _subscribeToMessages();

            // Wait a moment for the connection to stabilize
            await Future.delayed(Duration(milliseconds: 300));
          }
        } catch (e) {
          print('Reconnection before sending failed: $e');
          // Continue anyway - we'll try to send the message regardless
        }
      }

      // Then send the message to the server
      await sendMessage(message).timeout(
          const Duration(seconds: 8), // Increased timeout for reliability
          onTimeout: () => throw TimeoutException('Message send timeout'));

      print('Message sent successfully to server');

      // If successful, the server will send back the message via socket
      // which will be handled by the message stream

      // Emit connected state to ensure UI knows we're still connected
      if (currentState is! ChatConnected) {
        emit(ChatConnected());
      }
    } on TimeoutException catch (_) {
      // Show error but don't remove the message from UI
      print('Message send timed out, message will remain in UI');
      emit(ChatError('Message send timed out. Please check your connection.'));

      // Try to reconnect in the background since there might be connection issues
      _scheduleReconnect();

      // Return to previous state to keep the UI showing the message
      if (currentState is! ChatError && currentState is! ChatLoading) {
        emit(currentState);
      }
    } catch (e) {
      // Show error but don't remove the message from UI
      print('Error sending message: $e');
      emit(ChatError(
          'Failed to send message: ${e.toString().split('\n').first}'));

      // Try to reconnect in the background
      if (!_isReconnecting && _reconnectAttempts < _maxReconnectAttempts) {
        _scheduleReconnect();
      }

      // Return to previous state to keep the UI showing the message
      if (currentState is! ChatError && currentState is! ChatLoading) {
        emit(currentState);
      }
    }
  }

  Future<void> _onFetchMessages(
      ChatFetchMessagesEvent event, Emitter<ChatState> emit) async {
    // Don't emit loading state to prevent loading dialog
    // Only emit loading state if we're not already loading or connected
    // This prevents double loading indicators when fetching messages right after connection
    // if (state is! ChatLoading && state is! ChatConnected) {
    //   emit(ChatLoading());
    // }

    try {
      print("Fetching messages with payload: ${event.payload}");

      final messages = await fetchMessages(event.payload).timeout(
          const Duration(seconds: 10),
          onTimeout: () =>
              throw TimeoutException('Fetching messages timed out'));

      if (messages.isEmpty) {
        print('No messages found between users, returning empty list');
      }

      // Always emit the loaded state, even with empty messages
      // This is crucial for the UI to transition out of loading
      emit(ChatMessagesLoaded(messages));
    } on TimeoutException catch (_) {
      print('Message fetch timed out');
      // Even on timeout, emit empty messages to get out of loading state
      emit(ChatMessagesLoaded([]));
      emit(ChatError('Fetching messages timed out. Please try again.'));
    } catch (e) {
      print('Error fetching messages: $e');
      // Again, emit empty messages first to get out of loading state
      emit(ChatMessagesLoaded([]));

      // Provide a more user-friendly error message
      final String errorMessage = e.toString().contains('parse')
          ? 'Unable to load messages. The server response format was unexpected.'
          : 'Failed to fetch messages: ${e.toString().split('\n').first}';

      emit(ChatError(errorMessage));

      // After an error, try to reconnect if we're not already
      if (!_isReconnecting && _reconnectAttempts < _maxReconnectAttempts) {
        _scheduleReconnect();
      }
    }
  }

  Future<void> _onGetLastMessages(
      ChatGetLastMessagesEvent event, Emitter<ChatState> emit) async {
    // Don't emit loading state to prevent loading dialog
    // emit(ChatLoading());
    try {
      final lastMessages = await getLastMessages(event.userId).timeout(
          const Duration(seconds: 10),
          onTimeout: () =>
              throw TimeoutException('Fetching last messages timed out'));

      _lastmessaged = lastMessages;
      emit(ChatLastMessagesLoaded(lastMessages));
    } on TimeoutException catch (_) {
      emit(ChatError('Fetching last messages timed out. Please try again.'));
    } catch (e) {
      emit(ChatError(
          'Failed to fetch last messages: ${e.toString().split('\n').first}'));
    }
  }

  Future<void> _onMarkMessagesAsRead(
      ChatMarkMessagesAsReadEvent event, Emitter<ChatState> emit) async {
    try {
      // Store the current state to return to it after operation
      final currentState = state;

      print('Marking messages as read: ${event.messageIds.length} messages');

      final success = await markMessagesAsRead(event.messageIds).timeout(
          const Duration(seconds: 5),
          onTimeout: () =>
              throw TimeoutException('Marking messages as read timed out'));

      if (success) {
        print('Messages marked as read successfully');
        emit(ChatMessagesMarkedAsRead(event.messageIds));

        // Return to previous state to maintain UI consistency
        if (currentState is! ChatMessagesMarkedAsRead &&
            currentState is! ChatError &&
            currentState is! ChatLoading) {
          emit(currentState);
        }
      } else {
        print('Failed to mark messages as read');
        // Only emit error if it's a critical operation, otherwise just log
        // This prevents disrupting the user experience for a non-critical operation

        // Return to previous state without showing error
        if (currentState is! ChatMessagesMarkedAsRead &&
            currentState is! ChatError &&
            currentState is! ChatLoading) {
          emit(currentState);
        }
      }
    } on TimeoutException catch (_) {
      // Don't disrupt the user experience for read receipts timeout
      print('Marking messages as read timed out');
    } catch (e) {
      // Don't disrupt the user experience for read receipts errors
      print('Error marking messages as read: $e');
    }
  }

  @override
  Future<void> close() async {
    print("ChatBloc closing - cleanup started");

    // Cancel timers and subscriptions immediately
    try {
      _reconnectTimer?.cancel();
    } catch (e) {
      print("Non-fatal error canceling reconnect timer: $e");
    }

    try {
      await _messageSubscription?.cancel();
    } catch (e) {
      print("Non-fatal error canceling message subscription: $e");
    }

    _messageSubscription = null;

    // CRITICAL: Don't wait for disconnection to complete when closing the bloc
    try {
      // Fire and forget the disconnect operation with no-op callbacks
      // to prevent any errors from propagating
      print("Initiating non-blocking disconnect during bloc close");

      unawaited(disconnectSocket()
          .timeout(
            const Duration(milliseconds: 100), // Ultra short timeout
            onTimeout: () {
              print(
                  'Disconnect timed out during bloc close - continuing closure');
              return;
            },
          )
          .then((_) =>
              print("Disconnect completed successfully during bloc close"))
          .catchError((e) {
            print('Non-fatal error during disconnection in close(): $e');
          }));
    } catch (error) {
      print('Non-fatal error initiating disconnection in close(): $error');
    }

    print("ChatBloc close cleanup completed - closing bloc");
    return super.close();
  }
}
