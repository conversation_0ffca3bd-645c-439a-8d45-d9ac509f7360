part of 'chat_bloc.dart';

abstract class ChatState extends Equatable {
  const ChatState();

  @override
  List<Object> get props => [];
}

class ChatInitial extends ChatState {}

class ChatLoading extends ChatState {}

class ChatConnected extends ChatState {
  @override
  List<Object> get props => [];
}

class ChatDisconnected extends ChatState {
  @override
  List<Object> get props => [];
}

class ChatDisconnecting extends ChatState {
  @override
  List<Object> get props => [];
}

class ChatMessageReceived extends ChatState {
  final ChatModel message;

  const ChatMessageReceived(this.message);

  @override
  List<Object> get props => [message];
}

class ChatMessageSending extends ChatState {
  final ChatModel message;

  const ChatMessageSending(this.message);

  @override
  List<Object> get props => [message];
}

class ChatMessageStatusUpdated extends ChatState {
  final String messageId;
  final String status;
  final ChatModel? message;

  const ChatMessageStatusUpdated(this.messageId, this.status, [this.message]);

  @override
  List<Object> get props => [messageId, status, if (message != null) message!];
}

class ChatMessagesLoaded extends ChatState {
  final List<ChatModel> messages;

  const ChatMessagesLoaded(this.messages);

  @override
  List<Object> get props => [messages];
}

class ChatMessagesMarkedAsRead extends ChatState {
  final List<String> messageIds;

  const ChatMessagesMarkedAsRead(this.messageIds);

  @override
  List<Object> get props => [messageIds];
}

class ChatLastMessagesLoaded extends ChatState {
  final List<LastMessageModel> lastMessages;

  const ChatLastMessagesLoaded(this.lastMessages);

  @override
  List<Object> get props => [lastMessages];
}

class ChatError extends ChatState {
  final String error;

  const ChatError(this.error);

  @override
  List<Object> get props => [error];
}
