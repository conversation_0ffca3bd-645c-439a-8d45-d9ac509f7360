import 'package:class_z/core/imports.dart';



class CenterMessages extends StatefulWidget {
  final String userType;
  const CenterMessages({required this.userType, super.key});

  @override
  State<CenterMessages> createState() => _CenterMessagesState();
}

class _CenterMessagesState extends State<CenterMessages> {
  String userId = "";
  List<LastMessageModel>? lastMessages;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _initialize());
  }

  void _initialize() {
    final sharedRepo = locator<SharedRepository>();

    userId = widget.userType == 'center'
        ? sharedRepo.getCenterId()
        : sharedRepo.getUserId();

    lastMessages = context.read<ChatBloc>().lastMessage;

    if (lastMessages == null ||
        lastMessages!.isEmpty ||
        lastMessages?.length == 0) {
      _fetchLastMessages();
    } else
      setState(() {});
  }

  void _fetchLastMessages() {
    context.read<ChatBloc>().add(ChatGetLastMessagesEvent(userId));
  }

  void _navigateToChat(LastMessageModel message) {
    String? imagePath = locator<SharedRepository>().getParentData()?.image?.url;
    
    // Use the toNavigationData method to get consistent navigation parameters
    final navigationData = message.toNavigationData();
    
    // Add sender information to the navigation data
    navigationData['senderId'] = userId;
    navigationData['senderType'] = widget.userType;
    navigationData['senderImage'] = imagePath;
    
    print('Navigating to chat with: ${navigationData['title']}, isBroadcast: ${navigationData['isBroadcast']}');
    
    NavigatorService.pushNamed(
      AppRoutes.chat,
      arguments: navigationData,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title:widget.userType=='center'? "Center Message": "User Message",
        leading: customBackButton(),
      ),
      body: BlocListener<ChatBloc, ChatState>(
        listener: (context, state) {
          if (state is ChatLoading) {
            loadingState(context: context);
          } else {
            hideLoadingDialog(context);
          }

          if (state is ChatError) {
            errorState(context: context, error: state.error);
          }

          if (state is ChatLastMessagesLoaded) {
            setState(() {
              lastMessages = state.lastMessages;
            });
          }
        },
        child: RefreshIndicator(
          onRefresh: () async => _fetchLastMessages(),
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTitle(context),
                _buildMessagesList(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 25.w, top: 16.h),
      child: customtext(
        context: context,
        newYear: "Messages",
        font: 17.sp,
        weight: FontWeight.w600,
      ),
    );
  }

  Widget _buildMessagesList() {
    if (lastMessages == null || lastMessages!.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.only(top: 100.h),
          child: customtext(
            context: context,
            newYear: "No messages yet",
            font: 16.sp,
            weight: FontWeight.w400,
          ),
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 22.h),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: lastMessages!.length,
        separatorBuilder: (_, __) => SizedBox(height: 7.h),
        itemBuilder: (context, index) {
          final message = lastMessages![index];
          return InkWell(
            onTap: () => _navigateToChat(message),
            child: centerMessageCard(
              context: context,
              imagePath: imageStringGenerator(
                imagePath: message.mainImage?.url ?? '',
              ),
              name: message.name ?? 'unknown',
              message: message.lastMessage ?? '',
              read: false, // Maybe make this dynamic later?
              pastTime: message.lastMessageTime ?? DateTime.now(),
            ),
          );
        },
      ),
    );
  }
}
