import 'package:class_z/core/imports.dart';

class CoachManagementGrid extends StatelessWidget {
  const CoachManagementGrid({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBarDouble(
        title: "Coach Management",
        title2: "Manage your coaches",
        leading: customBackButton(),
      ),
      body: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildGridItem(
                    context,
                    "Request",
                    "View coach requests",
                    ImagePath.pendingSvg,
                    () => NavigatorService.pushNamed(
                        AppRoutes.centerCoachRequest),
                  ),
                ),
                SizedBox(width: 20.w),
                Expanded(
                  child: _buildGridItem(
                    context,
                    "Available Coach",
                    "View available coaches",
                    ImagePath.groupSvg,
                    () =>
                        NavigatorService.pushNamed(AppRoutes.availableCoaches),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            Row(
              children: [
                Expanded(
                  child: _buildGridItem(
                    context,
                    "Search Coach",
                    "Search for coaches",
                    ImagePath.filterSvg,
                    () => NavigatorService.pushNamed(AppRoutes.centerCoach),
                  ),
                ),
                SizedBox(width: 20.w),
                Expanded(
                  child: _buildGridItem(
                    context,
                    "Change Coach",
                    "Change class coach",
                    ImagePath.addChildSvg,
                    () => NavigatorService.pushNamed(AppRoutes.changeCoach),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGridItem(
    BuildContext context,
    String title,
    String subtitle,
    String imagePath,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: AppPallete.white,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            customSvgPicture(
              imagePath: imagePath,
              height: 32.h,
              width: 32.w,
              color: AppPallete.secondaryColor,
            ),
            SizedBox(height: 12.h),
            customtext(
              context: context,
              newYear: title,
              font: 18.sp,
              weight: FontWeight.w700,
            ),
            SizedBox(height: 4.h),
            customtext(
              context: context,
              newYear: subtitle,
              font: 14.sp,
              weight: FontWeight.w400,
              color: AppPallete.greyWord,
            ),
          ],
        ),
      ),
    );
  }
}
