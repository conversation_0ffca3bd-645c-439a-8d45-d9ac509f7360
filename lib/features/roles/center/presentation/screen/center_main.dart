import 'package:class_z/core/imports.dart';
import 'package:class_z/core/network/endpoints.dart';
import 'package:class_z/features/transactions/data/models/daily_transaction_model.dart';

class CenterMain extends StatefulWidget {
  final CenterData center;
  const CenterMain({super.key, required this.center});

  @override
  State<CenterMain> createState() => _CenterMainState();
}

class _CenterMainState extends State<CenterMain> {
  List<EventModel> events = [];
  List<PendingModel> pendings = [];
  bool _isLoadingPendings = false;
  bool _hasMorePendings = true;
  final int _pendingsLimit =
      5; // Limit the number of pendings to fetch initially
  bool _isInitialLoad = true;

  // Add transaction state
  List<DailyTransactionModel> _recentTransactions = [];
  bool _isLoadingTransactions = false;

  // State for total earnings
  double _totalEarnings = 0.0;
  bool _isLoadingEarnings = false;

  // Initialize with empty lists and update when data is loaded
  void _initializeLists() {
    events = [];
    pendings = [];
  }

  @override
  void initState() {
    super.initState();
    _initializeLists();

    // Get the current user ID
    final userData = context.read<SharedRepository>().getUserData();
    final userId = userData?.data?.center?.id ??
        userData?.data?.owner?.id ??
        userData?.data?.coach?.id ??
        userData?.data?.parent?.id;

    if (userId != null && userId.isNotEmpty) {
      context
          .read<NotificationBloc>()
          .add(GetNotificationsEvent(userId: userId));
    } else {
      debugPrint('User ID is null or empty, skipping notification fetch');
    }

    _fetchData();
    _checkForRecentTransactions();
    _fetchTotalEarnings();
  }

  Future<void> _fetchData() async {
    try {
      setState(() {
        _isLoadingPendings = true;
      });

      final bloc = context.read<CenterBloc>();
      final centerId = widget.center.id ?? '';
      final date = dateGenerator(
          date: DateTime.now(), monthName: false, format: 'yyyy-MM-dd');
      debugPrint('date: $date');

      // Split the data fetching into two parallel operations
      await Future.wait([
        // Fetch events
        Future(() {
          bloc.add(GetAllEventsEvent(
              filterType: 'centerId',
              filterValue: centerId,
              date: date,
              today: true));
        }),

        // Fetch pending reviews
        Future(() {
          bloc.add(GetPendingReviewEvent(centerId));
        }),
      ]);

      _isInitialLoad = false;
    } catch (e) {
      debugPrint('Error fetching data: $e');
      setState(() {
        _isLoadingPendings = false;
        _isInitialLoad = false;
      });
    }
  }

  // Fetch only pending reviews - more efficient than reloading everything
  void _fetchPendingReviewsOnly() {
    final bloc = context.read<CenterBloc>();
    final centerId = widget.center.id ?? '';

    if (centerId.isEmpty) return;

    setState(() {
      _isLoadingPendings = true;
    });

    bloc.add(GetPendingReviewEvent(centerId));
  }

  // Add this method to check for recent transactions
  Future<void> _checkForRecentTransactions() async {
    if (!mounted) return;

    setState(() {
      _isLoadingTransactions = true;
    });

    try {
      final sharedRepo = locator<SharedRepository>();
      final token = sharedRepo.getToken();
      final centerId = widget.center.id;

      if (centerId == null || centerId.isEmpty || token == null) {
        throw Exception('User not logged in or Center ID not available');
      }

      // Get today's date
      final today = DateTime.now();
      final formattedDate = DateFormat('yyyy-MM-dd').format(today);

      final apiService = ApiService(baseUrl: Endpoints.baseUrl);

      final response = await apiService.get(
        '${Endpoints.transactions}/center/$centerId/daily',
        token: token,
        queryParameters: {'date': formattedDate},
      );

      if (response is Map && response['success'] == true) {
        if (response['data'] is List) {
          final List<dynamic> transactionsData = response['data'];
          if (mounted) {
            setState(() {
              _recentTransactions = transactionsData
                  .map((data) => DailyTransactionModel.fromJson(
                      data as Map<String, dynamic>))
                  .toList();
            });
          }
        }
      }
    } catch (e) {
      debugPrint('Error fetching recent transactions: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingTransactions = false;
        });
      }
    }
  }

  // Fetch total earnings
  Future<void> _fetchTotalEarnings() async {
    if (!mounted) return;

    setState(() {
      _isLoadingEarnings = true;
    });

    try {
      final sharedRepo = locator<SharedRepository>();
      final token = sharedRepo.getToken();
      final centerId = sharedRepo.getCenterId();

      if (centerId.isEmpty || token == null) {
        throw Exception('User not logged in or Center ID not available');
      }

      final apiService = ApiService(baseUrl: Endpoints.baseUrl);

      // Use the new endpoint for total earnings
      final response = await apiService.get(
        '${Endpoints.transactions}/center/$centerId/total-earnings',
        token: token,
      );

      if (response is Map && response['success'] == true) {
        if (mounted) {
          setState(() {
            _totalEarnings = (response['data']?['total'] ?? 0.0).toDouble();
          });
        }
      }
    } catch (e) {
      debugPrint('Error fetching total earnings: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingEarnings = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          await _fetchData();
          await _checkForRecentTransactions();
          await _fetchTotalEarnings();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: BlocListener<CenterBloc, CenterState>(
              listener: (context, state) {
                debugPrint('current state: $state');
                if (state is CenterLoadingState) {
                  // Don't show full screen loading for better UX
                  // Keep the loading indicator only for the specific section
                } else if (state is CenterErrorState) {
                  setState(() {
                    _isLoadingPendings = false;
                  });
                  errorState(context: context, error: state.message);
                } else if (state is EventListFetchSuccessToday) {
                  setState(() {
                    events = state.events;
                  });
                } else if (state is PendingReviewFetchSuccess) {
                  debugPrint(
                      'pending review success ${state.pendingReviews.length}');
                  setState(() {
                    // If we got fewer items than requested, there are no more to load
                    _hasMorePendings =
                        state.pendingReviews.length >= _pendingsLimit;

                    // Replace the list instead of appending since we're not implementing
                    // true pagination yet (would require API changes)
                    pendings = state.pendingReviews;
                    _isLoadingPendings = false;
                  });
                }
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _stack(
                      context: context,
                      rating: widget.center.rating.toString()),
                  _sectionHeader(
                    context,
                    title: "Today's check-ins ",
                    onTap: () => NavigatorService.pushNamed(AppRoutes.checkin,
                        arguments: events),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 15.w, top: 11.h),
                    child: events.isEmpty
                        ? _noDataText(context, "No Check-IN today")
                        : SizedBox(
                            height: 134,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: events.length,
                              itemBuilder: (context, index) {
                                final event = events[index];
                                return Padding(
                                  padding: const EdgeInsets.only(right: 10.0),
                                  child: CheckInCard(
                                    context: context,
                                    event:
                                        event, // No need for null check here as we're iterating over non-null list
                                    onTap: () {
                                      NavigatorService.pushNamed(
                                        AppRoutes.verificationCode,
                                        arguments: {
                                          "classId": event.classId?.id,
                                          "totalStudent":
                                              event.classId?.student?.length,
                                          "bloc": context.read<CenterBloc>(),
                                        },
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
                          ),
                  ),
                  SizedBox(height: 18.h),
                  customDivider(padding: 16.w),
                  SizedBox(height: 19.h),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 37.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _fourRow(
                            context: context,
                            title: "Upcoming",
                            number: events
                                .where((event) =>
                                    event.date != null &&
                                    event.date!.isAfter(DateTime.now()))
                                .length,
                            width: 60.w),
                        _fourRow(
                            context: context,
                            title: "Required",
                            number: pendings.length,
                            width: 47.w),
                        _fourRow(
                            context: context,
                            title: "Vacancy",
                            number: (() {
                              int total = 0;
                              for (var event in events) {
                                final capacity =
                                    event.classId?.numberOfStudent ?? 0;
                                final enrolled =
                                    event.classId?.student?.length ?? 0;
                                total += (capacity - enrolled) as int;
                              }
                              return total;
                            })(),
                            width: 71.w),
                      ],
                    ),
                  ),
                  SizedBox(height: 18.h),
                  customDivider(width: 398.w, padding: 16.w),
                  SizedBox(height: 19.h),
                  _sectionHeader(
                    context,
                    title: "Pending review",
                    onTap: () => NavigatorService.pushNamed(
                        AppRoutes.pendingReviewsbyClass,
                        arguments: {
                          'centerId': locator<SharedRepository>().getCenterId()
                        }),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 15.w, top: 11.h),
                    child: Column(
                      children: [
                        SizedBox(
                          height: 126.h,
                          child: _isLoadingPendings &&
                                  (_isInitialLoad || pendings.isEmpty)
                              ? Center(
                                  child: CircularProgressIndicator(),
                                )
                              : pendings.isEmpty
                                  ? _noDataText(context, "No pending review")
                                  : ListView.separated(
                                      scrollDirection: Axis.horizontal,
                                      itemCount: pendings.length,
                                      separatorBuilder: (_, __) =>
                                          SizedBox(width: 10.w),
                                      itemBuilder: (context, index) {
                                        final pending = pendings[index];
                                        return pendingReviewCard(
                                          context: context,
                                          imagePath: pending
                                                  .studentId?.mainImage?.url ??
                                              "",
                                          name: pending.studentId?.fullname ??
                                              "Unknown name",
                                          course: pending.plainClassDetails
                                                  ?.classProviding ??
                                              "",
                                          dateTime: dateGenerator(
                                              date: pending.event?.date),
                                          onTap: () {
                                            NavigatorService.pushNamed(
                                              AppRoutes.centerProgressCheck,
                                              arguments: pending,
                                            ).then((_) {
                                              // Only refresh pending reviews, not everything
                                              _fetchPendingReviewsOnly();
                                            });
                                          },
                                        );
                                      },
                                    ),
                        ),
                        if (_isLoadingPendings && pendings.isNotEmpty)
                          Padding(
                            padding: EdgeInsets.only(top: 10.h),
                            child: SizedBox(
                              height: 20.h,
                              width: 20.w,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  SizedBox(height: 10),
                ],
              )),
        ),
      ),
    );
  }

  Widget _sectionHeader(BuildContext context,
      {required String title, required VoidCallback onTap}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          customtext(
            context: context,
            newYear: title,
            font: 20.sp,
            weight: FontWeight.w600,
          ),
          GestureDetector(
            onTap: onTap,
            child: customtext(
              context: context,
              newYear: "see all",
              font: 15.sp,
              weight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _noDataText(BuildContext context, String message) {
    return Center(
      child: customtext(
        context: context,
        newYear: message,
        font: 20.sp,
      ),
    );
  }

  Widget _stack({required BuildContext context, required String rating}) {
    return SizedBox(
        height: 470.h,
        child: Stack(
          children: [
            Container(
              height: 225.h,
              decoration: BoxDecoration(
                gradient: GradientProvider.getLinearGradient(),
              ),
            ),
            BlocBuilder<NotificationBloc, NotificationState>(
              builder: (context, state) {
                int unreadCount = 0;

                if (state is NotificationLoaded) {
                  unreadCount =
                      state.notifications.where((n) => n.isRead != true).length;
                } else if (state is NotificationError) {
                  debugPrint('Error loading notifications: ${state.message}');
                }

                return customTopBarOnlyIcon(
                  context: context,
                  icon2: Icons.settings,
                  badgeCount1: unreadCount,
                  badgeCount2: 0,
                  onTap1: () {
                    NavigatorService.pushNamed(
                      AppRoutes.notification,
                      arguments: widget.center.id,
                    );
                  },
                  onTap2: () {
                    NavigatorService.pushNamed(
                      AppRoutes.centerSettings,
                      arguments: widget.center,
                    );
                  },
                );
              },
            ),
            Positioned(
                top: 89.h,
                left: 23.w,
                child: CustomImageBuilder(
                    imagePath: imageStringGenerator(
                        imagePath: widget.center.mainImage?.url ?? ''),
                    height: 91.h,
                    width: 91.w,
                    borderRadius: 91.w)),
            Positioned(
                top: 108.h,
                left: 124.w,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    customtext(
                        context: context,
                        newYear: widget.center.legalName ?? "",
                        color: Colors.white,
                        font: 25.sp,
                        weight: FontWeight.w600),
                    SizedBox(
                      height: 3.h,
                    ),
                    customtext(
                        context: context,
                        newYear: widget.center.address?.city ?? "",
                        color: Colors.white,
                        font: 12.sp,
                        weight: FontWeight.w600),
                    SizedBox(
                      height: 3.h,
                    ),
                    customtext(
                        context: context,
                        newYear: widget.center.classZId ?? "invalid id",
                        color: Colors.white,
                        font: 15.sp,
                        weight: FontWeight.w600),
                  ],
                )),
            Positioned(
              top: 143.h,
              right: 44.8.w,
              child: Container(
                decoration: BoxDecoration(
                    boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
                height: 40.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    customSvgPicture(
                        imagePath: ImagePath.starSvg,
                        color: AppPallete.rating,
                        height: 17.5.h,
                        width: 18.9.w),
                    SizedBox(
                      width: 3.5.w,
                    ),
                    customtext(
                        context: context,
                        newYear: rating,
                        font: 20.sp,
                        weight: FontWeight.w700,
                        color: Colors.white)
                  ],
                ),
              ),
            ),
            Positioned(
                top: 189.h,
                left: 23.w,
                right: 23.w,
                child: Container(
                    height: 240.h,
                    width: double.infinity,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.r),
                        color: Colors.white,
                        boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
                    child: _afterStack(context: context, amount: 999999999)))
          ],
        ));
  }

  Widget _afterStack({required BuildContext context, required double amount}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 19.h, left: 12.w),
          child: customtext(
              context: context,
              newYear: "Earnings",
              font: 15.sp,
              weight: FontWeight.w600),
        ),
        // Display the actual total earnings fetched from the API
        Padding(
          padding: EdgeInsets.only(top: 13.h, left: 12.w),
          child: _isLoadingEarnings
              ? SizedBox(
                  height: 15.h,
                  width: 15.w,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                )
              : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    customSvgPicture(
                      imagePath: ImagePath.zSvg,
                      height: 15.h,
                      width: 15.w,
                    ),
                    SizedBox(width: 4.w),
                    customtext(
                      context: context,
                      newYear:
                          "${_totalEarnings.toStringAsFixed(0)} / HKD ${(_totalEarnings * 25).toStringAsFixed(0)}",
                      font: 15.sp,
                      weight: FontWeight.w600,
                    ),
                  ],
                ),
        ),
        SizedBox(
          height: 18.h,
        ),
        customDivider(width: 362.w, padding: 9.w),
        Padding(
          padding: EdgeInsets.only(left: 38.w, right: 44.w, top: 15.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              messageIcon(
                  context: context,
                  svg: ImagePath.commentSvg,
                  title: "message",
                  badgeCount: 0,
                  height: 30,
                  width: 30,
                  onTap: () {
                    NavigatorService.pushNamed(AppRoutes.centerMessage,
                        arguments: 'center');
                  }),
              messageIcon(
                context: context,
                svg: ImagePath.transactionSvg,
                title: "Transaction",
                onTap: () {
                  NavigatorService.pushNamed(AppRoutes.transaction).then((_) {
                    // Refresh data when returning from transaction screen
                    _fetchData();
                    _checkForRecentTransactions();
                  });
                },
                height: 30,
                width: 30,
                badgeCount: _recentTransactions.isNotEmpty
                    ? _recentTransactions.length
                    : 0, // Show badge with count of transactions
              ),
              messageIcon(
                context: context,
                svg: ImagePath.addChildSvg,
                onTap: () {
                  NavigatorService.pushNamed(AppRoutes.coachManagementGrid);
                },
                title: "Coach",
                height: 30,
                width: 30,
              )
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 38.w, right: 44.w, top: 18.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _makeIcon(
                  context: context,
                  svg: ImagePath.classSvg,
                  title: "Class",
                  height: 49.h,
                  width: 52.w,
                  onTap: () {
                    NavigatorService.pushNamed(AppRoutes.centerSavedClass,
                        arguments: {
                          'centerId': widget.center.id,
                        });
                  }),
              _makeIcon(
                  context: context,
                  svg: ImagePath.slotSvg,
                  title: "Slot",
                  height: 49.h,
                  width: 52.w,
                  onTap: () {
                    NavigatorService.pushNamed(AppRoutes.centerTimeSlotEdit);
                  }),
              _makeIcon(
                  context: context,
                  svg: ImagePath.promoteSvg,
                  title: "Announce",
                  height: 49.h,
                  width: 52.w,
                  onTap: () {
                    NavigatorService.pushNamed(AppRoutes.centerAnnouncement,
                        arguments: {
                          'filterType': 'centerId',
                          'filterValue': widget.center.id,
                        });
                  })
            ],
          ),
        )
      ],
    );
  }

  Widget _makeIcon(
      {required BuildContext context,
      required String svg,
      required String title,
      required double height,
      required double width,
      required VoidCallback onTap}) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: SizedBox(
          //  height: height,
          //  width: width,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              customSvgPicture(
                  imagePath: svg,
                  height: 37.h,
                  width: 37.w,
                  color: Colors.black),
              Center(
                child: customtext(
                    context: context,
                    newYear: title,
                    font: 10.sp,
                    weight: FontWeight.w600,
                    color: Colors.black),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _fourRow(
      {required BuildContext context,
      required String title,
      required int number,
      required double width}) {
    return SizedBox(
      // height: 40.h,
      width: width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: customtext(
                context: context,
                newYear: "$number",
                font: 20.sp,
                weight: FontWeight.w600,
                color: Colors.black),
          ),
          Center(
            child: customtext(
                context: context,
                newYear: title,
                font: 10.sp,
                // weight: FontWeight.w600,
                color: Colors.black),
          ),
        ],
      ),
    );
  }
}
