import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:flutter/services.dart';

class QRScannerPage extends StatelessWidget {
  final Function(String) onScanned;

  QRScannerPage({required this.onScanned});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: MobileScanner(
        onDetect: (barcode) {
          final BarcodeCapture barcodeCapture = barcode;
          if (barcodeCapture.barcodes.isNotEmpty) {
            final String? code = barcodeCapture.barcodes.first.rawValue;
            if (code != null && code.isNotEmpty) {
              Clipboard.setData(ClipboardData(text: code)).then((_) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('QR code data copied to clipboard!')),
                );
              });
              onScanned(code);
              Navigator.pop(context);
              // NavigatorService.goBack(); // Close the scanner page
            }
          }
        },
      ),
    );
  }
}
