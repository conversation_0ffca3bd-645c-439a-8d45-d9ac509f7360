import 'package:class_z/core/imports.dart';

class CenterProfileSetup6 extends StatefulWidget {
  final Map<String, dynamic> data;
  const CenterProfileSetup6({required this.data, super.key});

  @override
  State<CenterProfileSetup6> createState() => _CenterProfileSetup6State();
}

class _CenterProfileSetup6State extends State<CenterProfileSetup6> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.only(left: 21.w, right: 28.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomAppBar(
              title: 'PassCode',
              leading: customBackButton(),
            ),
            Sized<PERSON><PERSON>(
              height: 43.h,
            ),
            Center(
              child: CustomImageBuilder(
                imagePath: widget.data["mainImage"].path,
                height: 125.w,
                width: 125.w,
                borderRadius: 125.w,
              ),
            ),
            Sized<PERSON>ox(
              height: 23.h,
            ),
            customtext(
                context: context,
                newYear:
                    'Setup a 4-digit passcode for switching between branches',
                font: 17.sp,
                weight: FontWeight.w500),
            Sized<PERSON><PERSON>(
              height: 20.h,
            ),
            customRequiredText(
                context: context,
                title: 'passcode',
                font: 15.sp,
                weight: FontWeight.w400),
            SizedBox(
              height: 20.h,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.5.w),
              child: CustomPinCodeTextField(
                context: context,
              ),
            ),
            SizedBox(
              height: 20.h,
            ),
            customRequiredText(
                context: context,
                title: 're-type passcode',
                font: 15.sp,
                weight: FontWeight.w400),
            SizedBox(
              height: 20.h,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.5.w),
              child: CustomPinCodeTextField(
                context: context,
              ),
            ),
            Spacer(),
            Center(
              child: Button(
                buttonText: "create",
                color: AppPallete.secondaryColor,
                height: 49.h,
                width: 289.w,
                onPressed: () {
                  NavigatorService.pushNamed(
                    AppRoutes.openProfile,
                  );
                },
              ),
            ),
            SizedBox(
              height: 20.h,
            )
          ],
        ),
      ),
    );
  }
}
