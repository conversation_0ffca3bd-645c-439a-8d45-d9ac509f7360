import 'package:class_z/core/imports.dart';

class PendingReview extends StatelessWidget {
  const PendingReview({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 19.w, top: 85.h),
              child: customBackButton(),
            ),
            Padding(
              padding: EdgeInsets.only(left: 9.w),
              child: customtext(
                  context: context,
                  newYear: "Pending Review",
                  font: 30.sp,
                  weight: FontWeight.w500),
            ),
            Padding(
              padding: EdgeInsets.only(left: 13.w, right: 13.w, top: 0),
              child: ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    return SizedBox();
                    // return checkInCardAll(
                    //   context: context,
                    //   imagePath: ImagePath.school,
                    //   course: "Watercolour (intermediate)",
                    //   date: DateTime.now(),
                    //   time: "14:00-14:45",
                    //   group: 3.toString(),
                    //   age: "3-6",
                    //   name: "Charlie Own",
                    //   onTap: () {
                    //     NavigatorService.pushNamed(AppRoutes.verificationCode);
                    //   },
                    // );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 29.h,
                    );
                  },
                  itemCount: 10),
            ),
            SizedBox(
              height: 2.h,
            )
          ],
        ),
      ),
    );
  }
}
