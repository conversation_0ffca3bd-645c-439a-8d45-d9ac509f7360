import 'package:class_z/core/imports.dart';

class CenterSettings extends StatefulWidget {
  final CenterData center;
  const CenterSettings({required this.center, super.key});

  @override
  State<CenterSettings> createState() => _CenterSettingsState();
}

class _CenterSettingsState extends State<CenterSettings> {
  bool _activities = true;
  bool _promotion = false;
  void toggleActivities(bool value) {
    setState(() {
      _activities = value;
    });
  }

  void togglePromotion(bool value) {
    setState(() {
      _promotion = value;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [_stack(), _settingsMenu()],
      ),
    ));
  }

  Widget _stack() {
    return SizedBox(
      height: 180,
      child: Stack(
        children: [
          Container(
            height: 156.h,
            decoration:
                BoxDecoration(gradient: GradientProvider.getLinearGradient()),
          ),
          Positioned(
              top: 85.h,
              left: 19.w,
              child: customBackButton(color: Colors.white)),
          Positioned(
              top: 57.h,
              left: 0,
              right: 0,
              bottom: 0,
              child: Align(
                alignment: Alignment.center,
                child: CustomImageBuilder(
                  imagePath: "${AppText.device}${widget.center.mainImage?.url}",
                  height: 132.h,
                  width: 132.w,
                  borderRadius: 132.r,
                  boxshadow: [shadow(blurRadius: 15, opacity: 0.1)],
                ),
              )),
        ],
      ),
    );
  }

  Widget _settingsMenu() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 24.w),
          child: customtext(
              context: context,
              newYear: "Profile",
              font: 15.sp,
              color: AppPallete.darkGrey,
              weight: FontWeight.w600),
        ),

        profile(
            context: context,
            name: "Center Profile",
            iconData: Icons.person,
            onTap: () {
              NavigatorService.pushNamed(AppRoutes.centerProfile,
                  arguments: widget.center);
            }),

        const SizedBox(
          height: 6,
        ),
        customDivider(padding: 24, right: 24),

        SizedBox(
          height: 14,
        ),
        Padding(
          padding: EdgeInsets.only(left: 24.w),
          child: customtext(
              context: context,
              newYear: "Preference",
              font: 15.sp,
              color: AppPallete.darkGrey,
              weight: FontWeight.w600),
        ),

        ///Preference

        profile(context: context, name: "Language", iconData: Icons.language),
        const SizedBox(
          height: 6,
        ),
        customDivider(padding: 24, right: 24),
        const SizedBox(
          height: 6,
        ),

        notification(
          context: context,
          name: "Activities Notification",
          iconData: Icons.notifications,
          onChanged: toggleActivities,
          switchValue: _activities,
        ),
        const SizedBox(
          height: 6,
        ),
        customDivider(padding: 24, right: 24),
        const SizedBox(
          height: 6,
        ),

        notification(
          context: context,
          name: "Promotion Notification",
          iconData: Icons.telegram,
          onChanged: togglePromotion,
          switchValue: _promotion,
        ),
        const SizedBox(
          height: 6,
        ),
        customDivider(padding: 24, right: 24),

        ///Privacy

        SizedBox(
          height: 17.h,
        ),
        Padding(
          padding: EdgeInsets.only(left: 24.w),
          child: customtext(
              context: context,
              newYear: "Privacy",
              font: 15.sp,
              color: AppPallete.darkGrey,
              weight: FontWeight.w600),
        ),
        const SizedBox(
          height: 14,
        ),
        profile(
            context: context,
            name: "change password",
            iconData: Icons.fingerprint,
            onTap: () {
              // Get center email from widget
              final centerEmail = widget.center.email;

              if (centerEmail != null && centerEmail.isNotEmpty) {
                print("Navigating to reset password with email: $centerEmail");
                NavigatorService.pushNamed(AppRoutes.resetPassword,
                    arguments: centerEmail);
              } else {
                // Handle missing email
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Email not available. Please log in again.'),
                    duration: Duration(seconds: 2),
                  ),
                );
                print("Center email not available for Reset Password");
              }
            }),
        const SizedBox(
          height: 6,
        ),
        customDivider(padding: 24, right: 24),
        const SizedBox(
          height: 6,
        ),

        profile(
            context: context,
            name: "Terms and Condition",
            iconData: Icons.description,
            onTap: () {
              NavigatorService.pushNamed(AppRoutes.terms);
            }),
        const SizedBox(
          height: 6,
        ),
        customDivider(padding: 24, right: 24),
        const SizedBox(
          height: 14,
        ),

        ///Assistance

        Padding(
          padding: EdgeInsets.only(left: 24.w),
          child: customtext(
              context: context,
              newYear: "Assistance",
              font: 15.sp,
              color: AppPallete.darkGrey,
              weight: FontWeight.w600),
        ),
        const SizedBox(
          height: 14,
        ),

        profile(
            context: context,
            name: "Help Centre",
            iconData: Icons.help_outlined,
            onTap: () {
              NavigatorService.pushNamed(AppRoutes.faq);
            }),
        const SizedBox(
          height: 6,
        ),
        customDivider(padding: 24, right: 24),
        const SizedBox(
          height: 6,
        ),
        profile(
            context: context,
            name: "Contact Us",
            iconData: Icons.chat,
            onTap: () {
              NavigatorService.pushNamed(AppRoutes.contactUs);
            }),
        const SizedBox(
          height: 6,
        ),
        customDivider(padding: 24, right: 24),

        SizedBox(
          height: 60.h,
        ),
        Center(
          child: Button(
              buttonText: "Log Out",
              color: AppPallete.secondaryColor,
              width: 289.w,
              height: 49.h,
              onPressed: () async {
                await locator<SharedRepository>().logout();

                // Navigate to login screen
                NavigatorService.pushNamedAndRemoveUntil(AppRoutes.logIn);
              }),
        ),
      ],
    );
  }

  Widget profile(
      {required BuildContext context,
      required String name,
      required IconData iconData,
      VoidCallback? onTap}) {
    return Padding(
      padding: EdgeInsets.only(left: 24.w, right: 27.w),
      child: InkWell(
        onTap: onTap,
        child: SizedBox(
          height: 33.h,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    iconData,
                    color: AppPallete.darkGrey,
                    size: 30,
                  ),
                  SizedBox(
                    width: 26.w,
                  ),
                  customtext(
                      context: context,
                      newYear: name,
                      font: 15.sp,
                      weight: FontWeight.w500),
                ],
              ),
              const Icon(
                Icons.arrow_forward_ios,
                color: AppPallete.darkGrey,
                size: 17,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget notification(
      {required BuildContext context,
      required String name,
      required IconData iconData,
      required Function(bool) onChanged,
      required bool switchValue}) {
    return Padding(
      padding: EdgeInsets.only(left: 24.w, right: 27.w),
      child: InkWell(
        onTap: () {},
        child: SizedBox(
          height: 33.h,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    iconData,
                    color: AppPallete.darkGrey,
                    size: 30,
                  ),
                  SizedBox(
                    width: 26.w,
                  ),
                  customtext(
                      context: context,
                      newYear: name,
                      font: 15.sp,
                      weight: FontWeight.w500),
                ],
              ),
              Switch(
                  value: switchValue,
                  activeColor: AppPallete.secondaryColor,
                  onChanged: onChanged),
            ],
          ),
        ),
      ),
    );
  }
}
