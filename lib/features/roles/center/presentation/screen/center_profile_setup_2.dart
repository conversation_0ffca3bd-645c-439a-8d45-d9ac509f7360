import 'package:class_z/core/imports.dart';

class CenterProfileSetup2 extends StatefulWidget {
  final Map<String, dynamic> data;
  const CenterProfileSetup2({required this.data, super.key});

  @override
  State<CenterProfileSetup2> createState() => _CenterProfileSetup2State();
}

class _CenterProfileSetup2State extends State<CenterProfileSetup2> {
  int? _selectedOptionGroup1;
  int? _selectedOptionGroup2;
  final businessNumberController = TextEditingController();
  final yourNameController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  List<File> businessFile = [];
  List<File> sexualConvictionFile = [];
  List<File> hkidCard = [];
  bool isFreelanceEducator = false;
  @override
  void initState() {
    super.initState();
    // Check if the owner is an individual creator from Hive data
    final ownerData = locator<SharedRepository>().getOwnerData();
    isFreelanceEducator = ownerData?.isIndividualCreator ?? false;
  }

  @override
  void dispose() {
    businessNumberController.dispose();
    yourNameController.dispose();
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        title: "Center Profile",
        leading: customBackButton(),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 21, vertical: 43),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: CustomImageBuilder(
                    imagePath: widget.data['mainImage']?.path,
                    height: 125.h,
                    width: 125.w,
                    borderRadius: 99.r),
              ),
              const SizedBox(
                height: 17,
              ),
              _customForm(),
              SizedBox(
                height: 20.h,
              ),
              if (!isFreelanceEducator) ...[
                _radioWithText(
                  context: context,
                  titleWidget: customtext(
                      context: context,
                      newYear:
                          "I confirm that I own at least 25% of the company",
                      font: 15.sp,
                      weight: FontWeight.w400),
                  index: 0,
                  selectedOption: _selectedOptionGroup1,
                  onChanged: (value) {
                    setState(() {
                      _selectedOptionGroup1 = value;
                    });
                  },
                ),
              ],
              _radioWithText(
                context: context,
                titleWidget: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text:
                            '"I confirm that all of the company’s owners agree to Promato’s ',
                        style: TextStyle(
                            color: Colors.black,
                            fontSize: 15.sp,
                            fontWeight: FontWeight.w400),
                      ),
                      TextSpan(
                        text: 'terms and conditions',
                        style: TextStyle(
                          color: AppPallete.secondaryColor,
                          fontWeight: FontWeight.w400,
                          fontSize: 15.sp,
                          decoration: TextDecoration.underline,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            NavigatorService.pushNamed(AppRoutes.terms);
                          },
                      ),
                      TextSpan(
                        text: '*',
                        style: TextStyle(
                          color: AppPallete.red,
                          fontWeight: FontWeight.w400,
                          fontSize: 15.sp,
                        ),
                      ),
                    ],
                  ),
                ),
                index: 1,
                selectedOption: _selectedOptionGroup2,
                onChanged: (value) {
                  setState(() {
                    _selectedOptionGroup2 = value;
                  });
                },
              ),
              const SizedBox(
                height: 60,
              ),
              Center(
                child: Button(
                  buttonText: "next",
                  color: AppPallete.secondaryColor,
                  height: 49.h,
                  width: 289.w,
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      // Check documents based on freelance educator status
                      bool documentsValid = false;
                      if (isFreelanceEducator) {
                        documentsValid = hkidCard.isNotEmpty &&
                            sexualConvictionFile.isNotEmpty;
                        if (!documentsValid) {
                          errorState(
                              context: context,
                              error:
                                  'Please upload HKID card and Sexual Conviction Record Check');
                        }
                      } else {
                        documentsValid =
                            hkidCard.isNotEmpty && businessFile.isNotEmpty;
                        if (!documentsValid) {
                          errorState(
                              context: context,
                              error:
                                  'Please upload HKID card and Business Registration Certificate');
                        }
                      }

                      if (documentsValid &&
                          _selectedOptionGroup2 != null &&
                          (isFreelanceEducator ||
                              _selectedOptionGroup1 != null)) {
                        Map<String, dynamic> additionalData = {
                          "businessNumber": businessNumberController.text,
                          "hkidCard": hkidCard,
                          "isFreelanceEducator": isFreelanceEducator,
                        };

                        if (isFreelanceEducator) {
                          additionalData["sexualConvictionRecord"] =
                              sexualConvictionFile;
                        } else {
                          additionalData["businessCertificate"] = businessFile;
                        }

                        widget.data.addAll(additionalData);
                        print(widget.data);
                        NavigatorService.pushNamed(
                            AppRoutes.centerProfilesetup3,
                            arguments: widget.data);
                      } else if (_selectedOptionGroup2 == null ||
                          (!isFreelanceEducator &&
                              _selectedOptionGroup1 == null)) {
                        errorState(
                            context: context,
                            error: 'Please complete all confirmations');
                      }
                    }
                  },
                ),
              ),
              const SizedBox(
                height: 5,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _customForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          customtext(
              context: context,
              newYear: "Verification",
              font: 17.sp,
              weight: FontWeight.w500),
          const SizedBox(height: 20),
          if (!isFreelanceEducator) ...[
            customRequiredText(
                context: context,
                title: "Business registration number",
                font: 15.sp,
                weight: FontWeight.w400),
            const SizedBox(height: 20),
            AuthField(
              controller: businessNumberController,
              height: 30.h,
              width: 387.w,
              color: AppPallete.paleGrey,
              validator: (value) =>
                  value == null || value.isEmpty ? 'Value cant be empty' : null,
            ),
          ],
          SizedBox(
            height: 20.h,
          ),
          customtext(
              context: context,
              newYear: "Upload supporting document",
              font: 17.sp,
              weight: FontWeight.w500),
          SizedBox(
            height: 20.h,
          ),
          if (isFreelanceEducator) ...[
            customRequiredText(
                context: context,
                title: "Sexual Conviction Record Check",
                font: 15.sp,
                weight: FontWeight.w400),
            SizedBox(
              height: 20.h,
            ),
            AlbumCard(
              onImagesSelected: (images) {
                setState(() {
                  sexualConvictionFile = images;
                });
              },
            ),
          ] else ...[
            customRequiredText(
                context: context,
                title: "Business registration certificate",
                font: 15.sp,
                weight: FontWeight.w400),
            SizedBox(
              height: 20.h,
            ),
            AlbumCard(
              onImagesSelected: (images) {
                setState(() {
                  businessFile = images;
                });
              },
            ),
          ],
          SizedBox(
            height: 20.h,
          ),
          customRequiredText(
              context: context,
              title: "Your HKID card",
              font: 15.sp,
              weight: FontWeight.w400),
          SizedBox(
            height: 20.h,
          ),
          AlbumCard(
            onImagesSelected: (images) {
              setState(() {
                hkidCard = images;
              });
            },
          ),
          SizedBox(
            height: 20.h,
          ),
          customRequiredText(
              context: context,
              title: "File format: jpg, jpeg, png",
              font: 15.sp,
              weight: FontWeight.w400),
          SizedBox(
            height: 20.h,
          ),
          customRequiredText(
              context: context,
              title: "Not exceeding size: 5MB",
              font: 15.sp,
              weight: FontWeight.w400),
          SizedBox(
            height: 20.h,
          ),
          customtext(
              context: context,
              newYear: "Ownership",
              font: 17.sp,
              weight: FontWeight.w500),
          SizedBox(
            height: 20.h,
          ),
          customRequiredText(
              context: context,
              title: "Your Name",
              font: 15.sp,
              weight: FontWeight.w400),
          SizedBox(
            height: 20.h,
          ),
          AuthField(
            controller: yourNameController,
            height: 30.h,
            width: 387.w,
            color: AppPallete.paleGrey,
            validator: (value) =>
                value == null || value.isEmpty ? 'Value cant be empty' : null,
          ),
        ],
      ),
    );
  }

  Widget _radioWithText({
    required BuildContext context,
    required int index,
    required int? selectedOption,
    required ValueChanged<int?> onChanged,
    required Widget titleWidget, // 👈 change from String to Widget
  }) {
    return Row(
      children: [
        Radio<int>(
          value: index,
          groupValue: selectedOption,
          onChanged: (value) {
            if (selectedOption == value) {
              onChanged(null); // Deselect
            } else {
              onChanged(value);
            }
          },
        ),
        Expanded(child: titleWidget),
      ],
    );
  }

  // Widget _plus({
  //   required BuildContext context,
  //   required bool hkidCard,
  // }) {
  //   return InkWell(
  //     onTap: () =>
  //         _pickImage(hkidCard: hkidCard), // Fix: Use anonymous function
  //     child: Container(
  //       height: 103.h,
  //       width: 107.w,
  //       color: AppPallete.paleGrey,
  //       child: customSvgPicture(
  //         imagePath: ImagePath.plusSvg,
  //         height: 103.h,
  //         width: 107.w,
  //         color: Colors.white,
  //       ),
  //     ),
  //   );
  // }
}
