import 'package:class_z/core/imports.dart';

class CenterProfileSetup3 extends StatefulWidget {
  final Map<String, dynamic> data;
  const CenterProfileSetup3({required this.data, Key? key}) : super(key: key);

  @override
  _CenterProfileSetup3State createState() => _CenterProfileSetup3State();
}

class _CenterProfileSetup3State extends State<CenterProfileSetup3> {
  final List<bool> _selectedOptions =
      List.filled(6, false); // Initialize with 6 options
  List<String> services = [];
  List<String> selectedLanguages = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        title: "Center Profile",
        leading: customBackButton(),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 21, vertical: 43),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: CustomImageBuilder(
                  imagePath: widget.data['mainImage']?.path,
                  height: 125.h,
                  width: 125.w,
                  borderRadius: 99.r,
                ),
              ),
              const SizedBox(
                height: 22,
              ),
              customRequiredText(
                  context: context,
                  title: "Teaching language",
                  font: 17.sp,
                  weight: FontWeight.w500),
              SizedBox(
                height: 20.h,
              ),
              LanguageSelector(
                selectedLanguages: selectedLanguages,
                onChanged: (updatedLanguages) {
                  setState(() {
                    selectedLanguages =
                        updatedLanguages; // Update the parent's state
                  });
                  // Access the updated selectedLanguages here
                  print('Selected languages: $selectedLanguages');
                },
              ),
              SizedBox(
                height: 20.h,
              ),
              customtext(
                  context: context,
                  newYear: "Additional information",
                  font: 17.sp,
                  weight: FontWeight.w500),
              SizedBox(
                height: 20.h,
              ),
              customtext(
                  context: context,
                  newYear: "Check the provided service or amendment",
                  font: 15.sp,
                  weight: FontWeight.w500),
              SizedBox(
                height: 23.h,
              ),
              _radioWithText(
                context: context,
                title:
                    "Centre assists students to participate in external competitions",
                height: 35.h,
                index: 0,
              ),
              SizedBox(height: 20.h),
              _radioWithText(
                context: context,
                title: "Centre provides internal competitions/events regularly",
                height: 17.h,
                index: 1,
              ),
              SizedBox(height: 20.h),
              _radioWithText(
                context: context,
                title: "Equipment is provided for students within classes",
                height: 35.h,
                index: 2,
              ),
              SizedBox(height: 20.h),
              _radioWithText(
                context: context,
                title: "Day camp training is held regularly for students",
                height: 17.h,
                index: 3,
              ),
              SizedBox(height: 20.h),
              _radioWithText(
                context: context,
                title:
                    "Centre provides use of child-friendly supplies (e.g. desk, chair)",
                height: 35.h,
                index: 4,
              ),
              SizedBox(height: 20.h),
              _radioWithText(
                context: context,
                title:
                    "Centre provides babysitting service (allows students to stay behind after class)",
                height: 35.h,
                index: 5,
              ),
              const SizedBox(height: 60),
              Center(
                child: Button(
                  buttonText: "next",
                  color: AppPallete.secondaryColor,
                  height: 49.h,
                  width: 289.w,
                  onPressed: () {
                    if (selectedLanguages.isEmpty) {
                      errorState(
                          context: context, error: 'Please fill up languages');
                    } else {
                      widget.data.addAll({
                        "languages": selectedLanguages,
                        if (services.length > 0) "services": services,
                      });
                      print(widget.data);
                      NavigatorService.pushNamed(AppRoutes.centerProfilesetup4,
                          arguments: widget.data);
                    }
                  },
                ),
              ),
              const SizedBox(height: 5),
            ],
          ),
        ),
      ),
    );
  }

  Widget _radioWithText(
      {required BuildContext context,
      required String title,
      required int index,
      required double height}) {
    return Row(
      children: [
        Radio(
          value: index,
          groupValue: _selectedOptions[index] ? index : null,
          onChanged: (value) {
            setState(() {
              if (_selectedOptions[index]) {
                _selectedOptions[index] = false; // Deselect if already selected
              } else {
                _selectedOptions[index] = true; // Select if not selected
              }
              if (_selectedOptions[index]) {
                if (!services.contains(title)) {
                  services.add(title); // Add only if not already in the list
                }
              }
            });
          },
        ),
        Expanded(
          child: customtext(
            context: context,
            newYear: title,
            font: 15.sp,
            weight: FontWeight.w400,
          ),
        ),
      ],
    );
  }
}
