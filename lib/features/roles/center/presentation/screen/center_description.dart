import 'package:class_z/core/imports.dart';

class CenterDescription extends StatefulWidget {
  final String centerId;
  const CenterDescription({required this.centerId, super.key});

  @override
  State<CenterDescription> createState() => _CenterDescriptionState();
}

class _CenterDescriptionState extends State<CenterDescription> {
  final List<bool> _selectedOptions = List.filled(6, false);
  final businessNameController = TextEditingController();
  final yourNameController = TextEditingController();
  final descriptionController = TextEditingController();
  @override
  void initState() {
    super.initState();
    // Fetch existing center data to pre-fill the form
    _loadCenterData();
  }

  @override
  void dispose() {
    businessNameController.dispose();
    yourNameController.dispose();
    descriptionController.dispose();
    super.dispose();
  }

  void _loadCenterData() {
    print(
        "🔄 [CENTER_DESCRIPTION] _loadCenterData called for centerId: ${widget.centerId}");
    // First try to get data from local storage
    final localCenterData = locator<SharedRepository>().getCenterData();
    print(
        "🗄️ [CENTER_DESCRIPTION] Local center data: ${localCenterData?.toString()}");

    if (localCenterData != null && localCenterData.id == widget.centerId) {
      print("✅ [CENTER_DESCRIPTION] Using local center data for pre-filling");
      _populateFields(localCenterData);
    } else {
      print(
          "🌐 [CENTER_DESCRIPTION] Local data not available or doesn't match, fetching from API");
      print(
          "🚀 [CENTER_DESCRIPTION] Dispatching GetCenterDataEvent for ID: ${widget.centerId}");
      context.read<CenterBloc>().add(GetCenterDataEvent(id: widget.centerId));
    }
  }

  void _populateFields(CenterData center) {
    print("📝 [CENTER_DESCRIPTION] _populateFields called");
    print(
        "📊 [CENTER_DESCRIPTION] Center data: description='${center.description}', languages=${center.languages}, services=${center.services}");
    print(
        "🕒 [CENTER_DESCRIPTION] Opening hours from API: ${center.openingHours}");

    setState(() {
      descriptionController.text = center.description ?? '';

      // Populate selected languages
      if (center.languages != null) {
        selectedLanguages = List<String>.from(center.languages!);
        print(
            "🌐 [CENTER_DESCRIPTION] Languages populated: $selectedLanguages");
      }

      // Populate services
      if (center.services != null) {
        services = List<String>.from(center.services!);
        print("🔧 [CENTER_DESCRIPTION] Services populated: $services");
        // Update the selected options based on services
        for (int i = 0; i < _selectedOptions.length; i++) {
          String serviceText = _getServiceTextByIndex(i);
          _selectedOptions[i] = services.contains(serviceText);
        }
      }

      // Populate schedule from opening hours
      if (center.openingHours != null) {
        print(
            "🕒 [CENTER_DESCRIPTION] Processing ${center.openingHours!.length} opening hours");
        for (var openingHour in center.openingHours!) {
          print(
              "🕒 [CENTER_DESCRIPTION] Processing: day=${openingHour.day}, opening=${openingHour.openingTime}, closing=${openingHour.closingTime}");
          if (schedule.containsKey(openingHour.day)) {
            schedule[openingHour.day!] = {
              'from': openingHour.openingTime ?? '',
              'to': openingHour.closingTime ?? '',
            };
            print(
                "🕒 [CENTER_DESCRIPTION] Updated schedule for ${openingHour.day}: ${schedule[openingHour.day!]}");
          }
        }
        print("🕒 [CENTER_DESCRIPTION] Final schedule: $schedule");
      } else {
        print("⚠️ [CENTER_DESCRIPTION] No opening hours found in center data");
      }
    });

    print("✅ [CENTER_DESCRIPTION] Fields populated successfully");
    print(
        "🔄 [CENTER_DESCRIPTION] Triggering UI rebuild with updated schedule");
  }

  String _getServiceTextByIndex(int index) {
    const serviceTexts = [
      "Centre assists students to participate in external competitions",
      "Centre provides internal competitions/events regularly",
      "Equipment is provided for students within classes",
      "Day camp training is held regularly for students",
      "Centre provides use of child-friendly supplies (e.g. desk, chair)",
      "Centre provides babysitting service (allows students to stay behind after class)",
    ];
    return index < serviceTexts.length ? serviceTexts[index] : '';
  }

  Map<String, Map<String, String>> schedule = {
    'Monday': {'from': '', 'to': ''},
    'Tuesday': {'from': '', 'to': ''},
    'Wednesday': {'from': '', 'to': ''},
    'Thursday': {'from': '', 'to': ''},
    'Friday': {'from': '', 'to': ''},
    'Saturday': {'from': '', 'to': ''},
    'Sunday': {'from': '', 'to': ''},
  };
  List<String> selectedLanguages = [];
  List<String> services = [];
  List<File> photos = [];
  @override
  Widget build(BuildContext context) {
    return BlocListener<CenterBloc, CenterState>(
      listener: (context, state) {
        if (state is CenterLoadingState) {
          loadingState(context: context);
        } else
          hideLoadingDialog(context);
        if (state is CenterUpdateSuccess) {
          errorState(context: context, error: 'Update done');
          NavigatorService.goBack();
        }
        if (state is CenterErrorState) {
          // Only show error for update operations, not for data fetching
          if (state.message.contains('Update') ||
              state.message.contains('save')) {
            errorState(context: context, error: state.message);
          } else {
            // For data fetching errors, just log and continue
            print("Failed to load center data: ${state.message}");
            print("User can still edit the form manually");
          }
        }
        if (state is CenterInfoSuccess) {
          print("🎉 [CENTER_DESCRIPTION] CenterInfoSuccess received!");
          print(
              "📦 [CENTER_DESCRIPTION] Center data from API: ${state.center.toString()}");
          _populateFields(state.center);
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: CustomAppBar(
          title: "Center Profile",
          leading: customBackButton(),
          actions: [
            Padding(
              padding:
                  EdgeInsets.only(right: 20.w), // Add some padding to the right
              child: InkWell(
                onTap: () {
                  // Check if at least one day has opening hours set
                  bool hasAtLeastOneDaySchedule = schedule.values.any(
                    (day) =>
                        day['from'] != null &&
                        day['from']!.isNotEmpty &&
                        day['to'] != null &&
                        day['to']!.isNotEmpty,
                  );

                  print("Services: ${services}");
                  print("Schedule: ${schedule}");

                  if (services.isEmpty &&
                      selectedLanguages.isEmpty &&
                      descriptionController.text.isEmpty &&
                      !hasAtLeastOneDaySchedule) {
                    errorState(context: context, error: 'Nothing to Update');
                  } else {
                    final OpeningHourList = convertScheduleToJson(schedule);
                    Map<String, dynamic> data = {
                      if (services.isNotEmpty) "services": services,
                      "description": descriptionController.text,
                      if (selectedLanguages.isNotEmpty)
                        "languages": selectedLanguages,
                      if (hasAtLeastOneDaySchedule)
                        "openingHour": OpeningHourList,
                      if (photos.isNotEmpty) "images": photos
                    };
                    print("Data to submit: $data");
                    context.read<CenterBloc>().add(UpdateCenterEvent(
                        centerId: widget.centerId, data: data));
                  }
                },
                child: Center(
                  child: Text(
                    "save",
                    style: TextStyle(
                        color: AppPallete.change,
                        fontSize: 17.sp,
                        fontWeight: FontWeight.w400),
                  ),
                ),
              ), // Text on the right
            ),
          ],
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.only(
                left: 21.w, right: 21.w, top: 44.h, bottom: 20.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                customRequiredText(
                    context: context,
                    title: "Description",
                    font: 17.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 20.h,
                ),
                AuthField(
                  controller: descriptionController,
                  height: 132.h,
                  hintText: "No more than 250 words",
                ),
                SizedBox(
                  height: 20.h,
                ),
                customtext(
                    context: context,
                    newYear: "Photo",
                    font: 17.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 20.h,
                ),
                customRequiredText(
                    context: context,
                    title: "Album (up to 10 photos)",
                    font: 15.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 20.h,
                ),
                AlbumCard(
                  onImagesSelected: (images) {
                    photos = images;
                  },
                ),
                SizedBox(
                  height: 20.h,
                ),
                customRequiredText(
                    context: context,
                    title: "Opening Hours",
                    font: 15.sp,
                    weight: FontWeight.w500),
                const SizedBox(
                  height: 20,
                ),
                Container(
                  width: double.infinity,
                  constraints: BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width - 42.w),
                  child: ScheduleSelector(
                    key: ValueKey(schedule
                        .toString()), // Force rebuild when schedule changes
                    schedule: schedule,
                    onChanged: (updatedSchedule) {
                      setState(() {
                        schedule = updatedSchedule; // Update the parent's state
                      });
                      // Access the updated schedule here
                      print('Updated schedule: $schedule');
                    },
                  ),
                ),
                SizedBox(
                  height: 20.h,
                ),
                customRequiredText(
                    context: context,
                    title: "Teaching language",
                    font: 17.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 20.h,
                ),
                LanguageSelector(
                  selectedLanguages: selectedLanguages,
                  onChanged: (updatedLanguages) {
                    setState(() {
                      selectedLanguages =
                          updatedLanguages; // Update the parent's state
                    });
                    // Access the updated selectedLanguages here
                    print('Selected languages: $selectedLanguages');
                  },
                ),
                SizedBox(
                  height: 20.h,
                ),
                SizedBox(
                  height: 20.h,
                ),
                customtext(
                    context: context,
                    newYear: "Check the provided service or amendment",
                    font: 15.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 23.h,
                ),
                _radioWithText(
                  context: context,
                  title:
                      "Centre assists students to participate in external competitions",
                  index: 0,
                ),
                SizedBox(height: 20.h),
                _radioWithText(
                  context: context,
                  title:
                      "Centre provides internal competitions/events regularly",
                  index: 1,
                ),
                SizedBox(height: 20.h),
                _radioWithText(
                  context: context,
                  title: "Equipment is provided for students within classes",
                  index: 2,
                ),
                SizedBox(height: 20.h),
                _radioWithText(
                  context: context,
                  title: "Day camp training is held regularly for students",
                  index: 3,
                ),
                SizedBox(height: 20.h),
                _radioWithText(
                  context: context,
                  title:
                      "Centre provides use of child-friendly supplies (e.g. desk, chair)",
                  index: 4,
                ),
                SizedBox(height: 20.h),
                _radioWithText(
                  context: context,
                  title:
                      "Centre provides babysitting service (allows students to stay behind after class)",
                  index: 5,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _radioWithText({
    required BuildContext context,
    required String title,
    required int index,
  }) {
    return Row(
      children: [
        Radio(
          value: index,
          groupValue: _selectedOptions[index] ? index : null,
          onChanged: (value) {
            setState(() {
              if (_selectedOptions[index]) {
                _selectedOptions[index] = false; // Deselect if already selected
              } else {
                _selectedOptions[index] = true; // Select if not selected
              }
              if (_selectedOptions[index]) {
                if (!services.contains(title)) {
                  services.add(title); // Add only if not already in the list
                }
              }
            });
          },
        ),
        Flexible(
          child: customtext(
            context: context,
            newYear: title,
            font: 15.sp,
            weight: FontWeight.w400,
          ),
        ),
      ],
    );
  }
}
