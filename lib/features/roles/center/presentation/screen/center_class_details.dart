import 'package:class_z/core/imports.dart';
import 'package:class_z/core/common/data/models/class_model.dart';
import 'package:class_z/core/common/data/models/classDate_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../centerbloc/center_bloc.dart';

class CenterClassDetailsPage extends StatefulWidget {
  final ClassModel classModel;
  const CenterClassDetailsPage({Key? key, required this.classModel})
      : super(key: key);

  @override
  State<CenterClassDetailsPage> createState() => _CenterClassDetailsPageState();
}

class _CenterClassDetailsPageState extends State<CenterClassDetailsPage> {
  List<ClassDate> _availableSlots = [];

  @override
  void initState() {
    super.initState();
    if (widget.classModel.id != null && widget.classModel.id!.isNotEmpty) {
      context
          .read<CenterBloc>()
          .add(GetAllSlotsByClassIdEvent(classId: widget.classModel.id!));
    }
  }

  void _refreshSlots() {
    if (widget.classModel.id != null && widget.classModel.id!.isNotEmpty) {
      context
          .read<CenterBloc>()
          .add(GetAllSlotsByClassIdEvent(classId: widget.classModel.id!));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.classModel.classProviding ?? 'Class Details'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Class Details
              if (widget.classModel.mainImage?.url != null &&
                  widget.classModel.mainImage!.url!.isNotEmpty)
                CustomImageBuilder(
                  imagePath: widget.classModel.mainImage!.url!,
                  height: 180,
                  borderRadius: 12,
                ),

              SizedBox(height: 16),
              Text(
                widget.classModel.classProviding ?? 'Untitled',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              SizedBox(height: 8),

              if (widget.classModel.description != null &&
                  widget.classModel.description!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(widget.classModel.description!),
                ),
              SizedBox(height: 8),
              if (widget.classModel.level != null &&
                  widget.classModel.level!.isNotEmpty)
                Text('Level: ${widget.classModel.level!}'),
              SizedBox(height: 8),
              if (widget.classModel.initialCharge != null &&
                  widget.classModel.initialCharge!.isNotEmpty)
                Text('Initial Charge: ${widget.classModel.initialCharge!}'),
              SizedBox(height: 8),
              if (widget.classModel.charge != null)
                Text('Charge: ${widget.classModel.charge}'),
              if (widget.classModel.ageFrom != null &&
                  widget.classModel.ageTo != null)
                Text(
                    'Age Group: ${widget.classModel.ageFrom}-${widget.classModel.ageTo}'),
              if (widget.classModel.language != null &&
                  widget.classModel.language!.isNotEmpty)
                Text('Languages: ${widget.classModel.language!.join(", ")}'),
              if (widget.classModel.address != null &&
                  widget.classModel.address!.isNotEmpty)
                Text('Location: ${widget.classModel.address!}'),
              SizedBox(height: 24),
              Text('Available Slots',
                  style: Theme.of(context).textTheme.titleMedium),
              SizedBox(height: 8),
              BlocListener<CenterBloc, CenterState>(
                listener: (context, state) {
                  if (state is AllSlotsByClassIdFetchSuccess) {
                    setState(() {
                      _availableSlots = List<ClassDate>.from(state.slots);
                    });
                  } else if (state is DeleteSlotByIdSuccess && state.success) {
                    _refreshSlots();
                  }
                },
                child: _availableSlots.isEmpty
                    ? Text('No slots available.')
                    : ListView.separated(
                        shrinkWrap: true,
                        physics: NeverScrollableScrollPhysics(),
                        itemCount: _availableSlots.length,
                        separatorBuilder: (context, index) => Divider(),
                        itemBuilder: (context, index) {
                          final slot = _availableSlots[index];
                          return _SlotCard(
                            slot: slot,
                            onDelete: (slotId) async {
                              final confirm = await showDialog<bool>(
                                context: context,
                                builder: (context) => AlertDialog(
                                  title: Text('Delete Slot'),
                                  content: Text(
                                      'Are you sure you want to delete this slot?'),
                                  actions: [
                                    TextButton(
                                      onPressed: () =>
                                          Navigator.of(context).pop(false),
                                      child: Text('Cancel'),
                                    ),
                                    TextButton(
                                      onPressed: () =>
                                          Navigator.of(context).pop(true),
                                      child: Text('Delete'),
                                    ),
                                  ],
                                ),
                              );
                              if (confirm == true) {
                                context
                                    .read<CenterBloc>()
                                    .add(DeleteSlotByIdEvent(slotId: slotId));
                              }
                            },
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _slotTimeString(ClassDate slot) {
    if (slot.startTime != null && slot.endTime != null) {
      return '${slot.startTime} - ${slot.endTime}';
    } else if (slot.startTime != null) {
      return slot.startTime!;
    }
    return '';
  }
}

class _SlotCard extends StatelessWidget {
  final ClassDate slot;
  final Future<void> Function(String slotId)? onDelete;
  const _SlotCard({Key? key, required this.slot, this.onDelete})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Date: ${slot.date ?? '-'}',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                if ((slot.students?.isEmpty ?? true) &&
                    slot.id != null &&
                    onDelete != null)
                  IconButton(
                    icon: Icon(Icons.delete, color: Colors.red),
                    tooltip: 'Delete Slot',
                    onPressed: () => onDelete!(slot.id!),
                  ),
              ],
            ),
            SizedBox(height: 4),
            Text('Start Time: ${slot.startTime ?? '-'}'),
            Text('End Time: ${slot.endTime ?? '-'}'),
            Text('Duration: ${slot.durationMinutes ?? '-'}'),
            Text('Week Day: ${slot.weekDay ?? '-'}'),
            Text('Repeat: ${slot.repeat ?? '-'}'),
            Text('Total Number of Class: ${slot.numberOfClass ?? '0'}'),
            Text('Status: ${slot.status ?? '-'}'),
            Text('Charge: ${slot.charge?.toString() ?? '-'}'),
            Text('Students: ${slot.students?.length ?? 0}'),
          ],
        ),
      ),
    );
  }
}
