import 'package:class_z/core/imports.dart';
import 'package:class_z/core/widgets/compressed_image_picker.dart';
import 'package:flutter/services.dart';

class CenterDetails extends StatefulWidget {
  final String imagePath;
  final String centerId;
  final bool verified;
  const CenterDetails(
      {required this.verified,
      required this.centerId,
      required this.imagePath,
      super.key});

  @override
  State<CenterDetails> createState() => _CenterDetailsState();
}

class _CenterDetailsState extends State<CenterDetails> {
  final displaynameController = TextEditingController();
  final emailaddressController = TextEditingController();
  final companyphonenumberController = TextEditingController();
  final centerphonenumberController = TextEditingController();
  final citynameController = TextEditingController();
  final regionnameController = TextEditingController();
  final address1Controller = TextEditingController();
  final address2Controller = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  List<DayTime> weekSchedule = [
    DayTime(day: 'Monday'),
    DayTime(day: 'Tuesday'),
    DayTime(day: 'Wednesday'),
    DayTime(day: 'Thursday'),
    DayTime(day: 'Friday'),
    DayTime(day: 'Saturday'),
    DayTime(day: 'Sunday'),
  ];
  File? _selectedImage; // Variable to hold the selected image
  // Flag to ensure we only populate the form once per load
  bool _fieldsPopulated = false;
  bool _isCompressing = false; // Track compression state
  CenterData? _currentCenterData; // Store current center data for image display

  Future<void> _pickImage() async {
    try {
      setState(() {
        _isCompressing = true;
      });

      print("📸 Center image processing: Selecting image...");

      // Pick and compress image using the compressed image picker
      final result = await CompressedImagePicker.pickAndCompressImage(
        source: ImageSource.gallery,
        type: ImagePickerType.mainImage,
        onProgress: (message) {
          print('📸 Center image processing: $message');
        },
      );

      setState(() {
        _isCompressing = false;
      });

      if (result.isSuccess && result.file != null) {
        setState(() {
          _selectedImage = result.file;
          print("✅ Compressed image set to state: ${_selectedImage!.path}");
        });

        // Show compression feedback if significant compression occurred
        if (result.compressionRatio > 10) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Image optimized: ${result.compressionInfo}'),
              duration: Duration(seconds: 2),
            ),
          );
        }

        print("📊 Compression stats: ${result.compressionInfo}");
      } else if (result.isCancelled) {
        print("📸 Image selection cancelled by user");
      } else {
        print("❌ Image compression failed: ${result.error}");
        errorState(
            context: context, error: result.error ?? 'Failed to process image');
      }
    } catch (e) {
      setState(() {
        _isCompressing = false;
      });
      print("❌ Error in image selection: $e");
      errorState(context: context, error: 'Failed to select image: $e');
    }
  }

  bool _promotion = false;
  bool _sen = false; // Add SEN toggle state

  void _save() {
    // Validate the form first
    if (_formKey.currentState?.validate() ?? false) {
      try {
        // Check if image exists if selected
        if (_selectedImage != null && !_selectedImage!.existsSync()) {
          errorState(context: context, error: 'Selected image file not found');
          return;
        }

        // Create data map for the update
        Map<String, dynamic> data = {
          "displayName": displaynameController.text,
          "address": {
            "address1": address1Controller.text,
            "address2": address2Controller.text,
            "city": citynameController.text,
            "region": regionnameController.text,
          },
          "companyNumber": companyphonenumberController.text,
          "centerNumber": centerphonenumberController.text,
          "email": emailaddressController.text,
          "promotion": _promotion,
          "sen": _sen, // Add SEN field to data
        };

        // Add image only if it exists and is valid
        if (_selectedImage != null) {
          data["mainImage"] = _selectedImage;
        }

        // Dispatch the update event
        context
            .read<CenterBloc>()
            .add(UpdateCenterEvent(centerId: widget.centerId, data: data));
      } catch (e) {
        errorState(context: context, error: 'Error preparing data: $e');
      }
    } else {
      errorState(context: context, error: 'Please fill all required fields');
    }
  }

  @override
  void initState() {
    super.initState();
    // Fetch existing center data to pre-fill the form
    _loadCenterData();
  }

  void _loadCenterData() {
    // Only fetch data if the fields have not been populated yet.
    // This prevents overwriting user input when returning to the screen.
    if (!_fieldsPopulated) {
      print(
          "Fields not populated. Fetching fresh center data from API to ensure current values");
      context.read<CenterBloc>().add(GetCenterDataEvent(id: widget.centerId));
    }
  }

  void _populateFields(CenterData center) {
    // Add a print statement to log the data being used to populate fields
    print("Populating fields with data: ${center.toJson()}");

    setState(() {
      // Store the center data for image display
      _currentCenterData = center;

      displaynameController.text = center.displayName ?? '';
      emailaddressController.text = center.email ?? '';
      companyphonenumberController.text = center.companyNumber ?? '';
      centerphonenumberController.text = center.centerNumber ?? '';
      citynameController.text = center.address?.city ?? '';
      regionnameController.text = center.address?.region ?? '';
      address1Controller.text = center.address?.address1 ?? '';
      address2Controller.text = center.address?.address2 ?? '';
      _sen = center.sen ?? false; // Load SEN value from center data
      _promotion =
          center.promotion ?? false; // Load promotion value from center data
      _fieldsPopulated = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final centerBloc = BlocProvider.of<CenterBloc>(context);
    return BlocConsumer<CenterBloc, CenterState>(
      bloc: centerBloc,
      listener: (context, state) {
        // Add a print statement to log the state received by the listener
        print("CenterDetails listener received state: $state");

        if (state is CenterLoadingState) {
          loadingState(context: context);
        } else {
          hideLoadingDialog(context);
        }
        if (state is CenterErrorState) {
          // Only show error for update operations, not for data fetching
          if (state.message.contains('Update') ||
              state.message.contains('save')) {
            errorState(context: context, error: state.message);
          } else {
            // For data fetching errors, just log and continue
            print("Failed to load center data: ${state.message}");
            print("User can still edit the form manually");
          }
        }
        if (state is CenterUpdateSuccess) {
          errorState(context: context, error: 'Update Done');
          // Return true to indicate successful update
          NavigatorService.goBack(true);
        }
        if (state is CenterInfoSuccess) {
          _populateFields(state.center);
        }
      },
      builder: (context, state) {
        return Scaffold(
          resizeToAvoidBottomInset: true,
          appBar: AppBar(
            backgroundColor: AppPallete.whiteColor,
            elevation: 0,
            leading: customBackButton(),
            title: customtext(
                context: context,
                newYear: "Centre Details",
                font: 17.sp,
                weight: FontWeight.w500),
            actions: [
              Padding(
                padding: EdgeInsets.only(right: 20.w),
                child: InkWell(
                  onTap: _save,
                  child: customtext(
                      context: context,
                      newYear: "Save",
                      font: 17.sp,
                      weight: FontWeight.w500,
                      color: AppPallete.change),
                ),
              ),
            ],
          ),
          body: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: SingleChildScrollView(
              keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
              physics: const AlwaysScrollableScrollPhysics(),
              child: Form(
                key: _formKey,
                child: Padding(
                  padding: EdgeInsets.only(
                    top: 43.h,
                    left: 21.w,
                    right: 21.w,
                    bottom: MediaQuery.of(context).viewInsets.bottom + 20.h,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(child: _centerImage()),
                      SizedBox(
                        height: 23.h,
                      ),
                      customtext(
                          context: context,
                          newYear: 'Centre status',
                          font: 17.sp,
                          weight: FontWeight.w500),
                      SizedBox(
                        height: 20.h,
                      ),
                      Row(
                        children: [
                          customtext(
                              context: context,
                              newYear: 'Display on ClassZ',
                              font: 15.sp,
                              weight: FontWeight.w400),
                          SizedBox(width: 10),
                          SizedBox(
                            width: 30,
                            height: 30,
                            child: FittedBox(
                              fit: BoxFit.fill,
                              child: Switch(
                                value: _promotion,
                                activeColor: AppPallete.secondaryColor,
                                onChanged: (value) {
                                  setState(() {
                                    _promotion = value;
                                  });
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      // Add SEN services toggle
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                customtext(
                                    context: context,
                                    newYear:
                                        'Special Educational Needs (SEN) Services',
                                    font: 15.sp,
                                    weight: FontWeight.w400),
                                SizedBox(height: 4.h),
                                customtext(
                                    context: context,
                                    newYear:
                                        'Offer specialized support for students with special needs',
                                    font: 12.sp,
                                    weight: FontWeight.w400,
                                    color: AppPallete.darkGrey),
                              ],
                            ),
                          ),
                          SizedBox(width: 10),
                          SizedBox(
                            width: 30,
                            height: 30,
                            child: FittedBox(
                              fit: BoxFit.fill,
                              child: Switch(
                                value: _sen,
                                activeColor: AppPallete.secondaryColor,
                                onChanged: (value) {
                                  setState(() {
                                    _sen = value;
                                  });
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      customtext(
                          context: context,
                          newYear: "Status",
                          font: 16.sp,
                          weight: FontWeight.w500),
                      activeOrNot(context: context, active: widget.verified),
                      const SizedBox(
                        height: 20,
                      ),
                      _customForm(context: context)
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _centerImage() {
    // Debug logging for image display logic
    print("🖼️ _centerImage() called:");
    print("   - _isCompressing: $_isCompressing");
    print("   - _selectedImage != null: ${_selectedImage != null}");
    print(
        "   - _currentCenterData?.mainImage?.url: ${_currentCenterData?.mainImage?.url}");
    print("   - widget.imagePath: ${widget.imagePath}");

    final imagePath = _currentCenterData?.mainImage?.url ?? widget.imagePath;
    final generatedUrl = imageStringGenerator(imagePath: imagePath);
    print("   - Final imagePath: $imagePath");
    print("   - Generated URL: $generatedUrl");

    return SizedBox(
      height: 125.h,
      child: Stack(
        children: [
          Positioned(
            left: 0,
            right: 0,
            child: Align(
              alignment: Alignment.center,
              child: _isCompressing
                  ? Container(
                      height: 125.h,
                      width: 125.w,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(125.w),
                      ),
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    )
                  : _selectedImage != null
                      ? Container(
                          height: 125.h,
                          width: 125.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(125.w),
                            image: DecorationImage(
                              image: FileImage(_selectedImage!),
                              fit: BoxFit.cover,
                            ),
                          ),
                        )
                      : CustomImageBuilder(
                          imagePath: generatedUrl,
                          height: 125.h,
                          width: 125.r,
                          borderRadius: 99.r,
                        ),
            ),
          ),
          Positioned(
            top: 97.h,
            right: 0.w,
            left: 110.w,
            child: Align(
              alignment: Alignment.center,
              child: CustomIconButton(
                icon: Icons.camera_alt_rounded,
                onPressed: _isCompressing ? () {} : _pickImage,
                height: 28.55.h,
                width: 32.41.w,
                color: AppPallete.darkGrey,
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _customForm({required BuildContext context}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customtext(
            context: context,
            newYear: "Centre information",
            font: 17.sp,
            weight: FontWeight.w500),
        SizedBox(
          height: 20.h,
        ),
        _textbox(
            context: context,
            title: "Display name",
            controller: displaynameController,
            valid: true),
        SizedBox(
          height: 28.h,
        ),
        _textbox(
            context: context,
            title: "Address line 1",
            controller: address1Controller,
            valid: true),
        SizedBox(
          height: 28.h,
        ),
        _googleMapsLinkField(
          context: context,
          title: "Google Maps link",
          controller: address2Controller,
          valid: true,
          subtitle: "Paste a Google Maps link for your center's location",
        ),
        SizedBox(
          height: 28.h,
        ),
        _cityAndRegion(context: context),
        SizedBox(
          height: 28.h,
        ),
        _textbox(
          context: context,
          title: "Center Phone number",
          controller: centerphonenumberController,
          keyboardType: TextInputType.phone,
          valid: true,
        ),
        SizedBox(
          height: 28.h,
        ),
        _textbox(
          context: context,
          title: "Email Address",
          controller: emailaddressController,
          keyboardType: TextInputType.emailAddress,
          valid: true,
        ),
        SizedBox(
          height: 28.h,
        ),
      ],
    );
  }

  Widget _textbox({
    required BuildContext context,
    required String title,
    double? width,
    double? height,
    required bool valid,
    required TextEditingController controller,
    TextInputType? keyboardType,
    String? subtitle,
  }) {
    // Define the validator function based on the `valid` flag
    String? Function(String?)? validator;

    if (valid == true) {
      validator = (value) {
        if (value == null || value.isEmpty) {
          return 'This field is required';
        }
        return null;
      };
    } else {
      validator = null; // No validation if `valid` is not true
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            customtext(
                context: context,
                newYear: title,
                font: 15.sp,
                weight: FontWeight.w400),
            if (valid == true)
              customtext(
                  context: context,
                  newYear: "*",
                  font: 15.sp,
                  color: AppPallete.red,
                  weight: FontWeight.w400),
          ],
        ),
        if (subtitle != null)
          Padding(
            padding: EdgeInsets.only(top: 4.h),
            child: customtext(
              context: context,
              newYear: subtitle,
              font: 12.sp,
              weight: FontWeight.w400,
              color: AppPallete.darkGrey,
            ),
          ),
        SizedBox(
          height: subtitle != null ? 12.h : 20.h,
        ),
        AuthField(
          controller: controller,
          height: height ?? 30.h,
          width: width ?? double.infinity,
          color: AppPallete.paleGrey,
          keyboard: keyboardType ?? TextInputType.text,
          validator: validator,
          onTap: () {
            // Ensure the field is visible when tapped
            Future.delayed(const Duration(milliseconds: 300), () {
              Scrollable.ensureVisible(
                context,
                alignment: 0.5,
                duration: const Duration(milliseconds: 300),
              );
            });
          },
        ),
      ],
    );
  }

  Widget _googleMapsLinkField({
    required BuildContext context,
    required String title,
    double? width,
    required bool valid,
    required TextEditingController controller,
    String? subtitle,
  }) {
    // Define the validator function for Google Maps URLs
    String? Function(String?)? validator;

    if (valid == true) {
      validator = (value) {
        if (value == null || value.isEmpty) {
          return 'This field is required';
        }
        // Optional: Add Google Maps URL validation
        if (!value.contains('maps.google') &&
            !value.contains('goo.gl') &&
            !value.contains('maps.app.goo.gl') &&
            !value.contains('google.com/maps')) {
          return 'Please enter a valid Google Maps link';
        }
        return null;
      };
    } else {
      validator = null;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            customtext(
                context: context,
                newYear: title,
                font: 15.sp,
                weight: FontWeight.w400),
            if (valid == true)
              customtext(
                  context: context,
                  newYear: "*",
                  font: 15.sp,
                  color: AppPallete.red,
                  weight: FontWeight.w400),
          ],
        ),
        if (subtitle != null)
          Padding(
            padding: EdgeInsets.only(top: 4.h),
            child: customtext(
              context: context,
              newYear: subtitle,
              font: 12.sp,
              weight: FontWeight.w400,
              color: AppPallete.darkGrey,
            ),
          ),
        SizedBox(
          height: subtitle != null ? 12.h : 20.h,
        ),
        Stack(
          children: [
            AuthField(
              controller: controller,
              height: 80.h, // Increased height for long URLs
              width: width ?? double.infinity,
              color: AppPallete.paleGrey,
              keyboard: TextInputType.url, // Use URL keyboard
              maxline: 4, // Allow multiple lines
              validator: validator,
              hintText: "https://maps.google.com/...",
              onTap: () {
                // Ensure the field is visible when tapped
                Future.delayed(const Duration(milliseconds: 300), () {
                  Scrollable.ensureVisible(
                    context,
                    alignment: 0.5,
                    duration: const Duration(milliseconds: 300),
                  );
                });
              },
            ),
            // Paste button
            Positioned(
              right: 8.w,
              top: 8.h,
              child: InkWell(
                onTap: () async {
                  try {
                    final clipboardData =
                        await Clipboard.getData(Clipboard.kTextPlain);
                    if (clipboardData?.text != null) {
                      controller.text = clipboardData!.text!;
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Google Maps link pasted successfully'),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    }
                  } catch (e) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Failed to paste from clipboard'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  }
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: AppPallete.secondaryColor,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.content_paste,
                        size: 14.sp,
                        color: Colors.white,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        'Paste',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _cityAndRegion({required BuildContext context}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: _textbox(
              context: context,
              title: "City",
              controller: citynameController,
              valid: true),
        ),
        SizedBox(
          width: 18.w,
        ),
        Expanded(
          child: _textbox(
            context: context,
            title: "Region",
            controller: regionnameController,
            valid: true,
          ),
        ),
      ],
    );
  }
}
