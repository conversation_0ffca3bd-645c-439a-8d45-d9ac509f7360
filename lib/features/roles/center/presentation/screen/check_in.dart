import 'package:class_z/core/imports.dart';

class CheckIn extends StatelessWidget {
  final List<EventModel> events;
  const CheckIn({super.key, required this.events});

  @override
  Widget build(BuildContext context) {
    print('📅 CHECK-IN SCREEN: Received ${events.length} events');
    events.forEach((event) {
      print('📅 CHECK-IN SCREEN: Event ${event.id} - ${event.classId?.classProviding}');
    });
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 19.w, top: 85.h),
              child: customBackButton(),
            ),
            Padding(
              padding: EdgeInsets.only(left: 9.w),
              child: customtext(
                  context: context,
                  newYear: "Today's Check-ins",
                  font: 30.sp,
                  weight: FontWeight.w500),
            ),
            SizedBox(
              height: 21.h,
            ),
            customDivider(width: 430.w, padding: 0),
            if (events.isEmpty)
              Padding(
                padding: EdgeInsets.symmetric(vertical: 50.h, horizontal: 20.w),
                child: Center(
                  child: customtext(
                    context: context,
                    newYear: "No check-ins for today.",
                    font: 18.sp,
                    color: Colors.grey,
                  ),
                ),
              )
            else
              ListView.separated(
                  padding: EdgeInsets.only(left: 13.w, right: 13.w, top: 18.h),
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    final event = events[index];
                    return CheckInCard(
                      context: context,
                      event: event,
                      onTap: () {
                        NavigatorService.pushNamed(
                          AppRoutes.verificationCode,
                          arguments: {
                            "classId": event.classId?.id,
                            "totalStudent": event.classId?.student?.length,
                            "bloc": context.read<CenterBloc>(),
                          },
                        );
                      },
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 20.h,
                    );
                  },
                  itemCount: events.length)
          ],
        ),
      ),
    );
  }
}
