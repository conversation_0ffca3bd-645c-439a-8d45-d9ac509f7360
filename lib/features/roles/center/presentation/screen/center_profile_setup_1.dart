import 'package:class_z/core/imports.dart';

class CenterProfileSetup1 extends StatefulWidget {
  const CenterProfileSetup1({super.key});

  @override
  State<CenterProfileSetup1> createState() => _CenterProfileSetup1State();
}

class _CenterProfileSetup1State extends State<CenterProfileSetup1> {
  final legalnameController = TextEditingController();
  final displaynameController = TextEditingController();
  final emailaddressController = TextEditingController();
  final companyphonenumberController = TextEditingController();
  final centerphonenumberController = TextEditingController();
  final citynameController = TextEditingController();
  final regionnameController = TextEditingController();
  final address1Controller = TextEditingController();
  final address2Controller = TextEditingController();
  File? _selectedImage; // Variable to hold the selected image
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  bool isFreelanceEducator = false; // Add freelance educator state

  @override
  void initState() {
    super.initState();
    // Check if the owner is an individual creator
    final ownerData = locator<SharedRepository>().getOwnerData();
    isFreelanceEducator = ownerData?.isIndividualCreator ?? false;
  }

  Future<void> _pickImage() async {
    final ImagePicker _picker = ImagePicker();
    final XFile? pickedFile =
        await _picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        print(pickedFile.path);
        _selectedImage = File(pickedFile.path);
      });
    }
  }

  @override
  void dispose() {
    // Dispose of all the controllers to free up memory
    legalnameController.dispose();
    displaynameController.dispose();
    emailaddressController.dispose();
    companyphonenumberController.dispose();
    centerphonenumberController.dispose();
    citynameController.dispose();
    regionnameController.dispose();
    address1Controller.dispose();
    address2Controller.dispose();

    // If you're using the image picker or any other resources, clean up those as well.
    super.dispose(); // Always call super.dispose() at the end
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        title: "Center Profile",
        leading: customBackButton(),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 43.h),
              child: Center(child: _centerImage()),
            ),
            Padding(
              padding: const EdgeInsets.only(
                  top: 23, bottom: 48, left: 21, right: 21),
              child: _customForm(),
            ),
            SizedBox(
              height: 60.h,
            ),
            Center(
              child: Padding(
                padding: EdgeInsets.only(bottom: 48.w),
                child: Button(
                    buttonText: "next",
                    color: AppPallete.secondaryColor,
                    height: 49.h,
                    width: 289.w,
                    onPressed: () async {
                      if (formKey.currentState!.validate()) {
                        // String? imageData =
                        //     await encodeFileToBase64(_selectedImage);
                        if (_selectedImage == null) {
                          errorState(
                              context: context,
                              error: 'please select an image');
                        } else {
                          Map<String, dynamic> data = {
                            "baseUser":
                                locator<SharedRepository>().getBaseUserId(),
                            "owner": locator<SharedRepository>().getOwnerId(),
                            "mainImage": _selectedImage,
                            "legalName": legalnameController.text,
                            "displayName": displaynameController.text,
                            "address": {
                              "address1": address1Controller.text,
                              "address2": address2Controller.text,
                              "city": citynameController.text,
                              "region": regionnameController.text,
                            },
                            "companyNumber": companyphonenumberController.text,
                            "centerNumber": centerphonenumberController.text,
                            "email": emailaddressController.text,
                            "isFreelanceEducator": isFreelanceEducator,
                          };

                          print(data);
                          NavigatorService.pushNamed(
                              AppRoutes.centerProfilesetup2,
                              arguments: data);
                        }
                        // context
                        //     .read<CenterBloc>()
                        //     .add(CreateCenterBranchEvent(data: data));
                      }
                    }),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _centerImage() {
    return Stack(
      children: [
        Positioned(
            child: Container(
          height: 125.h,
          width: 125.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(125.w),
            color: AppPallete.paleGrey,
          ),
          child: _selectedImage != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(125.w),
                  child: Image.file(
                    _selectedImage!,
                    height: 125.h,
                    width: 125.w,
                    fit: BoxFit.cover,
                  ),
                )
              : Container(),
        )),
        Positioned(
            top: 97.h,
            right: 1.3.w,
            child: CustomIconButton(
              icon: Icons.camera_alt_rounded,
              onPressed: _pickImage,
              height: 28.55.h,
              width: 32.41.w,
              color: AppPallete.darkGrey,
            ))
      ],
    );
  }

  Widget _customForm() {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          customtext(
              context: context,
              newYear: "Centre information",
              font: 17.sp,
              weight: FontWeight.w500),
          SizedBox(
            height: 20.h,
          ),
          _textbox(
              context: context,
              title: "Legal name",
              controller: legalnameController),
          SizedBox(
            height: 28.h,
          ),
          _textbox(
              context: context,
              title: "Display name",
              controller: displaynameController),
          SizedBox(
            height: 28.h,
          ),
          _textbox(
              context: context,
              title: "Address line 1",
              controller: address1Controller),
          SizedBox(
            height: 28.h,
          ),
          _textbox(
              context: context,
              title: "Google Maps link",
              controller: address2Controller),
          SizedBox(
            height: 28.h,
          ),
          _cityAndRegion(context: context),
          SizedBox(
            height: 28.h,
          ),
          _companyRegistrationField(),
          SizedBox(
            height: 28.h,
          ),
          _textbox(
              context: context,
              title: "Center Phone number",
              controller: centerphonenumberController),
          SizedBox(
            height: 28.h,
          ),
          _textbox(
              context: context,
              title: "Email Address",
              controller: emailaddressController),
          SizedBox(
            height: 28.h,
          ),
          SizedBox(
            height: 20.h,
          ),
        ],
      ),
    );
  }

  Widget _companyRegistrationField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Company registration number",
          style: TextStyle(
            fontSize: 15.sp,
            fontWeight: FontWeight.w400,
            color: isFreelanceEducator ? AppPallete.darkGrey : AppPallete.black,
          ),
        ),
        SizedBox(height: 20.h),
        IgnorePointer(
          ignoring: isFreelanceEducator,
          child: Opacity(
            opacity: isFreelanceEducator ? 0.5 : 1.0,
            child: AuthField(
              controller: companyphonenumberController,
              height: 30.h,
              width: 387.w,
              color: AppPallete.paleGrey,
            ),
          ),
        ),
        SizedBox(height: 12.h),
      ],
    );
  }

  Widget _textbox(
      {required BuildContext context,
      required String title,
      double? width,
      double? height,
      required TextEditingController controller}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
            context: context,
            title: title,
            font: 15.sp,
            weight: FontWeight.w400),
        SizedBox(
          height: 20.h,
        ),
        AuthField(
          controller: controller,
          height: height ?? 30.h,
          width: width ?? 387.w,
          color: AppPallete.paleGrey,
          validator: (value) =>
              value == null || value.isEmpty ? 'Value cant be empty' : null,
        ),
      ],
    );
  }

  Widget _cityAndRegion({required BuildContext context}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _textbox(
            context: context,
            title: "City",
            controller: citynameController,
            width: 203.w),
        _textbox(
            context: context,
            title: "Region",
            controller: regionnameController,
            width: 166.w),
      ],
    );
  }
}
