import 'package:class_z/core/imports.dart';

class CenterPayoutDetails extends StatefulWidget {
  final String centerId;
  const CenterPayoutDetails({required this.centerId, super.key});

  @override
  State<CenterPayoutDetails> createState() => _CenterPayoutDetailsState();
}

class _CenterPayoutDetailsState extends State<CenterPayoutDetails> {
  final accountHoldernameController = TextEditingController();
  final bankCodeController = TextEditingController();
  final branchCodeController = TextEditingController();
  final accountNumberController = TextEditingController();
  @override
  void initState() {
    super.initState();
    // Fetch existing center data to pre-fill the form
    _loadCenterData();
  }

  @override
  void dispose() {
    // Dispose the text editing controllers
    accountHoldernameController.dispose();
    bankCodeController.dispose();
    branchCodeController.dispose();
    accountNumberController.dispose();

    super
        .dispose(); // Always call super.dispose() after disposing your controllers
  }

  void _loadCenterData() {
    print(
        "🔄 [CENTER_PAYOUT] _loadCenterData called for centerId: ${widget.centerId}");
    // First try to get data from local storage
    final localCenterData = locator<SharedRepository>().getCenterData();
    print(
        "🗄️ [CENTER_PAYOUT] Local center data: ${localCenterData?.toString()}");

    if (localCenterData != null && localCenterData.id == widget.centerId) {
      print("✅ [CENTER_PAYOUT] Using local center data for pre-filling");
      _populateFields(localCenterData);
    } else {
      print(
          "🌐 [CENTER_PAYOUT] Local data not available or doesn't match, fetching from API");
      print(
          "🚀 [CENTER_PAYOUT] Dispatching GetCenterDataEvent for ID: ${widget.centerId}");
      context.read<CenterBloc>().add(GetCenterDataEvent(id: widget.centerId));
    }
  }

  void _populateFields(CenterData center) {
    print("📝 [CENTER_PAYOUT] _populateFields called");
    print(
        "📊 [CENTER_PAYOUT] Center bankDetails: ${center.bankDetails?.toString()}");

    setState(() {
      if (center.bankDetails != null) {
        accountHoldernameController.text =
            center.bankDetails!.accountHolderName ?? '';
        bankCodeController.text = center.bankDetails!.bankCode ?? '';
        branchCodeController.text = center.bankDetails!.branchCode ?? '';
        accountNumberController.text = center.bankDetails!.accountNumber ?? '';

        // Set the selected bank based on bank name
        selectedBank = center.bankDetails!.bankName;
        bankCode = center.bankDetails!.bankCode;

        print(
            "✅ [CENTER_PAYOUT] Fields populated: accountHolder='${accountHoldernameController.text}', bankName='$selectedBank'");
      } else {
        print("⚠️ [CENTER_PAYOUT] No bank details found in center data");
      }
    });
  }

  // Map to store bank codes
  final Map<String, String> bankCodes = {
    'Bank of China (Hong Kong)': '001',
    'HSBC': '002',
    'Standard Chartered Bank': '003',
    'Hang Seng Bank': '004',
    'Bank of East Asia': '005',
    'Citibank': '006',
    'DBS Bank (Hong Kong)': '007',
    'China Construction Bank (Asia)': '008',
    'Shanghai Pudong Development Bank': '009',
    'Agricultural Bank of China (Hong Kong)': '010',
    'China Citic Bank International': '011',
    'Fubon Bank': '012',
    'Hong Kong Monetary Authority': '013',
    'Industrial and Commercial Bank of China (Hong Kong)': '014',
    'KBC Bank': '015',
    'Mizuho Bank': '016',
    'Nanyang Commercial Bank': '017',
    'OCBC Wing Hang Bank': '018',
    'Public Bank (Hong Kong)': '019',
    'Shanghai Commercial Bank': '020',
    'Taipei Fubon Bank': '021',
  };

  String? selectedBank;
  String? bankCode;

  @override
  Widget build(BuildContext context) {
    const List<String> hongKongBanks = [
      'Bank of China (Hong Kong)',
      'HSBC',
      'Standard Chartered Bank',
      'Hang Seng Bank',
      'Bank of East Asia',
      'Citibank',
      'DBS Bank (Hong Kong)',
      'China Construction Bank (Asia)',
      'Shanghai Pudong Development Bank',
      'Agricultural Bank of China (Hong Kong)',
      'China Citic Bank International',
      'Fubon Bank',
      'Hong Kong Monetary Authority',
      'Industrial and Commercial Bank of China (Hong Kong)',
      'KBC Bank',
      'Mizuho Bank',
      'Nanyang Commercial Bank',
      'OCBC Wing Hang Bank',
      'Public Bank (Hong Kong)',
      'Shanghai Commercial Bank',
      'Taipei Fubon Bank',
    ];
    final centerBloc = BlocProvider.of<CenterBloc>(context);

    return BlocConsumer<CenterBloc, CenterState>(
        bloc: centerBloc,
        listener: (context, state) {
          if (state is CenterLoadingState) {
            loadingState(context: context);
          } else
            hideLoadingDialog(context);
          if (state is CenterUpdateSuccess) {
            NavigatorService.goBack();
          }
          if (state is CenterErrorState) {
            // Only show error for update operations, not for data fetching
            if (state.message.contains('Update') ||
                state.message.contains('save')) {
              errorState(context: context, error: state.message);
            } else {
              // For data fetching errors, just log and continue
              print("Failed to load center data: ${state.message}");
              print("User can still edit the form manually");
            }
          }
          if (state is CenterInfoSuccess) {
            print("🎉 [CENTER_PAYOUT] CenterInfoSuccess received!");
            print(
                "📦 [CENTER_PAYOUT] Center data from API: ${state.center.toString()}");
            _populateFields(state.center);
          }
        },
        builder: (context, state) {
          return Scaffold(
            appBar: CustomAppBar(
              title: "Payout Details",
              leading: customBackButton(),
              actions: [
                Padding(
                  padding: EdgeInsets.only(right: 20.w),
                  child: GestureDetector(
                    onTap: () {
                      String bankName = selectedBank ?? '';
                      if (bankName.isEmpty ||
                          accountHoldernameController.text.isEmpty ||
                          bankCodeController.text.isEmpty ||
                          branchCodeController.text.isEmpty ||
                          branchCodeController.text.isEmpty ||
                          accountNumberController.text.isEmpty)
                        errorState(
                            context: context,
                            error: 'Please fill all the option');
                      else {
                        Map<String, dynamic> data = {
                          "bankDetails": {
                            "bankName": bankName,
                            "accountHolderName":
                                accountHoldernameController.text,
                            "bankCode": bankCodeController.text,
                            "branchCode": branchCodeController.text,
                            "accountNumber": accountNumberController.text
                          }
                        };
                        context.read<CenterBloc>().add(UpdateCenterEvent(
                            centerId: widget.centerId, data: data));
                      }
                    },
                    child: Center(
                      child: Text(
                        "save",
                        style: TextStyle(
                            color: AppPallete.change,
                            fontSize: 17.sp,
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            body: Padding(
              padding: EdgeInsets.only(top: 43.h, left: 21.w, right: 21.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  customtext(
                      context: context,
                      newYear: "Payout Bank account",
                      font: 17.sp,
                      weight: FontWeight.w500),
                  SizedBox(
                    height: 20.h,
                  ),
                  customRequiredText(
                      context: context,
                      title: "Bank",
                      font: 15.sp,
                      weight: FontWeight.w400),
                  SizedBox(
                    height: 20.h,
                  ),
                  DropDown(
                      height: 30.h,
                      label: "",
                      times: hongKongBanks,
                      color: AppPallete.paleGrey,
                      onChanged: (selectedBank) {
                        setState(() {
                          this.selectedBank = selectedBank;
                          this.bankCode = bankCodes[selectedBank];
                          bankCodeController.text = bankCode ?? '';
                        });
                      }),
                  SizedBox(
                    height: 27.h,
                  ),
                  customRequiredText(
                      context: context,
                      title: "Account holder’s name",
                      font: 15.sp,
                      weight: FontWeight.w400),
                  SizedBox(
                    height: 20.h,
                  ),
                  AuthField(
                    controller: accountHoldernameController,
                    height: 30.h,
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  _bankAndBranch(context: context),
                  SizedBox(
                    height: 27.h,
                  ),
                  customRequiredText(
                      context: context,
                      title: "Account number",
                      font: 15.sp,
                      weight: FontWeight.w400),
                  SizedBox(
                    height: 20.h,
                  ),
                  AuthField(
                    controller: accountNumberController,
                    height: 30.h,
                  ),
                ],
              ),
            ),
          );
        });
  }

  Widget _textbox(
      {required BuildContext context,
      required String title,
      double? height,
      required TextEditingController controller}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            customRequiredText(
                context: context,
                title: title,
                font: 15.sp,
                weight: FontWeight.w400),
          ],
        ),
        SizedBox(
          height: 20.h,
        ),
        AuthField(
          controller: controller,
          height: height ?? 30.h,
          color: AppPallete.paleGrey,
        ),
      ],
    );
  }

  Widget _bankCode(
      {required BuildContext context,
      required String title,
      double? width,
      required TextEditingController controller}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
            context: context,
            title: title,
            font: 15.sp,
            weight: FontWeight.w400),
        SizedBox(
          height: 20.h,
        ),
        AuthField(
          controller: bankCodeController,
          height: 30.h,
          width: 119.30.w,
        ),
      ],
    );
  }

  Widget _bankAndBranch({required BuildContext context}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _bankCode(
            context: context,
            title: "Bank Code",
            controller: bankCodeController,
            width: 119.30.w),
        SizedBox(
          width: 15.w,
        ),
        Expanded(
          child: _textbox(
            context: context,
            title: "Branch Code",
            controller: branchCodeController,
          ),
        ),
      ],
    );
  }
}
