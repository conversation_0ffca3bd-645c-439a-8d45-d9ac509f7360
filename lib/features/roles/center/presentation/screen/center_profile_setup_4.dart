import 'package:class_z/core/imports.dart';

class CenterProfileSetup4 extends StatefulWidget {
  final Map<String, dynamic> data;
  const CenterProfileSetup4({required this.data, super.key});

  @override
  State<CenterProfileSetup4> createState() => _CenterProfileSetup4State();
}

class _CenterProfileSetup4State extends State<CenterProfileSetup4> {
  final descriptionController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  List<File> photo = [];
  Map<String, Map<String, String>> schedule = {
    'Monday': {'from': '', 'to': ''},
    'Tuesday': {'from': '', 'to': ''},
    'Wednesday': {'from': '', 'to': ''},
    'Thursday': {'from': '', 'to': ''},
    'Friday': {'from': '', 'to': ''},
    'Saturday': {'from': '', 'to': ''},
    'Sunday': {'from': '', 'to': ''},
  };
  @override
  void dispose() {
    descriptionController.dispose();
    // TODO: implement dispose
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: CustomAppBar(
          title: "Center Profile",
          leading: customBackButton(),
        ),
        body: SingleChildScrollView(
            child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 21, vertical: 43),
          child: Form(
            key: _formKey,
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Center(
                child: CustomImageBuilder(
                    imagePath: widget.data['mainImage']?.path,
                    height: 125.h,
                    width: 125.w,
                    borderRadius: 99.r),
              ),
              const SizedBox(
                height: 23,
              ),
              customRequiredText(
                  context: context,
                  title: "Description",
                  font: 17.sp,
                  weight: FontWeight.w500),
              const SizedBox(
                height: 20,
              ),
              AuthField(
                controller: descriptionController,
                height: 132.h,
                width: 388.w,
                hintText: "No more than 250 words",
                maxline: 6,
                validator: (value) => value == null || value.isEmpty
                    ? 'Value cant be empty'
                    : null,
              ),
              const SizedBox(
                height: 20,
              ),
              customtext(
                  context: context,
                  newYear: "Photo",
                  font: 17.sp,
                  weight: FontWeight.w500),
              const SizedBox(
                height: 20,
              ),
              customRequiredText(
                  context: context,
                  title: "Album (up to 10 photos)",
                  font: 15.sp,
                  weight: FontWeight.w500),
              const SizedBox(
                height: 20,
              ),
              AlbumCard(
                onImagesSelected: (images) {
                  photo = images;
                },
              ),
              const SizedBox(
                height: 20,
              ),
              customRequiredText(
                  context: context,
                  title: "Opening Hours",
                  font: 15.sp,
                  weight: FontWeight.w500),
              const SizedBox(
                height: 20,
              ),
              ScheduleSelector(
                schedule: schedule,
                onChanged: (updatedSchedule) {
                  setState(() {
                    schedule = updatedSchedule; // Update the parent's state
                  });
                  // Access the updated schedule here
                  print('Updated schedule: $schedule');
                },
              ),
              const SizedBox(
                height: 60,
              ),
              Center(
                child: Button(
                    buttonText: "next",
                    color: AppPallete.secondaryColor,
                    height: 49.h,
                    width: 289.w,
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        if (photo.isEmpty) {
                          errorState(
                              context: context,
                              error: "Please add photos of your center");
                        } else {
                          final OpeningHourList =
                              convertScheduleToJson(schedule);

                          widget.data.addAll({
                            "description": descriptionController.text,
                            "openingHour": OpeningHourList,
                            "images": photo
                          });
                         print(widget.data);
                          NavigatorService.pushNamed(
                              AppRoutes.centerProfilesetup5,
                              arguments: widget.data);
                        }
                      }
                    }),
              ),
              const SizedBox(
                height: 5,
              )
            ]),
          ),
        )));
  }
}
