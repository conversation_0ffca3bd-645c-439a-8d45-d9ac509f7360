import 'package:class_z/core/imports.dart';

class CenterProfileEdit extends StatelessWidget {
  final String imagePath;
  final String centerId;
  final bool verified;

  const CenterProfileEdit(
      {required this.imagePath,
      required this.centerId,
      required this.verified,
      super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "Center Profile",
        leading: customBackButton(),
      ),
      body: Padding(
        padding: EdgeInsets.only(top: 44.h, left: 20.w, right: 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _container(
                context: context,
                title: "Centre details",
                onTap: () async {
                  print("🚀 Navigating to center details...");
                  final result = await NavigatorService.pushNamed(
                    AppRoutes.centerDetails,
                    arguments: {
                      'centerId': centerId,
                      'verified': verified,
                      'imagePath': imagePath,
                    },
                  );

                  print("🔙 Returned from center details with result: $result");

                  // If center details was updated successfully, return success to center profile
                  if (result == true) {
                    print(
                        "✅ Center details updated, returning success to center profile");
                    NavigatorService.goBack(true);
                  }
                }),
            SizedBox(
              height: 20.h,
            ),
            _container(
                context: context,
                title: "Centre description",
                onTap: () {
                  NavigatorService.pushNamed(
                    AppRoutes.centerDescription,
                    arguments: {
                      'centerId': centerId,
                    },
                  );
                }),
            SizedBox(
              height: 20.h,
            ),
            _container(
                context: context,
                title: "Business",
                onTap: () {
                  NavigatorService.pushNamed(
                    AppRoutes.centerBusiness,
                    arguments: centerId,
                  );
                }),
            SizedBox(
              height: 20.h,
            ),
            _container(
                context: context,
                title: "Payout details",
                onTap: () {
                  NavigatorService.pushNamed(
                    AppRoutes.centerPayout,
                    arguments: centerId,
                  );
                }),
          ],
        ),
      ),
    );
  }

  Widget _container(
      {required BuildContext context,
      required String title,
      required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 60.h,
        padding: EdgeInsets.only(left: 12.w, right: 18.w),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
            boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            customtext(
                context: context,
                newYear: title,
                font: 17.sp,
                weight: FontWeight.w500),
            Center(
              child: CustomIconButton(
                icon: Icons.arrow_forward_ios,
                onPressed: onTap,
                color: Colors.black,
                height: 24.h,
                width: 24.w,
              ),
            )
          ],
        ),
      ),
    );
  }
}
