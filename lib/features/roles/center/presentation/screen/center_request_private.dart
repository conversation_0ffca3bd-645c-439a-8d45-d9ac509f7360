import 'package:class_z/core/imports.dart';

class CenterRequestPrivate extends StatefulWidget {
  const CenterRequestPrivate({super.key});

  @override
  State<CenterRequestPrivate> createState() => _CenterRequestPrivateState();
}

class _CenterRequestPrivateState extends State<CenterRequestPrivate> {
  List<RequestModel>? requests;

  @override
  void initState() {
    super.initState();
    _fetchRequests();
  }

  void _fetchRequests() {
    // TODO: Implement request fetching logic
    // This should be implemented using your API service
    // For now, we'll use an empty list
    setState(() {
      requests = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "Private Class Request",
        leading: customBackButton(),
      ),
      body: Padding(
        padding: EdgeInsets.only(top: 16.h, left: 25.w),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              customtext(
                  context: context,
                  newYear: "Request (${requests?.length ?? 0})",
                  font: 20.sp,
                  weight: FontWeight.w600),
              SizedBox(
                height: 34.h,
              ),
              if (requests == null)
                const Center(child: CircularProgressIndicator())
              else if (requests!.isEmpty)
                Center(
                  child: customtext(
                    context: context,
                    newYear: "No requests found",
                    font: 20.sp,
                  ),
                )
              else
                Padding(
                  padding: EdgeInsets.only(left: 5.w, right: 25.w),
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      final request = requests![index];
                      return CenterRequestPrivateCard(
                        imagePath:
                            request.centerId?.mainImage?.url ?? ImagePath.coach,
                        center:
                            request.centerId?.displayName ?? "Unknown Center",
                        course:
                            "Unknown Course", // TODO: Add course to RequestModel
                        rating: (request.centerId?.rating ?? 0.0).toString(),
                        date: request.dateRequested != null
                            ? "${request.dateRequested!.day}/${request.dateRequested!.month}/${request.dateRequested!.year}"
                            : "No date",
                        duration:
                            "45mins", // TODO: Add duration to RequestModel
                        classTime:
                            "Unknown time", // TODO: Add classTime to RequestModel
                        location: request.centerId?.address?.toString() ??
                            "Location Unknown",
                        totalStudent:
                            "0", // TODO: Add totalStudent to RequestModel
                        sen: false, // TODO: Add sen to RequestModel
                        remarks: "", // TODO: Add remarks to RequestModel
                      );
                    },
                    separatorBuilder: (context, index) {
                      return SizedBox(
                        height: 20.h,
                      );
                    },
                    itemCount: requests!.length,
                  ),
                ),
              SizedBox(
                height: 10.h,
              )
            ],
          ),
        ),
      ),
    );
  }
}
