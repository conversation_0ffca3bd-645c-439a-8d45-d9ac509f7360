import 'package:class_z/core/imports.dart';

// FIXED: Duplicate classes issue in saved programs
// Previously, this screen was creating new classes for each schedule addition
// Now it properly adds schedules to existing classes to prevent duplicates
class CenterSlotConfirmation extends StatefulWidget {
  final Map<String, dynamic> payload;
  const CenterSlotConfirmation({required this.payload, super.key});

  @override
  State<CenterSlotConfirmation> createState() => _CenterSlotConfirmationState();
}

class _CenterSlotConfirmationState extends State<CenterSlotConfirmation> {
  ClassModel? originalClassData;
  bool isLoadingClassData = false;

  @override
  void initState() {
    super.initState();
    _fetchOriginalClassData();
  }

  void _fetchOriginalClassData() async {
    setState(() {
      isLoadingClassData = true;
    });

    try {
      // Get the class data from the saved classes (we need the template data)
      final sharedRepository =
          Provider.of<SharedRepository>(context, listen: false);
      UserModel? user = sharedRepository.getUserData();
      String centerId = user!.data!.center!.id!;

      context.read<CenterBloc>().add(GetAllClassesEvent(centerId: centerId));
    } catch (e) {
      print('🚨 ERROR: Failed to fetch original class data: $e');
      setState(() {
        isLoadingClassData = false;
      });
    }
  }

  // Function to add schedule to existing class instead of creating new class
  void _addScheduleToExistingClass() {
    String originalClassId = widget.payload['class'];

    if (originalClassId.isEmpty) {
      print('🚨 ERROR: No original class ID found in payload');
      errorState(
          context: context,
          error: 'Cannot add schedule: Original class not found');
      return;
    }

    print('🔍 DEBUG: Adding schedule to existing class ID: $originalClassId');

    // Prepare the schedule data from the payload
    Map<String, dynamic> scheduleData =
        Map<String, dynamic>.from(widget.payload);
    scheduleData['dates'] = widget.payload['dates'];
    scheduleData['event'] = true; // Ensure events are created
    scheduleData['addScheduleOnly'] =
        true; // Flag to indicate this is just adding schedules
    // Optionally override or add any other required fields here

    // Copy the mainImage from the original class if available
    if (originalClassData?.mainImage?.url != null) {
      scheduleData['mainImage'] = {
        'url': originalClassData!.mainImage!.url,
        'contentType':
            originalClassData!.mainImage!.contentType ?? 'image/jpeg',
      };
      print(
          '🔍 DEBUG: Copying mainImage from original class: ${originalClassData!.mainImage!.url}');
    }

    print('🔍 DEBUG: Schedule data: $scheduleData');
    print('🔍 DEBUG: Original payload data: ${widget.payload}');

    // Update the existing class with the new schedule information
    // This will add the new schedule/dates to the existing class instead of creating duplicates
    print('schdule data $scheduleData');
    context.read<CenterBloc>().add(UpdateClassEvent(
          classId: originalClassId,
          updatedData: scheduleData,
        ));
  }

  @override
  Widget build(BuildContext context) {
    // Debug print to see what payload we received
    print('🔍 DEBUG: Received payload: ${widget.payload}');

    final sharedRepository = Provider.of<SharedRepository>(context);
    UserModel? user = sharedRepository.getUserData();
    return Scaffold(
        appBar: CustomAppBarDouble(
          title: "Confirmation",
          title2: "confirm the opening of slot",
          leading: customBackButton(),
        ),
        body: BlocConsumer<CenterBloc, CenterState>(
          listener: (context, state) {
            print('State triggered: $state');
            print(
                'Current route stack: ${NavigatorService.navigatorKey.currentState}');

            if (state is CenterLoadingState) {
              CircularProgressIndicator();
            }

            if (state is CenterErrorState) {
              errorState(context: context, error: state.message);
            }

            if (state is ClassListFetchSuccess) {
              // Find the original class data
              String classId = widget.payload['class'];
              print('🔍 DEBUG: Looking for class with ID: $classId');
              print(
                  '🔍 DEBUG: Available classes: ${state.classes.map((c) => c.id).toList()}');

              originalClassData = state.classes.firstWhere(
                (classModel) => classModel.id == classId,
                orElse: () => ClassModel(),
              );

              print(
                  '🔍 DEBUG: Found original class data - ageFrom: ${originalClassData?.ageFrom}, ageTo: ${originalClassData?.ageTo}');

              setState(() {
                isLoadingClassData = false;
              });
            }

            // Handle schedule addition success
            if (state is ClassUpdateSuccess) {
              print('✅ DEBUG: Schedule added successfully, navigating back');
              NavigatorService.removePages(4);
            }
          },
          buildWhen: (previous, current) {
            // Prevent rebuilds for transient loading states
            return current is! CenterLoadingState;
          },
          builder: (context, state) {
            return SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 61.h),
                  centerSlotConfirmationCard(
                    context: context,
                    firstColor: AppPallete.secondaryColor,
                    firstText: widget.payload['dates'][0]['repeat'] ?? '',
                    address: widget.payload['address'] ?? '',
                    date: widget.payload['dates'][0]['date'] ?? '',
                    center: user?.data?.center?.displayName ?? '',
                    course: widget.payload['name'] ?? '',
                    classTime:
                        widget.payload['dates'][0]['durationMinutes'] ?? '',
                    time:
                        "${widget.payload['dates'][0]['startTime']} - ${widget.payload['dates'][0]['endTime']}",
                    currentStudent: '#',
                    numberOfStudent:
                        widget.payload['numberOfStudent']?.toString() ?? '0',
                    ageStart: originalClassData?.ageFrom?.toString() ?? '0',
                    agefinish: originalClassData?.ageTo?.toString() ?? '0',
                    coach: widget.payload['coachName'] ?? '',
                    charges:
                        (widget.payload['charge']?.toString() ?? '') + ' HKD',
                    senFriendly: originalClassData?.sen ?? false,
                  ),
                  SizedBox(height: 60.h),
                  Center(
                    child: Button(
                      onPressed: isLoadingClassData
                          ? null
                          : () {
                              _addScheduleToExistingClass();
                            },
                      buttonText: isLoadingClassData ? "Loading..." : "Confirm",
                      color: isLoadingClassData
                          ? AppPallete.greyColor
                          : AppPallete.secondaryColor,
                      width: 289.w,
                      height: 49.h,
                    ),
                  ),
                  SizedBox(height: 10.h),
                ],
              ),
            );
          },
        ));
  }
}
