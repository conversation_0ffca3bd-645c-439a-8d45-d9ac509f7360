import 'package:class_z/core/imports.dart';

class CenterAddSlot extends StatefulWidget {
  const CenterAddSlot({super.key});

  @override
  State<CenterAddSlot> createState() => _CenterAddSlotState();
}

class _CenterAddSlotState extends State<CenterAddSlot> {
  @override
  void initState() {
    context.read<CenterBloc>().add(GetAllClassesEvent(
        centerId: locator<SharedRepository>().getCenterId()));
    super.initState();
  }

  List<ClassModel> classes = [];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(
          title: "Add Schedule",
          subtitle: "Choose which class to provide",
          leading: customBackButton(),
        ),
        body: BlocListener<CenterBloc, CenterState>(
          listener: (context, state) {
            if (state is CenterLoadingState) {
              loadingState(context: context);
            } else
              hideLoadingDialog(context);
            if (state is CenterErrorState) {
              errorState(context: context, error: state.message);
            }
            if (state is ClassListFetchSuccess) {
              setState(() {
                classes = state.classes;
              });
            }
          },
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Padding(
                //   padding: EdgeInsets.only(top: 64.h, left: 27.w, right: 27.w),
                //   child: _flexibleSlot(context: context),
                // ),
                SizedBox(
                  height: 15.h,
                ),
                ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      final classModel = classes[index];
                      print("object");
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          centerAddSlotCard(
                              context: context,
                              imagePath:
                                  "${AppText.device}${classModel.mainImage?.url}",
                              title: classModel.classProviding ?? "Untitled",
                              category: "(${classModel.level})",
                              location: "Center",
                              ageGroup:
                                  "${classModel.ageFrom}-${classModel.ageTo}",
                              rate: classModel.charge.toString(),
                              onTap: () {
                                String classId = classModel.id!;
                                String classname = classModel.classProviding!;
                                print(classModel.coach?.id);
                                NavigatorService.pushNamed(
                                    AppRoutes.centerSelectCoach,
                                    arguments: {
                                      'classId': classId,
                                      'coachId': classModel.coach?.id
                                              .toString() ??
                                          '', // Always send coachId, even if null
                                      'className': classname,
                                    });
                              }),
                          SizedBox(
                            height: 15.h,
                          )
                        ],
                      );
                    },
                    itemCount: classes.length)
              ],
            ),
          ),
        ));
  }

  // Widget _flexibleSlot({required BuildContext context}) {
  //   return GestureDetector(
  //     onTap: () {
  //       NavigatorService.pushNamed(AppRoutes.centerSelectCoach, arguments: {
  //         'classId': 'flexible',
  //         'className': 'Flexible Slot',
  //       });
  //     },
  //     child: Container(
  //       height: 107.h,
  //       width: 376.w,
  //       decoration: BoxDecoration(
  //           borderRadius: BorderRadius.circular(20.r),
  //           gradient: LinearGradient(
  //             begin: Alignment.topLeft,
  //             end: Alignment.bottomRight,
  //             colors: [
  //               AppPallete.secondaryColor.withOpacity(0.5),
  //               AppPallete.color76.withOpacity(0.5),
  //               AppPallete.color128.withOpacity(0.4),
  //               AppPallete.color234.withOpacity(0.3),
  //             ],
  //           )),
  //       child: Stack(
  //         children: [
  //           Positioned(
  //               top: 58.h,
  //               left: 13.w,
  //               child: customtext(
  //                   context: context,
  //                   newYear: "Flexible slot",
  //                   font: 20.sp,
  //                   weight: FontWeight.w700,
  //                   color: Colors.white,
  //                   shadows: [
  //                     shadow(blurRadius: 2.0, opacity: 0.8),
  //                   ])),
  //           Positioned(
  //               top: 80.h,
  //               left: 13.w,
  //               child: customtext(
  //                   context: context,
  //                   newYear:
  //                       "(Charged according to the client's program choice)",
  //                   font: 15.sp,
  //                   weight: FontWeight.w600,
  //                   color: Colors.white,
  //                   shadows: [
  //                     shadow(blurRadius: 2.0, opacity: 0.8),
  //                   ])),
  //         ],
  //       ),
  //     ),
  //   );
  // }
}
