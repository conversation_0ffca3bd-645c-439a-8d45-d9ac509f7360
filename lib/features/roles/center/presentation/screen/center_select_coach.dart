import 'package:class_z/core/imports.dart';

class CenterSelectCoach extends StatefulWidget {
  final String classId;
  final String coachId;
  final String className;
  const CenterSelectCoach(
      {required this.classId,
      required this.coachId,
      required this.className,
      super.key});

  @override
  State<CenterSelectCoach> createState() => _CenterSelectCoachState();
}

class _CenterSelectCoachState extends State<CenterSelectCoach> {
  List<CoachModel> coachs = [];
  ClassModel? currentClass;
  bool isLoadingClass = true;
  bool isLoadingCoaches = true;

  @override
  void initState() {
    super.initState();
    final centerId = locator<SharedRepository>().getCenterId();

    print("🚀 CenterSelectCoach initialized");
    print("📋 Class ID: ${widget.classId}");
    print("📋 Class Name: ${widget.className}");
    print("🏢 Center ID: $centerId");
    print(
        "⏳ Initial loading states - Class: $isLoadingClass, Coaches: $isLoadingCoaches");

    // Load both coaches and current class data
    context
        .read<CenterBloc>()
        .add(GetCoachesByCenterIdEvent(centerId: centerId));

    // Load current class to check for existing coach and students
    context.read<CenterBloc>().add(GetAllClassesEvent(centerId: centerId));
  }

  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(
          title: "Select Coach",
          subtitle: 'Coach of the program',
          leading: customBackButton(),
        ),
        body: BlocListener<CenterBloc, CenterState>(
          listener: (context, state) {
            if (state is CenterLoadingState) {
              loadingState(context: context);
            } else
              hideLoadingDialog(context);
            if (state is CenterErrorState) {
              errorState(context: context, error: state.message);
            }
            if (state is CoachListFetchSuccess) {
              setState(() {
                coachs = state.coaches;
                isLoadingCoaches = false;
              });
              print("✅ Coaches loaded: ${state.coaches.length} coaches");
            }
            if (state is ClassListFetchSuccess) {
              print(
                  "🔍 ClassListFetchSuccess - Looking for class ID: ${widget.classId}");
              print(
                  "🔍 Available classes: ${state.classes.map((c) => '${c.id}:${c.classProviding}').toList()}");

              // Find the current class
              final foundClass = state.classes.firstWhere(
                (classModel) => classModel.id == widget.classId,
                orElse: () => ClassModel(),
              );

              print(
                  "🔍 Found class: ${foundClass.id} - ${foundClass.classProviding}");
              print("🔍 Found class coach object: ${foundClass.coach}");
              print(
                  "🔍 Found class coach type: ${foundClass.coach.runtimeType}");

              setState(() {
                currentClass = foundClass;
                isLoadingClass = false;
              });
              print("✅ Class data loaded - Class ID: ${foundClass.id}");
              print(
                  "✅ Current class coach: ${foundClass.coach?.id ?? 'No coach assigned'}");
              print(
                  "✅ Current class coach name: ${foundClass.coach?.displayName ?? 'N/A'}");
            }
            if (state is CoachUpdateInClassSuccess) {
              // Coach replacement successful - reload class data to reflect changes
              final centerId = locator<SharedRepository>().getCenterId();
              context
                  .read<CenterBloc>()
                  .add(GetAllClassesEvent(centerId: centerId));

              successState(
                  context: context, title: "Coach updated successfully!");
              // Navigate back to previous screen
              NavigatorService.goBack();
            }
          },
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (!_isDataFullyLoaded())
                  Center(
                    child: Padding(
                      padding: EdgeInsets.only(top: 100.h),
                      child: Column(
                        children: [
                          CircularProgressIndicator(
                            color: AppPallete.secondaryColor,
                          ),
                          SizedBox(height: 20.h),
                          customtext(
                            context: context,
                            newYear: isLoadingClass
                                ? "Loading class information..."
                                : "Loading coaches...",
                            font: 16.sp,
                            weight: FontWeight.w500,
                          ),
                        ],
                      ),
                    ),
                  )
                else ...[
                  // Show current coach info if exists
                  if (currentClass?.coach != null) ...[
                    Padding(
                      padding:
                          EdgeInsets.only(left: 20.w, right: 20.w, top: 30.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          customtext(
                            context: context,
                            newYear: "Current Coach",
                            font: 18.sp,
                            weight: FontWeight.w600,
                          ),
                          SizedBox(height: 15.h),
                          centerSelectCoachCard(
                            context: context,
                            imagePath:
                                "${AppText.device}${currentClass?.coach?.mainImage?.url}",
                            rating: currentClass?.coach?.rating ?? 0,
                            name: currentClass?.coach?.displayName ??
                                "Unknown Coach",
                            onTap: () {
                              NavigatorService.pushNamed(
                                AppRoutes.centerTimeSlotSetup,
                                arguments: {
                                  'classId': widget.classId,
                                  'coachId': currentClass?.coach.id.toString(),
                                  'className': widget.className,
                                  'coachName': currentClass?.coach.displayName
                                },
                              );
                            },
                          ),
                          SizedBox(height: 30.h),
                        ],
                      ),
                    ),
                  ]
                  // If no coach assigned, show all available coaches
                  else ...[
                    Padding(
                      padding:
                          EdgeInsets.only(left: 20.w, right: 20.w, top: 30.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          customtext(
                            context: context,
                            newYear: "Select a Coach",
                            font: 18.sp,
                            weight: FontWeight.w600,
                          ),
                          SizedBox(height: 15.h),
                          Wrap(
                            spacing: 22.w,
                            runSpacing: 22.h,
                            children: List.generate(coachs.length, (index) {
                              final coach = coachs[index];
                              return centerSelectCoachCard(
                                context: context,
                                imagePath:
                                    "${AppText.device}${coach.mainImage?.url}",
                                rating: coach.rating ?? 0,
                                name: coach.displayName ?? "Unknown",
                                onTap: () {
                                  NavigatorService.pushNamed(
                                    AppRoutes.centerTimeSlotSetup,
                                    arguments: {
                                      'classId': widget.classId,
                                      'coachId': coach.id,
                                      'className': widget.className,
                                      'coachName': coach.displayName
                                    },
                                  );
                                },
                              );
                            }),
                          ),
                          SizedBox(height: 30.h),
                        ],
                      ),
                    ),
                  ]
                ],
              ],
            ),
          ),
        ));
  }

  /// Check if the current class has enrolled students
  bool _hasStudents() {
    if (currentClass?.student == null) return false;

    // Handle both List<String> and List<dynamic> cases
    if (currentClass!.student is List) {
      return (currentClass!.student as List).isNotEmpty;
    }

    return false;
  }

  /// Check if all required data is loaded
  bool _isDataFullyLoaded() {
    final isLoaded = !isLoadingClass && !isLoadingCoaches;
    print(
        "🔍 Data loading status - Class: ${!isLoadingClass}, Coaches: ${!isLoadingCoaches}, Fully loaded: $isLoaded");
    return isLoaded;
  }

  /// Handle coach selection with business logic
  void _handleCoachSelection(CoachModel selectedCoach) {
    print('here');
    // If no current coach, proceed to time slot setup (new class)
    if (currentClass?.coach == null) {
      NavigatorService.pushNamed(AppRoutes.centerTimeSlotSetup, arguments: {
        'classId': widget.classId,
        'coachId': selectedCoach.id,
        'className': widget.className,
        'coachName': selectedCoach.displayName
      });
      return;
    }

    // If selecting the same coach, show message
    if (currentClass?.coach?.id == selectedCoach.id) {
      errorState(
          context: context,
          error: "This coach is already assigned to this program");
      return;
    }

    // If class has students, prevent replacement
    if (_hasStudents()) {
      errorState(
          context: context,
          error: "Cannot replace coach: This program has enrolled students");
      return;
    }

    // Show confirmation dialog for coach replacement
    _showCoachReplacementDialog(selectedCoach);
  }

  /// Show confirmation dialog for coach replacement
  void _showCoachReplacementDialog(CoachModel newCoach) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.swap_horiz, color: AppPallete.secondaryColor),
              SizedBox(width: 10.w),
              Text("Replace Coach"),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("Are you sure you want to replace the current coach?"),
              SizedBox(height: 15.h),
              Row(
                children: [
                  Text("From: ", style: TextStyle(fontWeight: FontWeight.w600)),
                  Text(currentClass?.coach?.displayName ?? "Unknown"),
                ],
              ),
              SizedBox(height: 5.h),
              Row(
                children: [
                  Text("To: ", style: TextStyle(fontWeight: FontWeight.w600)),
                  Text(newCoach.displayName ?? "Unknown"),
                ],
              ),
              SizedBox(height: 15.h),
              Container(
                padding: EdgeInsets.all(10.w),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.orange, size: 16.sp),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        "This action cannot be undone. The previous coach will be removed from this program.",
                        style: TextStyle(
                            fontSize: 12.sp, color: Colors.orange[800]),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text("Cancel"),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _replaceCoach(newCoach);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppPallete.secondaryColor,
              ),
              child:
                  Text("Replace Coach", style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }

  /// Replace the coach in the class
  void _replaceCoach(CoachModel newCoach) {
    context.read<CenterBloc>().add(
          UpdateCoachInClassEvent(
            classId: widget.classId,
            coachId: newCoach.id!,
          ),
        );
  }
}
