import 'package:class_z/core/imports.dart';

class ChangeCoachScreen extends StatefulWidget {
  const ChangeCoachScreen({Key? key}) : super(key: key);

  @override
  State<ChangeCoachScreen> createState() => _ChangeCoachScreenState();
}

class _ChangeCoachScreenState extends State<ChangeCoachScreen> {
  String centerId = '';

  @override
  void initState() {
    super.initState();
    centerId = locator<SharedRepository>().getCenterId();
    context.read<CenterBloc>().add(GetAllClassesEvent(centerId: centerId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBarDouble(
        title: "Change Coach",
        title2: "Select a class to change its coach",
        leading: customBackButton(),
      ),
      body: BlocBuilder<CenterBloc, CenterState>(
        builder: (context, state) {
          if (state is CenterLoadingState) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is ClassListFetchSuccess) {
            if (state.classes.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    customtext(
                      context: context,
                      newYear: "No classes available",
                      font: 18.sp,
                      weight: FontWeight.w500,
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.only(top: 30),
              itemCount: state.classes.length,
              itemBuilder: (context, index) {
                final classModel = state.classes[index];
                final bool hasNoCoach = classModel.coach?.id == null;
                return Padding(
                  padding: EdgeInsets.only(bottom: 16.h),
                  child: savedClassCard(
                    context: context,
                    hasNoCoach: hasNoCoach,
                    edit: false,
                    imagePath: "${AppText.device}${classModel.mainImage?.url}",
                    locationType: getLocationType(classModel.address) ?? '',
                    title: classModel.classProviding ?? "Untitled",
                    category: "(${classModel.level})",
                    coach: classModel.coach?.displayName ?? "Coach TBD",
                    ageGroup: _getChangeCoachAgeGroup(classModel),
                    rate: _getChangeCoachRate(classModel),
                    time: _getChangeCoachTime(classModel),
                    onDelete: () {},
                    onTap: () {
                      print('here clicking');
                      final Map<String, String> args = {
                        'classId': classModel.id?.toString() ?? '',
                        'className':
                            classModel.classProviding?.toString() ?? '',
                        'coachId': classModel.coach?.id?.toString() ?? '',
                      };
                      print('Navigation arguments: $args');
                      print(
                          'Route name: ${AppRoutes.centerUpdateCoachForClass}');
                      NavigatorService.pushNamed(
                        AppRoutes.centerUpdateCoachForClass,
                        arguments: args,
                      );
                    },
                  ),
                );
              },
            );
          }

          if (state is CenterErrorState) {
            return Center(
              child: customtext(
                context: context,
                newYear: state.message,
                font: 18.sp,
                weight: FontWeight.w500,
              ),
            );
          }

          return const SizedBox();
        },
      ),
    );
  }

  // Helper methods for change coach screen
  String _getChangeCoachAgeGroup(ClassModel classModel) {
    final ageFrom = classModel.ageFrom;
    final ageTo = classModel.ageTo;

    if (ageFrom == null && ageTo == null) return "Age TBD";
    if (ageFrom == null) return "Up to $ageTo";
    if (ageTo == null) return "$ageFrom+";
    return "$ageFrom-$ageTo";
  }

  String _getChangeCoachRate(ClassModel classModel) {
    // First check the main class charge
    final mainCharge = classModel.charge;
    if (mainCharge != null && mainCharge > 0) {
      return mainCharge.toString();
    }

    // If main charge is null/0, check the dates for charge information
    if (classModel.dates?.isNotEmpty == true) {
      final dateCharge = classModel.dates?.first.charge;
      if (dateCharge != null && dateCharge > 0) {
        return dateCharge.toString();
      }
    }

    // Fallback to "0" if no charge found
    return "0";
  }

  String _getChangeCoachTime(ClassModel classModel) {
    if (classModel.dates != null && classModel.dates!.isNotEmpty) {
      final duration = classModel.dates![0].durationMinutes;
      if (duration != null && duration.toString().isNotEmpty) {
        if (int.tryParse(duration.toString()) != null) {
          return "${duration}min";
        }
        return duration.toString();
      }
    }
    return "45min";
  }
}
