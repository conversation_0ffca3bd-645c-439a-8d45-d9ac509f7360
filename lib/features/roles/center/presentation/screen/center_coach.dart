import 'package:class_z/core/imports.dart';

class CenterCoach extends StatefulWidget {
  const CenterCoach({super.key});

  @override
  State<CenterCoach> createState() => _CenterCoachState();
}

class _CenterCoachState extends State<CenterCoach> {
  String centerId = '';
  List<String> assignedCoachIds = [];

  @override
  void initState() {
    centerId = locator<SharedRepository>().getCenterId();
    context
        .read<CenterBloc>()
        .add(GetCoachesByCenterIdEvent(centerId: centerId));
    // TODO: implement initState
    super.initState();
  }

  // Create a controller at the class level so it can be accessed from the BlocListener
  final searchTextController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBarDouble(
        title: titleGenerateWithAddress(),
        title2: "Centre coach",
        leading: customBackButton(),
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<OwnerBloc, OwnerState>(
            listener: (context, state) {
              if (state is AssignCoachSuccessState) {
                print("Coach assigned successfully - refreshing coach list");
                // Refresh coach list when a coach is added
                context
                    .read<CenterBloc>()
                    .add(GetCoachesByCenterIdEvent(centerId: centerId));

                // Add a delay before refreshing search to ensure coach list is updated first
                Future.delayed(Duration(milliseconds: 500), () {
                  if (searchTextController.text.isNotEmpty) {
                    context.read<SearchBloc>().add(SearchQueryEvent(
                        query: searchTextController.text,
                        isSearchingCoach: true));
                  }
                });
              } else if (state is RemoveCoachSuccessState) {
                print("Coach removed successfully - refreshing coach list");
                // Refresh coach list when a coach is removed
                context
                    .read<CenterBloc>()
                    .add(GetCoachesByCenterIdEvent(centerId: centerId));

                // Add a delay before refreshing search to ensure coach list is updated first
                Future.delayed(Duration(milliseconds: 500), () {
                  if (searchTextController.text.isNotEmpty) {
                    context.read<SearchBloc>().add(SearchQueryEvent(
                        query: searchTextController.text,
                        isSearchingCoach: true));
                  }
                });
              } else if (state is OwnerErrorState) {
                // Handle the "already assigned" case
                if (state.message.contains("already assigned") ||
                    state.message
                        .contains("Coach already assigned to this center")) {
                  print(
                      "Coach already assigned - refreshing to show current state");
                  // Refresh lists to show current state
                  context
                      .read<CenterBloc>()
                      .add(GetCoachesByCenterIdEvent(centerId: centerId));

                  // Add a delay before refreshing search to ensure coach list is updated first
                  Future.delayed(Duration(milliseconds: 500), () {
                    if (searchTextController.text.isNotEmpty) {
                      context.read<SearchBloc>().add(SearchQueryEvent(
                          query: searchTextController.text,
                          isSearchingCoach: true));
                    }
                  });
                }
              }
            },
          ),
        ],
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 21,
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: AuthField(
                  controller: searchTextController,
                  height: 35.h,

                  hintText: "Skills, Centre, Location",
                  suffixIcon: Material(
                    child: InkWell(
                        child: Icon(Icons.search),
                        onTap: () {
                          context.read<SearchBloc>().add(SearchQueryEvent(
                              query: searchTextController.text,
                              isSearchingCoach: true));
                        }),
                  ),
                  // onTap: () {
                  //   context.read<SearchBloc>().add(SearchQueryEvent(
                  //       query: searchTextController.text,
                  //       isSearchingCoach: true));
                  // }
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 13.w, right: 19.w, top: 16.53.h),
                child: InkWell(
                  onTap: () {
                    NavigatorService.pushNamed(AppRoutes.centerCoachRequest);
                  },
                  child: customtext(
                      context: context,
                      newYear: "Requests",
                      font: 17.sp,
                      color: AppPallete.change,
                      weight: FontWeight.w500),
                ),
              ),
              SizedBox(
                height: 15.h,
              ),
              BlocConsumer<SearchBloc, SearchState>(
                listener: (context, state) {
                  if (state is SearchLoading)
                    loadingState(context: context);
                  else
                    hideLoadingDialog(context);
                  if (state is SearchError) {
                    errorState(context: context, error: state.error);
                  }
                },
                builder: (context, state) {
                  if (state is SearchSuccessState) {
                    return _showCoaches(context, state.search.coaches ?? []);
                  }
                  return SizedBox();
                },
              ),
              BlocConsumer<CenterBloc, CenterState>(
                listener: (context, state) {
                  if (state is CenterLoadingState)
                    CircularProgressIndicator();
                  else if (state is CenterErrorState)
                    errorState(context: context, error: state.message);
                },
                builder: (context, state) {
                  if (state is CoachListFetchSuccess) {
                    // Update the list of assigned coach IDs
                    assignedCoachIds =
                        state.coaches.map((coach) => coach.id ?? '').toList();
                    print("Updated assigned coach IDs: $assignedCoachIds");

                    return ListView.separated(
                        shrinkWrap: true,
                        padding: EdgeInsets.symmetric(horizontal: 11),
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          final coach = state.coaches[index];
                          return CenterCoachCard(
                              manager: false,
                              added:
                                  true, // Always true in this list - these are already added coaches
                              pending: false,
                              image: imageStringGenerator(
                                  imagePath: coach.mainImage?.url ?? ''),
                              coach: coach.displayName ?? 'Unknown name',
                              location: addressGenerator(
                                  address: coach.address, condition: 'city'),
                              id: coach.id ?? 'Unknown',
                              rating: coach.rating ?? 0,
                              onTap: () {
                                print('Remove coach');
                                context.read<OwnerBloc>().add(
                                      RemoveCoachEvent(
                                        coachId: coach.id ?? '',
                                        centerId: centerId,
                                        type: 'coach',
                                      ),
                                    );
                              });
                        },
                        separatorBuilder: (context, index) {
                          return SizedBox(
                            height: 16.h,
                          );
                        },
                        itemCount: state.coaches.length);
                  }
                  return SizedBox(
                    child: Center(
                      child: Text('something went wrong'),
                    ),
                  );
                },
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _showCoaches(BuildContext context, List<CoachModel> coaches) {
    if (coaches.isEmpty) {
      return Center(
        child: customtext(
            context: context, newYear: "No data available", font: 18.sp),
      );
    }
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.symmetric(horizontal: 11),
      itemCount: coaches.length,
      itemBuilder: (context, index) {
        final coach = coaches[index];
        // Check if this coach is already added to the center
        // Use both the coach.center field AND our assignedCoachIds list
        bool isAlreadyAdded =
            coach.center == centerId || assignedCoachIds.contains(coach.id);

        print(
            "Coach ${coach.displayName}: center=${coach.center}, currentCenterId=$centerId, coachId=${coach.id}, assignedCoachIds=$assignedCoachIds, isAlreadyAdded=$isAlreadyAdded");

        return Column(
          children: [
            CenterCoachCard(
              manager: false,
              added: isAlreadyAdded,
              pending: false, // We don't have pending state information
              coach: coach.displayName ?? 'Unknown',
              image: imageStringGenerator(
                imagePath: coach.mainImage?.url ?? 'Unknown',
              ),
              location: addressGenerator(
                address: coach.address,
                condition: 'city',
              ),
              id: coach.id ?? 'Unknown',
              rating: coach.rating ?? 0,
              onTap: () {
                if (isAlreadyAdded) {
                  // If already added, remove coach
                  print('Remove coach');
                  context.read<OwnerBloc>().add(
                        RemoveCoachEvent(
                          coachId: coach.id ?? '',
                          centerId: centerId,
                          type: 'coach',
                        ),
                      );
                } else {
                  // If not added and not pending, send request
                  print('Add coach - sending request to join');
                  context.read<OwnerBloc>().add(
                        RequestCoachtoJoinEvent(
                          coachId: coach.id ?? '',
                          centerId: centerId,
                          type: 'coach',
                        ),
                      );
                }
                // If pending, do nothing on tap
              },
            ),
            SizedBox(
              height: 10,
            ),
            customDivider(),
            SizedBox(
              height: 16,
            )
          ],
        );
      },
    );
  }

  // Widget _textBox(
  //     {required BuildContext context,
  //     required TextEditingController controller,
  //     required VoidCallback onTap}) {
  //   return Padding(
  //     padding: EdgeInsets.only(left: 13.w, right: 19.w),
  //     child: Container(
  //         height: 35.h,
  //         decoration: BoxDecoration(
  //             color: AppPallete.inputBox,
  //             borderRadius: BorderRadius.circular(5.r)),
  //         child: TextFormField(
  //           controller: controller,
  //           decoration: InputDecoration(
  //             hintText: "Skills, Centre, Location",
  //             suffixIcon: InkWell(onTap: onTap, child: Icon(Icons.search)),
  //             hintStyle: TextStyle(
  //                 color: AppPallete.greyWord,
  //                 fontSize: 15.sp,
  //                 fontWeight: FontWeight.w500),
  //             border: InputBorder.none,
  //             // contentPadding: EdgeInsets.symmetric(vertical: 10.h),
  //           ),
  //         )),
  //   );
  // }
}
