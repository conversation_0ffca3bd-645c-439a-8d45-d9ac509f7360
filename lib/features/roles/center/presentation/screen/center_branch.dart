import 'package:class_z/core/imports.dart';

class CenterBranch extends StatefulWidget {
  const CenterBranch({super.key});

  @override
  State<CenterBranch> createState() => _CenterBranchState();
}

class _CenterBranchState extends State<CenterBranch> {
  String titleText = "Edit";
  bool title = false;
  String leftText = "Add branch";
  bool isIndividualEducator = false;
  List<CenterData> branches = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    final ownerId = locator<SharedRepository>().getOwnerId();
    final centerId = locator<SharedRepository>().getCenterId();

    if (ownerId.isNotEmpty) {
      // Get branches for the owner
      context.read<OwnerBloc>().add(GetBranchsEvent(ownerId: ownerId));
    }

    if (centerId.isNotEmpty) {
      // Get current center info to check if individual educator
      context.read<CenterBloc>().add(GetCenterDataEvent(id: centerId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "Center Branch",
        leading: customBackButton(),
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<CenterBloc, CenterState>(
            listener: (context, state) {
              if (state is CenterInfoSuccess) {
                setState(() {
                  isIndividualEducator =
                      state.center.isFreelanceEducator ?? false;
                });
              }
            },
          ),
          BlocListener<OwnerBloc, OwnerState>(
            listener: (context, state) {
              if (state is OwnerLoadingState) {
                setState(() {
                  isLoading = true;
                });
              } else {
                setState(() {
                  isLoading = false;
                });
              }

              if (state is GetBranchSuccessState) {
                setState(() {
                  branches = state.branches;
                });
              } else if (state is OwnerErrorState) {
                // Handle error - maybe show empty state
                setState(() {
                  branches = [];
                });
              }
            },
          ),
        ],
        child: Padding(
          padding: EdgeInsets.only(left: 21.w, right: 21.w),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _firstRow(context: context),
                SizedBox(height: 24.h),
                if (isLoading)
                  const Center(child: CircularProgressIndicator())
                else
                  _branch(context: context, title: title),
                SizedBox(height: 20.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _firstRow({required BuildContext context}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Only show "Add branch" button if NOT an individual educator
        if (!isIndividualEducator)
          GestureDetector(
            onTap: () {
              NavigatorService.pushNamed(AppRoutes.centerProfilesetup1);
            },
            child: customtext(
              context: context,
              newYear: leftText,
              font: 17.sp,
              color: AppPallete.change,
              weight: FontWeight.w400,
            ),
          ),
        // Show message for individual educators instead
        if (isIndividualEducator)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: AppPallete.paleGrey,
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Text(
              "Individual educators can only have one center",
              style: TextStyle(
                fontSize: 14.sp,
                color: AppPallete.darkGrey,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        if (branches.isNotEmpty && !isIndividualEducator)
          GestureDetector(
            onTap: () {
              setState(() {
                titleText = titleText == "Edit" ? "Done" : "Edit";
                if (titleText == "Done") {
                  title = true;
                  leftText = "";
                }
                if (titleText == "Edit") {
                  title = false;
                  leftText = "Add branch";
                }
              });
            },
            child: customtext(
              context: context,
              newYear: titleText,
              font: 17.sp,
              color: AppPallete.change,
              weight: FontWeight.w400,
            ),
          ),
      ],
    );
  }

  Widget _branch({required BuildContext context, required bool title}) {
    if (branches.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(32.h),
          child: Column(
            children: [
              Icon(
                Icons.business,
                size: 64,
                color: AppPallete.darkGrey,
              ),
              SizedBox(height: 16.h),
              Text(
                "No branches found",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppPallete.darkGrey,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                isIndividualEducator
                    ? "As an individual educator, you have one center."
                    : "Create your first branch to get started.",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppPallete.darkGrey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemBuilder: (context, index) {
        final branch = branches[index];
        return centerBranchCard(
          context: context,
          imagePath:
              imageStringGenerator(imagePath: branch.mainImage?.url ?? ''),
          edit: title,
          center: branch.displayName ?? "Unknown Center",
          location: addressGenerator(address: branch.address),
          upcomingClass: "0", // You might want to fetch actual class count
        );
      },
      separatorBuilder: (context, index) {
        return SizedBox(height: 29.h);
      },
      itemCount: branches.length,
    );
  }
}
