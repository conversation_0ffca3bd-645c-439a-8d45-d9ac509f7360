import 'package:class_z/core/imports.dart';

class StudentList extends StatefulWidget {
  final Map<String, dynamic> classData;
  const StudentList({required this.classData, super.key});

  @override
  State<StudentList> createState() => _StudentListState();
}

class _StudentListState extends State<StudentList> {
  @override
  void initState() {
    context.read<CenterBloc>().add(
        GetStudentsByClassIdEvent(classId: widget.classData['slotId'] ?? ""));
    // TODO: implement initState
    super.initState();
  }

  int totalStudent = 0;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomAppBarDouble(
              title: widget.classData['title'] ?? "Unknown Class",
              title2: "Student list",
              leading: customBackButton(),
            ),
            const SizedBox(
              height: 45,
            ),
            BlocConsumer<CenterBloc, CenterState>(
              listener: (context, state) {
                if (state is CenterLoadingState)
                  loadingState(context: context);
                else
                  hideLoadingDialog(context);
                if (state is CenterErrorState) {
                  errorState(context: context, error: state.message);
                }
                if (state is StudentsByClassIdFetchSuccess) {
                  setState(() {
                    totalStudent = state.students.length;
                  });
                }
              },
              builder: (context, state) {
                return Padding(
                  padding: const EdgeInsets.only(left: 14),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      customSvgPicture(
                          imagePath: ImagePath.groupSvg,
                          height: 13.h,
                          width: 13.w),
                      SizedBox(
                        width: 5.w,
                      ),
                      customtext(
                        context: context,
                        newYear:
                            "${totalStudent}/${widget.classData['numberOfStudent']}",
                        font: 15.sp,
                        weight: FontWeight.w700,
                      ),
                    ],
                  ),
                );
              },
            ),
            BlocConsumer<CenterBloc, CenterState>(
              listener: (context, state) {
                // Listener logic is handled in the first BlocConsumer
              },
              builder: (context, state) {
                if (state is StudentsByClassIdFetchSuccess) {
                  return ListView.builder(
                    padding: EdgeInsets.only(top: 19.h, left: 14.w),
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      final student = state.students[index];
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          coachStudentListCard(
                            context: context,
                            imagePath: imageStringGenerator(
                                imagePath: student.mainImage?.url ?? ''),
                            name: student.fullname ?? "Unknown student",
                            age: "3",
                            rating: student.rating.toString(),
                            sen: student.sen ?? false,
                            id: student.classZId ?? '',
                            message: widget.classData['message'],
                            onMessagePressed: () {
                              CenterData? center =
                                  locator<SharedRepository>().getCenterData();
                              NavigatorService.pushNamed(AppRoutes.chat,
                                  arguments: {
                                    "title": center?.displayName ?? '',
                                    "imagePath": center?.mainImage?.url ?? '',
                                    "id": student.parent ?? '',
                                    'senderId': center?.id,
                                    "senderType": 'center',
                                    "oppositeModel": 'user'
                                  });
                            },
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                        ],
                      );
                    },
                    itemCount: state.students.length,
                  );
                } else
                  return const Center(
                    child: Text("No students found"),
                  );
              },
            ),
          ],
        ),
      ),
    );
  }
}
