import 'package:class_z/core/imports.dart';

class CenterSendAnnouncement extends StatefulWidget {
  final EventModel event;
  const CenterSendAnnouncement({required this.event, super.key});

  @override
  State<CenterSendAnnouncement> createState() => _CenterSendAnnouncementState();
}

class _CenterSendAnnouncementState extends State<CenterSendAnnouncement> {
  TextEditingController messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<MessageEntity>? messages = [];
  List<ChildModel>? students = [];
  int? numberOfStudents;
  @override
  void initState() {
    print("DIPU");

    numberOfStudents = widget.event.dateId?.students.length;
    // context

    context.read<AnnouncementBloc>().add(GetAnnouncementEvent(
        id: widget.event.dateId?.id.toString() ?? '', type: 'slot'));
    // TODO: implement initState
    super.initState();
  }

  @override
  void dispose() {
    messageController.dispose();
    // TODO: implement dispose
    super.dispose();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });
  }

  void _sendMessage() {
    String text = messageController.text.trim();
    if (text.isNotEmpty) {
      final newMessage = MessageModel(
        message: text,
        createdAt: DateTime.now(), // The time when the message was sent
        id: DateTime.now().toString(), // Unique id using current time
      );
      print(newMessage);
      // Add it to the list

      setState(() {
        messages?.add(newMessage); // Add message to local list
      });

      // Send the message to the backend
      context.read<AnnouncementBloc>().add(PostAnnouncementEvent(payload: {
            "slotId": widget.event.dateId?.id.toString(),
            "message": messageController.text,
            "title": widget.event.classId?.classProviding ?? '',
            "senderName":
                locator<SharedRepository>().getCenterData()?.displayName ?? '',
          }));

      // Clear input field
      messageController.clear();

      // Scroll to the latest message
      Future.delayed(Duration(milliseconds: 100), () {
        _scrollToBottom();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomAppBar(
            title: 'Announcement',
            subtitle: 'Send your Announcement',
            leading: customBackButton(),
          ),
          const SizedBox(
            height: 18,
          ),
          _classDetails(context: context),
          const SizedBox(
            height: 14,
          ),
          customDivider(),
          SizedBox(
            height: 15,
          ),
          // _buildStudentImage(
          //     context: context,
          //     childs: students,
          //     numberOfStudent: numberOfStudents.toString()),
          // SizedBox(
          //   height: 15,
          // ),
          // _buildMessage(context: context),
          Expanded(child: _announcement(context: context)),
          //     Spacer(),
          buildMessageInput(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _classDetails({required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 14.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              customtext(
                context: context,
                newYear: dateGenerator(date: widget.event.date),
                font: 12,
                weight: FontWeight.w500,
              ),
              customtext(
                context: context,
                newYear:
                    widget.event.classId?.classProviding ?? "No Class Name",
                font: 15.sp,
                weight: FontWeight.w500,
              ),
            ],
          ),
          RichText(
              text: TextSpan(children: [
            customSpanText(
              text: '${widget.event.startTime ?? "--"} - '
                  '${widget.event.endTime ?? "--"} ',
              fontSize: 15,
            ),
            customSpanText(
                text: '${widget.event.durationMinutes ?? "--"}', fontSize: 12),
          ])),
        ],
      ),
    );
  }

  Widget _announcement({required BuildContext context}) {
    return BlocConsumer<AnnouncementBloc, AnnouncementState>(
        listener: (context, state) {
      print('state $state');
      if (state is AnnouncementLoading)
        loadingState(context: context);
      else
        hideLoadingDialog(context);
      if (state is AnnouncementLoadedState || state is AnnouncementPostedState)
        _scrollToBottom();
      if (state is AnnouncementError) {
        print('error ${state.message}');
        errorState(context: context, error: state.message);
      }
    }, builder: (context, state) {
      print('current state: $state');
      if (state is AnnouncementLoadedState) {
        if (state.announcements == null) {
          return const Center(
            child: Text('No announcement available'),
          );
        }
        messages = state.announcements?.messages;
        print('studnets :${state.announcements?.slotId?.students}');
        students = state.announcements?.slotId?.students as List<ChildModel>;

        numberOfStudents = state.announcements?.slotId?.numberOfStudent;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStudentImage(
                context: context,
                childs: students,
                numberOfStudent: numberOfStudents.toString()),
            SizedBox(
              height: 15,
            ),
            _buildMessage(context: context)
          ],
        );
      }
      if (state is AnnouncementPostedState) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStudentImage(
                context: context,
                childs: students,
                numberOfStudent: numberOfStudents.toString()),
            SizedBox(
              height: 15,
            ),
            _buildMessage(context: context)
          ],
        );
      }
      return const Center(
        child: Text('No anouncement available'),
      );
    });
  }

  Widget _buildStudentImage(
      {required BuildContext context,
      required List<ChildModel>? childs,
      required String numberOfStudent}) {
    if (childs == null || childs.isEmpty) {
      return Center(child: Text('No students available.'));
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15.0),
      child: Container(
        height: 135,
        decoration: BoxDecoration(
            color: AppPallete.white,
            boxShadow: [shadow()],
            borderRadius: BorderRadius.circular(20)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 13.0, left: 13),
              child: textWithSvg(
                  context: context,
                  title: '${childs.length}/ $numberOfStudent',
                  space: 5,
                  imagePath: ImagePath.groupSvg),
            ),
            Expanded(
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                // shrinkWrap: true,
                padding: EdgeInsets.only(left: 12),
                //  physics: const NeverScrollableScrollPhysics(),
                itemCount: childs.length,
                itemBuilder: (context, index) {
                  final child = childs[index];
                  return Row(
                    children: [
                      student(
                        context: context,
                        imagePath: child.mainImage?.url ?? '',
                        name: child.fullname ?? "",
                        verified: "",
                      ),
                      SizedBox(
                        width: 12,
                      )
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessage({required BuildContext context}) {
    if (messages == null || messages?.length == 0) {
      return const Center(child: Text('No messages yet'));
    }
    return Expanded(
      child: ListView.builder(
        controller: _scrollController,
        itemCount: messages?.length,
        // reverse: true,
        padding: EdgeInsets.only(right: 15, left: 90),
        itemBuilder: (context, index) {
          final message = messages?[index];
          return Container(
            margin: const EdgeInsets.symmetric(vertical: 5),
            padding: const EdgeInsets.symmetric(horizontal: 11, vertical: 5),
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                customtext(
                    context: context,
                    newYear: message?.message ?? "",
                    font: 15,
                    weight: FontWeight.w400,
                    color: AppPallete.white,
                    textAlign: TextAlign.right),
                customtext(
                    context: context,
                    newYear:
                        "send on ${dateGenerator(date: message?.createdAt, format: 'dd/MM/yyyy')}",
                    font: 10.sp)
              ],
            ),
          );
        },
      ),
    );
  }

  Widget buildMessageInput() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0),
      child: Row(
        children: [
          Expanded(child: AuthField(controller: messageController, border: 50)),
          const SizedBox(width: 20),
          IconButton(icon: const Icon(Icons.send), onPressed: _sendMessage),
        ],
      ),
    );
  }
}
