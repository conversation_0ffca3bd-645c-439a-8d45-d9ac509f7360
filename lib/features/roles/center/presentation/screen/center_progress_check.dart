import 'package:class_z/core/imports.dart';

class CenterProgressCheck extends StatefulWidget {
  final PendingModel pending;
  const CenterProgressCheck({required this.pending, super.key});

  @override
  State<CenterProgressCheck> createState() => _CenterProgressCheckState();
}

class _CenterProgressCheckState extends State<CenterProgressCheck> {
  // Helper method to extract student ID from pending model
  String getStudentIdFromPending(PendingModel pending) {
    // Try different possible sources for the student ID
    if (pending.studentId?.id != null) {
      return pending.studentId!.id!;
    } else if (pending.student != null) {
      // Try to extract ID from student field which could be a map or string
      if (pending.student is Map) {
        return pending.student['_id'] ?? "680aaa9ea7a8843160c47f15";
      } else if (pending.student is String) {
        return pending.student;
      }
    }

    // Fallback to a hardcoded ID
    return "680aaa9ea7a8843160c47f15";
  }

  List<ProgressQuestion> questions = [
    ProgressQuestion(
        title:
            "To what extent has the student demonstrated steady growth and development in their learning?",
        option1: "Emerging",
        option2: "Steady",
        option3: "Accelerating"),
    ProgressQuestion(
        title:
            "How engagingly does the student contribute and involve themselves in learning activities?",
        option1: "Minimal",
        option2: "Engaged",
        option3: "Exemplary"),
    ProgressQuestion(
        title:
            "How effectively does the student navigate social dynamics in the learning environment?",
        option1: "Limited",
        option2: "Developing",
        option3: "Extensive"),
    ProgressQuestion(
        title:
            "How prepared is the student to effectively tackle challenges and apply their knowledge?",
        option1: "Unprepared",
        option2: "Preparing",
        option3: "Ready"),
    ProgressQuestion(
        title: "To what extent does the student enjoy the process?",
        option1: "Disinterested",
        option2: "Engaged",
        option3: "Immersive"),
    ProgressQuestion(
        title: "How stands out the student's classroom conduct?",
        option1: "Typical",
        option2: "Noteworthy",
        option3: "Inspirational"),
  ];

  final additionalInfoController = TextEditingController();
  final topicController = TextEditingController();
  final List<double> progressValues = [0.1, 0.1, 0.1, 0.1, 0.1, 0.1];
  List<File> moments = [];
  // List<String> words = ["", "", "", "", "", ""];
  void _updateProgress(int index, double newValue) {
    setState(() {
      if (newValue > 0.1) progressValues[index] = newValue;
    });
  }

  double calculateRating(List<double> progressValues) {
    double totalProgress = progressValues.reduce((a, b) => a + b);
    double averageProgress = totalProgress / progressValues.length;
    return (averageProgress * 10).clamp(0.0, 10.0);
  }

  // String _updateWord(
  //     {required double value,
  //     required String option1,
  //     required String option2,
  //     required String option3}) {
  //   if (value < 0.33)
  //     return option1;
  //   else if (value < 0.66)
  //     return option2;
  //   else
  //     return option3;
  // }

  @override
  Widget build(BuildContext context) {
    Address address = widget.pending.plainClassDetails?.center?.address;

    // Debug the entire pending object
    print("PENDING OBJECT DUMP:");
    print("Student: ${widget.pending.studentId}");
    print("PlainClassDetails: ${widget.pending.plainClassDetails}");
    print("Event: ${widget.pending.event}");

    return Scaffold(
      appBar: CustomAppBar(
        title: "Progress Check",
        leading: customBackButton(),
      ),
      body: BlocListener<ReviewBloc, ReviewState>(
        listener: (context, state) {
          if (state is ReviewLoadingState)
            loadingState(context: context);
          else
            hideLoadingDialog(context);
          if (state is ReviewErrorState)
            errorState(context: context, error: state.message);
          if (state is PostReviewSuccessState) {
            successState(
                context: context,
                title: "Successful",
                onDismiss: () {
                  context
                      .read<CenterBloc>()
                      .removeSubmittedReviewFromPending(widget.pending);

                  NavigatorService.goBack();
                });
          }
        },
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 24.h,
              ),
              _stackImage(
                  imagePath: widget.pending.studentId?.mainImage?.url ?? "",
                  name: widget.pending.studentId?.fullname ?? "",
                  id: widget.pending.studentId?.classZId ?? ""),
              SizedBox(
                height: 32.h,
              ),
              Padding(
                padding: EdgeInsets.only(left: 29.w, right: 29.w),
                child: centerProgressFeedBackCard(
                    context: context,
                    user: "Class attended",
                    confirmed: true,
                    date: dateGenerator(date: widget.pending.event?.date),
                    center:
                        addressGenerator(address: address, condition: 'full'),
                    course:
                        widget.pending.plainClassDetails?.classProviding ?? "",
                    time:
                        "${widget.pending.event?.startTime} - ${widget.pending.event?.endTime}",
                    classTime: widget.pending.event?.durationMinutes ?? "",
                    special: widget.pending.plainClassDetails?.sen ?? false,
                    coachName:
                        widget.pending.plainClassDetails?.coach?.displayName ??
                            'Unkown name'),
              ),
              SizedBox(
                height: 17.h,
              ),
              customDivider(padding: 12.w),
              _progressContainer(context: context),
              SizedBox(
                height: 12.h,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _progressContainer({required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.only(left: 21.w, right: 21.w, top: 17.h),
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
            color: Colors.white,
            boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _progress(context: context),
            SizedBox(
              height: 39.h,
            ),
            ListView.separated(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  final question = questions[index];
                  final progressValue = progressValues[index];

                  return _progressPaint(
                      title: question.title,
                      title2: question.option1,
                      title3: question.option2,
                      title4: question.option3,
                      progressValue: progressValue,
                      updateProgress: (newValue) {
                        _updateProgress(index, newValue);
                      },
                      context: context);
                },
                separatorBuilder: (context, index) {
                  return SizedBox(
                    height: 32.h,
                  );
                },
                itemCount: questions.length),
            SizedBox(
              height: 32.h,
            ),
            _additionalInfo(
                context: context,
                title:
                    "What topics were covered during today's class? (optional)",
                hintText: "No more than 100 words",
                controller: topicController),
            SizedBox(
              height: 32.h,
            ),
            _additionalInfo(
                context: context,
                title: "Additional Comment(optional)",
                hintText: "No more than 100 words",
                controller: additionalInfoController),
            SizedBox(
              height: 32.h,
            ),
            Center(
              child: Button(
                  buttonText: "Done",
                  onPressed: () {
                    double rating = calculateRating(progressValues);

                    Map<String, double> questions = {
                      "q1": progressValues.length > 0 ? progressValues[0] : 0,
                      "q2": progressValues.length > 1 ? progressValues[1] : 0,
                      "q3": progressValues.length > 2 ? progressValues[2] : 0,
                      "q4": progressValues.length > 3 ? progressValues[3] : 0,
                      "q5": progressValues.length > 4 ? progressValues[4] : 0,
                      "q6": progressValues.length > 5 ? progressValues[5] : 0,
                    };

                    // Debug the entire pending object
                    print("PENDING OBJECT DUMP:");
                    print("Student: ${widget.pending.studentId}");
                    print(
                        "PlainClassDetails: ${widget.pending.plainClassDetails}");
                    print("Event: ${widget.pending.event}");

                    // Get the student ID from the event if available
                    // This is a temporary fix - we need to find the actual student ID
                    final studentId = getStudentIdFromPending(widget.pending);

                    print("Using student ID: $studentId");

                    var payload = {
                      "reviewerId":
                          "${widget.pending.plainClassDetails?.center?.id}",
                      "classId": "${widget.pending.plainClassDetails?.id}",
                      "title":
                          "${widget.pending.plainClassDetails?.classProviding}",
                      "date": widget.pending.event?.date.toString() ??
                          DateTime.now().toString(),
                      "coachName":
                          widget.pending.plainClassDetails?.coach?.displayName,
                      "centerName": locator<SharedRepository>()
                          .getCenterData()
                          ?.displayName,
                      "rating": rating,
                      "reviewerType": "center",
                      // Use the determined student ID
                      "revieweeId": studentId,
                      "revieweeType": "child",
                      "comment": additionalInfoController.text,
                      "topic": topicController.text,
                      "questions": questions,
                      "images": moments
                    };
                    print(payload);
                    context
                        .read<ReviewBloc>()
                        .add(PostReviewEvent(payload: payload));
                  },
                  color: AppPallete.secondaryColor,
                  width: 289.w,
                  shadows: [shadow(blurRadius: 4, opacity: 0.25)],
                  height: 49.h),
            ),
            SizedBox(
              height: 46.h,
            )
          ],
        ),
      ),
    );
  }

  Widget _stackImage(
      {required String imagePath, required String name, required String id}) {
    return SizedBox(
      height: 132.h,
      width: 430.w,
      child: Stack(
        children: [
          Positioned(
              top: 104.h,
              left: 12.w,
              right: 12.w,
              child: customDivider(padding: 0)),
          Positioned(
            left: 29.w,
            child: CustomImageBuilder(
                imagePath: "${AppText.device}$imagePath",
                height: 132.h,
                width: 132.w,
                borderRadius: 99.r),
          ),
          Positioned(
            top: 50.h,
            left: 175.w,
            child: Align(
              alignment: Alignment.center,
              child: customtext(
                  context: context,
                  newYear: name,
                  font: 25.sp,
                  weight: FontWeight.w600),
            ),
          ),
          Positioned(
            top: 83.h,
            left: 177.w,
            child: customtext(
                context: context,
                newYear: id,
                font: 10.sp,
                weight: FontWeight.w400),
          ),
        ],
      ),
    );
  }

  Widget _progress({required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.only(left: 14.w, top: 13.h, right: 20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              customtext(
                  context: context,
                  newYear: "Progress check",
                  font: 20.sp,
                  weight: FontWeight.w600),
              SizedBox(
                height: 6.h,
              ),
              customtext(
                  context: context,
                  newYear:
                      "Here's the class review for ${widget.pending.studentId?.fullname ?? ""}!",
                  font: 15.sp,
                  weight: FontWeight.w500),
              SizedBox(
                height: 46.h,
              ),
              customtext(
                  context: context,
                  newYear:
                      "Upload the best shot of ${widget.pending.studentId?.fullname ?? ""} in class! ",
                  font: 15.sp,
                  weight: FontWeight.w500),
              SizedBox(
                height: 12.h,
              ),
              AlbumCard(
                onImagesSelected: (images) {
                  setState(() {
                    moments = images;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _progressPaint(
      {required BuildContext context,
      required String title,
      required String title2,
      required String title3,
      required String title4,
      required double progressValue,
      Function(double)? updateProgress}) {
    return LayoutBuilder(builder: (context, constraints) {
      return Padding(
        padding: EdgeInsets.only(left: 8.w, right: 14.w),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 9.w),
                child: customtext(
                    context: context,
                    newYear: title,
                    font: 14.sp,
                    weight: FontWeight.w500),
              ),
              Padding(
                padding: EdgeInsets.only(left: 12.w, right: 4.w, top: 32.h),
                child: GestureDetector(
                  onPanUpdate: (details) {
                    final newProgress =
                        details.localPosition.dx / constraints.maxWidth;
                    updateProgress!(newProgress);
                  },
                  child: CustomPaint(
                    painter: ProgressBarPainter(progressValue),
                    child: Container(
                      height: 20.h,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 12.w, right: 4.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Center(
                      child: customtext(
                          context: context,
                          newYear: title2,
                          font: 14.sp,
                          weight: FontWeight.w400),
                    ),
                    customtext(
                        context: context,
                        newYear: title3,
                        font: 14.sp,
                        weight: FontWeight.w400),
                    customtext(
                        context: context,
                        newYear: title4,
                        font: 14.sp,
                        weight: FontWeight.w400),
                  ],
                ),
              )
            ]),
      );
    });
  }

  Widget _additionalInfo(
      {required BuildContext context,
      required String title,
      required String hintText,
      required TextEditingController controller}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 12.5.w),
          child: customtext(
              context: context,
              newYear: title,
              font: 12.sp,
              weight: FontWeight.w500),
        ),
        SizedBox(
          height: 13.h,
        ),
        Padding(
          padding: EdgeInsets.only(left: 5.5.w, right: 5.5.w),
          child: AuthField(
            controller: controller,
            height: 74.h,
            hintText: hintText,
            border: 5.r,
          ),
        )
      ],
    );
  }
}

class ProgressBarPainter extends CustomPainter {
  final double progress;

  ProgressBarPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint trackPaint = Paint()
      ..color = AppPallete.dividerTime
      ..strokeWidth = 2.0;

    final Paint progressPaint = Paint()
      ..color = AppPallete.secondaryColor
      ..strokeWidth = 4.0;

    final Paint circlePaint = Paint()
      ..color = AppPallete.secondaryColor
      ..style = PaintingStyle.fill;

    final Paint emptyCirclePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final double circleRadius = 11.r;

    // Draw track line
    canvas.drawLine(Offset(0, size.height / 2),
        Offset(size.width, size.height / 2), trackPaint);

    // Draw progress line
    canvas.drawLine(Offset(0, size.height / 2),
        Offset(size.width * progress, size.height / 2), progressPaint);

    // Draw progress circle
    canvas.drawCircle(Offset(size.width * progress, size.height / 2),
        circleRadius, circlePaint);

    // Draw empty circle inside the progress circle
    canvas.drawCircle(Offset(size.width * progress, size.height / 2),
        circleRadius - 2, emptyCirclePaint);

    // Draw hash marks on the track line
    for (int i = 0; i <= 10; i++) {
      double x = size.width * i / 10;
      canvas.drawLine(Offset(x, size.height / 2 - 4),
          Offset(x, size.height / 2 + 4), trackPaint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}
