import 'package:class_z/core/imports.dart';

class CenterCoachSearch extends StatefulWidget {
  const CenterCoachSearch({super.key});

  @override
  State<CenterCoachSearch> createState() => _CenterCoachSearchState();
}

class _CenterCoachSearchState extends State<CenterCoachSearch> {
  final searchController = TextEditingController();
  List<CoachModel>? coaches;

  @override
  void initState() {
    super.initState();
    _fetchCoaches();
  }

  void _fetchCoaches() {
    // TODO: Implement coach fetching logic
    // This should be implemented using your API service
    // For now, we'll use an empty list
    setState(() {
      coaches = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBarDouble(
        title: "ABC testing centre - Tsim Sa Tsui",
        title2: "Centre coach",
        leading: customBackButton(),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 13.w, top: 20.4.h),
              child: <PERSON><PERSON><PERSON><PERSON>(
                controller: searchController,
                width: 398.w,
                height: 30.h,
                hintText: "Coach name, ID",
              ),
            ),
            SizedBox(
              height: 25.53.h,
            ),
            if (coaches == null)
              const Center(child: CircularProgressIndicator())
            else if (coaches!.isEmpty)
              Center(
                child: customtext(
                  context: context,
                  newYear: "No coaches found",
                  font: 20.sp,
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  final coach = coaches![index];
                  return centerCoachSearchCard(
                    context: context,
                    coach: coach.displayName ?? "Unknown Coach",
                    location: coach.address?.city ?? "Location Unknown",
                    id: coach.classZId ?? '',
                    rating: coach.rating ?? 0.0,
                    situation: "added", // TODO: Determine actual situation
                  );
                },
                separatorBuilder: (context, index) {
                  return SizedBox(
                    height: 16.h,
                  );
                },
                itemCount: coaches!.length,
              )
          ],
        ),
      ),
    );
  }
}
