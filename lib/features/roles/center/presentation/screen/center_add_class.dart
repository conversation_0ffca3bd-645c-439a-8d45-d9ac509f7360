import 'package:class_z/core/imports.dart';
import 'package:class_z/core/widgets/compressed_image_picker.dart';

class CenterAddClass extends StatefulWidget {
  const CenterAddClass({super.key});

  @override
  State<CenterAddClass> createState() => _CenterAddClassState();
}

class _CenterAddClassState extends State<CenterAddClass> {
  final classProvidingController = TextEditingController();
  final levelOfSkillController = TextEditingController();
  final classCategoryController = TextEditingController();
  final descriptionController = TextEditingController();
  final toController = TextEditingController();
  final fromController = TextEditingController();

  // Add GlobalKeys for AuthFields
  final _programNameKey = GlobalKey<FormFieldState>();
  final _levelOfSkillKey = GlobalKey<FormFieldState>();
  final _descriptionKey = GlobalKey<FormFieldState>();

  int _mode = 1;
  int _management = 1;
  int _newcomer = 1;
  int _managementFee = 1; // 1 = No, 0 = Yes
  final initialChargeController = TextEditingController();
  List<String> ageRanges = List.generate(18, (index) => (index + 1).toString());
  XFile? _image;

  Future<void> _pickImage() async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Processing image...'),
            ],
          ),
        ),
      );

      // Pick and compress image
      final result = await CompressedImagePicker.pickAndCompressImage(
        source: ImageSource.gallery,
        type: ImagePickerType.mainImage,
        onProgress: (message) {
          print('📸 Image processing: $message');
        },
      );

      // Hide loading dialog
      Navigator.of(context).pop();

      if (result.isSuccess && result.file != null) {
        setState(() {
          _image = XFile(result.file!.path);
        });

        // Show compression info
        if (result.compressionRatio > 0) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Image compressed: ${result.compressionInfo}'),
              duration: Duration(seconds: 3),
            ),
          );
        }
      } else if (result.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result.error!)),
        );
      }
      // If cancelled, do nothing
    } catch (e) {
      // Hide loading dialog if still showing
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error selecting image: $e')),
      );
    }
  }

  List<String> categories = [
    "Music",
    "Art",
    "Sports",
    "Science",
    "Technology",
    "Dance",
    "Math",
    "Language",
    "Coding",
    "Drama",
    "Health & Fitness",
    "Photography",
    "Cooking",
    "Engineering",
    "History",
    "Robotics",
    "Chess",
    "Public Speaking",
    "Writing",
    "Nature & Environment",
  ];
  @override
  Widget build(BuildContext context) {
    final centerBloc = Provider.of<CenterBloc>(context);
    final _formKey = GlobalKey<FormState>();
    return Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: CustomAppBarDouble(
          title: "Program Setup",
          title2: "Add offfered Program",
          leading: customBackButton(),
        ),
        body: BlocListener(
            bloc: centerBloc,
            listener: (context, state) {
              if (state is CenterLoadingState) {
                loadingState(context: context);
              } else
                hideLoadingDialog(context);
              if (state is CenterErrorState) {
                errorState(context: context, error: state.message);
              } else if (state is ClassCreationSuccess) {
                print(
                    '🎉 Class creation successful! New class ID: ${state.newClassId}');
                // Return true to indicate successful class creation
                NavigatorService.goBack(true);
              }
            },
            child: Padding(
              padding: EdgeInsets.only(top: 38.h, left: 16.w, right: 16.w),
              child: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      customtext(
                          context: context,
                          newYear: "Program Details",
                          font: 20.sp,
                          weight: FontWeight.w600),
                      SizedBox(
                        height: 20.h,
                      ),
                      customtext(
                          context: context,
                          newYear: "Fill in your class information",
                          font: 15.sp,
                          weight: FontWeight.w500),
                      SizedBox(
                        height: 20.h,
                      ),
                      customRequiredText(
                        context: context,
                        title: "Program name",
                        font: 15.sp,
                        weight: FontWeight.w400,
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      AuthField(
                        key: _programNameKey,
                        controller: classProvidingController,
                        width: 398.w,
                        height: 30.h,
                        hintText: "e.g. swimming, watercolour",
                        weight: FontWeight.w400,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter program name';
                          }
                          return null;
                        },
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      customRequiredText(
                        context: context,
                        title: "Level of skill",
                        font: 15.sp,
                        weight: FontWeight.w400,
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      AuthField(
                        key: _levelOfSkillKey,
                        controller: levelOfSkillController,
                        width: 398.w,
                        height: 30.h,
                        hintText: "e.g. beginner, intermediate",
                        weight: FontWeight.w400,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter skill level';
                          }
                          return null;
                        },
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      customRequiredText(
                        context: context,
                        title: "Program Category",
                        font: 15.sp,
                        weight: FontWeight.w400,
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      DropDown(
                        label: 'e.g. Music, Sports',
                        width: 249.w,
                        times: categories,
                        color: AppPallete.paleGrey,
                        controller: classCategoryController,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a category';
                          }
                          return null;
                        },
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      customRequiredText(
                        context: context,
                        title: "Description",
                        font: 17.sp,
                        weight: FontWeight.w400,
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      AuthField(
                        key: _descriptionKey,
                        controller: descriptionController,
                        width: 398.w,
                        height: 132.h,
                        maxline: 10,
                        hintText: "No more than 500 words",
                        weight: FontWeight.w400,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a description';
                          }
                          return null;
                        },
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      customtext(
                          context: context,
                          newYear: "Program Setting",
                          font: 20.sp,
                          weight: FontWeight.w600),
                      SizedBox(
                        height: 20.h,
                      ),
                      _buildRadioOption(
                        context: context,
                        value: 2,
                        selectedMode: _mode,
                        title: "Special Educational Needs (SEN) friendly",
                        onChanged: (int? newValue) {
                          setState(() {
                            _mode = newValue!;
                          });
                        },
                      ),
                      _buildRadioOption(
                        context: context,
                        value: 2,
                        selectedMode: _newcomer,
                        title: "Welcome newcomers without experience",
                        onChanged: (int? newValue) {
                          setState(() {
                            _newcomer = newValue!;
                          });
                        },
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 20.w, top: 10.h),
                        child: customtext(
                            context: context,
                            newYear: "Any management fee for new enrolment?",
                            font: 15.sp,
                            weight: FontWeight.w400),
                      ),
                      SizedBox(
                        height: 10.h,
                      ),
                      Row(
                        children: [
                          Radio<int>(
                            value: 0,
                            groupValue: _managementFee,
                            activeColor: AppPallete.darkGrey,
                            onChanged: (int? value) {
                              setState(() {
                                _managementFee = value!;
                              });
                            },
                          ),
                          SizedBox(width: 8.w),
                          customtext(
                            context: context,
                            newYear: "Yes",
                            font: 15.sp,
                            weight: FontWeight.w400,
                          ),
                          SizedBox(width: 24.w),
                          Radio<int>(
                            value: 1,
                            groupValue: _managementFee,
                            activeColor: AppPallete.darkGrey,
                            onChanged: (int? value) {
                              setState(() {
                                _managementFee = value!;
                              });
                            },
                          ),
                          SizedBox(width: 8.w),
                          customtext(
                            context: context,
                            newYear: "No",
                            font: 15.sp,
                            weight: FontWeight.w400,
                          ),
                        ],
                      ),
                      if (_managementFee == 0) ...[
                        SizedBox(height: 10.h),
                        Padding(
                          padding: EdgeInsets.only(left: 8.w, right: 8.w),
                          child: AuthField(
                            controller: initialChargeController,
                            width: 200.w,
                            height: 30.h,
                            keyboard: TextInputType.number,
                            hintText: "Enter management fee (HKD)",
                            validator: (value) {
                              if (_managementFee == 0 &&
                                  (value == null || value.isEmpty)) {
                                return 'Please enter the management fee';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                      SizedBox(
                        height: 20.h,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          customRequiredText(
                              context: context,
                              title: "Age range",
                              font: 15.sp,
                              weight: FontWeight.w400),
                          SizedBox(
                            height: 10.h,
                          ),
                          Row(
                            children: [
                              Flexible(
                                child: DropDown(
                                  height: 30.h,
                                  width: 85.w,
                                  label: "From",
                                  times: ageRanges,
                                  controller:
                                      fromController, // Using controller
                                  color: AppPallete.inputBox,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please select the age range from';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              SizedBox(
                                width: 12.w,
                              ),
                              customtext(
                                  context: context,
                                  newYear: "-",
                                  font: 10.sp,
                                  weight: FontWeight.w400),
                              SizedBox(
                                width: 12.w,
                              ),
                              Flexible(
                                child: DropDown(
                                  height: 30.h,
                                  width: 85.w,
                                  label: "To",
                                  times: () {
                                    if (fromController.text.isNotEmpty) {
                                      int fromValue =
                                          int.tryParse(fromController.text) ??
                                              0;
                                      return ageRanges
                                          .where((age) =>
                                              int.parse(age) > fromValue)
                                          .toList();
                                    }
                                    return ageRanges;
                                  }(),
                                  controller: toController,
                                  color: AppPallete.inputBox,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please select the age range to';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      customtext(
                          context: context,
                          newYear: "Photo",
                          font: 20.sp,
                          weight: FontWeight.w600),
                      SizedBox(
                        height: 20.h,
                      ),
                      customtext(
                          context: context,
                          newYear: "Show the users a glimpse of your class",
                          font: 15.sp,
                          weight: FontWeight.w400),
                      SizedBox(
                        height: 20.h,
                      ),
                      GestureDetector(
                        onTap: _pickImage,
                        child: Container(
                          height: 103.h,
                          width: 107.w,
                          decoration: BoxDecoration(
                            color: AppPallete.paleGrey,
                            borderRadius: BorderRadius.circular(10.r),
                          ),
                          child: _image != null
                              ? ClipRRect(
                                  borderRadius: BorderRadius.circular(10.r),
                                  child: Image.file(
                                    File(_image!.path),
                                    height: 103.h,
                                    width: 107.w,
                                    fit: BoxFit.cover,
                                  ),
                                )
                              : Center(
                                  child: buildImage(
                                    imagePath: ImagePath.plusNew,
                                    height: 35.35.h,
                                    width: 34.w,
                                    borderRadius: 10.r,
                                  ),
                                ),
                        ),
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      SizedBox(
                        height: 60.h,
                      ),
                      Center(
                        child: Button(
                            buttonText: "Done",
                            onPressed: () {
                              if (_formKey.currentState!.validate()) {
                                File? imageFile =
                                    _image != null ? File(_image!.path) : null;
                                final String classProviding =
                                    classProvidingController.text;
                                final String level =
                                    levelOfSkillController.text;
                                final String description =
                                    descriptionController.text;
                                final String category =
                                    classCategoryController.text;
                                final String initialCharge = _managementFee == 0
                                    ? initialChargeController.text
                                    : "0";
                                // Pricing will be set later in time slot setup
                                final String charge =
                                    "0"; // Default charge, pricing handled in time_slot_setup
                                final bool mode = _mode == 1 ? true : false;
                                final bool sen =
                                    _management == 1 ? true : false;
                                final String from = fromController.text;
                                final String to = toController.text;
                                centerBloc.add(AddClassEvent(
                                    classProviding: classProviding,
                                    description: description,
                                    category: category,
                                    mode: mode,
                                    charge: charge,
                                    initialCharge: initialCharge,
                                    level: level,
                                    sen: sen,
                                    from: from,
                                    to: to,
                                    image: imageFile));
                              }
                              ;
                            },
                            color: AppPallete.secondaryColor,
                            width: 289.w,
                            height: 49.h),
                      ),
                      SizedBox(
                        height: 2.h,
                      ),
                    ],
                  ),
                ),
              ),
            )));
  }

  Widget _buildRadioOption(
      {required BuildContext context,
      required int value,
      required int selectedMode,
      required String title,
      required ValueChanged<int?> onChanged}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Radio<int>(
          activeColor: AppPallete.darkGrey,
          value: value,
          groupValue: selectedMode,
          onChanged: onChanged,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: customtext(
            context: context,
            newYear: title,
            font: 15.sp,
            weight: FontWeight.w400,
          ),
        ),
      ],
    );
  }
}
