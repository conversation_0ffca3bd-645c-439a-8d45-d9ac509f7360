import 'package:class_z/core/imports.dart';

class PendingReviewStudent extends StatefulWidget {
  final Map<String, dynamic> classData;
  const PendingReviewStudent({super.key, required this.classData});

  @override
  State<PendingReviewStudent> createState() => _PendingReviewStudentState();
}

class _PendingReviewStudentState extends State<PendingReviewStudent> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        // Safe access to widget.classData and handling null values
        final classData = widget.classData;
        final classId = classData['classId'] ?? '';
        if (classId.isNotEmpty) {
          // Ensure the widget is still mounted before calling context.read
          if (mounted) {
            context
                .read<CenterBloc>()
                .add(GetPendingReviewsByClassIdEvent(classId));
          }
        }
      } catch (e) {
        print('initState error: $e');
      }
    });
  }

  // Helper method to deduplicate reviews
  List<PendingModel> _deduplicateReviews(List<PendingModel> reviews) {
    final Map<String, PendingModel> uniqueReviews = {};

    for (final review in reviews) {
      // Create a unique key based on student ID, class ID, and event date
      final studentId = review.studentId?.id ?? '';
      final classId = review.plainClassDetails?.id ?? '';
      final eventDate = review.event?.date?.toString() ?? '';

      final key = '$studentId-$classId-$eventDate';

      // Only add if this key doesn't exist yet
      if (!uniqueReviews.containsKey(key)) {
        uniqueReviews[key] = review;
      }
    }

    print('Deduplicated reviews: ${reviews.length} -> ${uniqueReviews.length}');
    return uniqueReviews.values.toList();
  }

  // Helper to safely convert age to int
  int _getAgeAsInt(dynamic age) {
    if (age is int) return age;
    if (age is String) {
      try {
        return int.parse(age);
      } catch (_) {}
    }
    return 0; // Default age if conversion fails
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 32, left: 19),
                child: CustomAppBarDouble(
                  title: "Pending Review",
                  title2: widget.classData['name'] ?? '',
                  leading: customBackButton(),
                ),
              ),
              BlocConsumer<CenterBloc, CenterState>(listener: (context, state) {
                if (state is CenterLoadingState)
                  loadingState(context: context);
                else
                  hideLoadingDialog(context);
                if (state is CenterErrorState) {
                  errorState(context: context, error: state.message);
                }
              }, builder: (context, state) {
                print('current state in PendingReviewStudent: $state');
                if (state is PendingReviewsForClassFetchSuccess) {
                  // Deduplicate reviews before displaying
                  final reviews =
                      _deduplicateReviews(state.pendingReviewsForClass);

                  if (reviews.isEmpty) {
                    return Center(
                      child: Padding(
                        padding: EdgeInsets.only(top: 50.h),
                        child: Text(
                          "No pending reviews available",
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                            color: AppPallete.darkGrey,
                          ),
                        ),
                      ),
                    );
                  }

                  return ListView.separated(
                      padding:
                          EdgeInsets.only(left: 17.w, top: 38.h, right: 17.w),
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        final review = reviews[index];
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            pendingReviewStudentCard(
                              context: context,
                              imagePath: review.studentId?.mainImage?.url ??
                                  ImagePath.coach,
                              name: review.studentId?.fullname ?? "Unknown",
                              coach: review
                                      .plainClassDetails?.coach?.displayName ??
                                  "Unknown Coach",
                              course:
                                  review.plainClassDetails?.classProviding ??
                                      "Unknown Class",
                              rating: review.studentId?.rating ?? 0.0,
                              age: _getAgeAsInt(review.studentId?.age),
                              dateTime: review.event?.date ?? DateTime.now(),
                              onTap: () {
                                NavigatorService.pushNamed(
                                        AppRoutes.centerProgressCheck,
                                        arguments: review)
                                    .then((_) {
                                  // After coming back from the review screen,
                                  // refresh the pending reviews for this specific class.
                                  final classId =
                                      widget.classData['classId'] ?? '';
                                  if (classId.isNotEmpty && mounted) {
                                    context.read<CenterBloc>().add(
                                        GetPendingReviewsByClassIdEvent(
                                            classId));
                                  }
                                });
                              },
                            ),
                            SizedBox(
                              width: 27.w,
                            ),
                            if (index + 1 < reviews.length)
                              pendingReviewStudentCard(
                                  context: context,
                                  imagePath: reviews[index + 1]
                                          .studentId
                                          ?.mainImage
                                          ?.url ??
                                      ImagePath.coach,
                                  name:
                                      reviews[index + 1].studentId?.fullname ??
                                          "Unknown",
                                  coach: reviews[index + 1]
                                          .plainClassDetails
                                          ?.coach
                                          ?.displayName ??
                                      "Unknown Coach",
                                  course: reviews[index + 1]
                                          .plainClassDetails
                                          ?.classProviding ??
                                      "Unknown Class",
                                  rating:
                                      reviews[index + 1].studentId?.rating ??
                                          0.0,
                                  age: _getAgeAsInt(
                                      reviews[index + 1].studentId?.age),
                                  dateTime: reviews[index + 1].event?.date ??
                                      DateTime.now(),
                                  onTap: () {
                                    NavigatorService.pushNamed(
                                            AppRoutes.centerProgressCheck,
                                            arguments: reviews[index + 1])
                                        .then((_) {
                                      // After coming back from the review screen,
                                      // refresh the pending reviews for this specific class.
                                      final classId =
                                          widget.classData['classId'] ?? '';
                                      if (classId.isNotEmpty && mounted) {
                                        context.read<CenterBloc>().add(
                                            GetPendingReviewsByClassIdEvent(
                                                classId));
                                      }
                                    });
                                  })
                          ],
                        );
                      },
                      separatorBuilder: (context, index) {
                        return SizedBox(
                          height: 22.h,
                        );
                      },
                      // Adjust item count to account for displaying 2 items per row
                      itemCount: (reviews.length / 2).ceil());
                }
                return Center(
                  child: Padding(
                    padding: EdgeInsets.only(top: 50.h),
                    child: Text(
                      'Loading reviews...',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                        color: AppPallete.darkGrey,
                      ),
                    ),
                  ),
                );
              })
            ],
          ),
        ),
      ),
    );
  }
}
