import 'package:class_z/core/imports.dart';

class CenterUpdateCoachForClass extends StatefulWidget {
  final String classId;
  final String className;
  final String coachId;
  const CenterUpdateCoachForClass(
      {required this.classId,
      required this.className,
      required this.coachId,
      super.key});

  @override
  State<CenterUpdateCoachForClass> createState() =>
      _CenterUpdateCoachForClassState();
}

class _CenterUpdateCoachForClassState extends State<CenterUpdateCoachForClass> {
  @override
  void initState() {
    super.initState();
    final centerId = locator<SharedRepository>().getCenterId();
    context
        .read<CenterBloc>()
        .add(GetCoachesByCenterIdEvent(centerId: centerId));

    // Log the received parameters for debugging
    print(
        "CenterSelectCoach initialized with classId: ${widget.classId}, className: ${widget.className}");
  }

  List<CoachModel> coachs = [];
  List<CoachModel> getOrderedCoaches() {
    // Find the assigned coach
    final assignedCoachIndex =
        coachs.indexWhere((coach) => coach.id == widget.coachId);
    if (assignedCoachIndex == -1) return coachs;

    // Move assigned coach to the beginning of the list
    final assignedCoach = coachs[assignedCoachIndex];
    final otherCoaches = [...coachs];
    otherCoaches.removeAt(assignedCoachIndex);

    return [assignedCoach, ...otherCoaches];
  }

  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(
          title: "Select Coach",
          subtitle: 'Coach of the program',
          leading: customBackButton(),
        ),
        body: BlocListener<CenterBloc, CenterState>(
          listener: (context, state) {
            if (state is CenterLoadingState) {
              loadingState(context: context);
            } else
              hideLoadingDialog(context);
            if (state is CenterErrorState) {
              errorState(context: context, error: state.message);
            }
            if (state is CoachListFetchSuccess) {
              coachs = state.coaches;
              setState(() {});
            }
            if (state is CoachUpdateInClassSuccess) {
              greenState(
                  context: context, message: 'Coach updated successfully');
              NavigatorService.goBack();
            }
          },
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 66.h),
                  child: Wrap(
                    spacing: 22.w, // Space between the coach cards
                    runSpacing: 22.h, // Space between the rows
                    children: List.generate(coachs.length, (index) {
                      final orderedCoaches = getOrderedCoaches();
                      final coach = orderedCoaches[index];
                      final isAssignedCoach = coach.id == widget.coachId;
                      return Stack(
                        children: [
                          centerSelectCoachCard(
                            context: context,
                            imagePath:
                                "${AppText.device}${coach.mainImage?.url}", // Coach image
                            rating: coach.rating ?? 0, // Coach rating
                            name: coach.displayName ?? "a", // Coach name
                            onTap: isAssignedCoach
                                ? () {}
                                : () {
                                    print('changing coach');
                                    context
                                        .read<CenterBloc>()
                                        .add(UpdateCoachInClassEvent(
                                          classId: widget.classId,
                                          coachId: coach.id ?? '',
                                        ));
                                  },
                          ),
                          if (isAssignedCoach)
                            Positioned(
                              top: 10,
                              right: 10,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 8.w, vertical: 4.h),
                                decoration: BoxDecoration(
                                  color: Colors.green,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Current Coach',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      );
                    }),
                  ),
                )
              ],
            ),
          ),
        ));
  }
}
