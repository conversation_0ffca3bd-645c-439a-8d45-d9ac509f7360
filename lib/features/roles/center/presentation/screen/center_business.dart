import 'package:class_z/core/imports.dart';

class CenterBusiness extends StatefulWidget {
  final String centerId;
  const CenterBusiness({required this.centerId, super.key});

  @override
  State<CenterBusiness> createState() => _CenterBusinessState();
}

class _CenterBusinessState extends State<CenterBusiness> {
  final businessRegistrationNumberController = TextEditingController();
  final legalNameController = TextEditingController();
  @override
  void initState() {
    super.initState();
    // Fetch existing center data to pre-fill the form
    _loadCenterData();
  }

  @override
  void dispose() {
    businessRegistrationNumberController.dispose();
    legalNameController.dispose();
    super.dispose();
  }

  void _loadCenterData() {
    print(
        "🔄 [CENTER_BUSINESS] _loadCenterData called for centerId: ${widget.centerId}");
    // First try to get data from local storage
    final localCenterData = locator<SharedRepository>().getCenterData();
    print(
        "🗄️ [CENTER_BUSINESS] Local center data: ${localCenterData?.toString()}");

    if (localCenterData != null && localCenterData.id == widget.centerId) {
      print("✅ [CENTER_BUSINESS] Using local center data for pre-filling");
      _populateFields(localCenterData);
    } else {
      print(
          "🌐 [CENTER_BUSINESS] Local data not available or doesn't match, fetching from API");
      print(
          "🚀 [CENTER_BUSINESS] Dispatching GetCenterDataEvent for ID: ${widget.centerId}");
      context.read<CenterBloc>().add(GetCenterDataEvent(id: widget.centerId));
    }
  }

  void _populateFields(CenterData center) {
    print("📝 [CENTER_BUSINESS] _populateFields called");
    print(
        "📊 [CENTER_BUSINESS] Center data: businessNumber=${center.businessNumber}, legalName=${center.legalName}");

    setState(() {
      businessRegistrationNumberController.text = center.businessNumber ?? '';
      legalNameController.text = center.legalName ?? '';
    });

    print(
        "✅ [CENTER_BUSINESS] Fields populated: businessNumber='${businessRegistrationNumberController.text}', legalName='${legalNameController.text}'");
  }

  @override
  Widget build(BuildContext context) {
    final centerBloc = BlocProvider.of<CenterBloc>(context);
    return BlocConsumer(
      bloc: centerBloc,
      listener: (context, state) {
        if (state is CenterLoadingState) {
          loadingState(context: context);
        } else
          hideLoadingDialog(context);

        if (state is CenterUpdateSuccess) {
          errorState(context: context, error: 'Update done');
          NavigatorService.goBack();
        }
        if (state is CenterErrorState) {
          // Only show error for update operations, not for data fetching
          if (state.message.contains('Update') ||
              state.message.contains('save')) {
            errorState(context: context, error: state.message);
          } else {
            // For data fetching errors, just log and continue
            print("Failed to load center data: ${state.message}");
            print("User can still edit the form manually");
          }
        }
        if (state is CenterInfoSuccess) {
          print("🎉 [CENTER_BUSINESS] CenterInfoSuccess received!");
          print(
              "📦 [CENTER_BUSINESS] Center data from API: ${state.center.toString()}");
          _populateFields(state.center);
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: CustomAppBar(
            title: "Center Business",
            leading: customBackButton(),
            actions: [
              Padding(
                padding: EdgeInsets.only(
                    right: 20.w), // Add some padding to the right
                child: InkWell(
                  onTap: () {
                    if (businessRegistrationNumberController.text.isEmpty &&
                        legalNameController.text.isEmpty) {
                      errorState(context: context, error: 'Nothing to Update');
                      NavigatorService.goBack();
                    } else {
                      String businessNumber =
                          businessRegistrationNumberController.text;
                      String legalName = legalNameController.text;
                      Map<String, dynamic> data = {
                        "businessNumber": businessNumber,
                        "legalName": legalName
                      };
                      context.read<CenterBloc>().add(UpdateCenterEvent(
                          data: data, centerId: widget.centerId));
                    }
                  },
                  child: Center(
                    child: Text(
                      "save",
                      style: TextStyle(
                          color: AppPallete.change,
                          fontSize: 17.sp,
                          fontWeight: FontWeight.w400),
                    ),
                  ),
                ), // Text on the right
              ),
            ],
          ),
          body: Padding(
            padding: EdgeInsets.only(top: 44.h, left: 21.w, right: 21.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                customtext(
                    context: context,
                    newYear:
                        "Business information could not be amended, contact us if assistance is needed",
                    font: 15.sp,
                    weight: FontWeight.w400),
                SizedBox(
                  height: 20.h,
                ),
                customtext(
                    context: context,
                    newYear: "Verification",
                    font: 17.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 20.h,
                ),
                customtext(
                    context: context,
                    newYear: "Business registration number",
                    font: 15.sp,
                    weight: FontWeight.w400),
                SizedBox(
                  height: 20.h,
                ),
                // Check if this is a freelance educator from center data
                BlocBuilder<CenterBloc, CenterState>(
                  builder: (context, state) {
                    bool isFreelance = false;
                    if (state is CenterInfoSuccess) {
                      isFreelance = state.center.isFreelanceEducator ?? false;
                    }

                    return isFreelance
                        ? Container(
                            height: 30.h,
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(
                                horizontal: 12.w, vertical: 8.h),
                            decoration: BoxDecoration(
                              color: AppPallete.paleGrey,
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                            child: Text(
                              "Individual Educator",
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppPallete.darkGrey,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          )
                        : AuthField(
                            controller: businessRegistrationNumberController,
                            height: 30.h,
                            keyboard: TextInputType.number,
                          );
                  },
                ),
                SizedBox(
                  height: 20.h,
                ),
                customtext(
                    context: context,
                    newYear: "Ownership",
                    font: 17.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 20.h,
                ),
                customtext(
                    context: context,
                    newYear: "Your name",
                    font: 15.sp,
                    weight: FontWeight.w400),
                SizedBox(
                  height: 20.h,
                ),
                AuthField(
                  controller: legalNameController,
                  height: 30.h,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
