import 'package:class_z/core/imports.dart';

class AvailableCoachesScreen extends StatefulWidget {
  const AvailableCoachesScreen({Key? key}) : super(key: key);

  @override
  State<AvailableCoachesScreen> createState() => _AvailableCoachesScreenState();
}

class _AvailableCoachesScreenState extends State<AvailableCoachesScreen> {
  String centerId = '';

  @override
  void initState() {
    super.initState();
    centerId = locator<SharedRepository>().getCenterId();
    context
        .read<CenterBloc>()
        .add(GetCoachesByCenterIdEvent(centerId: centerId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBarDouble(
        title: "Available Coaches",
        title2: "Find your perfect coach",
        leading: customBackButton(),
      ),
      body: BlocBuilder<CenterBloc, CenterState>(
        builder: (context, state) {
          if (state is CenterLoadingState) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is CoachListFetchSuccess) {
            if (state.coaches.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    customtext(
                      context: context,
                      newYear: "No coaches available",
                      font: 18.sp,
                      weight: FontWeight.w500,
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              padding: EdgeInsets.all(16.w),
              itemCount: state.coaches.length,
              itemBuilder: (context, index) {
                final coach = state.coaches[index];
                return Padding(
                  padding: EdgeInsets.only(bottom: 16.h),
                  child: SimpleCenterCoachCard(
                    image: imageStringGenerator(
                        imagePath: coach.mainImage?.url ?? ''),
                    coach: coach.displayName ?? 'Unknown name',
                    location: addressGenerator(
                        address: coach.address, condition: 'city'),
                    id: coach.classZId ?? 'Unknown',
                    rating: coach.rating ?? 0,
                    onTap: () {
                      // Navigate to coach details or perform other actions
                    },
                  ),
                );
              },
            );
          }

          if (state is CenterErrorState) {
            return Center(
              child: customtext(
                context: context,
                newYear: state.message,
                font: 18.sp,
                weight: FontWeight.w500,
              ),
            );
          }

          return const SizedBox();
        },
      ),
    );
  }
}
