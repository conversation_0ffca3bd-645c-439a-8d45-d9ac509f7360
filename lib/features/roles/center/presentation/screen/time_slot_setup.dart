import 'package:class_z/core/imports.dart';
import 'package:class_z/core/services/currency_service.dart';
import 'package:flutter/services.dart';

class CenterTimeSlotSetup extends StatefulWidget {
  final String coachId;
  final String classId;
  final String className;
  final String coachName;
  const CenterTimeSlotSetup(
      {required this.classId,
      required this.coachId,
      required this.className,
      required this.coachName,
      super.key});

  @override
  State<CenterTimeSlotSetup> createState() => _CenterTimeSlotSetupState();
}

class _CenterTimeSlotSetupState extends State<CenterTimeSlotSetup> {
  final ValueNotifier<bool> showCourseStructure = ValueNotifier<bool>(false);

  List<Map<String, dynamic>> _selectedDay = [];
  DateTime? _tempDate;
  String? _tempDay;
  String? _durationText;
  void _openCustomCalendar(BuildContext context) async {
    DateTime? _pickedDate = await showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          child: CustomCalendar(
              focusedDay: DateTime.now(),
              selectedDay: _tempDate,
              onDaySelected: (selectedDay, focusDay) {
                Navigator.pop(context, selectedDay);
              }),
        );
      },
    );
    print(_pickedDate);
    if (_pickedDate != null) {
      setState(() {
        _tempDate = _pickedDate;
        _tempDay = DateFormat('EEE').format(_pickedDate);
        // _selectedDay.add(_pickedDate);
        // _dayName.add(DateFormat('EEE').format(_pickedDate));
      });
    }
  }

  void _removeDateEntry(int index) {
    setState(() {
      _selectedDay.removeAt(index);
    });
  }

  final List<String> repeat = [
    'Does not Repeat',
    'Every day',
    'Every week',
    'Every year'
  ];
  final List<String> times = [
    '6:00 AM',
    '7:00 AM',
    '8:00 AM',
    '9:00 AM',
    '10:00 AM',
    '11:00 AM',
    '12:00 PM',
    '1:00 PM',
    '2:00 PM',
    '3:00 PM',
    '4:00 PM',
    '5:00 PM',
    '6:00 PM',
    '7:00 PM',
    '8:00 PM',
    '9:00 PM',
    '10:00 PM',
  ];
  final List<String> timesTo = [
    '6:45 AM',
    '7:45 AM',
    '8:45 AM',
    '9:45 AM',
    '10:45 AM',
    '11:45 AM',
    '12:45 PM',
    '1:45 PM',
    '2:45 PM',
    '3:45 PM',
    '4:45 PM',
    '5:45 PM',
    '6:45 PM',
    '7:45 PM',
    '8:45 PM',
    '9:45 PM',
    '10:45 PM',
  ];
  final numberOfStudentController = TextEditingController();
  final minimumStudentController =
      TextEditingController(); // New controller for minimum students
  final classController = TextEditingController();
  final addressController = TextEditingController();
  final fromController = TextEditingController();
  final toController = TextEditingController();
  final hkdAmountController =
      TextEditingController(); // Changed from pricingController to HKD input
  final dateController = TextEditingController();
  final repeatController = TextEditingController();
  bool change = false;
  bool course = false;
  List<String> selectedLanguages = [];
  List<Map<String, String>> selectedDateEntries = [];
  List<Map<String, String>> dateTime = [];
  List<String> formattedDates = [];
  int? _editingIndex;
  @override
  void initState() {
    classController.addListener(() {
      final textValue = int.tryParse(classController.text);
      final shouldShow = textValue != null && textValue > 2;
      showCourseStructure.value = shouldShow;
      course = shouldShow ? true : false;
    });
    super.initState();
  }

  @override
  void dispose() {
    numberOfStudentController.dispose();
    minimumStudentController.dispose(); // Dispose the new controller
    classController.dispose();
    addressController.dispose();
    fromController.dispose();
    toController.dispose();
    hkdAmountController.dispose(); // Changed from pricingController
    dateController.dispose();
    repeatController.dispose();
    super.dispose();
  }

  List<String> formatDateList(List<Map<String, String>> dateList) {
    Map<String, String> monthMapping = {
      'January': '01',
      'February': '02',
      'March': '03',
      'April': '04',
      'May': '05',
      'June': '06',
      'July': '07',
      'August': '08',
      'September': '09',
      'October': '10',
      'November': '11',
      'December': '12',
    };

    String currentYear = DateTime.now().year.toString();
    List<String> formattedDates = []; // Clear the formattedDates list

    for (Map<String, String> dateMap in dateList) {
      String day = dateMap['day'] ?? '';
      String monthName = dateMap['month'] ?? '';
      String month = monthMapping[monthName] ?? '00';

      // Skip invalid or incomplete dates
      if (day.isNotEmpty && monthName.isNotEmpty) {
        // Ensure day is zero-padded if needed
        String paddedDay = day.padLeft(2, '0');

        formattedDates.add('$paddedDay/$month/$currentYear');
      }
    }
    return formattedDates;
  }

  void _updateDateTimeEntry(
      {required String date,
      required String startTime,
      required String endTime}) {
    // Calculate the duration
    if (startTime.isNotEmpty && endTime.isNotEmpty) {
      DateTime start = parseTime(startTime);
      DateTime end = parseTime(endTime);

      // Calculate the duration in minutes
      Duration duration = end.difference(start);
      int minutes = duration.inMinutes;
      setState(() {
        _durationText = '${minutes}mins';
      });

      // Check if the date already exists
    }
  }

  bool joinNew = false;

  void _joinNew(String option) {
    setState(() {
      option == 'Yes' ? joinNew = true : joinNew = false;
    });
  }

  final _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.only(left: 16.w, top: 31.h, right: 16.h),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomAppBarDouble(
                title: "Time Slot Setup",
                title2: "add upcoming slot",
                leading: customBackButton(),
              ),
              SizedBox(
                height: 62.h,
              ),
              customtext(
                  context: context,
                  newYear: "Program details",
                  font: 20.sp,
                  weight: FontWeight.w600),
              SizedBox(height: 20.h),
              customtext(
                  context: context,
                  newYear: widget.className,
                  font: 20.sp,
                  weight: FontWeight.w600),
              SizedBox(height: 20.h),
              customtext(
                  context: context,
                  newYear: "Address",
                  font: 20.sp,
                  weight: FontWeight.w600),
              SizedBox(height: 20.h),
              _addressRow(),
              SizedBox(height: 20.h),
              change ? _addressInput() : Container(),
              customRequiredText(
                  context: context,
                  title: "Number of Student",
                  font: 20.sp,
                  weight: FontWeight.w600),
              SizedBox(height: 22.h),
              _numberOfStudentsInput(),
              SizedBox(height: 20.h),
              customRequiredText(
                  context: context,
                  title: "Minimum Student Required",
                  font: 20.sp,
                  weight: FontWeight.w600),
              SizedBox(height: 22.h),
              _minimumStudentsInput(),
              SizedBox(height: 20.h),
              customRequiredText(
                  context: context,
                  title: "Teaching language",
                  font: 20.sp,
                  weight: FontWeight.w600),
              SizedBox(height: 20.h),
              _addLanguage(),
              SizedBox(height: 20.h),
              customRequiredText(
                  context: context,
                  title: "Number of Class",
                  font: 20.sp,
                  weight: FontWeight.w600),
              SizedBox(
                height: 20.h,
              ),
              AuthField(
                controller: classController,
                keyboard: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                height: 30.h,
                width: 150.w,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter class';
                  }
                  return null;
                },
              ),
              SizedBox(
                height: 20.h,
              ),
              customRequiredText(
                  context: context,
                  title: "Date",
                  font: 20.sp,
                  weight: FontWeight.w600),
              SizedBox(height: 20.h),
              customtext(
                  context: context,
                  newYear: "Set the date for the class",
                  font: 15.sp,
                  weight: FontWeight.w400),
              SizedBox(height: 20.h),
              _daymonthplus(),
              SizedBox(height: 20.h),
              ValueListenableBuilder<bool>(
                valueListenable: showCourseStructure,
                builder: (context, shouldShow, _) {
                  return shouldShow ? _courseStructure() : SizedBox.shrink();
                },
              ),
              SizedBox(
                height: 20.h,
              ),
              ValueListenableBuilder<bool>(
                  valueListenable: showCourseStructure,
                  builder: (context, shouldShow, _) {
                    return shouldShow ? _pricing('course') : _pricing('class');
                  }),
              SizedBox(height: 50.h),
              _nextButton(),
              SizedBox(height: 10.h),
            ],
          ),
        ),
      ),
    ));
  }

  Widget _addressRow() {
    return Padding(
      padding: EdgeInsets.only(right: 24.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          customtext(
              context: context,
              newYear: "On-site address:",
              font: 15.sp,
              weight: FontWeight.w500),
          SizedBox(width: 10.w),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customtext(
                    context: context,
                    newYear: "ABC Testing Centre",
                    font: 15.sp,
                    weight: FontWeight.w500),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      change = !change;
                    });
                  },
                  child: customtext(
                      context: context,
                      newYear: "change",
                      font: 15.sp,
                      weight: FontWeight.w500,
                      color: AppPallete.change),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _addressInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(right: 24.w),
          child: AuthField(controller: addressController, height: 30.h),
        ),
        SizedBox(height: 20.h),
      ],
    );
  }

  Widget _numberOfStudentsInput() {
    return Row(
      children: [
        AuthField(
          controller: numberOfStudentController,
          keyboard: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          width: 184.w,
          height: 30.h,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter student number';
            }
            return null;
          },
        ),
        SizedBox(width: 7.w),
        customtext(
            context: context,
            newYear: "students",
            font: 15.sp,
            weight: FontWeight.w400),
      ],
    );
  }

  // New widget for minimum students input
  Widget _minimumStudentsInput() {
    return Row(
      children: [
        AuthField(
          controller: minimumStudentController,
          keyboard: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          width: 184.w,
          height: 30.h,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter minimum student number';
            }
            final minStudents = int.tryParse(value);
            final maxStudents = int.tryParse(numberOfStudentController.text);
            if (minStudents != null &&
                maxStudents != null &&
                minStudents > maxStudents) {
              return 'Minimum cannot exceed maximum';
            }
            return null;
          },
        ),
        SizedBox(width: 7.w),
        customtext(
            context: context,
            newYear: "students minimum",
            font: 15.sp,
            weight: FontWeight.w400),
      ],
    );
  }

  Widget _addLanguage() {
    return LanguageSelector(
      selectedLanguages: selectedLanguages,
      onChanged: (languages) {
        setState(() {
          selectedLanguages = languages;
        });
      },
    );
  }

  Widget _courseStructure() {
    print('class ${classController.text}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
            context: context,
            title: "Course Structure",
            font: 20.sp,
            weight: FontWeight.w600),
        SizedBox(height: 20.h),
        customtext(
            context: context,
            newYear: "All classes must be purchased together",
            font: 15.sp,
            weight: FontWeight.w500),
        SizedBox(height: 20.h),
        customtext(
            context: context,
            newYear:
                "(e.g. program of 5 classes, students must purchase all classes in advance. If joining mid-course (first class ended), the remaining 3 classes must be purchased together as well)",
            font: 12.sp,
            weight: FontWeight.w300,
            color: AppPallete.color34),
        SizedBox(height: 20.h),
        customtext(
            context: context,
            newYear: "Join in setting",
            font: 20.sp,
            weight: FontWeight.w600),
        SizedBox(height: 20.h),
        _joining(),
      ],
    );
  }

  Widget _joining() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            customRequiredText(
                context: context,
                title: "Newcomers can join in anytime",
                font: 15.sp,
                weight: FontWeight.w500),
            customtext(
                context: context,
                newYear: "(Not from the initial class)",
                font: 15.sp,
                weight: FontWeight.w500,
                color: Colors.red)
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ElevatedButton(
              onPressed: () => _joinNew('Yes'),
              style: ElevatedButton.styleFrom(
                foregroundColor: joinNew == true ? Colors.white : Colors.black,
                backgroundColor: joinNew == true
                    ? AppPallete.secondaryColor
                    : AppPallete.paleGrey,
              ),
              child: Text('Yes'),
            ),
            SizedBox(width: 10.w), // Spacing between buttons
            ElevatedButton(
              onPressed: () => _joinNew('No'),
              style: ElevatedButton.styleFrom(
                foregroundColor: joinNew == false ? Colors.white : Colors.black,
                backgroundColor: joinNew == false
                    ? AppPallete.secondaryColor
                    : AppPallete.paleGrey,
              ),
              child: Text('No'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _daymonthplus() {
    //_selectedDay.add(DateTime.now()); // Make sure this logic is intended

    return ListView.separated(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: (_selectedDay.length + 1), // +1 for the add button
      separatorBuilder: (context, index) {
        return SizedBox(height: 20.h);
      },
      itemBuilder: (context, index) {
        if (index < _selectedDay.length) {
          final selectedDay = _selectedDay[index];

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Container(
                      height: 30.h,
                      padding: EdgeInsets.all(5.w),
                      decoration: BoxDecoration(
                        color: AppPallete.paleGrey,
                        borderRadius: BorderRadius.circular(5.r),
                      ),
                      child: Center(
                        child: customtext(
                          context: context,
                          newYear: selectedDay['date'],
                          font: 15.sp,
                          weight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 11.w),
                  customtext(
                    context: context,
                    newYear: "(${selectedDay['weekDay']})",
                    font: 15.sp,
                    weight: FontWeight.w400,
                  ),
                  SizedBox(width: 11.w),
                  Expanded(
                      child: Container(
                    width: 74.w,
                    height: 30.h,
                    decoration: BoxDecoration(
                        color: AppPallete.paleGrey,
                        borderRadius: BorderRadius.circular(5.r)),
                    child: Center(
                      child: customtext(
                          context: context,
                          newYear: selectedDay['startTime'],
                          font: 15.sp,
                          weight: FontWeight.w400),
                    ),
                  )),
                  SizedBox(width: 11.w),
                  Expanded(
                      child: Container(
                    width: 74.w,
                    height: 30.h,
                    decoration: BoxDecoration(
                        color: AppPallete.paleGrey,
                        borderRadius: BorderRadius.circular(5.r)),
                    child: Center(
                      child: customtext(
                          context: context,
                          newYear: selectedDay['endTime'],
                          font: 15.sp,
                          weight: FontWeight.w400),
                    ),
                  )),
                  SizedBox(width: 11.w),
                  customtext(
                    context: context,
                    newYear: selectedDay['durationMinutes'],
                    font: 15.sp,
                    weight: FontWeight.w400,
                  ),
                  SizedBox(width: 8.w),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        // Parse date string back to DateTime
                        try {
                          _tempDate = DateFormat('dd/MM/yy')
                              .parse(selectedDay['date'] ?? '');
                        } catch (_) {
                          _tempDate = null;
                        }
                        _tempDay = selectedDay['weekDay'];
                        fromController.text = selectedDay['startTime'] ?? '';
                        toController.text = selectedDay['endTime'] ?? '';
                        _durationText = selectedDay['durationMinutes'] ?? '';
                        repeatController.text = selectedDay['repeat'] ?? '';
                        _editingIndex = index;
                      });
                    },
                    child: Container(
                      width: 21.w,
                      height: 21.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(99.r),
                        color: Colors.blue,
                      ),
                      child: Center(
                        child: Icon(
                          Icons.edit,
                          color: Colors.white,
                          size: 12.h,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                      onTap: () {
                        _removeDateEntry(index);
                      },
                      child: Container(
                        width: 21.w,
                        height: 21.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(99.r),
                          color: Colors.red,
                        ),
                        child: Center(
                          child: Icon(
                            Icons.remove,
                            color: Colors.white,
                            size: 12.h,
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              ),
              SizedBox(
                height: 20.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: 229.w,
                    height: 30.h,
                    decoration: BoxDecoration(
                        color: AppPallete.paleGrey,
                        borderRadius: BorderRadius.circular(5.r)),
                    child: Center(
                      child: customtext(
                          context: context,
                          newYear: selectedDay['repeat'],
                          font: 15.sp,
                          weight: FontWeight.w400),
                    ),
                  ),
                ],
              ),
            ],
          );
        } else if (index == _selectedDay.length) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        _openCustomCalendar(context);
                      },
                      child: Container(
                        height: 30.h,
                        padding: EdgeInsets.all(5.w),
                        decoration: BoxDecoration(
                          color: AppPallete.paleGrey,
                          borderRadius: BorderRadius.circular(5.r),
                        ),
                        child: Center(
                          child: customtext(
                            context: context,
                            newYear: _tempDate == null
                                ? "select date"
                                : DateFormat('dd/MM/yy').format(_tempDate!),
                            font: 15.sp,
                            weight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 11.w),
                  _tempDay == null
                      ? SizedBox.shrink()
                      : customtext(
                          context: context,
                          newYear: "(${_tempDay})",
                          font: 15.sp,
                          weight: FontWeight.w400,
                        ),
                  SizedBox(width: 11.w),
                  Expanded(
                    child: DropDown(
                      label: "From",
                      width: 74.w,
                      times: times,
                      color: AppPallete.paleGrey,
                      controller: fromController,
                      onChanged: (value) {
                        _tempDate == null
                            ? null
                            : _updateDateTimeEntry(
                                date: _tempDate.toString(),
                                startTime: fromController.text,
                                endTime: toController.text,
                              );
                        // Reset toController if its value is now invalid
                        int fromIndex = times.indexOf(fromController.text);
                        int toIndex = timesTo.indexOf(toController.text);
                        if (toIndex <= fromIndex) {
                          toController.text = '';
                        }
                        setState(() {}); // Force rebuild to update To dropdown
                      },
                    ),
                  ),
                  SizedBox(width: 11.w),
                  Expanded(
                    child: DropDown(
                      label: "To",
                      width: 74.w,
                      times: () {
                        int fromIndex = times.indexOf(fromController.text);
                        if (fromIndex == -1) return timesTo;
                        return timesTo.sublist(fromIndex + 1);
                      }(),
                      color: AppPallete.paleGrey,
                      controller: toController,
                      onChanged: (value) {
                        _tempDate == null
                            ? null
                            : _updateDateTimeEntry(
                                date: _tempDate.toString(),
                                startTime: fromController.text,
                                endTime: toController.text,
                              );
                      },
                    ),
                  ),
                  SizedBox(width: 11.w),
                  _durationText != null
                      ? customtext(
                          context: context,
                          newYear: _durationText!,
                          font: 15.sp,
                          weight: FontWeight.w400,
                        )
                      : customtext(
                          context: context,
                          newYear: "",
                          font: 15.sp,
                        ),
                ],
              ),
              SizedBox(
                height: 20.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  DropDown(
                    label: "Select Repeat",
                    times: repeat,
                    width: 229.w,
                    color: AppPallete.paleGrey,
                    controller: repeatController,
                  ),
                  GestureDetector(
                    onTap: () {
                      int numberOfClass =
                          int.tryParse(classController.text) ?? 0;
                      if (_editingIndex == null &&
                          _selectedDay.length >= numberOfClass &&
                          numberOfClass > 0) {
                        errorState(
                            context: context,
                            error:
                                "You cannot add more dates than the number of classes ($numberOfClass).");
                        return;
                      }
                      if (index == _selectedDay.length &&
                          _tempDate != null &&
                          fromController.text.isNotEmpty &&
                          toController.text.isNotEmpty &&
                          repeatController.text.isNotEmpty) {
                        setState(() {
                          final newEntry = {
                            'date': DateFormat('dd/MM/yy')
                                .format(_tempDate!)
                                .toString(),
                            'weekDay': _tempDay,
                            'startTime': fromController.text,
                            'endTime': toController.text,
                            'durationMinutes': _durationText,
                            'repeat': repeatController.text
                          };
                          if (_editingIndex != null) {
                            _selectedDay[_editingIndex!] = newEntry;
                            _editingIndex = null;
                          } else {
                            _selectedDay.add(newEntry);
                          }
                          _sortSelectedDaysByDate(_selectedDay);

                          _tempDate = null;
                          _tempDay = null;
                          _durationText = null;
                          fromController.text = "";
                          toController.text = "";
                          repeatController.text = "";
                        });
                      } else {
                        errorState(
                            context: context,
                            error: "Please enter all the field");
                      }
                    },
                    child: Container(
                      width: 21.w,
                      height: 21.w,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(99.r),
                          color: AppPallete.secondaryColor),
                      child: Center(
                        child: Icon(
                          Icons.add,
                          color: Colors.white,
                          size: 12.h,
                        ),
                      ),
                    ),
                  )
                ],
              ),
              SizedBox(
                height: 10.h,
              ),
            ],
          );
        } else {
          return SizedBox.shrink(); // Optional: this case should not occur
        }
      },
    );
  }

  Widget _pricing(String type) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
          context: context,
          title: "Pricing",
          font: 20.sp,
          weight: FontWeight.w600,
        ),
        SizedBox(
          height: 20.h,
        ),
        customtext(
            context: context,
            newYear: "Set your pricing per $type in HKD",
            font: 15.sp,
            weight: FontWeight.w400),
        SizedBox(
          height: 15.h,
        ),
        Row(
          children: [
            AuthField(
              controller: hkdAmountController,
              keyboard: TextInputType.number,
              width: 150.w,
              height: 30.h,
              hintText: "HKD",
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter HKD amount';
                }
                final double? hkd = double.tryParse(value);
                if (hkd == null || hkd <= 0) {
                  return 'Enter a valid HKD amount';
                }
                return null;
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _nextButton() {
    return Center(
      child: Button(
        onPressed: () {
          if (_formKey.currentState!.validate()) {
            if (_selectedDay.length == 0) {
              errorState(
                  context: context,
                  error: 'Please select date and press plus button');
            } else if (selectedLanguages.length == 0) {
              errorState(context: context, error: 'Please select languages');
            } else {
              // Handle validation for multiple classes
              int numberOfClasses = int.parse(classController.text);
              if (numberOfClasses > 1) {
                // Check if any date has "Does not Repeat"
                bool hasNonRepeating = _selectedDay
                    .any((day) => day['repeat'] == 'Does not Repeat');
                int numberOfDates = _selectedDay.length;

                if (hasNonRepeating && numberOfDates < numberOfClasses) {
                  errorState(
                      context: context,
                      error:
                          'For $numberOfClasses classes with "Does not Repeat", you need $numberOfClasses different dates. Currently you have $numberOfDates date(s). Please add more dates or select a repeating schedule.');
                  return; // Exit early if validation fails
                }
              }

              // Continue with the navigation logic for all valid cases
              int numberOfStudent = int.parse(numberOfStudentController.text);
              int minimumStudent = int.parse(minimumStudentController.text);
              int numberOfClass = int.parse(classController.text);
              String address = addressController.text;
              if (address.isEmpty) address = "center";

              // Use HKD directly for charge
              final double hkdAmount =
                  double.tryParse(hkdAmountController.text) ?? 0.0;
              final String charge = hkdAmount.toString();

              Map<String, dynamic> payload = {
                "numberOfStudent": numberOfStudent,
                "minimumStudent": minimumStudent,
                "numberOfClass": numberOfClass,
                "address": address,
                "languageOptions": selectedLanguages,
                "dates": _selectedDay,
                "buyAll": true,
                "course": course,
                "newComer": joinNew,
                "joinNew": joinNew,
                "coach": widget.coachId,
                "name": widget.className,
                "charge": charge, // Now using HKD amount
                "coachName": widget.coachName,
                "class": widget.classId
              };
              NavigatorService.pushNamed(AppRoutes.centerSlotConfirmation,
                  arguments: payload);
            }
          }
        },
        buttonText: "next",
        color: AppPallete.secondaryColor,
        width: 289.w,
        height: 49.h,
      ),
    );
  }
}

void _sortSelectedDaysByDate(List<Map<String, dynamic>> list) {
  list.sort((a, b) {
    try {
      final aDate = DateFormat('dd/MM/yy').parse(a['date'] ?? '', true);
      final bDate = DateFormat('dd/MM/yy').parse(b['date'] ?? '', true);
      return aDate.compareTo(bDate);
    } catch (_) {
      return 0;
    }
  });
}
