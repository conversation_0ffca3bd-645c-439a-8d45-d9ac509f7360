import 'package:class_z/core/imports.dart';

class CenterCoachRequest extends StatefulWidget {
  const CenterCoachRequest({super.key});

  @override
  State<CenterCoachRequest> createState() => _CenterCoachRequestState();
}

class _CenterCoachRequestState extends State<CenterCoachRequest> {
  @override
  void initState() {
    context.read<RequestBloc>().add(GetRequestsByCenterEvent(
        centerId: locator<SharedRepository>().getCenterId()));
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBarDouble(
        title: titleGenerateWithAddress(),
        title2: "Centre Request",
        leading: customBackButton(),
      ),
      body: SingleChildScrollView(
        child: BlocConsumer<RequestBloc, RequestState>(
          listener: (context, state) {
            if (state is RequestLoading)
              loadingState(context: context);
            else
              hideLoadingDialog(context);
            if (state is RequestError)
              errorState(context: context, error: state.message);
            if (state is RequestStatusUpdatedSuccessState) {
              if (state.status == true) {
                errorState(context: context, error: 'Updated');
              }
            }
          },
          builder: (context, state) {
            if (state is RequestSuccessState) {
              if (state.requests?.length == 0) {
                return SizedBox(
                  height: getHeight(context: context) / 2,
                  child: Center(
                    child: Text('No request found'),
                  ),
                );
              }
              return ListView.builder(
                shrinkWrap: true,
                padding: EdgeInsets.only(top: 23),
                physics: const NeverScrollableScrollPhysics(),
                itemCount: state.requests?.length,
                itemBuilder: (context, index) {
                  final request = state.requests?[index];
                  return centerCoachRequestCard(
                      context: context,
                      requestId: request?.id ?? '',
                      imagePath: imageStringGenerator(
                          imagePath: request?.coachId?.mainImage?.url ?? ''),
                      coach: request?.coachId?.displayName ?? 'Unknown Name',
                      location: addressGenerator(
                          address: request?.coachId?.address,
                          condition: 'city'),
                      id: request?.coachId?.classZId ?? '',
                      rating: request?.coachId?.rating ?? 0);
                },
              );
            }
            return SizedBox(
              height: getHeight(context: context) / 2,
              child: Center(
                child: Text('No request found'),
              ),
            );
          },
        ),
      ),
    );
  }
}
