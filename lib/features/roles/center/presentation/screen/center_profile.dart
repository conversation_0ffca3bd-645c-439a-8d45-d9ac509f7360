import 'package:class_z/core/imports.dart';

class CenterProfile extends StatefulWidget {
  final CenterData centerModel;
  const CenterProfile({required this.centerModel, super.key});

  @override
  State<CenterProfile> createState() => _CenterProfileState();
}

class _CenterProfileState extends State<CenterProfile> {
  late CenterData currentCenterModel;

  @override
  void initState() {
    super.initState();
    currentCenterModel = widget.centerModel;
  }

  void _refreshCenterData() {
    print(
        "🔄 _refreshCenterData called for center ID: ${currentCenterModel.id}");
    // Fetch updated center data
    if (currentCenterModel.id != null) {
      print(
          "📡 Dispatching GetCenterDataEvent for ID: ${currentCenterModel.id}");
      context
          .read<CenterBloc>()
          .add(GetCenterDataEvent(id: currentCenterModel.id!));
    } else {
      print("❌ Cannot refresh - center ID is null");
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CenterBloc, CenterState>(
      listener: (context, state) {
        print(
            "🔄 Center Profile BlocListener received state: ${state.runtimeType}");

        if (state is CenterInfoSuccess) {
          print("📦 Fresh center data received:");
          print("   - Center ID: ${state.center.id}");
          print("   - Image URL: ${state.center.mainImage?.url}");
          print(
              "   - Generated URL: ${imageStringGenerator(imagePath: state.center.mainImage?.url ?? '')}");

          // Update the current center model with fresh data
          setState(() {
            currentCenterModel = state.center;
          });
          print("✅ Center profile UI updated with new data");
        }
      },
      child: Scaffold(
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomAppBar(
              title: 'Center Profile',
              leading: customBackButton(),
            ),
            SizedBox(
              height: 35.h,
            ),
            Padding(
              padding: EdgeInsets.only(top: 28.h, left: 37.w, right: 38.w),
              child: _profile(context: context),
            )
          ],
        ),
      ),
    );
  }

  Widget _profile({required BuildContext context}) {
    String date = "N/A";
    try {
      debugPrint('createdAt: ${currentCenterModel.createdAt}');
      debugPrint('updatedAt: ${currentCenterModel.updatedAt}');

      if (currentCenterModel.createdAt != null) {
        date = DateFormat('dd/MM/yyyy').format(currentCenterModel.createdAt!);
        debugPrint('Using createdAt: $date');
      } else if (currentCenterModel.updatedAt != null) {
        // Fallback to updatedAt if createdAt is not available
        date =
            'Since ${DateFormat('dd/MM/yyyy').format(currentCenterModel.updatedAt!)}';
        debugPrint('Using updatedAt: $date');
      } else {
        debugPrint('Both createdAt and updatedAt are null');
      }
    } catch (e, stackTrace) {
      debugPrint('Error formatting date: $e');
      debugPrint('Stack trace: $stackTrace');
    }
    return Container(
      // height: 445.h,
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            shadow(blurRadius: 15, opacity: 0.1, xoffset: 0, yoffset: 0),
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 150.h,
            child: Stack(
              children: [
                Positioned(
                    top: 22.h,
                    right: 19.w,
                    child: GestureDetector(
                      onTap: () async {
                        print("🚀 Navigating to center profile edit...");
                        final result = await NavigatorService.pushNamed(
                          AppRoutes.centerProfileEdit,
                          arguments: {
                            'imagePath': ImagePath.school,
                            'centerId': currentCenterModel.id ?? '',
                            'verified': currentCenterModel.verified ?? false,
                          },
                        );

                        print("🔙 Returned from edit with result: $result");

                        // Refresh center data if edit was successful
                        if (result == true) {
                          print(
                              "✅ Edit was successful, refreshing center data...");
                          _refreshCenterData();
                        } else {
                          print(
                              "ℹ️ Edit was cancelled or unsuccessful, no refresh needed");
                        }
                      },
                      child: Text(
                        "edit",
                        style: TextStyle(
                            color: AppPallete.change,
                            fontSize: 17.sp,
                            decoration: TextDecoration.underline,
                            fontWeight: FontWeight.w400),
                      ),
                    )),
                Positioned(
                    top: 22.h,
                    left: 0.w,
                    right: 0,
                    child: Align(
                      alignment: Alignment.center,
                      child: CustomImageBuilder(
                          imagePath: imageStringGenerator(
                              imagePath:
                                  currentCenterModel.mainImage?.url ?? ''),
                          height: 125.h,
                          width: 125.w,
                          borderRadius: 99.r),
                    )),
              ],
            ),
          ),
          SizedBox(
            height: 31.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 14.w, right: 17.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customtext(
                    context: context,
                    newYear: "Center Information",
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.darkGrey),
                customtext(
                    context: context,
                    newYear: currentCenterModel.classZId ?? '',
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.darkGrey)
              ],
            ),
          ),
          SizedBox(
            height: 26.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 14.w, right: 17.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customtext(
                    context: context,
                    newYear: currentCenterModel.displayName ?? "",
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.darkGrey),
                customtext(
                    context: context,
                    newYear: currentCenterModel.isFreelanceEducator == true
                        ? "Individual Educator"
                        : currentCenterModel.companyNumber ?? "",
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.darkGrey)
              ],
            ),
          ),
          SizedBox(
            height: 26.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 14.w, right: 17.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customtext(
                    context: context,
                    newYear: currentCenterModel.email ?? "",
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.darkGrey),
                customtext(
                    context: context,
                    newYear: currentCenterModel.centerNumber ?? "",
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.darkGrey)
              ],
            ),
          ),
          SizedBox(
            height: 26.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 14.w, right: 17.w),
            child: SizedBox(
              height: 75.h,
              child: customtext(
                  context: context,
                  newYear:
                      "${currentCenterModel.address!.address1}/${currentCenterModel.address!.city}/${currentCenterModel.address!.region}",
                  font: 15.sp,
                  weight: FontWeight.w500,
                  color: AppPallete.darkGrey),
            ),
          ),
          SizedBox(
            height: 26.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 14.w, right: 17.w),
            child: customtext(
                context: context,
                newYear: "Joined Since $date",
                font: 15.sp,
                weight: FontWeight.w500,
                color: AppPallete.darkGrey),
          ),
        ],
      ),
    );
  }
}
