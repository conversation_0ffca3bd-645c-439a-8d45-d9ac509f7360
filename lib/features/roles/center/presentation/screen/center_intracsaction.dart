import 'package:class_z/core/imports.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart'; // Commented out - rely on core/imports.dart or user setup
import 'package:class_z/features/transactions/data/models/daily_transaction_model.dart';

class CenterIntracsaction extends StatefulWidget {
  final DailyTransactionModel transactionDetail;

  const CenterIntracsaction({super.key, required this.transactionDetail});

  @override
  State<CenterIntracsaction> createState() => _CenterIntracsactionState();
}

class _CenterIntracsactionState extends State<CenterIntracsaction> {
  // Add currency consistency state
  bool _shouldShowZcoin = true;

  @override
  void initState() {
    super.initState();
    _checkCurrencyConsistency();
  }

  // Add method to check currency consistency
  void _checkCurrencyConsistency() {
    // CURRENCY CONSISTENCY: For centers, always show Zcoin for consistency
    // Centers should display earnings in Zcoin to maintain consistency with the app's currency system
    setState(() {
      _shouldShowZcoin = true; // Centers always display in Zcoin
    });
  }

  // Helper method to get proper coach display name
  String _getCoachDisplayName(String coachDisplayName) {
    if (coachDisplayName.isEmpty ||
        coachDisplayName == 'Unknown Coach' ||
        coachDisplayName == 'N/A' ||
        coachDisplayName == 'null') {
      return 'Coach'; // Just show "Coach" instead of "Unknown Coach"
    }
    return coachDisplayName;
  }

  @override
  Widget build(BuildContext context) {
    String rateDisplay = widget.transactionDetail.amount.toStringAsFixed(2);
    // String studentPerClassDisplay = 'HKD ${widget.transactionDetail.amount.toStringAsFixed(2)}'; // Removed as unused here, defined in _intracsactionCard as studentPerClassInfo

    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomAppBar(
              title: 'Transaction',
              subtitle: 'Transaction details',
              leading: customBackButton(),
            ),
            Padding(
              padding: EdgeInsets.only(top: 34.h, right: 25.w, left: 25.w),
              child: _intracsactionCard(
                  context: context,
                  transactionDetail: widget.transactionDetail),
            ),
            SizedBox(
              height: 24.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 16.w),
              child: customtext(
                  context: context,
                  newYear: 'Bill breakdown',
                  font: 20.sp,
                  weight: FontWeight.w600),
            ),
            SizedBox(
              height: 18.h,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 22.w),
              child: CustomBill(
                rows: [
                  BillRow.fromCells(cells: [
                    DataCell(customtext(
                        context: context,
                        newYear: 'Total Transaction Amount',
                        font: 15.sp,
                        weight: FontWeight.w500)),
                    DataCell(customtext(
                        context: context,
                        newYear: _shouldShowZcoin
                            ? '${widget.transactionDetail.amount.toStringAsFixed(0)} Zcoin'
                            : 'HKD $rateDisplay',
                        font: 15.sp,
                        weight: FontWeight.w500))
                  ])
                ],
                subtotal: widget.transactionDetail.amount.toInt(),
                discount: 0,
                zcoin: _shouldShowZcoin,
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _intracsactionCard(
      {required BuildContext context,
      required DailyTransactionModel transactionDetail}) {
    bool transactionComplete = transactionDetail.status == 'Completed';
    String formattedDate = DateFormat('dd MMM, HH:mm')
        .format(transactionDetail.createdAt.toLocal());
    String displayRate = _shouldShowZcoin
        ? '${transactionDetail.amount.toStringAsFixed(0)} Zcoin'
        : 'HKD ${transactionDetail.amount.toStringAsFixed(2)}';
    String studentPerClassInfo = _shouldShowZcoin
        ? '${transactionDetail.amount.toStringAsFixed(0)} Zcoin'
        : 'HKD ${transactionDetail.amount.toStringAsFixed(2)}';

    return Container(
      width: 380.w,
      decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [shadow(blurRadius: 15)],
          borderRadius: BorderRadius.circular(20.w)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 47.h,
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 18.w),
            decoration: BoxDecoration(
                color: AppPallete.secondaryColor,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.w),
                    topRight: Radius.circular(20.w))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customtext(
                    context: context,
                    newYear: transactionDetail.studentName,
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: Colors.white),
                customtext(
                    context: context,
                    newYear: transactionComplete
                        ? 'Payment completed'
                        : 'Payment pending',
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: transactionComplete
                        ? AppPallete.color34
                        : Colors.orange)
              ],
            ),
          ),
          SizedBox(
            height: 16.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.w, right: 11.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customtext(
                    context: context,
                    newYear: formattedDate,
                    font: 12.sp,
                    weight: FontWeight.w500),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 12.sp,
                    ),
                    SizedBox(
                      width: 8.33.w,
                    ),
                    customtext(
                        context: context,
                        newYear: transactionDetail.location,
                        font: 15.sp,
                        weight: FontWeight.w400,
                        color: AppPallete.wordsOfRequest)
                  ],
                )
              ],
            ),
          ),
          SizedBox(
            height: 12.h,
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: customtext(
                context: context,
                newYear: transactionDetail.className,
                font: 25.sp,
                color: AppPallete.color34,
                weight: FontWeight.w600),
          ),
          SizedBox(
            height: 10.h,
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: customtext(
                context: context,
                newYear: displayRate,
                font: 15.sp,
                color: AppPallete.color34,
                weight: FontWeight.w600),
          ),
          SizedBox(
            height: 10.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.w),
            child: Row(
              children: [],
            ),
          ),
          SizedBox(
            height: 5.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.w),
            child: customtext(
                context: context,
                newYear:
                    'by ${_getCoachDisplayName(transactionDetail.coachDisplayName)}',
                font: 10.sp,
                weight: FontWeight.w400),
          ),
          SizedBox(
            height: 14.3.h,
          ),
          customDivider(width: 343.w, padding: 18.w),
          SizedBox(
            height: 20.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.w),
            child: customtext(
                context: context,
                newYear: 'Charges',
                font: 15.sp,
                weight: FontWeight.w500),
          ),
          SizedBox(
            height: 15.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.w),
            child: customtext(
                context: context,
                newYear: 'Student per class: $studentPerClassInfo',
                font: 12.sp,
                weight: FontWeight.w400),
          ),
          SizedBox(
            height: 15.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.w),
            child: customtext(
                context: context,
                newYear: transactionDetail.isSen
                    ? 'Special note: SEN support included'
                    : 'Special note: SEN support not included',
                font: 12.sp,
                weight: FontWeight.w400),
          ),
          SizedBox(
            height: 20.h,
          ),
          customDivider(width: 343.w, padding: 18.w),
          SizedBox(
            height: 20.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.w),
            child: customtext(
                context: context,
                newYear: 'You acknowledged that',
                font: 15.sp,
                weight: FontWeight.w500),
          ),
          SizedBox(
            height: 10.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.w, right: 16.w),
            child: customtext(
                context: context,
                newYear:
                    '1. You have responsibility to coach the class on time with quality service',
                font: 12.sp,
                weight: FontWeight.w400),
          ),
          SizedBox(
            height: 5.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.w, right: 16.w),
            child: customtext(
                context: context,
                newYear: '2. Missed class may be subject to a service charge',
                font: 12.sp,
                weight: FontWeight.w400),
          ),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }
}
