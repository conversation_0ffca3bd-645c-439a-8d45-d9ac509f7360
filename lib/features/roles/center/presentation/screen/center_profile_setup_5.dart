import 'package:class_z/core/imports.dart';
import 'package:class_z/core/utils/print_long_string.dart';

class CenterProfileSetup5 extends StatefulWidget {
  final Map<String, dynamic> data;
  const CenterProfileSetup5({required this.data, super.key});

  @override
  State<CenterProfileSetup5> createState() => _CenterProfileSetup5State();
}

class _CenterProfileSetup5State extends State<CenterProfileSetup5> {
  String? bankName;

  final accountnumberController = TextEditingController();
  final accountHoldernameController = TextEditingController();
  final bankCodeController = TextEditingController();
  final branchCodeController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  @override
  void dispose() {
    accountnumberController.dispose();
    accountHoldernameController.dispose();
    bankCodeController.dispose();
    branchCodeController.dispose();
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        title: "Center Profile",
        leading: customBackButton(),
      ),
      body: BlocListener<CenterBloc, CenterState>(
        listener: (context, state) {
          if (state is CenterLoadingState)
            loadingState(context: context);
          else
            hideLoadingDialog(context);
          if (state is CenterErrorState) {
            errorState(context: context, error: state.message);
          }
          if (state is CreateCenterBranchSuccess) {
            if (state.isSuccess == true) {
              print('done');

              // Show the SuccessDialog with the correct context
              _showSuccessDialog(context);

              //  errorState(context: context, error: 'done'); // This will print after the dialog code, once the showDialog is triggered
            } else {
              errorState(context: context, error: "Something went Wrong");
            }
          }
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 21, vertical: 43),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: CustomImageBuilder(
                      imagePath: widget.data['mainImage']?.path,
                      height: 125.h,
                      width: 125.w,
                      borderRadius: 99.r),
                ),
                const SizedBox(
                  height: 23,
                ),
                customtext(
                    context: context,
                    newYear: "Payout Bank account",
                    font: 17.sp,
                    weight: FontWeight.w500),
                const SizedBox(height: 20),
                customRequiredText(
                    context: context,
                    title: "Bank",
                    font: 15.sp,
                    weight: FontWeight.w400),
                const SizedBox(height: 20),
                BankDetailsSTL(
                  height: 30.h,
                  width: 380.w,
                  label: "Select Bank",
                  onBankSelected: (p0) {
                    bankName = p0;
                  },
                ),
                const SizedBox(
                  height: 27,
                ),
                _textbox(
                    context: context,
                    title: "Account holder’s name",
                    controller: accountHoldernameController),
                const SizedBox(
                  height: 20,
                ),
                _bankAndBranch(context: context),
                const SizedBox(
                  height: 27,
                ),
                _textbox(
                    context: context,
                    title: "Account number",
                    controller: accountnumberController),
                const Spacer(),
                Center(
                  child: Button(
                    buttonText: "create",
                    color: AppPallete.secondaryColor,
                    height: 49.h,
                    width: 289.w,
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        widget.data.addAll({
                          "bankDetails": {
                            "bankName": bankName,
                            "accountHolderName":
                                accountHoldernameController.text,
                            "bankCode": bankCodeController.text,
                            "branchCode": branchCodeController.text,
                            "accountNumber": accountnumberController.text,
                          }
                        });

                        printLongString(widget.data.toString());
                        context
                            .read<CenterBloc>()
                            .add(CreateCenterBranchEvent(data: widget.data));
                      }
                    },
                  ),
                ),
                const SizedBox(
                  height: 5,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _textbox(
      {required BuildContext context,
      required String title,
      double? width,
      double? height,
      required TextEditingController controller}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
            context: context,
            title: title,
            font: 15.sp,
            weight: FontWeight.w400),
        const SizedBox(
          height: 20,
        ),
        AuthField(
          controller: controller,
          height: height ?? 30.h,
          width: width ?? 387.w,
          color: AppPallete.paleGrey,
          validator: (value) =>
              value == null || value.isEmpty ? "Value can't be empty" : null,
        ),
      ],
    );
  }

  Widget _bankAndBranch({required BuildContext context}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _textbox(
            context: context,
            title: "Bank Code",
            controller: bankCodeController,
            width: 119.30.w),
        _textbox(
            context: context,
            title: "Branch Code",
            controller: branchCodeController,
            width: 253.w),
      ],
    );
  }

  void _showSuccessDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Success'),
          content: Column(
            mainAxisSize:
                MainAxisSize.min, // Ensure the dialog takes up minimum space
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              customtext(
                context: context,
                newYear: "We have received your application!",
                font: 16.sp,
                weight: FontWeight.w600,
              ),
              customtext(
                context: context,
                newYear: 'It takes 3-5 working days for us to review it',
                font: 14.sp,
                weight: FontWeight.w300,
                color: AppPallete.greyColor,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context); // Close the dialog
                NavigatorService.removePagesAndNavigate(AppRoutes.ownerMain, 4);
              },
              child: Text('OK'),
            ),
          ],
        );
      },
    );
  }
}
