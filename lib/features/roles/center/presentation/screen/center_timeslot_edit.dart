import 'package:class_z/core/imports.dart';

class CenterTimeSlotEdit extends StatefulWidget {
  const CenterTimeSlotEdit({super.key});

  @override
  State<CenterTimeSlotEdit> createState() => _CenterTimeSlotEditState();
}

class _CenterTimeSlotEditState extends State<CenterTimeSlotEdit> {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  late ValueNotifier<List<EventModel>> _selectedEvents;
  List<EventModel> events = [];
  List<String>? eventDates = [];
  String leftText = "Add Schedule";
  String bottomText = "Private Class Request(99+)";
  String centerId = '';
  @override
  void initState() {
    super.initState();
    _selectedEvents = ValueNotifier<List<EventModel>>([]);
    eventDates = context.read<CenterBloc>().centerEventDates;
    if (eventDates == null || eventDates?.length == 0) {
      _fetchData();
    }
  }

  void _fetchData() {
    final sharedPref = locator<SharedRepository>();
    centerId = sharedPref.getCenterId();
    //  _selectedEvents = ValueNotifier<List<EventModel>>([]);

    // // Trigger initial event fetching
    context.read<CenterBloc>().add(GetEventDatesEvent(
        filterType: 'centerId',
        filterValue: centerId)); // Ensure centerId is provided
  }

  void _onselectedDay(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      String date = dateGenerator(
          date: selectedDay, monthName: false, format: 'yyyy-MM-dd');
      print(date);
      context.read<CenterBloc>().add(GetAllEventsEvent(
          filterType: 'centerId', filterValue: centerId, date: date));
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
      _selectedEvents.value =
          _getEventsForDay(selectedDay); // Update the event list
    });
  }

  // Function to get events for a given day
  List<EventModel> _getEventsForDay(DateTime dateTime) {
    return events
        .where((event) =>
            event.date?.year == dateTime.year &&
            event.date?.month == dateTime.month &&
            event.date?.day == dateTime.day)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBarDouble(
        title: "My Slot",
        title2: "Centre upcoming schedule",
        leading: customBackButton(),
      ),
      body: BlocListener<CenterBloc, CenterState>(
        listener: (context, state) {
          if (state is CenterLoadingState)
            loadingState(context: context);
          else
            hideLoadingDialog(context);
          if (state is CenterErrorState)
            errorState(context: context, error: state.message);
          if (state is EventListFetchSuccess) {
            setState(() {
              events = state.events;
              _selectedEvents.value =
                  _getEventsForDay(_selectedDay ?? _focusedDay);
            });
          }
          if (state is EventDatesFetchSuccess) {
            setState(() {
              eventDates = state.eventDates;
            });
          }
        },
        child: RefreshIndicator(
          onRefresh: () async {
            _fetchData();
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                SizedBox(height: 18.h),
                _buildMonthNavigator(),
                SizedBox(height: 18.h),
                CustomCalendar(
                  focusedDay: _focusedDay,
                  selectedDay: _selectedDay,
                  onDaySelected: _onselectedDay,
                  eventDates: eventDates,
                  onPageChanged: (focusedDay) {
                    setState(() {
                      _focusedDay = focusedDay;
                    });
                  },
                ),
                SizedBox(height: 8.h),
                customDivider(width: 406.w, padding: 12.w),
                const SizedBox(height: 19),
                ValueListenableBuilder<List<EventModel>>(
                  valueListenable: _selectedEvents,
                  builder: (context, value, _) {
                    if (value.isEmpty) {
                      return Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Text("No events available for this day."),
                      );
                    }
                    return _buildEventList(context, value);
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMonthNavigator() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: const Icon(Icons.chevron_left),
            onPressed: () {
              setState(() {
                _focusedDay =
                    DateTime(_focusedDay.year, _focusedDay.month - 1, 1);
              });
            },
          ),
          Text(
            DateFormat.yMMMM().format(_focusedDay),
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.chevron_right),
            onPressed: () {
              setState(() {
                _focusedDay =
                    DateTime(_focusedDay.year, _focusedDay.month + 1, 1);
              });
            },
          ),
        ],
      ),
    );
  }

  // Helper method to build the header with Add Schedule button
  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.only(top: 18.h, left: 19.w, right: 29.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              NavigatorService.pushNamed(AppRoutes.centerAddSlot);
            },
            child: customtext(
              context: context,
              newYear: leftText,
              font: 17.sp,
              weight: FontWeight.w400,
              color: AppPallete.change,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build the event list
  Widget _buildEventList(BuildContext context, List<EventModel> events) {
    // Filter out events with null or invalid class data to prevent "null-null" display
    final validEvents = events.where((event) {
      return event.classId != null &&
          event.classId!.id != null &&
          event.classId!.classProviding != null &&
          event.classId!.classProviding!.isNotEmpty;
    }).toList();

    // Deduplicate by event id (only first occurrence kept)
    final seenIds = <String?>{};
    final dedupedEvents = <EventModel>[];
    for (final event in validEvents) {
      if (event.id != null && !seenIds.contains(event.id)) {
        seenIds.add(event.id);
        dedupedEvents.add(event);
      } else if (event.id == null) {
        // If id is null, treat as unique (optional: skip or always add)
        dedupedEvents.add(event);
      }
    }

    // Sort events by startTime (nulls last)
    dedupedEvents.sort((a, b) {
      // Try to parse startTime as HH:mm or h:mm a
      DateTime? parseTime(String? t) {
        if (t == null) return null;
        try {
          // Try 24-hour format first
          final parts = t.split(":");
          if (parts.length == 2 &&
              !t.toLowerCase().contains("am") &&
              !t.toLowerCase().contains("pm")) {
            final now = DateTime.now();
            return DateTime(now.year, now.month, now.day, int.parse(parts[0]),
                int.parse(parts[1]));
          }
          // Try 12-hour format with AM/PM
          final regExp = RegExp(r'^(\d{1,2}):(\d{2})\s*([APap][Mm])');
          final match = regExp.firstMatch(t.replaceAll(" ", ""));
          if (match != null) {
            int hour = int.parse(match.group(1)!);
            int minute = int.parse(match.group(2)!);
            String period = match.group(3)!.toUpperCase();
            if (period == "PM" && hour != 12) hour += 12;
            if (period == "AM" && hour == 12) hour = 0;
            final now = DateTime.now();
            return DateTime(now.year, now.month, now.day, hour, minute);
          }
        } catch (_) {}
        return null;
      }

      final aTime = parseTime(a.startTime);
      final bTime = parseTime(b.startTime);
      if (aTime == null && bTime == null) return 0;
      if (aTime == null) return 1;
      if (bTime == null) return -1;
      return aTime.compareTo(bTime);
    });

    // If no valid events, show empty state
    if (dedupedEvents.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 40.h),
          child: customtext(
            context: context,
            newYear: "No scheduled classes for this date",
            font: 16.sp,
            color: AppPallete.greyWord,
          ),
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.only(left: 0.w),
      child: ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.zero,
        itemCount: dedupedEvents.length,
        itemBuilder: (context, index) {
          final event = dedupedEvents[index];

          // Safe null handling for age group
          String ageGroup = "TBD";
          if (event.classId?.ageFrom != null && event.classId?.ageTo != null) {
            ageGroup = "${event.classId!.ageFrom}-${event.classId!.ageTo}";
          }

          // Safe null handling for language - use schedule-specific first, then fall back to class-level
          String language = "";
          if (event.scheduleLanguageOptions != null &&
              event.scheduleLanguageOptions!.isNotEmpty) {
            language = event.scheduleLanguageOptions!.join(", ");
          } else if (event.classId?.language != null &&
              event.classId!.language!.isNotEmpty) {
            language = event.classId!.language!.join(", ");
          }

          return centreList(
            context: context,
            title: event.classId?.classProviding ?? "Unknown Class",
            time: event.startTime ?? "TBD",
            minute: event.durationMinutes ?? "0",
            imagePath:
                "${AppText.device}${event.classId?.mainImage?.url ?? ''}",
            category: event.classId?.level ?? "",
            name: event.coach?.displayName ?? "TBD",
            location: event.classId?.address ?? "center",
            language: language,
            ageGroup: ageGroup,
            currentStudent: event.dateId?.students.length.toString() ?? "0",
            totalStudent: (event.scheduleNumberOfStudent ??
                    event.classId?.numberOfStudent ??
                    0)
                .toString(),
            cost:
                (event.scheduleCharge ?? event.classId?.charge ?? 0).toString(),
            sen: event.classId?.sen ?? false,
            onTap: () {
              NavigatorService.pushNamed(AppRoutes.centerPressSlot,
                  arguments: event);
            },
          );
        },
      ),
    );
  }
}
