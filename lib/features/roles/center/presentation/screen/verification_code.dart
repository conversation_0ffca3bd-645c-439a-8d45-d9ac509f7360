import 'package:class_z/core/imports.dart';

class VerificationCode extends StatefulWidget {
  final Map<String, dynamic> data;

  const VerificationCode({required this.data, super.key});

  @override
  State<VerificationCode> createState() => _VerificationCodeState();
}

class _VerificationCodeState extends State<VerificationCode> {
  late String classId;
  late int totalStudent;
  late CenterBloc centerBloc;
  String date = "";
  @override
  void initState() {
    classId = widget.data['classId'];
    totalStudent = widget.data['totalStudent'];
    centerBloc = widget.data['bloc'];
    date = dateGenerator(
        date: DateTime.now(), monthName: false, format: 'yyyy-MM-dd');

    centerBloc
        .add(GetPresentAttendanceEvent(classId: classId, classDate: date));
    super.initState();
  }

  TextEditingController codeController = TextEditingController();
  int numberOfstudent = 0;
  List<AttendanceModel> attendances = [];
  String? qrCodeData;
  bool isScanning = false;
  void handleQRCodeScanned(String scannedCode) {
    setState(() {
      qrCodeData =
          scannedCode; // You can also clear the controller or perform any other actions here
    });
    centerBloc
        .add(PostAttendanceEvent(classId: classId, qrCodeData: qrCodeData));
  }

  @override
  Widget build(BuildContext context) {
    print("JIOa");
    print(attendances.length);
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        title: "verification",
        leading: CustomIconButton(
          icon: Icons.arrow_back_ios,
          onPressed: () {
            NavigatorService.goBack();
          },
        ),
      ),
      body: BlocListener<CenterBloc, CenterState>(
        listener: (context, state) {
          if (state is CenterLoadingState)
            loadingState(context: context);
          else
            hideLoadingDialog(context);
          if (state is CenterErrorState) {
            codeController.clear();
            errorState(context: context, error: state.message);
          }
          if (state is AttendancePostSuccess) {
            setState(() {
              // Check if the attendance already exists in the list
              bool isDuplicate = attendances.any((attendance) =>
                  attendance.isVerified?.id == state.attendance.isVerified?.id);

              if (!isDuplicate) {
                attendances.add(state.attendance);
                numberOfstudent++;
              } else {
                errorState(context: context, error: "Already Attendance taken");
              }
              codeController.clear();
            });
          }
          if (state is PresentAttendanceFetchSuccess) {
            setState(() {
              attendances = state.attendance;
              numberOfstudent = attendances.length;
            });
          }
        },
        child: Column(
          children: [
            const Center(
              child: Text(
                "Enter student’s code to start",
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
              ),
            ),
            SizedBox(
              height: 17.h,
            ),
            _group(context: context),
            SizedBox(
              height: 48.h,
            ),
            Padding(
                padding: EdgeInsets.only(left: 35.w, right: 35.w),
                child: CustomPinCodeTextField(
                  context: context,
                  controller: codeController,
                  codeController:
                      codeController, // Ensure the codeController is passed here
                  onChanged: (value) {
                    if (value.length == 4) {
                      codeController.text = value;
                      context.read<CenterBloc>().add(PostAttendanceEvent(
                          classId: classId,
                          code: codeController.text,
                          classDate: date));
                    }
                  },
                )),
            SizedBox(
              height: 106.h,
            ),
            customtext(
                context: context,
                newYear: "or",
                font: 20.sp,
                weight: FontWeight.w500),
            SizedBox(
              height: 106.h,
            ),
            Button(
              buttonText: "Scan QR code",
              color: AppPallete.secondaryColor,
              height: 64.h,
              width: 285,
              onPressed: () {
                NavigatorService.pushNamed(AppRoutes.scan,
                    arguments: handleQRCodeScanned);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _group({required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.only(left: 15.w, right: 14.w),
      child: Container(
          height: 135.h,
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20.r),
              boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 14.w, top: 13.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    customSvgPicture(
                        imagePath: ImagePath.groupSvg,
                        height: 12.h,
                        width: 13.w,
                        color: AppPallete.darkGrey),
                    SizedBox(
                      width: 4.w,
                    ),
                    customtext(
                        context: context,
                        newYear: "$numberOfstudent/${totalStudent}",
                        font: 15.sp,
                        weight: FontWeight.w700),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 20.w, right: 23, top: 12.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: List.generate(attendances.length, (index) {
                            final attendance = attendances[index];
                            return Padding(
                              padding: EdgeInsets.only(right: 12.w),
                              child: student(
                                context: context,
                                imagePath:
                                    attendance.isVerified?.studentImage ?? "",
                                name: attendance.isVerified?.studentName ?? "",
                                verified:
                                    attendance.isVerified?.attendanceStatus ??
                                        "",
                              ),
                            );
                          }),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          )),
    );
  }
}
