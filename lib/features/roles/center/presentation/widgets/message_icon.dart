import 'package:class_z/core/imports.dart';

Widget messageIcon(
    {required BuildContext context,
    required String svg,
    required String title,
    required double height,
    required double width,
    required VoidCallback onTap,
    int? badgeCount}) {
  return InkWell(
    onTap: onTap,
    child: Column(
      children: [
        Stack(
          children: [
            customSvgPicture(
                imagePath: svg,
                height: height,
                width: width,
                color: Colors.black),
            if (badgeCount != null && badgeCount > 0)
              Positioned(
                right: 0.w,
                top: 0,
                child: Container(
                  padding: EdgeInsets.all(5),
                  decoration: BoxDecoration(
                    color: AppPallete.secondaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  constraints: BoxConstraints(
                    minWidth: 13,
                    minHeight: 13,
                  ),
                  child: Text(
                    badgeCount.toString(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 7,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
        customtext(
            context: context,
            newYear: title,
            font: 10,
            weight: FontWeight.w600,
            color: Colors.black)
      ],
    ),
  );
}
