
import 'package:class_z/core/common/data/models/event_model.dart';
import 'package:class_z/core/common/presentation/widgets/centre_list.dart';
import 'package:class_z/core/constants/string.dart';
import 'package:flutter/material.dart';

Widget buildEventList(
    BuildContext context, EventModel event, VoidCallback onTap) {
  return centreList(
    context: context,
    title: event.title ?? "",
    time: event.startTime ?? "",
    minute: event.durationMinutes ?? "",
    imagePath: "${AppText.device}${event.classId?.mainImage?.url}",
    category: event.classId?.level ?? "",
    name: event.classId?.coach?.displayName ?? "",
    location: event.classId?.address ?? "",
    language: event.classId?.language.toString() ?? "",
    ageGroup: "${event.classId?.ageFrom}- ${event.classId?.ageTo}",
    currentStudent: event.classId?.numberOfStudent?.toString() ?? "0",
    totalStudent: event.classId?.numberOfStudent?.toString() ?? "0",
    cost: event.classId?.charge.toString() ?? "",
    sen: event.classId?.sen ?? false,
    onTap: onTap,
  );
}
