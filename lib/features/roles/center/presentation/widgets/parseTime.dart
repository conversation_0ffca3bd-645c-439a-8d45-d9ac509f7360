DateTime parseTime(String timeString) {
  List<String> parts = timeString.split(' '); // Split into time and AM/PM
  String time = parts[0]; // e.g., "9:00"
  String period = parts[1]; // e.g., "AM" or "PM"

  List<String> timeParts = time.split(':'); // Split into hours and minutes
  int hour = int.parse(timeParts[0]);
  int minute = int.parse(timeParts[1]);

  // Adjust hour for PM
  if (period == 'PM' && hour != 12) {
    hour += 12; // Convert to 24-hour format
  }
  // Adjust hour for 12 AM (midnight)
  if (period == 'AM' && hour == 12) {
    hour = 0; // Midnight case
  }

  DateTime now = DateTime.now();
  return DateTime(now.year, now.month, now.day, hour, minute);
}
