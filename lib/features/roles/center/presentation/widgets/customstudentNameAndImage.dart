import 'package:class_z/core/imports.dart';

Widget student(
    {required BuildContext context,
    required String imagePath,
    required String verified,
    required String name}) {
  bool check = false;
  if (verified == "present") check = true;
  return SizedBox(
    height: 81.h,
    width: 62.w,
    child: Stack(
      children: [
        CustomImageBuilder(
            imagePath: imageStringGenerator(imagePath: imagePath),
            height: 62.h,
            width: 62.w,
            borderRadius: 99.r),
        if (check == true)
          Positioned(
            right: 0,
            child: Container(
                height: 19.h,
                width: 19.w,
                decoration: BoxDecoration(
                    color: AppPallete.green,
                    borderRadius: BorderRadius.circular(99.r)),
                child: Icon(
                  Icons.check,
                  size: 10.5.w,
                  color: Colors.white,
                )),
          ),
        Positioned(
          top: 67.h,
          child: customtext(
              context: context,
              newYear: name,
              font: 12.sp,
              weight: FontWeight.w400),
        )
      ],
    ),
  );
}
