import 'package:class_z/core/common/data/models/classDate_model.dart';
import 'package:class_z/core/imports.dart';

part 'center_event.dart';
part 'center_state.dart';

class CenterBloc extends Bloc<CenterEvent, CenterState> {
  final CreateCenterBranchUseCase createCenterBranchUseCase;
  final CenterInfoCompleteUseCase centerInfoCompleteUseCase;
  final GetCenterUseCase getCenterUseCase;
  final UpdateCenterUseCase updateCenterUseCase;
  final GetAllCenterUseCase getAllCenterUseCase;
  final GetCoachsByCenterIdUseCase getCoachsByCenterIdUseCase;
  final GetManagerssByCenterIdUseCase getManagerssByCenterIdUseCase;
  final AddClassUseCase addClassUseCase;
  final GetAllClassUseCase getAllClassUseCase;
  final GetAllClassByCoachIdUseCase getAllClassByCoachIdUseCase;
  final DeleteClassUseCase deleteClassUseCase;
  final UpdateClassUseCase updateClassUseCase;
  final GetAllEventsUseCase getAllEventsUseCase;
  final DeleteEventsByEventIdUseCase deleteEventsByEventIdUseCase;
  final GetEventsByClassIdUseCase getEventsByClassIdUseCase;
  final GetEventsByDateUseCase getEventsByDateUseCase;
  final GetEventsByEventIdUseCase getEventsByEventIdUseCase;
  final PostAttendanceUseCase postAttendanceUseCase;
  final GetPresentAttendanceUseCase getPresentAttendanceUseCase;
  final GetPendingReviewUseCase getPendingReviewUseCase;
  final GetPendingReviewByClassIdUseCase getPendingReviewByClassIdUseCase;
  final GetPendingReviewByCoachIdUseCase getPendingReviewByCoachIdUseCase;
  final GetEventDatesUseCase getEventDatesUseCase;
  final GetStudentsByClassIdUseCase getStudentsByClassIdUseCase;
  final GetCoachAndCenterByClassIdUseCase getCoachAndCenterByClassIdUseCase;
  final ClassSlotDeleteUseCase classSlotDeleteUseCase;
  final GetRequestSendedByCenterToCoachUseCase
      getRequestSendedByCenterToCoachUseCase;
  final GetAllClassesForParentUseCase getAllClassesForParentUseCase;
  final UpdateCoachInClassUseCase updateCoachInClassUseCase;
  final GetAllSlotsByClassIdUseCase getAllSlotsByClassIdUseCase;
  final DeleteSlotByIdUseCase deleteSlotByIdUseCase;
  final DeleteEventByIdUseCase deleteEventByIdUseCase;

  CenterBloc(
      {required this.createCenterBranchUseCase,
      required this.centerInfoCompleteUseCase,
      required this.getCenterUseCase,
      required this.updateCenterUseCase,
      required this.getAllCenterUseCase,
      required this.getCoachsByCenterIdUseCase,
      required this.getManagerssByCenterIdUseCase,
      required this.addClassUseCase,
      required this.deleteClassUseCase,
      required this.getAllClassUseCase,
      required this.getAllClassByCoachIdUseCase,
      required this.updateClassUseCase,
      required this.getAllEventsUseCase,
      required this.deleteEventsByEventIdUseCase,
      required this.getEventsByClassIdUseCase,
      required this.getEventsByEventIdUseCase,
      required this.getEventsByDateUseCase,
      required this.postAttendanceUseCase,
      required this.getPresentAttendanceUseCase,
      required this.getPendingReviewUseCase,
      required this.getPendingReviewByClassIdUseCase,
      required this.getPendingReviewByCoachIdUseCase,
      required this.getEventDatesUseCase,
      required this.getStudentsByClassIdUseCase,
      required this.getCoachAndCenterByClassIdUseCase,
      required this.classSlotDeleteUseCase,
      required this.getRequestSendedByCenterToCoachUseCase,
      required this.getAllClassesForParentUseCase,
      required this.updateCoachInClassUseCase,
      required this.getAllSlotsByClassIdUseCase,
      required this.deleteSlotByIdUseCase,
      required this.deleteEventByIdUseCase})
      : super(CenterInitial()) {
    on<CreateCenterBranchEvent>(createCenterBranchEvent);
    on<CenterInfoCompleteEvent>(centerInfoCompelteEvent);
    on<GetCenterDataEvent>(getCenterDataEvent);
    on<UpdateCenterEvent>(updateCenterEvent);
    on<GetAllCenterEvent>(getAllCenterEvent);
    on<GetCoachesByCenterIdEvent>(getCoachsByCenterIdEvent);
    on<GetManagersByCenterIdEvent>(_getManagersByCenterIdEvent);
    on<AddClassEvent>(addClassEvent);

    on<GetAllClassesEvent>(getAllClassEvent);
    on<GetAllClassesByCoachIdEvent>(_getAllClassesByCoachIdEvent);
    on<DeleteClassEvent>(deleteClassEvent);
    on<UpdateClassEvent>(updateClassEvent);
    on<GetAllEventsEvent>(getAllEventsEvent);
    on<DeleteEventByIdEvent>(_deleteEventByIdEvent);

    on<GetEventsByClassIdEvent>(getEventsByClassIdEvent);
    on<GetEventsByDateEvent>(getEventsByDateEvent);
    on<PostAttendanceEvent>(postAttendanceEvent);
    on<GetPresentAttendanceEvent>(getPresentAttendanceEvent);
    on<GetPendingReviewEvent>(getPendingReviewEvent);
    on<GetPendingReviewsByClassIdEvent>(_getPendingReviewsByClassIdEvent);
    on<GetPendingReviewsByCoachIdEvent>(_getPendingReviewsByCoachIdEvent);
    on<GetEventDatesEvent>(_getEventDates);
    on<GetStudentsByClassIdEvent>(_getStudentsByClassIdEvent);
    on<GetCoachAndCenterByClassIdEvent>(_getCoachAndCenterByClassIdEvent);
    on<ClassSlotDeleteEvent>(_classSlotDeleteEvent);
    on<GetRequestSendedByCenterToCoachEvent>(
        _getRequestSendedByCenterToCoachEvent);
    on<GetAllClassesForParentEvent>(_getAllClassesForParentEvent);
    on<UpdateCoachInClassEvent>(_updateCoachInClassEvent);
    on<GetAllSlotsByClassIdEvent>(_getAllSlotsByClassIdEvent);
    on<DeleteSlotByIdEvent>(_deleteSlotByIdEvent);
  }
  List<EventModel>? _cachedEvents;
  List<CenterData>? _centers;
  List<PendingModel>? _centerPendingReviews;
  List<PendingModel>? _coachPendingReviews;
  List<EventModel>? _centerCheckIn;
  List<EventModel>? _coachCheckIn;
  List<EventModel>? get cachedEvents => _cachedEvents;
  List<EventModel>? get centerCheckIn => _centerCheckIn;
  List<EventModel>? get coachCheckIn => _coachCheckIn;
  List<String>? _centerEventDates;
  List<String>? _coachEventDates;
  List<CenterData>? get centers => _centers;

  List<PendingModel>? get centerPendingReviews => _centerPendingReviews;
  List<PendingModel>? get coachPendingReviews => _coachPendingReviews;
  List<String>? get centerEventDates => _centerEventDates;
  List<String>? get coachEventDates => _coachEventDates;

  FutureOr<void> createCenterBranchEvent(
      CreateCenterBranchEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      bool success = await createCenterBranchUseCase.call(data: event.data);
      emit(CreateCenterBranchSuccess(isSuccess: success));
    } catch (e) {
      print('bloc');
      print(e.toString());
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> centerInfoCompelteEvent(
      CenterInfoCompleteEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      CenterData centerModel = await centerInfoCompleteUseCase.call();
      emit(CenterInfoSuccess(center: centerModel));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getCenterDataEvent(
      GetCenterDataEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      CenterData centerModel = await getCenterUseCase.call(event.id);
      emit(CenterInfoSuccess(center: centerModel));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> updateCenterEvent(
      UpdateCenterEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      CenterData success = await updateCenterUseCase.call(
          centerId: event.centerId, data: event.data);
      emit(CenterUpdateSuccess(success: success));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getAllCenterEvent(
      GetAllCenterEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      List<CenterData> centers =
          await getAllCenterUseCase.call(page: event.page, limit: event.limit);
      _centers = centers;
      emit(CenterListFetchSuccess(centers: centers));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getCoachsByCenterIdEvent(
      GetCoachesByCenterIdEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      List<CoachModel> coaches =
          await getCoachsByCenterIdUseCase.call(id: event.centerId);
      emit(CoachListFetchSuccess(coaches: coaches));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _getManagersByCenterIdEvent(
      GetManagersByCenterIdEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      List<CoachModel> coaches =
          await getManagerssByCenterIdUseCase.call(id: event.centerId);
      emit(CoachListFetchSuccess(coaches: coaches));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> addClassEvent(
      AddClassEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      String? newClassId = await addClassUseCase(
          classProviding: event.classProviding,
          description: event.description,
          category: event.category,
          charge: event.charge,
          mode: event.mode,
          image: event.image,
          initialCharge: event.initialCharge,
          level: event.level,
          sen: event.sen,
          from: event.from,
          to: event.to);
      emit(ClassCreationSuccess(
          isSuccess: newClassId != null, newClassId: newClassId));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> deleteClassEvent(
      DeleteClassEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      bool success = await deleteClassUseCase.call(id: event.classId);
      emit(ClassDeletionSuccess(isSuccess: success));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getAllClassEvent(
      GetAllClassesEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      final response = await getAllClassUseCase.call(event.centerId);

      final result = response.fold(
        (failure) => failure,
        (classes) => classes,
      );

      if (result is Failure) {
        emit(CenterErrorState(message: result.message));
        return;
      }

      final classes = result as List<ClassModel>;

      // For classes with null charge, try to get charge from schedule
      for (var classModel in classes) {
        if (classModel.charge == null) {
          try {
            final slots =
                await getAllSlotsByClassIdUseCase.call(classModel.id ?? '');
            // Find the first slot with a charge > 0
            for (var slot in slots) {
              if (slot.charge != null && slot.charge! > 0) {
                classModel.charge = slot.charge;
                print(
                    "🔍 Updated class ${classModel.classProviding} charge from schedule: ${slot.charge}");
                break;
              }
            }
          } catch (e) {
            print("Error fetching slots for class ${classModel.id}: $e");
          }
        }
      }

      emit(ClassListFetchSuccess(classes: classes));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _getAllClassesByCoachIdEvent(
      GetAllClassesByCoachIdEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      final response = await getAllClassByCoachIdUseCase.call(event.coachId);
      response.fold(
        (failure) => emit(CenterErrorState(message: failure.message)),
        (classes) => emit(ClassListFetchSuccess(classes: classes)),
      );
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

// center_bloc.dart
  FutureOr<void> updateClassEvent(
      UpdateClassEvent event, Emitter<CenterState> emit) async {
    try {
      print(
          'Updating class: classId=${event.classId}, data=${event.updatedData}');
      emit(CenterLoadingState());
      bool success = await updateClassUseCase.call(
        classId: event.classId,
        updatedData: event.updatedData,
      );
      print('Update class success: $success');
      emit(ClassUpdateSuccess(isSuccess: success));
    } catch (e) {
      print('Update class error: $e');
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getAllEventsEvent(
      GetAllEventsEvent event, Emitter<CenterState> emit) async {
    try {
      print(
          'DEBUG BLOC: getAllEventsEvent called with filterType: ${event.filterType}, filterValue: ${event.filterValue}, today: ${event.today}');
      emit(CenterLoadingState());
      List<EventModel> events = await getAllEventsUseCase.call(
          filterType: event.filterType,
          filterValue: event.filterValue,
          date: event.date);
      print('DEBUG BLOC: API returned ${events.length} events');
      _cachedEvents = events;
      if (event.filterType == 'centerId' && event.today == true) {
        _centerCheckIn = events;
        print('DEBUG BLOC: Stored ${events.length} events in _centerCheckIn');
      } else if (event.filterType == 'coachId' && event.today == true) {
        _coachCheckIn = events;
        print('DEBUG BLOC: Stored ${events.length} events in _coachCheckIn');
      }
      if (event.today == true) {
        print(
            'DEBUG BLOC: Emitting EventListFetchSuccessToday with ${events.length} events');
        emit(EventListFetchSuccessToday(events: events));
      } else {
        print(
            'DEBUG BLOC: Emitting EventListFetchSuccess with ${events.length} events');
        emit(EventListFetchSuccess(events: events));
      }
    } catch (e) {
      print('DEBUG BLOC: Error in getAllEventsEvent: $e');
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getEventsByEventIdEvent(
      GetEventByIdEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      EventModel events =
          await getEventsByEventIdUseCase.call(eventId: event.eventId);
      emit(EventFetchSuccess(event: events));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getEventsByClassIdEvent(
      GetEventsByClassIdEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      EventDatesModel eventDates =
          await getEventsByClassIdUseCase.call(classId: event.classId);

      emit(EventDatesForClassSuccess(eventsDate: eventDates));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> deleteEventsByEventIdEvent(
      DeleteEventByIdEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      bool success =
          await deleteEventsByEventIdUseCase.call(eventId: event.eventId);
      emit(EventDeletionSuccess(isSuccess: success));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getEventsByDateEvent(
      GetEventsByDateEvent event, Emitter<CenterState> emit) async {
    try {
      // Check if cached data exists
      if (_cachedEvents != null && _cachedEvents!.isNotEmpty) {
        print('cached');
        emit(EventsByDateSuccess(events: _cachedEvents!));
        //  return;
      }

      // If no cache, fetch from API
      emit(CenterLoadingState());
      List<EventModel> events = await getEventsByDateUseCase.call(id: event.id);

      // Store in cache
      _cachedEvents = events;
      print('donw');
      emit(EventsByDateSuccess(events: events));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> postAttendanceEvent(
      PostAttendanceEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      AttendanceModel attendance = await postAttendanceUseCase.call(
          classId: event.classId,
          code: event.code,
          qrCodeData: event.qrCodeData,
          classDate: event.classDate);
      emit(AttendancePostSuccess(attendance: attendance));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getPresentAttendanceEvent(
      GetPresentAttendanceEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      List<AttendanceModel> attendance = await getPresentAttendanceUseCase.call(
        classId: event.classId,
        classDate: event.classDate,
      );
      emit(PresentAttendanceFetchSuccess(attendance: attendance));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getPendingReviewEvent(
      GetPendingReviewEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      List<PendingModel> pendings =
          await getPendingReviewUseCase.call(id: event.id);
      _centerPendingReviews = pendings;
      emit(PendingReviewFetchSuccess(pendingReviews: pendings));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _getPendingReviewsByClassIdEvent(
      GetPendingReviewsByClassIdEvent event, Emitter<CenterState> emit) async {
    final result =
        await getPendingReviewByClassIdUseCase.call(classId: event.classId);
    result.fold(
        (failure) => emit(CenterErrorState(message: failure.message)),
        (pendingReviews) => emit(PendingReviewsForClassFetchSuccess(
            pendingReviewsForClass: pendingReviews)));
  }

  FutureOr<void> _getPendingReviewsByCoachIdEvent(
      GetPendingReviewsByCoachIdEvent event, Emitter<CenterState> emit) async {
    final result =
        await getPendingReviewByCoachIdUseCase.call(coachId: event.coachId);
    result.fold((failure) => emit(CenterErrorState(message: failure.message)),
        (pendingReviews) {
      _coachPendingReviews = pendingReviews;
      emit(PendingReviewFetchSuccess(pendingReviews: pendingReviews));
    });
  }

  FutureOr<void> _getEventDates(
      GetEventDatesEvent event, Emitter<CenterState> emit) async {
    emit(CenterLoadingState());
    try {
      final result = await getEventDatesUseCase.call(
          filterType: event.filterType, filterValue: event.filterValue);
      result.fold((failure) => emit(CenterErrorState(message: failure.message)),
          (dates) {
        if (event.filterType == 'centerId') {
          _centerEventDates = dates;
        } else if (event.filterType == 'coachId') {
          _coachEventDates = dates;
        }
        emit(EventDatesFetchSuccess(eventDates: dates));
        ;
      });
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _getStudentsByClassIdEvent(
      GetStudentsByClassIdEvent event, Emitter<CenterState> emit) async {
    emit(CenterLoadingState());
    try {
      final result =
          await getStudentsByClassIdUseCase.call(classId: event.classId);
      result.fold(
        (failure) => emit(CenterErrorState(message: failure.message)),
        (students) => emit(StudentsByClassIdFetchSuccess(students: students)),
      );
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _getCoachAndCenterByClassIdEvent(
      GetCoachAndCenterByClassIdEvent event, Emitter<CenterState> emit) async {
    emit(CenterLoadingState());
    try {
      final result =
          await getCoachAndCenterByClassIdUseCase.call(classId: event.classId);
      result.fold(
        (failure) => emit(CenterErrorState(message: failure.message)),
        (classs) => emit(CoachAndCenterByClassIdFetchSuccess(classs: classs)),
      );
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _classSlotDeleteEvent(
      ClassSlotDeleteEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      bool success = await classSlotDeleteUseCase.call(
        classId: event.classId,
        cancelType: event.cancelType,
        eventId: event.eventId,
        newDate: event.newDate,
        newStartTime: event.newStartTime,
        newEndTime: event.newEndTime,
      );

      String message;
      if (event.cancelType == 'refund') {
        message = 'Slot cancelled and students will be refunded';
      } else if (event.cancelType == 'rearrange') {
        message = 'Slot cancelled and will be rearranged';
      } else {
        message = 'Slot cancelled successfully';
      }

      emit(ClassSlotDeleteSuccessState(success: success, message: message));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _getRequestSendedByCenterToCoachEvent(
      GetRequestSendedByCenterToCoachEvent event,
      Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      final result = await getRequestSendedByCenterToCoachUseCase.call(
          centerId: event.centerId, type: event.type);
      print('in bloc');
      result.fold(
          (failure) => emit(CenterErrorState(message: failure.message)),
          (requestList) => emit(RequestSendedByCenterToCoachFetchSuccess(
              requestList: requestList)));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _getAllClassesForParentEvent(
      GetAllClassesForParentEvent event, Emitter<CenterState> emit) async {
    try {
      emit(CenterLoadingState());
      final response = await getAllClassesForParentUseCase.call(
        centerId: event.centerId,
      );

      await response.fold(
        (failure) async => emit(CenterErrorState(message: failure.message)),
        (classes) async {
          // For classes with null charge, try to get charge from schedule
          for (var classModel in classes) {
            if (classModel.charge == null) {
              try {
                final slots =
                    await getAllSlotsByClassIdUseCase.call(classModel.id ?? '');

                // Find the first slot with a charge > 0
                for (var slot in slots) {
                  if (slot.charge != null && slot.charge! > 0) {
                    classModel.charge = slot.charge;
                    break;
                  }
                }
              } catch (e) {
                print(
                    "Error fetching slots for parent class ${classModel.id}: $e");
              }
            }
          }

          emit(ClassListFetchSuccess(classes: classes));
        },
      );
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _updateCoachInClassEvent(
    UpdateCoachInClassEvent event,
    Emitter<CenterState> emit,
  ) async {
    try {
      emit(CenterLoadingState());

      final result = await updateCoachInClassUseCase(
        UpdateCoachInClassParams(
          classId: event.classId,
          coachId: event.coachId,
        ),
      );

      result.fold(
        (failure) => emit(CenterErrorState(message: failure.message)),
        (success) {
          // Clear the event cache so that updated coach information is fetched
          clearCache();
          emit(CoachUpdateInClassSuccess(isSuccess: success));
        },
      );
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _getAllSlotsByClassIdEvent(
      GetAllSlotsByClassIdEvent event, Emitter<CenterState> emit) async {
    emit(CenterLoadingState());
    try {
      final slots = await getAllSlotsByClassIdUseCase.call(event.classId);
      emit(AllSlotsByClassIdFetchSuccess(slots: slots));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _deleteSlotByIdEvent(
      DeleteSlotByIdEvent event, Emitter<CenterState> emit) async {
    emit(CenterLoadingState());
    try {
      final success = await deleteSlotByIdUseCase.call(event.slotId);
      emit(DeleteSlotByIdSuccess(success: success));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _deleteEventByIdEvent(
      DeleteEventByIdEvent event, Emitter<CenterState> emit) async {
    emit(CenterLoadingState());
    try {
      final success = await deleteEventByIdUseCase.call(event.eventId);
      emit(DeleteEventByIdSuccess(success: success));
    } catch (e) {
      emit(CenterErrorState(message: e.toString()));
    }
  }

  // Method to clear cache (optional for refresh functionality)
  void clearCache() {
    _cachedEvents = null;
  }

  // Handle the PostReviewSuccessState by removing the submitted review from the pending reviews list
  void removeSubmittedReviewFromPending(PendingModel submittedReview) {
    bool centerListChanged = false;
    bool coachListChanged = false;

    // Remove from center pending reviews first, as this is often the primary list for UI
    if (_centerPendingReviews != null) {
      final oldLength = _centerPendingReviews!.length;
      _centerPendingReviews = _centerPendingReviews!.where((pending) {
        if (pending.studentId?.id == submittedReview.studentId?.id &&
            pending.plainClassDetails?.id ==
                submittedReview.plainClassDetails?.id &&
            pending.event?.id == submittedReview.event?.id) {
          return false; // Remove this review
        }
        return true; // Keep all other reviews
      }).toList();

      if (oldLength != _centerPendingReviews!.length) {
        centerListChanged = true;
      }
    }

    // Remove from coach pending reviews
    if (_coachPendingReviews != null) {
      final oldLength = _coachPendingReviews!.length;
      _coachPendingReviews = _coachPendingReviews!.where((pending) {
        if (pending.studentId?.id == submittedReview.studentId?.id &&
            pending.plainClassDetails?.id ==
                submittedReview.plainClassDetails?.id &&
            pending.event?.id == submittedReview.event?.id) {
          return false; // Remove this review
        }
        return true; // Keep all other reviews
      }).toList();
      if (oldLength != _coachPendingReviews!.length) {
        coachListChanged = true;
      }
    }

    if (centerListChanged) {
      emit(PendingReviewFetchSuccess(pendingReviews: _centerPendingReviews!));
      debugPrint('Emitted updated _centerPendingReviews after removal.');
    } else if (coachListChanged) {
      // Only emit for coach list if center list didn't change and wasn't the source of removal for this call
      // This case is less common if removeSubmittedReviewFromPending is generic
      emit(PendingReviewFetchSuccess(pendingReviews: _coachPendingReviews!));
      debugPrint(
          'Emitted updated _coachPendingReviews after removal (center list did not change).');
    }

    if (!centerListChanged && !coachListChanged) {
      debugPrint(
          'No review was removed from known lists by removeSubmittedReviewFromPending, or lists were null.');
      // Optionally, if no known list was updated but a review was submitted,
      // you might want to re-fetch the main pending list as a fallback,
      // though this should ideally be handled by specific UI list refreshes.
      // For example: add(GetPendingReviewEvent(locator<SharedRepository>().getCenterId() ?? ''));
    }
  }
}
