part of 'center_bloc.dart';

abstract class CenterState extends Equatable {
  @override
  List<Object?> get props => [];
}

class CenterInitial extends CenterState {}

class CenterLoadingState extends CenterState {
  CenterLoadingState();
}

class CenterErrorState extends CenterState {
  final String message;

  CenterErrorState({required this.message});

  @override
  List<Object?> get props => [message];
}

class CreateCenterBranchSuccess extends CenterState {
  final bool isSuccess;

  CreateCenterBranchSuccess({required this.isSuccess});

  @override
  List<Object?> get props => [isSuccess];
}

class CenterInfoSuccess extends CenterState {
  final CenterData center;

  CenterInfoSuccess({required this.center});

  @override
  List<Object?> get props => [center];
}

class CenterFetchSuccess extends CenterState {
  final CenterData center;

  CenterFetchSuccess({required this.center});

  @override
  List<Object?> get props => [center];
}

class CenterUpdateSuccess extends CenterState {
  final CenterData success;

  CenterUpdateSuccess({required this.success});

  @override
  List<Object?> get props => [success];
}

class CenterListFetchSuccess extends CenterState {
  final List<CenterData> centers;

  CenterListFetchSuccess({required this.centers});

  @override
  List<Object?> get props => [centers];
}

class CoachListFetchSuccess extends CenterState {
  final List<CoachModel> coaches;

  CoachListFetchSuccess({required this.coaches});

  @override
  List<Object?> get props => [coaches];
}

class ClassCreationSuccess extends CenterState {
  final bool isSuccess;
  final String? newClassId;

  ClassCreationSuccess({required this.isSuccess, this.newClassId});

  @override
  List<Object?> get props => [isSuccess, newClassId];
}

class ClassListFetchSuccess extends CenterState {
  final List<ClassModel> classes;

  ClassListFetchSuccess({required this.classes});

  @override
  List<Object?> get props => [classes];
}

class ClassDeletionSuccess extends CenterState {
  final bool isSuccess;

  ClassDeletionSuccess({required this.isSuccess});

  @override
  List<Object?> get props => [isSuccess];
}

class ClassUpdateSuccess extends CenterState {
  final bool isSuccess;

  ClassUpdateSuccess({required this.isSuccess});

  @override
  List<Object?> get props => [isSuccess];
}

class EventListFetchSuccess extends CenterState {
  final List<EventModel> events;

  EventListFetchSuccess({required this.events});

  @override
  List<Object?> get props => [events];
}

class EventListFetchSuccessToday extends CenterState {
  final List<EventModel> events;

  EventListFetchSuccessToday({required this.events});

  @override
  List<Object?> get props => [events];
}

class EventDatesForClassSuccess extends CenterState {
  final EventDatesModel eventsDate;

  EventDatesForClassSuccess({required this.eventsDate});
  @override
  List<Object?> get props => [eventsDate];
}

class EventsByClassIdSuccess extends CenterState {
  final List<EventModel> events;

  EventsByClassIdSuccess({required this.events});

  @override
  List<Object?> get props => [events];
}

class EventsByDateSuccess extends CenterState {
  final List<EventModel> events;

  EventsByDateSuccess({required this.events});

  @override
  List<Object?> get props => [events];
}

class EventFetchSuccess extends CenterState {
  final EventModel event;

  EventFetchSuccess({required this.event});

  @override
  List<Object?> get props => [event];
}

class EventDeletionSuccess extends CenterState {
  final bool isSuccess;

  EventDeletionSuccess({required this.isSuccess});

  @override
  List<Object?> get props => [isSuccess];
}

class AttendancePostSuccess extends CenterState {
  final AttendanceModel attendance;

  AttendancePostSuccess({required this.attendance});

  @override
  List<Object?> get props => [attendance];
}

class PresentAttendanceFetchSuccess extends CenterState {
  final List<AttendanceModel> attendance;

  PresentAttendanceFetchSuccess({required this.attendance});

  @override
  List<Object?> get props => [attendance];
}

class PendingReviewFetchSuccess extends CenterState {
  final List<PendingModel> pendingReviews;

  PendingReviewFetchSuccess({required this.pendingReviews});

  @override
  List<Object?> get props => [pendingReviews];
}

class PendingReviewByClassIdFetchSuccess extends CenterState {
  final List<PendingModel> pendingReviews;

  PendingReviewByClassIdFetchSuccess({required this.pendingReviews});

  @override
  List<Object?> get props => [pendingReviews];
}

class PendingReviewsForClassFetchSuccess extends CenterState {
  final List<PendingModel> pendingReviewsForClass;

  PendingReviewsForClassFetchSuccess({required this.pendingReviewsForClass});

  @override
  List<Object?> get props => [pendingReviewsForClass];
}

class EventDatesFetchSuccess extends CenterState {
  final List<String> eventDates;

  EventDatesFetchSuccess({required this.eventDates});

  @override
  List<Object?> get props => [eventDates];
}

class StudentsByClassIdFetchSuccess extends CenterState {
  final List<ChildModel> students;

  StudentsByClassIdFetchSuccess({required this.students});

  @override
  List<Object?> get props => [students];
}

class CoachAndCenterByClassIdFetchSuccess extends CenterState {
  final ClassModel classs;

  CoachAndCenterByClassIdFetchSuccess({required this.classs});

  @override
  List<Object?> get props => [classs];
}

class ClassSlotDeleteSuccessState extends CenterState {
  final bool success;
  final String? message;

  ClassSlotDeleteSuccessState({required this.success, this.message});

  @override
  List<Object?> get props => [success, message];
}

class RequestSendedByCenterToCoachFetchSuccess extends CenterState {
  final List<String> requestList;

  RequestSendedByCenterToCoachFetchSuccess({required this.requestList});

  @override
  List<Object?> get props => [requestList];
}

class CoachUpdateInClassSuccess extends CenterState {
  final bool isSuccess;

  CoachUpdateInClassSuccess({required this.isSuccess});

  @override
  List<Object?> get props => [isSuccess];
}

class AllSlotsByClassIdFetchSuccess extends CenterState {
  final List<ClassDate> slots; // Use dynamic to avoid part file type issues
  AllSlotsByClassIdFetchSuccess({required this.slots});

  @override
  List<Object?> get props => [slots];
}

class DeleteSlotByIdSuccess extends CenterState {
  final bool success;
  DeleteSlotByIdSuccess({required this.success});

  @override
  List<Object?> get props => [success];
}

class DeleteEventByIdSuccess extends CenterState {
  final bool success;
  DeleteEventByIdSuccess({required this.success});

  @override
  List<Object?> get props => [success];
}
