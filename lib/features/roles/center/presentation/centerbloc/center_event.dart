part of 'center_bloc.dart';

abstract class CenterEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class CenterInfoCompleteEvent extends CenterEvent {}

class CreateCenterBranchEvent extends CenterEvent {
  final Map<String, dynamic> data;
  final File? mainImage;
  final File? businessCertificate;
  final File? hkidCard;
  final List<File>? images;

  CreateCenterBranchEvent({
    required this.data,
    this.businessCertificate,
    this.hkidCard,
    this.images,
    this.mainImage,
  });

  @override
  List<Object?> get props =>
      [data, mainImage, businessCertificate, hkidCard, images];
}

class GetCenterDataEvent extends CenterEvent {
  final String id;

  GetCenterDataEvent({required this.id});

  @override
  List<Object?> get props => [id];
}

class UpdateCenterEvent extends CenterEvent {
  final Map<String, dynamic> data;
  final String centerId;

  UpdateCenterEvent({required this.centerId, required this.data});

  @override
  List<Object?> get props => [data];
}

class GetAllCenterEvent extends CenterEvent {
  final int page;
  final int limit;

  GetAllCenterEvent({required this.page, required this.limit});
}

class GetCoachesByCenterIdEvent extends CenterEvent {
  final String centerId;

  GetCoachesByCenterIdEvent({required this.centerId});

  @override
  List<Object?> get props => [centerId];
}

class GetManagersByCenterIdEvent extends CenterEvent {
  final String centerId;

  GetManagersByCenterIdEvent({required this.centerId});

  @override
  List<Object?> get props => [centerId];
}

class AddClassEvent extends CenterEvent {
  final String classProviding;
  final String category;
  final String? level;
  final String description;
  final bool mode;
  final bool? sen;
  final String? initialCharge;
  final String charge;
  final String from;
  final String to;
  final File? image;

  AddClassEvent({
    required this.classProviding,
    required this.category,
    required this.description,
    required this.mode,
    required this.charge,
    required this.from,
    required this.to,
    this.image,
    this.initialCharge,
    this.level,
    this.sen,
  });

  @override
  List<Object?> get props => [
        classProviding,
        category,
        level,
        description,
        mode,
        sen,
        initialCharge,
        charge,
        from,
        to,
        image
      ];
}

class GetAllClassesEvent extends CenterEvent {
  final String centerId;

  GetAllClassesEvent({required this.centerId});

  @override
  List<Object?> get props => [centerId];
}

class GetAllClassesByCoachIdEvent extends CenterEvent {
  final String coachId;

  GetAllClassesByCoachIdEvent({required this.coachId});

  @override
  List<Object?> get props => [coachId];
}

class DeleteClassEvent extends CenterEvent {
  final String classId;

  DeleteClassEvent({required this.classId});

  @override
  List<Object?> get props => [classId];
}

class UpdateClassEvent extends CenterEvent {
  final String classId;
  final Map<String, dynamic> updatedData;

  UpdateClassEvent({required this.classId, required this.updatedData});

  @override
  List<Object?> get props => [classId, updatedData];
}

class GetAllEventsEvent extends CenterEvent {
  final String filterType;
  final String filterValue;
  final String? date;
  final bool? today;
  GetAllEventsEvent(
      {required this.filterType,
      required this.filterValue,
      this.date,
      this.today});

  @override
  List<Object?> get props => [filterType, filterValue];
}

class GetEventsByClassIdEvent extends CenterEvent {
  final String classId;

  GetEventsByClassIdEvent({required this.classId});

  @override
  List<Object?> get props => [classId];
}

class GetEventByIdEvent extends CenterEvent {
  final String eventId;

  GetEventByIdEvent({required this.eventId});

  @override
  List<Object?> get props => [eventId];
}

class GetEventsByDateEvent extends CenterEvent {
  final String id;

  GetEventsByDateEvent(this.id);
  @override
  List<Object?> get props => [id];
}

class DeleteEventByIdEvent extends CenterEvent {
  final String eventId;

  DeleteEventByIdEvent({required this.eventId});

  @override
  List<Object?> get props => [eventId];
}

class PostAttendanceEvent extends CenterEvent {
  final String? classId;
  final String? code;
  final String? qrCodeData;
  final String? classDate;

  PostAttendanceEvent(
      {this.classId, this.code, this.qrCodeData, this.classDate});

  @override
  List<Object?> get props => [classId, code, qrCodeData];
}

class GetPresentAttendanceEvent extends CenterEvent {
  final String classId;
  final String classDate;
  GetPresentAttendanceEvent({required this.classId, required this.classDate});

  @override
  List<Object?> get props => [classId];
}

class GetPendingReviewEvent extends CenterEvent {
  final String id;

  GetPendingReviewEvent(this.id);
  @override
  List<Object?> get props => [id];
}

class GetPendingReviewsByClassIdEvent extends CenterEvent {
  final String classId;

  GetPendingReviewsByClassIdEvent(this.classId);
  @override
  List<Object?> get props => [classId];
}

class GetPendingReviewsByCoachIdEvent extends CenterEvent {
  final String coachId;

  GetPendingReviewsByCoachIdEvent(this.coachId);
  @override
  List<Object?> get props => [coachId];
}

class GetEventDatesEvent extends CenterEvent {
  final String filterType;
  final String filterValue;

  GetEventDatesEvent({required this.filterType, required this.filterValue});

  @override
  List<Object?> get props => [filterType, filterValue];
}

class GetStudentsByClassIdEvent extends CenterEvent {
  final String classId;

  GetStudentsByClassIdEvent({required this.classId});

  @override
  List<Object?> get props => [classId];
}

class GetCoachAndCenterByClassIdEvent extends CenterEvent {
  final String classId;

  GetCoachAndCenterByClassIdEvent({required this.classId});

  @override
  List<Object?> get props => [classId];
}

class ClassSlotDeleteEvent extends CenterEvent {
  final String classId;
  final String? cancelType;
  final String? eventId;
  final String? newDate;
  final String? newStartTime;
  final String? newEndTime;

  ClassSlotDeleteEvent(this.classId,
      {this.cancelType,
      this.eventId,
      this.newDate,
      this.newStartTime,
      this.newEndTime});

  @override
  List<Object?> get props =>
      [classId, cancelType, eventId, newDate, newStartTime, newEndTime];
}

class GetRequestSendedByCenterToCoachEvent extends CenterEvent {
  final String centerId;
  final String type;

  GetRequestSendedByCenterToCoachEvent(
      {required this.centerId, required this.type});

  @override
  List<Object?> get props => [centerId, type];
}

class GetAllClassesForParentEvent extends CenterEvent {
  final String centerId;

  GetAllClassesForParentEvent({required this.centerId});

  @override
  List<Object?> get props => [centerId];
}

class UpdateCoachInClassEvent extends CenterEvent {
  final String classId;
  final String coachId;

  UpdateCoachInClassEvent({
    required this.classId,
    required this.coachId,
  });

  @override
  List<Object?> get props => [classId, coachId];
}

class GetAllSlotsByClassIdEvent extends CenterEvent {
  final String classId;
  GetAllSlotsByClassIdEvent({required this.classId});

  @override
  List<Object?> get props => [classId];
}

class DeleteSlotByIdEvent extends CenterEvent {
  final String slotId;
  DeleteSlotByIdEvent({required this.slotId});

  @override
  List<Object?> get props => [slotId];
}
