import 'package:class_z/core/imports.dart';
import 'package:dartz/dartz.dart';

abstract class CenterRepoDomain {
  Future<bool> createCenterBranch(
      {required Map<String, dynamic> data,
      File? mainImage,
      File? businessCertificate,
      File? hkidCard,
      List<File>? images});
  Future<CenterData> centerInfoComplete();

  Future<CenterData> getCenter(String id);
  Future<List<CenterData>> getAllCenter(
      {required int page, required int limit});
  Future<List<CoachModel>> getCoachsByCenterId({required String id});
  Future<List<CoachModel>> getManagersByCenterId({required String id});
  Future<CenterData> UpdateCenter(
      {required String centerId, required Map<String, dynamic> data});
  Future<Either<Failure, List<ClassModel>>> getAllClassByCenterId(
      String centerId);
  Future<Either<Failure, List<ClassModel>>> getAllClassByCoachId(
      String coachId);
  Future<Either<Failure, List<ClassModel>>> getAllClassesForParent(
      {required String centerId});

  Future<String?> addClass(
      {required String classProviding,
      required String category,
      String? level,
      required String description,
      required bool mode,
      bool? sen,
      String? initialCharge,
      required String charge,
      required String from,
      required String to,
      File? image});
  Future<bool> deleteClass({required String id});
  Future<bool> updateClass(
      {required String classId, required Map<String, dynamic> updatedData});
  Future<Either<Failure, List<ChildModel>>> getStudentsByClassId(
      {required String classId});
  Future<List<EventModel>> getAllEvent(
      {required String filterType, required String filterValue, String? date});
  Future<Either<Failure, List<String>>> getEventDates(
      {required String filterType, required String filterValue});
  Future<EventDatesModel> getEventsByClassId({required String classId});
  Future<List<EventModel>> getEventsByDate({required String id});
  Future<EventModel> getEventsByEventId({required String eventId});
  Future<bool> deleteEventsbyEventId({required String eventId});
  Future<AttendanceModel> postAttendance(
      {String? classId, String? code, String? qrCodeData, String? classDate});
  Future<List<AttendanceModel>> getPresentAttendance(
      {required String classId, required String classDate});
  Future<List<PendingModel>> getPendingReview({required String id});
  Future<Either<Failure, List<PendingModel>>> getPendingReviewByClassId(
      {required String classId});
  Future<Either<Failure, List<PendingModel>>> getPendingReviewByCoachId(
      {required String coachId});
  Future<Either<Failure, ClassModel>> getCoachAndCenterByClassId(
      {required String classId});
  Future<bool> classSlotDelete({
    required String classId,
    String? cancelType,
    String? eventId,
    String? newDate,
    String? newStartTime,
    String? newEndTime,
  });
  Future<Either<Failure, List<String>>> getRequestSendedByCenterToCoach(
      {required String centerId, required String type});
  Future<Either<Failure, bool>> updateCoachInClass({
    required String classId,
    required String coachId,
  });
  Future<List<ClassDate>> getAllSlotsByClassId(String classId);
  Future<bool> deleteSlotById(String slotId);
  Future<bool> deleteEventById(String eventId);
}
