import 'package:class_z/core/imports.dart';
import 'package:dartz/dartz.dart';

class CreateCenterBranchUseCase {
  final CenterRepoDomain centerRepoDomain;

  CreateCenterBranchUseCase({required this.centerRepoDomain});
  Future<bool> call(
      {required Map<String, dynamic> data,
      File? mainImage,
      File? businessCertificate,
      File? hkidCard,
      List<File>? images}) async {
    return centerRepoDomain.createCenterBranch(
        data: data,
        mainImage: mainImage,
        businessCertificate: businessCertificate,
        hkidCard: hkidCard,
        images: images);
  }
}

class CenterInfoCompleteUseCase {
  final CenterRepoDomain centerRepoDomain;

  CenterInfoCompleteUseCase({required this.centerRepoDomain});
  Future<CenterData> call() async {
    return await centerRepoDomain.centerInfoComplete();
  }
}

class GetCenterUseCase {
  final CenterRepoDomain centerRepoDomain;

  GetCenterUseCase({required this.centerRepoDomain});
  Future<CenterData> call(String id) async {
    return await centerRepoDomain.getCenter(id);
  }
}

class GetCoachsByCenterIdUseCase {
  final CenterRepoImpl centerRepoImpl;

  GetCoachsByCenterIdUseCase({required this.centerRepoImpl});
  Future<List<CoachModel>> call({required String id}) async {
    return await centerRepoImpl.getCoachsByCenterId(id: id);
  }
}

class GetManagerssByCenterIdUseCase {
  final CenterRepoImpl centerRepoImpl;

  GetManagerssByCenterIdUseCase({required this.centerRepoImpl});
  Future<List<CoachModel>> call({required String id}) async {
    return await centerRepoImpl.getManagersByCenterId(id: id);
  }
}

class UpdateCenterUseCase {
  final CenterRepoDomain centerRepoDomain;

  UpdateCenterUseCase({required this.centerRepoDomain});
  Future<CenterData> call(
      {required String centerId, required Map<String, dynamic> data}) async {
    return await centerRepoDomain.UpdateCenter(centerId: centerId, data: data);
  }
}

class GetAllCenterUseCase {
  final CenterRepoDomain centerRepoDomain;

  GetAllCenterUseCase({required this.centerRepoDomain});
  Future<List<CenterData>> call({required int page, required int limit}) async {
    return await centerRepoDomain.getAllCenter(page: page, limit: limit);
  }
}

class AddClassUseCase {
  final CenterRepoDomain centerRepoDomain;

  AddClassUseCase({required this.centerRepoDomain});
  Future<String?> call(
      {required String classProviding,
      required String category,
      String? level,
      required String description,
      required bool mode,
      bool? sen,
      String? initialCharge,
      required String charge,
      required String from,
      required String to,
      File? image}) async {
    return await centerRepoDomain.addClass(
        classProviding: classProviding,
        category: category,
        description: description,
        mode: mode,
        charge: charge,
        image: image,
        initialCharge: initialCharge,
        level: level,
        sen: sen,
        from: from,
        to: to);
  }
}

class GetAllClassUseCase {
  final CenterRepoDomain centerRepoDomain;

  GetAllClassUseCase({required this.centerRepoDomain});
  Future<Either<Failure, List<ClassModel>>> call(String centerId) async {
    return await centerRepoDomain.getAllClassByCenterId(centerId);
  }
}

class GetAllClassByCoachIdUseCase {
  final CenterRepoDomain centerRepoDomain;

  GetAllClassByCoachIdUseCase({required this.centerRepoDomain});
  Future<Either<Failure, List<ClassModel>>> call(String coachId) async {
    return await centerRepoDomain.getAllClassByCoachId(coachId);
  }
}

class DeleteClassUseCase {
  final CenterRepoDomain centerRepoDomain;

  DeleteClassUseCase({required this.centerRepoDomain});
  Future<bool> call({required String id}) async {
    return await centerRepoDomain.deleteClass(id: id);
  }
}

class UpdateClassUseCase {
  final CenterRepoDomain centerRepoDomain;

  UpdateClassUseCase({required this.centerRepoDomain});
  Future<bool> call(
      {required String classId,
      required Map<String, dynamic> updatedData}) async {
    return await centerRepoDomain.updateClass(
        classId: classId, updatedData: updatedData);
  }
}

class GetAllEventsUseCase {
  final CenterRepoDomain centerRepoDomain;

  GetAllEventsUseCase({required this.centerRepoDomain});
  Future<List<EventModel>> call(
      {required String filterType,
      required String filterValue,
      String? date}) async {
    return await centerRepoDomain.getAllEvent(
        filterType: filterType, filterValue: filterValue, date: date);
  }
}

class GetEventsByClassIdUseCase {
  final CenterRepoDomain centerRepoDomain;

  GetEventsByClassIdUseCase({required this.centerRepoDomain});
  Future<EventDatesModel> call({required String classId}) async {
    return await centerRepoDomain.getEventsByClassId(classId: classId);
  }
}

class GetEventsByEventIdUseCase {
  final CenterRepoDomain centerRepoDomain;

  GetEventsByEventIdUseCase({required this.centerRepoDomain});
  Future<EventModel> call({required String eventId}) async {
    return await centerRepoDomain.getEventsByEventId(eventId: eventId);
  }
}

class GetEventsByDateUseCase {
  final CenterRepoDomain centerRepoDomain;

  GetEventsByDateUseCase({required this.centerRepoDomain});
  Future<List<EventModel>> call({required String id}) async {
    return await centerRepoDomain.getEventsByDate(id: id);
  }
}

class DeleteEventsByEventIdUseCase {
  final CenterRepoDomain centerRepoDomain;

  DeleteEventsByEventIdUseCase({required this.centerRepoDomain});
  Future<bool> call({required String eventId}) async {
    return await centerRepoDomain.deleteEventsbyEventId(eventId: eventId);
  }
}

class PostAttendanceUseCase {
  final CenterRepoDomain centerRepoDomain;

  PostAttendanceUseCase({required this.centerRepoDomain});
  Future<AttendanceModel> call(
      {String? classId,
      String? code,
      String? qrCodeData,
      String? classDate}) async {
    return await centerRepoDomain.postAttendance(
        classId: classId,
        code: code,
        qrCodeData: qrCodeData,
        classDate: classDate);
  }
}

class GetPresentAttendanceUseCase {
  final CenterRepoDomain centerRepoDomain;

  GetPresentAttendanceUseCase({required this.centerRepoDomain});
  Future<List<AttendanceModel>> call(
      {required String classId, required String classDate}) async {
    return await centerRepoDomain.getPresentAttendance(
        classId: classId, classDate: classDate);
  }
}

class GetPendingReviewUseCase {
  final CenterRepoDomain centerRepoDomain;

  GetPendingReviewUseCase({required this.centerRepoDomain});
  Future<List<PendingModel>> call({required String id}) async {
    return await centerRepoDomain.getPendingReview(id: id);
  }
}

class GetPendingReviewByClassIdUseCase {
  final CenterRepoDomain _centerRepoDomain;

  GetPendingReviewByClassIdUseCase(this._centerRepoDomain);
  Future<Either<Failure, List<PendingModel>>> call(
      {required String classId}) async {
    return await _centerRepoDomain.getPendingReviewByClassId(classId: classId);
  }
}

class GetPendingReviewByCoachIdUseCase {
  final CenterRepoDomain _centerRepoDomain;

  GetPendingReviewByCoachIdUseCase(this._centerRepoDomain);
  Future<Either<Failure, List<PendingModel>>> call(
      {required String coachId}) async {
    return await _centerRepoDomain.getPendingReviewByCoachId(coachId: coachId);
  }
}

class GetEventDatesUseCase {
  final CenterRepoDomain centerRepoDomain;
  GetEventDatesUseCase({required this.centerRepoDomain});
  Future<Either<Failure, List<String>>> call(
      {required String filterType, required String filterValue}) async {
    return await centerRepoDomain.getEventDates(
        filterType: filterType, filterValue: filterValue);
  }
}

class GetStudentsByClassIdUseCase {
  final CenterRepoImpl centerRepoImpl;
  GetStudentsByClassIdUseCase({required this.centerRepoImpl});
  Future<Either<Failure, List<ChildModel>>> call(
      {required String classId}) async {
    return await centerRepoImpl.getStudentsByClassId(classId: classId);
  }
}

class GetCoachAndCenterByClassIdUseCase {
  final CenterRepoImpl centerRepoImpl;
  GetCoachAndCenterByClassIdUseCase({required this.centerRepoImpl});
  Future<Either<Failure, ClassModel>> call({required String classId}) async {
    return await centerRepoImpl.getCoachAndCenterByClassId(classId: classId);
  }
}

class ClassSlotDeleteUseCase {
  final CenterRepoImpl centerRepoImpl;
  ClassSlotDeleteUseCase({required this.centerRepoImpl});
  Future<bool> call({
    required String classId,
    String? cancelType,
    String? eventId,
    String? newDate,
    String? newStartTime,
    String? newEndTime,
  }) async {
    return await centerRepoImpl.classSlotDelete(
      classId: classId,
      cancelType: cancelType,
      eventId: eventId,
      newDate: newDate,
      newStartTime: newStartTime,
      newEndTime: newEndTime,
    );
  }
}

class GetRequestSendedByCenterToCoachUseCase {
  final CenterRepoImpl centerRepoImpl;

  GetRequestSendedByCenterToCoachUseCase({required this.centerRepoImpl});
  Future<Either<Failure, List<String>>> call(
      {required String centerId, required String type}) async {
    return await centerRepoImpl.getRequestSendedByCenterToCoach(
        centerId: centerId, type: type);
  }
}

class GetAllClassesForParentUseCase {
  final CenterRepoDomain centerRepoDomain;

  GetAllClassesForParentUseCase({required this.centerRepoDomain});
  Future<Either<Failure, List<ClassModel>>> call(
      {required String centerId}) async {
    return await centerRepoDomain.getAllClassesForParent(centerId: centerId);
  }
}

class UpdateCoachInClassUseCase
    extends UseCase<bool, UpdateCoachInClassParams> {
  final CenterRepoDomain repository;

  UpdateCoachInClassUseCase(this.repository);

  @override
  Future<Either<Failure, bool>> call(UpdateCoachInClassParams params) async {
    return await repository.updateCoachInClass(
      classId: params.classId,
      coachId: params.coachId,
    );
  }
}

class UpdateCoachInClassParams extends Equatable {
  final String classId;
  final String coachId;

  const UpdateCoachInClassParams({
    required this.classId,
    required this.coachId,
  });

  @override
  List<Object?> get props => [classId, coachId];
}

class GetAllSlotsByClassIdUseCase {
  final CenterRepoDomain repo;
  GetAllSlotsByClassIdUseCase({required this.repo});
  Future<List<ClassDate>> call(String classId) async {
    return await repo.getAllSlotsByClassId(classId);
  }
}

class DeleteSlotByIdUseCase {
  final CenterRepoDomain repo;
  DeleteSlotByIdUseCase({required this.repo});
  Future<bool> call(String slotId) async {
    return await repo.deleteSlotById(slotId);
  }
}

class DeleteEventByIdUseCase {
  final CenterRepoDomain repo;
  DeleteEventByIdUseCase({required this.repo});
  Future<bool> call(String eventId) async {
    return await repo.deleteEventById(eventId);
  }
}
