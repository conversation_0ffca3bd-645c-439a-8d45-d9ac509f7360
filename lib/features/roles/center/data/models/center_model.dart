import 'package:class_z/core/imports.dart';
import 'package:hive/hive.dart';
import 'dart:convert';
part 'center_model.g.dart';

CentersModel centersModelFromJson(String str) =>
    CentersModel.fromJson(json.decode(str));

String centersModelToJson(CentersModel data) => json.encode(data.toJson());

@HiveType(typeId: 23)
class CentersModel {
  @HiveField(0)
  String? id;
  @HiveField(1)
  CenterData? centerData;

  CentersModel({
    this.id,
    this.centerData,
  });

  factory CentersModel.fromJson(Map<String, dynamic> json) => CentersModel(
        id: json["_id"],
        centerData: json["centerData"] == null
            ? null
            : CenterData.fromJson(json["centerData"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "centerData": centerData?.toJson(),
      };
  @override
  String toString() {
    return 'CentersModel(id: $id, centerData: $centerData)';
  }
}

@HiveType(typeId: 24)
class CenterData extends HiveObject {
  @HiveField(0)
  final List<CenterImage>? businessCertificate;

  @HiveField(1)
  final List<CenterImage>? hkidCard;

  @HiveField(2)
  final BusinessCertificate? mainImage;

  @HiveField(3)
  final String? id;

  @HiveField(4)
  final String? email;

  @HiveField(5)
  final String? password;

  @HiveField(6)
  final String? type;

  @HiveField(7)
  final String? t;

  @HiveField(8)
  final String? legalName;

  @HiveField(9)
  final String? displayName;

  @HiveField(10)
  final Address? address;

  @HiveField(11)
  final String? companyNumber;

  @HiveField(12)
  final String? centerNumber;

  @HiveField(13)
  final List<OpeningHour>? openingHours;

  @HiveField(14)
  final String? businessNumber;

  @HiveField(15)
  final List<String>? languages;

  @HiveField(16)
  final List<String>? services;

  @HiveField(17)
  final String? description;

  @HiveField(18)
  final List<CenterImage>? images;

  @HiveField(19)
  final BankDetails? bankDetails;

  @HiveField(20)
  final bool? isComplete;

  @HiveField(21)
  final DateTime? createdAt;

  @HiveField(22)
  final DateTime? updatedAt;

  @HiveField(23)
  final int? v;
  @HiveField(24)
  final String? startAge;
  @HiveField(25)
  final bool? sen;
  @HiveField(38)
  final bool? promotion;
  @HiveField(26)
  String? baseUser;
  @HiveField(27)
  List<String>? coachs;
  // @HiveField(28)
  // int? rating;
  @HiveField(29)
  int? reviewCount;
  @HiveField(30)
  List<String>? managers;
  @HiveField(31)
  String? coachId;
  @HiveField(32)
  double? rating;
  @HiveField(33)
  bool? verified;
  @HiveField(34)
  dynamic ownerId;
  @HiveField(35)
  String? classZId;
  @HiveField(36)
  final double? priceFrom;
  @HiveField(37)
  final double? priceTo;
  @HiveField(39)
  final List<CenterImage>? sexualConvictionRecord;
  @HiveField(40)
  final bool? isFreelanceEducator;
  CenterData(
      {this.businessCertificate,
      this.hkidCard,
      this.mainImage,
      this.id,
      this.email,
      this.password,
      this.type,
      this.t,
      this.legalName,
      this.displayName,
      this.address,
      this.companyNumber,
      this.centerNumber,
      this.openingHours,
      this.businessNumber,
      this.languages,
      this.services,
      this.description,
      this.images,
      this.bankDetails,
      this.isComplete,
      this.createdAt,
      this.updatedAt,
      this.v,
      this.startAge,
      this.sen,
      this.baseUser,
      this.coachs,
      //  this.rating,
      this.reviewCount,
      this.coachId,
      this.managers,
      this.rating,
      this.verified,
      this.ownerId,
      this.classZId,
      this.priceFrom,
      this.priceTo,
      this.promotion,
      this.sexualConvictionRecord,
      this.isFreelanceEducator});

  @override
  String toString() {
    return 'CenterData(businessCertificate: $businessCertificate, hkidCard: $hkidCard, mainImage: $mainImage, id: $id, email: $email, password: $password, type: $type, t: $t, legalName: $legalName, displayName: $displayName, startAge: $startAge, sen:$sen, promotion:$promotion, address: $address, companyNumber: $companyNumber, centerNumber: $centerNumber, openingHours: $openingHours, businessNumber: $businessNumber, languages: $languages, services: $services, description: $description, images: $images, bankDetails: $bankDetails, isComplete: $isComplete, createdAt: $createdAt, updatedAt: $updatedAt, v: $v)';
  }

  factory CenterData.fromJson(Map<String, dynamic> json) {
    try {
      return CenterData(
          mainImage: json['mainImage'] != null
              ? BusinessCertificate.fromJson(json['mainImage'])
              : null,
          id: json['_id'],
          baseUser: json["baseUser"] != null ? json["baseUser"] : null,
          email: json['email'] != null ? json['email'] : null,
          password: json['password'] != null ? json['password'] : null,
          type: json['type'] != null ? json['type'] : null,
          t: json['__t'] != null ? json['__t'] : null,
          legalName: json['legalName'] != null ? json['legalName'] : null,
          displayName: json['displayName'] != null ? json['displayName'] : null,
          startAge: json['startAge'] != null ? json['startAge'] : null,
          address: json['address'] != null
              ? Address.fromJson(json['address'])
              : null,
          companyNumber:
              json['companyNumber'] != null ? json['companyNumber'] : null,
          centerNumber:
              json['centerNumber'] != null ? json['centerNumber'] : null,
          openingHours: json['openingHour'] != null
              ? List<OpeningHour>.from(
                  json['openingHour'].map((x) => OpeningHour.fromJson(x)))
              : null,
          businessNumber: json['businessNumber'],
          languages: json['languages'] != null
              ? List<String>.from(json['languages'])
              : null,
          services: json['services'] != null
              ? List<String>.from(json['services'])
              : null,
          description: json['description'],
          images: json['images'] != null
              ? List<CenterImage>.from(
                  json['images'].map((x) => CenterImage.fromJson(x)))
              : null,
          bankDetails: json['bankDetails'] != null
              ? BankDetails.fromJson(json['bankDetails'])
              : null,
          isComplete: json['isComplete'] != null ? json['isComplete'] : null,
          createdAt: json['createdAt'] != null
              ? DateTime.parse(json['createdAt'].toString())
              : null,
          updatedAt: json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'].toString())
              : null,
          v: json['__v'] != null ? json['__v'] : null,
          rating: json['rating'] != null
              ? (json['rating'] as num).toDouble()
              : null,
          reviewCount: json['reviewCount'] != null ? json['reviewCount'] : null,
          coachs:
              json['coachs'] != null ? List<String>.from(json['coachs']) : null,
          managers: json['managers'] != null
              ? List<String>.from(json['managers'])
              : [],
          coachId: json['coachId'] != null ? json['coachId'] : null,
          verified: json['verified'] != null ? json['verified'] : null,
          ownerId: () {
            final ownerJson = json['owner'];

            if (ownerJson is String) {
              return ownerJson;
            }

            if (ownerJson is Map<String, dynamic>) {
              return OwnerModel.fromJson(ownerJson);
            }

            return '';
          }(),
          classZId: json['classzId'] != null ? json['classzId'] : null,
          priceFrom: json['priceFrom'] != null
              ? (json['priceFrom'] as num).toDouble()
              : null,
          priceTo: json['priceTo'] != null
              ? (json['priceTo'] as num).toDouble()
              : null,
          sen: json['sen'] != null ? json['sen'] : null,
          promotion: json['promotion'] != null ? json['promotion'] : null,
          sexualConvictionRecord: json['sexualConvictionRecord'] != null
              ? List<CenterImage>.from(
                  json['sexualConvictionRecord'].map((x) => CenterImage.fromJson(x)))
              : null,
          isFreelanceEducator: json['isFreelanceEducator'] != null ? json['isFreelanceEducator'] : null);
    } catch (e) {
      print(e.toString());
      rethrow;
    }
  }

  Map<String, dynamic> toJson() => {
        'businessCertificate':
            businessCertificate?.map((x) => x.toJson()).toList(),
        'hkidCard': hkidCard?.map((x) => x.toJson()).toList(),
        'mainImage': mainImage?.toJson(),
        '_id': id,
        "baseUser": baseUser,
        'email': email,
        'password': password,
        'type': type,
        '__t': t,
        'legalName': legalName,
        'displayName': displayName,
        'startAge': startAge,
        'sen': sen,
        'address': address?.toJson(),
        'companyNumber': companyNumber,
        'centerNumber': centerNumber,
        'openingHour': openingHours?.map((x) => x.toJson()).toList(),
        'businessNumber': businessNumber,
        'languages': languages,
        'services': services,
        'description': description,
        'images': images?.map((x) => x.toJson()).toList(),
        'bankDetails': bankDetails?.toJson(),
        'isComplete': isComplete,
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
        '__v': v,
        'coachs': coachs,
        'rating': rating,
        // 'reviewCount':reviewCount
        'managers': managers,
        'coachId': coachId,
        'verified': verified,
        'owner': ownerId,
        'classzId': classZId,
        'priceFrom': priceFrom,
        'priceTo': priceTo,
        'promotion': promotion,
        'sexualConvictionRecord':
            sexualConvictionRecord?.map((x) => x.toJson()).toList(),
        'isFreelanceEducator': isFreelanceEducator,
      };
}

@HiveType(typeId: 25)
class Address {
  @HiveField(0)
  final String? address1;

  @HiveField(1)
  final String? address2;

  @HiveField(2)
  final String? city;

  @HiveField(3)
  final String? region;

  @HiveField(4)
  final String? id;

  // Add coordinates field
  final Map<String, dynamic>? coordinates;

  // Add country field
  @HiveField(5)
  final String? country;

  Address({
    this.address1,
    this.address2,
    this.city,
    this.region,
    this.id,
    this.coordinates,
    this.country,
  });

  @override
  String toString() {
    return ' $address1/$address2, $city, $region${country != null ? ', $country' : ''}';
  }

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        address1: json['address1'],
        address2: json['address2'],
        city: json['city'],
        region: json['region'],
        id: json['_id'],
        coordinates: json['coordinates'],
        country: json['country'],
      );

  Map<String, dynamic> toJson() => {
        'address1': address1,
        'city': city,
        'region': region,
        '_id': id,
        'coordinates': coordinates,
        'address2': address2,
        'country': country,
      };
}

@HiveType(typeId: 26)
class BankDetails {
  @HiveField(0)
  final String? bankName;

  @HiveField(1)
  final String? accountHolderName;

  @HiveField(2)
  final String? bankCode;

  @HiveField(3)
  final String? branchCode;

  @HiveField(4)
  final String? accountNumber;

  @HiveField(5)
  final String? id;

  BankDetails({
    this.bankName,
    this.accountHolderName,
    this.bankCode,
    this.branchCode,
    this.accountNumber,
    this.id,
  });

  @override
  String toString() {
    return 'BankDetails(bankName: $bankName, accountHolderName: $accountHolderName, bankCode: $bankCode, branchCode: $branchCode, accountNumber: $accountNumber, id: $id)';
  }

  factory BankDetails.fromJson(Map<String, dynamic> json) => BankDetails(
        bankName: json['bankName'],
        accountHolderName: json['accountHolderName'],
        bankCode: json['bankCode'],
        branchCode: json['branchCode'],
        accountNumber: json['accountNumber'],
        id: json['_id'],
      );

  Map<String, dynamic> toJson() => {
        'bankName': bankName,
        'accountHolderName': accountHolderName,
        'bankCode': bankCode,
        'branchCode': branchCode,
        'accountNumber': accountNumber,
        '_id': id,
      };
}

@HiveType(typeId: 27)
class BusinessCertificate {
  @HiveField(0)
  final String? url;

  @HiveField(1)
  final String? contentType;

  BusinessCertificate({
    this.url,
    this.contentType,
  });

  @override
  String toString() {
    return 'BusinessCertificate(url: $url, contentType: $contentType)';
  }

  factory BusinessCertificate.fromJson(Map<String, dynamic> json) =>
      BusinessCertificate(
        url: json['url'],
        contentType: json['contentType'],
      );

  Map<String, dynamic> toJson() => {
        'url': url,
        'contentType': contentType,
      };
}

@HiveType(typeId: 28)
class CenterImage {
  @HiveField(0)
  final String? url;

  @HiveField(1)
  final String? contentType;

  CenterImage({
    this.url,
    this.contentType,
  });

  @override
  String toString() {
    return 'CenterImage(url: $url, contentType: $contentType)';
  }

  factory CenterImage.fromJson(Map<String, dynamic> json) => CenterImage(
        url: json['url'],
        contentType: json['contentType'],
      );

  Map<String, dynamic> toJson() => {
        'url': url,
        'contentType': contentType,
      };
}

@HiveType(typeId: 29)
class OpeningHour {
  @HiveField(0)
  final String? day;

  @HiveField(1)
  final String? openingTime;

  @HiveField(2)
  final String? closingTime;

  OpeningHour({
    this.day,
    this.openingTime,
    this.closingTime,
  });

  @override
  String toString() {
    return 'OpeningHour(day: $day, openingTime: $openingTime, closingTime: $closingTime)';
  }

  factory OpeningHour.fromJson(Map<String, dynamic> json) => OpeningHour(
        day: json['day'],
        openingTime: json['openingTime'],
        closingTime: json['closingTime'],
      );

  Map<String, dynamic> toJson() => {
        'day': day,
        'openingTime': openingTime,
        'closingTime': closingTime,
      };
}
