// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'center_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CentersModelAdapter extends TypeAdapter<CentersModel> {
  @override
  final int typeId = 23;

  @override
  CentersModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CentersModel(
      id: fields[0] as String?,
      centerData: fields[1] as CenterData?,
    );
  }

  @override
  void write(BinaryWriter writer, CentersModel obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.centerData);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CentersModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CenterDataAdapter extends TypeAdapter<CenterData> {
  @override
  final int typeId = 24;

  @override
  CenterData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CenterData(
      businessCertificate: (fields[0] as List?)?.cast<CenterImage>(),
      hkidCard: (fields[1] as List?)?.cast<CenterImage>(),
      mainImage: fields[2] as BusinessCertificate?,
      id: fields[3] as String?,
      email: fields[4] as String?,
      password: fields[5] as String?,
      type: fields[6] as String?,
      t: fields[7] as String?,
      legalName: fields[8] as String?,
      displayName: fields[9] as String?,
      address: fields[10] as Address?,
      companyNumber: fields[11] as String?,
      centerNumber: fields[12] as String?,
      openingHours: (fields[13] as List?)?.cast<OpeningHour>(),
      businessNumber: fields[14] as String?,
      languages: (fields[15] as List?)?.cast<String>(),
      services: (fields[16] as List?)?.cast<String>(),
      description: fields[17] as String?,
      images: (fields[18] as List?)?.cast<CenterImage>(),
      bankDetails: fields[19] as BankDetails?,
      isComplete: fields[20] as bool?,
      createdAt: fields[21] as DateTime?,
      updatedAt: fields[22] as DateTime?,
      v: fields[23] as int?,
      startAge: fields[24] as String?,
      sen: fields[25] as bool?,
      baseUser: fields[26] as String?,
      coachs: (fields[27] as List?)?.cast<String>(),
      reviewCount: fields[29] as int?,
      coachId: fields[31] as String?,
      managers: (fields[30] as List?)?.cast<String>(),
      rating: fields[32] as double?,
      verified: fields[33] as bool?,
      ownerId: fields[34] as dynamic,
      classZId: fields[35] as String?,
      priceFrom: fields[36] as double?,
      priceTo: fields[37] as double?,
      promotion: fields[38] as bool?,
      sexualConvictionRecord: (fields[39] as List?)?.cast<CenterImage>(),
      isFreelanceEducator: fields[40] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, CenterData obj) {
    writer
      ..writeByte(40)
      ..writeByte(0)
      ..write(obj.businessCertificate)
      ..writeByte(1)
      ..write(obj.hkidCard)
      ..writeByte(2)
      ..write(obj.mainImage)
      ..writeByte(3)
      ..write(obj.id)
      ..writeByte(4)
      ..write(obj.email)
      ..writeByte(5)
      ..write(obj.password)
      ..writeByte(6)
      ..write(obj.type)
      ..writeByte(7)
      ..write(obj.t)
      ..writeByte(8)
      ..write(obj.legalName)
      ..writeByte(9)
      ..write(obj.displayName)
      ..writeByte(10)
      ..write(obj.address)
      ..writeByte(11)
      ..write(obj.companyNumber)
      ..writeByte(12)
      ..write(obj.centerNumber)
      ..writeByte(13)
      ..write(obj.openingHours)
      ..writeByte(14)
      ..write(obj.businessNumber)
      ..writeByte(15)
      ..write(obj.languages)
      ..writeByte(16)
      ..write(obj.services)
      ..writeByte(17)
      ..write(obj.description)
      ..writeByte(18)
      ..write(obj.images)
      ..writeByte(19)
      ..write(obj.bankDetails)
      ..writeByte(20)
      ..write(obj.isComplete)
      ..writeByte(21)
      ..write(obj.createdAt)
      ..writeByte(22)
      ..write(obj.updatedAt)
      ..writeByte(23)
      ..write(obj.v)
      ..writeByte(24)
      ..write(obj.startAge)
      ..writeByte(25)
      ..write(obj.sen)
      ..writeByte(38)
      ..write(obj.promotion)
      ..writeByte(26)
      ..write(obj.baseUser)
      ..writeByte(27)
      ..write(obj.coachs)
      ..writeByte(29)
      ..write(obj.reviewCount)
      ..writeByte(30)
      ..write(obj.managers)
      ..writeByte(31)
      ..write(obj.coachId)
      ..writeByte(32)
      ..write(obj.rating)
      ..writeByte(33)
      ..write(obj.verified)
      ..writeByte(34)
      ..write(obj.ownerId)
      ..writeByte(35)
      ..write(obj.classZId)
      ..writeByte(36)
      ..write(obj.priceFrom)
      ..writeByte(37)
      ..write(obj.priceTo)
      ..writeByte(39)
      ..write(obj.sexualConvictionRecord)
      ..writeByte(40)
      ..write(obj.isFreelanceEducator);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CenterDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AddressAdapter extends TypeAdapter<Address> {
  @override
  final int typeId = 25;

  @override
  Address read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Address(
      address1: fields[0] as String?,
      address2: fields[1] as String?,
      city: fields[2] as String?,
      region: fields[3] as String?,
      id: fields[4] as String?,
      country: fields[5] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Address obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.address1)
      ..writeByte(1)
      ..write(obj.address2)
      ..writeByte(2)
      ..write(obj.city)
      ..writeByte(3)
      ..write(obj.region)
      ..writeByte(4)
      ..write(obj.id)
      ..writeByte(5)
      ..write(obj.country);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AddressAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BankDetailsAdapter extends TypeAdapter<BankDetails> {
  @override
  final int typeId = 26;

  @override
  BankDetails read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BankDetails(
      bankName: fields[0] as String?,
      accountHolderName: fields[1] as String?,
      bankCode: fields[2] as String?,
      branchCode: fields[3] as String?,
      accountNumber: fields[4] as String?,
      id: fields[5] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, BankDetails obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.bankName)
      ..writeByte(1)
      ..write(obj.accountHolderName)
      ..writeByte(2)
      ..write(obj.bankCode)
      ..writeByte(3)
      ..write(obj.branchCode)
      ..writeByte(4)
      ..write(obj.accountNumber)
      ..writeByte(5)
      ..write(obj.id);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BankDetailsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BusinessCertificateAdapter extends TypeAdapter<BusinessCertificate> {
  @override
  final int typeId = 27;

  @override
  BusinessCertificate read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BusinessCertificate(
      url: fields[0] as String?,
      contentType: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, BusinessCertificate obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.url)
      ..writeByte(1)
      ..write(obj.contentType);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BusinessCertificateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CenterImageAdapter extends TypeAdapter<CenterImage> {
  @override
  final int typeId = 28;

  @override
  CenterImage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CenterImage(
      url: fields[0] as String?,
      contentType: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, CenterImage obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.url)
      ..writeByte(1)
      ..write(obj.contentType);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CenterImageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class OpeningHourAdapter extends TypeAdapter<OpeningHour> {
  @override
  final int typeId = 29;

  @override
  OpeningHour read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return OpeningHour(
      day: fields[0] as String?,
      openingTime: fields[1] as String?,
      closingTime: fields[2] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, OpeningHour obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.day)
      ..writeByte(1)
      ..write(obj.openingTime)
      ..writeByte(2)
      ..write(obj.closingTime);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OpeningHourAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
