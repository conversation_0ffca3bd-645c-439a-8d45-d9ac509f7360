import 'package:class_z/core/imports.dart';
import 'package:dartz/dartz.dart';

class CenterRepoImpl extends CenterRepoDomain {
  final CenterDataSource centerDataSource;

  CenterRepoImpl({required this.centerDataSource});
  @override
  Future<bool> createCenterBranch(
      {required Map<String, dynamic> data,
      File? mainImage,
      File? businessCertificate,
      File? hkidCard,
      List<File>? images}) async {
    try {
      return await centerDataSource.CreateBranchCenter(
          data: data,
          mainImage: mainImage,
          hkidCard: hkidCard,
          businessCertificate: businessCertificate,
          images: images);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<CenterData> centerInfoComplete() async {
    try {
      final CenterData centerModel =
          await centerDataSource.centerInfoComplete();
      return _mapCenterModelToCenter(centerModel);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<CenterData> getCenter(String id) async {
    try {
      // Fetch the CenterModel from the data source using the provided ID
      return await centerDataSource.getCenter(id);
    } catch (e) {
      // Handle or rethrow the exception as needed
      rethrow;
    }
  }

  @override
  Future<List<CenterData>> getAllCenter(
      {required int page, required int limit}) async {
    try {
      return await centerDataSource.getAllCenter(page: page, limit: limit);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<CoachModel>> getCoachsByCenterId({required String id}) async {
    try {
      return await centerDataSource.getCoachsByCenterId(id: id);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<CoachModel>> getManagersByCenterId({required String id}) async {
    try {
      return await centerDataSource.getManagersByCenterId(id: id);
    } catch (e) {
      rethrow;
    }
  }

  // Helper method to map CenterModel to Center
  CenterData _mapCenterModelToCenter(CenterData centerModel) {
    return CenterData(
      id: centerModel.id,
      email: centerModel.email,
      password: centerModel.password,
      type: centerModel.type,
      t: centerModel.t,
      legalName: centerModel.legalName,
      displayName: centerModel.displayName,
      address: centerModel.address,
      companyNumber: centerModel.companyNumber,
      centerNumber: centerModel.centerNumber,
      openingHours: centerModel.openingHours,
      businessNumber: centerModel.businessNumber,
      languages: centerModel.languages,
      services: centerModel.services,
      description: centerModel.description,
      images: centerModel.images,
      bankDetails: centerModel.bankDetails,
      isComplete: centerModel.isComplete,
      businessCertificate: centerModel.businessCertificate,
      hkidCard: centerModel.hkidCard,
      mainImage: centerModel.mainImage,
      createdAt: centerModel.createdAt,
      updatedAt: centerModel.updatedAt,
      v: centerModel.v,
    );
  }

  @override
  Future<CenterData> UpdateCenter(
      {required String centerId, required Map<String, dynamic> data}) async {
    try {
      return await centerDataSource.updateCenter(
          centerId: centerId, data: data);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<String?> addClass(
      {required String classProviding,
      required String category,
      String? level,
      required String description,
      required bool mode,
      bool? sen,
      String? initialCharge,
      required String charge,
      required String from,
      required String to,
      File? image}) async {
    try {
      return await centerDataSource.addClass(
          classProviding: classProviding,
          category: category,
          description: description,
          mode: mode,
          charge: charge,
          image: image,
          initialCharge: initialCharge,
          level: level,
          sen: sen,
          from: from,
          to: to);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> deleteClass({required String id}) async {
    try {
      return await centerDataSource.deleteClass(id: id);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<Either<Failure, List<ClassModel>>> getAllClassByCenterId(
      String centerId) async {
    try {
      var response = await centerDataSource.getAllClassByCenterId(centerId);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<ClassModel>>> getAllClassByCoachId(
      String centerId) async {
    try {
      var response = await centerDataSource.getAllClassByCoachId(centerId);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<bool> updateClass(
      {required String classId,
      required Map<String, dynamic> updatedData}) async {
    try {
      return await centerDataSource.updateClass(
          classId: classId, updatedData: updatedData);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<EventModel>> getAllEvent(
      {required String filterType,
      required String filterValue,
      String? date}) async {
    try {
      return await centerDataSource.getAllEvents(
          filterType: filterType, filterValue: filterValue, date: date);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<Either<Failure, List<String>>> getEventDates(
      {required String filterType, required String filterValue}) async {
    try {
      final response = await centerDataSource.getEventDates(
          filterType: filterType, filterValue: filterValue);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<bool> deleteEventsbyEventId({required String eventId}) async {
    try {
      return await centerDataSource.deleteEventsbyEventId(eventId: eventId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<EventModel>> getEventsByDate({required String id}) async {
    try {
      return await centerDataSource.getEventsByDate(id: id);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<EventDatesModel> getEventsByClassId({required String classId}) async {
    try {
      return await centerDataSource.getEventsByClassId(classId: classId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<EventModel> getEventsByEventId({required String eventId}) async {
    try {
      return await centerDataSource.getEventsByEventId(eventId: eventId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<AttendanceModel> postAttendance(
      {String? classId,
      String? code,
      String? qrCodeData,
      String? classDate}) async {
    try {
      return await centerDataSource.postAttendance(
          classId: classId,
          code: code,
          qrCodeData: qrCodeData,
          classDate: classDate);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<AttendanceModel>> getPresentAttendance(
      {required String classId, required String classDate}) async {
    try {
      return await centerDataSource.getPresentAttendance(
          classId: classId, classDate: classDate);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<PendingModel>> getPendingReview({required String id}) async {
    try {
      return await centerDataSource.getPendingReview(id: id);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<Either<Failure, List<PendingModel>>> getPendingReviewByClassId(
      {required String classId}) async {
    try {
      var response =
          await centerDataSource.getPendingReviewByClassId(classId: classId);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<PendingModel>>> getPendingReviewByCoachId(
      {required String coachId}) async {
    try {
      var response =
          await centerDataSource.getPendingReviewByCoachId(coachId: coachId);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<ChildModel>>> getStudentsByClassId(
      {required String classId}) async {
    try {
      var response =
          await centerDataSource.getStudentsByClassId(classId: classId);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, ClassModel>> getCoachAndCenterByClassId(
      {required String classId}) async {
    try {
      var response =
          await centerDataSource.getCoachAndCenterByClassId(classId: classId);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<bool> classSlotDelete({
    required String classId,
    String? cancelType,
    String? eventId,
    String? newDate,
    String? newStartTime,
    String? newEndTime,
  }) async {
    try {
      return await centerDataSource.classSlotDelete(
        classId: classId,
        cancelType: cancelType,
        eventId: eventId,
        newDate: newDate,
        newStartTime: newStartTime,
        newEndTime: newEndTime,
      );
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<Either<Failure, List<String>>> getRequestSendedByCenterToCoach(
      {required String centerId, required String type}) async {
    try {
      var response = await centerDataSource.getRequestSendedByCenterToCoach(
          centerId: centerId, type: type);
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<ClassModel>>> getAllClassesForParent(
      {required String centerId}) async {
    try {
      var response =
          await centerDataSource.getAllClassesForParent(centerId: centerId);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> updateCoachInClass({
    required String classId,
    required String coachId,
  }) async {
    try {
      final result = await centerDataSource.updateCoachInClass(
        classId: classId,
        coachId: coachId,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    } catch (e) {
      return Left(ServerFailure(message: "An unexpected error occurred"));
    }
  }

  @override
  Future<List<ClassDate>> getAllSlotsByClassId(String classId) async {
    return await centerDataSource.getAllSlotsByClassId(classId);
  }

  @override
  Future<bool> deleteSlotById(String slotId) async {
    return await centerDataSource.deleteSlotById(slotId);
  }

  @override
  Future<bool> deleteEventById(String eventId) async {
    return await centerDataSource.deleteEventById(eventId);
  }
}
