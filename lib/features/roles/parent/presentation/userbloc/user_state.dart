part of 'user_bloc.dart';

abstract class UserState {}

class UserInitial extends UserState {}

class UserLoadingState extends UserState {}

class UserErrorState extends UserState {
  final String message;

  UserErrorState({required this.message});
}

class CreateChildSuccessState extends UserState {
  final ChildModel child;

  CreateChildSuccessState({required this.child});
}

class UpdateChildSuccessState extends UserState {
  final ChildModel child;

  UpdateChildSuccessState({required this.child});
}

class GetChildByParentIdSuccessState extends UserState {
  final List<ChildModel> child;

  GetChildByParentIdSuccessState({required this.child});
}

class DeleteChildByIdSuccessState extends UserState {
  final bool child;

  DeleteChildByIdSuccessState({required this.child});
}

class createOrderSuccessState extends UserState {
  final bool success;

  createOrderSuccessState({required this.success});
}

class GetAllOrdersSuccessState extends UserState {
  final List<OrderModel> orders;

  GetAllOrdersSuccessState({required this.orders});
}

class GetOrdersByUserSuccessState extends UserState {
  final List<GetOrderByUserModel> orders;

  GetOrdersByUserSuccessState({required this.orders});
}

class QrCodeGenerateForUserSuccessState extends UserState {
  final QrCodeModel code;

  QrCodeGenerateForUserSuccessState({required this.code});
}

class GetReviewByChildByIdSuccessState extends UserState {
  final ReviewResponseModel reviews;

  GetReviewByChildByIdSuccessState({required this.reviews});
}

class GetPurchasedHistorySuccessState extends UserState {
  final List<PurchasedHistoryModel> purchased;

  GetPurchasedHistorySuccessState({required this.purchased});
}

class GetBalanceSuccessState extends UserState {
  final BalanceModel balance;

  GetBalanceSuccessState({required this.balance});
}

class GetCardSuccessState extends UserState {
  final CardModel card;

  GetCardSuccessState({required this.card});
}

class DeleteCardSuccessState extends UserState {}

class GetDiscountSuccessState extends UserState {
  final List<DiscountEntity> discounts;

  GetDiscountSuccessState({required this.discounts});
}

class UpdateAddressSuccessState extends UserState {
  final bool success;

  UpdateAddressSuccessState({required this.success});
}

class DeleteAddressSuccessState extends UserState {
  final bool success;

  DeleteAddressSuccessState({required this.success});
}

class ChangeDefaultAddressSuccessState extends UserState {
  final bool success;

  ChangeDefaultAddressSuccessState({required this.success});
}

class ContactUsSuccessState extends UserState {
  final bool success;

  ContactUsSuccessState({required this.success});
}

class GetChildPerformanceMetricsSuccessState extends UserState {
  final Map<String, dynamic> metrics;

  GetChildPerformanceMetricsSuccessState({required this.metrics});
}

class ParentPendingReviewsSuccessState extends UserState {
  final ReviewResponse pendingReviews;

  ParentPendingReviewsSuccessState({required this.pendingReviews});

  @override
  List<Object?> get props => [pendingReviews];
}

class ParentReviewHistoryLoadingState extends UserState {}

class ParentReviewHistorySuccessState extends UserState {
  final List<ReviewOfChildModel> history;

  ParentReviewHistorySuccessState({required this.history});

  @override
  List<Object?> get props => [history];
}

class ParentReviewHistoryErrorState extends UserState {
  final String message;

  ParentReviewHistoryErrorState({required this.message});

  @override
  List<Object?> get props => [message];
}

class RefundLoadingState extends UserState {}

class RefundSuccessState extends UserState {
  final List<RefundModel> refunds;
  RefundSuccessState({required this.refunds});
}

class RefundErrorState extends UserState {
  final String message;
  RefundErrorState({required this.message});
}

class GetTimeTableByParentIdSuccessState extends UserState {
  final TimetableUpModel timetableList;
  GetTimeTableByParentIdSuccessState({required this.timetableList});
}
///Many things done