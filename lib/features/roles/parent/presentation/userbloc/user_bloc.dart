import 'package:class_z/core/common/data/models/pending_for_parent.dart';
import 'package:class_z/core/imports.dart';
import 'package:class_z/features/roles/parent/domain/usecases/get_child_performance_metrics_usecase.dart';
import 'package:class_z/features/roles/parent/domain/usecases/get_parent_pending_reviews_usecase.dart';
import 'package:class_z/features/roles/parent/domain/usecases/get_parent_review_history_usecase.dart';

part 'user_event.dart';
part 'user_state.dart';

class UserBloc extends Bloc<UserEvent, UserState> {
  final CreateChildUseCase createChildUseCase;
  final UpdateChildUseCase updateChildUseCase;
  final GetChildByParentIdUseCase getChildByParentIdUseCase;
  final DeleteChildByIdUseCase deleteChildByIdUseCase;
  final OrderUseCase orderUseCase;
  final QrCodeUseCase qrCodeUseCase;
  final GetHistoryOfChildIdUseCase getHistoryOfChildIdUseCase;
  final GetPurchasedHistoryUseCase getPurchasedHistoryUseCase;
  final GetBalanceUseCase getBalanceUseCase;
  final GetCardUseCase getCardUseCase;
  final DeleteCardUseCase deleteCardUseCase;
  final GetDiscountUseCase getDiscountUseCase;
  final UpdateAddressUseCase updateAddressUseCase;
  final DeleteAddressUseCase deleteAddressUseCase;
  final ChangeDefaultAddressUseCase changeDefaultAddressUseCase;
  final ContactUsUseCase contactUsUseCase;
  final GetChildPerformanceMetricsUseCase getChildPerformanceMetricsUseCase;
  final GetParentPendingReviewsUseCase getParentPendingReviewsUseCase;
  final GetParentReviewHistoryUseCase getParentReviewHistoryUseCase;
  final RefundUseCase refundUseCase;
  final GetTimeTableByParentIdUseCase getTimeTableByParentIdUseCase;

  UserBloc({
    required this.createChildUseCase,
    required this.updateChildUseCase,
    required this.getChildByParentIdUseCase,
    required this.deleteChildByIdUseCase,
    required this.orderUseCase,
    required this.qrCodeUseCase,
    required this.getHistoryOfChildIdUseCase,
    required this.getPurchasedHistoryUseCase,
    required this.getBalanceUseCase,
    required this.getCardUseCase,
    required this.deleteCardUseCase,
    required this.getDiscountUseCase,
    required this.deleteAddressUseCase,
    required this.updateAddressUseCase,
    required this.changeDefaultAddressUseCase,
    required this.contactUsUseCase,
    required this.getChildPerformanceMetricsUseCase,
    required this.getParentPendingReviewsUseCase,
    required this.getParentReviewHistoryUseCase,
    required this.refundUseCase,
    required this.getTimeTableByParentIdUseCase,
  }) : super(UserInitial()) {
    on<CreateChildEvent>(_onCreateChildEvent);
    on<UpdateChildEvent>(_onUpdateChildEvent);
    on<GetChildByParentIdEvent>(_onGetChildByParentIdEvent);
    on<DeleteChildByIdEvent>(_deleteChildByIdEvent);
    on<CreateOrderEvent>(createOrderEvent);
    on<GetOrdersByUserEvent>(getOrdersByUserEvent);
    on<GetAllOrdersEvent>(getAllOrdersEvent);
    on<QrCodeGenerateForUserEvent>(qrCodeGenerateForUserEvent);
    on<GetHistoryOfChildIdEvent>(_getHistoryOfChildIdEvent);
    on<GetPurchasedHistoryEvent>(getPurchasedHistoryEvent);
    on<GetBalanceEvent>(getBalanceEvent);
    on<GetCardEvent>(getCardEvent);
    on<DeleteCardEvent>(_onDeleteCardEvent);
    on<GetDiscountEvent>(_getDiscountEvent);
    on<UpdateAddressEvent>(_updateAddressEvent);
    on<DeleteAddressEvent>(_deleteAddressEvent);
    on<ChangeDefaultAddressEvent>(_changeDefaultAddressChangeEvent);
    on<ContactUsEvent>(_contactUsEvent);
    on<GetChildPerformanceMetricsEvent>(_getChildPerformanceMetricsEvent);
    on<GetParentPendingReviewsEvent>(_onGetParentPendingReviewsEvent);
    on<GetParentReviewHistoryEvent>(_onGetParentReviewHistoryEvent);
    on<RefundEvent>(_onRefundEvent);
    on<GetTimeTableByParentIdEvent>(_onGetTimeTableByParentIdEvent);
  }
  List<GetOrderByUserModel> _orders = [];
  List<GetOrderByUserModel> get orders => _orders;
  FutureOr<void> _onCreateChildEvent(
      CreateChildEvent event, Emitter<UserState> emit) async {
    try {
      emit(UserLoadingState());
      final child = await createChildUseCase.call(
          parentId: event.parentId, data: event.data);
      emit(CreateChildSuccessState(child: child));
    } catch (e) {
      emit(UserErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _onUpdateChildEvent(
      UpdateChildEvent event, Emitter<UserState> emit) async {
    try {
      emit(UserLoadingState());
      final child = await updateChildUseCase.call(
          childId: event.childId, data: event.data);
      emit(UpdateChildSuccessState(child: child));
    } catch (e) {
      emit(UserErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _onGetChildByParentIdEvent(
      GetChildByParentIdEvent event, Emitter<UserState> emit) async {
    try {
      print("Getting child information for parent");
      emit(UserLoadingState());

      // Add a small delay to ensure the loading state is visible
      await Future.delayed(Duration(milliseconds: 200));

      try {
        final children = await getChildByParentIdUseCase.call(event.parentId);
        // Successfully got the list (even if it's empty)
        print("Child information retrieved: ${children.length} children found");
        emit(GetChildByParentIdSuccessState(child: children));
      } catch (innerError) {
        print("Inner error getting child data: $innerError");
        // Emit success with empty list rather than error to show the "Add Child" UI
        emit(GetChildByParentIdSuccessState(child: []));
      }
    } catch (e) {
      print("Error getting child data: $e");
      // Emit success with empty list for a better UX
      emit(GetChildByParentIdSuccessState(child: []));
    }
  }

  FutureOr<void> _deleteChildByIdEvent(
      DeleteChildByIdEvent event, Emitter<UserState> emit) async {
    try {
      emit(UserLoadingState());
      final child = await deleteChildByIdUseCase(event.id);
      emit(DeleteChildByIdSuccessState(child: child));
    } catch (e) {
      emit(UserErrorState(message: e.toString()));
    }
  }

  FutureOr<void> createOrderEvent(
      CreateOrderEvent event, Emitter<UserState> emit) async {
    try {
      emit(UserLoadingState());
      bool success = await orderUseCase.createOrder(orderData: event.orderData);
      emit(createOrderSuccessState(success: success));
    } catch (e) {
      emit(UserErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getAllOrdersEvent(
      GetAllOrdersEvent event, Emitter<UserState> emit) async {
    try {
      //print("object");
      //print(StackTrace.current);
      emit(UserLoadingState());
      List<OrderModel> orders = await orderUseCase.getAllOrder(
          skip: event.skip, limit: event.limit, paid: event.paid);
      emit(GetAllOrdersSuccessState(orders: orders));
    } catch (e) {
      emit(UserErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getOrdersByUserEvent(
      GetOrdersByUserEvent event, Emitter<UserState> emit) async {
    try {
      emit(UserLoadingState());
      List<GetOrderByUserModel> orders = await orderUseCase.getByUserOrder();
      _orders = orders;
      emit(GetOrdersByUserSuccessState(orders: orders));
    } catch (e) {
      emit(UserErrorState(message: e.toString()));
    }
  }

  FutureOr<void> qrCodeGenerateForUserEvent(
      QrCodeGenerateForUserEvent event, Emitter<UserState> emit) async {
    try {
      emit(UserLoadingState());
      QrCodeModel code = await qrCodeUseCase.generateQrCodeForUser(
          classId: event.classId,
          studentId: event.studentId,
          classDate: event.classDate);
      emit(QrCodeGenerateForUserSuccessState(code: code));
    } catch (e) {
      emit(UserErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _getHistoryOfChildIdEvent(
      GetHistoryOfChildIdEvent event, Emitter<UserState> emit) async {
    try {
      emit(UserLoadingState());
      ReviewResponseModel reviews =
          await getHistoryOfChildIdUseCase.call(childId: event.childId);
      emit(GetReviewByChildByIdSuccessState(reviews: reviews));
    } catch (e) {
      emit(UserErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getPurchasedHistoryEvent(
      GetPurchasedHistoryEvent event, Emitter<UserState> emit) async {
    try {
      emit(UserLoadingState());
      List<PurchasedHistoryModel> purchased =
          await getPurchasedHistoryUseCase.call();
      emit(GetPurchasedHistorySuccessState(purchased: purchased));
    } catch (e) {
      emit(UserErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getBalanceEvent(
      GetBalanceEvent event, Emitter<UserState> emit) async {
    try {
      emit(UserLoadingState());

      // Add a short delay to ensure loading state is visible
      await Future.delayed(Duration(milliseconds: 100));

      // Wrap this in another try-catch to handle any unexpected errors
      try {
        BalanceModel balance = await getBalanceUseCase.call();
        emit(GetBalanceSuccessState(balance: balance));
      } catch (innerError) {
        // Log the inner error
        print('Inner error in getBalanceEvent: $innerError');
        // Always emit success with a default balance model
        emit(GetBalanceSuccessState(balance: BalanceModel(balance: 0)));
      }
    } catch (e) {
      // Log the error but emit success state with default balance
      print('Error in getBalanceEvent: $e');
      // Always emit success with a default balance model
      emit(GetBalanceSuccessState(balance: BalanceModel(balance: 0)));
    }
  }

  FutureOr<void> getCardEvent(
      GetCardEvent event, Emitter<UserState> emit) async {
    try {
      emit(UserLoadingState());
      CardModel card = await getCardUseCase.call();
      emit(GetCardSuccessState(card: card));
    } catch (e) {
      // Log the error but emit success state with default card
      print('Error in getCardEvent: $e');
      // Instead of emitting error state, emit success with default card
      emit(GetCardSuccessState(card: CardModel()));
    }
  }

  FutureOr<void> _onDeleteCardEvent(
      DeleteCardEvent event, Emitter<UserState> emit) async {
    try {
      emit(UserLoadingState());
      await deleteCardUseCase.call();
      emit(DeleteCardSuccessState());
    } catch (e) {
      emit(UserErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _getDiscountEvent(
      GetDiscountEvent event, Emitter<UserState> emit) async {
    emit(UserLoadingState());
    final discounts = await this.getDiscountUseCase.call(NoParams());
    discounts.fold((failure) => UserErrorState(message: failure.message),
        (discounts) => emit(GetDiscountSuccessState(discounts: discounts)));
  }

  FutureOr<void> _updateAddressEvent(
      UpdateAddressEvent event, Emitter<UserState> emit) async {
    emit(UserLoadingState());
    final success =
        await this.updateAddressUseCase.call(data: event.data, id: event.id);
    print('bloc sucess: $success');
    success.fold((failure) => emit(UserErrorState(message: failure.message)),
        (success) => emit(UpdateAddressSuccessState(success: success)));
  }

  FutureOr<void> _deleteAddressEvent(
      DeleteAddressEvent event, Emitter<UserState> emit) async {
    emit(UserLoadingState());
    final success = await this
        .deleteAddressUseCase
        .call(id: event.id, addressId: event.addressId);
    success.fold((failure) => emit(UserErrorState(message: failure.message)),
        (success) => emit(DeleteAddressSuccessState(success: success)));
  }

  FutureOr<void> _changeDefaultAddressChangeEvent(
      ChangeDefaultAddressEvent event, Emitter<UserState> emit) async {
    print('address id: ${event.addressId}');
    print('id: ${event.id}');
    emit(UserLoadingState());
    final success = await this
        .changeDefaultAddressUseCase
        .call(id: event.id, addressId: event.addressId);
    success.fold((failure) => emit(UserErrorState(message: failure.message)),
        (success) => emit(ChangeDefaultAddressSuccessState(success: success)));
  }

  FutureOr<void> _contactUsEvent(
      ContactUsEvent event, Emitter<UserState> emit) async {
    emit(UserLoadingState());
    final success =
        await this.contactUsUseCase.call(email: event.email, data: event.data);
    success.fold((failure) => emit(UserErrorState(message: failure.message)),
        (success) => emit(ContactUsSuccessState(success: success)));
  }

  FutureOr<void> _getChildPerformanceMetricsEvent(
      GetChildPerformanceMetricsEvent event, Emitter<UserState> emit) async {
    try {
      emit(UserLoadingState());
      final result = await getChildPerformanceMetricsUseCase(
          Params(childId: event.childId));

      result.fold(
          (failure) => emit(UserErrorState(message: failure.message)),
          (metrics) =>
              emit(GetChildPerformanceMetricsSuccessState(metrics: metrics)));
    } catch (e) {
      emit(UserErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _onGetParentPendingReviewsEvent(
      GetParentPendingReviewsEvent event, Emitter<UserState> emit) async {
    emit(UserLoadingState());
    try {
      final result = await getParentPendingReviewsUseCase.call(event.parentId);
      result.fold(
        (failure) => emit(UserErrorState(
            message: failure.message ?? 'Failed to fetch pending reviews')),
        (pendingReviews) {
          emit(
              ParentPendingReviewsSuccessState(pendingReviews: pendingReviews));
        },
      );
    } catch (e) {
      emit(UserErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _onGetParentReviewHistoryEvent(
      GetParentReviewHistoryEvent event, Emitter<UserState> emit) async {
    emit(ParentReviewHistoryLoadingState());
    try {
      final result = await getParentReviewHistoryUseCase.call(
        event.parentId,
        childId: event.childId,
      );
      result.fold(
        (failure) =>
            emit(ParentReviewHistoryErrorState(message: failure.message)),
        (history) => emit(ParentReviewHistorySuccessState(history: history)),
      );
    } catch (e) {
      emit(ParentReviewHistoryErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _onRefundEvent(
      RefundEvent event, Emitter<UserState> emit) async {
    emit(RefundLoadingState());
    final result = await refundUseCase(parentId: event.parentId);
    result.fold(
      (failure) => emit(RefundErrorState(message: failure.message)),
      (refunds) => emit(RefundSuccessState(refunds: refunds)),
    );
  }

  FutureOr<void> _onGetTimeTableByParentIdEvent(
      GetTimeTableByParentIdEvent event, Emitter<UserState> emit) async {
    try {
      emit(UserLoadingState());
      final timetableList =
          await getTimeTableByParentIdUseCase.call(parentId: event.parentId);
      emit(GetTimeTableByParentIdSuccessState(timetableList: timetableList));
    } catch (e) {
      emit(UserErrorState(message: e.toString()));
    }
  }
}
