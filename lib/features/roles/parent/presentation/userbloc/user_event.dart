part of 'user_bloc.dart';

abstract class UserEvent extends Equatable {
  const UserEvent();

  @override
  List<Object?> get props => [];
}

class CreateChildEvent extends UserEvent {
  final String parentId;
  final Map<String, dynamic> data;

  CreateChildEvent({
    required this.parentId,
    required this.data,
  });

  @override
  List<Object?> get props => [parentId, data];
}

class GetChildByParentIdEvent extends UserEvent {
  final String parentId;

  GetChildByParentIdEvent({required this.parentId});

  @override
  List<Object?> get props => [parentId];
}

class UpdateChildEvent extends UserEvent {
  final String childId;
  final Map<String, dynamic> data;

  UpdateChildEvent({required this.childId, required this.data});

  @override
  List<Object?> get props => [childId, data];
}

class DeleteChildByIdEvent extends UserEvent {
  final String id;

  DeleteChildByIdEvent({required this.id});

  @override
  List<Object?> get props => [id];
}

class CreateOrderEvent extends UserEvent {
  final Map<String, dynamic> orderData;

  CreateOrderEvent({required this.orderData});

  @override
  List<Object?> get props => [orderData];
}

class GetOrderByIdEvent extends UserEvent {
  final String orderId;

  GetOrderByIdEvent({required this.orderId});

  @override
  List<Object?> get props => [orderId];
}

class GetAllOrdersEvent extends UserEvent {
  final int skip;
  final int limit;
  final bool? paid;

  GetAllOrdersEvent({required this.skip, required this.limit, this.paid});

  @override
  List<Object?> get props => [skip, limit, paid];
}

class UpdateOrderEvent extends UserEvent {
  final String orderId;
  final OrderModel order;

  UpdateOrderEvent({required this.orderId, required this.order});

  @override
  List<Object?> get props => [orderId, order];
}

class DeleteOrderEvent extends UserEvent {
  final String orderId;

  DeleteOrderEvent({required this.orderId});

  @override
  List<Object?> get props => [orderId];
}

class GetOrdersByUserEvent extends UserEvent {}

class QrCodeGenerateForUserEvent extends UserEvent {
  final String classId;
  final String studentId;
  final DateTime classDate;

  QrCodeGenerateForUserEvent(
      {required this.classId,
      required this.studentId,
      required this.classDate});

  @override
  List<Object?> get props => [classId, studentId, classDate];
}

class GetHistoryOfChildIdEvent extends UserEvent {
  final String childId;

  GetHistoryOfChildIdEvent({required this.childId});

  @override
  List<Object?> get props => [childId];
}

class GetPurchasedHistoryEvent extends UserEvent {}

class GetBalanceEvent extends UserEvent {}

class GetCardEvent extends UserEvent {}

class DeleteCardEvent extends UserEvent {}

class GetDiscountEvent extends UserEvent {}

class UpdateAddressEvent extends UserEvent {
  final String id;
  final Map<String, dynamic> data;

  UpdateAddressEvent({required this.id, required this.data});

  @override
  List<Object?> get props => [id, data];
}

class DeleteAddressEvent extends UserEvent {
  final String id;
  final String addressId;

  DeleteAddressEvent({required this.id, required this.addressId});

  @override
  List<Object?> get props => [id, addressId];
}

class ChangeDefaultAddressEvent extends UserEvent {
  final String id;
  final String addressId;

  ChangeDefaultAddressEvent({required this.id, required this.addressId});

  @override
  List<Object?> get props => [id, addressId];
}

class ContactUsEvent extends UserEvent {
  final String email;
  final Map<String, dynamic> data;

  ContactUsEvent({required this.email, required this.data});

  @override
  List<Object?> get props => [email, data];
}

class GetChildPerformanceMetricsEvent extends UserEvent {
  final String childId;

  const GetChildPerformanceMetricsEvent({required this.childId});

  @override
  List<Object?> get props => [childId];
}

class GetParentPendingReviewsEvent extends UserEvent {
  final String parentId;

  const GetParentPendingReviewsEvent({required this.parentId});

  @override
  List<Object?> get props => [parentId];
}

class GetParentReviewHistoryEvent extends UserEvent {
  final String parentId;
  final String? childId;

  const GetParentReviewHistoryEvent({required this.parentId, this.childId});

  @override
  List<Object?> get props => [parentId, childId];
}

class RefundEvent extends UserEvent {
  final String parentId;
  const RefundEvent({required this.parentId});
  @override
  List<Object?> get props => [parentId];
}

class GetTimeTableByParentIdEvent extends UserEvent {
  final String parentId;
  const GetTimeTableByParentIdEvent({required this.parentId});
  @override
  List<Object?> get props => [parentId];
}
