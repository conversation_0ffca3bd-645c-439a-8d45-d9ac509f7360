import 'package:class_z/core/imports.dart';

import 'package:class_z/core/common/presentation/widgets/schdule_card.dart';

class ProgressFeedBackUserDashbboard extends StatefulWidget {
  const ProgressFeedBackUserDashbboard({super.key});

  @override
  State<ProgressFeedBackUserDashbboard> createState() =>
      _ProgressFeedBackUserDashbboardState();
}

class _ProgressFeedBackUserDashbboardState
    extends State<ProgressFeedBackUserDashbboard>
    with AutomaticKeepAliveClientMixin {
  // Ensure the widget is kept alive when switching tabs to avoid
  // re-fetching data and showing the loading dialog repeatedly.
  @override
  bool get wantKeepAlive => true;

  bool _hasLoadedInitialData = false; // NEW: prevents duplicate fetches

  @override
  void initState() {
    super.initState();
    _loadInitialData();
  }

  void _loadInitialData() {
    if (_hasLoadedInitialData) return;

    final parentData = locator<SharedRepository>().getParentData();
    final parentId = parentData?.id ?? "";
    print("Loading children for parentId: $parentId");
    context.read<UserBloc>().add(GetChildByParentIdEvent(parentId: parentId));

    _hasLoadedInitialData = true;
  }

  List<ChildModel> childs = [];
  ReviewResponseModel reviews = ReviewResponseModel(
    reviews: [],
    bestQuestion: null,
    worstQuestion: null,
  );
  ChildModel? currentChild;

  // Calculate total hours from reviews
  int calculateTotalHours() {
    int totalMinutes = 0;
    int reviewsWithDuration = 0;

    for (var review in reviews.reviews) {
      // Check if classId exists
      if (review.classId != null) {
        print('Review ${review.id}: Class ID exists');

        // Get the event duration from the review's class
        final dates = review.classId?.dates;
        print('Class dates: ${dates?.length ?? 0}');

        final durationStr =
            dates?.isNotEmpty == true ? dates?.first.durationMinutes : null;

        print('Duration string: $durationStr');

        if (durationStr != null && durationStr.isNotEmpty) {
          // Parse the duration minutes (remove 'mins' suffix if present)
          String cleanDuration = durationStr.replaceAll('mins', '').trim();
          try {
            int duration = int.parse(cleanDuration);
            totalMinutes += duration;
            reviewsWithDuration++;
            print('Added $duration minutes, total: $totalMinutes');
          } catch (e) {
            print('Error parsing duration: $e');
          }
        }
      } else {
        print('Review ${review.id}: No Class ID');
      }
    }

    // If no minutes calculated but we have reviews, estimate based on average class duration
    if (totalMinutes == 0 && reviews.reviews.isNotEmpty) {
      // Assume average class is 60 minutes if we couldn't get actual durations
      print('No duration data found, using review count as fallback');
      return reviews.reviews.length; // Each review counts as 1 hour
    }

    final hours = (totalMinutes / 60).ceil();
    print('Total minutes: $totalMinutes, hours: $hours');

    // Convert total minutes to hours (rounded up)
    return hours;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required when using AutomaticKeepAliveClientMixin
    return Scaffold(
      body: BlocListener<UserBloc, UserState>(
        listener: (context, state) {
          if (state is UserLoadingState)
            loadingState(context: context);
          else
            hideLoadingDialog(context);
          if (state is UserErrorState) {
            errorState(context: context, error: state.message);
          }
          if (state is GetChildByParentIdSuccessState) {
            setState(() {
              childs = state.child;
              currentChild = childs.isNotEmpty ? childs[0] : null;
            });
            if (state.child.isNotEmpty) {
              context
                  .read<UserBloc>()
                  .add(GetHistoryOfChildIdEvent(childId: state.child[0].id!));
            }
          }
          if (state is GetReviewByChildByIdSuccessState) {
            print("DEBUG: Received reviews state");
            print("DEBUG: Reviews length: ${state.reviews.reviews.length}");
            print("DEBUG: Best Question: ${state.reviews.bestQuestion}");
            print("DEBUG: Worst Question: ${state.reviews.worstQuestion}");
            setState(() {
              reviews = state.reviews;
            });
          }
        },
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              customTopBarOnlyIcon(
                context: context,
                badgeCount1: 0,
                badgeCount2: 0,
                onTap1: () {
                  NavigatorService.pushNamed(AppRoutes.notification,
                      arguments:
                          locator<SharedRepository>().getParentData()?.id);
                },
                onTap2: () {
                  NavigatorService.pushNamed(AppRoutes.centerMessage,
                      arguments: 'user');
                },
              ),
              SizedBox(height: 8.97.h),
              _rest()
            ],
          ),
        ),
      ),
    );
  }

  Widget _rest() {
    // Calculate total hours
    final int totalHours = calculateTotalHours();

    // Get the current child's metrics
    final outstandingQuality = currentChild?.outstandingQuality ?? 0;
    final keyCompetency = currentChild?.keyCompetency ?? 0;

    // Convert numeric values to percentage for display
    final outstandingQualityDisplay =
        (outstandingQuality * 100).toStringAsFixed(1);
    final keyCompetencyDisplay = (keyCompetency * 100).toStringAsFixed(1);

    return Padding(
      padding: EdgeInsets.only(left: 20.w, right: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              customtext(
                context: context,
                newYear: "Dashboard",
                font: 30.sp,
                weight: FontWeight.w500,
              ),
              SizedBox(width: 42.w),
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: 12), // Adjust padding as needed
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: AppPallete.secondaryColor, // Border color
                      width: 1.5, // Border width
                    ),
                    borderRadius:
                        BorderRadius.circular(8), // Optional: Rounded corners
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<ChildModel>(
                      hint: Text("Select Child"),
                      value: currentChild,
                      isExpanded: true, // Ensures the dropdown takes full width
                      items: childs.map((ChildModel child) {
                        return DropdownMenuItem<ChildModel>(
                          value: child,
                          child: Text(child.fullname ?? ""),
                        );
                      }).toList(),
                      onChanged: (ChildModel? newValue) {
                        setState(() {
                          currentChild = newValue;
                        });
                        if (newValue != null) {
                          print(
                              "Selected child: ${newValue.fullname} with ID: ${newValue.id}");
                          context.read<UserBloc>().add(
                              GetHistoryOfChildIdEvent(childId: newValue.id!));
                        }
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 30.h),
          _overView(context: context),
          // SizedBox(height: 25.h),
          // dashBoardCard(
          //   context: context,
          //   imagePath: currentChild?.mainImage?.url ?? "",
          //   rating: currentChild?.rating.toString() ?? "0",
          //   cooperative: 8.2.toString(),
          //   involvement: 8.2.toString(),
          //   preparedness: 8.2.toString(),
          //   improvement: 8.2.toString(),
          // ),
          SizedBox(height: 25.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: _containerTwo(
                    context: context,
                    title1: "Outstanding Quality",
                    title2: "$outstandingQualityDisplay%"),
              ),
              SizedBox(width: 27.w),
              Expanded(
                child: _containerTwo(
                    context: context,
                    title1: "Key Competency",
                    title2: "$keyCompetencyDisplay%"),
              ),
            ],
          ),
          SizedBox(height: 25.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: schduleCard(
                  context: context,
                  title1: "Class Participated",
                  title2: "Classes",
                  classNumber: reviews.reviews.length,
                  date: DateTime.now(),
                ),
              ),
              SizedBox(width: 27.w),
              Expanded(
                child: schduleCard(
                  context: context,
                  title1: "Hourly Participation",
                  title2: "Hours",
                  classNumber: totalHours, // Use the calculated total hours
                  date: DateTime.now(),
                ),
              ),
            ],
          ),
          SizedBox(height: 25.h),
          customtext(
            context: context,
            newYear: "Feedback",
            font: 20.sp,
            weight: FontWeight.w500,
          ),
          customtext(
            context: context,
            newYear: "Snapshot of the past ${reviews.reviews.length} classes",
            font: 15.sp,
            weight: FontWeight.w500,
          ),
          SizedBox(
            height: 20.h,
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.zero,
            itemCount: reviews.reviews.length,
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 8.h,
              );
            },
            itemBuilder: (context, index) {
              final review = reviews.reviews[index];

              return lastClassCard(
                context: context,
                date: dateGenerator(date: review.date),
                course: review.title ?? "",
                coach: review.coachName ?? "",
                center: review.centerName ?? "",
                image: review.classId?.mainImage?.url ?? "",
                roundBottomCorners: true,
                onTap: () {
                  NavigatorService.pushNamed(AppRoutes.progressUser,
                      arguments: review);
                },
              );
            },
          ),
          SizedBox(
            height: 32.h,
          )
        ],
      ),
    );
  }

  Widget _containerTwo(
      {required BuildContext context,
      required String title1,
      required String title2}) {
    return Container(
      width: double.infinity,
      height: 95.h,
      decoration: BoxDecoration(
          color: AppPallete.dashboard,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: customtext(
                context: context,
                newYear: title1,
                font: 12.sp,
                weight: FontWeight.w500),
          ),
          SizedBox(
            height: 21.h,
          ),
          Center(
            child: customtext(
                context: context,
                newYear: title2,
                font: 15.sp,
                weight: FontWeight.w700),
          ),
        ],
      ),
    );
  }

  Widget _overView({required BuildContext context}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 22, vertical: 19.5),
      decoration: BoxDecoration(
        color: AppPallete.dashboard,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          shadow(blurRadius: 15, opacity: 0.1),
        ], // Assuming shadow() function is defined
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          customtext(
            context: context,
            newYear: "OverView",
            font: 20.sp,
            weight: FontWeight.w500,
          ),
          SizedBox(height: 24.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CircularImageWithProgress(
                  imageUrl: imageStringGenerator(
                      imagePath: currentChild?.mainImage?.url ?? ""),
                  progress: currentChild?.rating ?? 0),

              //  SizedBox(width: 16.w), // Added spacing between image and text
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      customSvgPicture(
                        imagePath: ImagePath.starSvg,
                        height: 24.h,
                        width: 27.w,
                        color: AppPallete.rating,
                      ),

                      customtext(
                        context: context,
                        newYear: currentChild?.rating.toString() ?? "0",
                        font: 30.sp,
                        weight: FontWeight.w700,
                      ),
                      SizedBox(width: 10.w), // Small spacing
                      Padding(
                        padding: EdgeInsets.only(
                            top: 18.h), // Moves /5 slightly lower
                        child: customtext(
                          context: context,
                          newYear: "/10",
                          font: 12.sp,
                          weight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 24,
                  ),
                  Button(
                    buttonText: "Moment",
                    color: AppPallete.secondaryColor,
                    height: 43.h,
                    width: 170.w,
                    onPressed: () {
                      if (currentChild != null && currentChild!.id != null) {
                        print(
                            "Navigating to Moments with childId: ${currentChild!.id}");
                        NavigatorService.pushNamed(AppRoutes.moments,
                            arguments: currentChild!.id);
                      } else {
                        print(
                            "Cannot navigate to Moments: currentChild or childId is null");
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text("Please select a child first"),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }
                    },
                  )
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class CircularImageWithProgress extends StatelessWidget {
  final double progress; // Value from 0 to 10
  final String imageUrl;

  const CircularImageWithProgress({
    Key? key,
    required this.progress,
    required this.imageUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double normalizedProgress =
        (progress / 10).clamp(0.0, 1.0); // Convert 0-10 to 0-1

    return SizedBox(
      width: 114, // Adjust size as needed
      height: 114,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Profile Image
          ClipOval(
            child: CustomImageBuilder(
              borderRadius: 106.w,
              imagePath:
                  imageUrl.isNotEmpty ? imageUrl : null, // Handle empty URLs
              height: 106.h,
              width: 106.w,
            ),
          ),

          // Circular Progress
          SizedBox(
            width: 114,
            height: 114,
            child: CircularProgressIndicator(
              value: normalizedProgress,
              strokeWidth: 6,
              backgroundColor: Colors.grey.shade300, // Optional background
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          ),
        ],
      ),
    );
  }
}
