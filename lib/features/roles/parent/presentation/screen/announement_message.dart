import 'package:class_z/core/imports.dart';
import 'package:class_z/core/utils/formate_date.dart';

class AnnouncementMessage extends StatefulWidget {
  final Map<String, dynamic> announcement;
  const AnnouncementMessage({required this.announcement, super.key});

  @override
  State<AnnouncementMessage> createState() => _AnnouncementMessageState();
}

class _AnnouncementMessageState extends State<AnnouncementMessage> {
  @override
  void initState() {
    super.initState();
    context
        .read<AnnouncementBloc>()
        .add(GetAnnouncementEvent(id: widget.announcement['id']));
  }

  String centerName = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomAppBarDouble(
              title: "Announcement",
              title2: widget.announcement['className'],
              leading: customBackButton(),
            ),
            <PERSON><PERSON><PERSON>ox(height: 50),
            _buildIconsRow(),
            SizedBox(height: 11),
            customDivider(),
            BlocConsumer<AnnouncementBloc, AnnouncementState>(
              listener: (context, state) {
                if (state is AnnouncementLoading) {
                  loadingState(context: context);
                } else {
                  hideLoadingDialog(context);
                }
                if (state is AnnouncementError) {
                  errorState(context: context, error: state.message);
                }
              },
              builder: (context, state) {
                if (state is AnnouncementLoadedState) {
                  return _announcementDesign(state.announcements);
                }
                return Center(child: CircularProgressIndicator());
              },
            )
          ],
        ),
      ),
    );
  }

  /// Builds the info and message icons with a separator
  Widget _buildIconsRow() {
    return Padding(
      padding: const EdgeInsets.only(right: 28.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          InkWell(
            onTap: () {},
            child: Icon(Icons.info_outline, color: AppPallete.black),
          ),
          SizedBox(
            width: 5,
          ),
          InkWell(
            onTap: () {
              NavigatorService.pushNamed(AppRoutes.chat, arguments: {
                "title": 'center',
                "imagePath": '',
                "id": '67190235d493e133bbdab127'
              });
            },
            child: Icon(Icons.message_outlined, color: AppPallete.black),
          ),
        ],
      ),
    );
  }

  /// Builds the announcement details
  Widget _announcementDesign(AnnouncementEntity? announcement) {
    if (announcement == null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.only(top: 20),
          child: customtext(
            context: context,
            newYear: 'No announcement available',
            font: 15.sp,
            weight: FontWeight.w400,
          ),
        ),
      );
    }
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 19.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAnnouncementHeader(announcement),
          SizedBox(height: 30),
          _buildMessageList(announcement),
        ],
      ),
    );
  }

  /// Builds the announcement header with class details
  Widget _buildAnnouncementHeader(AnnouncementEntity announcement) {
    print(announcement.toString());
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 19.0),
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppPallete.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [shadow()],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    customtext(
                      context: context,
                      newYear: dateGenerator(
                          date:
                              announcement.createdAt ?? DateTime.now()), // Date
                      font: 15.sp,
                    ),
                    SizedBox(width: 15),
                    customtext(
                      context: context,
                      newYear: timeGenerator(
                          announcement.createdAt ?? DateTime.now()), // Time
                      font: 15.sp,
                    ),
                  ],
                ),
                customtext(
                  context: context,
                  newYear: announcement.title ?? 'Announcement',
                  font: 20.sp,
                  weight: FontWeight.w500,
                ),
                customtext(
                  context: context,
                  newYear: 'by ${announcement.senderName ?? 'Unknown'}',
                  font: 12.sp,
                  weight: FontWeight.w500,
                ),
              ],
            ),
            CustomImageBuilder(
              height: 128,
              width: 128,
              borderRadius: 20,
              imagePath: imageStringGenerator(
                  imagePath: announcement.mainImage?.url ?? ''),
            )
          ],
        ),
      ),
    );
  }

  /// Builds the message list
  Widget _buildMessageList(AnnouncementEntity announcement) {
    List messages = announcement.messages?.reversed.toList() ?? [];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.only(top: 0, left: 22, right: 22),
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        bool showDateHeader = false;
        final DateTime messageDate = message.createdAt ?? DateTime.now();
        if (index == 0) {
          showDateHeader = true;
        } else {
          final previousMessage = messages[index - 1];
          final DateTime previousDate =
              previousMessage.createdAt ?? DateTime.now();

          // Show date header if the date is different from the previous message
          if (!isSameDay(previousDate, messageDate)) {
            showDateHeader = true;
          }
        }
        return Padding(
          padding: const EdgeInsets.only(bottom: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (showDateHeader)
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 10.h),
                  child: Center(
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        formatDate(messageDate),
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
              Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(vertical: 6, horizontal: 11),
                decoration: BoxDecoration(
                  color: AppPallete.lightGrey,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: customtext(
                  context: context,
                  newYear: message.message ?? '',
                  font: 15.sp,
                  weight: FontWeight.w400,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
