import 'package:class_z/core/imports.dart';

class EditChild extends StatefulWidget {
  final ChildModel child;

  const EditChild({required this.child, super.key});

  @override
  State<EditChild> createState() => _EditChildState();
}

class _EditChildState extends State<EditChild> {
  final TextEditingController fullnameController = TextEditingController();
  final TextEditingController idcardController = TextEditingController();
  final TextEditingController birthdayController = TextEditingController();
  final TextEditingController schoolController = TextEditingController();
  final TextEditingController languageController = TextEditingController();
  final TextEditingController yearController = TextEditingController();
  final TextEditingController monthController = TextEditingController();
  final TextEditingController dayController = TextEditingController();
  final TextEditingController labelController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();

  // Store reference to UserBloc to avoid accessing deactivated widget context
  late UserBloc _userBloc;

  // Generate lists for years, months, and days
  final List<String> years =
      List.generate(100, (index) => (DateTime.now().year - index).toString());
  final List<String> months =
      List.generate(12, (index) => (index + 1).toString().padLeft(2, '0'));
  final List<String> days =
      List.generate(31, (index) => (index + 1).toString().padLeft(2, '0'));
  List<String> number = ['+852', '+99'];
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  bool _isYesPressed = false;
  final ImagePicker _picker = ImagePicker();
  File? _image;
  String? _currentImageUrl;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Store reference to UserBloc to use safely even after widget disposal
    _userBloc = context.read<UserBloc>();
  }

  void _initializeData() {
    // Pre-fill the form with existing child data
    fullnameController.text = widget.child.fullname ?? '';
    idcardController.text = widget.child.idcard ?? '';
    schoolController.text = widget.child.school ?? '';
    _isYesPressed = widget.child.sen ?? false;

    // Initialize current image URL
    _currentImageUrl = widget.child.mainImage?.url;

    // Parse birthday
    if (widget.child.birthday != null) {
      try {
        final parts = widget.child.birthday!.split('-');
        if (parts.length >= 3) {
          yearController.text = parts[0];
          monthController.text = parts[1];
          dayController.text = parts[2];
        }
      } catch (e) {
        print("Error parsing birthday: $e");
      }
    }

    // Parse phone number
    if (widget.child.phone != null) {
      String fullPhoneNumber = widget.child.phone!;
      bool codeFound = false;
      for (String code in number) {
        if (fullPhoneNumber.startsWith(code)) {
          labelController.text = code;
          phoneController.text = fullPhoneNumber.substring(code.length).trim();
          codeFound = true;
          break;
        }
      }
      if (!codeFound) {
        phoneController.text = fullPhoneNumber;
        labelController.text = number.first;
      }
    } else {
      labelController.text = number.first;
    }
  }

  @override
  void dispose() {
    phoneController.dispose();
    labelController.dispose();
    dayController.dispose();
    monthController.dispose();
    yearController.dispose();
    languageController.dispose();
    schoolController.dispose();
    birthdayController.dispose();
    idcardController.dispose();
    fullnameController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final XFile? pickedImage =
        await _picker.pickImage(source: ImageSource.gallery);
    if (pickedImage != null) {
      setState(() {
        _image = File(pickedImage.path);
      });
    }
  }

  Widget _imageWidget({required BuildContext context}) {
    return Center(
      child: Stack(
        children: [
          Container(
            height: 125.h,
            width: 125.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(99.r),
              color: AppPallete.paleGrey,
              border: Border.all(
                color: AppPallete.greyWord,
                width: 2,
              ),
            ),
            child: _image != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(99.r),
                    child: Image.file(
                      _image!,
                      height: 125.h,
                      width: 125.w,
                      fit: BoxFit.cover,
                    ),
                  )
                : _currentImageUrl != null && _currentImageUrl!.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(99.r),
                        child: CustomImageBuilder(
                          imagePath: imageStringGenerator(
                              imagePath: _currentImageUrl!),
                          height: 125.h,
                          width: 125.w,
                          borderRadius: 99.r,
                        ),
                      )
                    : Icon(
                        Icons.person,
                        size: 60,
                        color: AppPallete.greyWord,
                      ),
          ),
          Positioned(
            bottom: 0,
            right: 0,
            child: IconButton(
              icon: CustomIconButton(
                icon: Icons.camera_alt,
                color: AppPallete.greyWord,
                onPressed: () {
                  _pickImage();
                },
              ),
              onPressed: () {
                _pickImage();
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: customTitle(context, "Edit Child", 20.sp),
        centerTitle: true,
        leading: CustomIconButton(
          icon: Icons.arrow_back_ios,
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          GestureDetector(
            onTap: () {
              if (formKey.currentState!.validate()) {
                String fullname = fullnameController.text;
                String idCardNumber = idcardController.text;
                String year = yearController.text.trim();
                String month = monthController.text.trim();
                String day = dayController.text.trim();

                // Validate birthday components
                if (year.isEmpty || month.isEmpty || day.isEmpty) {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text("Error"),
                      content: const Text(
                          "Please select a valid birthday (year, month, and day)."),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('OK'),
                        ),
                      ],
                    ),
                  );
                  return;
                }

                String birthday = "$year-$month-$day";
                String school = schoolController.text.trim();
                final String phone =
                    "${labelController.text}${phoneController.text}";
                bool specialEducationRequired = _isYesPressed;

                Map<String, dynamic> data = {
                  'fullname': fullname,
                  'idcard': idCardNumber,
                  'birthday': birthday,
                  'sen': specialEducationRequired,
                  'phone': phone,
                  if (school.isNotEmpty) 'school': school,
                  if (_image != null) 'mainImage': _image,
                };

                // Debug: Check if child ID exists
                if (widget.child.id == null) {
                  print('ERROR: Child ID is null, cannot update');
                  if (mounted) {
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text("Error"),
                        content: const Text(
                            "Child ID is missing. Cannot update child information."),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('OK'),
                          ),
                        ],
                      ),
                    );
                  }
                  return;
                }

                print(
                    'DEBUG: About to call UpdateChildEvent with child ID: ${widget.child.id}');
                print('DEBUG: Data to send: $data');

                try {
                  _userBloc.add(
                      UpdateChildEvent(childId: widget.child.id!, data: data));
                } catch (e) {
                  print('ERROR adding UpdateChildEvent: $e');
                  if (mounted) {
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text("Error"),
                        content: Text("Failed to update child: $e"),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('OK'),
                          ),
                        ],
                      ),
                    );
                  }
                }
              }
            },
            child: Padding(
              padding: EdgeInsets.only(right: 20.w),
              child: Center(
                child: Text(
                  "Save",
                  style: TextStyle(
                    color: AppPallete.change,
                    fontSize: 17.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
          )
        ],
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: BlocListener<UserBloc, UserState>(
        listener: (context, state) {
          if (state is UserLoadingState) {
            // Show loading indicator
          }
          if (state is UpdateChildSuccessState) {
            if (mounted) {
              Navigator.pop(context, true); // Return success result
            }
          }
          if (state is UserErrorState) {
            if (mounted) {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text("Error"),
                  content: Text(state.message),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('OK'),
                    ),
                  ],
                ),
              );
            }
          }
        },
        child: SingleChildScrollView(
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.manual,
          child: Padding(
            padding: EdgeInsets.only(
              left: 21.w,
              right: 22.w,
              bottom: MediaQuery.of(context).viewInsets.bottom + 20,
            ),
            child: Form(
              key: formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 20.h),
                  _imageWidget(context: context),
                  SizedBox(height: 50.h),
                  customtext(
                      context: context,
                      newYear: "Your Information",
                      font: 17.sp,
                      weight: FontWeight.w500),
                  SizedBox(
                    height: 22.h,
                  ),
                  customRequiredText(
                      context: context,
                      title: "Full name",
                      font: 15.sp,
                      weight: FontWeight.w400),
                  SizedBox(
                    height: 15.h,
                  ),
                  AuthField(
                    controller: fullnameController,
                    width: 387.w,
                    height: 30.h,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Fullname is required';
                      }
                      return null;
                    },
                  ),
                  SizedBox(
                    height: 22.h,
                  ),
                  customRequiredText(
                      context: context,
                      title: "ID card number",
                      font: 15.sp,
                      weight: FontWeight.w400),
                  SizedBox(
                    height: 15.h,
                  ),
                  AuthField(
                    controller: idcardController,
                    width: 387.w,
                    height: 30.h,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Id card number is required';
                      }
                      return null;
                    },
                  ),
                  SizedBox(
                    height: 15.h,
                  ),
                  customRequiredText(
                      context: context,
                      title: "Birthday",
                      font: 15.sp,
                      weight: FontWeight.w400),
                  SizedBox(
                    height: 15.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: DropDown(
                          label: "Year",
                          times: years,
                          color: AppPallete.paleGrey,
                          controller: yearController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Year is required';
                            }
                            return null;
                          },
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        child: DropDown(
                          label: "Month",
                          times: months,
                          color: AppPallete.paleGrey,
                          controller: monthController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Month is required';
                            }
                            return null;
                          },
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Expanded(
                        child: DropDown(
                          label: "Day",
                          times: days,
                          color: AppPallete.paleGrey,
                          controller: dayController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Day is required';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 15.h,
                  ),
                  customtext(
                      context: context,
                      newYear: "School",
                      font: 15.sp,
                      weight: FontWeight.w400),
                  SizedBox(
                    height: 15.h,
                  ),
                  AuthField(
                    hintText: "Name of the school",
                    controller: schoolController,
                    width: 387.w,
                    height: 30.h,
                    textInputAction: TextInputAction.next,
                  ),
                  SizedBox(
                    height: 53.h,
                  ),
                  customtext(
                      context: context,
                      newYear: "Special Note",
                      font: 17.sp,
                      weight: FontWeight.w500),
                  SizedBox(
                    height: 16.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      customtext(
                          context: context,
                          newYear: "Special Education Need",
                          font: 15.sp,
                          weight: FontWeight.w400),
                      Row(
                        children: [
                          Button(
                            buttonText: "YES",
                            height: 32.h,
                            width: 55.w,
                            textSize: 14.sp,
                            fontWeight: FontWeight.w400,
                            onPressed: () {
                              setState(() {
                                _isYesPressed = true;
                              });
                            },
                            textColorFinal:
                                _isYesPressed ? AppPallete.white : Colors.black,
                            color: _isYesPressed
                                ? AppPallete.secondaryColor
                                : AppPallete.paleGrey,
                          ),
                          SizedBox(width: 7.w),
                          Button(
                            buttonText: "NO",
                            height: 32.h,
                            width: 55.w,
                            textSize: 14.sp,
                            fontWeight: FontWeight.w400,
                            onPressed: () {
                              setState(() {
                                _isYesPressed = false;
                              });
                            },
                            textColorFinal:
                                _isYesPressed ? Colors.black : Colors.white,
                            color: !_isYesPressed
                                ? AppPallete.secondaryColor
                                : AppPallete.paleGrey,
                          ),
                        ],
                      )
                    ],
                  ),
                  SizedBox(
                    height: 53.h,
                  ),
                  customRequiredText(
                      context: context,
                      title: 'Emergency Number',
                      font: 15.sp,
                      weight: FontWeight.w400),
                  SizedBox(
                    height: 20.h,
                  ),
                  Row(
                    children: [
                      SizedBox(
                        width: 80.w,
                        child: DropDown(
                          label: "",
                          times: number,
                          color: AppPallete.paleGrey,
                          controller: labelController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Country Code is required';
                            }
                            return null;
                          },
                        ),
                      ),
                      SizedBox(
                        width: 6.w,
                      ),
                      Expanded(
                        child: AuthField(
                          hintText: "PhoneNumber",
                          controller: phoneController,
                          keyboard: TextInputType.number,
                          height: 30.h,
                          textInputAction: TextInputAction.done,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Phone number is required';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 50.h,
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
