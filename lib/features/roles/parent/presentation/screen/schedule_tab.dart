import 'package:class_z/core/imports.dart';

class ScheduleTab extends StatelessWidget {
  final List<EventModel> events;
  final ValueNotifier<int> currentPageIndex;
  final ClassModel? classModel;
  final CenterData? center;
  final Function(List<EventModel> finalEvents) checkIfEligibleAndSendRequest;

  const ScheduleTab({
    Key? key,
    required this.events,
    required this.currentPageIndex,
    this.classModel,
    this.center,
    required this.checkIfEligibleAndSendRequest,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: currentPageIndex,
      builder: (context, value, child) {
        final currentClassDate = classModel?.dates![currentPageIndex.value];
        final matchingEvents = events
            .where((eventDetail) => eventDetail.dateId == currentClassDate?.id)
            .toList();

        final isJoinable = checkIfEligibleAndSendRequest(matchingEvents);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 43.h,
            ),
            Expanded(
              child: ListView.separated(
                padding: EdgeInsets.symmetric(vertical: 8.h),
                itemCount: matchingEvents.length,
                itemBuilder: (context, index) {
                  final eventDetail = matchingEvents[index];
                  return schduleTimeSlotCenterCourse(
                    context: context,
                    number: index + 1,
                    dateTime: eventDetail.date!,
                    start: eventDetail.startTime ?? "",
                    finish: eventDetail.endTime ?? "",
                    duration: eventDetail.durationMinutes ?? "",
                    isJoinable: isJoinable,
                    onTap: () {
                      NavigatorService.pushNamed(
                        AppRoutes.request,
                        arguments: {
                          'classModel': classModel,
                          'center': center,
                          'eventDetails': matchingEvents,
                        },
                      );
                    },
                  );
                },
                separatorBuilder: (context, index) {
                  return SizedBox(
                    height: 18.h,
                  );
                },
              ),
            )
          ],
        );
      },
    );
  }
}
