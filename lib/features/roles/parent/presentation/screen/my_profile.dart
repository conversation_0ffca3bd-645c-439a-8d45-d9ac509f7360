import 'package:class_z/core/imports.dart';
import 'package:flutter/services.dart';

class MyProfile extends StatefulWidget {
  final UserModel userData;
  const MyProfile({required this.userData, super.key});

  @override
  State<MyProfile> createState() => _MyProfileState();
}

class _MyProfileState extends State<MyProfile> {
  bool _isLoading = false;
  String? _errorMessage;
  late UserModel userData;

  @override
  void initState() {
    super.initState();
    userData = widget.userData;

    // Make sure any loading dialogs are dismissed when this screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      hideLoadingDialog(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    // Check if parent data exists
    final hasParentData = userData.data?.parent != null;

    // Safely access URL only if parent data exists
    final String? url = hasParentData
        ? "${AppText.device}${userData.data?.parent?.image?.url}"
        : null;

    final GlobalKey<RefreshIndicatorState> _refreshKey = GlobalKey();

    // If we have no parent data, show an error state
    if (!hasParentData) {
      return Scaffold(
        appBar: AppBar(
          title: Text('My Profile'),
          leading: IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 64),
            SizedBox(height: 20),
            Text(
              'Profile data unavailable',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 30),
              child: Text(
                'We couldn\'t load your profile data. This might happen if you\'re using a different account type.',
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Go Back'),
            )
          ],
        )),
      );
    }

    // Show loading indicator if we're in a loading state
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: Text('My Profile'),
          leading: IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Show error message if there's an error
    if (_errorMessage != null) {
      return Scaffold(
        appBar: AppBar(
          title: Text('My Profile'),
          leading: IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 64),
            SizedBox(height: 20),
            Text(
              'Error loading profile',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 30),
              child: Text(
                _errorMessage!,
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Go Back'),
            )
          ],
        )),
      );
    }

    // Normal profile view if we have parent data
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          // Force rebuild by changing the key
          Navigator.pushReplacement(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation1, animation2) => MyProfile(
                userData: userData,
              ),
              transitionDuration: Duration.zero,
              reverseTransitionDuration: Duration.zero,
            ),
          );
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                  padding: EdgeInsets.only(left: 19.w, top: 85.h),
                  child: customBackButton()),
              Padding(
                padding: EdgeInsets.only(top: 13.w, left: 20.w),
                child: SizedBox(
                  height: 36.h,
                  child: customtext(
                      context: context,
                      newYear: "My Profile",
                      font: 30.sp,
                      weight: FontWeight.w500),
                ),
              ),
              SizedBox(
                height: 35.h,
              ),
              //Text(userData.user.createdAt, cast as String)
              Padding(
                  padding: EdgeInsets.only(top: 28.h, left: 37.w, right: 38.w),
                  child: _profile(
                      context: context,
                      classZId: userData.data?.parent?.classZId ?? '',
                      name: userData.data?.parent?.nickname ?? "",
                      email: userData.data?.parent?.email ?? "",
                      number: userData.data?.parent?.phone ?? "",
                      imagepath: url ?? '',
                      dateTime: userData.data?.parent?.createdAt))
            ],
          ),
        ),
      ),
    );
  }

  Widget _profile(
      {required BuildContext context,
      required String classZId,
      required String name,
      required String email,
      required String number,
      required String imagepath,
      DateTime? dateTime}) {
    String? date;
    if (dateTime != null) date = DateFormat('dd/MM/yyyy').format(dateTime);
    return Center(
      child: Container(
        //  height: 349.h,
        //  width: 357.w,
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              shadow(blurRadius: 15, opacity: 0.1, xoffset: 0, yoffset: 0),
            ]),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 150.h,
              // width: 357.w,
              child: Stack(
                children: [
                  Positioned(
                      top: 22.h,
                      right: 19.w,
                      child: GestureDetector(
                        onTap: () {
                          // Directly navigate to edit profile
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => MyProfileEdit(
                                userData: userData,
                              ),
                            ),
                          ).catchError((error) {
                            // Show error message if navigation fails
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content:
                                    Text('Failed to open edit profile: $error'),
                                duration: Duration(seconds: 3),
                              ),
                            );
                          });
                        },
                        child: Text(
                          "edit",
                          style: TextStyle(
                              color: AppPallete.change,
                              fontSize: 17.sp,
                              decoration: TextDecoration.underline,
                              fontWeight: FontWeight.w400),
                        ),
                      )),
                  Align(
                    alignment: Alignment.center,
                    child: CustomImageBuilder(
                      imagePath: imagepath,
                      height: 125.w,
                      width: 125.w,
                      borderRadius: 125.w,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 31.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 14.w, right: 17.w),
              child: Text(
                "Your Information",
                style: TextStyle(
                  color: AppPallete.darkGrey,
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),

            SizedBox(
              height: 26.h,
            ),

            Padding(
              padding: EdgeInsets.only(left: 14.w, right: 17.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    flex: 1,
                    child: customtext(
                        context: context,
                        newYear: name,
                        font: 15.sp,
                        weight: FontWeight.w500,
                        color: AppPallete.darkGrey),
                  ),
                  Expanded(
                    flex: 2,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Flexible(
                          child: Text(
                            "Referral code: $classZId",
                            style: TextStyle(
                              color: AppPallete.darkGrey,
                              fontSize: 15.sp,
                              fontWeight: FontWeight.w500,
                            ),
                            textAlign: TextAlign.right,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 8.w),
                        GestureDetector(
                          onTap: () {
                            Clipboard.setData(ClipboardData(text: classZId));
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('ClassZ ID copied to clipboard'),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          },
                          child: Icon(
                            Icons.copy,
                            size: 20.sp,
                            color: AppPallete.darkGrey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 26.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 14.w, right: 17.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      email,
                      style: TextStyle(
                        color: AppPallete.darkGrey,
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w500,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Text(
                      number,
                      style: TextStyle(
                        color: AppPallete.darkGrey,
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.right,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 26.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 14.w),
              child: customtext(
                  context: context,
                  newYear: "Joined ClassZ since $date",
                  font: 15.sp,
                  weight: FontWeight.w500,
                  color: AppPallete.darkGrey),
            ),
            SizedBox(
              height: 33.h,
            ),
            // Developer options section removed
          ],
        ),
      ),
    );
  }
}
