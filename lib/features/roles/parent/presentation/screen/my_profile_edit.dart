import 'package:class_z/core/imports.dart';
import 'package:class_z/core/widgets/compressed_image_picker.dart';

class MyProfileEdit extends StatefulWidget {
  final UserModel? userData;
  const MyProfileEdit({required this.userData, super.key});

  @override
  State<MyProfileEdit> createState() => _MyProfileEditState();
}

class _MyProfileEditState extends State<MyProfileEdit> {
  final formKey = GlobalKey<FormState>();
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final labelController = TextEditingController();

  final List<String> countryCodes = ['+852', '+99'];
  XFile? _image;
  bool _isCompressing = false;
  String? _imageErrorText;
  String? initialImageUrl;

  @override
  void initState() {
    super.initState();
    // Pre-fill data
    final parentData = widget.userData?.data?.parent;
    if (parentData != null) {
      nameController.text = parentData.fullname ?? '';
      emailController.text = parentData.email ?? '';

      String fullPhoneNumber = parentData.phone ?? '';
      bool codeFound = false;
      for (String code in countryCodes) {
        if (fullPhoneNumber.startsWith(code)) {
          labelController.text = code;
          phoneController.text = fullPhoneNumber.substring(code.length).trim();
          codeFound = true;
          break;
        }
      }
      if (!codeFound) {
        phoneController.text = fullPhoneNumber;
        // set a default country code if one isn't found
        if (countryCodes.isNotEmpty) {
          labelController.text = countryCodes.first;
        }
      }

      if (parentData.image?.url != null && parentData.image!.url!.isNotEmpty) {
        initialImageUrl = "${AppText.device}${parentData.image!.url!}";
      }
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    labelController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      // Clear previous error
      setState(() {
        _imageErrorText = null;
        _isCompressing = true;
      });

      // Pick and compress image using the new service
      final result = await CompressedImagePicker.pickAndCompressImage(
        source: ImageSource.gallery,
        type: ImagePickerType.profile,
        onProgress: (message) {
          print('📸 Profile image processing: $message');
        },
      );

      setState(() {
        _isCompressing = false;
      });

      if (result.isSuccess && result.file != null) {
        setState(() {
          _image = XFile(result.file!.path);
        });

        // Show compression info if significant compression occurred
        if (result.compressionRatio > 10) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Image optimized: ${result.compressionInfo}'),
              duration: Duration(seconds: 3),
            ),
          );
        }
      } else if (result.error != null) {
        setState(() {
          _imageErrorText = result.error;
        });
      }
      // If cancelled, do nothing
    } catch (e) {
      setState(() {
        _isCompressing = false;
        _imageErrorText = 'Error selecting image: $e';
      });
      print('Error picking image: $e');
    }
  }

  void _saveProfile() {
    if (_isCompressing) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please wait, image is being processed...')),
      );
      return;
    }

    if (_imageErrorText != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(_imageErrorText!)),
      );
      return;
    }

    String name = nameController.text;
    String email = emailController.text;
    String phoneNumber = phoneController.text;
    String countryCode = labelController.text;
    final imageFile = _image != null ? File(_image!.path) : null;

    // Get the existing parent data from the shared repository
    final parentData = locator<SharedRepository>().getParentData();

    // Create update data with profile fields
    final Map<String, dynamic> updateData = {
      if (name.isNotEmpty) 'nickname': name,
      if (imageFile != null) 'mainImage': imageFile,
      if (email.isNotEmpty) 'email': email,
      if (phoneNumber.isNotEmpty && countryCode.isNotEmpty)
        'phone': '$countryCode$phoneNumber',
    };

    // If the parent has location data, include it in the update to preserve it
    if (parentData?.location != null && parentData!.location!.isNotEmpty) {
      // Use a Set to track unique location IDs to prevent duplicates
      final Set<String> processedIds = {};
      List<Map<String, dynamic>> locationList = [];

      // Process each location and only include unique ones
      for (var location in parentData.location!) {
        final String locationId = location.userAddressId ?? '';

        // Skip if we've already processed this location or if it has no ID
        if (locationId.isEmpty || processedIds.contains(locationId)) {
          continue;
        }

        // Add to our processed set
        processedIds.add(locationId);

        // Add to our location list
        locationList.add({
          'flatFloorBlock': location.flatFloorBlock ?? 'Not specified',
          'buildingEstate': location.buildingEstate ?? 'Not specified',
          'district': location.district ?? 'Not specified',
          'region': location.region ?? 'Not specified',
          'country': location.country ?? 'Hong Kong',
          'default': location.userAddressDefault ?? false,
          if (locationId.isNotEmpty) '_id': locationId,
        });
      }

      // If we ended up with no valid locations, add a default one
      if (locationList.isEmpty) {
        locationList.add({
          'flatFloorBlock': 'Not specified',
          'buildingEstate': 'Not specified',
          'district': 'Not specified',
          'region': 'Not specified',
          'country': 'Hong Kong',
          'default': true
        });
      }

      // Add location data to the update
      updateData['location'] = locationList;
    } else {
      // If no location data exists, add a default one to satisfy validation
      updateData['location'] = [
        {
          'flatFloorBlock': 'Not specified',
          'buildingEstate': 'Not specified',
          'district': 'Not specified',
          'region': 'Not specified',
          'country': 'Hong Kong',
          'default': true
        }
      ];
    }

    if (updateData.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No changes to save')),
      );
      return;
    }

    print("Sending update with data: $updateData");
    context
        .read<AuthBloc>()
        .add(ParentInfoCompleteEvent(updateData: updateData));
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthLoading) {
          loadingState(context: context);
        } else {
          hideLoadingDialog(context);
        }

        if (state is AuthError) {
          errorState(context: context, error: state.message);
        }

        if (state is ParnetAccountInfoSuccessState) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Profile updated successfully')),
          );

          // Refresh the user data to update the navigation bar image
          // Add a small delay to ensure server has processed the update
          Future.delayed(Duration(milliseconds: 500), () {
            if (mounted) {
              context.read<AuthBloc>().add(RefreshUserEvent());
            }
          });

          NavigatorService.goBack();
        }
      },
      builder: (context, state) {
        return Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: CustomAppBar(
            title: "My Profile",
            leading: customBackButton(),
            actions: [
              Padding(
                padding: EdgeInsets.only(right: 20.w),
                child: InkWell(
                  onTap: _saveProfile,
                  child: Center(
                    child: Text(
                      "Save",
                      style: TextStyle(
                        color: AppPallete.change,
                        fontSize: 17.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          body: Padding(
            padding: EdgeInsets.symmetric(horizontal: 21.w),
            child: SingleChildScrollView(
              child: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 29.h),
                    _buildProfileImageSection(),
                    SizedBox(height: 16.h),
                    _buildSectionTitle("Your Information"),
                    SizedBox(height: 22.h),
                    _buildInputField("Profile Name", nameController),
                    SizedBox(height: 20.h),
                    _buildInputField(
                      "Email Address",
                      emailController,
                      validator: _emailValidator,
                    ),
                    SizedBox(height: 20.h),
                    _buildPhoneInput(),
                    SizedBox(height: 20.h),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProfileImageSection() {
    return Column(
      children: [
        SizedBox(
          height: 125.h,
          child: Stack(
            children: [
              PositionedItemWidget(
                  top: 60.h, left: 0, right: 0, child: customDivider()),
              PositionedItemWidget(
                top: 0.h,
                left: 0,
                right: 0.w,
                child: Center(
                  child: _isCompressing
                      ? Container(
                          height: 125.h,
                          width: 125.w,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(125.w),
                          ),
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        )
                      : _image != null
                          ? Container(
                              height: 125.h,
                              width: 125.w,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(125.w),
                                image: DecorationImage(
                                  image: FileImage(File(_image!.path)),
                                  fit: BoxFit.cover,
                                ),
                              ),
                            )
                          : CustomImageBuilder(
                              imagePath: initialImageUrl ?? '',
                              height: 125.h,
                              width: 125.w,
                              borderRadius: 125.w,
                            ),
                ),
              ),
              PositionedItemWidget(
                left: 90.w,
                right: 0.w,
                bottom: 0.h,
                child: CustomIconButton(
                  color: AppPallete.greyColor,
                  icon: Icons.camera_alt,
                  height: 29.h,
                  width: 33.w,
                  onPressed: _isCompressing ? () {} : _pickImage,
                ),
              ),
            ],
          ),
        ),
        if (_imageErrorText != null)
          Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: Text(
              _imageErrorText!,
              style: TextStyle(color: Colors.red, fontSize: 12.sp),
              textAlign: TextAlign.center,
            ),
          ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return customtext(
      context: context,
      newYear: title,
      font: 17.sp,
      weight: FontWeight.w500,
    );
  }

  Widget _buildInputField(String label, TextEditingController controller,
      {String? Function(String?)? validator}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customtext(
          context: context,
          newYear: label,
          font: 15.sp,
          weight: FontWeight.w400,
        ),
        SizedBox(height: 20.h),
        AuthField(
          controller: controller,
          height: 30.h,
          validator: validator ??
              (value) {
                if (value == null || value.isEmpty) {
                  return '$label is required';
                }
                return null;
              },
        ),
      ],
    );
  }

  Widget _buildPhoneInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customtext(
          context: context,
          newYear: "Phone Number",
          font: 15.sp,
          weight: FontWeight.w400,
        ),
        SizedBox(height: 20.h),
        Row(
          children: [
            DropDown(
              label: "",
              width: 95.w,
              height: 30.h,
              times: countryCodes,
              color: AppPallete.paleGrey,
              controller: labelController,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Country Code is required';
                }
                return null;
              },
            ),
            SizedBox(width: 6.w),
            Flexible(
              child: AuthField(
                hintText: "Phone Number",
                controller: phoneController,
                keyboard: TextInputType.number,
                height: 30.h,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Phone number is required';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  String? _emailValidator(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    if (!emailRegex.hasMatch(value)) {
      return 'Enter a valid email address';
    }
    return null;
  }
}
