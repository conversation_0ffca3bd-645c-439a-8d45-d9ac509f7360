import 'package:class_z/core/imports.dart';
import 'package:flutter/services.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:http/http.dart' as http;
// Import for fetchBalanceFromApi
import 'package:class_z/core/widgets/custom_credit_card.dart';

class CardNumberInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    String newText = newValue.text.replaceAll(RegExp(r'[^0-9]'), '');

    if (newText.length > 16) {
      newText = newText.substring(0, 16);
    }

    String formattedText = '';
    for (int i = 0; i < newText.length; i++) {
      if (i > 0 && i % 4 == 0) {
        formattedText += ' ';
      }
      formattedText += newText[i];
    }

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: formattedText.length),
    );
  }
}

class CreditCardScreen extends StatefulWidget {
  final dynamic paymentDetails;

  const CreditCardScreen({this.paymentDetails, super.key});

  @override
  State<CreditCardScreen> createState() => _CreditCardState();
}

class _CreditCardState extends State<CreditCardScreen> {
  final cardNumberController = TextEditingController();
  final cardCVCController = TextEditingController();
  final cardholderController = TextEditingController();
  final cardnumberMonthController = TextEditingController();
  final cardnumberYearController = TextEditingController();
  final cardExpMonthController = TextEditingController();
  final cardExpYearController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  bool isPaymentMode = false;
  int? amountToPay;
  int? zcoinsAmount;
  String? planId; // Add plan ID for subscription creation
  bool isSubscription = false; // Add subscription flag
  bool isLoading = false;
  CardModel? savedCard;
  bool usingSavedCard = false;

  // Add class booking support
  bool isClassBooking = false;
  Map<String, dynamic>? orderDetails;

  @override
  void initState() {
    super.initState();

    print(
        "CreditCardScreen initState, paymentDetails: ${widget.paymentDetails}");

    // Check if payment details were passed
    if (widget.paymentDetails != null) {
      // Handle both Map and direct values
      if (widget.paymentDetails is Map) {
        amountToPay = widget.paymentDetails['amount'];
        zcoinsAmount = widget.paymentDetails['zcoins'];
        planId = widget.paymentDetails['planId']; // Extract plan ID
        isSubscription = widget.paymentDetails['isSubscription'] ??
            false; // Extract subscription flag

        // Add class booking support
        isClassBooking = widget.paymentDetails['isClassBooking'] ?? false;
        orderDetails = widget.paymentDetails['orderDetails'];
      } else {
        // Try to parse the payment amount directly
        try {
          amountToPay = int.tryParse(widget.paymentDetails.toString());
          zcoinsAmount = amountToPay;
        } catch (e) {
          print("Failed to parse payment details: $e");
        }
      }

      isPaymentMode = amountToPay != null && amountToPay! > 0;
      print(
          "Payment mode: $isPaymentMode, amount: $amountToPay, zcoins: $zcoinsAmount, planId: $planId, isSubscription: $isSubscription, isClassBooking: $isClassBooking");
    }

    // Load saved card details regardless of payment mode
    _loadSavedCardDetails();
  }

  @override
  void didUpdateWidget(CreditCardScreen oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If payment details have changed, update state
    if (widget.paymentDetails != oldWidget.paymentDetails) {
      amountToPay = widget.paymentDetails?['amount'];
      zcoinsAmount = widget.paymentDetails?['zcoins'];
      planId = widget.paymentDetails?['planId']; // Extract plan ID
      isSubscription = widget.paymentDetails?['isSubscription'] ??
          false; // Extract subscription flag
      isPaymentMode = amountToPay != null && zcoinsAmount != null;

      // If in payment mode, try to load saved card details
      if (isPaymentMode && savedCard == null) {
        _loadSavedCardDetails();
      }
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _loadSavedCardDetails() async {
    print("_loadSavedCardDetails started");
    setState(() {
      isLoading = true;
    });

    try {
      final userData = locator<SharedRepository>().getUserData();
      final userId = userData?.data?.parent?.id;
      final authToken = locator<SharedRepository>().getToken();

      print("Attempting to load card for userId: $userId");

      if (userId == null) {
        throw Exception("User not logged in");
      }

      final response = await http.get(
        Uri.parse('${AppText.device}/api/payment/getcard/$userId'),
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken ?? '',
        },
      );

      print("Saved card API response status: ${response.statusCode}");
      print("Saved card API response body: ${response.body}");

      if (response.statusCode == 200) {
        final cardData = json.decode(response.body);

        // Create a CardModel from the response
        if (cardData != null && cardData['last4'] != null) {
          print("Valid card data found, updating UI");

          // Create the card model first
          final card = CardModel(
            name: cardData["name"],
            last4: cardData["last4"],
            expiryDate: cardData["expiry_date"],
            brand: cardData["brand"],
          );

          print(
              "Card model created: ${card.brand}, ${card.last4}, ${card.name}, ${card.expiryDate}");

          // Then update state with the data
          setState(() {
            savedCard = card;
            usingSavedCard = true;

            // Pre-fill the form with masked card data
            cardNumberController.text =
                "•••• •••• •••• ${savedCard!.last4 ?? ''}";
            cardholderController.text = savedCard!.name ?? '';
            cardCVCController.text =
                ""; // Reset CVC as it needs to be entered each time

            // Parse expiry date
            if (savedCard!.expiryDate != null) {
              final parts = savedCard!.expiryDate!.split('/');
              if (parts.length == 2) {
                cardExpMonthController.text = parts[0];
                cardExpYearController.text = parts[1];
              }
            }
          });

          print(
              "UI updated with card data: using saved card = $usingSavedCard");
          print("Card number field text: ${cardNumberController.text}");
        } else {
          print("Invalid card data: $cardData");
        }
      } else {
        print("Failed to get card data: ${response.statusCode}");
      }
    } catch (e) {
      print("Error loading saved card: $e");
    } finally {
      print("Finishing _loadSavedCardDetails, setting isLoading = false");
      setState(() {
        isLoading = false;
      });
      print("_loadSavedCardDetails finished, isLoading = $isLoading");
    }
  }

  void _switchToNewCard() {
    setState(() {
      usingSavedCard = false;
      cardNumberController.clear();
      cardholderController.clear();
      cardCVCController.clear();
      cardExpMonthController.clear();
      cardExpYearController.text = '';
    });
  }

  void _switchToSavedCard() {
    if (savedCard != null) {
      setState(() {
        usingSavedCard = true;
        cardNumberController.text = "•••• •••• •••• ${savedCard!.last4 ?? ''}";
        cardholderController.text = savedCard!.name ?? '';
        cardCVCController.text = "";

        // Parse expiry date
        if (savedCard!.expiryDate != null) {
          final parts = savedCard!.expiryDate!.split('/');
          if (parts.length == 2) {
            cardExpMonthController.text = parts[0];
            cardExpYearController.text = parts[1];
          }
        }
      });
    }
  }

  void _saveCard() async {
    // If we're in payment mode and have a saved card, we can skip most validation
    // but still need to verify CVC
    if (isPaymentMode && savedCard != null && usingSavedCard) {
      if (cardCVCController.text.length < 3 ||
          !RegExp(r'^[0-9]{3,4}$').hasMatch(cardCVCController.text)) {
        errorState(context: context, error: "Please enter a valid CVC");
        return;
      }

      try {
        // Show loading indicator
        loadingState(context: context);

        // Get the payment method ID
        final userData = locator<SharedRepository>().getUserData();
        final userId = userData?.data?.parent?.id;
        final authToken = locator<SharedRepository>().getToken();

        if (userId == null) {
          hideLoadingDialog(context);
          throw Exception("User not logged in");
        }

        // Get the existing payment method from the backend
        final response = await http.get(
          Uri.parse('${AppText.device}/api/payment/getpaymentmethod/$userId'),
          headers: {
            'Content-Type': 'application/json',
            'auth-token': authToken ?? '',
          },
        );

        print(
            "Payment method response: ${response.statusCode}, ${response.body}");

        if (response.statusCode != 200) {
          hideLoadingDialog(context);
          throw Exception(
              "Failed to retrieve payment method. Please try again.");
        }

        final paymentData = json.decode(response.body);
        final String paymentMethodId = paymentData['paymentMethodId'];

        if (paymentMethodId.isEmpty) {
          hideLoadingDialog(context);
          throw Exception("No payment method found. Please add a new card.");
        }

        // Process payment with the saved card
        await processPaymentWithSavedCard(paymentMethodId,
            cardCVCController.text, amountToPay!, zcoinsAmount!);

        return;
      } catch (e) {
        hideLoadingDialog(context);
        errorState(
            context: context,
            error: "Failed to process payment: ${e.toString()}");
        return;
      }
    }

    // Regular card validation for new cards
    if (cardNumberController.text.isEmpty ||
        cardCVCController.text.isEmpty ||
        cardholderController.text.isEmpty ||
        cardExpMonthController.text.isEmpty ||
        cardExpYearController.text.isEmpty) {
      errorState(context: context, error: "Please fill in all card details");
      return;
    }

    // Basic validation for card number (should be numeric and around 16 digits)
    if (cardNumberController.text
                .replaceAll(' ', '')
                .replaceAll('•', '')
                .length <
            13 ||
        (!RegExp(r'^[0-9\s]+$')
                .hasMatch(cardNumberController.text.replaceAll('•', '')) &&
            !cardNumberController.text.contains('•'))) {
      errorState(context: context, error: "Please enter a valid card number");
      return;
    }

    // Validate expiry month (1-12)
    int? month = int.tryParse(cardExpMonthController.text);
    if (month == null || month < 1 || month > 12) {
      errorState(context: context, error: "Please enter a valid month (1-12)");
      return;
    }

    // Validate expiry year (current year or later, 2-digit or 4-digit format)
    int? year = int.tryParse(cardExpYearController.text);
    int currentYear = DateTime.now().year;
    int currentYearShort = currentYear % 100;

    if (year == null) {
      errorState(context: context, error: "Please enter a valid year");
      return;
    }

    // Convert 2-digit year to 4-digit
    if (year < 100) {
      year += 2000;
    }

    // Check if card is expired
    if (year < currentYear ||
        (year == currentYear && month < DateTime.now().month)) {
      errorState(context: context, error: "Card has expired");
      return;
    }

    try {
      // Show loading indicator
      loadingState(context: context);

      CardTokenParams cardParams = CardTokenParams(
        type: TokenType.Card,
        name: cardholderController.text,
      );

      // Update card details in Stripe
      await Stripe.instance.dangerouslyUpdateCardDetails(CardDetails(
        number: cardNumberController.text
            .replaceAll('•', '4'), // Replace masked digits with 4s for demo
        cvc: cardCVCController.text,
        expirationMonth: int.tryParse(cardExpMonthController.text),
        expirationYear: int.tryParse(cardExpYearController.text),
      ));

      // Create token with Stripe
      TokenData tokenData = await Stripe.instance.createToken(
        CreateTokenParams.card(params: cardParams),
      );

      print("Flutter Stripe token created: ${tokenData.id}");

      // If in payment mode, process payment directly
      if (isPaymentMode && amountToPay != null && zcoinsAmount != null) {
        await processPayment(tokenData.id, amountToPay!, zcoinsAmount!);
      } else {
        // Just save the card if not in payment mode
        await sendTokenToBackend(tokenData.id);

        // Hide loading indicator
        hideLoadingDialog(context);

        // Show success message
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text("Success"),
            content: Text("Your card has been saved successfully."),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context); // Close dialog
                  // Trigger card refresh before going back
                  context.read<UserBloc>().add(GetCardEvent());
                  Navigator.pop(
                      context); // Go back to previous screen (subscriptions)
                },
                child: Text("OK"),
              ),
            ],
          ),
        );
      }
    } on StripeException catch (e) {
      hideLoadingDialog(context);
      errorState(context: context, error: e.toString());
    } catch (e) {
      hideLoadingDialog(context);
      errorState(
          context: context, error: "Failed to save card: ${e.toString()}");
    }
  }

  Future<void> processPayment(String cardToken, int amount, int zcoins) async {
    final userData = locator<SharedRepository>().getUserData();
    final userId = userData?.data?.parent?.id;
    final authToken = locator<SharedRepository>().getToken();

    if (userId == null) {
      hideLoadingDialog(context);
      throw Exception("User not logged in");
    }

    try {
      print(
          "Processing payment with amount: $amount, cardToken: ${cardToken.substring(0, 8)}...");

      final response = await http.post(
        Uri.parse('${AppText.device}/api/payment/purchase'),
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken ?? '',
        },
        body: json.encode({
          'userId': userId,
          'amount': amount,
          'cardToken': cardToken,
        }),
      );

      print("Payment response: ${response.statusCode}, ${response.body}");

      // Hide loading indicator
      hideLoadingDialog(context);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        // If this is a class booking, create the order after successful payment
        if (isClassBooking && orderDetails != null) {
          context.read<UserBloc>().add(CreateOrderEvent(
                orderData: orderDetails!,
              ));
        } else {
          // For ZCoin purchases or subscriptions, update the balance
          await updateBalance(userId, zcoins);
          if (isSubscription && planId != null) {
            await createSubscription(userId, planId!);
          }
        }

        // Show success message
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text("Payment Successful"),
            content: Text(isSubscription
                ? "You have successfully subscribed to the plan and received $zcoins ZCoins."
                : isClassBooking
                    ? "Payment successful! Your class booking has been confirmed."
                    : "You have successfully purchased $zcoins ZCoins."),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  // Navigate based on payment type
                  if (isClassBooking) {
                    // Navigate to reserved/confirmation page for class bookings
                    NavigatorService.pushNamedAndRemoveUntil(
                      AppRoutes.homePage,
                      arguments: null,
                    );
                    NavigatorService.pushNamed(AppRoutes.reserved);
                  } else {
                    // Navigate back to wallet screen with updated balance
                    NavigatorService.pushNamedAndRemoveUntil(
                      AppRoutes.homePage,
                      arguments: null,
                    );
                    // Navigate to subscription screen if it was a subscription purchase, otherwise to wallet
                    if (isSubscription) {
                      NavigatorService.pushNamed(AppRoutes.mySubscriptions);
                    } else {
                      NavigatorService.pushNamed(AppRoutes.myWallet,
                          arguments: zcoinsAmount);
                    }
                  }
                },
                child: Text("OK"),
              ),
            ],
          ),
        );
      } else {
        String errorMessage = 'Payment failed';
        try {
          final errorData = json.decode(response.body);
          if (errorData['message'] != null) {
            errorMessage = errorData['message'];
          }
        } catch (e) {
          errorMessage = 'Payment failed: HTTP ${response.statusCode}';
        }

        throw Exception(errorMessage);
      }
    } catch (e) {
      hideLoadingDialog(context);
      errorState(context: context, error: e.toString());
    }
  }

  Future<void> processPaymentWithSavedCard(
      String paymentMethodId, String cvc, int amount, int zcoins) async {
    final userData = locator<SharedRepository>().getUserData();
    final userId = userData?.data?.parent?.id;
    final authToken = locator<SharedRepository>().getToken();

    if (userId == null) {
      hideLoadingDialog(context);
      throw Exception("User not logged in");
    }

    try {
      print("Processing payment with saved card, amount: $amount");

      // Validate the CVC locally using Stripe SDK
      try {
        // Update the card details locally with just the CVC
        await Stripe.instance.dangerouslyUpdateCardDetails(
          CardDetails(cvc: cvc),
        );
      } catch (e) {
        print("Error validating CVC: $e");
        throw Exception(
            "Invalid security code (CVC). Please check and try again.");
      }

      // Now send the payment request to the backend without the CVC
      final response = await http.post(
        Uri.parse('${AppText.device}/api/payment/paywithsavedcard'),
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken ?? '',
        },
        body: json.encode({
          'userId': userId,
          'amount': amount,
          'paymentMethodId': paymentMethodId,
        }),
      );

      print(
          "Payment with saved card response: ${response.statusCode}, ${response.body}");

      // Hide loading indicator
      hideLoadingDialog(context);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        print("Payment with saved card successful, updating balance");
        // Payment successful, update balance
        await updateBalance(userId, zcoins);

        // If this is a subscription purchase, create the subscription
        if (isSubscription && planId != null) {
          await createSubscription(userId, planId!);
        }

        // If this is a class booking, create the order after successful payment
        if (isClassBooking && orderDetails != null) {
          await createClassOrder(orderDetails!);
        }

        // Show success dialog
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text("Payment Successful"),
            content: Text(isSubscription
                ? "You have successfully subscribed to the plan and received $zcoins ZCoins."
                : isClassBooking
                    ? "Payment successful! Your class booking has been confirmed."
                    : "You have successfully purchased $zcoins ZCoins."),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  // Navigate based on payment type
                  if (isClassBooking) {
                    // Navigate to reserved/confirmation page for class bookings
                    NavigatorService.pushNamedAndRemoveUntil(
                      AppRoutes.homePage,
                      arguments: null,
                    );
                    NavigatorService.pushNamed(AppRoutes.reserved);
                  } else {
                    // Navigate back to home and then to the appropriate screen
                    NavigatorService.pushNamedAndRemoveUntil(
                      AppRoutes.homePage,
                      arguments: null,
                    );
                    if (isSubscription) {
                      NavigatorService.pushNamed(AppRoutes.mySubscriptions);
                    } else {
                      NavigatorService.pushNamed(AppRoutes.myWallet,
                          arguments: zcoinsAmount);
                    }
                  }
                },
                child: Text("OK"),
              ),
            ],
          ),
        );
      } else {
        String errorMessage = 'Payment failed';
        try {
          final errorData = json.decode(response.body);
          if (errorData['message'] != null) {
            String rawMessage = errorData['message'];

            // Make error messages more user-friendly
            if (rawMessage.contains("authentication_required")) {
              errorMessage =
                  "Your card requires authentication. Please try again or use a different card.";
            } else if (rawMessage.contains("card_declined")) {
              errorMessage =
                  "Your card was declined. Please try a different card.";
            } else if (rawMessage.contains("insufficient_funds")) {
              errorMessage =
                  "Your card has insufficient funds. Please try a different card.";
            } else if (rawMessage.contains("invalid_cvc")) {
              errorMessage =
                  "The security code (CVC) you entered is invalid. Please check and try again.";
            } else if (rawMessage.contains("expired_card")) {
              errorMessage =
                  "Your card has expired. Please use a different card.";
            } else {
              // Trim long error messages to a reasonable length
              if (rawMessage.length > 100) {
                rawMessage = rawMessage.substring(0, 100) + "...";
              }
              errorMessage = "Payment failed: " + rawMessage;
            }
          }
        } catch (e) {
          errorMessage = 'Payment failed: HTTP ${response.statusCode}';
        }

        throw Exception(errorMessage);
      }
    } catch (e) {
      hideLoadingDialog(context);
      String errorMsg = e.toString();

      // Clean up error message by removing "Exception: " prefix
      if (errorMsg.startsWith("Exception: ")) {
        errorMsg = errorMsg.substring("Exception: ".length);
      }

      errorState(context: context, error: errorMsg);
    }
  }

  Future<void> updateBalance(String userId, int zcoins) async {
    try {
      final authToken = locator<SharedRepository>().getToken();

      print("💰 Adding $zcoins ZCoins to user $userId");

      final response = await http.post(
        Uri.parse('${AppText.device}/api/balance/add/$userId'),
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken ?? '',
        },
        body: json.encode({'amount': zcoins}),
      );

      if (response.statusCode == 200) {
        print(
            "💰 Balance successfully updated, added $zcoins ZCoins. New balance: ${response.body}");
      } else {
        print(
            "❌ Balance update failed: ${response.statusCode} - ${response.body}");
      }
    } catch (e) {
      print("❌ Error updating balance: $e");
    }
  }

  // New method to create subscription after successful payment
  Future<void> createSubscription(String userId, String planId) async {
    try {
      final authToken = locator<SharedRepository>().getToken();

      print("🔄 Creating subscription for user $userId with plan $planId");

      final response = await http.post(
        Uri.parse('${AppText.device}/api/subscription'),
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken ?? '',
        },
        body: json.encode({
          'userId': userId,
          'planId': planId,
        }),
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        print("✅ Subscription created successfully: ${response.body}");
      } else {
        print(
            "❌ Subscription creation failed: ${response.statusCode} - ${response.body}");
        throw Exception("Failed to create subscription");
      }
    } catch (e) {
      print("❌ Error creating subscription: $e");
      throw e;
    }
  }

  Future<void> createClassOrder(Map<String, dynamic> orderDetails) async {
    try {
      final authToken = locator<SharedRepository>().getToken();

      print(
          "🔄 Creating class order for user with order details: $orderDetails");

      final response = await http.post(
        Uri.parse('${AppText.device}/api/orders'),
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken ?? '',
        },
        body: jsonEncode(orderDetails),
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        print("✅ Class order created successfully: ${response.body}");
      } else {
        print(
            "❌ Class order creation failed: ${response.statusCode} - ${response.body}");
        throw Exception("Failed to create class order");
      }
    } catch (e) {
      print("❌ Error creating class order: $e");
      throw e;
    }
  }

  Future<void> sendTokenToBackend(String token) async {
    final userData = locator<SharedRepository>().getUserData();
    final userId = userData?.data?.parent?.id;
    final authToken = locator<SharedRepository>().getToken();

    print("Sending token to backend with userId: $userId");
    print("Auth token available: ${authToken != null}");

    if (userId == null) {
      throw Exception("User not logged in");
    }

    try {
      print("Making API request to ${AppText.device}/api/payment/savecard");

      final requestBody = {
        'userId': userId,
        'cardToken': token,
      };
      print("Request body: ${json.encode(requestBody)}");

      final response = await http.post(
        Uri.parse('${AppText.device}/api/payment/savecard'),
        headers: {
          'Content-Type': 'application/json',
          'auth-token': authToken ?? '',
        },
        body: json.encode(requestBody),
      );

      print("Response status code: ${response.statusCode}");
      print("Response body: ${response.body}");

      if (response.statusCode >= 200 && response.statusCode < 300) {
        print('Card saved successfully');
      } else {
        // Parse the error message from the response if possible
        String errorMessage = 'Failed to save card';
        try {
          final errorData = json.decode(response.body);
          if (errorData['message'] != null) {
            errorMessage = errorData['message'];
          }
        } catch (e) {
          // If parsing fails, use the status code
          errorMessage = 'Failed to save card: HTTP ${response.statusCode}';
        }

        print('Failed to save card: ${response.statusCode}, ${response.body}');
        throw Exception(errorMessage);
      }
    } catch (e) {
      // Re-throw the exception with more context if it's not already an Exception
      if (e is! Exception) {
        throw Exception('Network error while saving card: ${e.toString()}');
      }
      rethrow;
    }
  }

  String? _getBrandFromNumber(String number) {
    final cleanNumber = number.replaceAll(' ', '').replaceAll('•', '');
    if (cleanNumber.startsWith('4')) {
      return 'visa';
    } else if (cleanNumber.startsWith(
        RegExp(r'^(5[1-5]|222[1-9]|22[3-9]|2[3-6]|27[0-1]|2720)'))) {
      return 'mastercard';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    print(
        "Building CreditCardScreen: paymentMode=$isPaymentMode, usingSavedCard=$usingSavedCard, isLoading=$isLoading");
    if (savedCard != null) {
      print(
          "Saved card available: ${savedCard!.brand} ending in ${savedCard!.last4}");
    } else {
      print("No saved card available");
    }

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        appBar: CustomAppBar(
          title: isPaymentMode ? "Payment" : "Credit Card",
          leading: customBackButton(),
        ),
        bottomNavigationBar: customBottomButton(
          buttonText: isPaymentMode ? "Pay Now" : "Save Card",
          onTap: () {
            _saveCard();
          },
        ),
        body: isLoading
            ? Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 24.w, vertical: 20.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Message for saved card
                      if (isPaymentMode && savedCard != null) ...[
                        Container(
                          padding: EdgeInsets.all(16.w),
                          margin: EdgeInsets.only(bottom: 20.h),
                          decoration: BoxDecoration(
                            color: usingSavedCard
                                ? Colors.green.withOpacity(0.1)
                                : Colors.grey.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(
                                color: usingSavedCard
                                    ? Colors.green.withOpacity(0.3)
                                    : Colors.grey.withOpacity(0.3)),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                usingSavedCard
                                    ? Icons.check_circle
                                    : Icons.credit_card,
                                color:
                                    usingSavedCard ? Colors.green : Colors.grey,
                                size: 24.sp,
                              ),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      usingSavedCard
                                          ? "Using saved card"
                                          : "Saved card available",
                                      style: TextStyle(
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.w600,
                                        color: usingSavedCard
                                            ? Colors.green[800]
                                            : Colors.grey[800],
                                      ),
                                    ),
                                    SizedBox(height: 4.h),
                                    Text(
                                      usingSavedCard
                                          ? "You're using your saved ${savedCard!.brand} card ending in ${savedCard!.last4}. Please enter the CVC to confirm payment."
                                          : "You have a saved ${savedCard!.brand} card ending in ${savedCard!.last4}.",
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: usingSavedCard
                                            ? Colors.green[800]
                                            : Colors.grey[800],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (!usingSavedCard)
                                TextButton(
                                  onPressed: _switchToSavedCard,
                                  style: TextButton.styleFrom(
                                    backgroundColor: AppPallete.secondaryColor
                                        .withOpacity(0.1),
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12.w, vertical: 8.h),
                                  ),
                                  child: Text(
                                    "Use Card",
                                    style: TextStyle(
                                      color: AppPallete.secondaryColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              if (usingSavedCard)
                                TextButton(
                                  onPressed: _switchToNewCard,
                                  style: TextButton.styleFrom(
                                    backgroundColor: Colors.grey[200],
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12.w, vertical: 8.h),
                                  ),
                                  child: Text(
                                    "Use New",
                                    style: TextStyle(
                                      color: Colors.grey[700],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                      // Credit card visual preview
                      CustomCreditCard(
                        brand: usingSavedCard
                            ? savedCard?.brand
                            : _getBrandFromNumber(cardNumberController.text),
                        last4: usingSavedCard
                            ? savedCard?.last4
                            : (cardNumberController.text.length > 4
                                ? cardNumberController.text
                                    .replaceAll(' ', '')
                                    .substring(cardNumberController.text
                                            .replaceAll(' ', '')
                                            .length -
                                        4)
                                : null),
                        cardholderName: cardholderController.text,
                        expiryDate: (cardExpMonthController.text.isNotEmpty &&
                                cardExpYearController.text.isNotEmpty)
                            ? "${cardExpMonthController.text}/${cardExpYearController.text}"
                            : null,
                      ),

                      SizedBox(height: 30.h),

                      // Card input section header
                      Text(
                        "Card Information",
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w600,
                          color: AppPallete.darkGrey,
                        ),
                      ),

                      SizedBox(height: 20.h),

                      // If using saved card, only show CVC field
                      if (isPaymentMode && usingSavedCard)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildInputLabel("Security Code (CVC)"),
                            SizedBox(height: 8.h),
                            AuthField(
                              controller: cardCVCController,
                              height: 50.h,
                              width: double.infinity,
                              hintText: "123",
                              keyboard: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                LengthLimitingTextInputFormatter(4),
                              ],
                              suffixIcon:
                                  Icon(Icons.lock, color: AppPallete.greyWord),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return "CVC is required";
                                }
                                if (!RegExp(r'^[0-9]{3,4}$').hasMatch(value)) {
                                  return "Invalid CVC";
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              "The CVC is the 3 or 4-digit security code on the back of your card",
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: AppPallete.greyWord,
                              ),
                            ),
                          ],
                        )
                      else
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Card Number
                            _buildInputLabel("Card Number"),
                            SizedBox(height: 8.h),
                            AuthField(
                              controller: cardNumberController,
                              onChanged: (_) => setState(() {}),
                              height: 50.h,
                              width: double.infinity,
                              hintText: "1234 5678 9012 3456",
                              keyboard: TextInputType.number,
                              inputFormatters: [CardNumberInputFormatter()],
                              suffixIcon: Icon(Icons.credit_card,
                                  color: AppPallete.greyWord),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return "Card number is required";
                                }
                                if (value
                                            .replaceAll(' ', '')
                                            .replaceAll('•', '')
                                            .length <
                                        13 ||
                                    (!RegExp(r'^[0-9\s]+$').hasMatch(
                                            value.replaceAll('•', '')) &&
                                        !value.contains('•'))) {
                                  return "Please enter a valid card number";
                                }
                                return null;
                              },
                            ),

                            SizedBox(height: 20.h),

                            // Cardholder Name
                            _buildInputLabel("Cardholder Name"),
                            SizedBox(height: 8.h),
                            AuthField(
                              controller: cardholderController,
                              onChanged: (_) => setState(() {}),
                              height: 50.h,
                              width: double.infinity,
                              hintText: "Name as it appears on card",
                              suffixIcon: Icon(Icons.person,
                                  color: AppPallete.greyWord),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return "Cardholder name is required";
                                }
                                return null;
                              },
                            ),

                            SizedBox(height: 20.h),

                            // Expiry and CVC row
                            Row(
                              children: [
                                // Expiry date column
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      _buildInputLabel("Expiry Date"),
                                      SizedBox(height: 8.h),
                                      Row(
                                        children: [
                                          // Month field
                                          Expanded(
                                            child: AuthField(
                                              controller:
                                                  cardExpMonthController,
                                              onChanged: (_) => setState(() {}),
                                              height: 50.h,
                                              hintText: "MM",
                                              keyboard: TextInputType.number,
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .digitsOnly,
                                                LengthLimitingTextInputFormatter(
                                                    2),
                                              ],
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return "Required";
                                                }
                                                int? month =
                                                    int.tryParse(value);
                                                if (month == null ||
                                                    month < 1 ||
                                                    month > 12) {
                                                  return "Invalid";
                                                }
                                                return null;
                                              },
                                            ),
                                          ),
                                          Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 8.w),
                                            child: Text(
                                              "/",
                                              style: TextStyle(
                                                fontSize: 20.sp,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                          // Year field
                                          Expanded(
                                            child: AuthField(
                                              controller: cardExpYearController,
                                              onChanged: (_) => setState(() {}),
                                              height: 50.h,
                                              hintText: "YY",
                                              keyboard: TextInputType.number,
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .digitsOnly,
                                                LengthLimitingTextInputFormatter(
                                                    2),
                                              ],
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return "Required";
                                                }
                                                int? year = int.tryParse(value);
                                                if (year == null) {
                                                  return "Invalid";
                                                }
                                                return null;
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),

                                SizedBox(width: 20.w),

                                // CVC field
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      _buildInputLabel("CVC"),
                                      SizedBox(height: 8.h),
                                      AuthField(
                                        controller: cardCVCController,
                                        height: 50.h,
                                        hintText: "123",
                                        keyboard: TextInputType.number,
                                        inputFormatters: [
                                          FilteringTextInputFormatter
                                              .digitsOnly,
                                          LengthLimitingTextInputFormatter(4),
                                        ],
                                        suffixIcon: Icon(Icons.lock,
                                            color: AppPallete.greyWord),
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return "CVC is required";
                                          }
                                          if (!RegExp(r'^[0-9]{3,4}$')
                                              .hasMatch(value)) {
                                            return "Invalid CVC";
                                          }
                                          return null;
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),

                      SizedBox(height: 20.h),

                      // Security info
                      Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.security,
                                color: AppPallete.secondaryColor),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: Text(
                                "Your card details are secure and encrypted",
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: AppPallete.darkGrey,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  // Helper method to build consistent input labels
  Widget _buildInputLabel(String label) {
    return Text(
      label,
      style: TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w500,
        color: AppPallete.darkGrey,
      ),
    );
  }
}
