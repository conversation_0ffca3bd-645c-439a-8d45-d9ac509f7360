import 'package:class_z/core/imports.dart';
import 'package:class_z/core/common/presentation/blocs/savedCenterBloc/saved_center_bloc.dart';
import 'package:class_z/core/common/domain/repositories/saved_center_repository.dart';

class CentreSpotlight extends StatefulWidget {
  final List<CenterData> centerModel;
  const CentreSpotlight({required this.centerModel, super.key});

  @override
  State<CentreSpotlight> createState() => _CentreSpotlightState();
}

class _CentreSpotlightState extends State<CentreSpotlight> {
  int _activeIndex = 0;
  String userId = '';
  // Add streams for badge counts
  late StreamSubscription<int> _notificationCountSubscription;
  late StreamSubscription<int> _messageCountSubscription;
  int _notificationCount = 0;
  int _messageCount = 0;
  int _currentPage = 1;
  final int _limit = 10;
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  List<CenterData> _centers = [];
  final ScrollController _scrollController = ScrollController();
  // Saved centers map to track saved status
  Map<String, bool> savedCentersMap = {};

  // Flag to prevent infinite checking of saved centers
  bool _hasCentersBeenChecked = false;

  // Add rate limiting to prevent too many concurrent requests
  int _lastCheckTime = 0;
  static const int _checkCooldown = 1000; // 1 second cooldown

  @override
  void initState() {
    super.initState();
    _centers = widget.centerModel;

    // Fetch user ID in a safer way first, then check saved status
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        try {
          userId = locator<SharedRepository>().getUserId();

          // Initialize badge count service
          final badgeService = locator<BadgeCountService>();
          badgeService.initialize(userId);

          // Listen for notification count updates
          _notificationCountSubscription =
              badgeService.notificationCountStream.listen((count) {
            setState(() => _notificationCount = count);
          });

          // Listen for message count updates
          _messageCountSubscription =
              badgeService.messageCountStream.listen((count) {
            setState(() => _messageCount = count);
          });

          // Set initial counts
          _notificationCount = badgeService.notificationCount;
          _messageCount = badgeService.messageCount;
        } catch (e) {
          print('Error getting userId or initializing badge counts: $e');
          userId = '';
        }
      });

      // Check saved status for all centers after user ID is set
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          _checkSavedCenters();
        }
      });
    });
    _scrollController.addListener(() {
      print('here is center');
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 100 &&
          !_isLoadingMore &&
          _hasMoreData) {
        _loadMoreCenters();
      }
    });
  }

  void _checkSavedCenters() {
    // Rate limiting: prevent too frequent checks
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    if (currentTime - _lastCheckTime < _checkCooldown) {
      print('Rate limited: skipping saved centers check');
      return;
    }
    _lastCheckTime = currentTime;

    // Collect all center IDs that need checking
    final centerIdsToCheck = <String>[];
    for (var center in _centers) {
      if (center.id != null && !savedCentersMap.containsKey(center.id!)) {
        centerIdsToCheck.add(center.id!);
      }
    }

    if (centerIdsToCheck.isEmpty) {
      return;
    }

    // Use batch check instead of individual calls
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mounted) {
        try {
          final parentId = locator<SharedRepository>().getUserId();
          final repository = locator<SavedCenterRepository>();

          final results = await repository.batchCheckSavedCenters(
            parentId: parentId,
            centerIds: centerIdsToCheck,
          );

          if (mounted) {
            setState(() {
              savedCentersMap.addAll(results);
            });
          }
        } catch (e) {
          // Silent error handling
        }
      }
    });

    // Mark centers as checked to prevent infinite loop
    _hasCentersBeenChecked = true;
  }

  void _refreshSavedStatus() {
    // Force refresh of all center saved statuses
    for (var center in _centers) {
      if (center.id != null && mounted) {
        context.read<SavedCenterBloc>().add(CheckCenterSavedEvent(center.id!));
      }
    }
  }

  void _onFavoriteToggle(String centerId, bool newValue) {
    // Immediately update the UI state to provide instant feedback
    setState(() {
      savedCentersMap[centerId] = newValue;
    });

    if (newValue) {
      context.read<SavedCenterBloc>().add(SaveCenterEvent(centerId));
    } else {
      context.read<SavedCenterBloc>().add(UnsaveCenterEvent(centerId));
    }
  }

  @override
  void dispose() {
    // Cancel stream subscriptions to prevent memory leaks
    _notificationCountSubscription.cancel();
    _messageCountSubscription.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  void _onTabIndexChanged(int index) {
    setState(() {
      _activeIndex = index;
    });
    handleTabIndexChanged(index);
  }

  // Get user ID safely when needed
  String _getUserId() {
    if (userId.isEmpty) {
      try {
        return locator<SharedRepository>().getUserId();
      } catch (e) {
        print('Error getting userId: $e');
        return '';
      }
    }
    return userId;
  }

  // Navigate to messages/chat safely
  void _navigateToMessages() {
    try {
      final currentUserId = _getUserId();
      if (currentUserId.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('User ID not available. Please try again later.')),
        );
        return;
      }

      // Get all necessary data for the chat
      final sharedRepo = locator<SharedRepository>();
      final parentData = sharedRepo.getParentData();
      final String? userImage = parentData?.image?.url;

      // Find the first center or use a default placeholder
      CenterData? centerData =
          widget.centerModel.isNotEmpty ? widget.centerModel[0] : null;

      // If we don't have a center, we can still navigate to messages list instead
      if (centerData == null) {
        // Fallback: Navigate to main message list if available
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No centers available for messaging')),
        );
        return;
      }
      if (currentUserId.isNotEmpty) {
        NavigatorService.pushNamed(AppRoutes.centerMessage, arguments: 'user');
      } else
        errorState(context: context, error: 'please login');
      // Required data for Chat component
      // final Map<String, dynamic> messagesData = {
      //   "title": centerData.legalName ?? "Center",
      //   "imagePath": centerData.mainImage?.url ?? '',
      //   "senderImage": userImage ?? '',
      //   "id": centerData.id ?? '',
      //   "senderId": currentUserId,
      //   "senderType": "user",
      //   "oppositeModel": "center",
      // };

      // NavigatorService.pushNamed(
      //   AppRoutes.chat,
      //   arguments: messagesData,
      // );
    } catch (e) {
      print('Error navigating to chat: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not open chat: $e')),
      );
    }
  }

  void _fetchCenters({required int page}) {
    setState(() {
      _isLoadingMore = true;
    });

    context
        .read<CenterBloc>()
        .add(GetAllCenterEvent(page: page, limit: _limit));
  }

  void _loadMoreCenters() {
    if (_isLoadingMore) return;

    _currentPage++;
    _fetchCenters(page: _currentPage);
  }

  void _updateCenters(List<CenterData> newCenters) {
    setState(() {
      _isLoadingMore = false;

      if (newCenters.length < _limit) {
        _hasMoreData = false; // No more data from backend
      }

      _centers.addAll(newCenters);
    });

    // Check saved status for new centers
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _checkSavedCenters();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<SavedCenterBloc>(
          create: (context) => SavedCenterBloc(
            savedCenterRepository: locator<SavedCenterRepository>(),
            sharedRepository: locator<SharedRepository>(),
          ),
        ),
      ],
      child: Scaffold(
        body: Padding(
          padding: EdgeInsets.only(left: 9.w, right: 9.w),
          child: MultiBlocListener(
            listeners: [
              BlocListener<SavedCenterBloc, SavedCenterState>(
                listener: (context, state) {
                  if (state is CenterSavedSuccess) {
                    setState(() {
                      savedCentersMap[state.centerId] = true;
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Centre saved successfully')),
                    );
                  } else if (state is CenterUnsavedSuccess) {
                    setState(() {
                      savedCentersMap[state.centerId] = false;
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Centre removed from saved')),
                    );
                  } else if (state is SavedCenterError) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(state.message)),
                    );
                  }
                },
              ),
              BlocListener<CenterBloc, CenterState>(
                listener: (context, state) {
                  if (state is CenterListFetchSuccess) {
                    // Handle success state here
                    //   print("Success: ${state.center}");
                    _updateCenters(state.centers);

                    //  print(centers);

                    // You can perform any action you need here, like navigating to another page or showing a success message
                  }
                  if (state is CenterErrorState) {
                    setState(() {
                      _isLoadingMore = false;
                    });
                    errorState(context: context, error: state.message);
                    // Handle error state here
                    // print("Error: ${state.message}");
                  }
                },
              )
            ],
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding:
                        EdgeInsets.only(top: 70.h, left: 19.w, right: 34.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        GestureDetector(
                          onTap: () {
                            NavigatorService.goBack();
                          },
                          child: Icon(
                            Icons.arrow_back_ios,
                            color: AppPallete.secondaryColor,
                            size: 24.w,
                          ),
                        ),
                        Row(
                          children: [
                            GestureDetector(
                              onTap: () {
                                // Navigate to notification screen with user ID
                                final currentUserId = _getUserId();
                                NavigatorService.pushNamed(
                                  AppRoutes.notification,
                                  arguments: currentUserId,
                                ).then((_) {
                                  // Refresh notification count after returning from notifications screen
                                  locator<BadgeCountService>()
                                      .refreshNotificationCount(currentUserId);
                                });
                              },
                              child: notification_badge(
                                  context: context,
                                  icon: Icons.notifications,
                                  badgeCount: _notificationCount),
                            ),
                            SizedBox(width: 4.w),
                            GestureDetector(
                              onTap: _navigateToMessages,
                              child: notification_badge(
                                  context: context,
                                  icon: Icons.message,
                                  badgeCount: _messageCount),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 5.h),
                  const Text(
                    "Centre Spotlight",
                    style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 20.h),
                  _centers.isNotEmpty
                      ? ListView.separated(
                          shrinkWrap: true,
                          padding: EdgeInsets.only(top: 0),
                          physics: const NeverScrollableScrollPhysics(),
                          scrollDirection: Axis.vertical,
                          itemBuilder: (context, index) {
                            if (index < _centers.length) {
                              var center = _centers[index];
                              // Get saved status from map
                              final isSaved =
                                  savedCentersMap[center.id] ?? false;

                              // Use the buildCenterRowGallery function with saved status
                              return buildCenterRowGallery(
                                context: context,
                                center: center,
                                isSaved: isSaved,
                                onFavoriteToggle: (newValue) {
                                  if (center.id != null) {
                                    _onFavoriteToggle(center.id!, newValue);
                                  }
                                },
                                onTap: () {
                                  NavigatorService.pushNamed(
                                      AppRoutes.centreView,
                                      arguments: {
                                        'center': center,
                                        "isSaved": isSaved,
                                        'bottomView': true
                                      });
                                },
                              );
                            } else {
                              return const Padding(
                                padding: EdgeInsets.all(8.0),
                                child:
                                    Center(child: CircularProgressIndicator()),
                              );
                            }
                          },
                          separatorBuilder: (context, index) {
                            return SizedBox(height: 29.h);
                          },
                          itemCount: _centers.length + (_isLoadingMore ? 1 : 0),
                        )
                      : SizedBox(
                          height: getHeight(context: context) / 2,
                          child: Center(
                            child: Text(
                              "There is no center",
                              style: TextStyle(
                                  fontSize: 18.sp, fontWeight: FontWeight.w500),
                            ),
                          ),
                        ),
                  SizedBox(height: 16.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
