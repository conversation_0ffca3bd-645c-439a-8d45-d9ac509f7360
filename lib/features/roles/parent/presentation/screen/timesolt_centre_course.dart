import 'package:class_z/core/common/data/models/classDate_model.dart';
import 'package:class_z/core/imports.dart';

class TimeSlotCentreCourse extends StatefulWidget {
  final CenterData? center;
  final String? coachName;
  final ClassModel? classModel;
  const TimeSlotCentreCourse(
      {super.key, this.classModel, this.center, this.coachName});

  @override
  State<TimeSlotCentreCourse> createState() => _TimeSlotCentreCourse();
}

class _TimeSlotCentreCourse extends State<TimeSlotCentreCourse> {
  final PageController pageController = PageController();
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  late ValueNotifier<List<EventModel>> _selectedEvents;
  List<EventModel> events = [];
  EventDatesModel? eventDates;
  List<String> _calendarDates = [];
  ValueNotifier<int> currentPageIndex = ValueNotifier<int>(0);

  @override
  void initState() {
    super.initState();
    _selectedEvents = ValueNotifier<List<EventModel>>([]);

    context
        .read<CenterBloc>()
        .add(GetEventsByClassIdEvent(classId: widget.classModel?.id ?? ""));
  }

  // Callback when a day is selected
  void _onselectedDay(DateTime selectedDay, DateTime focusedDay) {
    setState(() {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
      // _selectedEvents.value =
      //     _getEventsForDay(_selectedDay!); // Update the event list
    });
  }

  // // Function to get events for a given day
  // List<EventModel> _getEventsForDay(DateTime dateTime) {
  //   return events
  //       .where((event) =>
  //           event.date?.year == dateTime.year &&
  //           event.date?.month == dateTime.month &&
  //           event.date?.day == dateTime.day)
  //       .toList();
  // }

  int _checkClassNumber(DateTime eventDate, List<EventModel> matchingEvents) {
    // Sort matching events by date
    final sortedEvents = List<EventModel>.from(matchingEvents);
    sortedEvents.sort((a, b) {
      if (a.date == null || b.date == null) return 0;
      return a.date!.compareTo(b.date!);
    });

    // Find the index of the event with the given date
    for (int i = 0; i < sortedEvents.length; i++) {
      final event = sortedEvents[i];
      if (event.date != null &&
          event.date!.year == eventDate.year &&
          event.date!.month == eventDate.month &&
          event.date!.day == eventDate.day) {
        return i + 1; // Adding 1 to make it 1-based indexing
      }
    }
    return 0; // Return 0 if date not found
  }

  bool _checkIfEligibleAndSendRequest(List<EventModel> finalEvents) {
    DateTime now = DateTime.now();
    bool newComer =
        widget.classModel?.newComer ?? true; // default to true if null

    // Check if finalEvents is empty first
    if (finalEvents.isEmpty) {
      return false; // Return false if there are no events
    }

    DateTime? finalDate = finalEvents[0].date;

    if (finalDate != null) {
      if (now.isBefore(finalDate)) {
        return true;
      } else {
        return newComer;
      }
    } else {
      return false;
    }
  }

  int zcoinToHkd(int? zcoin) => zcoin != null ? zcoin * 25 : 0;

  @override
  Widget build(BuildContext context) {
    print(widget.classModel);
    return Scaffold(
        body: BlocListener<CenterBloc, CenterState>(
      listener: (context, state) {
        if (state is CenterLoadingState)
          loadingState(context: context);
        else
          hideLoadingDialog(context);
        if (state is CenterErrorState)
          errorState(context: context, error: state.message);
        if (state is EventDatesForClassSuccess) {
          setState(() {
            eventDates = state.eventsDate;

            //  print(eventDates);
            // _selectedEvents.value =
            //     _getEventsForDay(_selectedDay ?? _focusedDay);
          });
        }
      },
      child: DefaultTabController(
        length: 2,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomAppBarDouble(
              title: widget.center?.displayName ?? "Unknown Name",
              title2: "Schedule the experience",
              leading: customBackButton(),
            ),
            SizedBox(height: 24.h),
            buildProgressSteps(
                context: context,
                cont1: AppPallete.secondaryColor,
                cont2: AppPallete.dividerTime,
                cont3: AppPallete.dividerTime),
            SizedBox(
              height: 35.h,
            ),
            SizedBox(
              height: 200.h,
              //width: 430.w,
              child: PageView.builder(
                controller: pageController,
                itemCount: eventDates?.eventDates?.length,
                onPageChanged: (index) {
                  currentPageIndex.value = index;
                },
                itemBuilder: (context, index) {
                  // final matchingEvents = events
                  //     .where((eventDetail) =>
                  //         eventDetail.dateId ==
                  //         widget.classModel?.dates![index].id)
                  //     .toList();
                  // final isJoinable =
                  //     _checkIfEligibleAndSendRequest(matchingEvents);
                  return Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      centreListCourse(
                          context: context,
                          title: widget.classModel?.classProviding ?? "",
                          imagePath: imageStringGenerator(
                              imagePath:
                                  widget.classModel?.mainImage?.url ?? ''),
                          category: widget.classModel?.level ?? "",
                          name: widget.coachName ?? "",
                          location:
                              eventDates?.eventDates?[index].dateId?.address ??
                                  (widget.classModel?.address ?? ""),
                          language: eventDates
                                  ?.eventDates?[index].dateId?.languageOptions
                                  ?.toString() ??
                              (widget.classModel?.language.toString() ?? ""),
                          ageGroup:
                              "${widget.classModel?.ageFrom} - ${widget.classModel?.ageTo}",
                          currentStudent: eventDates?.eventDates?[index].dateId
                                  ?.students?.length ??
                              0,
                          totalStudent: eventDates?.eventDates?[index].dateId
                                  ?.numberOfStudent ??
                              0,
                          sen: widget.classModel?.sen ?? false,
                          isJoinable: true,
                          onTap: () {
                            NavigatorService.pushNamed(
                              AppRoutes.request,
                              arguments: {
                                'classModel': widget.classModel,
                                'center': widget.center,
                                'eventDetails': eventDates
                                    ?.eventDates?[currentPageIndex.value],
                              },
                            );
                          }),
                      // Show charge below the course card
                      //   if (widget.classModel?.charge != null)
                      ValueListenableBuilder<int>(
                          valueListenable: currentPageIndex,
                          builder: (context, value, child) {
                            final classDate = eventDates
                                ?.eventDates?[currentPageIndex.value].dateId;
                            final zcoin = classDate?.charge;
                            final hkd = zcoinToHkd(zcoin);
                            return Padding(
                              padding: EdgeInsets.only(top: 8.h, left: 20.w),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      customSvgPicture(
                                        imagePath: ImagePath.zSvg,
                                        height: 18.h,
                                        width: 18.w,
                                      ),
                                      SizedBox(width: 6.w),
                                      Text(
                                        'charge: ${zcoin ?? '-'} Zcoins/ $hkd HKD',
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w600,
                                          color: AppPallete.secondaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            );
                          }),
                      SizedBox(
                        height: 10.h,
                      ),
                      SmoothPageIndicator(
                        controller: pageController,
                        count: eventDates?.eventDates?.length ?? 0,
                        effect: const WormEffect(
                          dotHeight: 8.0,
                          dotWidth: 8.0,
                          spacing: 16.0,
                          activeDotColor: AppPallete.secondaryColor,
                          dotColor: AppPallete.paleGrey,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
            Material(
              color: Colors.transparent,
              child: TabBar(tabs: [
                Tab(
                    child: customtext(
                        context: context,
                        newYear: "Schedule",
                        font: 17.sp,
                        weight: FontWeight.w700)),
                Tab(
                    child: customtext(
                        context: context,
                        newYear: "Calendar",
                        font: 17.sp,
                        weight: FontWeight.w700)),
              ]),
            ),
            Expanded(
                child: TabBarView(children: [
              Center(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 43.h,
                  ),
                  Expanded(
                    child: ValueListenableBuilder<int>(
                      valueListenable: currentPageIndex,
                      builder: (context, value, child) {
                        return eventDates?.eventDates?[currentPageIndex.value]
                                    .dates?.length ==
                                0
                            ? Center(child: Text('No class in this schedule'))
                            : ListView.separated(
                                padding: EdgeInsets.symmetric(vertical: 8.h),
                                itemCount: eventDates
                                        ?.eventDates?[currentPageIndex.value]
                                        .dates
                                        ?.length ??
                                    0,
                                itemBuilder: (context, index) {
                                  final date = eventDates
                                      ?.eventDates?[currentPageIndex.value]
                                      .dates?[index];
                                  ClassDate? _classDate = eventDates
                                      ?.eventDates?[currentPageIndex.value]
                                      .dateId;
                                  return schduleTimeSlotCenterCourse(
                                    context: context,
                                    number: index + 1,
                                    dateTime: date ??
                                        DateTime
                                            .now(), // Direct DateTime from eventDates
                                    start: _classDate?.startTime ??
                                        "", // Add if available
                                    finish: _classDate?.endTime ??
                                        "", // Add if available
                                    duration: _classDate?.durationMinutes ??
                                        "", // Add if available
                                    isJoinable:
                                        false, // You can set this using a checker
                                    onTap: () {
                                      NavigatorService.pushNamed(
                                        AppRoutes.request,
                                        arguments: {
                                          'classModel': widget.classModel,
                                          'center': widget.center,
                                          'eventDetails':
                                              [], // You can pass matchingEvents by filtering
                                        },
                                      );
                                    },
                                  );
                                },
                                separatorBuilder: (context, index) =>
                                    SizedBox(height: 18.h),
                              );
                        // // Convert Map<String, List<DateTime>> to List<MapEntry>
                        // final dateEntries = eventDates
                        //     ?.eventDates?[currentPageIndex.value].dates;
                        // if (dateEntries?.length == 0) {
                        //   return SizedBox(
                        //     child: Text('No class in this schedule'),
                        //   );
                        // }
                        // // Ensure the page index is within bounds
                        // if (currentPageIndex.value >= dateEntries!.length) {
                        //   return const Center(
                        //       child: Text('No event dates available.'));
                        // }

                        // // Get the currently selected dateId and its dates
                        // ClassDate? _classDate = eventDates
                        //     ?.eventDates?[currentPageIndex.value].dateId;

                        // return ListView.separated(
                        //   padding: EdgeInsets.symmetric(vertical: 8.h),
                        //   itemCount: dateEntries.length,
                        //   itemBuilder: (context, index) {
                        //     final date = dateEntries[index];

                        //     return
                        //      schduleTimeSlotCenterCourse(
                        //       context: context,
                        //       number: index + 1,
                        //       dateTime: date, // Direct DateTime from eventDates
                        //       start: _classDate?.startTime ??
                        //           "", // Add if available
                        //       finish:
                        //           _classDate?.endTime ?? "", // Add if available
                        //       duration: _classDate?.durationMinutes ??
                        //           "", // Add if available
                        //       isJoinable:
                        //           false, // You can set this using a checker
                        //       onTap: () {
                        //         NavigatorService.pushNamed(
                        //           AppRoutes.request,
                        //           arguments: {
                        //             'classModel': widget.classModel,
                        //             'center': widget.center,
                        //             'eventDetails':
                        //                 [], // You can pass matchingEvents by filtering
                        //           },
                        //         );
                        //       },
                        //     );
                        //   },
                        //   separatorBuilder: (context, index) =>
                        //       SizedBox(height: 18.h),
                        // );
                      },
                    ),
                  ),
                ],
              )),
              SingleChildScrollView(
                  child: ValueListenableBuilder<int>(
                      valueListenable: currentPageIndex,
                      builder: (context, value, child) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomCalendar(
                                focusedDay: _focusedDay,
                                selectedDay: _selectedDay,
                                onDaySelected: _onselectedDay,
                                eventDates: eventDates
                                            ?.eventDates?[
                                                currentPageIndex.value]
                                            .dates !=
                                        null
                                    ? eventDates!
                                        .eventDates![currentPageIndex.value]
                                        .dates!
                                        .map((date) => date.toIso8601String())
                                        .toList()
                                    : []),

                            SizedBox(height: 22.h),
                            customDivider(width: 406.w, padding: 12.w),
                            SizedBox(height: 22.h),
                            // List of events for the selected day
                            ValueListenableBuilder<int>(
                              valueListenable: currentPageIndex,
                              builder: (context, value, _) {
                                final currentEventDates = eventDates
                                    ?.eventDates?[currentPageIndex.value];
                                final List<DateTime> dateList = eventDates
                                        ?.eventDates?[currentPageIndex.value]
                                        .dates ??
                                    [];

                                final int dateIndex = dateList.indexWhere(
                                  (date) =>
                                      DateUtils.isSameDay(date, _selectedDay),
                                );

                                // Find if there is a match for _selectedDay
                                final hasEventOnSelectedDay =
                                    currentEventDates?.dates?.any(
                                          (date) => DateUtils.isSameDay(
                                              date, _selectedDay),
                                        ) ??
                                        false;

                                if (!hasEventOnSelectedDay) {
                                  return Center(
                                      child: Text(
                                    "No events on selected date.",
                                    style: TextStyle(
                                        fontSize: 16.sp, color: Colors.grey),
                                  ));
                                }

                                ClassDate? _classDate =
                                    currentEventDates?.dateId;

                                return ListView.separated(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  padding: EdgeInsets.zero,
                                  separatorBuilder: (context, index) =>
                                      SizedBox(height: 18.h),
                                  itemBuilder: (context, index) {
                                    return schduleTimeSlotCenterCourse(
                                      context: context,
                                      number: dateIndex + 1,
                                      dateTime: _selectedDay!,
                                      start: _classDate?.startTime ?? "",
                                      finish: _classDate?.endTime ?? "",
                                      duration:
                                          _classDate?.durationMinutes ?? "",
                                      isJoinable: true,
                                      onTap: () {
                                        // Navigator.push...
                                      },
                                    );
                                  },
                                  itemCount: 1,
                                );
                              },
                            ),

                            SizedBox(
                              height: 10,
                            )
                          ],
                        );
                      }))
            ]))
          ],
        ),
      ),
    ));
  }
}
