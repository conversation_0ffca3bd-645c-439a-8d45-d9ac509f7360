import 'package:class_z/core/imports.dart';

class UpcomingClass extends StatefulWidget {
  final String? centerId;
  const UpcomingClass({this.centerId, super.key});

  @override
  State<UpcomingClass> createState() => _UpcomingClassState();
}

class _UpcomingClassState extends State<UpcomingClass> {
  @override
  void initState() {
    print("UpcomingClass initState with centerId: ${widget.centerId}");
    if (widget.centerId == null || widget.centerId!.isEmpty) {
      print("WARNING: centerId is null or empty!");
    }

    // Fetch classes with the provided centerId
    context
        .read<CenterBloc>()
        .add(GetAllClassesForParentEvent(centerId: widget.centerId ?? ''));
    super.initState();
  }

  List<ClassModel>? classes;

  @override
  Widget build(BuildContext context) {
    print(
        "UpcomingClass build method called, classes: ${classes?.length ?? 'null'}");
    return Scaffold(
      body: BlocListener<CenterBloc, CenterState>(
        listener: (context, state) {
          print("CenterBloc state: $state");
          if (state is CenterLoadingState) {
            print("Loading state detected");
            loadingState(context: context);
          } else {
            hideLoadingDialog(context);
          }
          if (state is ClassListFetchSuccess) {
            print("ClassListFetchSuccess with ${state.classes.length} classes");
            setState(() {
              classes = state.classes;
            });
          }
          if (state is CenterErrorState) {
            print("Error state: ${state.message}");
            errorState(context: context, error: state.message);
          }
        },
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                  padding: EdgeInsets.only(top: 34.h),
                  child: CustomAppBar(
                    title: 'Programs',
                    subtitle: 'Explore the programs',
                    leading: customBackButton(),
                  )),
              if (classes == null)
                SizedBox(
                  height: getHeight(context: context) / 2,
                  child: Center(
                    child: customtext(
                      context: context,
                      newYear: "No upcoming Class Available",
                      font: 20.sp,
                      weight: FontWeight.w600,
                    ),
                  ),
                )
              else if (classes!.isEmpty)
                SizedBox(
                  height: getHeight(context: context) / 2,
                  child: Center(
                    child: customtext(
                      context: context,
                      newYear: "No programs available for this center",
                      font: 20.sp,
                      weight: FontWeight.w600,
                    ),
                  ),
                )
              else
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.only(top: 34.h),
                  itemBuilder: (context, index) {
                    final classModel = classes?[index];
                    return upcomingClassCard(
                      context: context,
                      imagePath: imageStringGenerator(
                          imagePath: classModel?.mainImage?.url ?? ''),
                      title: classModel?.classProviding ?? "",
                      category: classModel?.level ?? "",
                      location: _getLocationText(classModel),
                      ageGroup: _getAgeGroupText(classModel),
                      rate: _getRateText(classModel),
                      time: _getTimeText(classModel),
                      course: classModel?.course ?? false,
                      onTap: () {
                        print("Class clicked: ${classModel?.id}");
                        NavigatorService.pushNamed(
                          AppRoutes.classDetails,
                          arguments: {
                            'classModel': classModel,
                            'edit': true,
                            'currentSessionIndex': classModel?.dates != null &&
                                    classModel!.dates!.isNotEmpty
                                ? classModel.dates!.length ~/
                                    2 // For testing, use half course
                                : 0,
                          },
                        );
                      },
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 15.h,
                    );
                  },
                  itemCount: classes?.length ?? 0,
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods to handle data formatting and fallbacks
  String _getLocationText(ClassModel? classModel) {
    if (classModel?.mode == null) return "Location TBD";
    return classModel!.mode! ? "In-Center" : "Online";
  }

  String _getAgeGroupText(ClassModel? classModel) {
    final ageFrom = classModel?.ageFrom;
    final ageTo = classModel?.ageTo;

    if (ageFrom == null && ageTo == null) return "(Age TBD)";
    if (ageFrom == null) return "(Up to $ageTo)";
    if (ageTo == null) return "($ageFrom+)";
    return "($ageFrom-$ageTo)";
  }

  String _getRateText(ClassModel? classModel) {
    // First check the main class charge
    final mainCharge = classModel?.charge;
    if (mainCharge != null && mainCharge > 0) {
      return mainCharge.toString();
    }

    // If main charge is null/0, check the dates for charge information
    if (classModel?.dates?.isNotEmpty == true) {
      final dateCharge = classModel?.dates?.first.charge;
      if (dateCharge != null && dateCharge > 0) {
        return dateCharge.toString();
      }
    }

    // Fallback to "0" if no charge found
    return "0";
  }

  String _getTimeText(ClassModel? classModel) {
    // Check if dates exist and have duration
    if (classModel?.dates?.isNotEmpty == true) {
      final duration = classModel?.dates?.first.durationMinutes;
      if (duration != null && duration.toString().isNotEmpty) {
        // If duration is a number, add "min" suffix
        if (int.tryParse(duration.toString()) != null) {
          return "${duration}min";
        }
        return duration.toString();
      }
    }

    // Fallback to a reasonable default
    return "45min";
  }
}
