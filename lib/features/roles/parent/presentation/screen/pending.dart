import 'package:class_z/core/imports.dart';

class Pending extends StatelessWidget {
  const Pending({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Column(
          children: [
            SizedBox(
              height: 210.h,
            ),
            customSvgPicture(
              imagePath: ImagePath.pendingSvg,
              height: 72.h,
              width: 63.w,
              color: AppPallete.secondaryColor,
            ),
            SizedBox(
              height: 40.h,
            ),
            Center(
              child: customtext(
                context: context,
                newYear: "Pending for confirmation...",
                font: 20.sp,
                weight: FontWeight.w700,
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            Center(
                child: Text(
              "You'll receive confirmation of your request\n once the provider accepts it!",
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 15.sp, fontWeight: FontWeight.w500),
            )),
            SizedBox(
              height: 13.h,
            ),
            SizedBox(
                //height: 30.h,
                width: 288.w,
                child: TextButton(
                    onPressed: () {},
                    child: Center(
                        child: RichText(
                            textAlign: TextAlign.center,
                            text: TextSpan(children: <TextSpan>[
                              TextSpan(
                                  text: "My Orders",
                                  style: TextStyle(
                                    fontSize: 15.sp,
                                    fontWeight: FontWeight.w500,
                                    color: AppPallete.secondaryColor,
                                    decoration: TextDecoration
                                        .underline, // Underline this part
                                    decorationColor: AppPallete.secondaryColor,
                                  ))
                            ]))))),
            SizedBox(
              height: 40.h,
            ),
            Center(
              child: Button(
                height: 49.h,
                width: 289.w,
                buttonText: "Continue Shopping",
                onPressed: () {
                  NavigatorService.pushNamed(AppRoutes.homePage);
                },
                color: AppPallete.secondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
