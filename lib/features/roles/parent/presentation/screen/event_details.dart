import 'package:class_z/core/imports.dart';

class EventDetailsConTimetable extends StatefulWidget {
  final EventElement event;
  const EventDetailsConTimetable({required this.event, super.key});

  @override
  State<EventDetailsConTimetable> createState() =>
      _EventDetailsConTimetableState();
}

class _EventDetailsConTimetableState extends State<EventDetailsConTimetable> {
  QrCodeModel? code;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(title: "My Class", leading: customBackButton()),
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildProgressSteps(
                  context: context,
                  cont1: AppPallete.secondaryColor,
                  cont2: AppPallete.secondaryColor,
                  cont3: AppPallete.secondaryColor),
              SizedBox(
                height: 15.h,
              ),
              _prepare(context: context)
            ],
          ),
        ));
  }

  Widget _prepare({required BuildContext context}) {
    ParentData1? parent = locator<SharedRepository>().getParentData();
    return Padding(
      padding: EdgeInsets.only(left: 23.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          customtext(
              context: context,
              newYear: "Prepare your class code and notify the coach at start",
              font: 15.sp,
              weight: FontWeight.w600),
          SizedBox(
            height: 20.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 4.w),
            child: InkWell(
              onTap: () {
                NavigatorService.pushNamed(AppRoutes.qrcode,
                    arguments: widget.event);
              },
              child: Container(
                height: 40.h,
                width: 158.w,
                decoration: BoxDecoration(
                    color: AppPallete.red,
                    borderRadius: BorderRadius.circular(10.r)),
                child: Center(
                  child: customtext(
                      context: context,
                      newYear: "Verification code",
                      font: 15.sp,
                      color: Colors.white,
                      weight: FontWeight.w700),
                ),
              ),
            ),
          ),
          SizedBox(
            height: 20.h,
          ),
          Padding(
              padding: EdgeInsets.only(left: 2.w),
              child: timeTableCardFull(
                  context: context,
                  user: widget.event.childId?.fullname ?? '',
                  confirmed: widget.event.event?.status == 'active',
                  date: dateGenerator(
                      date: widget.event.event?.date, format: 'dd/MM/yyyy'),
                  location: widget.event.event?.classId?.center?.displayName,
                  course: widget.event.event?.classId?.classProviding ?? '',
                  time:
                      "${widget.event.event?.dateId?.startTime} - ${widget.event.event?.dateId?.endTime}",
                  classTime: widget.event.event?.dateId?.durationMinutes ?? '',
                  special: widget.event.childId?.sen == true
                      ? "Special note: SEN service"
                      : "",
                  coach:
                      "by ${widget.event.event?.classId?.coach?.displayName}",
                  participantName: widget.event.childId?.fullname ?? '',
                  contact: parent?.phone ?? '',
                  email: parent?.email ?? '',
                  coachingAddress: widget.event.event?.classId?.address ==
                          'center'
                      ? addressGenerator(
                          address: widget.event.event?.classId?.center?.address)
                      : widget.event.event?.classId?.address ?? '')),
          SizedBox(
            height: 20.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 3.5.w),
            child: customtext(
                context: context,
                newYear:
                    "Please arrive the coaching address on time to attend the lesson",
                font: 13.sp,
                weight: FontWeight.w300),
          ),
          SizedBox(
            height: 10.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 3.5.w),
            child: customtext(
                context: context,
                newYear: "No compensation time will be granted upon lateness",
                font: 13.sp,
                weight: FontWeight.w300),
          ),
          SizedBox(
            height: 10.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 3.5.w),
            child: customtext(
                context: context,
                newYear: "No-show may be subject to a service charge",
                font: 13.sp,
                weight: FontWeight.w300),
          ),
          SizedBox(
            height: 20.h,
          ),
          // Calculate real pricing data
          Builder(
            builder: (context) {
              // Get the actual charge from dateId (schedule-specific) or classId (class-level)
              int actualCharge = widget.event.event?.dateId?.charge ??
                  widget.event.event?.classId?.charge ??
                  0;
              int quantity = 1; // Single event session
              int calculatedSubtotal = actualCharge * quantity;

              // Debug logging
              print('=== EVENT DETAILS PRICING DEBUG ===');
              print('DateId charge: ${widget.event.event?.dateId?.charge}');
              print('ClassId charge: ${widget.event.event?.classId?.charge}');
              print('Actual charge: $actualCharge');
              print('Calculated subtotal: $calculatedSubtotal');
              print('=== END DEBUG ===');

              return CustomBill(
                discount:
                    0, // No discount unless there's a specific discount system
                subtotal: calculatedSubtotal,
                rows: [
                  BillRow.fromCells(cells: [
                    DataCell(customtext(
                        context: context,
                        newYear:
                            "${widget.event.event?.classId?.classProviding} (${widget.event.event?.classId?.level})",
                        font: 13.sp,
                        weight: FontWeight.w500)),
                    DataCell(Center(
                        child: customtext(
                            context: context,
                            newYear: '1', // Single event session
                            font: 13.sp,
                            weight: FontWeight.w500))),
                    DataCell(Center(
                        child: customtext(
                            context: context,
                            newYear: actualCharge > 0
                                ? actualCharge.toString()
                                : 'Free',
                            font: 13.sp,
                            weight: FontWeight.w500))),
                    DataCell(Align(
                        alignment: Alignment.centerRight,
                        child: customtext(
                            context: context,
                            newYear: calculatedSubtotal > 0
                                ? calculatedSubtotal.toString()
                                : 'Free',
                            font: 13.sp,
                            weight: FontWeight.w500))),
                  ]),
                ],
              );
            },
          ),
          // Add bottom padding to prevent cropping
          SizedBox(
            height: 40.h,
          ),
        ],
      ),
    );
  }
}
