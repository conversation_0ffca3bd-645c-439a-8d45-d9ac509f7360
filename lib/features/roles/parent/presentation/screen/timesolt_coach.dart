import 'package:class_z/core/imports.dart';


class TimeSlotCoach extends StatelessWidget {
  const TimeSlotCoach({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(
            children: [
              //_top(context),
              Sized<PERSON>ox(
                height: 24.h,
              ),
              _oneTwoThree(context),
              <PERSON><PERSON><PERSON>ox(
                height: 18.h,
              ),
              _date(context: context, date: DateTime.now()),
              <PERSON><PERSON><PERSON><PERSON>(
                height: 8.h,
              ),
              customDivider(),
              SizedBox(
                height: 16.h,
              ),
              Padding(
                padding: EdgeInsets.only(left: 8.w),
                child: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      return coachList(
                          context: context,
                          imagepath: ImagePath.violin,
                          course: "Free Course",
                          level: "Beginner",
                          name: "<PERSON> Own",
                          duration: "45 mins",
                          classTime: "7:00 AM",
                          location: "center",
                          language: "Bangla",
                          ageGroup: "3-6",
                          student: 3.toString(),
                          fee: 300.toString());
                    },
                    separatorBuilder: (context, builder) {
                      return SizedBox(
                        height: 12.h,
                      );
                    },
                    itemCount: 10),
              )
            ],
          ),
        ),
      ),
    );
  }
}

Widget _oneTwoThree(BuildContext context) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Padding(
        padding: EdgeInsets.only(left: 36.w, right: 36.w),
        child: Row(
          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(20.r),
              child: Container(
                height: 34.h,
                width: 34.w,
                color: AppPallete.secondaryColor,
                child: Center(
                  child: customtext(
                      context: context,
                      newYear: "1",
                      font: 20,
                      color: Colors.white),
                ),
              ),
            ),
            Container(
              width: 128.w,
              height: 1.h,
              color: AppPallete.dividerTime,
            ),
            ClipRRect(
              borderRadius: BorderRadius.circular(20.r),
              child: Container(
                height: 34.h,
                width: 34.w,
                color: AppPallete.dividerTime,
                child: Center(
                  child: customtext(
                    context: context,
                    newYear: "2",
                    font: 20,
                  ),
                ),
              ),
            ),
            Container(
              width: 128.w,
              height: 1.h,
              color: AppPallete.dividerTime,
            ),
            ClipRRect(
              borderRadius: BorderRadius.circular(20.r),
              child: Container(
                height: 34.h,
                width: 34.w,
                color: AppPallete.dividerTime,
                child: Center(
                  child: customtext(
                    context: context,
                    newYear: "3",
                    font: 20,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
      Padding(
        padding: EdgeInsets.only(left: 12.w),
        child: Row(
          children: [
            SizedBox(
              height: 15.h,
              width: 82.w,
              child: customtext(
                  context: context, newYear: "choose class", font: 15.sp),
            ),
            SizedBox(
              width: 80.w,
            ),
            SizedBox(
              height: 15.h,
              width: 82.w,
              child: customtext(
                  context: context, newYear: "send request", font: 15.sp),
            ),
            SizedBox(
              width: 80.w,
            ),
            SizedBox(
              height: 15.h,
              width: 79.w,
              child: customtext(
                  context: context, newYear: "confirmation", font: 15.sp),
            ),
          ],
        ),
      )
    ],
  );
}

Widget _top(BuildContext context) {
  return Row(
    children: [
      Padding(
          padding: EdgeInsets.only(top: 44.h, left: 19.w),
          child: customBackButton()),
      Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 33.h, left: 83.w),
            child: customtext(
                context: context,
                newYear: AppText.name,
                font: 20.sp,
                weight: FontWeight.w700),
          ),
          Padding(
            padding: EdgeInsets.only(left: 83.w),
            child: customtext(
                context: context,
                newYear: "Choose your Class",
                font: 20.sp,
                weight: FontWeight.w500),
          ),
        ],
      )
    ],
  );
}

Widget _date({required BuildContext context, required DateTime date}) {
  final DateTime today = DateTime.now();
  // Generate the dates for the entire week
  final List<DateTime> weekDays = List<DateTime>.generate(7, (index) {
    return date.add(Duration(days: index));
  });
  return Padding(
    padding: EdgeInsets.only(left: 12.w),
    child: Row(
      mainAxisAlignment:
          MainAxisAlignment.spaceBetween, // Ensure spacing between columns
      children: weekDays.map((day) {
        bool isToday = day.day == today.day &&
            day.month == today.month &&
            day.year == today.year;
        return Column(
          children: [
            customtext(
              context: context,
              newYear: isToday ? "Today" : "",
              font: isToday ? 17.sp : 15.sp,
              weight: isToday ? FontWeight.w700 : FontWeight.w500,
            ),
            customtext(
              context: context,
              newYear: "${day.day}/${day.month}",
              font: isToday ? 17.sp : 15.sp,
              weight: isToday ? FontWeight.w700 : FontWeight.w500,
            ),
          ],
        );
      }).toList(),
    ),
  );
}

Widget _location(BuildContext context) {
  return Padding(
    padding: EdgeInsets.only(left: 12.w),
    child: SizedBox(
      width: 418.w,
      height: 28.h,
      child: Row(
        children: [
          SizedBox(
            width: 4.w,
          ),
          SizedBox(
            width: 5.h,
          ),
          buildLocation(
              context: context, text: "all", color: AppPallete.lightGreyReal),
          SizedBox(
            width: 5.h,
          ),
          buildLocation(
              context: context,
              text: "Tsim Sha Tsui",
              color: AppPallete.scheduleColor2),
          SizedBox(
            width: 5.h,
          ),
          buildLocation(
              context: context,
              text: "Causeway Bay",
              color: AppPallete.scheduleColor3),
          SizedBox(
            width: 5.h,
          ),
          buildLocation(
              context: context,
              text: "Tseung Kwan O",
              color: AppPallete.scheduleColor4),
          SizedBox(
            width: 5.h,
          ),
          buildLocation(
              context: context,
              text: "Tai Koo",
              color: AppPallete.scheduleColor5),
        ],
      ),
    ),
  );
}
