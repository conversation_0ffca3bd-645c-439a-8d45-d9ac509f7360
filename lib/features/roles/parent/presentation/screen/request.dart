import 'package:class_z/core/imports.dart';
// Explicit import for BillRow
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;

class RequestSTL extends StatefulWidget {
  final ClassModel classModel;
  final EventDate? eventDetails; // List of events (optional)

  RequestSTL({Key? key, required this.classModel, this.eventDetails})
      : super(key: key);

  @override
  State<RequestSTL> createState() => _RequestSTLState();
}

class _RequestSTLState extends State<RequestSTL> {
  String _selectedOption = 'Credit Card';
  final remarksController = TextEditingController();
  final discountController = TextEditingController();

  // Real-time balance tracking
  int _realBalance = 0;
  bool _isLoadingBalance = true;

  void _handleRadioValueChanged(String value) {
    setState(() {
      _selectedOption = value;
    });
  }

  // Variables for coupon handling
  String? selectedCouponCode;
  String? discountType;
  int? discountAmount;
  String? discountDisplayValue;

  // Track if SEN is selected
  bool sen = false;

  final senController = TextEditingController();
  final childController = TextEditingController();

  // Fetch real balance from API
  Future<void> _fetchRealBalance() async {
    if (!mounted) return;

    try {
      final userData = locator<SharedRepository>().getUserData();
      final userId = userData?.data?.parent?.id;
      final token = locator<SharedRepository>().getToken();

      if (userId == null || token == null) {
        print("❌ Cannot fetch balance: User ID or token not available");
        setState(() {
          _realBalance = 0;
          _isLoadingBalance = false;
        });
        return;
      }

      print("🔄 Fetching real balance from API for user: $userId");

      final response = await http.get(
        Uri.parse("${AppText.device}/api/balance/$userId"),
        headers: {
          'Content-Type': 'application/json',
          'auth-token': token,
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data != null && data['balance'] != null) {
          final balance = data['balance'] as int;
          print("💰 Real balance fetched from API: $balance");

          if (mounted) {
            setState(() {
              _realBalance = balance;
              _isLoadingBalance = false;
            });
          }
        } else {
          print("❌ Invalid balance data received");
          setState(() {
            _realBalance = 0;
            _isLoadingBalance = false;
          });
        }
      } else {
        print("❌ Failed to fetch balance: ${response.statusCode}");
        setState(() {
          _realBalance = 0;
          _isLoadingBalance = false;
        });
      }
    } catch (e) {
      print("❌ Error fetching real balance: $e");
      if (mounted) {
        setState(() {
          _realBalance = 0;
          _isLoadingBalance = false;
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    senController.addListener(() {
      setState(() {
        if (senController.text == "SEN")
          sen = true;
        else
          sen = false;
      }); // Triggers rebuild on text change
    });

    // Fetch real balance on initialization
    _fetchRealBalance();
  }

  @override
  Widget build(BuildContext context) {
    final sharedRepository = Provider.of<SharedRepository>(context);
    UserModel? userData = sharedRepository.getUserData();
    return Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: CustomAppBar(
          title: "Send Request",
          leading: customBackButton(),
        ),
        bottomNavigationBar: SizedBox(
            height: 76.h,
            width: 430.w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                customDivider(width: 430.w, padding: 0),
                SizedBox(
                  height: 13.h,
                ),
                Button(
                    height: 49.h,
                    width: 289.w,
                    buttonText: "Pay",
                    onPressed: () async {
                      // Validate child selection first
                      if (childController.text.isEmpty) {
                        errorState(
                            context: context,
                            error: "Please select a child before proceeding");
                        return;
                      }

                      // NEW: Check for duplicate booking before proceeding
                      try {
                        final userData =
                            locator<SharedRepository>().getUserData();
                        final token = locator<SharedRepository>().getToken();

                        print(
                            '🔍 Checking if child has already booked this class...');
                        print('Child ID: ${childController.text}');
                        print('Class ID: ${widget.classModel.id}');

                        final response = await http.get(
                          Uri.parse(
                              "${AppText.device}/api/orders/can-book/${childController.text}/${widget.classModel.id}"),
                          headers: {
                            'Content-Type': 'application/json',
                            'auth-token': token ?? '',
                          },
                        );

                        if (response.statusCode == 200) {
                          final data = jsonDecode(response.body);
                          if (data['alreadyBooked'] == true) {
                            print('❌ Child has already booked this class');
                            errorState(
                                context: context,
                                error:
                                    "This child has already booked this program. Each child can only book a program once.");
                            return;
                          }
                          print('✅ Child is eligible to book this class');
                        } else {
                          print(
                              '⚠️ Could not verify booking eligibility, proceeding with caution');
                        }
                      } catch (e) {
                        print('⚠️ Error checking booking eligibility: $e');
                        // Continue with booking process but log the error
                      }

                      // Calculate payment details
                      final subtotal = calculateSubtotal();
                      final discountAmount = calculateDiscountAmount(subtotal);
                      final finalAmount = subtotal - discountAmount;

                      // PAYMENT FLOW: Use REAL balance from API instead of cached data
                      final hasSufficientZcoin = _realBalance >= finalAmount;

                      print('=== PAYMENT VALIDATION ===');
                      print('Real Balance from API: $_realBalance');
                      print('Final Amount: $finalAmount');
                      print('Has Sufficient Zcoin: $hasSufficientZcoin');
                      print('Selected Payment Option: $_selectedOption');
                      print('=== END VALIDATION ===');

                      // Determine payment path based on selection or availability
                      if (_selectedOption == 'Zcoin' && hasSufficientZcoin) {
                        // SUFFICIENT ZCOIN & ZCOIN SELECTED: Proceed with direct Zcoin payment
                        List<String?> dates = widget.eventDetails?.dates
                                ?.map((date) => date.toIso8601String())
                                .toList() ??
                            [];

                        var payload = {
                          "classId": widget.classModel.id,
                          "userId": userData?.data?.parent?.id,
                          "dateId": widget.eventDetails?.dateId?.id ?? '',
                          "childId": [childController.text],
                          "sen": sen,
                          "date": dates,
                          "paid": true,
                          "course": widget.eventDetails!.dates!.length > 1
                              ? true
                              : false,
                          "Discount": discountAmount,
                          "amount": finalAmount,
                          "quantity": widget.eventDetails?.dates?.length,
                          "startTime": widget.eventDetails?.dateId?.startTime,
                          "endTime": widget.eventDetails?.dateId?.endTime,
                          "durationMinutes":
                              widget.eventDetails?.dateId?.durationMinutes,
                        };

                        print('=== ZCOIN PAYMENT PAYLOAD ===');
                        print('Paying with Zcoin balance: $payload');
                        print('=== END ZCOIN PAYLOAD ===');

                        context
                            .read<UserBloc>()
                            .add(CreateOrderEvent(orderData: payload));
                      } else {
                        // CREDIT CARD SELECTED OR INSUFFICIENT ZCOIN: Redirect to credit card payment
                        print(
                            'Proceeding with credit card payment (selected or insufficient Zcoin)');

                        // Convert Zcoin amount to HKD for credit card payment
                        final hkdAmount = finalAmount * 25; // 1 Zcoin = 25 HKD

                        // Store the order details for after payment
                        final orderDetails = {
                          "classId": widget.classModel.id,
                          "userId": userData?.data?.parent?.id,
                          "childId": [childController.text],
                          "sen": sen,
                          "date": widget.eventDetails?.dates
                                  ?.map((date) => date.toIso8601String())
                                  .toList() ??
                              [],
                          "paid": true,
                          "course": widget.eventDetails!.dates!.length > 1
                              ? true
                              : false,
                          "Discount": discountAmount,
                          "amount": finalAmount,
                          "quantity": widget.eventDetails?.dates?.length,
                          "startTime": widget.eventDetails?.dateId?.startTime,
                          "endTime": widget.eventDetails?.dateId?.endTime,
                          "durationMinutes":
                              widget.eventDetails?.dateId?.durationMinutes,
                        };

                        // Navigate to credit card screen with payment details
                        final paymentDetails = {
                          'amount': hkdAmount.toInt(),
                          'zcoins': finalAmount,
                          'isClassBooking':
                              true, // Flag to indicate this is for class booking
                          'orderDetails':
                              orderDetails, // Store order details for after payment
                        };

                        print('=== CREDIT CARD PAYMENT ===');
                        print(
                            'Redirecting to credit card with HKD amount: ${hkdAmount.toInt()}');
                        print('Equivalent Zcoin: $finalAmount');
                        print('=== END CREDIT CARD ===');

                        NavigatorService.pushNamed(AppRoutes.creditCard,
                            arguments: paymentDetails);
                      }
                    },
                    color: AppPallete.secondaryColor),
              ],
            )),
        body: BlocListener<UserBloc, UserState>(
          listener: (context, state) {
            if (state is UserLoadingState)
              loadingState(context: context);
            else
              hideLoadingDialog(context);
            if (state is UserErrorState) {
              errorState(context: context, error: state.message);
            }
            if (state is createOrderSuccessState) {
              if (state.success == true)
                NavigatorService.popAndPushNamed(AppRoutes.reserved);
              else
                errorState(context: context, error: "Something went wrong");
            }
          },
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 24.h),
                buildProgressSteps(
                    context: context,
                    cont1: AppPallete.secondaryColor,
                    cont2: AppPallete.secondaryColor,
                    cont3: AppPallete.dividerTime),
                SizedBox(height: 32.h),
                _rest(context: context, userdata: userData)
              ],
            ),
          ),
        ));
  }

  Widget _rest({required BuildContext context, UserModel? userdata}) {
    return Padding(
      padding: EdgeInsets.only(left: 22.5.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          customtext(
              context: context,
              newYear: "Address",
              font: 20.sp,
              weight: FontWeight.w600),
          SizedBox(
            height: 4.h,
          ),
          customtext(
              context: context,
              newYear: '${widget.eventDetails?.dateId?.address}',
              //"Program is non-refundable after confirmation",
              font: 13.sp,
              weight: FontWeight.w500),
          SizedBox(
            height: 17.h,
          ),
          widget.eventDetails?.dateId?.address == 'center'
              ? _address(
                  centerName: widget.classModel.center?.displayName ?? "",
                  address: addressGenerator(
                      address: widget.classModel.center?.address),
                  city: addressGenerator(
                      address: widget.classModel.center?.address,
                      condition: 'city'),
                )
              : _offSite(),
          SizedBox(
            height: 50.h,
          ),
          customtext(
              context: context,
              newYear: "Course Details",
              font: 20.sp,
              weight: FontWeight.w600),
          customtext(
              context: context,
              newYear: "Details are not amendable after confirming",
              font: 13.sp,
              weight: FontWeight.w500),
          SizedBox(
            height: 17.h,
          ),
          RequestInfoCard(
            date: widget.eventDetails?.dates?[0] ?? DateTime.now(),
            startTime: widget.eventDetails?.dateId?.startTime ?? "",
            endTime: widget.eventDetails?.dateId?.endTime ?? "",
            center: widget.classModel.mode == true ? "center" : "Remote Only",
            age: "Age ${widget.classModel.ageFrom}-${widget.classModel.ageTo}",
            classTime: widget.eventDetails?.dateId?.durationMinutes ?? "",
            cost: widget.eventDetails?.dateId?.charge?.toString() ?? '',
            name: widget.classModel.coach?.displayName ?? "",
            student: widget.classModel.numberOfStudent.toString(),
            eventDates: widget.eventDetails?.dates,
            controller: senController,
            childController: childController,
            classId: widget.classModel.id,
          ),
          SizedBox(
            height: 50.h,
          ),
          Row(
            children: [
              customtext(
                  context: context,
                  newYear: "Remarks",
                  font: 20.sp,
                  weight: FontWeight.w600),
              SizedBox(
                width: 7.w,
              ),
              customtext(
                  context: context,
                  newYear: "(Optional)",
                  font: 15.sp,
                  weight: FontWeight.w600,
                  color: AppPallete.greyWord)
            ],
          ),
          SizedBox(
            height: 4.h,
          ),
          customtext(
              context: context,
              newYear: "Your note will be taken into account by the centre",
              font: 13.sp,
              weight: FontWeight.w500),
          SizedBox(
            height: 13.h,
          ),
          AuthField(
            controller: remarksController,
            width: 380.w,
            height: 124.h,
            hintText: "(Please remark)",
          ),
          SizedBox(
            height: 50.h,
          ),
          customtext(
              context: context,
              newYear: "Discount",
              font: 20.sp,
              weight: FontWeight.w600),
          SizedBox(
            height: 22.h,
          ),
          Row(
            children: [
              customtext(
                  context: context,
                  newYear: "Discount code:",
                  font: 15.sp,
                  weight: FontWeight.w500),
              SizedBox(
                width: 12.5.w,
              ),
              AuthField(
                controller: discountController,
                width: 258.w,
                height: 30.h,
                hintText: "If Applicable",
              )
            ],
          ),
          SizedBox(
            height: 16.h,
          ),
          CouponContainer(onCouponSelected: (result) {
            // Update the state with the coupon data
            setState(() {
              selectedCouponCode = result['codeName'];
              discountType = result['discountType'];
              final rawValue = result['discountValue'];
              if (rawValue is num) {
                discountAmount = rawValue.toInt();
              } else if (rawValue is String) {
                discountAmount = int.tryParse(rawValue);
              } else {
                discountAmount = null;
              }
              discountDisplayValue = result['discount'];
            });
          }),
          SizedBox(
            height: 50.h,
          ),
          () {
            // Show loading state while fetching balance
            if (_isLoadingBalance) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  customtext(
                    context: context,
                    newYear: "Payment Method",
                    font: 20.sp,
                    weight: FontWeight.w600,
                  ),
                  SizedBox(height: 16.h),
                  CircularProgressIndicator(),
                  SizedBox(height: 30.h),
                ],
              );
            }

            final subtotal = calculateSubtotal();
            final discount = calculateDiscountAmount(subtotal);
            final finalTotal = subtotal - discount;

            // Use REAL balance from API instead of cached user data
            final hasSufficientZcoin = _realBalance >= finalTotal;

            print('=== PAYMENT OPTIONS DEBUG ===');
            print('Real API Balance: $_realBalance');
            print('Final Total: $finalTotal');
            print('Has Sufficient Zcoin: $hasSufficientZcoin');
            print('=== END DEBUG ===');

            if (hasSufficientZcoin) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  customtext(
                    context: context,
                    newYear: "Payment Method",
                    font: 20.sp,
                    weight: FontWeight.w600,
                  ),
                  SizedBox(height: 16.h),
                  _buildCustomRadioButton(
                    'Zcoin',
                    'Pay with Zcoin Balance (${_realBalance.toStringAsFixed(0)} Zcoin)',
                  ),
                  SizedBox(height: 12.h),
                  _buildCustomRadioButton(
                    'Credit Card',
                    'Pay with Credit Card',
                  ),
                  SizedBox(height: 30.h),
                ],
              );
            }
            return SizedBox.shrink();
          }(),
          customtext(
              context: context,
              newYear: "Bill Breakdown",
              font: 20.sp,
              weight: FontWeight.w600),
          SizedBox(
            height: 18.h,
          ),
          () {
            final subtotal = calculateSubtotal();
            final discount = calculateDiscountAmount(subtotal);
            final discountLabel =
                (selectedCouponCode != null && discountDisplayValue != null)
                    ? '$selectedCouponCode ($discountDisplayValue off)'
                    : null;

            // CURRENCY CONSISTENCY: Use REAL balance from API instead of cached data
            final finalTotal = subtotal - discount;
            final hasSufficientZcoin = _realBalance >= finalTotal;

            return CustomBill(
              discount: discount,
              subtotal: subtotal,
              discountLabel: discountLabel,
              zcoin:
                  hasSufficientZcoin, // This can now be used for conditional display if needed
              showBothCurrencies: true, // Force display of both HKD and Zcoin
              rows: [
                BillRow.fromCells(cells: [
                  DataCell(customtext(
                      context: context,
                      newYear:
                          "${widget.classModel.classProviding} (${widget.classModel.level})",
                      font: 13.sp,
                      weight: FontWeight.w500)),
                  DataCell(Center(
                      child: customtext(
                          context: context,
                          newYear:
                              widget.eventDetails?.dates?.length.toString() ??
                                  "",
                          font: 13.sp,
                          weight: FontWeight.w500))),
                  DataCell(rateWithIconRate(
                    context: context,
                    rate: widget.eventDetails?.dateId?.charge?.toString() ?? '',
                    zcoin: hasSufficientZcoin,
                    showBoth: true,
                  )),
                  DataCell(Align(
                      alignment: Alignment.centerRight,
                      child: rateWithIconTotal(
                        context: context,
                        rate: ((num.tryParse(widget.eventDetails?.dateId?.charge
                                            ?.toString() ??
                                        '0') ??
                                    0) *
                                (widget.eventDetails?.dates?.length ?? 0))
                            .toString(),
                        zcoin: hasSufficientZcoin,
                        showBoth: true,
                      ))),
                ]),
                if (sen)
                  BillRow.fromCells(cells: [
                    DataCell(customtext(
                        context: context,
                        newYear: "Special Request (SEN)",
                        font: 13.sp,
                        weight: FontWeight.w500)),
                    DataCell(Center(
                        child: customtext(
                            context: context,
                            newYear:
                                widget.eventDetails?.dates?.length.toString() ??
                                    "0",
                            font: 13.sp,
                            weight: FontWeight.w500))),
                    DataCell(rateWithIconRate(
                        context: context,
                        rate: "0", // LEGAL COMPLIANCE: SEN services are FREE
                        zcoin: hasSufficientZcoin)),
                    DataCell(Align(
                        alignment: Alignment.centerRight,
                        child: rateWithIconTotal(
                          context: context,
                          rate: "0", // LEGAL COMPLIANCE: SEN services cost 0
                          zcoin: hasSufficientZcoin,
                        ))),
                  ]),
              ],
            );
          }(),
          // Warning if currentStudent < minimumStudent
          if ((widget.eventDetails?.dateId?.students?.length ?? 0) <
              (widget.eventDetails?.dateId?.minimumStudent ?? 0))
            Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.orange, size: 22),
                  SizedBox(width: 8),
                  Expanded(
                    child: customtext(
                      context: context,
                      newYear:
                          'Warning: This class can be cancelled if minimum student requirement is not met. If cancelled, refund will be sent to your account.',
                      font: 13.sp,
                      weight: FontWeight.w600,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _offSite() {
    String address = widget.eventDetails?.dateId?.address ?? '';
    String? url;

    // Sanitize and parse address
    if (address.contains('http')) {
      final urlMatch = RegExp(r'https?://[^\s]+').firstMatch(address);
      if (urlMatch != null) {
        url = urlMatch.group(0);
        address = address.replaceAll(url!, '').replaceAll(',', '').trim();
      }
    }

    // If address is a plain string (no http), show it directly
    if (!address.contains('http') && (url == null || url.isEmpty)) {
      return Padding(
        padding: const EdgeInsets.only(right: 24.5),
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.r),
              color: AppPallete.color255),
          child: Padding(
            padding: EdgeInsets.only(
                left: 12.w, right: 13.5.h, top: 14.h, bottom: 19.h),
            child: customtext(
                context: context,
                newYear: address,
                font: 20.sp,
                weight: FontWeight.w500),
          ),
        ),
      );
    }

    if (address.isEmpty && url != null) {
      address = "View location on map";
    }

    Future<void> _launchUrl(String? urlString) async {
      if (urlString != null) {
        final uri = Uri.parse(urlString);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          errorState(context: context, error: 'Could not launch map');
        }
      }
    }

    return Padding(
      padding: const EdgeInsets.only(right: 24.5),
      child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.r),
              color: AppPallete.color255),
          child: Padding(
            padding: EdgeInsets.only(
                left: 12.w, right: 13.5.h, top: 14.h, bottom: 19.h),
            child: Row(
              children: [
                Expanded(
                  child: customtext(
                      context: context,
                      newYear: address,
                      font: 20.sp,
                      weight: FontWeight.w500),
                ),
                if (url != null) ...[
                  SizedBox(width: 8.w),
                  GestureDetector(
                    onTap: () => _launchUrl(url),
                    child: Icon(Icons.open_in_new,
                        size: 20.w, color: AppPallete.secondaryColor),
                  ),
                ]
              ],
            ),
          )),
    );
  }

  Widget _address(
      {required String centerName,
      required String address,
      required String city}) {
    String displayAddress = address;
    String? url;

    if (address.contains('http')) {
      final urlMatch = RegExp(r'https?://[^\s]+').firstMatch(address);
      if (urlMatch != null) {
        url = urlMatch.group(0);
        displayAddress =
            address.replaceAll(url!, '').replaceAll(',', '').trim();
      }
    }

    if (displayAddress.isEmpty && url != null) {
      displayAddress = "View location on map";
    }

    Future<void> _launchUrl(String? urlString) async {
      if (urlString != null) {
        final uri = Uri.parse(urlString);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          errorState(context: context, error: 'Could not launch map');
        }
      }
    }

    return Padding(
      padding: const EdgeInsets.only(right: 24.5),
      child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.r),
              color: AppPallete.color255),
          child: Padding(
            padding: EdgeInsets.only(
                left: 12.w, right: 13.5.h, top: 14.h, bottom: 19.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      customtext(
                          context: context,
                          newYear: centerName.isNotEmpty
                              ? centerName
                              : "Center name not available",
                          font: 20.sp,
                          weight: FontWeight.w500),
                      SizedBox(
                        height: 7.h,
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: customtext(
                                context: context,
                                newYear: displayAddress.isNotEmpty
                                    ? displayAddress
                                    : "Address not available",
                                font: 13.sp,
                                weight: FontWeight.w500),
                          ),
                          if (url != null) ...[
                            SizedBox(width: 8.w),
                            GestureDetector(
                              onTap: () => _launchUrl(url),
                              child: Icon(Icons.open_in_new,
                                  size: 20.w, color: AppPallete.secondaryColor),
                            ),
                          ]
                        ],
                      ),
                    ],
                  ),
                ),
                if (city.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: Container(
                      padding: EdgeInsets.all(7),
                      decoration: BoxDecoration(
                          color: AppPallete.scheduleColor2,
                          borderRadius: BorderRadius.circular(20.r)),
                      child: customtext(
                          context: context,
                          newYear: city,
                          font: 13.sp,
                          weight: FontWeight.w400),
                    ),
                  ),
              ],
            ),
          )),
    );
  }

  Widget _buildCustomRadioButton(String value, String text) {
    bool isSelected = _selectedOption == value;

    return GestureDetector(
      onTap: () => _handleRadioValueChanged(value),
      child: Row(
        children: [
          Container(
            width: 20.w,
            height: 20.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.black),
            ),
            child: isSelected
                ? Center(
                    child: Container(
                      width: 10.w,
                      height: 10.h,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.black,
                      ),
                    ),
                  )
                : null,
          ),
          SizedBox(
              width: 8.5.w), // Adjust the gap between the radio button and text
          customtext(
              context: context,
              newYear: text,
              font: 15.sp,
              weight: FontWeight.w500)
        ],
      ),
    );
  }

  int calculateSubtotal() {
    int subtotal = 0;

    // Regular class charge calculation
    int regularCharge = ((num.tryParse(
                    widget.eventDetails?.dateId?.charge?.toString() ?? '0') ??
                0) *
            (widget.eventDetails?.dates?.length ?? 0))
        .toInt();
    subtotal += regularCharge;

    // LEGAL COMPLIANCE: SEN services are FREE - no additional charges
    // SEN support is provided at no extra cost to comply with accessibility laws
    int specialCharge = 0; // Always 0 for legal compliance

    // Debug logging
    print('=== SUBTOTAL CALCULATION DEBUG ===');
    print('Class Charge: ${widget.eventDetails?.dateId?.charge}');
    print('Event Count: ${widget.eventDetails?.dates?.length}');
    print('SEN Selected: $sen');
    print('Regular Charge: $regularCharge');
    print('SEN Charge: $specialCharge (FREE - Legal Compliance)');
    print('Total Subtotal: $subtotal');
    print('=== END SUBTOTAL DEBUG ===');

    return subtotal;
  }

  // Calculate the discount amount based on selected coupon
  int calculateDiscountAmount(int subtotal) {
    if (discountAmount == null || discountType == null) {
      return 0;
    }

    int calculatedDiscount;
    if (discountType == 'fixed') {
      // Fixed amount discount (e.g., 50 HKD off)
      calculatedDiscount = discountAmount!;
    } else {
      // Percentage discount (e.g., 20% off)
      calculatedDiscount = (subtotal * discountAmount! / 100).round();
    }

    // Debug logging
    print('=== DISCOUNT CALCULATION DEBUG ===');
    print('Subtotal: $subtotal');
    print('Discount Type: $discountType');
    print('Discount Amount: $discountAmount');
    print('Calculated Discount: $calculatedDiscount');
    print('=== END DEBUG ===');

    return calculatedDiscount;
  }

  // Calculate the final total after discount
  int calculateTotal() {
    int subtotal = calculateSubtotal();
    int discount = calculateDiscountAmount(subtotal);
    return subtotal - discount;
  }
}

class CouponContainer extends StatefulWidget {
  // Callback to notify parent when a coupon is selected
  final Function(Map<String, dynamic>)? onCouponSelected;

  const CouponContainer({this.onCouponSelected, Key? key}) : super(key: key);

  @override
  _CouponContainerState createState() => _CouponContainerState();
}

class _CouponContainerState extends State<CouponContainer> {
  String? selectedCouponCode;
  String? discountValue;
  String? discountType;
  int? discountAmount;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50.h,
      width: 379.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
        color: Colors.white,
        boxShadow: [shadow(blurRadius: 4, opacity: 0.25)],
      ),
      child: Padding(
        padding: EdgeInsets.only(left: 11.w, right: 19.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(
                  Icons.local_offer,
                  size: 20.w,
                ),
                SizedBox(
                  width: 9.w,
                ),
                customtext(
                  context: context,
                  newYear: "Apply Coupon",
                  font: 15.sp,
                  weight: FontWeight.w500,
                  color: AppPallete.green,
                ),
              ],
            ),
            GestureDetector(
              onTap: () async {
                // Navigate to the MyCoupn page and wait for the selected coupon details
                final result =
                    await NavigatorService.pushNamed(AppRoutes.myCoupn)
                        as Map<String, dynamic>?;

                // Update state with the returned coupon code and discount
                if (result != null && result.isNotEmpty) {
                  setState(() {
                    selectedCouponCode = result['codeName']; // Coupon code
                    discountValue =
                        result['discount']; // Discount display value
                    discountType =
                        result['discountType']; // Type (fixed or percentage)
                    discountAmount =
                        result['discountValue']; // Actual numeric value

                    // Debug log
                    print('Selected coupon: $selectedCouponCode');
                    print('Discount value: $discountValue');
                    print('Discount type: $discountType');
                    print('Discount amount: $discountAmount');
                  });

                  // Notify parent if callback exists
                  if (widget.onCouponSelected != null) {
                    widget.onCouponSelected!(result);
                  }
                }
              },
              child: customtext(
                context: context,
                newYear: selectedCouponCode != null
                    ? '$selectedCouponCode (${discountValue ?? ''} off)'
                    : "Select",
                font: 15.sp,
                weight: FontWeight.w500,
                color: AppPallete.green,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
