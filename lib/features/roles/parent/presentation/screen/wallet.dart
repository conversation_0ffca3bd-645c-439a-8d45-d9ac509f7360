import 'package:class_z/core/imports.dart';
// Add this import for Timer and StreamSubscription
// Import GetIt
// Import AuthBloc
import 'package:http/http.dart' as http;

// Add a helper function to get the balance directly from the API
Future<int> fetchBalanceFromApi(String userId, String? token) async {
  try {
    if (token == null) {
      print("Cannot fetch balance: token is null");
      return 0;
    }

    final response = await http.get(
      Uri.parse("${AppText.device}/api/balance/$userId"),
      headers: {
        'Content-Type': 'application/json',
        'auth-token': token,
      },
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data != null && data['balance'] != null) {
        final balance = data['balance'];
        print("💰 Balance fetched from API: $balance");
        return balance;
      }
    } else {
      print("Failed to fetch balance: ${response.statusCode}");
    }
  } catch (e) {
    print("Error fetching balance directly: $e");
  }
  return 0;
}

class MyWallet extends StatefulWidget {
  final int zCoin;
  const MyWallet({required this.zCoin, super.key});

  @override
  State<MyWallet> createState() => _MyWalletState();
}

class _MyWalletState extends State<MyWallet> with WidgetsBindingObserver {
  int _activeIndex = 2;
  bool _isLoading = false;
  List<PurchasedHistoryModel> _purchasedHistory = [];
  String? _errorMessage;
  late int currentBalance;
  bool hasActiveSubscription = false; // Add subscription status tracking

  // Badge counts for notifications and messages
  int _notificationCount = 0;
  int _messageCount = 0;
  StreamSubscription<int>? _notificationCountSubscription;
  StreamSubscription<int>? _messageCountSubscription;

  // Simplified loading state management - no timers or multiple flags
  bool _isLoadingDialogVisible = false;

  late StreamSubscription<AuthState> _authSubscription;

  void _onTabIndexChanged(int index) {
    setState(() {
      _activeIndex = index;
    });
    handleTabIndexChanged(index);
  }

  // Custom back button handler to ensure we can go back
  void _handleBackPress() {
    print("Manual back button pressed in wallet screen");

    // Make sure loading dialog is closed before navigation
    if (_isLoadingDialogVisible) {
      hideLoadingDialog(context);
      _isLoadingDialogVisible = false;
    }

    // Check if this screen was pushed as part of a navigation stack
    final canPop = Navigator.of(context).canPop();
    print("Can pop from wallet screen: $canPop");

    if (canPop) {
      // If we can pop normally, do so
      Navigator.of(context).pop();
    } else {
      // If we can't pop (this might be the root), navigate to the main screen
      try {
        // Navigate to the home screen which contains the navigation bar
        NavigatorService.pushNamedAndRemoveUntil(AppRoutes.homePage);
      } catch (e) {
        print("Error navigating from wallet screen: $e");
        // Fallback to userMain as a secondary option
        NavigatorService.pushNamedAndRemoveUntil(AppRoutes.userMain);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    currentBalance = widget.zCoin;
    _initializeBadgeCounts();
    _fetchLatestBalance();
    _fetchPurchaseHistory();
    _fetchBalanceDirectly();

    // Register as an observer to detect when the screen comes into focus
    WidgetsBinding.instance.addObserver(this);

    // Listen to auth state changes to refresh data when user data changes
    final authBloc = BlocProvider.of<AuthBloc>(context, listen: false);
    _authSubscription = authBloc.stream.listen((state) {
      if (state is UserLoaded) {
        print("Auth state changed: UserLoaded");
        _fetchBalanceDirectly();
      }
    });

    // Get subscription information
    context.read<SubscriptionBloc>().add(GetSubscriptionPlanEvent());
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _notificationCountSubscription?.cancel();
    _messageCountSubscription?.cancel();
    _authSubscription.cancel();
    if (_isLoadingDialogVisible) {
      hideLoadingDialog(context);
    }
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // App came to foreground, refresh data
      _fetchBalanceDirectly();
    }
  }

  // Add a method to directly fetch balance from the API
  Future<void> _fetchBalanceDirectly() async {
    if (!mounted) return;

    try {
      final userData = locator<SharedRepository>().getUserData();
      final userId = userData?.data?.parent?.id;
      final token = locator<SharedRepository>().getToken();

      if (userId == null || token == null) {
        print("Cannot fetch balance: User ID or token not available");
        return;
      }

      print("🔄 Explicitly fetching latest balance data");

      // Use the helper function to get the balance
      final newBalance = await fetchBalanceFromApi(userId, token);

      if (mounted && newBalance > 0) {
        setState(() {
          currentBalance = newBalance;
          print("💰 Balance updated from API: $currentBalance");
        });
      }
    } catch (e) {
      print("Error fetching balance directly: $e");
    }
  }

  // Add a method to explicitly fetch the latest balance
  void _fetchLatestBalance() {
    if (mounted) {
      print("🔄 Explicitly fetching latest balance data");
      // Force a direct fetch of user data
      context.read<AuthBloc>().add(GetUserEvent());
    }
  }

  void _initializeBadgeCounts() {
    try {
      final userId = locator<SharedRepository>().getUserId();

      // Ensure BadgeCountService is registered
      if (!GetIt.instance.isRegistered<BadgeCountService>()) {
        print('BadgeCountService not found, registering it now');
        GetIt.instance.registerLazySingleton<BadgeCountService>(
            () => BadgeCountService());
      }

      final badgeService = locator<BadgeCountService>();

      // Initialize the badge service
      badgeService.initialize(userId);

      // Listen for notification count updates
      _notificationCountSubscription =
          badgeService.notificationCountStream.listen((count) {
        if (mounted) {
          setState(() => _notificationCount = count);
        }
      });

      // Listen for message count updates
      _messageCountSubscription =
          badgeService.messageCountStream.listen((count) {
        if (mounted) {
          setState(() => _messageCount = count);
        }
      });

      // Set initial counts
      setState(() {
        _notificationCount = badgeService.notificationCount;
        _messageCount = badgeService.messageCount;
      });
    } catch (e) {
      print('Error initializing badge counts: $e');
    }
  }

  void _fetchPurchaseHistory() {
    if (mounted) {
      // Set loading state but don't show dialog
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Fetch data
      context.read<UserBloc>().add(GetPurchasedHistoryEvent());
    }
  }

  void _refreshData() {
    // Show loading indicator
    setState(() {
      _isLoading = true;
    });

    print("♻️ Refreshing wallet data...");

    // Fetch the latest balance first
    _fetchBalanceDirectly().then((_) {
      // Then fetch user data and purchase history
      _fetchLatestBalance();
      _fetchPurchaseHistory();

      // Show a success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text("Balance updated successfully"),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ));
      }
    }).catchError((error) {
      print("Error refreshing data: $error");

      // Show an error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text("Failed to update balance"),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ));
      }
    }).whenComplete(() {
      // Hide loading indicator when done
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Get the latest user data directly from SharedRepository
    final sharedRepo = Provider.of<SharedRepository>(context);
    final userData = sharedRepo.getUserData();

    // Add explicit debug logging for balance
    print(
        "💰 WALLET DEBUG - Raw user data: ${userData?.data?.parent?.toString()}");
    print(
        "💰 WALLET DEBUG - Balance from userData: ${userData?.data?.parent?.balance}");
    print("💰 WALLET DEBUG - Current balance variable: $currentBalance");

    // Print all fields in the parent object to identify any issues
    final parent = userData?.data?.parent;
    if (parent != null) {
      print("💰 WALLET DEBUG - ALL PARENT FIELDS:");
      print("  - ID: ${parent.id}");
      print("  - Fullname: ${parent.fullname}");
      print("  - Nickname: ${parent.nickname}");
      print("  - Email: ${parent.email}");
      print("  - Phone: ${parent.phone}");
      print("  - Referal: ${parent.referal}");
      print("  - Exp: ${parent.exp}");
      print("  - Balance: ${parent.balance}");
      print("  - Base User: ${parent.baseUser}");
    }

    return Scaffold(
      body: MultiBlocListener(
        listeners: [
          BlocListener<UserBloc, UserState>(
            listener: (context, state) {
              if (state is UserLoadingState) {
                setState(() => _isLoading = true);
              } else if (state is UserErrorState) {
                setState(() {
                  _isLoading = false;
                  _errorMessage = state.message;
                });
                ScaffoldMessenger.of(context)
                    .showSnackBar(SnackBar(content: Text(state.message)));
              } else if (state is GetPurchasedHistorySuccessState) {
                setState(() {
                  _isLoading = false;
                  _purchasedHistory =
                      state.purchased.where((p) => p.order != null).toList();
                  _errorMessage = null;
                });
              } else if (state is GetBalanceSuccessState) {
                setState(() {
                  currentBalance = state.balance.balance ?? currentBalance;
                  print(
                      "💰 Balance updated from state: ${state.balance.balance}");
                });
              }
            },
          ),
          BlocListener<SubscriptionBloc, SubscriptionState>(
            listener: (context, state) {
              if (state is GetSubscribeSuccessState) {
                // Check if there's actually an active subscription
                final hasActiveSub = state.subscriptionModel.current != null;
                setState(() {
                  hasActiveSubscription = hasActiveSub;
                });
                print(
                    "🔔 Subscription status updated: hasActiveSubscription = $hasActiveSub");
              } else if (state is SubscriptionCancelledState) {
                setState(() {
                  hasActiveSubscription = false;
                });
                print(
                    "🔔 Subscription cancelled: hasActiveSubscription = false");
              } else if (state is SubscriptionErrorState) {
                setState(() {
                  hasActiveSubscription = false;
                });
                print("🔔 Subscription error: hasActiveSubscription = false");
              }
            },
          ),
        ],
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 70.68.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 19.0),
                  child: InkWell(
                    onTap: _handleBackPress,
                    child: Icon(Icons.arrow_back_ios,
                        color: AppPallete.secondaryColor),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(right: 34.w),
                  child: SizedBox(
                    width: 94.w,
                    height: 42.34.h,
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: () {
                            final parentId =
                                locator<SharedRepository>().getParentData()?.id;
                            if (parentId != null) {
                              NavigatorService.pushNamed(AppRoutes.notification,
                                  arguments: parentId);
                              try {
                                final badgeService =
                                    locator<BadgeCountService>();
                                badgeService.setNotificationCount(0);
                              } catch (e) {
                                print('Error resetting notification count: $e');
                              }
                            }
                          },
                          child: notification_badge(
                              context: context,
                              icon: Icons.notifications,
                              badgeCount: _notificationCount),
                        ),
                        SizedBox(width: 9.w),
                        GestureDetector(
                          onTap: () {
                            NavigatorService.pushNamed(AppRoutes.centerMessage,
                                arguments: 'user');
                            try {
                              final badgeService = locator<BadgeCountService>();
                              badgeService.setMessageCount(0);
                            } catch (e) {
                              print('Error resetting message count: $e');
                            }
                          },
                          child: notification_badge(
                              context: context,
                              icon: Icons.messenger_outline_sharp,
                              badgeCount: _messageCount),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.only(left: 20.w, top: 24.h),
              child: Row(
                children: [
                  CustomImageBuilder(
                      imagePath:
                          "${AppText.device}${userData?.data?.parent?.image?.url}",
                      height: 97.h,
                      width: 97.w,
                      borderRadius: 99.r),
                  SizedBox(width: 13.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      customtext(
                          context: context,
                          newYear: userData?.data?.parent?.fullname ?? "",
                          font: 25.sp,
                          weight: FontWeight.w600),
                      customtext(
                          context: context,
                          newYear:
                              "Referral code: ${userData?.data?.parent?.classZId}",
                          font: 15.sp,
                          weight: FontWeight.w400)
                    ],
                  )
                ],
              ),
            ),
            SizedBox(height: 7.h),
            customDivider(width: 430.w, padding: 0),
            SizedBox(height: 15.h),
            walletCardBig(
                context: context,
                money: currentBalance,
                hasActiveSubscription: hasActiveSubscription),
            Padding(
              padding: EdgeInsets.only(left: 35.w, top: 42.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  customtext(
                      context: context,
                      newYear: "Purchase History",
                      font: 25.sp,
                      weight: FontWeight.w600),
                  Padding(
                    padding: EdgeInsets.only(right: 35.w),
                    child: IconButton(
                      icon:
                          Icon(Icons.refresh, color: AppPallete.secondaryColor),
                      onPressed: () {
                        _refreshData();
                      },
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 35.w, top: 1.h),
              child: customtext(
                  context: context,
                  newYear: "Display the most recent purchases",
                  font: 15.sp,
                  weight: FontWeight.w600),
            ),
            SizedBox(height: 10.h),
            Expanded(
              child: _isLoading
                  ? _buildLoadingView()
                  : _buildPurchaseHistoryView(),
            ),
          ],
        ),
      ),
    );
  }

  // Loading indicator
  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor:
                AlwaysStoppedAnimation<Color>(AppPallete.secondaryColor),
          ),
          SizedBox(height: 20.h),
          customtext(
              context: context,
              newYear: "Loading purchase history...",
              font: 16.sp,
              weight: FontWeight.w400),
        ],
      ),
    );
  }

  Widget _buildPurchaseHistoryCard(PurchasedHistoryModel purchase) {
    String formattedDate = DateFormat('dd/MM/yyyy').format(purchase.createdAt!);
    return Card(
      color: Colors.white,
      margin: EdgeInsets.symmetric(vertical: 8.h, horizontal: 4.w),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    purchase.order?.classId?.classProviding ?? "Unknown Item",
                    style:
                        TextStyle(fontWeight: FontWeight.bold, fontSize: 16.sp),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 16.w),
                Row(
                  children: [
                    Text(
                      purchase.order?.amount.toString() ?? '0',
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16.sp,
                          color: AppPallete.secondaryColor),
                    ),
                    SizedBox(width: 4.w),
                    customSvgPicture(
                        imagePath: ImagePath.zSvg, height: 18, width: 18),
                  ],
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Date: $formattedDate',
                  style: TextStyle(color: Colors.grey[600], fontSize: 14.sp),
                ),
                Text(
                  'Quantity: ${purchase.order?.quantity ?? 0}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 14.sp),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Purchase history view based on state
  Widget _buildPurchaseHistoryView() {
    // Error state
    if (_errorMessage != null && _purchasedHistory.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 40, color: Colors.red),
            SizedBox(height: 16),
            customtext(
                context: context,
                newYear: "Error loading purchase history",
                font: 20.sp,
                weight: FontWeight.w500),
            SizedBox(height: 8),
            TextButton(
                onPressed: _fetchPurchaseHistory, child: Text("Try Again")),
          ],
        ),
      );
    }

    // Empty state
    if (_purchasedHistory.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history, size: 40, color: Colors.grey),
            SizedBox(height: 16),
            customtext(
                context: context,
                newYear: "No purchase history yet",
                font: 20.sp,
                weight: FontWeight.w500),
          ],
        ),
      );
    }

    // Data view - make it scrollable
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 8.h),
      itemCount: _purchasedHistory.length,
      itemBuilder: (context, index) {
        final purchase = _purchasedHistory[index];
        return _buildPurchaseHistoryCard(purchase);
      },
    );
  }
}
