import 'package:class_z/core/imports.dart';

class Orders extends StatefulWidget {
  const Orders({super.key});

  @override
  State<Orders> createState() => _OrdersState();
}

class _OrdersState extends State<Orders> {
  @override
  void initState() {
    context.read<UserBloc>().orders;
    super.initState();
    _fetchData();
  }

  void _fetchData() {
    context.read<UserBloc>().add(GetOrdersByUserEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "All Classes",
        leading: customBackButton(),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          _fetchData();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.only(top: 48.h, left: 25.w, right: 25.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BlocBuilder<UserBloc, UserState>(
                  builder: (context, state) {
                    if (state is UserLoadingState) {
                      return Center(child: CircularProgressIndicator());
                    } else if (state is UserErrorState) {
                      return Center(child: Text(state.message));
                    } else if (state is GetOrdersByUserSuccessState) {
                      final orders = state.orders;
                      if (orders.isEmpty) {
                        return SizedBox(
                          height: getHeight(context: context) / 2,
                          child: Center(
                            child: Text('No classes found'),
                          ),
                        );
                      }
                      return ListView.builder(
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            final currentOrder = orders[index];

                            // Handle different date formats
                            DateTime? d;
                            if (currentOrder.date is String) {
                              d = DateTime.tryParse(
                                  currentOrder.date as String);
                            } else if (currentOrder.date is DateTime) {
                              d = currentOrder.date as DateTime;
                            } else if (currentOrder.date is List &&
                                (currentOrder.date as List).isNotEmpty) {
                              final dateValue =
                                  (currentOrder.date as List).first;
                              if (dateValue is String) {
                                d = DateTime.tryParse(dateValue);
                              } else if (dateValue is DateTime) {
                                d = dateValue;
                              }
                            }

                            if (d == null) {
                              // Skip items with invalid dates
                              return SizedBox.shrink();
                            }

                            return Column(
                              children: [
                                timeTableCard(
                                    context: context,
                                    user: currentOrder.child?.fullname ?? "",
                                    confirmed: true,
                                    date: dateGenerator(
                                        date: d, format: 'dd/MM/yyyy'),
                                    location: currentOrder.classs?.address ==
                                            'center'
                                        ? currentOrder
                                                .classs?.center?.displayName ??
                                            "Unknown"
                                        : "OffSite",
                                    course:
                                        currentOrder.classs?.classProviding ??
                                            "",
                                    time:
                                        "${currentOrder.order?.startTime ?? 'N/A'} - ${currentOrder.order?.endTime ?? 'N/A'}",
                                    classTime:
                                        "${currentOrder.order?.durationMinutes ?? 'N/A'}",
                                    special: currentOrder.order?.sen == true
                                        ? "Special note: SEN service"
                                        : "",
                                    coach:
                                        "by ${currentOrder.classs?.coach?.displayName ?? 'Unknown'}",
                                    order: currentOrder),
                                SizedBox(
                                  height: 27.h,
                                )
                              ],
                            );
                          },
                          itemCount: orders.length);
                    }
                    return SizedBox(
                      height: getHeight(context: context) / 2,
                      child: Center(
                        child: Text('Something went wrong. Try again'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
