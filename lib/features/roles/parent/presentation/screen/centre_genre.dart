import 'package:class_z/core/imports.dart';

class CentreGenre extends StatefulWidget {
  const CentreGenre({super.key});

  @override
  State<CentreGenre> createState() => _CentreGenreState();
}

class _CentreGenreState extends State<CentreGenre> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 29.68.h,
            ),
            customNoTitleWidget(context: context),
            <PERSON>zed<PERSON>ox(
              height: 6.97.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 9.w),
              child: Sized<PERSON><PERSON>(
                height: 28.h,
                child: customtext(
                    context: context,
                    newYear: "Music",
                    font: 30.sp,
                    weight: FontWeight.w500),
              ),
            ),
            Sized<PERSON><PERSON>(
              height: 21.h,
            ),
            customDivider(width: 430.w, padding: 0),
            <PERSON><PERSON><PERSON><PERSON>(
              height: 12.h,
            ),
            rowOf<PERSON>ilter(context: context),
            <PERSON><PERSON><PERSON><PERSON>(
              height: 12.h,
            ),
            Expanded(
                child: ListView.separated(
              scrollDirection: Axis.vertical,
              itemBuilder: (context, index) {
                return centreSpotlightWidget(context, center: CenterData());
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 29.h,
                );
              },
              itemCount: 10,
            ))
          ],
        ),
      ),
    );
  }

  Widget rowOfFilter({required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.only(left: 11.w),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              NavigatorService.pushNamed(AppRoutes.searchGenre);
            },
            child: Container(
              height: 22.h,
              width: 59.w,
              decoration: BoxDecoration(
                  color: AppPallete.paleGrey,
                  borderRadius: BorderRadius.circular(10.r)),
              child: Center(
                child: customtext(
                    context: context,
                    newYear: "Coach",
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.darkGrey),
              ),
            ),
          ),
          SizedBox(
            width: 15.w,
          ),
          GestureDetector(
            onTap: () {
              NavigatorService.pushNamed(AppRoutes.centreGenre);
            },
            child: Container(
              height: 22.h,
              width: 59.w,
              decoration: BoxDecoration(
                  color: AppPallete.secondaryColor,
                  borderRadius: BorderRadius.circular(10.r)),
              child: Center(
                child: customtext(
                    context: context,
                    newYear: "Centre",
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.white),
              ),
            ),
          ),
          SizedBox(
            width: 232.w,
          ),
          GestureDetector(
            onTap: () {
              //NavigatorService.pushNamed();
            },
            child: customSvgPicture(
                imagePath: ImagePath.filterSvg, height: 24.h, width: 21.33.w),
          )
        ],
      ),
    );
  }
}
