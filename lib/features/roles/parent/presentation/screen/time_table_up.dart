import 'package:class_z/core/imports.dart';
import 'package:class_z/features/roles/parent/presentation/widgets/custom_calendar_order.dart';

class TimetableUp extends StatefulWidget {
  const TimetableUp({super.key});

  @override
  State<TimetableUp> createState() => _TimetableUpState();
}

class _TimetableUpState extends State<TimetableUp>
    with SingleTickerProviderStateMixin {
  TimetableUpModel? timetableList;
  int currentPage = 0;
  final int pageSize = 10;
  bool isLoading = false;
  bool _hasLoadedTimetables = false;
  final ScrollController _scrollController = ScrollController();

  late ValueNotifier<List<EventElement>> _selectedTimetable;
  late TabController _tabController;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;

  @override
  void initState() {
    super.initState();

    _selectedTimetable = ValueNotifier<List<EventElement>>([]);
    _tabController = TabController(length: 2, vsync: this);

    _tabController.addListener(() {
      if (_tabController.indexIsChanging == false) {
        setState(() {}); // Rebuild UI when tab changes
      }
    });

    _scrollController.addListener(_scrollListener);

    if (timetableList == null) {
      final parentId = locator<SharedRepository>().getParentData()?.id ?? "";
      context
          .read<UserBloc>()
          .add(GetTimeTableByParentIdEvent(parentId: parentId));
    } else {
      _loadMoreTimetables();
    }
  }

  void _scrollListener() {
    // Remove pagination logic, not needed for this model
  }

  void _loadMoreTimetables() {
    // Remove pagination logic, not needed for this model
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_hasLoadedTimetables) {
      _fetchData();
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _selectedTimetable.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _fetchData() {
    _hasLoadedTimetables = true;
    final parentId = locator<SharedRepository>().getParentData()?.id ?? "";
    context
        .read<UserBloc>()
        .add(GetTimeTableByParentIdEvent(parentId: parentId));
  }

  DateTime? _parseOrderDate(dynamic dateValue) {
    if (dateValue == null) return null;

    if (dateValue is String) {
      return DateTime.tryParse(dateValue);
    } else if (dateValue is DateTime) {
      return dateValue;
    } else if (dateValue is List && dateValue.isNotEmpty) {
      final firstDate = dateValue.first;
      if (firstDate is String) {
        return DateTime.tryParse(firstDate);
      } else if (firstDate is DateTime) {
        return firstDate;
      }
    }
    return null;
  }

  void _onselectedDay(DateTime selectedDay, DateTime focusedDay) {
    print("$selectedDay,$focusedDay");
    setState(() {
      _selectedDay = selectedDay;
      _focusedDay = focusedDay;
      _selectedTimetable.value = _getTimetableForDay(selectedDay);
    });
  }

  List<EventElement> _getTimetableForDay(DateTime dateTime) {
    final events = timetableList?.events ?? [];
    return events.where((e) {
      final eventDate = e.event?.date;
      return eventDate != null &&
          eventDate.year == dateTime.year &&
          eventDate.month == dateTime.month &&
          eventDate.day == dateTime.day;
    }).toList();
  }

  Widget _topbar1(
      {required BuildContext context,
      required TimetableUpModel? timetableList}) {
    return _buildAllUpcomingOrders();
  }

  Widget calendar({required BuildContext context}) {
    final events = _getTimetableForDay(_selectedDay ?? _focusedDay);
    return Padding(
      padding: EdgeInsets.only(left: 25.w, right: 25.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 14.h,
          ),
          CustomCalendarOrder(
            focusedDay: _focusedDay,
            selectedDay: _selectedDay,
            onDaySelected: _onselectedDay,
            timeTables: timetableList?.events ?? [],
          ),
          SizedBox(
            height: 25.h,
          ),
          customtext(
              context: context,
              newYear:
                  "Class on this day - ${dateGenerator(date: _selectedDay ?? _focusedDay, format: 'dd/MM/yyyy')}",
              font: 20.sp,
              weight: FontWeight.w600),
          SizedBox(
            height: 18.h,
          ),
          Expanded(
              child: events.isEmpty
                  ? Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text("No events available for this day."),
                    )
                  : _buildOrdersForCalender()
              //  ListView.separated(
              //     padding: const EdgeInsets.only(top: 0),
              //     physics: const AlwaysScrollableScrollPhysics(),
              //     itemCount: events.length,
              //     itemBuilder: (context, index) {
              //       final eventElement = events[index];
              //       final event = eventElement.event;
              //       final classId = event?.classId;
              //       final date = event?.date;
              //       return ListTile(
              //         title: Text(eventElement.childName ?? ''),
              //         subtitle: Text(classId?.classProviding ?? ''),
              //       );
              //     },
              //     separatorBuilder: (context, index) {
              //       return SizedBox(
              //         height: 27.h,
              //       );
              //     },
              //   ),
              ),
        ],
      ),
    );
  }

  // Place _buildAllUpcomingOrders before _topbar1 to avoid reference errors
  Widget _buildAllUpcomingOrders() {
    final events = List<EventElement>.from(timetableList?.events ?? [])
      ..sort((a, b) {
        final aDate = a.event?.date;
        final bDate = b.event?.date;
        if (aDate == null && bDate == null) return 0;
        if (aDate == null) return 1;
        if (bDate == null) return -1;
        return aDate.compareTo(bDate);
      });
    if (events.isEmpty) {
      return Center(
        child: customtext(
          context: context,
          newYear: "No upcoming classes",
          font: 20.sp,
        ),
      );
    }
    return ListView.separated(
      padding: const EdgeInsets.only(top: 0, left: 25, right: 25),
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: events.length,
      itemBuilder: (context, index) {
        final eventElement = events[index];
        final event = eventElement.event;
        final classId = event?.classId;
        final date = event?.date;
        if (date == null) return SizedBox.shrink();
        // Extract start and end time from event.dateId (ClassDate)
        final startTime = event?.dateId?.startTime ?? '';
        final endTime = event?.dateId?.endTime ?? '';
        final time = (startTime.isNotEmpty && endTime.isNotEmpty)
            ? '$startTime - $endTime'
            : (startTime.isNotEmpty
                ? startTime
                : (endTime.isNotEmpty ? endTime : ''));
        // Map fields for timeTableCard (same as before)
        final imagePath =
            imageStringGenerator(imagePath: classId?.mainImage?.url ?? '');
        final course = classId?.course ?? false;
        final title = classId?.classProviding ?? '';
        final category = classId?.level ?? '';
        final location = classId?.address ?? '';
        final ageGroup = "(${classId?.ageFrom ?? ''}-${classId?.ageTo ?? ''})";
        final rate = classId?.charge?.toString() ?? '0';
        final user = eventElement.childId?.fullname ?? '';
        final confirmed = true;
        final classTime = "-";
        String special = '';
        if (eventElement.childId != null &&
            eventElement.childId is Map &&
            (eventElement.childId as Map)['sen'] == true) {
          special = 'Special Request :SEN service';
        } else if (eventElement.childId != null &&
            eventElement.childId is ChildModel &&
            (eventElement.childId as ChildModel).sen == true) {
          special = 'Special Request :SEN service';
        }
        final coach = "by " + (classId?.coach?.displayName ?? "Unknown");

        return timeTableCard(
          context: context,
          user: user,
          confirmed: confirmed,
          date: dateGenerator(date: date, format: 'dd/MM/yyyy'),
          location: location,
          course: title,
          time: time,
          classTime: event?.dateId?.durationMinutes ?? 'Unknown',
          special: special,
          coach: coach,
          event: eventElement,
        );
      },
      separatorBuilder: (context, index) => SizedBox(height: 27.h),
    );
  }

  Widget _buildOrdersForCalender() {
    // Get and sort events by date ascending
    final events = List<EventElement>.from(_selectedTimetable.value)
      ..sort((a, b) {
        final aDate = a.event?.date;
        final bDate = b.event?.date;
        if (aDate == null && bDate == null) return 0;
        if (aDate == null) return 1;
        if (bDate == null) return -1;
        return aDate.compareTo(bDate);
      });
    return ListView.separated(
      padding: const EdgeInsets.only(top: 0),
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: events.length,
      itemBuilder: (context, index) {
        final eventElement = events[index];
        final event = eventElement.event;
        final classId = event?.classId;
        final date = event?.date;
        if (date == null) {
          return SizedBox.shrink();
        }
        // Extract start and end time from event.dateId (ClassDate)
        final startTime = event?.dateId?.startTime ?? '';
        final endTime = event?.dateId?.endTime ?? '';
        final time = (startTime.isNotEmpty && endTime.isNotEmpty)
            ? '$startTime - $endTime'
            : (startTime.isNotEmpty
                ? startTime
                : (endTime.isNotEmpty ? endTime : ''));
        // Map fields for timeTableCard
        final imagePath =
            imageStringGenerator(imagePath: classId?.mainImage?.url ?? '');
        final course = classId?.course ?? false;
        final title = classId?.classProviding ?? '';
        final category = classId?.level ?? '';
        final location = classId?.address ?? '';
        final ageGroup = "(${classId?.ageFrom ?? ''}-${classId?.ageTo ?? ''})";
        final rate = classId?.charge?.toString() ?? '0';
        final user = eventElement.childId?.fullname ?? '';
        final confirmed = true;
        final classTime = "-";
        // Special request logic for SEN
        String special = '';
        if (eventElement.childId != null &&
            eventElement.childId is Map &&
            (eventElement.childId as Map)['sen'] == true) {
          special = 'Special Request :SEN service';
        } else if (eventElement.childId != null &&
            eventElement.childId is ChildModel &&
            (eventElement.childId as ChildModel).sen == true) {
          special = 'Special Request :SEN service';
        }
        final coach = "by ${classId?.coach?.displayName ?? "Unknown"}";

        return timeTableCard(
          context: context,
          user: user,
          confirmed: confirmed,
          date: dateGenerator(date: date, format: 'dd/MM/yyyy'),
          location: location,
          course: title,
          time: time,
          classTime: event?.dateId?.durationMinutes ?? 'Unknown',
          special: special,
          coach: coach,
          event: eventElement,
        );
      },
      separatorBuilder: (context, index) {
        return SizedBox(
          height: 27.h,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: RefreshIndicator(
          onRefresh: () async {
            _fetchData();
          },
          child: DefaultTabController(
            length: 2,
            child: BlocListener<UserBloc, UserState>(
              listener: (context, state) {
                if (state is UserLoadingState)
                  loadingState(context: context);
                else
                  hideLoadingDialog(context);
                if (state is UserErrorState)
                  errorState(context: context, error: state.message);
                if (state is GetTimeTableByParentIdSuccessState) {
                  setState(() {
                    timetableList = state.timetableList;
                  });
                }
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  topBar(context: context),
                  Padding(
                    padding: EdgeInsets.only(top: 8.97.h, left: 20.w),
                    child: customtext(
                      context: context,
                      newYear: "Timetable",
                      font: 30.sp,
                      weight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 24.h),
                  Material(
                    color: Colors.transparent,
                    child: TabBar(
                      controller: _tabController,
                      tabs: [
                        Tab(
                            child: customtext(
                                context: context,
                                newYear: "Upcoming",
                                font: 17.sp,
                                weight: FontWeight.w700)),
                        Tab(
                            child: customtext(
                                context: context,
                                newYear: "Calendar",
                                font: 17.sp,
                                weight: FontWeight.w700)),
                      ],
                      indicator: UnderlineTabIndicator(
                        borderSide: BorderSide(width: 1.w, color: Colors.black),
                      ),
                    ),
                  ),
                  SizedBox(height: 20.h),
                  Expanded(
                    child: Builder(
                      builder: (_) {
                        print(_tabController.index.toString() + "index");
                        if (_tabController.index == 0) {
                          return GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onHorizontalDragEnd: (details) {
                              if ((details.primaryVelocity ?? 0) < -200) {
                                _tabController.animateTo(1);
                              }
                            },
                            child: _topbar1(
                                context: context, timetableList: timetableList),
                          );
                        } else {
                          return calendar(context: context);
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          )),
    );
  }

  Widget topBar({required BuildContext context}) {
    return customTopBarOnlyIcon(
      context: context,
      badgeCount1: 0,
      badgeCount2: 0,
      onTap1: () {
        NavigatorService.pushNamed(AppRoutes.notification,
            arguments: locator<SharedRepository>().getParentData()?.id);
      },
      onTap2: () {
        NavigatorService.pushNamed(AppRoutes.centerMessage, arguments: 'user');
      },
    );
  }
}
