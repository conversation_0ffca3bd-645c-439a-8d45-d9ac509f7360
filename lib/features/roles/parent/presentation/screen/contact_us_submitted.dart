import 'package:class_z/core/imports.dart';

class ContactUsSubmitted extends StatelessWidget {
  const ContactUsSubmitted({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppPallete.secondaryColor,
      body: Padding(
        padding: EdgeInsets.only(left: 33.w, right: 37.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 184.h,
            ),
            customtext(
                context: context,
                newYear: "We hear you! Thanks for reaching out!",
                font: 50.sp,
                color: Colors.white,
                weight: FontWeight.w700),
            Sized<PERSON>ox(
              height: 37.h,
            ),
            customtext(
                context: context,
                newYear: "Please kindly await our response.",
                font: 20.sp,
                color: Colors.white,
                weight: FontWeight.w400),
            <PERSON><PERSON><PERSON><PERSON>(
              height: 227.h,
            ),
            Center(
              child: But<PERSON>(
                buttonText: "Back",
                color: AppPallete.white,
                height: 35.5.h,
                width: 218.w,
                textColorFinal: AppPallete.secondaryColor,
                textSize: 17.sp,
                fontWeight: FontWeight.w500,
                onPressed: () {
                  NavigatorService.pushNamed(AppRoutes.homePage );
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}
