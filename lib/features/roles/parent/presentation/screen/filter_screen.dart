import 'package:class_z/core/imports.dart';
import 'package:flutter/material.dart';

class FilterScreen extends StatefulWidget {
  final String? title;
  final int? minPrice;
  final int? maxPrice;
  final int? minAge;
  final int? maxAge;
  final String? location;
  final String? sortBy;
  final bool? senService;
  final double? rating;

  const FilterScreen({
    this.title,
    this.minPrice,
    this.maxPrice,
    this.minAge,
    this.maxAge,
    this.location,
    this.sortBy,
    this.senService,
    this.rating,
    Key? key,
  }) : super(key: key);

  @override
  State<FilterScreen> createState() => _FilterScreenState();
}

class _FilterScreenState extends State<FilterScreen> {
  // Filter state
  String? _selectedSortBy;
  String? _selectedRegion;
  String? _selectedDistrict;
  double _minPrice = 12.0;
  double _maxPrice = 36.0;
  int? _minAge = 3;
  int? _maxAge = 6;
  double? _rating;
  bool _senService = false;

  // Text controllers
  late TextEditingController _minPriceController;
  late TextEditingController _maxPriceController;
  late TextEditingController _minAgeController;
  late TextEditingController _maxAgeController;

  // Price range constants
  static const double kMinPriceLimit = 10.0;
  static const double kMaxPriceLimit = 50.0;

  // Simulated centre distribution data (in real app, this would come from API)
  final List<int> _centreDistribution = [
    5,
    8,
    12,
    18,
    25,
    32,
    28,
    22,
    35,
    40,
    45,
    38,
    30,
    25,
    20,
    15,
    12,
    8,
    6,
    3
  ];

  // District data
  final Map<String, List<String>> _regionDistricts = {
    'Your location': [],
    'Hong Kong Island': [
      'Chai Wan',
      'Hang Fa Chuen',
      'Sau Kei Wan',
      'Sai Wan Ho',
      'Tai Koo',
      'Quarry Bay',
      'North Point',
      'Fortress Hill',
      'Tin Hau',
      'Causeway Bay',
      'Wan Chai',
      'Admiralty',
    ],
    'Kowloon': [
      'Tsim Sha Tsui',
      'Yau Ma Tei',
      'Mong Kok',
      'Sham Shui Po',
      'Cheung Sha Wan',
      'Lai Chi Kok',
      'Mei Foo',
      'Lai King',
    ],
    'New Territories': [
      'Tsuen Wan',
      'Kwai Fong',
      'Kwai Chung',
      'Tsing Yi',
      'Tung Chung',
      'Yuen Long',
      'Tin Shui Wai',
      'Tuen Mun',
    ],
  };

  // Rating options
  final List<double> _ratingOptions = [1.0, 2.0, 3.0, 4.0, 5.0];

  @override
  void initState() {
    super.initState();
    // Initialize values from passed parameters
    _minPrice = (widget.minPrice ?? 12).toDouble();
    _maxPrice = (widget.maxPrice ?? 36).toDouble();
    _minAge = widget.minAge ?? 3;
    _maxAge = widget.maxAge ?? 6;
    _selectedSortBy = widget.sortBy ?? 'distance';
    _selectedRegion = widget.location?.split(',').first ?? 'Hong Kong Island';
    _selectedDistrict = widget.location?.contains(',') == true
        ? widget.location?.split(',').last
        : 'Causeway Bay';
    _rating = widget.rating ?? 4.0;
    _senService = widget.senService ?? false;

    // Initialize text controllers
    _minPriceController =
        TextEditingController(text: _minPrice.round().toString());
    _maxPriceController =
        TextEditingController(text: _maxPrice.round().toString());
    _minAgeController = TextEditingController(text: _minAge?.toString() ?? '3');
    _maxAgeController = TextEditingController(text: _maxAge?.toString() ?? '6');
  }

  @override
  void dispose() {
    _minPriceController.dispose();
    _maxPriceController.dispose();
    _minAgeController.dispose();
    _maxAgeController.dispose();
    super.dispose();
  }

  void _updatePriceControllers() {
    _minPriceController.text = _minPrice.round().toString();
    _maxPriceController.text = _maxPrice.round().toString();
  }

  void _applyFilters() {
    // Collect all filter values and return to previous screen
    Map<String, dynamic> result = {
      'minPrice': _minPrice.round(),
      'maxPrice': _maxPrice.round(),
      'minAge': _minAge ?? 3,
      'maxAge': _maxAge ?? 6,
      'location': _selectedRegion == 'Your location'
          ? 'Your location'
          : _selectedDistrict != null && _selectedRegion != null
              ? '$_selectedRegion,$_selectedDistrict'
              : _selectedRegion,
      'sortBy': _selectedSortBy ?? 'distance',
      'rating': _rating,
      'senService': _senService,
    };

    print('Applying filters: $result');
    Navigator.of(context).pop(result);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Filter',
          style: TextStyle(
            color: const Color(0xFF424242),
            fontSize: 15,
            fontFamily: 'SF Pro Rounded',
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: false,
        leading: IconButton(
          icon: Icon(Icons.close, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(1),
          child: Container(
            color: const Color(0xFFD9D9D9),
            height: 1,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Sort by
            _buildSection(
              title: 'Sort by',
              subtitle: 'Choose how to order your search results',
              child: _buildSortByOptions(),
            ),

            // Location
            _buildSection(
              title: 'Location',
              subtitle: 'Select your location or choose from regions',
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Regions
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _regionDistricts.keys
                        .map((region) => _buildFilterChip(
                              text: region,
                              isSelected: _selectedRegion == region,
                              onTap: () {
                                setState(() {
                                  if (_selectedRegion == region) {
                                    _selectedRegion = null;
                                    _selectedDistrict = null;
                                  } else {
                                    _selectedRegion = region;
                                    // Clear district selection when changing region
                                    _selectedDistrict = null;
                                  }
                                });
                              },
                            ))
                        .toList(),
                  ),

                  // Districts for selected region
                  if (_selectedRegion != null &&
                      _selectedRegion != 'Your location' &&
                      _regionDistricts[_selectedRegion]!.isNotEmpty)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 12),
                        Text(
                          'Select district',
                          style: TextStyle(
                            color: const Color(0xFF424242),
                            fontSize: 15,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: _regionDistricts[_selectedRegion]!
                              .map((district) => _buildFilterChip(
                                    text: district,
                                    isSelected: _selectedDistrict == district,
                                    onTap: () {
                                      setState(() {
                                        if (_selectedDistrict == district) {
                                          _selectedDistrict = null;
                                        } else {
                                          _selectedDistrict = district;
                                        }
                                      });
                                    },
                                  ))
                              .toList(),
                        ),
                      ],
                    ),
                ],
              ),
            ),

            // Price Range
            _buildSection(
              title: 'Price Range',
              subtitle:
                  'Drag the sliders to set minimum and maximum price (in \$ per hour) - shows centre distribution',
              child: _buildPriceRangeSlider(),
            ),

            // Age
            _buildSection(
              title: 'Age',
              subtitle: 'Enter the age range you are interested in',
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Min. Age',
                          style: TextStyle(
                            color: const Color(0xFF424242),
                            fontSize: 15,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        SizedBox(height: 4),
                        TextField(
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            contentPadding:
                                EdgeInsets.symmetric(horizontal: 12),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide: BorderSide(
                                color: const Color(0xFF424242),
                                width: 0.5,
                              ),
                            ),
                            hintText: _minAge?.toString() ?? '3',
                            suffixText: 'years',
                          ),
                          controller: _minAgeController,
                          onChanged: (value) {
                            if (value.isNotEmpty) {
                              try {
                                setState(() {
                                  _minAge = int.tryParse(value) ?? 3;
                                });
                              } catch (e) {
                                print('Error parsing min age: $e');
                              }
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Max. Age',
                          style: TextStyle(
                            color: const Color(0xFF424242),
                            fontSize: 15,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                        SizedBox(height: 4),
                        TextField(
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            contentPadding:
                                EdgeInsets.symmetric(horizontal: 12),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                              borderSide: BorderSide(
                                color: const Color(0xFF424242),
                                width: 0.5,
                              ),
                            ),
                            hintText: _maxAge?.toString() ?? '6',
                            suffixText: 'years',
                          ),
                          controller: _maxAgeController,
                          onChanged: (value) {
                            if (value.isNotEmpty) {
                              try {
                                setState(() {
                                  _maxAge = int.tryParse(value) ?? 6;
                                });
                              } catch (e) {
                                print('Error parsing max age: $e');
                              }
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Rating
            _buildSection(
              title: 'Rating',
              subtitle: 'Filter by minimum rating (1-5 stars)',
              child: Row(
                children: _ratingOptions.map((rating) {
                  bool isSelected = _rating == rating;
                  return Padding(
                    padding: EdgeInsets.only(right: 8),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          if (_rating == rating) {
                            _rating = null;
                          } else {
                            _rating = rating;
                          }
                        });
                      },
                      child: Container(
                        width: 53,
                        height: 23,
                        decoration: BoxDecoration(
                          color: isSelected
                              ? const Color(0xFF3E90F1)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          border: !isSelected
                              ? Border.all(
                                  width: 0.5, color: const Color(0xFF424242))
                              : null,
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          rating.toStringAsFixed(1),
                          style: TextStyle(
                            color: isSelected
                                ? Colors.white
                                : const Color(0xFF424242),
                            fontSize: 15,
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),

            // SEN Service
            _buildSection(
              title: 'Special Educational Needs (SEN) Services',
              subtitle:
                  'Filter centres that provide special education services',
              child: Row(
                children: [
                  Switch(
                    value: _senService,
                    onChanged: (value) {
                      setState(() {
                        _senService = value;
                      });
                    },
                    activeColor: const Color(0xFF3E90F1),
                  ),
                  SizedBox(width: 8),
                  Text(
                    _senService ? 'Enabled' : 'Disabled',
                    style: TextStyle(
                      color: const Color(0xFF424242),
                      fontSize: 15,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 80),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.all(16),
        child: GestureDetector(
          onTap: _applyFilters,
          child: Container(
            height: 49,
            decoration: BoxDecoration(
              color: const Color(0xFF3E90F1),
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.25),
                  blurRadius: 4,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            alignment: Alignment.center,
            child: Text(
              'Search',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriceRangeSlider() {
    return Column(
      children: [
        // Price range slider with better visual design
        Container(
          height: 60,
          child: Column(
            children: [
              // Range slider with custom styling
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: const Color(0xFF3E90F1),
                  inactiveTrackColor: const Color(0xFFE0E0E0),
                  thumbColor: const Color(0xFF3E90F1),
                  overlayColor: const Color(0xFF3E90F1).withOpacity(0.2),
                  thumbShape: RoundSliderThumbShape(enabledThumbRadius: 12),
                  overlayShape: RoundSliderOverlayShape(overlayRadius: 20),
                  trackHeight: 4,
                  rangeThumbShape:
                      RoundRangeSliderThumbShape(enabledThumbRadius: 12),
                  rangeTrackShape: RoundedRectRangeSliderTrackShape(),
                  valueIndicatorColor: const Color(0xFF3E90F1),
                  valueIndicatorTextStyle: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  showValueIndicator: ShowValueIndicator.always,
                ),
                child: RangeSlider(
                  values: RangeValues(_minPrice, _maxPrice),
                  min: kMinPriceLimit,
                  max: kMaxPriceLimit,
                  divisions: 40,
                  labels: RangeLabels(
                    '\$${_minPrice.round()}',
                    '\$${_maxPrice.round()}',
                  ),
                  onChanged: (RangeValues values) {
                    setState(() {
                      _minPrice = values.start;
                      _maxPrice = values.end;
                      _updatePriceControllers();
                    });
                  },
                ),
              ),
            ],
          ),
        ),

        // Price range display with better styling
        Container(
          margin: EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: const Color(0xFFF8F9FA),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: const Color(0xFFE0E0E0)),
                ),
                child: Text(
                  '\$${_minPrice.round()}',
                  style: TextStyle(
                    color: const Color(0xFF3E90F1),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: Row(
                  children: [
                    Container(
                      width: 30,
                      height: 1,
                      color: const Color(0xFF3E90F1),
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Price Range',
                      style: TextStyle(
                        color: const Color(0xFF757575),
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    SizedBox(width: 8),
                    Container(
                      width: 30,
                      height: 1,
                      color: const Color(0xFF3E90F1),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: const Color(0xFFF8F9FA),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: const Color(0xFFE0E0E0)),
                ),
                child: Text(
                  '\$${_maxPrice.round()}',
                  style: TextStyle(
                    color: const Color(0xFF3E90F1),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: 20),

        // Quick price presets
        Container(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Quick presets',
                style: TextStyle(
                  color: const Color(0xFF424242),
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildPricePreset('Budget', 10, 20),
                  _buildPricePreset('Mid-range', 20, 35),
                  _buildPricePreset('Premium', 35, 50),
                  _buildPricePreset('All prices', 10, 50),
                ],
              ),
            ],
          ),
        ),

        SizedBox(height: 20),

        // Enhanced price input fields
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Min. Price',
                    style: TextStyle(
                      color: const Color(0xFF424242),
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 4,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: const Color(0xFFE0E0E0),
                            width: 1,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: const Color(0xFF3E90F1),
                            width: 2,
                          ),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        hintText: _minPrice.round().toString(),
                        prefixText: '\$',
                        prefixStyle: TextStyle(
                          color: const Color(0xFF3E90F1),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      controller: _minPriceController,
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          try {
                            double newValue =
                                double.tryParse(value) ?? kMinPriceLimit;
                            if (newValue >= kMinPriceLimit &&
                                newValue <= _maxPrice) {
                              setState(() {
                                _minPrice = newValue;
                              });
                            }
                          } catch (e) {
                            print('Error parsing min price: $e');
                          }
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Max. Price',
                    style: TextStyle(
                      color: const Color(0xFF424242),
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 4,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: const Color(0xFFE0E0E0),
                            width: 1,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: const Color(0xFF3E90F1),
                            width: 2,
                          ),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        hintText: _maxPrice.round().toString(),
                        prefixText: '\$',
                        prefixStyle: TextStyle(
                          color: const Color(0xFF3E90F1),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      controller: _maxPriceController,
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          try {
                            double newValue =
                                double.tryParse(value) ?? kMaxPriceLimit;
                            if (newValue <= kMaxPriceLimit &&
                                newValue >= _minPrice) {
                              setState(() {
                                _maxPrice = newValue;
                              });
                            }
                          } catch (e) {
                            print('Error parsing max price: $e');
                          }
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPricePreset(String label, double min, double max) {
    bool isSelected = _minPrice == min && _maxPrice == max;

    return GestureDetector(
      onTap: () {
        setState(() {
          _minPrice = min;
          _maxPrice = max;
          _updatePriceControllers();
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF3E90F1) : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                isSelected ? const Color(0xFF3E90F1) : const Color(0xFFE0E0E0),
            width: 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: const Color(0xFF3E90F1).withOpacity(0.2),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : const Color(0xFF424242),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(width: 4),
            Text(
              '(\$${min.round()}-\$${max.round()})',
              style: TextStyle(
                color: isSelected
                    ? Colors.white.withOpacity(0.8)
                    : const Color(0xFF757575),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    String? subtitle,
    required Widget child,
  }) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16, 16, 16, 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              color: const Color(0xFF424242),
              fontSize: 15,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (subtitle != null) ...[
            SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: const Color(0xFF424242),
                fontSize: 13,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
          SizedBox(height: 8),
          child,
        ],
      ),
    );
  }

  Widget _buildSortByOptions() {
    return Row(
      children: [
        _buildFilterChip(
          text: 'Price',
          isSelected: _selectedSortBy == 'price',
          onTap: () {
            setState(() {
              _selectedSortBy = _selectedSortBy == 'price' ? null : 'price';
            });
          },
        ),
        SizedBox(width: 8),
        _buildFilterChip(
          text: 'Distance',
          isSelected: _selectedSortBy == 'distance',
          onTap: () {
            setState(() {
              _selectedSortBy =
                  _selectedSortBy == 'distance' ? null : 'distance';
            });
          },
        ),
        SizedBox(width: 8),
        _buildFilterChip(
          text: 'Rating',
          isSelected: _selectedSortBy == 'rating',
          onTap: () {
            setState(() {
              _selectedSortBy = _selectedSortBy == 'rating' ? null : 'rating';
            });
          },
        ),
      ],
    );
  }

  Widget _buildFilterChip({
    required String text,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF3E90F1) : const Color(0xFFF4F4F4),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: isSelected ? Colors.white : const Color(0xFF424242),
            fontSize: 15,
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }
}
