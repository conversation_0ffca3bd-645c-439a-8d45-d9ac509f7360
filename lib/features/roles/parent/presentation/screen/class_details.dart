import 'package:class_z/core/imports.dart';
import 'package:class_z/core/common/presentation/widgets/center_map_view.dart';
import 'package:class_z/core/services/course_progress_service.dart';
import 'package:url_launcher/url_launcher_string.dart';

class ClassDetails extends StatefulWidget {
  final ClassModel? classModel;
  final bool edit;
  final int? currentSessionIndex;

  const ClassDetails({
    super.key,
    this.classModel,
    required this.edit,
    this.currentSessionIndex,
  });

  @override
  State<ClassDetails> createState() => _ClassDetailsState();
}

class _ClassDetailsState extends State<ClassDetails> {
  CoachModel? coachModel;
  CenterData? centerModel;
  final CourseProgressService _courseProgressService =
      locator<CourseProgressService>();
  List<PendingModel> _pendingReviews = [];

  @override
  void initState() {
    super.initState();
    context.read<CenterBloc>().add(
          GetCoachAndCenterByClassIdEvent(
            classId: widget.classModel?.id ?? '',
          ),
        );

    // if (widget.classModel?.course == true) {
    //   context.read<CenterBloc>().add(
    //         GetPendingReviewsByClassIdEvent(
    //           widget.classModel?.id ?? '',
    //         ),
    //       );

    //   WidgetsBinding.instance.addPostFrameCallback((_) {
    //     _checkAndShowReviewPopup();
    //   });
    // }
  }

  void _checkAndShowReviewPopup() async {
    if (widget.classModel?.course != true) return;

    final totalSessions = widget.classModel?.dates?.length ?? 0;
    if (totalSessions <= 1) return;

    final currentIndex = widget.currentSessionIndex ?? 0;

    final dummyPending = PendingModel(
      plainClassDetails: widget.classModel,
    );

    final shouldShow = await _courseProgressService.shouldShowReview(
      context: context,
      classModel: widget.classModel!,
      pending: dummyPending,
      currentSessionIndex: currentIndex,
      totalSessions: totalSessions,
    );

    if (shouldShow) {
      NavigatorService.pushNamed(AppRoutes.myReviews);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: widget.edit ? _bottomBar() : const SizedBox.shrink(),
      body: SingleChildScrollView(
        child: Column(
          children: [
            CustomAppBar(
              title: 'Program Details',
              subtitle:
                  "${widget.classModel?.classProviding} (${widget.classModel?.level})",
              leading: customBackButton(),
            ),
            SizedBox(height: 24.h),
            if (widget.edit)
              buildProgressSteps(
                context: context,
                cont1: AppPallete.secondaryColor,
                cont2: AppPallete.dividerTime,
                cont3: AppPallete.dividerTime,
              ),
            _classDetails(context),
          ],
        ),
      ),
    );
  }

  Widget _bottomBar() {
    return SizedBox(
      height: 76.h,
      child: Column(
        children: [
          customDivider(padding: 0),
          SizedBox(height: 13.h),
          Button(
            height: 49.h,
            width: 289.w,
            buttonText: "Program Schedule",
            onPressed: () {
              if (locator<SharedRepository>().getToken() == null) {
                NavigatorService.pushNamed(AppRoutes.logIn);
              } else {
                ClassModel? classs = widget.classModel;
                classs?.center = centerModel;

                final isCourse = widget.classModel?.course ?? false;
                // final route = isCourse
                //     ? AppRoutes.timeslotCentreCourse
                //     : AppRoutes.timeslotCentre;
                // final args = isCourse
                //     ? {
                //         'classModel': widget.classModel,
                //         'center': centerModel,
                //         'coachName': coachModel?.displayName
                //       }
                //     : widget.classModel?.id;
                final args = {
                  'classModel': widget.classModel,
                  'center': centerModel,
                  'coachName': coachModel?.displayName
                };
                NavigatorService.pushNamed(AppRoutes.timeslotCentreCourse,
                    arguments: args);
              }
            },
            color: AppPallete.secondaryColor,
          ),
        ],
      ),
    );
  }

  Widget _classDetails(BuildContext context) {
    final classModel = widget.classModel;

    return BlocConsumer<CenterBloc, CenterState>(listener: (context, state) {
      if (state is CenterLoadingState) {
        loadingState(context: context);
      } else {
        hideLoadingDialog(context);
      }
      if (state is CenterErrorState) {
        errorState(context: context, error: state.message);
      }
    }, builder: (context, state) {
      if (state is CoachAndCenterByClassIdFetchSuccess) {
        coachModel = state.classs.coach;
        centerModel = state.classs.center;
      }

      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 36.h),
            customtext(
              context: context,
              newYear: widget.classModel?.classProviding != null &&
                      widget.classModel!.classProviding!.isNotEmpty
                  ? widget.classModel!.classProviding!
                  : "Program Name Not Available",
              font: 30.sp,
              weight: FontWeight.w700,
            ),
            SizedBox(height: 17.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customtext(
                  context: context,
                  newYear: widget.classModel?.level != null &&
                          widget.classModel!.level!.isNotEmpty
                      ? "(${widget.classModel?.level})"
                      : "",
                  font: 20.sp,
                  weight: FontWeight.w600,
                ),
                customtext(
                  context: context,
                  newYear: widget.classModel?.ageFrom != null &&
                          widget.classModel?.ageTo != null
                      ? "Age ${widget.classModel?.ageFrom}-${widget.classModel?.ageTo}"
                      : "Age range not specified",
                  font: 20.sp,
                  weight: FontWeight.w600,
                ),
              ],
            ),
            SizedBox(height: 17.h),
            widget.classModel?.mainImage?.url != null &&
                    widget.classModel!.mainImage!.url!.isNotEmpty
                ? CustomImageBuilder(
                    imagePath: imageStringGenerator(
                      imagePath: widget.classModel?.mainImage?.url ?? '',
                    ),
                    height: 170.h,
                    borderRadius: 20.r,
                  )
                : Container(
                    height: 170.h,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.image_not_supported_outlined,
                          size: 50.r,
                          color: Colors.grey[400],
                        ),
                        SizedBox(height: 10.h),
                        customtext(
                          context: context,
                          newYear: "No Image Available",
                          font: 16.sp,
                          weight: FontWeight.w500,
                          color: Colors.grey[600],
                        ),
                      ],
                    ),
                  ),
            SizedBox(height: 17.h),
            _descriptionSection(context),
            customDivider(padding: 0),
            if (classModel?.course == true)
              _courseInfo(context, classModel?.numberOfClass.toString() ?? ""),
            SizedBox(height: 1.h),
            _coachSection(context, coachModel?.skill ?? []),
            SizedBox(height: 17.h),
            customDivider(width: 406.w, padding: 0),
            SizedBox(height: 17.h),
            _locationSection(context),
            SizedBox(height: 17.h),
          ],
        ),
      );
    });
  }

  Widget _descriptionSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(15.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              customtext(
                context: context,
                newYear: "Description",
                font: 20.sp,
                weight: FontWeight.w600,
              ),
              widget.classModel?.charge != null
                  ? () {
                      // CURRENCY CONSISTENCY: Check user's Zcoin balance
                      final sharedRepo = locator<SharedRepository>();
                      final userData = sharedRepo.getUserData();
                      final userBalance = userData?.data?.parent?.balance ?? 0;
                      final classCharge = widget.classModel?.charge ?? 0;
                      final hasSufficientZcoin = userBalance >= classCharge;

                      return Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 6.h),
                        decoration: BoxDecoration(
                          color: Colors.grey
                              .withOpacity(0.1), // Neutral background
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        child: Row(
                          children: [
                            hasSufficientZcoin
                                ? customSvgPicture(
                                    imagePath: ImagePath.zSvg,
                                    height: 15.h,
                                    width: 15.w,
                                  )
                                : customtext(
                                    context: context,
                                    newYear: 'HKD',
                                    font: 13.sp,
                                    weight: FontWeight.w600,
                                  ),
                            SizedBox(width: 6.w),
                            customtext(
                              context: context,
                              newYear: hasSufficientZcoin
                                  ? widget.classModel?.charge.toString() ?? "0"
                                  : ((widget.classModel?.charge ?? 0) * 25)
                                      .toStringAsFixed(0), // Convert to HKD
                              font: 15.sp,
                              weight: FontWeight.w600,
                            ),
                          ],
                        ),
                      );
                    }()
                  : SizedBox.shrink(),
            ],
          ),
          SizedBox(height: 14.h),
          customtext(
            context: context,
            newYear: widget.classModel?.description != null &&
                    widget.classModel!.description!.isNotEmpty
                ? widget.classModel!.description!
                : "No description available",
            font: 15.sp,
            weight: FontWeight.w400,
          ),
        ],
      ),
    );
  }

  Widget _courseInfo(BuildContext context, String numberOfClasses) {
    final hasDates = widget.classModel?.dates != null &&
        widget.classModel!.dates!.any((d) => d.date != null);

    return Container(
      margin: EdgeInsets.symmetric(vertical: 15.h),
      padding: EdgeInsets.all(15.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          customtext(
            context: context,
            newYear: "Course Information",
            font: 20.sp,
            weight: FontWeight.w600,
          ),
          SizedBox(height: 14.h),
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.r),
                decoration: BoxDecoration(
                  color: AppPallete.secondaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.calendar_today_rounded,
                  size: 20.r,
                  color: AppPallete.secondaryColor,
                ),
              ),
              SizedBox(width: 10.w),
              customtext(
                context: context,
                newYear: "$numberOfClasses class(es)",
                font: 16.sp,
                weight: FontWeight.w500,
              ),
            ],
          ),
          SizedBox(height: 12.h),
          if (hasDates)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                customtext(
                  context: context,
                  newYear: "Course Dates:",
                  font: 16.sp,
                  weight: FontWeight.w500,
                ),
                SizedBox(height: 8.h),
                ...widget.classModel!.dates!.asMap().entries.map((entry) {
                  final index = entry.key;
                  final date = entry.value;
                  return Padding(
                    padding: EdgeInsets.only(bottom: 4.h),
                    child: Row(
                      children: [
                        Icon(
                          Icons.event,
                          size: 16.r,
                          color: AppPallete.secondaryColor,
                        ),
                        SizedBox(width: 8.w),
                        customtext(
                          context: context,
                          newYear: "Slot ${index + 1}",
                          font: 14.sp,
                          weight: FontWeight.w500,
                          color: AppPallete.secondaryColor,
                        ),
                        if (date.date != null) ...[
                          SizedBox(width: 8.w),
                          customtext(
                            context: context,
                            newYear: "- ${date.date}",
                            font: 14.sp,
                            weight: FontWeight.w400,
                          ),
                        ],
                      ],
                    ),
                  );
                }).toList(),
              ],
            )
          else
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: AppPallete.secondaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    Icons.calendar_today_rounded,
                    size: 20.r,
                    color: AppPallete.secondaryColor,
                  ),
                ),
                SizedBox(width: 10.w),
                customtext(
                  context: context,
                  newYear: "Dates not specified",
                  font: 16.sp,
                  weight: FontWeight.w500,
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _coachSection(BuildContext context, List<Skill> skills) {
    // Check if coach data exists
    final hasCoachData = coachModel != null &&
        coachModel?.displayName != null &&
        coachModel!.displayName!.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customtext(
          context: context,
          newYear: "Coach",
          font: 20.sp,
          weight: FontWeight.w600,
        ),
        SizedBox(height: 17.h),
        if (hasCoachData)
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: Offset(0, 3),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(15.r),
                onTap: () {
                  if (coachModel?.id != null && coachModel!.id!.isNotEmpty) {
                    NavigatorService.pushNamed(AppRoutes.coachView,
                        arguments: coachModel?.id ?? '');
                  }
                },
                child: Padding(
                  padding: EdgeInsets.all(15.r),
                  child: Row(
                    children: [
                      Container(
                        width: 80.w,
                        height: 80.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.r),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 5,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: CustomImageBuilder(
                            imagePath: imageStringGenerator(
                                imagePath: coachModel?.mainImage?.url ?? ''),
                            height: 80.h,
                            width: 80.w,
                            borderRadius: 10.r,
                          ),
                        ),
                      ),
                      SizedBox(width: 15.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: customtext(
                                    context: context,
                                    newYear: coachModel?.displayName ??
                                        "Unknown Coach",
                                    font: 18.sp,
                                    weight: FontWeight.w600,
                                  ),
                                ),
                                customRating(
                                  context: context,
                                  rating: (coachModel?.rating ?? 0).toString(),
                                  fontSize: 14.sp,
                                  weight: FontWeight.w600,
                                ),
                              ],
                            ),
                            SizedBox(height: 8.h),
                            if (coachModel?.ageFrom != null &&
                                coachModel?.ageTo != null)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.person_outline,
                                      size: 16.r,
                                      color: AppPallete.secondaryColor,
                                    ),
                                    SizedBox(width: 5.w),
                                    customtext(
                                      context: context,
                                      newYear:
                                          "Age ${coachModel?.ageFrom} to ${coachModel?.ageTo}",
                                      font: 14.sp,
                                      weight: FontWeight.w400,
                                    ),
                                  ],
                                ),
                              ),
                            if (skills.isNotEmpty)
                              Wrap(
                                spacing: 5.w,
                                runSpacing: 5.h,
                                children: skills.map((skill) {
                                  return Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 8.w,
                                      vertical: 4.h,
                                    ),
                                    decoration: BoxDecoration(
                                      color: AppPallete.secondaryColor
                                          .withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(20.r),
                                    ),
                                    child: customtext(
                                      context: context,
                                      newYear: skill.skillname ?? "",
                                      font: 12.sp,
                                      weight: FontWeight.w500,
                                      color: AppPallete.secondaryColor,
                                    ),
                                  );
                                }).toList(),
                              ),
                            if (coachModel?.sen == true)
                              Padding(
                                padding: EdgeInsets.only(top: 8.h),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.check_circle_outline,
                                      size: 16.r,
                                      color: Colors.green,
                                    ),
                                    SizedBox(width: 5.w),
                                    customtext(
                                      context: context,
                                      newYear: "SEN Certified",
                                      font: 14.sp,
                                      weight: FontWeight.w500,
                                      color: Colors.green,
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          )
        else
          Container(
            padding: EdgeInsets.all(20.r),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: Offset(0, 3),
                ),
              ],
            ),
            child: Center(
              child: customtext(
                context: context,
                newYear: "Coach information not available",
                font: 16.sp,
                weight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
      ],
    );
  }

  Widget _locationSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customtext(
          context: context,
          newYear: "Location",
          font: 20.sp,
          weight: FontWeight.w600,
        ),
        SizedBox(height: 17.h),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Map container - takes 60% of the width
            Expanded(
              flex: 3,
              child: Container(
                height: 200.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 10,
                      offset: Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(15.r),
                  child: CenterMapView(
                    address: centerModel?.address,
                    centerId: centerModel?.id ?? '',
                    height: 200.h,
                    width: double.infinity,
                    borderRadius: 15.r,
                  ),
                ),
              ),
            ),
            SizedBox(width: 12.w),
            // Contact info container - takes 40% of the width
            Expanded(
              flex: 2,
              child: Container(
                height: 200.h,
                padding: EdgeInsets.all(12.r),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 8,
                      offset: Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    customtext(
                      context: context,
                      newYear: "Contact Info",
                      font: 16.sp,
                      weight: FontWeight.w600,
                    ),
                    SizedBox(height: 12.h),
                    // Address
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        customSvgPicture(
                          imagePath: ImagePath.locationSvg,
                          height: 16.h,
                          width: 16.w,
                          color: AppPallete.secondaryColor,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: customtext(
                            context: context,
                            newYear: centerModel?.address != null
                                ? "${centerModel?.address?.address1 ?? ''}, ${centerModel?.address?.city ?? ''}"
                                : "Address not available",
                            font: 12.sp,
                            weight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 12.h),
                    // Phone
                    Row(
                      children: [
                        customSvgPicture(
                          imagePath: ImagePath.phoneSvg,
                          height: 16.h,
                          width: 16.w,
                          color: AppPallete.secondaryColor,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: customtext(
                            context: context,
                            newYear:
                                centerModel?.centerNumber?.isNotEmpty == true
                                    ? centerModel!.centerNumber!
                                    : "Phone number not available",
                            font: 12.sp,
                            weight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 12.h),
                    // Opening Hours
                    if (centerModel?.openingHours != null &&
                        centerModel!.openingHours!.isNotEmpty)
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.access_time_rounded,
                            size: 16.r,
                            color: AppPallete.secondaryColor,
                          ),
                          SizedBox(width: 8.w),
                          Expanded(
                            child: OpeningHoursFormatter
                                .createCollapsibleOpeningHours(
                                    context, centerModel?.openingHours,
                                    dayStyle: TextStyle(
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.bold),
                                    hoursStyle: TextStyle(fontSize: 12.sp)),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 15.h),
        // Open in Maps button
        GestureDetector(
          onTap: () {
            _openInGoogleMaps(context);
          },
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(vertical: 12.h),
            decoration: BoxDecoration(
              color: AppPallete.secondaryColor,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.map_outlined,
                  color: Colors.white,
                  size: 18.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  "Open in Google Maps",
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _openInGoogleMaps(BuildContext context) async {
    if (centerModel?.address == null) {
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('No address available for this center')));
      return;
    }

    try {
      // Format address for maps URL
      final address = centerModel!.address!;
      final parts = [
        if (address.address1 != null && address.address1!.isNotEmpty)
          address.address1,
        if (address.city != null && address.city!.isNotEmpty) address.city,
        if (address.region != null && address.region!.isNotEmpty)
          address.region,
        if (address.country != null && address.country!.isNotEmpty)
          address.country
        else
          'Hong Kong'
      ];

      final formattedAddress = parts.join(', ');
      if (formattedAddress.isNotEmpty) {
        final encodedAddress = Uri.encodeComponent(formattedAddress);
        final url = Platform.isIOS
            ? 'https://maps.apple.com/?q=$encodedAddress'
            : 'https://www.google.com/maps/search/?api=1&query=$encodedAddress';

        final success = await launchUrlString(url);
        if (!success) {
          ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Could not open maps application')));
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Could not determine center location')));
      }
    } catch (e) {
      print('Error opening maps: $e');
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open maps application')));
    }
  }

  // Helper method to format opening hours
  String _formatOpeningHours(List<OpeningHour> hours) {
    if (hours.isEmpty) return "Hours not available";

    // Group by day of week
    Map<String, List<String>> dayHours = {};

    for (var hour in hours) {
      final day = hour.day ?? "Unknown";
      final openTime = hour.openingTime ?? "N/A";
      final closeTime = hour.closingTime ?? "N/A";

      if (!dayHours.containsKey(day)) {
        dayHours[day] = [];
      }

      dayHours[day]!.add("$openTime - $closeTime");
    }

    // Format the output
    List<String> formattedHours = [];
    dayHours.forEach((day, times) {
      formattedHours.add("$day: ${times.join(', ')}");
    });

    return formattedHours.join('\n');
  }
}
