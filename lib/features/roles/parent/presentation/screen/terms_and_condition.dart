import 'package:class_z/core/imports.dart';

class TermsAndCondition extends StatelessWidget {
  const TermsAndCondition({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
          title: "Terms and Condition",
          leading: CustomIconButton(
            icon: Icons.arrow_back_ios,
            onPressed: () {
              NavigatorService.goBack();
            },
          )),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(left: 36.w, right: 35.w, top: 18.h),
          child: SizedBox(
            height: 2800.h,
            width: 359.w,
            child: customtext(
                context: context,
                newYear: AppText.terms,
                font: 17.sp,
                color: Colors.black),
          ),
        ),
      ),
    );
  }
}
