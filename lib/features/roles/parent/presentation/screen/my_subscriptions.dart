import 'package:class_z/core/imports.dart';
import 'package:class_z/core/widgets/custom_credit_card.dart';

class MySubscriptionPage extends StatefulWidget {
  @override
  _MySubscriptionPageState createState() => _MySubscriptionPageState();
}

class _MySubscriptionPageState extends State<MySubscriptionPage> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh data when page becomes visible again
    _loadData();
  }

  void _loadData() {
    context.read<UserBloc>().add(GetCardEvent());
    context.read<SubscriptionBloc>().add(GetSubscriptionPlanEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "My Subscriptions",
        leading: CustomIconButton(
          icon: Icons.arrow_back_ios,
          onPressed: () {
            NavigatorService.goBack();
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: BlocBuilder<SubscriptionBloc, SubscriptionState>(
                builder: (context, state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  customtext(
                    context: context,
                    newYear: "Card",
                    font: 30.sp,
                    weight: FontWeight.w500,
                  ),
                  BlocListener<UserBloc, UserState>(
                    listener: (context, state) {
                      if (state is DeleteCardSuccessState) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Card deleted successfully'),
                            backgroundColor: Colors.green,
                          ),
                        );
                        // Refresh the card state
                        context.read<UserBloc>().add(GetCardEvent());
                      } else if (state is UserErrorState) {
                        if (state.message.contains('delete') ||
                            state.message.contains('card')) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                  'Failed to delete card: ${state.message}'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      }
                    },
                    child: BlocBuilder<UserBloc, UserState>(
                      builder: (context, state) {
                        if (state is UserLoadingState) {
                          return Center(child: CircularProgressIndicator());
                        } else if (state is UserErrorState) {
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            errorState(context: context, error: state.message);
                          });
                          return Center(
                              child:
                                  Text('An error occurred: ${state.message}'));
                        } else if (state is GetCardSuccessState) {
                          // Check if card has valid data (not empty/default)
                          if (state.card.last4 != null &&
                              state.card.last4!.isNotEmpty) {
                            return _cardAdded(
                                context: context, card: state.card);
                          } else {
                            return _cardNotAdded(context: context);
                          }
                        } else if (state is DeleteCardSuccessState) {
                          return _cardNotAdded(context: context);
                        } else {
                          return _cardNotAdded(context: context);
                        }
                      },
                    ),
                  ),
                  SizedBox(height: 32.h),
                  customtext(
                    context: context,
                    newYear: "Membership Plans",
                    font: 30.sp,
                    weight: FontWeight.w500,
                  ),
                  customtext(
                    context: context,
                    newYear: "Zcoins will be released each subscription month",
                    font: 15.sp,
                    weight: FontWeight.w600,
                  ),
                  SizedBox(height: 14.h),
                  BlocBuilder<SubscriptionBloc, SubscriptionState>(
                    builder: (context, state) {
                      if (state is SubscriptionLoadingState) {
                        return Center(child: CircularProgressIndicator());
                      } else if (state is SubscriptionErrorState) {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          errorState(context: context, error: state.error);
                        });
                        return Center(
                            child: Text('An error occurred: ${state.error}'));
                      } else if (state is SubscriptionCancelledState) {
                        return Center(
                          child: Column(
                            children: [
                              SizedBox(height: 50.h),
                              Icon(
                                Icons.check_circle,
                                color: Colors.green,
                                size: 60.h,
                              ),
                              SizedBox(height: 20.h),
                              customtext(
                                context: context,
                                newYear: "You have unsubscribed",
                                font: 24.sp,
                                weight: FontWeight.w600,
                              ),
                              SizedBox(height: 10.h),
                              customtext(
                                context: context,
                                newYear:
                                    "Your subscription has been cancelled.",
                                font: 16.sp,
                                weight: FontWeight.w400,
                              ),
                              customtext(
                                context: context,
                                newYear: "Choose a plan below to resubscribe.",
                                font: 16.sp,
                                weight: FontWeight.w400,
                              ),
                              SizedBox(height: 50.h),
                              Button(
                                buttonText: "View Available Plans",
                                color: AppPallete.secondaryColor,
                                height: 50.h,
                                width: 250.w,
                                onPressed: () {
                                  context
                                      .read<SubscriptionBloc>()
                                      .add(GetSubscriptionPlanEvent());
                                },
                              ),
                            ],
                          ),
                        );
                      } else if (state is GetSubscribeSuccessState) {
                        SubscriptionModel subscriptionPlans =
                            state.subscriptionModel;
                        return ListView.separated(
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: subscriptionPlans.plans?.length ?? 0,
                          separatorBuilder: (context, index) {
                            return SizedBox(
                              height: 7.h,
                            );
                          },
                          itemBuilder: (context, index) {
                            final plan = subscriptionPlans.plans?[index];
                            bool isCurrentPlan =
                                plan?.id == subscriptionPlans.current?.planId;

                            return PricingPlanCard(
                              name: plan?.name ?? "",
                              amount: plan?.amount ?? 0,
                              zCoin: plan?.zCoin ?? 0,
                              interval: plan?.interval ?? "",
                              color: isCurrentPlan
                                  ? AppPallete.currentColor
                                  : Colors.white,
                              isSelected: isCurrentPlan,
                              currentPlan: isCurrentPlan,
                              onTap: () {
                                if (isCurrentPlan) {
                                  NavigatorService.pushNamed(
                                      AppRoutes.unsubscribe);
                                } else {
                                  print(plan.toString());
                                  NavigatorService.pushNamed(AppRoutes.topUp,
                                      arguments: {
                                        'usd': plan?.amount ?? 0,
                                        'zCoin': plan?.zCoin ?? 0,
                                        'discount': 10,
                                        'planId': plan?.id,
                                        'isSubscription': true
                                      });
                                }
                              },
                            );
                          },
                        );
                      } else {
                        return customtext(
                            context: context, newYear: "ASA", font: 11.sp);
                      }
                    },
                  ),
                ],
              );
            })),
      ),
    );
  }

  Widget _cardAdded({required BuildContext context, required CardModel card}) {
    return GestureDetector(
      onTap: () {
        showDialog(
          context: context,
          builder: (BuildContext dialogContext) {
            return AlertDialog(
              title: const Text('Manage Card'),
              content:
                  const Text('You can edit your card details or delete it.'),
              actions: <Widget>[
                TextButton(
                  child: const Text('Edit'),
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                    NavigatorService.pushNamed(AppRoutes.creditCard,
                        arguments: card);
                  },
                ),
                TextButton(
                  child:
                      const Text('Delete', style: TextStyle(color: Colors.red)),
                  onPressed: () {
                    Navigator.of(dialogContext).pop(); // close first dialog
                    showDialog(
                      context: context,
                      builder: (BuildContext confirmContext) {
                        return AlertDialog(
                          title: const Text('Delete Card'),
                          content: const Text(
                              'Are you sure you want to delete this card? This action cannot be undone.'),
                          actions: [
                            TextButton(
                              child: const Text('Cancel'),
                              onPressed: () {
                                Navigator.of(confirmContext).pop();
                              },
                            ),
                            TextButton(
                              child: const Text('Delete',
                                  style: TextStyle(color: Colors.red)),
                              onPressed: () {
                                context.read<UserBloc>().add(DeleteCardEvent());
                                Navigator.of(confirmContext).pop();
                              },
                            ),
                          ],
                        );
                      },
                    );
                  },
                ),
                TextButton(
                  child: const Text('Cancel'),
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                ),
              ],
            );
          },
        );
      },
      child: Padding(
        padding: EdgeInsets.only(top: 26.h, left: 37.w, right: 57.w),
        child: CustomCreditCard(
          brand: card.brand,
          last4: card.last4,
          cardholderName: card.name,
          expiryDate: card.expiryDate,
          showCvc: true,
        ),
      ),
    );
  }

  Widget _cardNotAdded({required BuildContext context}) {
    return GestureDetector(
      onTap: () {
        NavigatorService.pushNamed(AppRoutes.creditCard);
      },
      child: Padding(
        padding: EdgeInsets.only(top: 26.h, left: 37.w, right: 57.w),
        child: Container(
          height: 188.h,
          width: 313.w,
          decoration: BoxDecoration(
            color: AppPallete.darkGrey,
            boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Stack(
            children: [
              Positioned(
                top: 18.h,
                left: 14.w,
                child: Row(
                  children: [
                    Container(
                      height: 40.h,
                      width: 40.h,
                      decoration: BoxDecoration(
                        color: AppPallete.white,
                        borderRadius: BorderRadius.circular(20.r),
                      ),
                      child: Icon(
                        Icons.add,
                        size: 20.h,
                        color: AppPallete.darkGrey,
                      ),
                    ),
                    SizedBox(width: 6.w),
                    customtext(
                      context: context,
                      newYear: "ADD",
                      font: 25.sp,
                      color: Colors.white,
                      weight: FontWeight.w400,
                    ),
                  ],
                ),
              ),
              Positioned(
                bottom: 19.h,
                left: 12.w,
                child: SizedBox(
                  height: 40.h,
                  width: 288.w,
                  child: customtext(
                    context: context,
                    newYear:
                        "Add your card to participate in the community, explore ways to reserve class",
                    font: 15.sp,
                    weight: FontWeight.w400,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
