import 'package:class_z/core/imports.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  int _activeIndex = 0;
  late PageController _pageController;
  UserModel? userData;
  String? imagePath;
  String? fullUrl;
  Color? activeColor;
  DateTime? lastBackPress;

  final List<Widget> _pages = [
    MainPage(),
    Search(),
    MiddleProfileSwitch(),
    TimetableUp(),
    ProgressFeedBackUserDashbboard(),
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _pageController = PageController(
      initialPage: 0,
      keepPage: true,
    );
    _refreshUserData();
    activeColor = AppPallete.secondaryColor;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _refreshUserData();
    }
  }

  void _refreshUserData() {
    setState(() {
      userData = locator<SharedRepository>().getUserData();
      imagePath = userData?.data?.parent?.image?.url;
      fullUrl = imageStringGenerator(imagePath: imagePath ?? '');
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _refreshUserData();
  }

  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _pageController.dispose();
    super.dispose();
  }

  void _onIndexChanged(int index) {
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Future<bool> _onWillPop() async {
    if (_activeIndex != 0) {
      _pageController.animateToPage(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      return false;
    }

    if (lastBackPress == null ||
        DateTime.now().difference(lastBackPress!) >
            const Duration(seconds: 2)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Press back again to exit'),
          duration: Duration(seconds: 2),
        ),
      );
      lastBackPress = DateTime.now();
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthSignInSuccess || state is UserLoaded) {
          _refreshUserData();
        }
      },
      child: WillPopScope(
        onWillPop: _onWillPop,
        child: Scaffold(
          body: PageView(
            controller: _pageController,
            physics:
                const NeverScrollableScrollPhysics(), // Disable swipe to change pages
            children: _pages,
            onPageChanged: (index) {
              setState(() {
                _activeIndex = index;
              });
            },
          ),
          bottomNavigationBar: CustomNavBar(
            selectedIndex: _activeIndex,
            onTabChange: _onIndexChanged,
          ),
        ),
      ),
    );
  }
}
