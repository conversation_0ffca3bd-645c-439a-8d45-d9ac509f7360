import 'package:class_z/core/imports.dart';

class UnsubscribeMessage extends StatelessWidget {
  const UnsubscribeMessage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(
          title: "Unsubscribe",
          leading: customBackButton(),
        ),
        body: Center(
          child: Column(
            children: [
              SizedBox(height: 30.h),
              customtext(
                  context: context,
                  newYear: "Are you sure you want to",
                  font: 22.sp,
                  weight: FontWeight.w600),
              customtext(
                  context: context,
                  newYear: "unsubscribe?",
                  font: 22.sp,
                  weight: FontWeight.w600),
              SizedBox(height: 20.h),
              customtext(
                  context: context,
                  newYear:
                      "Once you unsubscribe, your current plan will be\ndeactivated and you won't be able to book any\nclasses until you resubscribe.",
                  font: 15.sp,
                  weight: FontWeight.w400),
              Sized<PERSON>ox(height: 70.h),
              BlocListener<SubscriptionBloc, SubscriptionState>(
                listener: (context, state) {
                  if (state is SubscriptionCancelledState) {
                    // Subscription successfully cancelled
                    Navigator.of(context).pop();
                    Navigator.of(context).pop(); // Pop unsubscribe page too
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text('You have successfully unsubscribed.')),
                    );
                  } else if (state is SubscriptionErrorState) {
                    // Show error
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Error: ${state.error}')),
                    );
                  }
                },
                child: BlocBuilder<SubscriptionBloc, SubscriptionState>(
                  builder: (context, state) {
                    final isLoading = state is SubscriptionLoadingState;

                    return Button(
                        buttonText: isLoading ? "Cancelling..." : "Unsubcribe",
                        color: AppPallete.greyWord,
                        height: 61.h,
                        shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                        width: 365.w,
                        onPressed: isLoading
                            ? null
                            : () {
                                context
                                    .read<SubscriptionBloc>()
                                    .add(CancelSubscriptionEvent());
                              });
                  },
                ),
              ),
              SizedBox(height: 15.h),
              Button(
                  buttonText: "Cancel",
                  textColorFinal: AppPallete.darkGrey,
                  textSize: 17.sp,
                  fontWeight: FontWeight.w600,
                  color: AppPallete.white,
                  shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                  height: 61.h,
                  width: 365.w,
                  onPressed: () {
                    Navigator.of(context).pop();
                  }),
            ],
          ),
        ));
  }
}
