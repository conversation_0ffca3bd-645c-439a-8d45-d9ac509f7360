import 'package:class_z/core/imports.dart';
import 'package:url_launcher/url_launcher.dart';

class FAQScreen extends StatefulWidget {
  const FAQScreen({super.key});

  @override
  State<FAQScreen> createState() => _FAQScreenState();
}

class _FAQScreenState extends State<FAQScreen> {
  final TextEditingController _searchController = TextEditingController();
  final List<FAQItem> _faqItems = [
    FAQItem(
      question: "How do i change my password?",
      answer:
          "To change your password, proceed to menu and select your profile. Then retype your current password and new password then click confirm.",
    ),
    FAQItem(
      question: "How do i change my profile status?",
      answer:
          "Go to your profile settings, select 'Edit Profile', and you can update your status there.",
    ),
    FAQItem(
      question: "How do i logout of my account?",
      answer:
          "To logout, go to the profile menu and scroll to the bottom. Click the 'Log Out' button to sign out of your account.",
    ),
    FAQItem(
      question: "How do i block an account?",
      answer:
          "Visit the profile of the account you want to block, tap the menu icon, and select 'Block Account'.",
    ),
    FAQItem(
      question: "How do i share my account to other?",
      answer:
          "Go to your profile, tap the share icon, and choose how you'd like to share your profile link.",
    ),
  ];

  List<FAQItem> _filteredFAQs = [];

  @override
  void initState() {
    super.initState();
    _filteredFAQs = List.from(_faqItems);
    _searchController.addListener(_filterFAQs);
  }

  void _filterFAQs() {
    if (_searchController.text.isEmpty) {
      setState(() => _filteredFAQs = List.from(_faqItems));
      return;
    }

    setState(() {
      _filteredFAQs = _faqItems
          .where((faq) => faq.question
              .toLowerCase()
              .contains(_searchController.text.toLowerCase()))
          .toList();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    try {
      if (!await launchUrl(uri)) {
        throw 'Could not launch $url';
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not open website: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        title: Text(
          'FAQ and Support',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
            child: Text(
              "Didn't find the answers you are looking for?\nVisit our website for more information!",
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
                height: 1.5,
              ),
            ),
          ),
          _buildSupportOption(
            icon: Icons.language,
            title: "Go to our website",
            onTap: () => _launchURL('https://classz.com'),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
            child: Container(
              decoration: BoxDecoration(
                color: Color(0xFFF3F0FF),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: AuthField(
                controller: _searchController,
                hintText: "Search for question",
                suffixIcon:
                    Icon(Icons.search, color: AppPallete.greyWord, size: 22),
                height: 50.h,
                border: 12.r,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              itemCount: _filteredFAQs.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: EdgeInsets.only(bottom: 12.h),
                  child: _FAQExpansionTile(faqItem: _filteredFAQs[index]),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSupportOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
        child: Row(
          children: [
            Icon(
              icon,
              color: Colors.grey[600],
              size: 24,
            ),
            SizedBox(width: 20.w),
            Text(
              title,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.black87,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FAQExpansionTile extends StatelessWidget {
  final FAQItem faqItem;

  const _FAQExpansionTile({required this.faqItem});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFF8F7FB),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
          splashColor: Colors.transparent,
          highlightColor: Colors.transparent,
        ),
        child: ExpansionTile(
          tilePadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
          title: Text(
            faqItem.question,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          trailing: Icon(
            Icons.keyboard_arrow_down,
            color: Colors.grey[600],
            size: 24,
          ),
          children: [
            Padding(
              padding: EdgeInsets.only(
                left: 16.w,
                right: 16.w,
                bottom: 16.h,
              ),
              child: Text(
                faqItem.answer,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                  height: 1.5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FAQItem {
  final String question;
  final String answer;

  FAQItem({required this.question, required this.answer});
}
