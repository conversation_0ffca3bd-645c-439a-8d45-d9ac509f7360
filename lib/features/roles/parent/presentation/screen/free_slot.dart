import 'package:class_z/core/imports.dart';


class FreeSlot extends StatelessWidget {
  const FreeSlot({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SizedBox(
          width: 430.w,
          height: 891.h,
          child: <PERSON><PERSON><PERSON>(
            children: [
              _top(context),
              <PERSON><PERSON><PERSON><PERSON>(
                height: 24.h,
              ),
              _oneTwoThree(context),
              <PERSON><PERSON><PERSON><PERSON>(
                height: 26.h,
              ),
              Padding(
                  padding: EdgeInsets.only(left: 28.w, right: 27.w),
                  child: freeSlotList(
                      context: context,
                      imagePath: ImagePath.free1,
                      title: "WaterColour Painting",
                      category: "(intermediate)",
                      coach: "Charlie Own",
                      ageGroup: "12-36",
                      leastAge: "12",
                      time: "45mins")),
              Si<PERSON><PERSON><PERSON>(
                height: 15.h,
              ),
              Padding(
                  padding: EdgeInsets.only(left: 28.w, right: 27.w),
                  child: freeSlotList(
                      context: context,
                      imagePath: ImagePath.free2,
                      title: "WaterColour Painting",
                      category: "(intermediate)",
                      coach: "<PERSON> Own",
                      ageGroup: "12-36",
                      leastAge: "12",
                      time: "45mins")),
              <PERSON><PERSON><PERSON><PERSON>(
                height: 15.h,
              ),
              Padding(
                  padding: EdgeInsets.only(left: 28.w, right: 27.w),
                  child: freeSlotList(
                      context: context,
                      imagePath: ImagePath.free3,
                      title: "WaterColour Painting",
                      category: "(intermediate)",
                      coach: "Charlie Own",
                      ageGroup: "12-36",
                      leastAge: "12",
                      time: "45mins")),
              SizedBox(
                height: 15.h,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Widget _oneTwoThree(BuildContext context) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Padding(
        padding: EdgeInsets.only(left: 36.w),
        child: Row(
          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(20.r),
              child: Container(
                height: 34.h,
                width: 34.w,
                color: AppPallete.secondaryColor,
                child: Center(
                  child: customtext(
                      context: context,
                      newYear: "1",
                      font: 20,
                      color: Colors.white),
                ),
              ),
            ),
            Container(
              width: 128.w,
              height: 1.h,
              color: AppPallete.dividerTime,
            ),
            ClipRRect(
              borderRadius: BorderRadius.circular(20.r),
              child: Container(
                height: 34.h,
                width: 34.w,
                color: AppPallete.dividerTime,
                child: Center(
                  child: customtext(
                    context: context,
                    newYear: "2",
                    font: 20,
                  ),
                ),
              ),
            ),
            Container(
              width: 128.w,
              height: 1.h,
              color: AppPallete.dividerTime,
            ),
            ClipRRect(
              borderRadius: BorderRadius.circular(20.r),
              child: Container(
                height: 34.h,
                width: 34.w,
                color: AppPallete.dividerTime,
                child: Center(
                  child: customtext(
                    context: context,
                    newYear: "3",
                    font: 20,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
      Padding(
        padding: EdgeInsets.only(left: 20.w),
        child: Row(
          children: [
            SizedBox(
              height: 15.h,
              width: 82.w,
              child: customtext(
                  context: context, newYear: "choose class", font: 15.sp),
            ),
            SizedBox(
              width: 80.w,
            ),
            SizedBox(
              height: 15.h,
              width: 82.w,
              child: customtext(
                  context: context, newYear: "send request", font: 15.sp),
            ),
            SizedBox(
              width: 80.w,
            ),
            SizedBox(
              height: 15.h,
              width: 79.w,
              child: customtext(
                  context: context, newYear: "confirmation", font: 15.sp),
            ),
          ],
        ),
      )
    ],
  );
}

Widget _top(BuildContext context) {
  return Padding(
    padding: EdgeInsets.only(top: 34.h),
    child: Row(
      children: [
        Padding(
          padding: EdgeInsets.only(left: 19.w),
          child: CustomIconButton(
            icon: Icons.arrow_back_ios,
            color: Colors.red,
            onPressed: () {},
          ),
        ),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              customtext(
                context: context,
                newYear: "Class of Your Choice",
                font: 20.sp,
                weight: FontWeight.w700,
              ),
              customtext(
                context: context,
                newYear: "choose your interest class",
                font: 20.sp,
                weight: FontWeight.w500,
              ),
            ],
          ),
        ),
        SizedBox(width: 48.w),
      ],
    ),
  );
}

Widget _date(BuildContext context) {
  return Padding(
    padding: EdgeInsets.only(left: 12.w),
    child: Row(
      children: [
        Column(
          children: [
            customtext(
                context: context,
                newYear: "Today",
                font: 15.sp,
                weight: FontWeight.w700),
            customtext(
                context: context,
                newYear: "28/1",
                font: 15.sp,
                weight: FontWeight.w700)
          ],
        ),
        SizedBox(
          width: 40.w,
        ),
        Column(
          children: [
            customtext(
                context: context,
                newYear: "Today",
                font: 15.sp,
                weight: FontWeight.w700),
            customtext(
                context: context,
                newYear: "28/1",
                font: 15.sp,
                weight: FontWeight.w700)
          ],
        ),
        SizedBox(
          width: 40.w,
        ),
        Column(
          children: [
            customtext(
                context: context,
                newYear: "Today",
                font: 15.sp,
                weight: FontWeight.w700),
            customtext(
                context: context,
                newYear: "28/1",
                font: 15.sp,
                weight: FontWeight.w700)
          ],
        ),
        SizedBox(
          width: 40.w,
        ),
        Column(
          children: [
            customtext(
                context: context,
                newYear: "Today",
                font: 15.sp,
                weight: FontWeight.w700),
            customtext(
                context: context,
                newYear: "28/1",
                font: 15.sp,
                weight: FontWeight.w700)
          ],
        ),
        SizedBox(
          width: 40.w,
        ),
        Column(
          children: [
            customtext(
                context: context,
                newYear: "Today",
                font: 15.sp,
                weight: FontWeight.w700),
            customtext(
                context: context,
                newYear: "28/1",
                font: 15.sp,
                weight: FontWeight.w700)
          ],
        ),
        SizedBox(
          width: 40.w,
        ),
        Column(
          children: [
            customtext(
                context: context,
                newYear: "Today",
                font: 15.sp,
                weight: FontWeight.w700),
            customtext(
                context: context,
                newYear: "28/1",
                font: 15.sp,
                weight: FontWeight.w700)
          ],
        ),
      ],
    ),
  );
}

Widget _location(BuildContext context) {
  return Padding(
    padding: EdgeInsets.only(left: 12.w),
    child: SizedBox(
      width: 418.w,
      height: 28.h,
      child: Row(
        children: [
          SizedBox(
            width: 4.w,
          ),
          SizedBox(
            width: 5.h,
          ),
          buildLocation(
              context: context, text: "all", color: AppPallete.lightGreyReal),
          SizedBox(
            width: 5.h,
          ),
          buildLocation(
              context: context,
              text: "Tsim Sha Tsui",
              color: AppPallete.scheduleColor2),
          SizedBox(
            width: 5.h,
          ),
          buildLocation(
              context: context,
              text: "Causeway Bay",
              color: AppPallete.scheduleColor3),
          SizedBox(
            width: 5.h,
          ),
          buildLocation(
              context: context,
              text: "Tseung Kwan O",
              color: AppPallete.scheduleColor4),
          SizedBox(
            width: 5.h,
          ),
          buildLocation(
              context: context,
              text: "Tai Koo",
              color: AppPallete.scheduleColor5),
        ],
      ),
    ),
  );
}
