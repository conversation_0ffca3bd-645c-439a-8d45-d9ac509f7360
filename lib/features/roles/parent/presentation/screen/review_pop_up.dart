import 'package:class_z/core/common/data/models/pending_for_parent.dart';
import 'package:class_z/core/imports.dart';

class ReviewDetailsBottomSheet extends StatefulWidget {
  final PendingReviewNew review;

  const ReviewDetailsBottomSheet({super.key, required this.review});

  @override
  State<ReviewDetailsBottomSheet> createState() =>
      _ReviewDetailsBottomSheetState();
}

class _ReviewDetailsBottomSheetState extends State<ReviewDetailsBottomSheet> {
  int centerRating = 0;
  int coachRating = 0;

  final TextEditingController centerController = TextEditingController();
  final TextEditingController coachController = TextEditingController();

  String? _getCoachDisplayName(dynamic coach) {
    if (coach == null) return null;
    if (coach is String) {
      return 'Coach';
    }
    if (coach is CoachModel) {
      return coach.displayName;
    }
    if (coach is Map<String, dynamic>) {
      return coach['displayName'] as String?;
    }
    return null;
  }

  String? _getCenterDisplayName(dynamic center) {
    if (center == null) return null;
    if (center is String) {
      return 'Center';
    }
    if (center is CenterData) {
      return center.displayName;
    }
    if (center is Map<String, dynamic>) {
      return center['displayName'] as String?;
    }
    return null;
  }

  String? _getCoachId(dynamic coach) {
    if (coach == null) return '';
    if (coach is String) {
      return coach;
    }
    if (coach is CoachModel) {
      return coach.id;
    }
    if (coach is Map<String, dynamic>) {
      return coach['_id'] as String? ?? coach['id'] as String?;
    }
    return '';
  }

  String? _getCenterId(dynamic center) {
    if (center == null) return '';
    if (center is String) {
      return center;
    }
    if (center is CenterData) {
      return center.id;
    }
    if (center is Map<String, dynamic>) {
      return center['_id'] as String? ?? center['id'] as String?;
    }
    return '';
  }

  void setRating({required int rating, required bool isCenter}) {
    setState(() {
      if (isCenter) {
        centerRating = rating;
      } else {
        coachRating = rating;
      }
    });
  }

  @override
  void dispose() {
    centerController.dispose();
    coachController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final review = widget.review;

    return BlocListener<ReviewBloc, ReviewState>(
      listener: (context, state) {
        if (state is ReviewLoadingState)
          loadingState(context: context);
        else
          hideLoadingDialog(context);
        if (state is ReviewErrorState) {
          NavigatorService.goBack();
          errorState(context: context, error: state.message);
        }
        if (state is PostReviewSuccessState) {
          print('in page');
          if (state.review == true) {
            NavigatorService.goBack();
            errorState(context: context, error: 'Review Posted Successfully');
          } else {
            errorState(
                context: context,
                error: 'Failed to post review.please try again');
          }
        }
      },
      child: FractionallySizedBox(
        heightFactor: 723.h / MediaQuery.of(context).size.height,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 40.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: customtext(
                  context: context,
                  newYear: "Rate your Experience",
                  font: 25.sp,
                  weight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 25.h),
              customtext(
                context: context,
                newYear:
                    "How was ${_getCenterDisplayName(review.classId?.center) ?? 'Center'}",
                font: 18.sp,
                weight: FontWeight.w500,
              ),
              SizedBox(height: 15.h),
              buildReviewStars(
                rating: centerRating,
                isCenter: true,
                setRating: setRating,
              ),
              SizedBox(height: 15.h),
              AuthField(
                controller: centerController,
                hintText: 'Write about the center',
                height: 90.h,
                width: double.infinity,
                border: 20.sp,
              ),
              SizedBox(height: 25.h),
              customtext(
                context: context,
                newYear:
                    "How was ${_getCoachDisplayName(review.classId?.coach) ?? 'Coach'}",
                font: 18.sp,
                weight: FontWeight.w500,
              ),
              SizedBox(height: 15.h),
              buildReviewStars(
                rating: coachRating,
                isCenter: false,
                setRating: setRating,
              ),
              SizedBox(height: 15.h),
              AuthField(
                controller: coachController,
                hintText: 'write about the coach',
                height: 90.h,
                width: double.infinity,
                border: 20.sp,
              ),
              SizedBox(height: 25.h),
              Center(
                child: Button(
                  buttonText: "Rate",
                  color: AppPallete.secondaryColor,
                  height: 49.h,
                  width: 249.w,
                  onPressed: () {
                    var combinedPayload = {
                      "centerReview": {
                        "reviewerId": locator<SharedRepository>().getUserId(),
                        "reviewerType": "Parent",
                        "revieweeId":
                            _getCenterId(review.classId?.center) ?? '',
                        "revieweeType": "center",
                        "centerName":
                            _getCenterDisplayName(review.classId?.center) ?? '',
                        "coachName":
                            _getCoachDisplayName(review.classId?.coach) ?? '',
                        "classId": review.classId?.id ?? '',
                        "rating": centerRating,
                        "title": centerController.text,
                      },
                      "coachReview": {
                        "reviewerId": locator<SharedRepository>().getUserId(),
                        "reviewerType": "Parent",
                        "revieweeId": _getCoachId(review.classId?.coach) ?? '',
                        "revieweeType": "coach",
                        "centerName":
                            _getCenterDisplayName(review.classId?.center) ?? '',
                        "coachName":
                            _getCoachDisplayName(review.classId?.coach) ?? '',
                        "classId": review.classId?.id ?? '',
                        "rating": coachRating,
                        "title": coachController.text,
                      }
                    };

                    context
                        .read<ReviewBloc>()
                        .add(PostReviewByParentEvent(payload: combinedPayload));

                    // NavigatorService.goBack();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Widget buildReviewStars({
  required int rating,
  required bool isCenter,
  required Function setRating,
}) {
  return Row(
    children: List.generate(5, (index) {
      return InkWell(
        onTap: () => setRating(rating: index + 1, isCenter: isCenter),
        child: customSvgPicture(
          imagePath: ImagePath.starSvg,
          color: index < rating ? AppPallete.rating : AppPallete.rateGray,
          height: 42.h,
          width: 42.w,
        ),
      );
    }),
  );
}
