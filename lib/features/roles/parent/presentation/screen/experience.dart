import 'package:class_z/core/imports.dart';

class MyExperience extends StatelessWidget {
  final String tier;
  final int exp;
  final double ratio;
  const MyExperience(
      {required this.exp, required this.tier, required this.ratio, super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.only(left: 13.w, right: 13.w),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 82.h, left: 25.w),
                    child: customBackButton(),
                  ),
                  customTopBarOnlyIcon(
                    context: context,
                    badgeCount1: 0,
                    badgeCount2: 0,
                    onTap1: () {
                      NavigatorService.pushNamed(AppRoutes.notification,
                          arguments:
                              locator<SharedRepository>().getParentData()?.id);
                    },
                    onTap2: () {
                      NavigatorService.pushNamed(AppRoutes.centerMessage,
                          arguments: 'user');
                    },
                  ),
                ],
              ),
              SizedBox(
                height: 13.h,
              ),
              Padding(
                padding: EdgeInsets.only(left: 7.w),
                child: customtext(
                    context: context,
                    newYear: "Experience",
                    font: 30.sp,
                    weight: FontWeight.w500),
              ),
              SizedBox(
                height: 25.h,
              ),
              experienceCard(
                  context: context, exp: exp, tier: tier, progress: ratio),
              SizedBox(
                height: 10.h,
              )
            ],
          ),
        ),
      ),
    );
  }
}
