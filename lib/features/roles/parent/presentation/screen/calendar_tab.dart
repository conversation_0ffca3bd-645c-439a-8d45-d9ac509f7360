import 'package:class_z/core/imports.dart';

class CalendarTab extends StatelessWidget {
  final List<EventModel> events;
  final ValueNotifier<int> currentPageIndex;
  final bool isJoinable;
  final ClassModel? classModel;
  final CenterData? center;
  final ValueNotifier<List<EventModel>> selectedEvents;
  final Function(DateTime selectedDay, DateTime focusedDay) onDaySelected;
  final DateTime focusedDay;
  final DateTime? selectedDay;
  final Function(DateTime eventDate, List<EventModel> matchingEvents)
      checkClassNumber;
  final Function(List<EventModel> finalEvents) checkIfEligibleAndSendRequest;

  const CalendarTab({
    Key? key,
    required this.events,
    required this.currentPageIndex,
    required this.isJoinable,
    this.classModel,
    this.center,
    required this.selectedEvents,
    required this.onDaySelected,
    required this.focusedDay,
    this.selectedDay,
    required this.checkClassNumber,
    required this.checkIfEligibleAndSendRequest,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: currentPageIndex,
      builder: (context, value, child) {
        final currentClassDate = classModel?.dates![currentPageIndex.value];
        final matchingEvents = events
            .where((eventDetail) => eventDetail.dateId == currentClassDate?.id)
            .toList();

        final isJoinable = checkIfEligibleAndSendRequest(matchingEvents);

        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomCalendar(
                focusedDay: focusedDay,
                selectedDay: selectedDay,
                onDaySelected: onDaySelected,
                eventDates: [],
              ),
              SizedBox(height: 22.h),
              customDivider(width: 406.w, padding: 12.w),
              SizedBox(height: 22.h),
              ValueListenableBuilder<List<EventModel>>(
                valueListenable: selectedEvents,
                builder: (context, value, _) {
                  print('my event $value');
                  return ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.zero,
                    separatorBuilder: (context, index) {
                      return SizedBox(
                        height: 18.h,
                      );
                    },
                    itemBuilder: (context, index) {
                      print('events of a ${matchingEvents[0].date}');
                      final event = value[index];
                      DateTime eventDate = event.date!;
                      return schduleTimeSlotCenterCourse(
                        context: context,
                        number: checkClassNumber(eventDate, matchingEvents),
                        dateTime: eventDate,
                        start: event.startTime ?? "",
                        finish: event.endTime ?? "",
                        duration: event.durationMinutes ?? "",
                        isJoinable: isJoinable,
                        onTap: () {
                          NavigatorService.pushNamed(
                            AppRoutes.request,
                            arguments: {
                              'classModel': classModel,
                              'center': center,
                              'eventDetails': matchingEvents,
                            },
                          );
                        },
                      );
                    },
                    itemCount: value.length,
                  );
                },
              ),
              SizedBox(
                height: 10,
              )
            ],
          ),
        );
      },
    );
  }
}
