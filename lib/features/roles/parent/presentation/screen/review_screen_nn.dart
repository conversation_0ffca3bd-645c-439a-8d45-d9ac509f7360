import 'package:class_z/core/common/data/models/pending_for_parent.dart';
import 'package:class_z/core/imports.dart';
import 'package:class_z/core/services/course_progress_service.dart';
import 'package:class_z/core/common/data/models/reviewable_item_model.dart';
import 'package:class_z/features/roles/parent/presentation/screen/review_pop_up.dart';

class MyReviewsScreenNN extends StatefulWidget {
  const MyReviewsScreenNN({super.key});

  @override
  State<MyReviewsScreenNN> createState() => _MyReviewsNNScreenState();
}

class _MyReviewsNNScreenState extends State<MyReviewsScreenNN>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final CourseProgressService _courseProgressService = CourseProgressService();
  List<ReviewableItem> _reviewableItems = [];
  bool _isLoadingUiForBloc = true;
  String? _parentId;
  ReviewResponse _rawPendingModels = ReviewResponse(
      message: '', allReviews: AllReviews(reviews: [], pendingReviewsNew: []));
  bool _isHistoryLoading = false;
  List<ReviewOfChildModel> _reviewHistoryItems = [];
  List<ChildModel> _children = [];
  ChildModel? _selectedChild;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _isHistoryLoading = _tabController.index == 1;
    _loadParentIdAndDispatchFetch();

    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        // Tab is about to change
        print(
            "[MyReviewsScreen] Tab listener: indexIsChanging - new index \${_tabController.index}"); // DEBUG
      } else {
        // Tab has finished changing
        print(
            "[MyReviewsScreen] Tab listener: finished changing to index \${_tabController.index}"); // DEBUG
        print(
            "[MyReviewsScreen] Tab listener: Checking conditions: index == 1 (\${_tabController.index == 1}), historyEmpty (\${_reviewHistoryItems.isEmpty}), !isLoading (\${!_isHistoryLoading}), parentIdNotNull (\${_parentId != null}), parentIdNotEmpty (\${_parentId?.isNotEmpty})"); // DEBUG

        if (_tabController.index == 1 && // History tab is selected
            _reviewHistoryItems.isEmpty && // And history hasn't been loaded
            !_isHistoryLoading && // And not already loading
            _parentId != null &&
            _parentId!.isNotEmpty) {
          print(
              "[MyReviewsScreen] Tab listener: All conditions met. Dispatching GetParentReviewHistoryEvent for parentId: \$_parentId"); // DEBUG
          // context
          //     .read<UserBloc>()
          //     .add(GetParentReviewHistoryEvent(parentId: _parentId!));
        } else {
          print(
              "[MyReviewsScreen] Tab listener: Conditions NOT met for dispatching GetParentReviewHistoryEvent."); // DEBUG
          if (_tabController.index != 1) print("  - Reason: Not history tab");
          if (_reviewHistoryItems.isNotEmpty)
            print(
                "  - Reason: History items not empty (\${_reviewHistoryItems.length})");
          if (_isHistoryLoading) print("  - Reason: Already loading history");
          if (_parentId == null) print("  - Reason: parentId is null");
          if (_parentId != null && _parentId!.isEmpty)
            print("  - Reason: parentId is empty");
        }
      }
    });

    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   print(
    //       "[MyReviewsScreen] PostFrameCallback: Current tab index \${_tabController.index}"); // DEBUG
    //   print(
    //       "[MyReviewsScreen] PostFrameCallback: Checking conditions: index == 1 (\${_tabController.index == 1}), historyEmpty (\${_reviewHistoryItems.isEmpty}), !isLoading (\${!_isHistoryLoading}), parentIdNotNull (\${_parentId != null}), parentIdNotEmpty (\${_parentId?.isNotEmpty})"); // DEBUG
    //   if (_tabController.index == 1 &&
    //       _reviewHistoryItems.isEmpty &&
    //       !_isHistoryLoading &&
    //       _parentId != null &&
    //       _parentId!.isNotEmpty) {
    //     context
    //         .read<UserBloc>()
    //         .add(GetParentReviewHistoryEvent(parentId: _parentId!));
    //   }
    // });
  }

  Future<void> _loadParentIdAndDispatchFetch() async {
    final parentData = locator<SharedRepository>().getParentData();
    _parentId = parentData?.id;
    if (_parentId != null && _parentId!.isNotEmpty) {
      context
          .read<UserBloc>()
          .add(GetChildByParentIdEvent(parentId: _parentId!));

      context
          .read<UserBloc>()
          .add(GetParentPendingReviewsEvent(parentId: _parentId!));

      if (_tabController.index == 1) {
        if (_reviewHistoryItems.isEmpty) {
          setState(() {
            _isHistoryLoading = true;
          });
          // context
          //     .read<UserBloc>()
          //     .add(GetParentReviewHistoryEvent(parentId: _parentId!));
        } else {
          setState(() {
            _isHistoryLoading = false;
          });
        }
      } else {
        setState(() {
          _isHistoryLoading = false;
        });
      }
    } else {
      if (mounted) {
        setState(() {
          _isLoadingUiForBloc = false;
          _isHistoryLoading = false;
        });
      }
    }
  }

  // Future<void> _processPendingModelsFromBloc(
  //     ReviewResponse pendingModels) async {
  //   if (!mounted || _parentId == null) return;
  //   setState(() {
  //     _isLoadingUiForBloc = true;
  //   });

  //   try {
  //     final items = await _courseProgressService.getReviewableItemsForParent(
  //       pendingModels: pendingModels,
  //       userId: _parentId!,
  //     );
  //     if (mounted) {
  //       setState(() {
  //         _reviewableItems = items;
  //         _isLoadingUiForBloc = false;
  //       });
  //     }
  //   } catch (e) {
  //     if (mounted) {
  //       setState(() {
  //         _isLoadingUiForBloc = false;
  //       });
  //     }
  //     print("Error processing reviewable items: $e");
  //   }
  // }

  @override
  void dispose() {
    _tabController.removeListener(
        () {}); // It's good practice to remove listener if added directly like this, though dispose of controller handles it.
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: CustomIconButton(
          icon: Icons.arrow_back_ios,
          onPressed: () {
            NavigatorService.goBack();
          },
        ),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            customtext(
                context: context,
                newYear: 'My Reviews',
                font: 20.sp,
                weight: FontWeight.w600),
          ],
        ),
        actions: [
          Padding(
            padding: EdgeInsets.only(right: 8.w),
            child: NotificationBadge(
              icon: Icons.notifications_outlined,
              badgeCount: 0,
              iconColor: AppPallete.darkGrey,
              onTap: () {
                if (_parentId != null && _parentId!.isNotEmpty) {
                  NavigatorService.pushNamed(AppRoutes.notification,
                      arguments: _parentId);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text(
                            'Could not determine user for notifications.')),
                  );
                }
              },
            ),
          ),
          Padding(
            padding: EdgeInsets.only(right: 16.w),
            child: NotificationBadge(
              icon: Icons.messenger_outline_sharp,
              badgeCount: 0,
              iconColor: AppPallete.darkGrey,
              onTap: () {
                NavigatorService.pushNamed(AppRoutes.centerMessage,
                    arguments: 'user');
              },
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppPallete.secondaryColor,
          unselectedLabelColor: AppPallete.darkGrey,
          indicatorColor: AppPallete.secondaryColor,
          tabs: const [
            Tab(text: 'To Review'),
            Tab(text: 'History'),
          ],
        ),
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<UserBloc, UserState>(
            listener: (context, state) {
              if (state is UserLoadingState && _parentId != null) {
                // This is a general loading state, might want to be more specific
                // if needed, or rely on specific loading states like ParentReviewHistoryLoadingState.
              } else if (state is ParentPendingReviewsSuccessState) {
                setState(() {
                  _rawPendingModels = state.pendingReviews;
                  // _processPendingModelsFromBloc(
                  //     _rawPendingModels); // This updates _reviewableItems and _isLoadingUiForBloc
                });
              } else if (state is UserErrorState && _parentId != null) {
                if (mounted) {
                  setState(() {
                    _isLoadingUiForBloc = false;
                    _reviewableItems = [];
                    // Also ensure history loading is stopped on a general user error if it was active
                    _isHistoryLoading = false;
                  });
                }
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      content: Text(
                          "Error fetching pending reviews: ${state.message}")),
                );
              }

              // Handle Get Children States
              if (state is GetChildByParentIdSuccessState) {
                if (mounted) {
                  setState(() {
                    _children = state.child;
                    // Optionally, set the first child as selected by default
                    if (_children.isNotEmpty) {
                      _selectedChild = _children[0];
                    } else {
                      _selectedChild = null;
                    }
                    // After fetching children, you might want to re-filter or re-fetch reviews
                    // if the lists depend on the selected child and are already populated.
                    // For now, just updating the children list.
                  });
                }
              } else if (state is UserErrorState &&
                  state.message.contains("children")) {
                // Be more specific if possible
                // Handle error fetching children, maybe show a message
                print(
                    "[MyReviewsScreen] Error fetching children: ${state.message}");
              }

              // Handle Review History States
              if (state is ParentReviewHistoryLoadingState) {
                print(
                    "[MyReviewsScreen] BlocListener: ParentReviewHistoryLoadingState received"); // DEBUG
                if (mounted) {
                  setState(() {
                    _isHistoryLoading = true;
                  });
                }
              } else if (state is ParentReviewHistorySuccessState) {
                print(
                    "[MyReviewsScreen] BlocListener: ParentReviewHistorySuccessState received with ${state.history.length} items"); // DEBUG
                if (mounted) {
                  setState(() {
                    _reviewHistoryItems = state.history;
                    _isHistoryLoading = false;
                  });
                }
              } else if (state is ParentReviewHistoryErrorState) {
                print(
                    "[MyReviewsScreen] BlocListener: ParentReviewHistoryErrorState received: ${state.message}"); // DEBUG
                if (mounted) {
                  setState(() {
                    _isHistoryLoading = false;
                    _reviewHistoryItems = []; // Clear history on error
                  });
                }
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      content: Text(
                          "Error fetching review history: ${state.message}")),
                );
              }
            },
          ),
          BlocListener<ReviewBloc, ReviewState>(
            listener: (context, state) {
              if (state is PostReviewSuccessState) {
                print("[MyReviewsScreen] Review submitted successfully");
                // Refresh pending reviews to remove the submitted one
                if (_parentId != null) {
                  context
                      .read<UserBloc>()
                      .add(GetParentPendingReviewsEvent(parentId: _parentId!));

                  // Also refresh review history if we're on that tab
                  if (_tabController.index == 1) {
                    context.read<UserBloc>().add(GetParentReviewHistoryEvent(
                        parentId: _parentId!, childId: _selectedChild?.id));
                  }
                }
              } else if (state is ReviewErrorState) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      content:
                          Text("Error submitting review: ${state.message}")),
                );
              }
            },
          ),
        ],
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildToReviewTab(context),
            _buildHistoryTab(context),
          ],
        ),
      ),
    );
  }

  String? _getCoachDisplayName(dynamic coach) {
    if (coach == null) return null;
    if (coach is String) {
      // Coach is just an ID string, we don't have the display name
      return 'Coach';
    }
    if (coach is CoachModel) {
      return coach.displayName;
    }
    // If it's a Map (raw JSON), try to extract displayName
    if (coach is Map<String, dynamic>) {
      return coach['displayName'] as String?;
    }
    return null;
  }

  String? _getCenterDisplayName(dynamic center) {
    if (center == null) return null;
    if (center is String) {
      // Center is just an ID string, we don't have the display name
      return 'Center';
    }
    if (center is CenterData) {
      return center.displayName;
    }
    // If it's a Map (raw JSON), try to extract displayName
    if (center is Map<String, dynamic>) {
      return center['displayName'] as String?;
    }
    return null;
  }

  Widget _buildToReviewTab(BuildContext context) {
    // if (_isLoadingUiForBloc) {
    //   return const Center(child: CircularProgressIndicator());
    // }

    List<PendingReviewNew> _reviews =
        _rawPendingModels.allReviews?.pendingReviewsNew ?? [];

    if (_reviews.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.rate_review_outlined,
                size: 100.h, color: AppPallete.greyWord),
            SizedBox(height: 20.h),
            customtext(
              context: context,
              newYear: "You don't have any pending reviews",
              font: 16.sp,
              color: AppPallete.darkGrey,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _reviews.length,
      itemBuilder: (context, index) {
        final item = _reviews[index];

        return Card(
          margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          child: Padding(
            padding: EdgeInsets.all(12.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                customtext(
                  context: context,
                  newYear: item.classId?.classProviding ?? 'Unnamed Class',
                  font: 18.sp,
                  weight: FontWeight.bold,
                ),
                SizedBox(height: 4.h),
                RichText(
                    text: TextSpan(children: [
                  customSpanText(
                      text: 'Center Name: ',
                      fontSize: 15.sp,
                      color: AppPallete.secondaryColor,
                      fontWeight: FontWeight.w500),
                  customSpanText(
                      text: _getCenterDisplayName(item.classId?.center) ??
                          'Unknown Center')
                ])),
                SizedBox(height: 4.h),
                RichText(
                    text: TextSpan(children: [
                  customSpanText(
                      text: 'Coach Name: ',
                      fontSize: 15.sp,
                      color: AppPallete.secondaryColor,
                      fontWeight: FontWeight.w500),
                  customSpanText(
                      text: _getCoachDisplayName(item.classId?.coach) ??
                          'Unknown Coach')
                ])),
                Padding(
                  padding: EdgeInsets.only(bottom: 4.h),
                  child: customtext(
                    context: context,
                    newYear:
                        "For: ${item.childId?.fullname ?? 'Unknown Child'}",
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.secondaryColor,
                  ),
                ),
                customtext(
                  context: context,
                  newYear:
                      "Order Date: ${dateGenerator(date: item.createdAt, format: 'dd/MM/yyyy')}",
                  font: 14.sp,
                  color: AppPallete.darkGrey,
                ),
                SizedBox(height: 4.h),
                SizedBox(height: 12.h),
                Align(
                  alignment: Alignment.centerRight,
                  child: ElevatedButton(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(10.r),
                            topRight: Radius.circular(10.r),
                          ),
                        ),
                        builder: (context) =>
                            ReviewDetailsBottomSheet(review: item),
                      );
                    },
                    child: customtext(
                        context: context,
                        newYear: 'Review Now',
                        font: 14.sp,
                        color: Colors.white),
                    style: ElevatedButton.styleFrom(
                        backgroundColor: AppPallete.secondaryColor),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // // Add this method to show review details
  // void _showReviewDetailsBottomSheet(BuildContext context, Review review) {
  //   showModalBottomSheet(
  //     context: context,
  //     isScrollControlled: true,
  //     shape: RoundedRectangleBorder(
  //       borderRadius: BorderRadius.only(
  //         topLeft: Radius.circular(20.r),
  //         topRight: Radius.circular(20.r),
  //       ),
  //     ),
  //     builder: (BuildContext context) {
  //       return Container(
  //         padding: EdgeInsets.all(20.h),
  //         constraints: BoxConstraints(
  //           maxHeight: MediaQuery.of(context).size.height * 0.7,
  //         ),
  //         child: SingleChildScrollView(
  //           child: Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             mainAxisSize: MainAxisSize.min,
  //             children: [
  //               // Header with class name
  //               Center(
  //                 child: customtext(
  //                   context: context,
  //                   newYear: review.classId.classProviding ?? 'Unknown Class',
  //                   font: 22.sp,
  //                   weight: FontWeight.bold,
  //                 ),
  //               ),
  //               SizedBox(height: 20.h),

  //               Row(
  //                 children: [
  //                   Icon(
  //                     Icons.person,
  //                     color: AppPallete.secondaryColor,
  //                     size: 24.h,
  //                   ),
  //                   SizedBox(width: 10.w),
  //                   Expanded(
  //                     child: customtext(
  //                       context: context,
  //                       newYear:
  //                           "Coach: ${review.classId.coach.displayName ?? 'Unknown Coach'}",
  //                       font: 18.sp,
  //                       weight: FontWeight.w500,
  //                       color: AppPallete.secondaryColor,
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //               SizedBox(height: 15.h),

  //               // Reviewed item - Center section

  //               Row(
  //                 children: [
  //                   Icon(
  //                     Icons.business,
  //                     color: AppPallete.secondaryColor,
  //                     size: 24.h,
  //                   ),
  //                   SizedBox(width: 10.w),
  //                   Expanded(
  //                     child: customtext(
  //                       context: context,
  //                       newYear:
  //                           "Center: ${review.classId.center.displayName ?? 'Unknown Center'}",
  //                       font: 18.sp,
  //                       weight: FontWeight.w500,
  //                       color: AppPallete.secondaryColor,
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //               SizedBox(height: 15.h),

  //               // Child section (if this is a child review)

  //               Row(
  //                 children: [
  //                   Icon(
  //                     Icons.child_care,
  //                     color: AppPallete.secondaryColor,
  //                     size: 24.h,
  //                   ),
  //                   SizedBox(width: 10.w),
  //                   Expanded(
  //                     child: customtext(
  //                       context: context,
  //                       newYear:
  //                           "For: ${review.childId.fullname ?? 'Unknown Child'}",
  //                       font: 18.sp,
  //                       weight: FontWeight.w500,
  //                       color: AppPallete.secondaryColor,
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //               SizedBox(height: 15.h),

  //               // Review date

  //               Row(
  //                 children: [
  //                   Icon(Icons.calendar_today,
  //                       color: AppPallete.darkGrey, size: 20.h),
  //                   SizedBox(width: 10.w),
  //                   customtext(
  //                     context: context,
  //                     newYear:
  //                         "Reviewed on: ${dateGenerator(date: review.createdAt, format: 'dd/MM/yyyy')}",
  //                     font: 16.sp,
  //                     color: AppPallete.darkGrey,
  //                   ),
  //                 ],
  //               ),
  //               SizedBox(height: 15.h),

  //               // Rating with stars
  //               Row(
  //                 children: [
  //                   Icon(Icons.star, color: AppPallete.rating, size: 20.h),
  //                   SizedBox(width: 10.w),
  //                   customtext(
  //                     context: context,
  //                     newYear:
  //                         "Rating: {review.rating?.toStringAsFixed(1) ?? 'N/A'} / 10",
  //                     font: 16.sp,
  //                     color: AppPallete.darkGrey,
  //                     weight: FontWeight.w500,
  //                   ),
  //                 ],
  //               ),
  //               SizedBox(height: 10.h),

  //               Row(
  //                 children: List.generate(
  //                   5,
  //                   (index) => Padding(
  //                     padding: EdgeInsets.only(right: 5.w),
  //                     child: Icon(
  //                       Icons.star,
  //                       color:
  //                           index < 5 ? AppPallete.rating : AppPallete.rateGray,
  //                       size: 30.h,
  //                     ),
  //                   ),
  //                 ),
  //               ),
  //               SizedBox(height: 20.h),

  //               // Comment section

  //               customtext(
  //                 context: context,
  //                 newYear: "Comment:",
  //                 font: 16.sp,
  //                 weight: FontWeight.w500,
  //                 color: AppPallete.darkGrey,
  //               ),
  //               SizedBox(height: 5.h),
  //               Container(
  //                 padding: EdgeInsets.all(10.h),
  //                 decoration: BoxDecoration(
  //                   color: Colors.grey[100],
  //                   borderRadius: BorderRadius.circular(10.r),
  //                 ),
  //                 child: customtext(
  //                   context: context,
  //                   newYear: 'review.comment!',
  //                   font: 16.sp,
  //                   color: AppPallete.darkGrey,
  //                 ),
  //               ),
  //               SizedBox(height: 20.h),

  //               // Topic if available

  //               customtext(
  //                 context: context,
  //                 newYear: "Topic:",
  //                 font: 16.sp,
  //                 weight: FontWeight.w500,
  //                 color: AppPallete.darkGrey,
  //               ),
  //               SizedBox(height: 5.h),
  //               customtext(
  //                 context: context,
  //                 newYear: 'review.topic!',
  //                 font: 16.sp,
  //                 color: AppPallete.darkGrey,
  //               ),
  //               SizedBox(height: 20.h),

  //               // Close button
  //               SizedBox(height: 30.h),
  //               Center(
  //                 child: ElevatedButton(
  //                   onPressed: () => Navigator.of(context).pop(),
  //                   style: ElevatedButton.styleFrom(
  //                     backgroundColor: AppPallete.secondaryColor,
  //                     padding: EdgeInsets.symmetric(
  //                         horizontal: 30.w, vertical: 12.h),
  //                     shape: RoundedRectangleBorder(
  //                       borderRadius: BorderRadius.circular(30.r),
  //                     ),
  //                   ),
  //                   child: customtext(
  //                     context: context,
  //                     newYear: "Close",
  //                     font: 16.sp,
  //                     color: Colors.white,
  //                   ),
  //                 ),
  //               ),
  //               SizedBox(height: 10.h),
  //             ],
  //           ),
  //         ),
  //       );
  //     },
  //   );
  // }

  // Helper method to build question rating display
  Widget _buildQuestionRating(
      BuildContext context, String questionTitle, double rating) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          customtext(
            context: context,
            newYear: questionTitle,
            font: 14.sp,
            color: AppPallete.darkGrey,
          ),
          Row(
            children: List.generate(
              5,
              (index) => Icon(
                Icons.star,
                color: index < (rating / 2)
                    ? AppPallete.rating
                    : AppPallete.rateGray,
                size: 18.h,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Also add logging to the history tab to debug the data
  Widget _buildHistoryTab(BuildContext context) {
    List<ReviewNew> _reviews = _rawPendingModels.allReviews?.reviews ?? [];

    if (_reviews.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history_toggle_off_outlined,
                size: 100.h, color: AppPallete.greyWord),
            SizedBox(height: 20.h),
            customtext(
              context: context,
              newYear: "You haven\'t reviewed any classes yet",
              font: 16.sp,
              color: AppPallete.darkGrey,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _reviews.length,
      itemBuilder: (context, index) {
        final review = _reviews[index];

        // Log individual review data for debugging
        print(
            "[MyReviewsScreen] Review $index: ${review.revieweeType} - ${review.revieweeId}");

        // Determine what was reviewed (class, coach, center)
        String reviewedItemName = '';
        if (review.revieweeType == 'coach' && review.coachName != null) {
          reviewedItemName = "Coach: ${review.coachName}";
        } else if (review.revieweeType == 'center' &&
            review.centerName != null) {
          reviewedItemName = "Center: ${review.centerName}";
        } else if (review.revieweeType == 'child') {
          // Find the child's name for display
          final childName = _children
              .firstWhere((child) => child.id == review.revieweeId,
                  orElse: () => ChildModel(fullname: "Unknown Child"))
              .fullname;
          reviewedItemName = "Child: $childName";
        }

        // Get class name if available
        String className = review.classId?.classProviding ?? 'Unknown Class';

        return Card(
          margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          child: InkWell(
            onTap: () {},
            borderRadius: BorderRadius.circular(4.r),
            child: Padding(
              padding: EdgeInsets.all(12.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Display class name
                  customtext(
                    context: context,
                    newYear: className,
                    font: 18.sp,
                    weight: FontWeight.bold,
                  ),
                  SizedBox(height: 4.h),
                  customtext(
                    context: context,
                    newYear:
                        "child name: ${review.childId?.fullname ?? 'Unknown Child'}",
                    font: 15.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(height: 4.h),

                  // Display what was reviewed
                  if (reviewedItemName.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(bottom: 4.h),
                      child: customtext(
                        context: context,
                        newYear: reviewedItemName,
                        font: 15.sp,
                        weight: FontWeight.w500,
                        color: AppPallete.secondaryColor,
                      ),
                    ),

                  // Display review date
                  if (review.date != null)
                    customtext(
                      context: context,
                      newYear:
                          "Reviewed on: ${DateFormat('yyyy-MM-dd').format(review.date!)}",
                      font: 14.sp,
                      color: AppPallete.darkGrey,
                    ),
                  SizedBox(height: 4.h),

                  // Display rating
                  customtext(
                    context: context,
                    newYear:
                        "Rating: ${review.rating?.toStringAsFixed(1) ?? 'N/A'} / 5",
                    font: 14.sp,
                    color: AppPallete.darkGrey,
                  ),

                  Padding(
                    padding: EdgeInsets.only(top: 4.h),
                    child: customtext(
                      context: context,
                      newYear: "Comment: ${review.title ?? 'No information'}",
                      font: 14.sp,
                      color: AppPallete.darkGrey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
