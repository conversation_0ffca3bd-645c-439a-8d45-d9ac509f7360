import 'package:class_z/core/imports.dart';

class OrdersDetailsConTimetable extends StatefulWidget {
  final GetOrderByUserModel order;
  const OrdersDetailsConTimetable({required this.order, super.key});

  @override
  State<OrdersDetailsConTimetable> createState() =>
      _OrdersDetailsConTimetableState();
}

class _OrdersDetailsConTimetableState extends State<OrdersDetailsConTimetable> {
  QrCodeModel? code;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(title: "My Class", leading: customBackButton()),
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildProgressSteps(
                  context: context,
                  cont1: AppPallete.secondaryColor,
                  cont2: AppPallete.secondaryColor,
                  cont3: AppPallete.secondaryColor),
              SizedBox(
                height: 15.h,
              ),
              _prepare(context: context)
            ],
          ),
        ));
  }

  Widget _prepare({required BuildContext context}) {
    ParentData1? parent = locator<SharedRepository>().getParentData();
    return Padding(
      padding: EdgeInsets.only(left: 23.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          customtext(
              context: context,
              newYear: "Prepare your class code and notify the coach at start",
              font: 15.sp,
              weight: FontWeight.w600),
          SizedBox(
            height: 20.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 4.w),
            child: InkWell(
              onTap: () {
                NavigatorService.pushNamed(AppRoutes.qrcode,
                    arguments: widget.order);
              },
              child: Container(
                height: 40.h,
                width: 158.w,
                decoration: BoxDecoration(
                    color: AppPallete.red,
                    borderRadius: BorderRadius.circular(10.r)),
                child: Center(
                  child: customtext(
                      context: context,
                      newYear: "Verification code",
                      font: 15.sp,
                      color: Colors.white,
                      weight: FontWeight.w700),
                ),
              ),
            ),
          ),
          SizedBox(
            height: 20.h,
          ),
          Padding(
              padding: EdgeInsets.only(left: 2.w),
              child: timeTableCardFull(
                  context: context,
                  user: widget.order.child?.fullname ?? '',
                  confirmed: true,
                  date: dateGenerator(
                      date: widget.order.date, format: 'dd/MM/yyyy'),
                  location: widget.order.classs?.center?.displayName,
                  course: widget.order.classs?.classProviding ?? '',
                  time:
                      "${widget.order.order?.startTime} - ${widget.order.order?.endTime}",
                  classTime: widget.order.order?.durationMinutes ?? '',
                  special: widget.order.order?.sen == true
                      ? "Special note: SEN service"
                      : "",
                  coach: "by ${widget.order.classs?.coach?.displayName}",
                  participantName: widget.order.child?.fullname ?? '',
                  contact: parent?.phone ?? '',
                  email: parent?.email ?? '',
                  coachingAddress: widget.order.classs?.address == 'center'
                      ? addressGenerator(
                          address: widget.order.classs?.center?.address)
                      : widget.order.classs?.address ?? '')),
          SizedBox(
            height: 20.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 3.5.w),
            child: customtext(
                context: context,
                newYear:
                    "Please arrive the coaching address on time to attend the lesson",
                font: 13.sp,
                weight: FontWeight.w300),
          ),
          SizedBox(
            height: 10.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 3.5.w),
            child: customtext(
                context: context,
                newYear: "No compensation time will be granted upon lateness",
                font: 13.sp,
                weight: FontWeight.w300),
          ),
          SizedBox(
            height: 10.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 3.5.w),
            child: customtext(
                context: context,
                newYear: "No-show may be subject to a service charge",
                font: 13.sp,
                weight: FontWeight.w300),
          ),
          SizedBox(
            height: 20.h,
          ),
          // Calculate real pricing data from order
          Builder(
            builder: (context) {
              // Get real data from the order
              int actualDiscount = widget.order.order?.discount ?? 0;
              int actualAmount = widget.order.order?.amount ??
                  widget.order.classs?.charge ??
                  0;
              int actualQuantity = widget.order.order?.quantity ?? 1;
              int actualSubtotal = actualAmount * actualQuantity;

              return CustomBill(
                discount: actualDiscount,
                subtotal: actualSubtotal,
                rows: [
                  BillRow.fromCells(cells: [
                    DataCell(customtext(
                        context: context,
                        newYear:
                            "${widget.order.classs?.classProviding} (${widget.order.classs?.level})",
                        font: 13.sp,
                        weight: FontWeight.w500)),
                    DataCell(Center(
                        child: customtext(
                            context: context,
                            newYear: actualQuantity.toString(),
                            font: 13.sp,
                            weight: FontWeight.w500))),
                    DataCell(Center(
                        child: customtext(
                            context: context,
                            newYear: actualAmount > 0
                                ? actualAmount.toString()
                                : 'Free',
                            font: 13.sp,
                            weight: FontWeight.w500))),
                    DataCell(Align(
                        alignment: Alignment.centerRight,
                        child: customtext(
                            context: context,
                            newYear: actualSubtotal > 0
                                ? actualSubtotal.toString()
                                : 'Free',
                            font: 13.sp,
                            weight: FontWeight.w500))),
                  ]),
                ],
              );
            },
          ),
          // Add bottom padding to prevent cropping
          SizedBox(
            height: 40.h,
          ),
        ],
      ),
    );
  }
}
