import 'package:class_z/core/imports.dart';

import 'package:class_z/core/common/presentation/blocs/savedCenterBloc/saved_center_bloc.dart';

class SavedCentersScreen extends StatefulWidget {
  const SavedCentersScreen({Key? key}) : super(key: key);

  @override
  _SavedCentersScreenState createState() => _SavedCentersScreenState();
}

class _SavedCentersScreenState extends State<SavedCentersScreen> {
  // Flag to prevent infinite checking of saved centers
  bool _hasCentersBeenChecked = false;
  List<CenterData>? _cachedCenters;

  @override
  void initState() {
    super.initState();
    _fetchSavedCenters();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Check if the bloc already has loaded data
    final currentState = context.read<SavedCenterBloc>().state;
    if (currentState is SavedCentersLoaded && !_hasCentersBeenChecked) {
      print(
          'Found existing loaded state with ${currentState.centers.length} centers');
      _cachedCenters = currentState.centers;
      _hasCentersBeenChecked = true;
    }
  }

  void _fetchSavedCenters() {
    if (_hasCentersBeenChecked) return;

    print('Fetching saved centers');
    context.read<SavedCenterBloc>().add(GetSavedCentersEvent());

    // Mark centers as checked to prevent infinite loop
    _hasCentersBeenChecked = true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "Saved Centres",
        leading: customBackButton(),
      ),
      //   AppBar(
      //   title: Text('Saved Centres'),
      //   backgroundColor: Colors.white,
      //   foregroundColor: Colors.black,
      //   elevation: 0,
      // ),
      body: BlocConsumer<SavedCenterBloc, SavedCenterState>(
        listener: (context, state) {
          if (state is CenterUnsavedSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Centre removed from saved')),
            );

            // Refresh the list after removing a center
            _hasCentersBeenChecked = false;
            _cachedCenters = null;
            _fetchSavedCenters();
          } else if (state is SavedCenterError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          }
        },
        builder: (context, state) {
          print(
              'SavedCentersScreen BlocConsumer builder called with state: ${state.runtimeType}');

          // If we have cached centers and current state is loading, show cached data
          if (state is SavedCenterLoading && _cachedCenters != null) {
            print('Using cached centers while loading');
            return _buildCentersList(_cachedCenters!);
          }

          if (state is SavedCenterLoading) {
            print('Showing loading indicator');
            return Center(child: CircularProgressIndicator());
          } else if (state is SavedCentersLoaded) {
            final centers = state.centers;
            print('SavedCentersLoaded state with ${centers.length} centers');
            // Cache the loaded centers
            _cachedCenters = centers;

            if (centers.isEmpty) {
              return _buildEmptyState();
            }
            return _buildCentersList(centers);
          } else if (state is SavedCenterError) {
            print('SavedCenterError state: ${state.message}');
            // If we have cached data, show it with error message
            if (_cachedCenters != null && _cachedCenters!.isNotEmpty) {
              return Column(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    color: Colors.red.shade100,
                    child: Row(
                      children: [
                        Icon(Icons.error_outline, color: Colors.red),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Failed to refresh. Showing cached data.',
                            style: TextStyle(color: Colors.red.shade800),
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            _hasCentersBeenChecked = false;
                            _cachedCenters = null;
                            _fetchSavedCenters();
                          },
                          child: Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                  Expanded(child: _buildCentersList(_cachedCenters!)),
                ],
              );
            }

            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Error loading saved centres',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    state.message,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      // Reset the flag and try fetching again
                      _hasCentersBeenChecked = false;
                      _cachedCenters = null;
                      _fetchSavedCenters();
                    },
                    child: Text('Try Again'),
                  ),
                ],
              ),
            );
          } else {
            // Initial state - check if we have cached data
            if (_cachedCenters != null && _cachedCenters!.isNotEmpty) {
              print('Showing cached centers for initial state');
              return _buildCentersList(_cachedCenters!);
            }
            // Initial state or any other state
            print(
                'Showing loading indicator for initial/unknown state: ${state.runtimeType}');
            return Center(child: CircularProgressIndicator());
          }
        },
      ),
    );
  }

  Widget _buildTopBar() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 82.h, left: 19.w),
              child: customBackButton(),
            ),
            customTopBarOnlyIcon(
              context: context,
              badgeCount1: 0,
              badgeCount2: 0,
              onTap1: () {
                NavigatorService.pushNamed(AppRoutes.notification,
                    arguments: locator<SharedRepository>().getParentData()?.id);
              },
              onTap2: () {
                NavigatorService.pushNamed(AppRoutes.centerMessage,
                    arguments: 'user');
              },
            ),
          ],
        ),
        SizedBox(height: 6.h),
        Padding(
          padding: EdgeInsets.only(left: 19.w),
          child: Align(
            alignment: Alignment.centerLeft,
            child: customtext(
              context: context,
              newYear: "Saved Centres",
              font: 30.sp,
              weight: FontWeight.w500,
            ),
          ),
        ),
        SizedBox(height: 21.h),
        customDivider(width: 430.w, padding: 0),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'No Saved Centres',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Start saving your favourite centres',
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              NavigatorService.pushNamed(AppRoutes.search);
            },
            child: Text('Explore Centres'),
          ),
        ],
      ),
    );
  }

  Widget _buildCentersList(List<CenterData> centers) {
    return ListView.separated(
      padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 16.h),
      itemCount: centers.length,
      separatorBuilder: (context, index) => SizedBox(height: 20.h),
      itemBuilder: (context, index) {
        final center = centers[index];
        return buildCenterRowGallery(
          context: context,
          center: center,
          isSaved: true, // Centers are saved by definition in this screen
          onFavoriteToggle: (newValue) {
            if (!newValue) {
              context
                  .read<SavedCenterBloc>()
                  .add(UnsaveCenterEvent(center.id!));
            }
          },
          onTap: () {
            print(center);
            NavigatorService.pushNamed(
              AppRoutes.centreView,
              arguments: {
                'center': center,
                'isSaved': true,
                'bottomView': true
              },
            );
          },
        );
      },
    );
  }
}
