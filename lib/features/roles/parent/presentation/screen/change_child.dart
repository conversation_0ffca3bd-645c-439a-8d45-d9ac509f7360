import 'package:class_z/core/imports.dart';
import 'package:http/http.dart' as http;

class ChangeChild extends StatefulWidget {
  final String? classId;

  const ChangeChild({super.key, this.classId});

  @override
  State<ChangeChild> createState() => _ChangeChildState();
}

class _ChangeChildState extends State<ChangeChild> {
  List<ChildModel> childs = [];
  List<ChildModel> eligibleChilds = [];
  bool _isLoadingEligibility = false;
  String? _eligibilityMessage;
  String? _classId;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args =
          ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      if (args != null && args['classId'] != null) {
        setState(() {
          _classId = args['classId'] as String;
        });
      } else {
        _classId = widget.classId;
      }
    });

    context.read<UserBloc>().add(GetChildByParentIdEvent(parentId: ""));
  }

  Future<void> _filterEligibleChildren() async {
    final classIdToUse = _classId ?? widget.classId;

    if (classIdToUse == null || childs.isEmpty) {
      setState(() {
        eligibleChilds = childs;
        _eligibilityMessage = null;
      });
      return;
    }

    setState(() {
      _isLoadingEligibility = true;
    });

    try {
      final userData = locator<SharedRepository>().getUserData();
      final userId = userData?.data?.parent?.id;
      final token = locator<SharedRepository>().getToken();

      if (userId == null || token == null) {
        throw Exception("User ID or token not available");
      }

      print('🔍 Fetching eligible children for class: $classIdToUse');

      final response = await http.get(
        Uri.parse(
            "${AppText.device}/api/orders/eligible-children/$userId/$classIdToUse"),
        headers: {
          'Content-Type': 'application/json',
          'auth-token': token,
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final eligibleChildrenData = data['eligibleChildren'] as List;

        final eligibleChildIds = eligibleChildrenData
            .map((child) => child['_id'].toString())
            .toSet();

        setState(() {
          eligibleChilds = childs
              .where((child) => eligibleChildIds.contains(child.id?.toString()))
              .toList();

          _eligibilityMessage = data['message'];
          _isLoadingEligibility = false;
        });

        print(
            '✅ Found ${eligibleChilds.length} eligible children out of ${childs.length} total');

        if (eligibleChilds.isEmpty) {
          print(
              'ℹ️ No eligible children found - all have already booked this class');
        }
      } else {
        throw Exception(
            "Failed to fetch eligible children: ${response.statusCode}");
      }
    } catch (e) {
      print('❌ Error filtering eligible children: $e');
      setState(() {
        eligibleChilds = childs;
        _eligibilityMessage = "Could not verify booking eligibility";
        _isLoadingEligibility = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "Change Child",
        leading: customBackButton(),
      ),
      body: BlocListener<UserBloc, UserState>(
        listener: (context, state) {
          if (state is UserLoadingState) {
            loadingState(context: context);
          } else
            hideLoadingDialog(context);
          if (state is UserErrorState) {
            errorState(context: context, error: state.message);
          }
          if (state is GetChildByParentIdSuccessState) {
            setState(() {
              childs = state.child;
            });
            _filterEligibleChildren();
          }
        },
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if ((_classId ?? widget.classId) != null &&
                  _eligibilityMessage != null)
                Padding(
                  padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 20.h),
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: eligibleChilds.isEmpty
                          ? Colors.orange.shade50
                          : Colors.green.shade50,
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: eligibleChilds.isEmpty
                            ? Colors.orange.shade300
                            : Colors.green.shade300,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          eligibleChilds.isEmpty
                              ? Icons.info_outline
                              : Icons.check_circle_outline,
                          color: eligibleChilds.isEmpty
                              ? Colors.orange.shade700
                              : Colors.green.shade700,
                          size: 20.w,
                        ),
                        SizedBox(width: 8.w),
                        Expanded(
                          child: customtext(
                            context: context,
                            newYear: _eligibilityMessage!,
                            font: 13.sp,
                            weight: FontWeight.w500,
                            color: eligibleChilds.isEmpty
                                ? Colors.orange.shade700
                                : Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              if (_isLoadingEligibility)
                Padding(
                  padding: EdgeInsets.only(top: 40.h),
                  child: Center(child: CircularProgressIndicator()),
                ),
              if (!_isLoadingEligibility)
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.only(
                      left: 20.w,
                      top: (_classId ?? widget.classId) != null ? 20.h : 72.h,
                      right: 20.w),
                  itemBuilder: (context, index) {
                    final child = eligibleChilds[index];
                    return GestureDetector(
                      onTap: () {
                        NavigatorService.goBack({
                          'imagePath':
                              "${AppText.device}${child.mainImage?.url}",
                          'name': child.fullname ?? "",
                          'sen': child.sen ?? false,
                          'school': child.school ?? "",
                          'dateTime': child.birthday ?? "",
                          'id': child.id,
                          'phone': child.phone ?? ''
                        });
                      },
                      child: changeChildCard(
                        context: context,
                        imagePath: "${AppText.device}${child.mainImage?.url}",
                        name: child.fullname ?? "",
                        sen: child.sen ?? false,
                        school: child.school ?? "",
                        dateTime: child.birthday ?? "",
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(height: 15.h);
                  },
                  itemCount: eligibleChilds.length,
                ),
              if (!_isLoadingEligibility &&
                  eligibleChilds.isEmpty &&
                  childs.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: 40.h),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.child_care,
                          size: 60.w,
                          color: Colors.grey[400],
                        ),
                        SizedBox(height: 16.h),
                        customtext(
                          context: context,
                          newYear:
                              "All children have already booked this program",
                          font: 16.sp,
                          weight: FontWeight.w600,
                          color: Colors.grey[600],
                        ),
                        SizedBox(height: 8.h),
                        customtext(
                          context: context,
                          newYear: "Each child can only book a program once",
                          font: 13.sp,
                          weight: FontWeight.w400,
                          color: Colors.grey[500],
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
