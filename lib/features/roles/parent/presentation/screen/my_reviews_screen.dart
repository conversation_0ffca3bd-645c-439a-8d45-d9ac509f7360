// import 'package:class_z/core/imports.dart';
// import 'package:class_z/core/services/course_progress_service.dart';
// import 'package:class_z/core/common/data/models/reviewable_item_model.dart';
// import 'package:class_z/core/common/data/models/pending_model.dart';
// import 'package:class_z/core/common/data/models/review_of_child.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:class_z/features/roles/parent/presentation/userbloc/user_bloc.dart';
// import 'package:class_z/core/common/presentation/widgets/custom_notification_badge.dart';
// import 'package:flutter/foundation.dart';
// import 'package:class_z/core/common/presentation/blocs/reviewBloc/review_bloc.dart';

// class MyReviewsScreen extends StatefulWidget {
//   const MyReviewsScreen({super.key});

//   @override
//   State<MyReviewsScreen> createState() => _MyReviewsScreenState();
// }

// class _MyReviewsScreenState extends State<MyReviewsScreen>
//     with SingleTickerProviderStateMixin {
//   late TabController _tabController;
//   final CourseProgressService _courseProgressService = CourseProgressService();
//   List<ReviewableItem> _reviewableItems = [];
//   bool _isLoadingUiForBloc = true;
//   String? _parentId;
//   List<PendingModel> _rawPendingModels = [];
//   bool _isHistoryLoading = false;
//   List<ReviewOfChildModel> _reviewHistoryItems = [];
//   List<ChildModel> _children = [];
//   ChildModel? _selectedChild;

//   @override
//   void initState() {
//     super.initState();
//     _tabController = TabController(length: 2, vsync: this);
//     _isHistoryLoading = _tabController.index == 1;
//     _loadParentIdAndDispatchFetch();

//     _tabController.addListener(() {
//       if (_tabController.indexIsChanging) {
//         // Tab is about to change
//         print(
//             "[MyReviewsScreen] Tab listener: indexIsChanging - new index \${_tabController.index}"); // DEBUG
//       } else {
//         // Tab has finished changing
//         print(
//             "[MyReviewsScreen] Tab listener: finished changing to index \${_tabController.index}"); // DEBUG
//         print(
//             "[MyReviewsScreen] Tab listener: Checking conditions: index == 1 (\${_tabController.index == 1}), historyEmpty (\${_reviewHistoryItems.isEmpty}), !isLoading (\${!_isHistoryLoading}), parentIdNotNull (\${_parentId != null}), parentIdNotEmpty (\${_parentId?.isNotEmpty})"); // DEBUG

//         if (_tabController.index == 1 && // History tab is selected
//             _reviewHistoryItems.isEmpty && // And history hasn't been loaded
//             !_isHistoryLoading && // And not already loading
//             _parentId != null &&
//             _parentId!.isNotEmpty) {
//           print(
//               "[MyReviewsScreen] Tab listener: All conditions met. Dispatching GetParentReviewHistoryEvent for parentId: \$_parentId"); // DEBUG
//           context
//               .read<UserBloc>()
//               .add(GetParentReviewHistoryEvent(parentId: _parentId!));
//         } else {
//           print(
//               "[MyReviewsScreen] Tab listener: Conditions NOT met for dispatching GetParentReviewHistoryEvent."); // DEBUG
//           if (_tabController.index != 1) print("  - Reason: Not history tab");
//           if (_reviewHistoryItems.isNotEmpty)
//             print(
//                 "  - Reason: History items not empty (\${_reviewHistoryItems.length})");
//           if (_isHistoryLoading) print("  - Reason: Already loading history");
//           if (_parentId == null) print("  - Reason: parentId is null");
//           if (_parentId != null && _parentId!.isEmpty)
//             print("  - Reason: parentId is empty");
//         }
//       }
//     });

//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       print(
//           "[MyReviewsScreen] PostFrameCallback: Current tab index \${_tabController.index}"); // DEBUG
//       print(
//           "[MyReviewsScreen] PostFrameCallback: Checking conditions: index == 1 (\${_tabController.index == 1}), historyEmpty (\${_reviewHistoryItems.isEmpty}), !isLoading (\${!_isHistoryLoading}), parentIdNotNull (\${_parentId != null}), parentIdNotEmpty (\${_parentId?.isNotEmpty})"); // DEBUG
//       if (_tabController.index == 1 &&
//           _reviewHistoryItems.isEmpty &&
//           !_isHistoryLoading &&
//           _parentId != null &&
//           _parentId!.isNotEmpty) {
//         context
//             .read<UserBloc>()
//             .add(GetParentReviewHistoryEvent(parentId: _parentId!));
//       }
//     });
//   }

//   Future<void> _loadParentIdAndDispatchFetch() async {
//     final parentData = locator<SharedRepository>().getParentData();
//     _parentId = parentData?.id;
//     if (_parentId != null && _parentId!.isNotEmpty) {
//       context
//           .read<UserBloc>()
//           .add(GetChildByParentIdEvent(parentId: _parentId!));

//       context
//           .read<UserBloc>()
//           .add(GetParentPendingReviewsEvent(parentId: _parentId!));

//       if (_tabController.index == 1) {
//         if (_reviewHistoryItems.isEmpty) {
//           setState(() {
//             _isHistoryLoading = true;
//           });
//           context
//               .read<UserBloc>()
//               .add(GetParentReviewHistoryEvent(parentId: _parentId!));
//         } else {
//           setState(() {
//             _isHistoryLoading = false;
//           });
//         }
//       } else {
//         setState(() {
//           _isHistoryLoading = false;
//         });
//       }
//     } else {
//       if (mounted) {
//         setState(() {
//           _isLoadingUiForBloc = false;
//           _isHistoryLoading = false;
//         });
//       }
//     }
//   }

//   Future<void> _processPendingModelsFromBloc(
//       List<PendingModel> pendingModels) async {
//     if (!mounted || _parentId == null) return;
//     setState(() {
//       _isLoadingUiForBloc = true;
//     });

//     try {
//       final items = await _courseProgressService.getReviewableItemsForParent(
//         pendingModels: pendingModels,
//         userId: _parentId!,
//       );
//       if (mounted) {
//         setState(() {
//           _reviewableItems = items;
//           _isLoadingUiForBloc = false;
//         });
//       }
//     } catch (e) {
//       if (mounted) {
//         setState(() {
//           _isLoadingUiForBloc = false;
//         });
//       }
//       print("Error processing reviewable items: $e");
//     }
//   }

//   @override
//   void dispose() {
//     _tabController.removeListener(
//         () {}); // It's good practice to remove listener if added directly like this, though dispose of controller handles it.
//     _tabController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         leading: CustomIconButton(
//           icon: Icons.arrow_back_ios,
//           onPressed: () {
//             NavigatorService.goBack();
//           },
//         ),
//         title: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             customtext(
//                 context: context,
//                 newYear: 'My Reviews',
//                 font: 20.sp,
//                 weight: FontWeight.w600),
//             if (_children.length >
//                 1) // Only show dropdown if more than one child
//               Expanded(
//                 child: Padding(
//                   padding: EdgeInsets.only(
//                       left: 16.w, right: 4.w), // Adjust padding as needed
//                   child: DropdownButtonHideUnderline(
//                     child: DropdownButton<ChildModel>(
//                       value: _selectedChild,
//                       isExpanded: true,
//                       hint: customtext(
//                           context: context,
//                           newYear: "Select Child",
//                           font: 14.sp), //  Added a hint
//                       icon: Icon(Icons.arrow_drop_down,
//                           color: AppPallete.darkGrey), // Added icon
//                       items: _children.map((ChildModel child) {
//                         return DropdownMenuItem<ChildModel>(
//                           value: child,
//                           child: customtext(
//                               context: context,
//                               newYear: child.fullname ?? 'Unnamed Child',
//                               font: 14.sp,
//                               color: AppPallete.darkGrey), // Display child name
//                         );
//                       }).toList(),
//                       onChanged: (ChildModel? newValue) {
//                         if (newValue != null) {
//                           setState(() {
//                             _selectedChild = newValue;
//                             // When child selection changes, we need to re-evaluate/re-filter
//                             // the lists for both tabs.
//                             // For "To Review", we might need to re-process _rawPendingModels
//                             // or filter _reviewableItems directly if childId is available.
//                             // For "History", we might need to re-fetch or filter _reviewHistoryItems.
//                             // This part will be refined when updating the tab building methods.
//                             print(
//                                 "[MyReviewsScreen] Selected child: ${_selectedChild?.fullname}");

//                             // Tentatively, re-trigger processing for "To Review" if it's the current tab
//                             if (_tabController.index == 0) {
//                               _processPendingModelsFromBloc(_rawPendingModels);
//                             }
//                             // For History, re-fetch if it's the current tab and parentId is valid
//                             if (_tabController.index == 1 &&
//                                 _parentId != null &&
//                                 _parentId!.isNotEmpty) {
//                               setState(() {
//                                 _isHistoryLoading =
//                                     true; // Show loading for history
//                                 _reviewHistoryItems = []; // Clear old history
//                               });
//                               context.read<UserBloc>().add(
//                                   GetParentReviewHistoryEvent(
//                                       parentId: _parentId!,
//                                       childId: _selectedChild?.id));
//                             }
//                           });
//                         }
//                       },
//                     ),
//                   ),
//                 ),
//               ),
//           ],
//         ),
//         actions: [
//           Padding(
//             padding: EdgeInsets.only(right: 8.w),
//             child: NotificationBadge(
//               icon: Icons.notifications_outlined,
//               badgeCount: 0,
//               iconColor: AppPallete.darkGrey,
//               onTap: () {
//                 if (_parentId != null && _parentId!.isNotEmpty) {
//                   NavigatorService.pushNamed(AppRoutes.notification,
//                       arguments: _parentId);
//                 } else {
//                   ScaffoldMessenger.of(context).showSnackBar(
//                     const SnackBar(
//                         content: Text(
//                             'Could not determine user for notifications.')),
//                   );
//                 }
//               },
//             ),
//           ),
//           Padding(
//             padding: EdgeInsets.only(right: 16.w),
//             child: NotificationBadge(
//               icon: Icons.messenger_outline_sharp,
//               badgeCount: 0,
//               iconColor: AppPallete.darkGrey,
//               onTap: () {
//                 NavigatorService.pushNamed(AppRoutes.centerMessage,
//                     arguments: 'user');
//               },
//             ),
//           ),
//         ],
//         bottom: TabBar(
//           controller: _tabController,
//           labelColor: AppPallete.secondaryColor,
//           unselectedLabelColor: AppPallete.darkGrey,
//           indicatorColor: AppPallete.secondaryColor,
//           tabs: const [
//             Tab(text: 'To Review'),
//             Tab(text: 'History'),
//           ],
//         ),
//       ),
//       body: MultiBlocListener(
//         listeners: [
//           BlocListener<UserBloc, UserState>(
//             listener: (context, state) {
//               if (state is UserLoadingState && _parentId != null) {
//                 // This is a general loading state, might want to be more specific
//                 // if needed, or rely on specific loading states like ParentReviewHistoryLoadingState.
//               } else if (state is ParentPendingReviewsSuccessState) {
//                 setState(() {
//                   _rawPendingModels = state.pendingReviews;
//                   _processPendingModelsFromBloc(
//                       _rawPendingModels); // This updates _reviewableItems and _isLoadingUiForBloc
//                 });
//               } else if (state is UserErrorState && _parentId != null) {
//                 if (mounted) {
//                   setState(() {
//                     _isLoadingUiForBloc = false;
//                     _reviewableItems = [];
//                     // Also ensure history loading is stopped on a general user error if it was active
//                     _isHistoryLoading = false;
//                   });
//                 }
//                 ScaffoldMessenger.of(context).showSnackBar(
//                   SnackBar(
//                       content: Text(
//                           "Error fetching pending reviews: ${state.message}")),
//                 );
//               }

//               // Handle Get Children States
//               if (state is GetChildByParentIdSuccessState) {
//                 if (mounted) {
//                   setState(() {
//                     _children = state.child;
//                     // Optionally, set the first child as selected by default
//                     if (_children.isNotEmpty) {
//                       _selectedChild = _children[0];
//                     } else {
//                       _selectedChild = null;
//                     }
//                     // After fetching children, you might want to re-filter or re-fetch reviews
//                     // if the lists depend on the selected child and are already populated.
//                     // For now, just updating the children list.
//                   });
//                 }
//               } else if (state is UserErrorState &&
//                   state.message.contains("children")) {
//                 // Be more specific if possible
//                 // Handle error fetching children, maybe show a message
//                 print(
//                     "[MyReviewsScreen] Error fetching children: ${state.message}");
//               }

//               // Handle Review History States
//               if (state is ParentReviewHistoryLoadingState) {
//                 print(
//                     "[MyReviewsScreen] BlocListener: ParentReviewHistoryLoadingState received"); // DEBUG
//                 if (mounted) {
//                   setState(() {
//                     _isHistoryLoading = true;
//                   });
//                 }
//               } else if (state is ParentReviewHistorySuccessState) {
//                 print(
//                     "[MyReviewsScreen] BlocListener: ParentReviewHistorySuccessState received with ${state.history.length} items"); // DEBUG
//                 if (mounted) {
//                   setState(() {
//                     _reviewHistoryItems = state.history;
//                     _isHistoryLoading = false;
//                   });
//                 }
//               } else if (state is ParentReviewHistoryErrorState) {
//                 print(
//                     "[MyReviewsScreen] BlocListener: ParentReviewHistoryErrorState received: ${state.message}"); // DEBUG
//                 if (mounted) {
//                   setState(() {
//                     _isHistoryLoading = false;
//                     _reviewHistoryItems = []; // Clear history on error
//                   });
//                 }
//                 ScaffoldMessenger.of(context).showSnackBar(
//                   SnackBar(
//                       content: Text(
//                           "Error fetching review history: ${state.message}")),
//                 );
//               }
//             },
//           ),
//           BlocListener<ReviewBloc, ReviewState>(
//             listener: (context, state) {
//               if (state is PostReviewSuccessState) {
//                 print("[MyReviewsScreen] Review submitted successfully");
//                 // Refresh pending reviews to remove the submitted one
//                 if (_parentId != null) {
//                   context
//                       .read<UserBloc>()
//                       .add(GetParentPendingReviewsEvent(parentId: _parentId!));

//                   // Also refresh review history if we're on that tab
//                   if (_tabController.index == 1) {
//                     context.read<UserBloc>().add(GetParentReviewHistoryEvent(
//                         parentId: _parentId!, childId: _selectedChild?.id));
//                   }
//                 }
//               } else if (state is ReviewErrorState) {
//                 ScaffoldMessenger.of(context).showSnackBar(
//                   SnackBar(
//                       content:
//                           Text("Error submitting review: ${state.message}")),
//                 );
//               }
//             },
//           ),
//         ],
//         child: TabBarView(
//           controller: _tabController,
//           children: [
//             _buildToReviewTab(context),
//             _buildHistoryTab(context),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget _buildToReviewTab(BuildContext context) {
//     print('here is ${_rawPendingModels.length}');
//     // if (_isLoadingUiForBloc) {
//     //   return const Center(child: CircularProgressIndicator());
//     // }

//     // Filter reviewable items based on the selected child
//     final List<ReviewableItem> filteredReviewableItems = _selectedChild == null
//         ? _reviewableItems // Show all if no child is selected or if only one child
//         : _reviewableItems
//             .where((item) => item.childId == _selectedChild!.id)
//             .toList();

//     if (filteredReviewableItems.isEmpty) {
//       return Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(Icons.rate_review_outlined,
//                 size: 100.h, color: AppPallete.greyWord),
//             SizedBox(height: 20.h),
//             customtext(
//               context: context,
//               newYear: "You don't have any pending reviews",
//               font: 16.sp,
//               color: AppPallete.darkGrey,
//             ),
//           ],
//         ),
//       );
//     }

//     return ListView.builder(
//       itemCount: filteredReviewableItems.length,
//       itemBuilder: (context, index) {
//         final item = filteredReviewableItems[index];
//         // Find the child's name for display
//         final childName = _children
//             .firstWhere((child) => child.id == item.childId,
//                 orElse: () => ChildModel(fullname: "Unknown Child"))
//             .fullname;

//         return Card(
//           margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
//           child: Padding(
//             padding: EdgeInsets.all(12.h),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 customtext(
//                   context: context,
//                   newYear: item.classModel.classProviding ?? 'Unnamed Class',
//                   font: 18.sp,
//                   weight: FontWeight.bold,
//                 ),
//                 SizedBox(height: 4.h),
//                 // Display Child Name
//                 if (childName != null &&
//                     childName.isNotEmpty &&
//                     _children.length >
//                         1) // Show child name if available and multiple children exist
//                   Padding(
//                     padding: EdgeInsets.only(bottom: 4.h),
//                     child: customtext(
//                       context: context,
//                       newYear: "For: $childName",
//                       font: 15.sp,
//                       weight: FontWeight.w500,
//                       color: AppPallete.secondaryColor,
//                     ),
//                   ),
//                 if (item.sessionEvent.date != null)
//                   customtext(
//                     context: context,
//                     newYear:
//                         "Session Date: ${DateFormat('yyyy-MM-dd HH:mm').format(item.sessionEvent.date!)}",
//                     font: 14.sp,
//                     color: AppPallete.darkGrey,
//                   ),
//                 SizedBox(height: 4.h),
//                 customtext(
//                   context: context,
//                   newYear: "Review Type: ${item.reviewType}",
//                   font: 14.sp,
//                   color: AppPallete.darkGrey,
//                 ),
//                 SizedBox(height: 12.h),
//                 Align(
//                   alignment: Alignment.centerRight,
//                   child: ElevatedButton(
//                     onPressed: () async {
//                       if (_parentId != null) {
//                         // _courseProgressService.showReviewPopup(
//                         //   context: context,
//                         //   classModel: item.classModel,
//                         //   sessionEvent: item.sessionEvent,
//                         //   isHalfCourse: item.isFirstLessonReview,
//                         //   sessionDate: item.sessionEvent.date,
//                         // );

//                         final parentData =
//                             locator<SharedRepository>().getParentData();
//                         if (parentData?.id == null ||
//                             item.classModel.id == null) {
//                           // Handle missing data, maybe show an error
//                           if (mounted) {
//                             errorState(
//                                 context: context,
//                                 error:
//                                     "Missing required data to submit review.");
//                           }
//                           return;
//                         }

//                         DateTime? sessionDateTime;
//                         if (item.sessionEvent.date != null) {
//                           // Assuming item.sessionEvent.date is already a DateTime object
//                           // If it's a String, it needs parsing similar to what was done in class_details.dart
//                           if (item.sessionEvent.date is String) {
//                             try {
//                               // Attempt to parse if it's a common format, e.g., ISO8601 or "dd/MM/yy"
//                               // This part might need adjustment based on the actual string format
//                               sessionDateTime = DateTime.tryParse(
//                                       item.sessionEvent.date as String) ??
//                                   (DateFormat('dd/MM/yy').tryParse(
//                                       item.sessionEvent.date as String));
//                             } catch (e) {
//                               if (kDebugMode) {
//                                 print(
//                                     "Error parsing session date in MyReviewsScreen: ${item.sessionEvent.date}, Error: $e");
//                               }
//                             }
//                           } else if (item.sessionEvent.date is DateTime) {
//                             sessionDateTime =
//                                 item.sessionEvent.date as DateTime;
//                           }
//                         }

//                         showCombinedCourseReviewBottomSheet(
//                           context: context,
//                           classModel: item.classModel,
//                           sessionEvent: item.sessionEvent,
//                           isHalfCourse: item.isFirstLessonReview,
//                           reviewerId: parentData!.id!,
//                           classId: item.classModel.id!,
//                           sessionDate: sessionDateTime,
//                           actualReviewerType: "Parent",
//                         );

//                         await Future.delayed(const Duration(milliseconds: 800));
//                         if (mounted) {
//                           context.read<UserBloc>().add(
//                               GetParentPendingReviewsEvent(
//                                   parentId: _parentId!));
//                         }
//                       }
//                     },
//                     child: customtext(
//                         context: context,
//                         newYear: 'Review Now',
//                         font: 14.sp,
//                         color: Colors.white),
//                     style: ElevatedButton.styleFrom(
//                         backgroundColor: AppPallete.secondaryColor),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         );
//       },
//     );
//   }

//   // Add this method to show review details
//   void _showReviewDetailsBottomSheet(
//       BuildContext context, ReviewOfChildModel review) {
//     // Debug log to see the actual review data
//     print("[ReviewDetails] Full review data: ${review.toJson()}");

//     // Get class name if available
//     String className = review.classId?.classProviding ?? 'Unknown Class';
//     print("[ReviewDetails] Class name: $className");

//     // Check if this is a combined review (has both coach and center info)
//     bool hasCoachReview =
//         review.revieweeType == 'coach' || review.coachName != null;
//     bool hasCenterReview =
//         review.revieweeType == 'center' || review.centerName != null;
//     bool isCombinedReview = hasCoachReview && hasCenterReview;

//     print(
//         "[ReviewDetails] hasCoachReview: $hasCoachReview, hasCenterReview: $hasCenterReview, isCombinedReview: $isCombinedReview");

//     // Determine what was reviewed (class, coach, center)
//     String reviewedItemName = '';
//     if (review.revieweeType == 'coach' && review.coachName != null) {
//       reviewedItemName = "Coach: ${review.coachName}";
//     } else if (review.revieweeType == 'center' && review.centerName != null) {
//       reviewedItemName = "Center: ${review.centerName}";
//     } else if (review.revieweeType == 'child') {
//       // Find the child's name for display
//       final childName = _children
//           .firstWhere((child) => child.id == review.revieweeId,
//               orElse: () => ChildModel(fullname: "Unknown Child"))
//           .fullname;
//       reviewedItemName = "Child: $childName";
//     }

//     print("[ReviewDetails] Reviewee item name: $reviewedItemName");
//     print("[ReviewDetails] Reviewee type: ${review.revieweeType}");
//     print("[ReviewDetails] Reviewee ID: ${review.revieweeId}");

//     showModalBottomSheet(
//       context: context,
//       isScrollControlled: true,
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.only(
//           topLeft: Radius.circular(20.r),
//           topRight: Radius.circular(20.r),
//         ),
//       ),
//       builder: (BuildContext context) {
//         return Container(
//           padding: EdgeInsets.all(20.h),
//           constraints: BoxConstraints(
//             maxHeight: MediaQuery.of(context).size.height * 0.7,
//           ),
//           child: SingleChildScrollView(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 // Header with class name
//                 Center(
//                   child: customtext(
//                     context: context,
//                     newYear: className,
//                     font: 22.sp,
//                     weight: FontWeight.bold,
//                   ),
//                 ),
//                 SizedBox(height: 20.h),

//                 // Reviewed item - Coach section
//                 if (hasCoachReview) ...[
//                   Row(
//                     children: [
//                       Icon(
//                         Icons.person,
//                         color: AppPallete.secondaryColor,
//                         size: 24.h,
//                       ),
//                       SizedBox(width: 10.w),
//                       Expanded(
//                         child: customtext(
//                           context: context,
//                           newYear:
//                               "Coach: ${review.coachName ?? 'Unknown Coach'}",
//                           font: 18.sp,
//                           weight: FontWeight.w500,
//                           color: AppPallete.secondaryColor,
//                         ),
//                       ),
//                     ],
//                   ),
//                   SizedBox(height: 15.h),
//                 ],

//                 // Reviewed item - Center section
//                 if (hasCenterReview) ...[
//                   Row(
//                     children: [
//                       Icon(
//                         Icons.business,
//                         color: AppPallete.secondaryColor,
//                         size: 24.h,
//                       ),
//                       SizedBox(width: 10.w),
//                       Expanded(
//                         child: customtext(
//                           context: context,
//                           newYear:
//                               "Center: ${review.centerName ?? 'Unknown Center'}",
//                           font: 18.sp,
//                           weight: FontWeight.w500,
//                           color: AppPallete.secondaryColor,
//                         ),
//                       ),
//                     ],
//                   ),
//                   SizedBox(height: 15.h),
//                 ],

//                 // Child section (if this is a child review)
//                 if (review.revieweeType == 'child') ...[
//                   Row(
//                     children: [
//                       Icon(
//                         Icons.child_care,
//                         color: AppPallete.secondaryColor,
//                         size: 24.h,
//                       ),
//                       SizedBox(width: 10.w),
//                       Expanded(
//                         child: customtext(
//                           context: context,
//                           newYear: reviewedItemName,
//                           font: 18.sp,
//                           weight: FontWeight.w500,
//                           color: AppPallete.secondaryColor,
//                         ),
//                       ),
//                     ],
//                   ),
//                   SizedBox(height: 15.h),
//                 ],

//                 // Review date
//                 if (review.date != null)
//                   Row(
//                     children: [
//                       Icon(Icons.calendar_today,
//                           color: AppPallete.darkGrey, size: 20.h),
//                       SizedBox(width: 10.w),
//                       customtext(
//                         context: context,
//                         newYear:
//                             "Reviewed on: ${DateFormat('yyyy-MM-dd').format(review.date!)}",
//                         font: 16.sp,
//                         color: AppPallete.darkGrey,
//                       ),
//                     ],
//                   ),
//                 SizedBox(height: 15.h),

//                 // Rating with stars
//                 Row(
//                   children: [
//                     Icon(Icons.star, color: AppPallete.rating, size: 20.h),
//                     SizedBox(width: 10.w),
//                     customtext(
//                       context: context,
//                       newYear:
//                           "Rating: ${review.rating?.toStringAsFixed(1) ?? 'N/A'} / 10",
//                       font: 16.sp,
//                       color: AppPallete.darkGrey,
//                       weight: FontWeight.w500,
//                     ),
//                   ],
//                 ),
//                 SizedBox(height: 10.h),

//                 // Rating visualization with stars
//                 if (review.rating != null)
//                   Row(
//                     children: List.generate(
//                       5,
//                       (index) => Padding(
//                         padding: EdgeInsets.only(right: 5.w),
//                         child: Icon(
//                           Icons.star,
//                           color: index < (review.rating! / 2)
//                               ? AppPallete.rating
//                               : AppPallete.rateGray,
//                           size: 30.h,
//                         ),
//                       ),
//                     ),
//                   ),
//                 SizedBox(height: 20.h),

//                 // Comment section
//                 if (review.comment != null && review.comment!.isNotEmpty) ...[
//                   customtext(
//                     context: context,
//                     newYear: "Comment:",
//                     font: 16.sp,
//                     weight: FontWeight.w500,
//                     color: AppPallete.darkGrey,
//                   ),
//                   SizedBox(height: 5.h),
//                   Container(
//                     padding: EdgeInsets.all(10.h),
//                     decoration: BoxDecoration(
//                       color: Colors.grey[100],
//                       borderRadius: BorderRadius.circular(10.r),
//                     ),
//                     child: customtext(
//                       context: context,
//                       newYear: review.comment!,
//                       font: 16.sp,
//                       color: AppPallete.darkGrey,
//                     ),
//                   ),
//                   SizedBox(height: 20.h),
//                 ],

//                 // Topic if available
//                 if (review.topic != null && review.topic!.isNotEmpty) ...[
//                   customtext(
//                     context: context,
//                     newYear: "Topic:",
//                     font: 16.sp,
//                     weight: FontWeight.w500,
//                     color: AppPallete.darkGrey,
//                   ),
//                   SizedBox(height: 5.h),
//                   customtext(
//                     context: context,
//                     newYear: review.topic!,
//                     font: 16.sp,
//                     color: AppPallete.darkGrey,
//                   ),
//                   SizedBox(height: 20.h),
//                 ],

//                 // Questions if available
//                 if (review.questions != null) ...[
//                   customtext(
//                     context: context,
//                     newYear: "Question Ratings:",
//                     font: 16.sp,
//                     weight: FontWeight.w500,
//                     color: AppPallete.darkGrey,
//                   ),
//                   SizedBox(height: 10.h),
//                   if (review.questions!.q1 != null)
//                     _buildQuestionRating(
//                         context, "Question 1", review.questions!.q1!),
//                   if (review.questions!.q2 != null)
//                     _buildQuestionRating(
//                         context, "Question 2", review.questions!.q2!),
//                   if (review.questions!.q3 != null)
//                     _buildQuestionRating(
//                         context, "Question 3", review.questions!.q3!),
//                   if (review.questions!.q4 != null)
//                     _buildQuestionRating(
//                         context, "Question 4", review.questions!.q4!),
//                   if (review.questions!.q5 != null)
//                     _buildQuestionRating(
//                         context, "Question 5", review.questions!.q5!),
//                   if (review.questions!.q6 != null)
//                     _buildQuestionRating(
//                         context, "Question 6", review.questions!.q6!),
//                 ],

//                 // Best/Worst Questions if available
//                 if ((review.bestQuestion != null &&
//                         review.bestQuestion!.isNotEmpty) ||
//                     (review.worstQuestion != null &&
//                         review.worstQuestion!.isNotEmpty)) ...[
//                   SizedBox(height: 20.h),
//                   if (review.bestQuestion != null &&
//                       review.bestQuestion!.isNotEmpty) ...[
//                     customtext(
//                       context: context,
//                       newYear: "Best Question:",
//                       font: 16.sp,
//                       weight: FontWeight.w500,
//                       color: AppPallete.darkGrey,
//                     ),
//                     SizedBox(height: 5.h),
//                     customtext(
//                       context: context,
//                       newYear: review.bestQuestion!,
//                       font: 16.sp,
//                       color: AppPallete.darkGrey,
//                     ),
//                   ],
//                   SizedBox(height: 10.h),
//                   if (review.worstQuestion != null &&
//                       review.worstQuestion!.isNotEmpty) ...[
//                     customtext(
//                       context: context,
//                       newYear: "Worst Question:",
//                       font: 16.sp,
//                       weight: FontWeight.w500,
//                       color: AppPallete.darkGrey,
//                     ),
//                     SizedBox(height: 5.h),
//                     customtext(
//                       context: context,
//                       newYear: review.worstQuestion!,
//                       font: 16.sp,
//                       color: AppPallete.darkGrey,
//                     ),
//                   ],
//                 ],

//                 // Close button
//                 SizedBox(height: 30.h),
//                 Center(
//                   child: ElevatedButton(
//                     onPressed: () => Navigator.of(context).pop(),
//                     style: ElevatedButton.styleFrom(
//                       backgroundColor: AppPallete.secondaryColor,
//                       padding: EdgeInsets.symmetric(
//                           horizontal: 30.w, vertical: 12.h),
//                       shape: RoundedRectangleBorder(
//                         borderRadius: BorderRadius.circular(30.r),
//                       ),
//                     ),
//                     child: customtext(
//                       context: context,
//                       newYear: "Close",
//                       font: 16.sp,
//                       color: Colors.white,
//                     ),
//                   ),
//                 ),
//                 SizedBox(height: 10.h),
//               ],
//             ),
//           ),
//         );
//       },
//     );
//   }

//   // Helper method to build question rating display
//   Widget _buildQuestionRating(
//       BuildContext context, String questionTitle, double rating) {
//     return Padding(
//       padding: EdgeInsets.only(bottom: 8.h),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           customtext(
//             context: context,
//             newYear: questionTitle,
//             font: 14.sp,
//             color: AppPallete.darkGrey,
//           ),
//           Row(
//             children: List.generate(
//               5,
//               (index) => Icon(
//                 Icons.star,
//                 color: index < (rating / 2)
//                     ? AppPallete.rating
//                     : AppPallete.rateGray,
//                 size: 18.h,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }

//   // Also add logging to the history tab to debug the data
//   Widget _buildHistoryTab(BuildContext context) {
//     if (_isHistoryLoading) {
//       return const Center(child: CircularProgressIndicator());
//     }

//     // Debug print to see what's in the history items
//     print(
//         "[MyReviewsScreen] _buildHistoryTab: _reviewHistoryItems count = ${_reviewHistoryItems.length}");
//     if (_reviewHistoryItems.isNotEmpty) {
//       print(
//           "[MyReviewsScreen] First review: revieweeType = ${_reviewHistoryItems[0].revieweeType}, revieweeId = ${_reviewHistoryItems[0].revieweeId}");
//       // Print more details about the first review
//       try {
//         print(
//             "[MyReviewsScreen] First review details: ${_reviewHistoryItems[0].toJson()}");
//       } catch (e) {
//         print("[MyReviewsScreen] Error converting review to JSON: $e");
//       }
//     }

//     // Filter history items based on the selected child, but don't filter by revieweeType
//     final List<ReviewOfChildModel> filteredHistoryItems = _selectedChild == null
//         ? _reviewHistoryItems // Show all if no child is selected
//         : _reviewHistoryItems
//             .where((review) =>
//                 // Include reviews where the child is the reviewee
//                 (review.revieweeId == _selectedChild!.id) ||
//                 // Or include reviews that aren't specifically for a child (like center or coach reviews)
//                 (review.revieweeType != 'child'))
//             .toList();

//     print(
//         "[MyReviewsScreen] _buildHistoryTab: filteredHistoryItems count = ${filteredHistoryItems.length}");

//     if (filteredHistoryItems.isEmpty) {
//       return Center(
//         child: Column(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Icon(Icons.history_toggle_off_outlined,
//                 size: 100.h, color: AppPallete.greyWord),
//             SizedBox(height: 20.h),
//             customtext(
//               context: context,
//               newYear: "You haven\'t reviewed any classes yet",
//               font: 16.sp,
//               color: AppPallete.darkGrey,
//             ),
//           ],
//         ),
//       );
//     }

//     return ListView.builder(
//       itemCount: filteredHistoryItems.length,
//       itemBuilder: (context, index) {
//         final review = filteredHistoryItems[index];

//         // Log individual review data for debugging
//         print(
//             "[MyReviewsScreen] Review $index: ${review.revieweeType} - ${review.revieweeId}");

//         // Determine what was reviewed (class, coach, center)
//         String reviewedItemName = '';
//         if (review.revieweeType == 'coach' && review.coachName != null) {
//           reviewedItemName = "Coach: ${review.coachName}";
//         } else if (review.revieweeType == 'center' &&
//             review.centerName != null) {
//           reviewedItemName = "Center: ${review.centerName}";
//         } else if (review.revieweeType == 'child') {
//           // Find the child's name for display
//           final childName = _children
//               .firstWhere((child) => child.id == review.revieweeId,
//                   orElse: () => ChildModel(fullname: "Unknown Child"))
//               .fullname;
//           reviewedItemName = "Child: $childName";
//         }

//         // Get class name if available
//         String className = review.classId?.classProviding ?? 'Unknown Class';

//         return Card(
//           margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
//           child: InkWell(
//             onTap: () => _showReviewDetailsBottomSheet(context, review),
//             borderRadius: BorderRadius.circular(4.r),
//             child: Padding(
//               padding: EdgeInsets.all(12.h),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   // Display class name
//                   customtext(
//                     context: context,
//                     newYear: className,
//                     font: 18.sp,
//                     weight: FontWeight.bold,
//                   ),
//                   SizedBox(height: 4.h),

//                   // Display what was reviewed
//                   if (reviewedItemName.isNotEmpty)
//                     Padding(
//                       padding: EdgeInsets.only(bottom: 4.h),
//                       child: customtext(
//                         context: context,
//                         newYear: reviewedItemName,
//                         font: 15.sp,
//                         weight: FontWeight.w500,
//                         color: AppPallete.secondaryColor,
//                       ),
//                     ),

//                   // Display review date
//                   if (review.date != null)
//                     customtext(
//                       context: context,
//                       newYear:
//                           "Reviewed on: ${DateFormat('yyyy-MM-dd').format(review.date!)}",
//                       font: 14.sp,
//                       color: AppPallete.darkGrey,
//                     ),
//                   SizedBox(height: 4.h),

//                   // Display rating
//                   customtext(
//                     context: context,
//                     newYear:
//                         "Rating: ${review.rating?.toStringAsFixed(1) ?? 'N/A'} / 10",
//                     font: 14.sp,
//                     color: AppPallete.darkGrey,
//                   ),

//                   // Display comment if available
//                   if (review.comment != null && review.comment!.isNotEmpty)
//                     Padding(
//                       padding: EdgeInsets.only(top: 4.h),
//                       child: customtext(
//                         context: context,
//                         newYear: "Comment: ${review.comment}",
//                         font: 14.sp,
//                         color: AppPallete.darkGrey,
//                       ),
//                     ),
//                 ],
//               ),
//             ),
//           ),
//         );
//       },
//     );
//   }
// }
