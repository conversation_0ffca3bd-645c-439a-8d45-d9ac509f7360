import 'package:class_z/core/imports.dart';
import 'package:class_z/core/utils/loading_manager.dart';

class Search extends StatefulWidget {
  const Search({super.key});

  @override
  State<Search> createState() => _RequestState();
}

class _RequestState extends State<Search> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    LoadingManager.hide();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    TextEditingController searchController = TextEditingController();
    return Scaffold(
      body: BlocListener<SearchBloc, SearchState>(
        listener: (context, state) {
          if (state is SearchLoading) {
            if (!LoadingManager.isLoading) {
              LoadingManager.show(context);
            }
          } else {
            LoadingManager.hide();
            if (state is SearchError) {
              errorState(context: context, error: state.error);
            } else if (state is SearchSuccessState) {
              NavigatorService.pushNamed(AppRoutes.searchGenre, arguments: {
                "result": state.search,
              });
            }
          }
        },
        child: SingleChildScrollView(
          child: Column(
            children: [
              _topBar(context: context),
              SizedBox(height: 16.76.h),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 13),
                child: AuthField(
                  controller: searchController,
                  height: 40.h,
                  hintText: "Skills, Centre, Location",
                  suffixIcon: IconButton(
                    onPressed: () {
                      context.read<SearchBloc>().add(
                            SearchQueryEvent(query: searchController.text),
                          );
                    },
                    icon: Icon(Icons.search),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 13.w, right: 19.w, top: 23.h),
                child: _category(context: context),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _topBar({required BuildContext context}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 82.h, left: 25.w),
          child: customtext(
              context: context,
              newYear: "Categories",
              font: 30.sp,
              weight: FontWeight.w500),
        ),
        customTopBarOnlyIcon(
          context: context,
          badgeCount1: 0,
          badgeCount2: 0,
          onTap1: () {
            NavigatorService.pushNamed(AppRoutes.notification,
                arguments: locator<SharedRepository>().getParentData()?.id);
          },
          onTap2: () {
            NavigatorService.pushNamed(AppRoutes.centerMessage,
                arguments: 'user');
          },
        ),
      ],
    );
  }

  Widget _textBox(
      {required BuildContext context,
      required TextEditingController controller,
      required VoidCallback onTap}) {
    return Padding(
      padding: EdgeInsets.only(left: 13.w, right: 19.w),
      child: Container(
          height: 35.h,
          decoration: BoxDecoration(
              color: AppPallete.inputBox,
              borderRadius: BorderRadius.circular(5.r)),
          child: TextFormField(
            controller: controller,
            decoration: InputDecoration(
              hintText: "Skills, Centre, Location",
              suffixIcon: InkWell(onTap: onTap, child: Icon(Icons.search)),
              hintStyle: TextStyle(
                  color: AppPallete.greyWord,
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w500),
              border: InputBorder.none,
              // contentPadding: EdgeInsets.symmetric(vertical: 10.h),
            ),
          )),
    );
  }

  Widget _category({required BuildContext context}) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      child: GridView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.only(bottom: 20.h),
        physics: AlwaysScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 24.w,
            mainAxisSpacing: 16.h,
            childAspectRatio: 1.6),
        itemCount: Category.categories.length,
        itemBuilder: (context, index) {
          final title = Category.categories[index];
          return categoriesCard(
              context: context,
              text: title,
              onPressed: () {
                NavigatorService.pushNamed(AppRoutes.searchGenre,
                    arguments: {"title": title});
              });
        },
      ),
    );
  }
}
