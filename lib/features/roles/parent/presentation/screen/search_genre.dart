import 'package:class_z/core/imports.dart';
import 'package:class_z/features/roles/parent/presentation/screen/filter_screen.dart';
import 'package:class_z/services/location_service.dart';
import 'package:geolocator/geolocator.dart';
import 'package:class_z/core/common/presentation/blocs/savedCenterBloc/saved_center_bloc.dart';
import 'package:class_z/core/common/widgets/list_item_skeleton.dart';

class SearchGenre extends StatefulWidget {
  final String? title;
  final SearchModel? result;
  const SearchGenre({this.title, this.result, super.key});

  @override
  State<SearchGenre> createState() => _SearchGenreState();
}

class _SearchGenreState extends State<SearchGenre> {
  // Filter state
  int? _minPrice;
  int? _maxPrice;
  int? _minAge;
  int? _maxAge;
  String? _location;
  String? _sortBy;
  double? _rating;
  bool? _senService;
  bool _isInitialSearch = true;
  bool _isSearching = false;
  List<dynamic> _centers = [];
  final ScrollController _scrollController = ScrollController();
  late final SavedCenterBloc _savedCenterBloc;
  Map<String, bool> savedCentersMap = {};
  bool _hasCentersBeenChecked = false;
  // Location service
  final LocationService _locationService = locator<LocationService>();

  @override
  void initState() {
    super.initState();
    _savedCenterBloc = context.read<SavedCenterBloc>();

    // Only trigger initial search if we have a title
    if (widget.title != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _performInitialSearch();
      });
    }
    if (widget.result != null) {
      _centers.addAll(widget.result!.centers!);
      _checkSavedCenters();
    }
  }

  @override
  void dispose() {
    // LoadingManager.hide();
    super.dispose();
  }

  // Handle initial search
  void _performInitialSearch() {
    if (!_isInitialSearch || _isSearching) return;
    _isInitialSearch = false;
    _isSearching = true;

    if (_location == 'Your location') {
      _handleLocationBasedSearch();
    } else if (widget.title != null) {
      print('Performing initial category search for: ${widget.title}');
      context.read<SearchBloc>().add(SearchCenterByCategory(
            category: widget.title!,
            priceMin: _minPrice,
            priceMax: _maxPrice,
            ageFrom: _minAge,
            ageTo: _maxAge,
            location: _location,
            sortBy: _sortBy,
            rating: _rating,
            senService: _senService,
          ));
    }
  }

  void _updateClass(List<ClassModel> newClass) {
    print('hi');
    if (newClass.length > 0) {
      for (var classs in newClass) {
        _centers.add(classs.center);
      }

      _checkSavedCenters();
    }
  }

  void _updateCenters(List<CenterData> newCenters) {
    setState(() {
      _centers.addAll(newCenters);
      _checkSavedCenters();
    });
  }

  void _checkSavedCenters() {
    if (_hasCentersBeenChecked) return;

    print('Checking saved centers status ${_centers.length}');
    for (var center in _centers) {
      if (center.id != null) {
        print('Checking saved status for center: ${center.id}');
        _savedCenterBloc.add(CheckCenterSavedEvent(center.id!));
      }
    }

    _hasCentersBeenChecked = true;
  }

  void _onFavoriteToggle(String centerId, bool newValue) {
    print('Toggling favorite for center: $centerId to $newValue');
    if (newValue) {
      _savedCenterBloc.add(SaveCenterEvent(centerId));
    } else {
      _savedCenterBloc.add(UnsaveCenterEvent(centerId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: MultiBlocListener(
        listeners: [
          BlocListener<SavedCenterBloc, SavedCenterState>(
            listener: (context, state) {
              if (state is SavedCenterLoading) {
                errorState(context: context, error: 'Saving....');
              }
              if (state is UnSavedCenterLoading) {
                errorState(context: context, error: 'Removing....');
              }
              print('SavedCenterBloc search genre state: $state');
              if (state is CenterSavedStatus) {
                setState(() {
                  savedCentersMap[state.centerId] = state.isSaved;
                });
              } else if (state is CenterSavedSuccess) {
                print('saved');
                setState(() {
                  savedCentersMap[state.centerId] = true;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Center saved successfully')),
                );
              } else if (state is CenterUnsavedSuccess) {
                setState(() {
                  savedCentersMap[state.centerId] = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Center removed from saved')),
                );
              } else if (state is SavedCenterError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(state.message)),
                );
              }
            },
          ),
          BlocListener<SearchBloc, SearchState>(
            listener: (context, state) {
              if (state is SearchLoading) {
                // Handled by BlocBuilder
              } else {
                _isSearching = false;
                // LoadingManager.hide();
                if (state is SearchError) {
                  errorState(context: context, error: state.error);
                }
              }
            },
          ),
        ],
        child: SingleChildScrollView(
          child: Column(
            children: [
              _topBar(context: context),
              SizedBox(height: 16.76.h),
              _filter(context: context),
              widget.title != null
                  ? _titleSearch(context: context)
                  : Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 13.w, vertical: 13.h),
                      child:
                          _showResult(context: context, result: widget.result!),
                    )
            ],
          ),
        ),
      ),
    );
  }

  Widget _topBar({required BuildContext context}) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 82.h, left: 19.w),
              child: customBackButton(),
            ),
            customTopBarOnlyIcon(
              context: context,
              badgeCount1: 0,
              badgeCount2: 0,
              onTap1: () {
                NavigatorService.pushNamed(AppRoutes.notification,
                    arguments: locator<SharedRepository>().getParentData()?.id);
              },
              onTap2: () {
                NavigatorService.pushNamed(AppRoutes.centerMessage,
                    arguments: 'user');
              },
            ),
          ],
        ),
        const SizedBox(height: 6),
        Padding(
          padding: EdgeInsets.only(left: 9.w),
          child: customtext(
            context: context,
            newYear: widget.title ?? "Search Result",
            font: 30.sp,
            weight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 21.h),
        customDivider(width: 430.w, padding: 0),
        SizedBox(height: 10.h),
      ],
    );
  }

  Widget _filter({required BuildContext context}) {
    return Align(
      alignment: Alignment.topRight,
      child: Padding(
        padding: EdgeInsets.only(right: 33.w),
        child: InkWell(
          onTap: () async {
            // Navigate to filter screen and wait for result
            final result = await Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => FilterScreen(
                  minPrice: _minPrice,
                  maxPrice: _maxPrice,
                  minAge: _minAge,
                  maxAge: _maxAge,
                  location: _location,
                  sortBy: _sortBy,
                  rating: _rating,
                  senService: _senService,
                ),
              ),
            );

            // If filters were applied, update state and trigger a new search
            if (result != null && result is Map<String, dynamic>) {
              setState(() {
                _minPrice = result['minPrice'];
                _maxPrice = result['maxPrice'];
                _minAge = result['minAge'];
                _maxAge = result['maxAge'];
                _location = result['location'];
                _sortBy = result['sortBy'];
                _rating = result['rating'];
                _senService = result['senService'];
                _isSearching = true; // Set searching flag
                _centers.clear(); // Clear old results
              });

              // Dispatch the search event with the new filters
              context.read<SearchBloc>().add(SearchCenterByCategory(
                    category: widget.title!,
                    priceMin: _minPrice,
                    priceMax: _maxPrice,
                    ageFrom: _minAge,
                    ageTo: _maxAge,
                    location: _location,
                    sortBy: _sortBy,
                    rating: _rating,
                    senService: _senService,
                  ));
            }
          },
          child: customSvgPicture(
            imagePath: ImagePath.filterSvg,
            height: 24.h,
            width: 21.33.w,
          ),
        ),
      ),
    );
  }

  Future<void> _handleLocationBasedSearch() async {
    if (_isSearching) return;
    _isSearching = true;

    try {
      final Position? position = await _locationService.getCurrentLocation();

      if (position != null && mounted) {
        print('User location: ${position.latitude}, ${position.longitude}');

        context.read<SearchBloc>().add(
              FindNearbyCenters(
                longitude: position.longitude,
                latitude: position.latitude,
                maxDistance: 5000,
                priceMin: _minPrice,
                priceMax: _maxPrice,
                ageFrom: _minAge,
                ageTo: _maxAge,
                sortBy: _sortBy,
                rating: _rating,
                senService: _senService,
              ),
            );
      } else if (mounted) {
        // LoadingManager.hide();
        _isSearching = false;

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Could not access your location. Using default search instead.'),
            duration: Duration(seconds: 3),
          ),
        );

        if (widget.title != null) {
          context.read<SearchBloc>().add(
                SearchCenterByCategory(
                  category: widget.title!,
                  priceMin: _minPrice,
                  priceMax: _maxPrice,
                  ageFrom: _minAge,
                  ageTo: _maxAge,
                  sortBy: _sortBy,
                  rating: _rating,
                  senService: _senService,
                ),
              );
        }
      }
    } catch (e) {
      if (mounted) {
        // LoadingManager.hide();
        _isSearching = false;

        print('Location error: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error accessing location: $e'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Widget _titleSearch({required BuildContext context}) {
    return BlocBuilder<SearchBloc, SearchState>(
      builder: (context, state) {
        print('current state is searchbloc $state');
        if (state is SearchCentersByCategorySuccessState) {
          final validClasses =
              state.searchs.classes?.where((c) => c.center != null).toList();
          _updateClass(validClasses ?? []);
          return _buildSearchResults(context: context, classes: validClasses);
        } else if (state is NearbyCentersSuccessState) {
          final validClasses =
              state.searchs.classes?.where((c) => c.center != null).toList();
          _updateClass(validClasses ?? []);
          return _buildSearchResults(context: context, classes: validClasses);
        } else if (state is SearchLoading) {
          return _buildSkeletonLoader();
        } else {
          return Center(
            child: customtext(
              context: context,
              newYear: "No data available",
              font: 18.sp,
            ),
          );
        }
      },
    );
  }

  Widget _buildSearchResults(
      {required BuildContext context, required List<ClassModel>? classes}) {
    if (classes == null || classes.isEmpty) {
      return Center(
        child: customtext(
          context: context,
          newYear: "No data available",
          font: 18.sp,
        ),
      );
    }

    return ListView.separated(
      shrinkWrap: true,
      padding: EdgeInsets.only(left: 14.w, right: 14.w, top: 14.h),
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final classs = classes[index];
        final isSaved = savedCentersMap[classs.center.id] ?? false;
        return buildCenterRowGallery(
          context: context,
          imageHeight: 163.h,
          imageWidth: 331.w,
          center: classs.center,
          isSaved: isSaved,
          fromClass: classs, // Pass the class data for pricing fallback
          onFavoriteToggle: (newValue) {
            if (classs.center.id != null) {
              _onFavoriteToggle(classs.center.id!, newValue);
            }
          },
          onTap: () {
            NavigatorService.pushNamed(AppRoutes.centreView, arguments: {
              'center': classs.center,
              "isSaved": isSaved,
              'bottomView': true
            });
          },
        );
      },
      separatorBuilder: (context, index) => SizedBox(height: 20.h),
      itemCount: classes.length,
    );
  }

  Widget _showResult(
      {required BuildContext context, required SearchModel result}) {
    // Combine the classes and centers into a single list
    final combinedList = [...?result.classes, ...?result.centers];

    _updateCenters(result.centers ?? []);
    // If the list is empty, show a "No result found" message
    if (combinedList.isEmpty) {
      return Center(
        child: customtext(
          context: context,
          newYear: "No result found",
          font: 18.sp,
        ),
      );
    }

    // Render the results using ListView
    return ListView.separated(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: combinedList.length,
      separatorBuilder: (context, index) => SizedBox(height: 20.h),
      itemBuilder: (context, index) {
        final item = combinedList[index];

        // If the item is ClassModel, render it with upcomingClassCard
        if (item is ClassModel) {
          return upcomingClassCard(
            context: context,
            noPadding: true,
            imagePath: item.mainImage?.url ?? "",
            title: item.classProviding ?? "",
            category: item.level ?? "",
            location: _getLocationForSearch(item),
            ageGroup: _getAgeGroupForSearch(item),
            rate: _getRateForSearch(item),
            time: _getTimeForSearch(item),
            onTap: () {
              print("Navigating to class center: ${item.center}");
              NavigatorService.pushNamed(
                AppRoutes.centreView,
                arguments: {'center': item.center, 'bottomView': true},
              );
            },
          );
        }

        // If the item is CenterData, render it with buildRowGallery
        else if (item is CenterData) {
          final isSaved = savedCentersMap[item.id] ?? false;
          return buildCenterRowGallery(
            context: context,
            imageHeight: 163.h,
            imageWidth: 331.w,
            center: item,
            isSaved: isSaved,
            onFavoriteToggle: (newValue) {
              if (item.id != null) {
                _onFavoriteToggle(item.id!, newValue);
              }
            },
            onTap: () {
              NavigatorService.pushNamed(AppRoutes.centreView, arguments: {
                'center': item,
                "isSaved": isSaved,
                'bottomView': true
              });
            },
          );
        }

        // Fallback for unknown item types
        return Center(
          child: customtext(
            context: context,
            newYear: "Invalid item type",
            font: 18.sp,
          ),
        );
      },
    );
  }

  Widget _buildSkeletonLoader() {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 5, // Show 5 skeletons while loading
      padding: EdgeInsets.only(left: 14.w, right: 14.w, top: 14.h),
      separatorBuilder: (context, index) => SizedBox(height: 20.h),
      itemBuilder: (context, index) => const ListItemSkeleton(),
    );
  }

  // Helper methods for search results
  String _getLocationForSearch(ClassModel item) {
    if (item.mode == null) return "Location TBD";
    return item.mode! ? "In-Center" : "Online";
  }

  String _getAgeGroupForSearch(ClassModel item) {
    final ageFrom = item.ageFrom;
    final ageTo = item.ageTo;

    if (ageFrom == null && ageTo == null) return "(Age TBD)";
    if (ageFrom == null) return "(Up to $ageTo)";
    if (ageTo == null) return "($ageFrom+)";
    return "($ageFrom-$ageTo)";
  }

  String _getRateForSearch(ClassModel item) {
    // First check the main class charge
    final mainCharge = item.charge;
    if (mainCharge != null && mainCharge > 0) {
      return mainCharge.toString();
    }

    // If main charge is null/0, check the dates for charge information
    if (item.dates?.isNotEmpty == true) {
      final dateCharge = item.dates?.first.charge;
      if (dateCharge != null && dateCharge > 0) {
        return dateCharge.toString();
      }
    }

    // Fallback to "0" if no charge found
    return "0";
  }

  String _getTimeForSearch(ClassModel item) {
    if (item.dates != null && item.dates!.isNotEmpty) {
      final duration = item.dates![0].durationMinutes;
      if (duration != null && duration.toString().isNotEmpty) {
        if (int.tryParse(duration.toString()) != null) {
          return "${duration}min";
        }
        return duration.toString();
      }
    }
    return "45min";
  }
}
