import 'package:class_z/core/imports.dart';
import 'package:class_z/core/utils/loading_manager.dart';
import 'package:shimmer/shimmer.dart';

class ChildInfo extends StatefulWidget {
  final String? parentId;
  const ChildInfo({this.parentId, super.key});

  @override
  State<ChildInfo> createState() => _ChildInfoState();
}

class _ChildInfoState extends State<ChildInfo> {
  String titleText = "Edit";
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _getChildInfo();
  }

  @override
  void dispose() {
    // Ensure loading is hidden when disposing
    LoadingManager.hide();
    super.dispose();
  }

  void _getChildInfo() {
    try {
      setState(() {
        isLoading = true;
        errorMessage = null;
      });

      // Use the provided parentId or get it from shared preferences
      String parentId = widget.parentId ?? "";

      // If no parentId provided, try to get it from shared repository
      if (parentId.isEmpty) {
        final sharedRepository = locator<SharedRepository>();
        final userData = sharedRepository.getUserData();
        parentId = userData?.data?.parent?.id ?? "";
      }

      print("DEBUG: Using parentId: $parentId"); // <--- Debug print

      // Only make the API call if we have a valid parentId
      if (parentId.isNotEmpty) {
        final userBloc = BlocProvider.of<UserBloc>(context);
        userBloc.add(GetChildByParentIdEvent(parentId: parentId));
      } else {
        setState(() {
          isLoading = false;
          errorMessage = "Parent ID not found. Please log in again.";
        });
      }
    } catch (e) {
      print("Error getting child info: $e");
      setState(() {
        isLoading = false;
        errorMessage = "Error loading child information: $e";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: errorMessage != null
          ? _buildErrorState()
          : BlocConsumer<UserBloc, UserState>(
              listener: (context, state) {
                if (state is UserLoadingState) {
                  // Show page skeleton while loading
                  setState(() {
                    isLoading = true;
                  });
                } else {
                  // Hide any possible overlay triggered elsewhere
                  LoadingManager.hide();

                  setState(() {
                    isLoading = false;
                  });

                  if (state is UserErrorState) {
                    setState(() {
                      errorMessage = state.message;
                    });
                  }
                }
              },
              builder: (context, state) {
                print("DEBUG: BlocConsumer state: $state"); // <--- Debug print
                if (state is GetChildByParentIdSuccessState) {
                  print("DEBUG: Children: ${state.child}");
                  return state.child.isNotEmpty
                      ? _buildChildInfoBody(context, state.child)
                      : _buildZeroProfile(context);
                }

                // Show skeleton while loading data
                if (isLoading) {
                  return _buildChildInfoSkeleton();
                }

                return _buildZeroProfile(context);
              },
            ),
      floatingActionButton: BlocBuilder<UserBloc, UserState>(
        builder: (context, state) {
          // Only show the FAB if there is at least one child profile.
          if (state is GetChildByParentIdSuccessState &&
              state.child.isNotEmpty) {
            return Padding(
              padding: EdgeInsets.only(bottom: 32.h),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Circular add button
                  GestureDetector(
                    onTap: () {
                      Navigator.pushNamed(context, AppRoutes.addChild)
                          .then((_) => _getChildInfo());
                    },
                    child: Container(
                      width: 100.w, // Increased size to match Figma
                      height: 100.h, // Increased size to match Figma
                      decoration: BoxDecoration(
                        color: AppPallete.secondaryColor,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 15,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Icon(
                          Icons.add,
                          color: Colors.white,
                          size: 42, // Increased icon size to match Figma
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 12.h), // Adjusted spacing
                  // "Add new profile" text
                  Text(
                    "Add new profile",
                    style: TextStyle(
                      fontSize: 20.sp,
                      color: const Color(0xFF0E7AFE),
                      fontWeight: FontWeight.w400,
                      fontFamily: "SF Pro Display",
                    ),
                  ),
                ],
              ),
            );
          }
          return const SizedBox.shrink(); // Don't show FAB while loading
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 60,
          ),
          SizedBox(height: 20),
          Text(
            'Error',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              errorMessage ?? 'An unknown error occurred',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
          ),
          SizedBox(height: 30),
          ElevatedButton(
            onPressed: () {
              _getChildInfo();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
            ),
            child: Text('Retry'),
          ),
          SizedBox(height: 10),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildChildInfoBody(BuildContext context, List<ChildModel> child) {
    return Stack(
      children: [
        SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                  padding: EdgeInsets.only(left: 19.w, top: 81.h),
                  child: customBackButton()),
              SizedBox(height: 25.h),
              Padding(
                padding: EdgeInsets.only(left: 20.w, right: 32.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    customtext(
                      context: context,
                      newYear: "Child Information",
                      font: 30.sp,
                      weight: FontWeight.w500,
                    ),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          titleText = titleText == "Edit" ? "Done" : "Edit";
                        });
                      },
                      child: Text(
                        titleText,
                        style: TextStyle(
                          color: AppPallete.change,
                          fontSize: 17.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                    left: 20.w, right: 20.w, top: 23.h, bottom: 120.h),
                child: ListView.separated(
                  padding: EdgeInsets.zero,
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return addedChildInfo(
                      context: context,
                      id: child[index].id!,
                      imagePath: imageStringGenerator(
                          imagePath: child[index].mainImage?.url ?? ''),
                      fullname: child[index].fullname ?? "",
                      birthday: child[index].birthday,
                      sen: child[index].sen ?? false,
                      school: child[index].school,
                      done: titleText.toLowerCase(),
                      onTap: () {
                        // Navigate to edit child screen when tapped
                        Navigator.pushNamed(context, AppRoutes.editChild,
                                arguments: child[index])
                            .then((_) => _getChildInfo());
                      },
                      removeTap: () async {
                        // Show loading using LoadingManager
                        LoadingManager.show(context);

                        // Delete child
                        BlocProvider.of<UserBloc>(context)
                            .add(DeleteChildByIdEvent(id: child[index].id!));

                        // Add delay before refreshing the list
                        await Future.delayed(const Duration(milliseconds: 500));
                        _getChildInfo();
                      },
                    );
                  },
                  separatorBuilder: (context, index) => SizedBox(height: 10.h),
                  itemCount: child.length,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildZeroProfile(BuildContext context) {
    return Column(
      children: [
        // Top section with back button and title
        Padding(
          padding: EdgeInsets.only(left: 19.w, right: 32.w, top: 81.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              customBackButton(),
              SizedBox(width: 25.w),
              customtext(
                context: context,
                newYear: "Child",
                font: 30.sp,
                weight: FontWeight.w500,
              ),
            ],
          ),
        ),
        // Centered add button and text
        Expanded(
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Circular add button
                GestureDetector(
                  onTap: () {
                    Navigator.pushNamed(context, AppRoutes.addChild)
                        .then((_) => _getChildInfo());
                  },
                  child: Container(
                    width: 100.w,
                    height: 100.h,
                    decoration: BoxDecoration(
                      color: AppPallete.secondaryColor,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 15,
                          offset: Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Center(
                      child: Icon(
                        Icons.add,
                        color: Colors.white,
                        size: 42,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 12.h),
                // "Add new profile" text
                Text(
                  "Add new profile",
                  style: TextStyle(
                    fontSize: 20.sp,
                    color: const Color(0xFF0E7AFE),
                    fontWeight: FontWeight.w400,
                    fontFamily: "SF Pro Display",
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildChildInfoSkeleton() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SingleChildScrollView(
        physics: const NeverScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 19.w, top: 81.h),
              child: Container(width: 24.w, height: 24.h, color: Colors.white),
            ),
            SizedBox(height: 25.h),
            Padding(
              padding: EdgeInsets.only(left: 20.w, right: 32.w),
              child: Row(
                children: [
                  Container(width: 150.w, height: 30.h, color: Colors.white),
                  const Spacer(),
                  Container(width: 60.w, height: 20.h, color: Colors.white),
                ],
              ),
            ),
            SizedBox(height: 23.h),
            Padding(
              padding: EdgeInsets.only(left: 20.w, right: 20.w),
              child: ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: 3,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: 10.h),
                    child: Row(
                      children: [
                        Container(
                          width: 60.w,
                          height: 60.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                  width: 150.w,
                                  height: 14.h,
                                  color: Colors.white),
                              SizedBox(height: 6.h),
                              Container(
                                  width: 100.w,
                                  height: 14.h,
                                  color: Colors.white),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
