import 'package:class_z/core/imports.dart';

class RefundScreen extends StatefulWidget {
  final String parentId;
  const RefundScreen({Key? key, required this.parentId}) : super(key: key);

  @override
  State<RefundScreen> createState() => _RefundScreenState();
}

class _RefundScreenState extends State<RefundScreen> {
  @override
  void initState() {
    super.initState();
    context.read<UserBloc>().add(RefundEvent(parentId: widget.parentId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Refunds'),
      ),
      body: BlocBuilder<UserBloc, UserState>(
        builder: (context, state) {
          if (state is RefundLoadingState) {
            return const Center(child: CircularProgressIndicator());
          } else if (state is RefundErrorState) {
            return Center(child: Text('Error: ${state.message}'));
          } else if (state is RefundSuccessState) {
            if (state.refunds.isEmpty) {
              return const Center(child: Text('No refunds found.'));
            }
            return RefreshIndicator(
              onRefresh: () async {
                context
                    .read<UserBloc>()
                    .add(RefundEvent(parentId: widget.parentId));
                // Wait for the bloc to update state (optional: add a small delay)
                await Future.delayed(Duration(milliseconds: 500));
              },
              child: ListView.separated(
                itemCount: state.refunds.length,
                separatorBuilder: (_, __) => Divider(),
                itemBuilder: (context, index) {
                  final refund = state.refunds[index];
                  return ListTile(
                    leading: Icon(Icons.monetization_on),
                    title: Text('Amount: ${refund.amount ?? '-'}'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Status: ${refund.status ?? '-'}'),
                        Text(
                            'Class: ${refund.classInfo?.classProviding ?? '-'}'),
                        Text(
                            'Center: ${refund.classInfo?.center?.displayName ?? ''}'),
                        Text(
                            'Coach: ${refund.classInfo?.coach?.displayName ?? ''}'),
                        Text(
                            'cancelled date: ${dateGenerator(date: refund.date)}'),
                        Text(
                            'Processed: ${refund.processedAt != null ? dateGenerator(date: refund.processedAt) : '-'}'),
                      ],
                    ),
                    trailing:
                        refund.notes != null ? Icon(Icons.info_outline) : null,
                  );
                },
              ),
            );
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }
}
