import 'package:class_z/core/imports.dart';

class CoachSpotlight extends StatefulWidget {
  const CoachSpotlight({super.key});

  @override
  State<CoachSpotlight> createState() => _CoachSpotlightState();
}

class _CoachSpotlightState extends State<CoachSpotlight> {
  int _activeIndex = 0;
  String userId = '';
  // Add streams for badge counts
  late StreamSubscription<int> _notificationCountSubscription;
  late StreamSubscription<int> _messageCountSubscription;
  int _notificationCount = 0;
  int _messageCount = 0;
  // Add a list to store coach data
  List<CoachModel> coaches = [];

  @override
  void initState() {
    super.initState();
    // Fetch user ID in a safer way
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        try {
          userId = locator<SharedRepository>().getUserId();
          // Initialize badge count service
          final badgeService = locator<BadgeCountService>();
          badgeService.initialize(userId);

          // Listen for notification count updates
          _notificationCountSubscription =
              badgeService.notificationCountStream.listen((count) {
            setState(() => _notificationCount = count);
          });

          // Listen for message count updates
          _messageCountSubscription =
              badgeService.messageCountStream.listen((count) {
            setState(() => _messageCount = count);
          });

          // Set initial counts
          _notificationCount = badgeService.notificationCount;
          _messageCount = badgeService.messageCount;

          // You might want to fetch coaches data here if available
        } catch (e) {
          print('Error getting userId or initializing badge counts: $e');
          userId = '';
        }
      });
    });
  }

  @override
  void dispose() {
    // Cancel stream subscriptions to prevent memory leaks
    _notificationCountSubscription.cancel();
    _messageCountSubscription.cancel();
    super.dispose();
  }

  void _onTabIndexChanged(int index) {
    setState(() {
      _activeIndex = index;
    });
    handleTabIndexChanged(index);
  }

  // Get user ID safely when needed
  String _getUserId() {
    if (userId.isEmpty) {
      try {
        return locator<SharedRepository>().getUserId();
      } catch (e) {
        print('Error getting userId: $e');
        return '';
      }
    }
    return userId;
  }

  // Navigate to messages/chat safely
  void _navigateToMessages() {
    try {
      final currentUserId = _getUserId();
      if (currentUserId.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('User ID not available. Please try again later.')),
        );
        return;
      }

      // Get all necessary data for the chat
      final sharedRepo = locator<SharedRepository>();
      final parentData = sharedRepo.getParentData();
      final String? userImage = parentData?.image?.url;

      // Try to get the coach data if available
      final coachData = sharedRepo.getCoachData();

      if (coachData == null) {
        // Coach data not available, create a dummy coach data with a fixed ID to avoid null errors
        final Map<String, dynamic> messagesData = {
          "title": "Coach Messages",
          "imagePath": "", // No image available
          "senderImage": userImage ?? '',
          "id": "dummy-coach-id", // Dummy ID to avoid null
          "senderId": currentUserId,
          "senderType": "parent",
          "oppositeModel": "coach",
        };

        NavigatorService.pushNamed(
          AppRoutes.chat,
          arguments: messagesData,
        );
        return;
      }

      // Required data for Chat component with proper property access for CoachModel
      final Map<String, dynamic> messagesData = {
        "title": coachData.displayName ?? "Coach",
        "imagePath":
            coachData.mainImage?.url ?? '', // Use mainImage instead of image
        "senderImage": userImage ?? '',
        "id": coachData.id ?? '',
        "senderId": currentUserId,
        "senderType": "parent",
        "oppositeModel": "coach",
      };

      NavigatorService.pushNamed(
        AppRoutes.chat,
        arguments: messagesData,
      );
    } catch (e) {
      print('Error navigating to chat: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Could not open chat: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Padding(
      padding: EdgeInsets.only(left: 9.w, right: 9.w),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // New top bar with back button
            Padding(
              padding: EdgeInsets.only(top: 70.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Back button
                  GestureDetector(
                    onTap: () {
                      NavigatorService.goBack();
                    },
                    child: Icon(
                      Icons.arrow_back_ios,
                      color: AppPallete.secondaryColor,
                      size: 24.w,
                    ),
                  ),
                  // Notification icons
                  Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          // Navigate to notification screen with user ID
                          final currentUserId = _getUserId();
                          NavigatorService.pushNamed(
                            AppRoutes.notification,
                            arguments: currentUserId,
                          ).then((_) {
                            // Refresh notification count after returning from notifications screen
                            locator<BadgeCountService>()
                                .refreshNotificationCount(currentUserId);
                          });
                        },
                        child: notification_badge(
                            context: context,
                            icon: Icons.notifications,
                            badgeCount: _notificationCount),
                      ),
                      SizedBox(width: 4.w),
                      GestureDetector(
                        onTap: _navigateToMessages,
                        child: notification_badge(
                            context: context,
                            icon: Icons.message,
                            badgeCount: _messageCount),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(height: 5.h),
            const Text(
              "Coach Spotlight",
              style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20.h),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              separatorBuilder: (context, index) =>
                  SizedBox(height: 20.h), // Adjust spacing as needed
              itemCount: (coaches.length / 2).ceil(), // Show coaches in pairs
              itemBuilder: (context, index) {
                final firstIndex = index * 2;
                final secondIndex = firstIndex + 1;
                final hasSecondCoach = secondIndex < coaches.length;

                return Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 8.w),
                      child: coachSpotLightWidget(
                        context: context,
                        ageGroup:
                            "Age ${coaches[firstIndex].ageFrom ?? '6'}-${coaches[firstIndex].ageTo ?? '12'}",
                        name:
                            coaches[firstIndex].displayName ?? "Unknown Coach",
                        imageUrl: coaches[firstIndex].mainImage?.url ??
                            ImagePath.coach,
                        location:
                            coaches[firstIndex].address?.city ?? "Upon Request",
                        price:
                            "From ${(coaches[firstIndex].center as CenterData?)?.priceFrom?.toStringAsFixed(0) ?? '12'}",
                        rating: coaches[firstIndex].rating ?? 0.0,
                        skills: coaches[firstIndex]
                                .skill
                                ?.map((s) => s.skillname)
                                .join(", ") ??
                            "No skills listed",
                      ),
                    ),
                    SizedBox(width: 27.w),
                    if (hasSecondCoach)
                      coachSpotLightWidget(
                        context: context,
                        ageGroup:
                            "Age ${coaches[secondIndex].ageFrom ?? '6'}-${coaches[secondIndex].ageTo ?? '12'}",
                        name:
                            coaches[secondIndex].displayName ?? "Unknown Coach",
                        imageUrl: coaches[secondIndex].mainImage?.url ??
                            ImagePath.coach,
                        location: coaches[secondIndex].address?.city ??
                            "Upon Request",
                        price:
                            "From ${(coaches[secondIndex].center as CenterData?)?.priceFrom?.toStringAsFixed(0) ?? '12'}",
                        rating: coaches[secondIndex].rating ?? 0.0,
                        skills: coaches[secondIndex]
                                .skill
                                ?.map((s) => s.skillname)
                                .join(", ") ??
                            "No skills listed",
                      ),
                  ],
                );
              },
            )
          ],
        ),
      ),
    ));
  }
}
