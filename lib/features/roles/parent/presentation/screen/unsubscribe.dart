import 'package:class_z/core/imports.dart';

class Unsubscribe extends StatelessWidget {
  const Unsubscribe({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(
          title: "Current Plan",
          leading: customBackButton(),
        ),
        bottomNavigationBar: customBottomButton(
          buttonText: "Unsubscribe",
          color: Colors.white,
          textColor: AppPallete.darkGrey,
          onTap: () {
            NavigatorService.pushNamed(AppRoutes.unsubscribeMessage);
          },
        ),
        body: Padding(
          padding: EdgeInsets.only(left: 20.w, right: 19.w, top: 49.h),
          child: Stack(
            children: [
              Positioned(
                top: 12.h,
                child: Container(
                  height: 100.h,
                  width: 391.w,
                  decoration: BoxDecoration(
                    color: AppPallete.currentColor,
                    borderRadius: BorderRadius.circular(20.r),
                    boxShadow: [
                      BoxShadow(
                          blurRadius: 15,
                          color: Colors.black.withOpacity(0.1))
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 23.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: 11.h),
                            Text(
                              "Current Plan",
                              style: TextStyle(
                                  fontSize: 13.sp,
                                  color: AppPallete.red,
                                  fontWeight: FontWeight.w500),
                            ),
                            Text(
                              "Exclusive Club",
                              style: TextStyle(
                                  fontSize: 17.sp,
                                  color: AppPallete.darkGrey,
                                  fontWeight: FontWeight.w700),
                            ),
                            Text(
                              "8400/ Month",
                              style: TextStyle(
                                  fontSize: 15.sp,
                                  color: AppPallete.darkGrey,
                                  fontWeight: FontWeight.w300),
                            ),
                            Text(
                              "~17-24 classes",
                              style: TextStyle(
                                  fontSize: 15.sp,
                                  color: AppPallete.darkGrey,
                                  fontWeight: FontWeight.w300),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(right: 25.w),
                        child: Row(
                          children: [
                            Text(
                              "292",
                              style: TextStyle(
                                  fontSize: 35.sp,
                                  color: AppPallete.darkGrey,
                                  fontWeight: FontWeight.w500),
                            ),
                            SizedBox(width: 7.w),
                            customSvgPicture(
                                imagePath: ImagePath.zSvg,
                                height: 45.h,
                                width: 45.w,
                                color: AppPallete.darkGrey)
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                top: 0,
                right: 25.w,
                child: Container(
                  height: 23.h,
                  width: 73.w,
                  decoration: BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: Center(
                    child: Text(
                      "10% OFF",
                      style: TextStyle(
                          fontSize: 13.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ));
  }
}
