import 'package:class_z/core/common/data/models/user_address_model.dart';
import 'package:class_z/core/imports.dart';

class MyAddress extends StatefulWidget {
  const MyAddress({super.key});

  @override
  State<MyAddress> createState() => _MyAddressState();
}

class _MyAddressState extends State<MyAddress> {
  String titleText = "Edit";
  final flatDescriptionController = TextEditingController();
  final streetDescriptionController = TextEditingController();
  final districtDescriptionController = TextEditingController();
  final regionDescriptionController = TextEditingController();
  final countryDescriptionController = TextEditingController();
  List<UserAddress>? addresses = [];
  bool newAddressIsDefault = false;

  @override
  initState() {
    super.initState();
    _fetchLocation();
  }

  @override
  void dispose() {
    flatDescriptionController.dispose();
    streetDescriptionController.dispose();
    districtDescriptionController.dispose();
    regionDescriptionController.dispose();
    countryDescriptionController.dispose();
    super.dispose();
  }

  // Function to unfocus and hide keyboard
  void _unfocus() {
    FocusScope.of(context).unfocus();
  }

  int selectedAddressIndex = 0;

// Inside your widget's initState or where the data is first loaded
  void _initializeSelectedAddress() {
    // Find the index of the default address if any
    selectedAddressIndex = addresses
            ?.indexWhere((address) => address.userAddressDefault == true) ??
        0;
  }

  // get numberOfChild => null;
  ParentData1? parent = locator<SharedRepository>().getParentData();

  void _fetchLocation() {
    addresses = locator<SharedRepository>().getParentData()?.location;
    _initializeSelectedAddress();
    setState(() {}); // Ensure UI updates after fetching
  }

  void _clearForm() {
    flatDescriptionController.clear();
    streetDescriptionController.clear();
    districtDescriptionController.clear();
    regionDescriptionController.clear();
    newAddressIsDefault = false;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // When tapped anywhere outside of a text field, dismiss the keyboard
      // onTap: _unfocus,
      child: Scaffold(
        // Allow the screen to resize when keyboard appears
        resizeToAvoidBottomInset: true,
        body: MultiBlocListener(
          listeners: [
            BlocListener<UserBloc, UserState>(
              listener: (context, state) {
                print('current state: $state');
                if (state is UserLoadingState) {
                  loadingState(context: context);
                } else
                  hideLoadingDialog(context);
                if (state is UserErrorState) {
                  errorState(context: context, error: state.message);
                }
                if (state is UpdateAddressSuccessState) {
                  if (state.success == true) {
                    setState(() {
                      titleText = "Edit";
                      _fetchLocation();
                    });
                  } else
                    errorState(
                        context: context, error: 'Something went wrong!');
                }
                if (state is DeleteAddressSuccessState) {
                  if (state.success == true) {
                    setState(() {
                      titleText = "Edit";
                      _fetchLocation();
                    });
                    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                        content: Text('Address deleted successfully')));
                  } else
                    errorState(
                        context: context, error: 'Failed to delete address');
                }
                if (state is ChangeDefaultAddressSuccessState) {
                  if (state.success == true) {
                    _fetchLocation();
                    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                        content: Text('Default address updated successfully')));
                  } else
                    errorState(
                        context: context,
                        error: 'Failed to update default address');
                }
              },
            ),
            BlocListener<AuthBloc, AuthState>(
              listener: (context, state) {
                if (state is AuthLoading) {
                  loadingState(context: context);
                } else {
                  hideLoadingDialog(context);
                }

                if (state is AuthError) {
                  errorState(context: context, error: state.message);
                }

                if (state is ParnetAccountInfoSuccessState) {
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context); // Close modal on success
                  }
                  setState(() {
                    titleText = "Edit";
                    _clearForm(); // Clear form after successful addition
                    _fetchLocation();
                  });
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                      content: Text('Address added successfully')));
                }
              },
            ),
          ],
          child: RefreshIndicator(
            onRefresh: () async {
              _unfocus(); // Also dismiss keyboard on refresh
              _fetchLocation();
            },
            child: SingleChildScrollView(
              // Dismiss keyboard when user scrolls
              keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _topBar(context: context),
                  Padding(
                    padding:
                        EdgeInsets.only(left: 22.w, right: 25.w, top: 23.h),
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: InkWell(
                          onTap: () {
                            setState(() {
                              titleText = titleText == "Edit" ? "Done" : "Edit";
                            });
                          },
                          child: customtext(
                              context: context,
                              newYear: titleText,
                              font: 17.sp,
                              color: AppPallete.change,
                              weight: FontWeight.w400)),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 5.h),
                    child: customDivider(padding: 0),
                  ),
                  Padding(
                      padding: EdgeInsets.only(left: 22.w, top: 22.h),
                      child: customtext(
                          context: context,
                          newYear:
                              "Choose the address to receive your free STEAM kits",
                          font: 17.sp,
                          weight: FontWeight.w500)),
                  if (addresses?.isNotEmpty == true)
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.only(top: 19.h),
                      itemCount: addresses?.length ?? 0,
                      itemBuilder: (context, index) {
                        final address = addresses?[index];
                        print(address?.userAddressDefault);
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildAddressTile(
                              context: context,
                              value: index,
                              address: address,
                              title:
                                  "${address?.flatFloorBlock}\n${address?.buildingEstate}\n${address?.district}, ${address?.region}",
                              editable: titleText == "Done",
                              onIconTap: () {
                                context.read<UserBloc>().add(DeleteAddressEvent(
                                      id: parent?.id ?? '',
                                      addressId: address?.userAddressId ?? '',
                                    ));
                              },
                              onChanged: (int? newValue) {
                                if (newValue != null &&
                                    newValue != selectedAddressIndex) {
                                  context
                                      .read<UserBloc>()
                                      .add(ChangeDefaultAddressEvent(
                                        id: parent?.id ?? '',
                                        addressId: address?.userAddressId ?? '',
                                      ));
                                }
                              },
                            ),
                            if (index != (addresses?.length ?? 0) - 1)
                              customDivider(width: 406.w, padding: 12),
                            if (index != (addresses?.length ?? 0) - 1)
                              SizedBox(
                                height: 20.h,
                              ),
                          ],
                        );
                      },
                    ),
                  if (addresses?.isEmpty == true)
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 40.h),
                      child: Center(
                        child: customtext(
                          context: context,
                          newYear:
                              "No addresses found. Add your first address.",
                          font: 16.sp,
                          color: AppPallete.darkGrey,
                          weight: FontWeight.w400,
                        ),
                      ),
                    ),
                  Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 22.w, vertical: 20.h),
                    child: ElevatedButton(
                      onPressed: () => _showAddAddressModal(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppPallete.change,
                        minimumSize: Size(double.infinity, 50.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        'Add New Address',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showAddAddressModal(BuildContext context) {
    _clearForm(); // Clear form fields before showing
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (modalBuilderContext) => BlocProvider.value(
        value: context.read<AuthBloc>(),
        child: StatefulBuilder(
          builder: (BuildContext context, StateSetter modalSetState) {
            return Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                child: SingleChildScrollView(
                  child: _editOptionEnable(
                      context: context, modalSetState: modalSetState),
                ),
              ),
            );
          },
        ),
      ),
    ).whenComplete(() {
      _unfocus();
    });
  }

  Widget _topBar({required BuildContext context}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 19.w, top: 85.h),
          child: CustomIconButton(
            icon: Icons.arrow_back_ios,
            onPressed: () {
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              } else {
                NavigatorService.pushNamedAndRemoveUntil(
                    AppRoutes.middleProfileSwitch);
              }
            },
          ),
        ),
        SizedBox(height: 13.h),
        Padding(
          padding: EdgeInsets.only(left: 20.w, right: 32.w),
          child: customtext(
            context: context,
            newYear: "My Address",
            font: 30.sp,
            weight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildAddressTile(
      {required BuildContext context,
      required int value,
      required UserAddress? address,
      required String title,
      required bool editable,
      VoidCallback? onIconTap,
      required ValueChanged<int?> onChanged}) {
    // Use the actual default status from the address object
    bool isSelected = address?.userAddressDefault == true;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          editable == false
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Radio<int>(
                        activeColor: AppPallete.darkGrey,
                        value: value,
                        groupValue:
                            isSelected ? value : -1, // Use -1 when not selected
                        onChanged: onChanged),
                    customtext(
                      context: context,
                      newYear: "Default Address",
                      font: 15.sp,
                      weight: FontWeight.w400,
                    ),
                  ],
                )
              : Expanded(
                  child: InkWell(
                    onTap: onIconTap,
                    child: Icon(
                      Icons.remove_circle_rounded,
                      color: Colors.red,
                    ),
                  ),
                ),
          SizedBox(width: 31.w),
          Expanded(
            child: customtext(
              context: context,
              newYear: title,
              font: 15.sp,
              weight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _editOptionEnable(
      {required BuildContext context, required StateSetter modalSetState}) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Padding(
          padding: EdgeInsets.symmetric(vertical: 8.h),
          child: customtext(
            context: context,
            newYear: "Add a New Address",
            font: 20.sp,
            weight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 10.h),
        SwitchListTile(
          title: Text('Set as default address'),
          value: newAddressIsDefault,
          onChanged: (bool value) {
            modalSetState(() {
              newAddressIsDefault = value;
            });
          },
          activeColor: AppPallete.change,
          contentPadding: EdgeInsets.zero,
        ),
        SizedBox(
          height: 18.h,
        ),
        AuthField(
          controller: flatDescriptionController,
          hintText: "Flat description (required)",
          height: 30.h,
          isObsecureText: false,
          keyboard: TextInputType.streetAddress,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Flat description is required';
            }
            return null;
          },
        ),
        SizedBox(
          height: 20.h,
        ),
        AuthField(
          controller: streetDescriptionController,
          hintText: "Street details (required)",
          height: 30.h,
          isObsecureText: false,
          keyboard: TextInputType.streetAddress,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Street details are required';
            }
            return null;
          },
        ),
        SizedBox(
          height: 20.h,
        ),
        Row(
          children: [
            Expanded(
              child: AuthField(
                controller: districtDescriptionController,
                hintText: "District (required)",
                height: 30.h,
                isObsecureText: false,
                keyboard: TextInputType.streetAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'District is required';
                  }
                  return null;
                },
              ),
            ),
            SizedBox(
              width: 17.w,
            ),
            Expanded(
              child: AuthField(
                controller: regionDescriptionController,
                hintText: "Region (required)",
                height: 30.h,
                isObsecureText: false,
                keyboard: TextInputType.streetAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Region is required';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        SizedBox(
          height: 18.h,
        ),
        Align(
          alignment: Alignment.centerRight,
          child: InkWell(
            onTap: () {
              // Validate all fields are filled
              final flatFloor = flatDescriptionController.text.trim();
              final buildingEstate = streetDescriptionController.text.trim();
              final district = districtDescriptionController.text.trim();
              final region = regionDescriptionController.text.trim();

              // Check if any required field is empty
              if (flatFloor.isEmpty ||
                  buildingEstate.isEmpty ||
                  district.isEmpty ||
                  region.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                    content: Text('Please fill all required fields')));
                return;
              }

              print("Creating address with default: ${newAddressIsDefault}");

              // Create a location object with the structure expected by the server
              Map<String, dynamic> locationData = {
                "flatFloorBlock": flatFloor,
                "buildingEstate": buildingEstate,
                "district": district,
                "region": region,
                "country": "Hong Kong",
                "default": newAddressIsDefault
              };

              // Create the update data with just the new location
              Map<String, dynamic> updateData = {
                "location": [locationData]
              };

              print("Sending address data: $updateData");

              // Use AuthBloc with ParentInfoCompleteEvent instead
              context
                  .read<AuthBloc>()
                  .add(ParentInfoCompleteEvent(updateData: updateData));
            },
            child: customtext(
                context: context,
                newYear: "save",
                font: 17.sp,
                color: AppPallete.change,
                weight: FontWeight.w400),
          ),
        ),
      ]),
    );
  }
}
