import 'package:class_z/core/imports.dart';
import 'package:class_z/features/roles/parent/presentation/widgets/images_for_moment.dart';

class Moments extends StatefulWidget {
  final String childId;
  const Moments({super.key, required this.childId});

  @override
  State<Moments> createState() => _MomentsState();
}

class _MomentsState extends State<Moments> {
  bool _isInvalidChildId = false;

  @override
  void initState() {
    super.initState();
    print("Moments screen initialized with childId: ${widget.childId}");

    // Basic validation to prevent API errors
    if (widget.childId.isEmpty) {
      setState(() {
        _isInvalidChildId = true;
      });
      print("Error: Empty childId provided to Moments screen");
    } else {
      context.read<ReviewBloc>().add(GetMomentsEvent(childId: widget.childId));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomAppBar(
              title: "Moments",
              leading: customBackButton(),
            ),
            if (_isInvalidChildId)
              Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Column(
                    children: [
                      Icon(Icons.error_outline, size: 48, color: Colors.red),
                      SizedBox(height: 16),
                      customtext(
                        context: context,
                        newYear: "No child selected",
                        font: 20.sp,
                        weight: FontWeight.w500,
                      ),
                      SizedBox(height: 8),
                      customtext(
                        context: context,
                        newYear: "Please go back and select a child first",
                        font: 16.sp,
                        weight: FontWeight.w400,
                      ),
                      SizedBox(height: 24),
                      Button(
                        buttonText: "Go Back",
                        color: AppPallete.secondaryColor,
                        height: 43.h,
                        width: 170.w,
                        onPressed: () {
                          Navigator.pop(context);
                        },
                      ),
                    ],
                  ),
                ),
              )
            else
              BlocConsumer<ReviewBloc, ReviewState>(
                listener: (context, state) {
                  if (state is ReviewLoadingState)
                    loadingState(context: context);
                  else
                    hideLoadingDialog(context);
                  if (state is ReviewErrorState)
                    errorState(context: context, error: state.message);
                },
                builder: (context, state) {
                  if (state is GetMomentsSuccessState) {
                    if (state.moments.isEmpty) {
                      return Center(
                        child: Padding(
                          padding: EdgeInsets.all(20.0),
                          child: Column(
                            children: [
                              Icon(Icons.photo_album_outlined,
                                  size: 48, color: Colors.grey),
                              SizedBox(height: 16),
                              customtext(
                                context: context,
                                newYear: "No moments found",
                                font: 20.sp,
                                weight: FontWeight.w500,
                              ),
                              SizedBox(height: 8),
                              customtext(
                                context: context,
                                newYear:
                                    "There are no moments recorded for this child yet",
                                font: 16.sp,
                                weight: FontWeight.w400,
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    }

                    return ListView.builder(
                      padding:
                          EdgeInsets.symmetric(horizontal: 17.5, vertical: 46),
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      itemCount: state.moments.length,
                      itemBuilder: (context, index) {
                        final moment = state.moments[index];
                        if (moment.images == null || moment.images!.isEmpty) {
                          return SizedBox();
                        }
                        // Get the weekday name
                        String weekday =
                            DateFormat('EEEE').format(moment.date!);

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            customtext(
                                context: context,
                                newYear: weekday,
                                font: 16.sp,
                                weight: FontWeight.w500,
                                color: AppPallete.color136),
                            customtext(
                                context: context,
                                newYear: dateGenerator(
                                    date: moment.date!, monthName: true),
                                font: 24.sp,
                                weight: FontWeight.w600),
                            imagesForMoments(
                                images: moment.images ?? [], context: context),
                            SizedBox(
                              height: 12,
                            )
                          ],
                        );
                      },
                    );
                  }

                  // Default loading state
                  return Center(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 100),
                      child: CircularProgressIndicator(),
                    ),
                  );
                },
              )
          ],
        ),
      ),
    );
  }
}
