import 'package:class_z/core/imports.dart';

class <PERSON><PERSON>iew extends StatefulWidget {
  final String? coachId;
  const CoachView({this.coachId, super.key});

  @override
  State<CoachView> createState() => _CoachViewState();
}

class _CoachViewState extends State<CoachView> {
  @override
  void initState() {
    super.initState();

    Future.microtask(() {
      context
          .read<CoachBloc>()
          .add(GetCoachInfoByIdEvent(coachId: widget.coachId ?? ''));
    });

    Future.microtask(() {
      context
          .read<ReviewBloc>()
          .add(GetReviewByIdEvent(id: widget.coachId ?? "", type: "coach"));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: BlocConsumer<CoachBloc, CoachState>(
          listener: (context, state) {
            if (state is LoadingCoachState) {
              loadingState(context: context);
            } else
              hideLoadingDialog(context);
            if (state is ErrorCoachState) {
              errorState(context: context, error: state.message);
            }
          },
          builder: (context, state) {
            if (state is GetCoachInfoByIdSuccessState) {
              return _buildCoachView(context, state.coach);
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  Widget _buildCoachView(BuildContext context, CoachModel? coach) {
    return Padding(
      padding: EdgeInsets.only(left: 23.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 85.h,
          ),
          customBackButton(),
          SizedBox(
            height: 11.h,
          ),
          _picAndSEN(
              context: context,
              name: coach?.displayName ?? "",
              image: coach?.mainImage?.url),
          customtext(
              context: context,
              newYear: "BIO",
              font: 20.sp,
              weight: FontWeight.w600),
          Container(
            margin: EdgeInsets.only(right: 34.w),
            child: Text(
              coach?.description ?? "",
              maxLines: 4,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(
            height: 18.h,
          ),
          Transform.translate(
              offset: Offset(-16.w, 0),
              child: customDivider(width: 406.w, padding: 0)),
          SizedBox(
            height: 18.h,
          ),
          customTitle(context, AppText.classLanguage, 20),
          SizedBox(
            height: 12.h,
          ),
          ListView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                String? language = coach?.languages![index];
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _classLanguage(context, language!),
                    const SizedBox(
                      height: 10,
                    ),
                  ],
                );
              },
              itemCount: coach?.languages?.length ?? 0),
          const SizedBox(
            height: 18,
          ),
          Transform.translate(
              offset: Offset(-16, 0), child: customDivider(padding: 0)),
          const SizedBox(
            height: 18,
          ),
          customtext(
              context: context,
              newYear: "Skills",
              font: 20.sp,
              weight: FontWeight.w600),
          const SizedBox(
            height: 10,
          ),
          ListView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                var skill = coach?.skill![index];
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _skills(
                        context: context,
                        skillname: skill?.skillname ?? "",
                        experience: skill?.skillsince ?? "",
                        verification: skill?.skillnameofassociation ?? ""),
                    const SizedBox(
                      height: 10,
                    )
                  ],
                );
              },
              itemCount: coach?.skill?.length ?? 0),
          SizedBox(
            height: 18.h,
          ),
          Transform.translate(
              offset: Offset(-16.w, 0),
              child: customDivider(width: 406.w, padding: 0)),
          SizedBox(
            height: 18.h,
          ),
          customTitle(context, "Review", 20.sp),
          SizedBox(
            height: 18.h,
          ),
          BlocBuilder<ReviewBloc, ReviewState>(
            builder: (context, state) {
              if (state is ReviewLoadingState)
                CircularProgressIndicator();
              else if (state is ReviewErrorState)
                errorState(context: context, error: state.message);
              else if (state is GetReviewByCoachIdSuccessState) {
                return state.reviews.length > 0
                    ? SizedBox(
                        height: 110,
                        child: ListView.builder(
                          shrinkWrap: true,
                          scrollDirection: Axis
                              .horizontal, // Make the list scroll horizontally
                          itemCount: state.reviews.length,
                          itemBuilder: (context, index) {
                            final review = state.reviews[index];
                            String formattedDate =
                                DateFormat('dd/MM/yyyy').format(review.date!);

                            return reviewCard(
                                context: context,
                                title: review.title ?? "",
                                rating: review.rating.toString(),
                                comment: review.comment ?? "",
                                dateTime: formattedDate);
                          },
                        ),
                      )
                    : Center(
                        child: Text('no Review'),
                      );
              }
              return Center(
                child: Text('no Review'),
              );
            },
          ),
          SizedBox(
            height: 18.h,
          ),
          Transform.translate(
              offset: Offset(-16.w, 0),
              child: customDivider(width: 406.w, padding: 0)),
          SizedBox(
            height: 18.h,
          ),
          customtext(
              context: context,
              newYear: "Album",
              font: 20.sp,
              weight: FontWeight.w700),
          SizedBox(
            height: 18.h,
          ),
          coach?.images?.length == 0
              ? Center(
                  child: Text('No images to show'),
                )
              : Transform.translate(
                  offset: Offset(-12.w, 0.h),
                  child: SizedBox(
                    height: 100.h,
                    child: ListView.separated(
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (context, index) {
                          final image = coach?.images?[index];
                          return CustomImageBuilder(
                            height: 100.h,
                            width: 180.w,
                            borderRadius: 20.r,
                            imagePath: imageStringGenerator(
                                imagePath: image?.url ?? ''),
                          );
                        },
                        separatorBuilder: (context, index) {
                          return SizedBox(
                            width: 10.w,
                          );
                        },
                        itemCount: coach?.images?.length ?? 0),
                  ),
                ),
          SizedBox(
            height: 18.h,
          ),
          Transform.translate(
              offset: Offset(-16.w, 0),
              child: customDivider(width: 406.w, padding: 0)),
          SizedBox(
            height: 18.h,
          ),
          customtext(
              context: context,
              newYear: "Accreditation",
              font: 20.sp,
              weight: FontWeight.w600),
          SizedBox(
            height: 18.h,
          ),
          ListView.separated(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                var accredation = coach?.accredation![index];
                return _accreditation_row(
                    context: context,
                    year: accredation?.year ?? "",
                    title: accredation?.name ?? "",
                    isCheck: true,
                    text1: accredation?.result ?? "",
                    text2: "Hong kong SR");
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 10.h,
                );
              },
              itemCount: coach?.accredation?.length ?? 0),
          SizedBox(
            height: 18.h,
          ),
          Transform.translate(
              offset: Offset(-16.w, 0),
              child: customDivider(width: 406.w, padding: 0)),
          SizedBox(
            height: 18.h,
          ),
          customtext(
              context: context,
              newYear: "Experience",
              font: 20.sp,
              weight: FontWeight.w600),
          SizedBox(
            height: 18.h,
          ),
          ListView.separated(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                var experience = coach?.experience![index];
                return _accreditation_row(
                    context: context,
                    year: experience?.from ?? "",
                    title: experience?.title ?? "",
                    isCheck: true,
                    text1: experience?.organization ?? "",
                    text2: "Hong kong SR");
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 10.h,
                );
              },
              itemCount: coach?.experience?.length ?? 0),
          SizedBox(
            height: 18.h,
          ),
          Transform.translate(
              offset: Offset(-16.w, 0),
              child: customDivider(width: 406.w, padding: 0)),
          SizedBox(
            height: 18.h,
          ),
          customTitle(context, "Working Centre", 20.sp),
          SizedBox(
            height: 18.h,
          ),
          Transform.translate(
              offset: Offset(-16.w, 0),
              child: buildRowGallery(
                context: context,
                startAge: coach?.center?.startAge ?? '',
                center: coach?.center?.displayName ?? '',
                location: coach?.center?.address?.address1 ?? "",
                imageHeight: 163.h,
                imageWidth: 331.w,
                category: coach?.center?.description ?? '',
                imagepath: imageStringGenerator(
                    imagePath: coach?.center?.mainImage?.url ?? ''),
                rating: coach?.center?.rating ?? 0,
                onTap: () {
                  NavigatorService.pushNamed(AppRoutes.centreView, arguments: {
                    'center': coach?.center,
                    'bottomView': false
                  });
                },
              )),
          SizedBox(
            height: 12.h,
          )
        ],
      ),
    );
  }

  Widget _picAndSEN(
      {required BuildContext context, required String name, String? image}) {
    return SizedBox(
      height: heightRatio(height: 163) * getHeight(context: context),
      width: double.infinity,
      child: Stack(
        children: [
          Positioned(
              top: 105.h,
              left: 0,
              child: customDivider(width: 406.w, padding: 0)),
          Positioned(
            top: 0,
            left: 0,
            child: CustomImageBuilder(
              imagePath: imageStringGenerator(imagePath: image ?? ''),
              height: 141.h,
              width: 141.h,
              borderRadius: 141.w,
            ),
          ),
          Positioned(
            top: 63.h,
            left: 154.w,
            child: customtext(
                context: context,
                newYear: name,
                font: 25.sp,
                weight: FontWeight.w700),
          ),
        ],
      ),
    );
  }

  Widget _buildBadminton(
      {required BuildContext context,
      required bool isCheck,
      required String text,
      double? padding}) {
    return Row(
      children: [
        Text(
          text,
          style: const TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
        ),
        SizedBox(
          width: 5.w,
        ),
        if (isCheck == true)
          Icon(
            Icons.verified,
            color: AppPallete.secondaryColor,
            size: 20.r,
          )
      ],
    );
  }

  Widget _classLanguage(BuildContext context, String text,
      {bool? check, Color? color, double? font}) {
    return Row(
      children: [
        if (check != null)
          const Icon(
            Icons.check_box,
            size: 16,
          )
        else
          const Icon(
            Icons.check,
            size: 16,
          ),
        Padding(
          padding: EdgeInsets.only(left: 8.w),
          child: Text(
            text,
            style: TextStyle(
                fontSize: font ?? 15.sp, color: color ?? Colors.black),
          ),
        )
      ],
    );
  }

  Widget _skills(
      {required BuildContext context,
      required String skillname,
      required String experience,
      required String verification}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        customtext(
            context: context,
            newYear: skillname,
            font: 15.sp,
            weight: FontWeight.w700),
        SizedBox(
          height: 10.h,
        ),
        customtext(
            context: context,
            newYear: experience,
            font: 15.sp,
            weight: FontWeight.w700),
        SizedBox(
          height: 10.h,
        ),
        customtext(
            context: context,
            newYear: verification,
            font: 15,
            weight: FontWeight.w500),
      ],
    );
  }

// ignore: non_constant_identifier_names
  Widget _accreditation_row(
      {required BuildContext context,
      required String year,
      required String title,
      required String text1,
      required bool isCheck,
      required String text2}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customtext(
            context: context,
            newYear: year,
            font: 15.sp,
            weight: FontWeight.w500),
        SizedBox(
          width: 9.w,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBadminton(
                context: context, isCheck: isCheck, text: title, padding: 0),
            SizedBox(
              height: 10.h,
            ),
            customtext(
                context: context,
                newYear: text1,
                font: 15.sp,
                weight: FontWeight.w500),
            SizedBox(
              height: 10.h,
            ),
            customtext(
                context: context,
                newYear: text2,
                font: 13.sp,
                weight: FontWeight.w400),
          ],
        ),
      ],
    );
  }
}
