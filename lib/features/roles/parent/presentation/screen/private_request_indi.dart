import 'package:class_z/core/imports.dart';

class PrivateRequestIndividual extends StatefulWidget {
  const PrivateRequestIndividual({super.key});

  @override
  State<PrivateRequestIndividual> createState() =>
      _PrivateRequestIndividualState();
}

int? _sen;

class _PrivateRequestIndividualState extends State<PrivateRequestIndividual> {
  List<bool> isSelected = [true, false];
  String? currentText;
  final List<String> month = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'August'
  ];
  final List<String> days = ['1', '2', '3', '4', '5', '6', '8', '9', '10'];
  final List<String> times = [
    '6:00 AM',
    '7:00 AM',
    '8:00 AM',
    '9:00 AM',
    '10:00 AM',
    '11:00 AM',
    '12:00 PM',
    '1:00 PM',
    '2:00 PM',
    '3:00 PM',
    '4:00 PM',
    '5:00 PM',
    '6:00 PM',
    '7:00 PM',
    '8:00 PM',
    '9:00 PM',
    '10:00 PM',
  ];
  int _mode = 1;
  int _class = 1;

  final organizationController = TextEditingController();
  final numberOfStudentController = TextEditingController();
  final addresline1Controller = TextEditingController();
  final addresline2Controller = TextEditingController();
  final cityController = TextEditingController();
  final regionController = TextEditingController();
  final remarkController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
        title: "Send Request",
        leading: customBackButton(),
      ),
      bottomNavigationBar: SizedBox(
          height: 76.h,
          width: 430.w,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              customDivider(width: 430.w, padding: 0),
              SizedBox(
                height: 13.h,
              ),
              Button(
                  height: 49.h,
                  width: 289.w,
                  buttonText: "Ask for Price",
                  shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                  onPressed: () {
                    NavigatorService.pushNamed(AppRoutes.pending);
                  },
                  color: AppPallete.secondaryColor),
            ],
          )),
      body: SingleChildScrollView(
          child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildProgressSteps(
              context: context,
              cont1: AppPallete.secondaryColor,
              cont2: AppPallete.secondaryColor,
              cont3: AppPallete.dividerTime),
          SizedBox(
            height: 19.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 21.w),
            child: _buildModeHeader(context),
          ),
          Padding(
            padding: EdgeInsets.only(left: 21.w),
            child: _buildModeOptions(context),
          ),
          SizedBox(
            height: 17.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 21.w),
            child: customtext(
              context: context,
              newYear: "Class details",
              font: 20.sp,
              weight: FontWeight.w600,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 21.w),
            child: customtext(
              context: context,
              newYear: "The class is arranged as the following",
              font: 13.sp,
              weight: FontWeight.w500,
            ),
          ),
          SizedBox(
            height: 20.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 21.w),
            child: customtext(
              context: context,
              newYear: "Watercolour (intermediate)",
              font: 20.sp,
              weight: FontWeight.w500,
            ),
          ),
          SizedBox(
            height: 22.h,
          ),
          Center(
            child: ToggleButtons(
              borderRadius: BorderRadius.circular(20.r),
              selectedBorderColor: Colors.blue,
              selectedColor: Colors.white,
              fillColor: Colors.blue,
              color: Colors.black,
              constraints: BoxConstraints(minHeight: 27.h, minWidth: 100.0),
              isSelected: isSelected,
              onPressed: (int index) {
                setState(() {
                  for (int i = 0; i < isSelected.length; i++) {
                    isSelected[i] = i == index;
                  }
                });
              },
              children: <Widget>[
                Text(
                  'Individual',
                  style:
                      TextStyle(fontSize: 15.sp, fontWeight: FontWeight.w500),
                ),
                Text(
                  'Organization',
                  style:
                      TextStyle(fontSize: 15.sp, fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 22.h,
          ),
          if (!isSelected[0]) _organisation(),
          if (isSelected[0])
            _individual(
                imagePath: ImagePath.coach,
                studentName: "Charlie Own",
                studentNumber: "+852 1234 5678",
                dateTime: DateTime.now()),
          _rest()
        ],
      )),
    );
  }

  Widget _individual({
    required String imagePath,
    required String studentName,
    required String studentNumber,
    required DateTime dateTime,
  }) {
    String studentDate = DateFormat('dd/MM/yyyy').format(dateTime);
    return Center(
        child: Container(
            height: 83.h,
            width: 325.w,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                color: Colors.white,
                boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
            child: Stack(children: [
              Positioned(
                top: 6.h,
                left: 13.w,
                child: CustomImageBuilder(
                    imagePath: imagePath,
                    height: 71.h,
                    width: 71.w,
                    borderRadius: 99.r),
              ),
              Positioned(
                  top: 10.h,
                  left: 103.5.w,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      customtext(
                          context: context,
                          newYear: studentName,
                          font: 14.sp,
                          weight: FontWeight.w500),
                      SizedBox(
                        height: 10.h,
                      ),
                      customtext(
                          context: context,
                          newYear: studentNumber,
                          font: 14.sp,
                          weight: FontWeight.w500),
                      SizedBox(
                        height: 10.h,
                      ),
                      customtext(
                          context: context,
                          newYear: studentDate,
                          font: 14.sp,
                          weight: FontWeight.w500),
                      SizedBox(
                        height: 11.h,
                      ),
                    ],
                  )),
              Positioned(
                top: 10.h,
                right: 20.h,
                child: GestureDetector(
                  onTap: () {
                    NavigatorService.pushNamed(AppRoutes.changeChild);
                  },
                  child: customtext(
                      context: context,
                      newYear: "change",
                      font: 13.sp,
                      weight: FontWeight.w500,
                      color: AppPallete.change),
                ),
              )
            ])));
  }

  Widget _organisation() {
    return Padding(
      padding: EdgeInsets.only(left: 21.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          customRequiredText(
              context: context,
              title: "Name of Organization",
              font: 20.sp,
              weight: FontWeight.w500),
          SizedBox(
            height: 22.h,
          ),
          AuthField(
            controller: organizationController,
            height: 30.h,
            width: 332.w,
            hintText: "Address line 1",
          ),
        ],
      ),
    );
  }

  Widget _buildModeHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            customtext(
              context: context,
              newYear: "Mode",
              font: 20.sp,
              weight: FontWeight.w600,
            ),
            customtext(
              context: context,
              newYear: "Please select your mode of coaching",
              font: 13.sp,
              weight: FontWeight.w500,
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              //     height: 49.h,
              width: 170.w,
              padding: EdgeInsets.only(left: 5.w, top: 11.h),
              decoration: BoxDecoration(
                  color: AppPallete.paleGrey,
                  borderRadius: BorderRadius.circular(5.r)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  customtext(
                    context: context,
                    newYear: "ABC Testing Center",
                    font: 13.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(height: 5.h),
                  Container(
                    decoration: BoxDecoration(
                        color: AppPallete.scheduleColor2,
                        borderRadius: BorderRadius.circular(20.r)),
                    child: customtext(
                      context: context,
                      newYear: "Tsim Sha Tsui",
                      font: 13.sp,
                      weight: FontWeight.w400,
                    ),
                  ),
                  SizedBox(height: 5.h),
                ],
              ),
            ),
            customtext(
                context: context,
                newYear: "detailed location",
                font: 12.sp,
                weight: FontWeight.w400,
                color: AppPallete.change),
          ],
        ),
      ],
    );
  }

  Widget _buildModeOptions(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 22.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildRadioOption(
            context: context,
            value: 0,
            selectedMode: _mode,
            title: "On-Site Coaching",
            subtitle: "Learn at the place arranged by the coach",
            onChanged: (int? newValue) {
              setState(() {
                _mode = newValue!;
              });
            },
          ),
          SizedBox(height: 3.h),
          customDivider(padding: 0, width: 343.w),
          SizedBox(height: 3.h),
          _buildRadioOption(
            context: context,
            value: 1,
            selectedMode: _mode,
            title: "In-Home Coaching",
            subtitle: "Private session, learn at your place",
            onChanged: (int? newValue) {
              setState(() {
                _mode = newValue!;
              });
            },
          ),
          SizedBox(height: 3.h),
          customDivider(padding: 0, width: 343.w),
          SizedBox(height: 3.h),
          _buildRadioOption(
            context: context,
            value: 2,
            selectedMode: _mode,
            title: "Online Coaching",
            subtitle: "Learn through video conferencing platform - Zoom",
            onChanged: (int? newValue) {
              setState(() {
                _mode = newValue!;
              });
            },
          ),
          SizedBox(height: 3.h),
          customDivider(padding: 0, width: 343.w),
        ],
      ),
    );
  }

  Widget _buildRadioOption(
      {required BuildContext context,
      required int value,
      required int selectedMode,
      required String title,
      required String subtitle,
      required ValueChanged<int?> onChanged}) {
    return SizedBox(
      width: 343.w,
      height: 35.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Radio<int>(
              activeColor: AppPallete.darkGrey,
              value: value,
              groupValue: selectedMode,
              onChanged: onChanged),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 19.h,
                child: customtext(
                  context: context,
                  newYear: title,
                  font: 15.sp,
                  weight: FontWeight.w400,
                ),
              ),
              SizedBox(
                height: 15.h,
                child: customtext(
                  context: context,
                  newYear: subtitle,
                  font: 12.sp,
                  weight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRadioOptionForClassDetails(
      {required BuildContext context,
      required int value,
      required int? selectedMode,
      required String title,
      required ValueChanged<int?> onChanged}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          constraints: BoxConstraints.tightFor(width: 24.w, height: 24.h),
          child: Radio<int>(
            activeColor: AppPallete.darkGrey,
            value: value,
            groupValue: selectedMode,
            onChanged: onChanged,
          ),
        ),
        SizedBox(
          width: 300.w,
          child: customtext(
            context: context,
            newYear: title,
            font: 15.sp,
            weight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Widget _rest() {
    return Padding(
      padding: EdgeInsets.only(left: 21.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 22.h,
          ),
          customRequiredText(
              context: context,
              title: "Number of Student",
              font: 20.sp,
              weight: FontWeight.w500),
          SizedBox(
            height: 22.h,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              AuthField(
                controller: numberOfStudentController,
                height: 30.h,
                width: 127.w,
              ),
              SizedBox(
                width: 6.w,
              ),
              customtext(context: context, newYear: "students", font: 15.sp)
            ],
          ),
          SizedBox(
            height: 22.h,
          ),
          customRequiredText(
              context: context,
              title: "Teaching language",
              font: 20.sp,
              weight: FontWeight.w500),
          SizedBox(
            height: 22.h,
          ),
          AddLanguage(label: "Add language", height: 30.h, width: 158.w),
          SizedBox(
            height: 22.h,
          ),
          customRequiredText(
              context: context,
              title: "Date",
              font: 20.sp,
              weight: FontWeight.w500),
          SizedBox(
            height: 22.h,
          ),
          customtext(
            context: context,
            newYear: "Set the date for the class",
            font: 15.sp,
            weight: FontWeight.w500,
          ),
          SizedBox(
            height: 22.h,
          ),
          dayMonthCard(
              context: context,
              title1: "Month",
              title2: "Date",
              list1: month,
              list2: days),
          SizedBox(
            height: 20.h,
          ),
          customRequiredText(
              context: context,
              title: "Time",
              font: 20.sp,
              weight: FontWeight.w600),
          SizedBox(
            height: 20.h,
          ),
          customtext(
              context: context,
              newYear: "Set the time for the class",
              font: 15.sp,
              weight: FontWeight.w400),
          SizedBox(
            height: 20.h,
          ),
          dayMonthCard(
              context: context,
              title1: "From",
              title2: "To",
              list1: times,
              list2: times),
          SizedBox(
            height: 20.h,
          ),
          customtext(
            context: context,
            newYear: "Address",
            font: 20.sp,
            weight: FontWeight.w500,
          ),
          SizedBox(
            height: 20.h,
          ),
          _buildRadioOptionForClassDetails(
            context: context,
            value: 1,
            selectedMode: _class,
            title: "Flat A, 1/F, Block A, ABC Estate, Tseung Kwan ...",
            onChanged: (int? newValue) {
              setState(() {
                _class = newValue!;
              });
            },
          ),
          SizedBox(height: 10.h),
          customDivider(padding: 0, width: 326.w),
          SizedBox(height: 10.h),
          _buildRadioOptionForClassDetails(
            context: context,
            value: 2,
            selectedMode: _class,
            title: "Flat A, 1/F, Block A, ABC Estate, Tseung Kwan ...",
            onChanged: (int? newValue) {
              setState(() {
                _class = newValue!;
              });
            },
          ),
          SizedBox(height: 10.h),
          customDivider(padding: 0, width: 326.w),
          SizedBox(height: 10.h),
          _buildRadioOptionForClassDetails(
            context: context,
            value: 3,
            selectedMode: _class,
            title: "Flat A, 1/F, Block A, ABC Estate, Tseung Kwan ...",
            onChanged: (int? newValue) {
              setState(() {
                _class = newValue!;
              });
            },
          ),
          SizedBox(height: 10.h),
          customDivider(padding: 0, width: 326.w),
          SizedBox(height: 10.h),
          _buildRadioOptionForClassDetails(
            context: context,
            value: 4,
            selectedMode: _class,
            title:
                "Flat A, 1/F, Block A, ABC Estate, Tseung Kwanaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa ...",
            onChanged: (int? newValue) {
              setState(() {
                _class = newValue!;
              });
            },
          ),
          SizedBox(height: 10.h),
          customDivider(padding: 0, width: 326.w),
          SizedBox(height: 10.h),
          _buildRadioOptionForClassDetails(
            context: context,
            value: 5,
            selectedMode: _class,
            title: "Flat A, 1/F, Block A, ABC Estate, Tseung Kwan ...",
            onChanged: (int? newValue) {
              setState(() {
                _class = newValue!;
              });
            },
          ),
          SizedBox(height: 10.h),
          customDivider(padding: 0, width: 326.w),
          SizedBox(
            height: 20.h,
          ),
          AuthField(
            controller: addresline1Controller,
            height: 30.h,
            width: 332.w,
            hintText: "Address line 1",
          ),
          SizedBox(
            height: 20.h,
          ),
          AuthField(
            controller: addresline2Controller,
            height: 30.h,
            width: 332.w,
            hintText: "Address line 2",
          ),
          SizedBox(
            height: 20.h,
          ),
          Row(
            children: [
              AuthField(
                controller: cityController,
                height: 30.h,
                width: 157.w,
                hintText: "city",
              ),
              SizedBox(
                width: 18.w,
              ),
              AuthField(
                controller: regionController,
                height: 30.h,
                width: 157.w,
                hintText: "region",
              )
            ],
          ),
          SizedBox(
            height: 20.h,
          ),
          customtext(
              context: context,
              newYear: "Special Request",
              font: 15.sp,
              weight: FontWeight.w500),
          SizedBox(
            height: 15.h,
          ),
          _buildRadioOptionForClassDetails(
            context: context,
            value: 0,
            selectedMode: _sen,
            title: "SEN service",
            onChanged: (int? value) {
              setState(() {
                // Toggle selection: if the clicked value is the same as the current selected value, deselect it
                if (_sen == value) {
                  _sen = null; // Deselect if the same value is clicked
                } else {
                  _sen = value; // Select new value
                }
              });
            },
          ),
          SizedBox(
            height: 25.h,
          ),
          Row(
            children: [
              customtext(
                  context: context,
                  newYear: "Special Request",
                  font: 20.sp,
                  weight: FontWeight.w600),
              customtext(
                  context: context,
                  newYear: "(optional)",
                  color: AppPallete.greyWord,
                  font: 15.sp,
                  weight: FontWeight.w500),
            ],
          ),
          customtext(
              context: context,
              newYear: "Your remarks may be considered by the coach",
              font: 15.sp,
              weight: FontWeight.w500),
          SizedBox(
            height: 13.h,
          ),
          AuthField(
            controller: remarkController,
            height: 124.h,
            width: 380.w,
            hintText: "(Please remark)",
          ),
          SizedBox(
            height: 25.h,
          ),
          SizedBox(
            width: 332.w,
            child: customtext(
                context: context,
                newYear:
                    "You are free to change the time slot up to 10 days before the lesson starts",
                font: 13.sp,
                weight: FontWeight.w600),
          ),
          SizedBox(
            height: 9.h,
          ),
          SizedBox(
            width: 332.w,
            child: customtext(
                context: context,
                newYear:
                    "Changes made within 10 days prior to the lesson may be subject to a service charge",
                font: 13.sp,
                weight: FontWeight.w600),
          ),
          SizedBox(
            height: 100.h,
          ),
        ],
      ),
    );
  }
}
