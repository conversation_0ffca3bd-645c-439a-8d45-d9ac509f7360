// import 'dart:io';
// import 'package:class_z/core/imports.dart';
// import 'package:class_z/features/roles/parent/presentation/widgets/load_centers_button.dart';
// import 'package:path_provider/path_provider.dart';

// class CentersDataExample extends StatefulWidget {
//   const CentersDataExample({Key? key}) : super(key: key);

//   @override
//   State<CentersDataExample> createState() => _CentersDataExampleState();
// }

// class _CentersDataExampleState extends State<CentersDataExample> {
//   // The full centers JSON data provided by the user
//   final String centersJsonData = '''
// [{"mainImage":{"url":"/uploads/1745428247190-mainImage-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg"},"location":{"type":"Point","coordinates":[0,0]},"price":0,"ageRangeFrom":0,"ageRangeTo":0,"categories":[],"_id":"68091f17c739f053563e8bc8","baseUser":"679e504d0d7ec3d2868ee1c8","owner":"679e59b14e210f9f21eabf53","coachs":[],"rating":0,"reviewCount":0,"legalName":"s","displayName":"a","startAge":"","sen":false,"address":{"address1":"a","address2":"a","city":"a","region":"a","_id":"68091f17c739f053563e8bc9"},"companyNumber":"a","centerNumber":"a","email":"a","openingHour":[{"day":"Monday","openingTime":"03:00 AM","closingTime":"03:00 AM","_id":"68091f17c739f053563e8bca"},{"day":"Tuesday","openingTime":"05:00 AM","closingTime":"05:00 AM","_id":"68091f17c739f053563e8bcb"},{"day":"Wednesday","openingTime":"06:00 AM","closingTime":"06:00 AM","_id":"68091f17c739f053563e8bcc"},{"day":"Thursday","openingTime":"07:00 AM","closingTime":"07:00 AM","_id":"68091f17c739f053563e8bcd"},{"day":"Friday","openingTime":"08:00 AM","closingTime":"09:00 AM","_id":"68091f17c739f053563e8bce"},{"day":"Saturday","openingTime":"10:00 AM","closingTime":"10:00 AM","_id":"68091f17c739f053563e8bcf"},{"day":"Sunday","openingTime":"12:00 PM","closingTime":"12:00 PM","_id":"68091f17c739f053563e8bd0"}],"businessNumber":"a","businessCertificate":[{"url":"D:\\\\classZ_backend\\\\server\\\\uploads\\\\1745428246868-images (1)_1.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bd1"},{"url":"D:\\\\classZ_backend\\\\server\\\\uploads\\\\1745428246892-images_2.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bd2"},{"url":"/uploads/1745428247184-businessCertificate-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bd3"},{"url":"/uploads/1745428247185-businessCertificate-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bd4"}],"hkidCard":[{"url":"D:\\\\classZ_backend\\\\server\\\\uploads\\\\1745428246918-images (1)_3.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bd5"},{"url":"D:\\\\classZ_backend\\\\server\\\\uploads\\\\1745428246949-images_4.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bd6"},{"url":"/uploads/1745428247187-hkidCard-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bd7"},{"url":"/uploads/1745428247188-hkidCard-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bd8"}],"languages":["Cantonese","English"],"services":[],"description":"a","images":[{"url":"D:\\\\classZ_backend\\\\server\\\\uploads\\\\1745428246974-images (1)_5.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bd9"},{"url":"D:\\\\classZ_backend\\\\server\\\\uploads\\\\1745428246994-images_6.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bda"},{"url":"D:\\\\classZ_backend\\\\server\\\\uploads\\\\*************-center_7.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bdb"},{"url":"/uploads/*************-images-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bdc"},{"url":"/uploads/*************-images-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bdd"},{"url":"/uploads/*************-images-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg","_id":"68091f17c739f053563e8bde"}],"bankDetails":{"bankName":"Hang Seng Bank","accountHolderName":"a","bankCode":"004","branchCode":"a","accountNumber":"a","_id":"6809438162a731df866ed179"},"verified":false,"__v":0,"managers":[]},{"mainImage":{"url":"/uploads/*************-mainImage-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg"},"location":{"type":"Point","coordinates":[0,0]},"price":0,"ageRangeFrom":0,"ageRangeTo":0,"categories":[],"_id":"68092f6ddb24c90ef3e63592","baseUser":"679e504d0d7ec3d2868ee1c8","owner":"679e59b14e210f9f21eabf53","coachs":[],"rating":0,"reviewCount":0,"legalName":"asasa","displayName":"","startAge":"","sen":false,"address":{"address1":"","address2":"","city":"","region":"","_id":"68095e8277282447683043db"},"companyNumber":"","centerNumber":"","email":"","openingHour":[{"day":"Monday","openingTime":"11:00 AM","closingTime":"11:00 AM","_id":"68092f6ddb24c90ef3e63594"},{"day":"Tuesday","openingTime":"12:00 PM","closingTime":"12:00 PM","_id":"68092f6ddb24c90ef3e63595"},{"day":"Wednesday","openingTime":"01:00 PM","closingTime":"02:00 PM","_id":"68092f6ddb24c90ef3e63596"},{"day":"Thursday","openingTime":"01:00 PM","closingTime":"01:00 PM","_id":"68092f6ddb24c90ef3e63597"},{"day":"Friday","openingTime":"02:00 PM","closingTime":"02:00 PM","_id":"68092f6ddb24c90ef3e63598"},{"day":"Saturday","openingTime":"11:00 AM","closingTime":"11:00 AM","_id":"68092f6ddb24c90ef3e63599"},{"day":"Sunday","openingTime":"12:00 PM","closingTime":"12:00 PM","_id":"68092f6ddb24c90ef3e6359a"}],"businessNumber":"","businessCertificate":[{"url":"D:\\\\classZ_backend\\\\server\\\\uploads\\\\1745432429698-images_1.jpg","contentType":"image/jpeg","_id":"68092f6ddb24c90ef3e6359b"},{"url":"/uploads/1745432429908-businessCertificate-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg","_id":"68092f6ddb24c90ef3e6359c"}],"hkidCard":[{"url":"D:\\\\classZ_backend\\\\server\\\\uploads\\\\1745432429723-images (1)_2.jpg","contentType":"image/jpeg","_id":"68092f6ddb24c90ef3e6359d"},{"url":"/uploads/1745432429909-hkidCard-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg","_id":"68092f6ddb24c90ef3e6359e"}],"languages":["Cantonese","English"],"services":["Centre assists students to participate in external competitions","Centre provides internal competitions/events regularly","Equipment is provided for students within classes"],"description":"test description","images":[{"url":"D:\\\\classZ_backend\\\\server\\\\uploads\\\\*************-center_0.jpg","contentType":"image/jpeg","_id":"680954519077c3618e0061d1"},{"url":"/uploads/*************-images-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg","_id":"680954519077c3618e0061d2"}],"bankDetails":{"bankName":"Bank of East Asia (BEA)","accountHolderName":"b","bankCode":"b","branchCode":"b","accountNumber":"b","_id":"68092f6ddb24c90ef3e635a1"},"verified":true,"__v":0,"managers":[]},{"mainImage":{"url":"/uploads/*************-mainImage-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg"},"location":{"type":"Point","coordinates":[0,0]},"price":0,"ageRangeFrom":0,"ageRangeTo":0,"categories":[],"_id":"6809d8deacb92d1adbe9fb18","baseUser":"679e504d0d7ec3d2868ee1c8","owner":"679e59b14e210f9f21eabf53","coachs":[],"rating":0,"reviewCount":0,"legalName":"c","displayName":"Testing center","startAge":"","sen":false,"address":{"address1":"","address2":"","city":"","region":"","_id":"6809d90aacb92d1adbe9fbb9"},"companyNumber":"","centerNumber":"","email":"","openingHour":[{"day":"Monday","openingTime":"07:00 AM","closingTime":"07:00 AM","_id":"6809d8dfacb92d1adbe9fb1a"},{"day":"Tuesday","openingTime":"08:00 AM","closingTime":"08:00 AM","_id":"6809d8dfacb92d1adbe9fb1b"},{"day":"Wednesday","openingTime":"10:00 AM","closingTime":"10:00 AM","_id":"6809d8dfacb92d1adbe9fb1c"},{"day":"Thursday","openingTime":"11:00 AM","closingTime":"11:00 AM","_id":"6809d8dfacb92d1adbe9fb1d"},{"day":"Friday","openingTime":"12:00 PM","closingTime":"12:00 PM","_id":"6809d8dfacb92d1adbe9fb1e"},{"day":"Saturday","openingTime":"01:00 PM","closingTime":"02:00 PM","_id":"6809d8dfacb92d1adbe9fb1f"},{"day":"Sunday","openingTime":"12:00 PM","closingTime":"12:00 PM","_id":"6809d8dfacb92d1adbe9fb20"}],"businessNumber":"d","businessCertificate":[{"url":"/home/<USER>/classZ_Backend/uploads/1745475805958-images (1)_1.jpg","contentType":"image/jpeg","_id":"6809d8dfacb92d1adbe9fb21"},{"url":"/uploads/1745475806830-businessCertificate-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg","_id":"6809d8dfacb92d1adbe9fb22"}],"hkidCard":[{"url":"/home/<USER>/classZ_Backend/uploads/*************-images_2.jpg","contentType":"image/jpeg","_id":"6809d8dfacb92d1adbe9fb23"},{"url":"/uploads/*************-hkidCard-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg","_id":"6809d8dfacb92d1adbe9fb24"}],"languages":["Cantonese","English"],"services":[],"description":"ada","images":[{"url":"/home/<USER>/classZ_Backend/uploads/*************-images (1)_3.jpg","contentType":"image/jpeg","_id":"6809d8dfacb92d1adbe9fb25"},{"url":"/uploads/*************-images-679e504d0d7ec3d2868ee1c8.jpg","contentType":"image/jpeg","_id":"6809d8dfacb92d1adbe9fb26"}],"bankDetails":{"bankName":"Bank of East Asia (BEA)","accountHolderName":"as","bankCode":"s","branchCode":"d","accountNumber":"s","_id":"6809d8dfacb92d1adbe9fb27"},"verified":false,"__v":0,"managers":[]}]
// ''';

//   bool _isSaved = false;
//   String _filePath = '';

//   Future<void> _saveJsonToFile() async {
//     try {
//       final directory = await getApplicationDocumentsDirectory();
//       final file = File('${directory.path}/centers_data.json');
//       await file.writeAsString(centersJsonData);
//       setState(() {
//         _isSaved = true;
//         _filePath = file.path;
//       });
      
//       // Show success message
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text('JSON saved to: $_filePath'),
//           duration: Duration(seconds: 5),
//         ),
//       );
//     } catch (e) {
//       // Show error message
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text('Error saving JSON: $e'),
//           backgroundColor: Colors.red,
//         ),
//       );
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text('Centers Data Example'),
//         backgroundColor: AppPallete.secondaryColor,
//       ),
//       body: Center(
//         child: Padding(
//           padding: EdgeInsets.all(16.0),
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               Text(
//                 'This example demonstrates loading and displaying centers data from JSON',
//                 style: TextStyle(fontSize: 16),
//                 textAlign: TextAlign.center,
//               ),
//               SizedBox(height: 20),
//               Text(
//                 'The data contains ${centersJsonData.length} characters of JSON representing 3 centers',
//                 style: TextStyle(fontSize: 14, color: Colors.grey),
//                 textAlign: TextAlign.center,
//               ),
//               SizedBox(height: 30),
//               LoadCentersButton(
//                 centersJsonData: centersJsonData,
//                 buttonText: 'View All Centers',
//               ),
//               SizedBox(height: 20),
//               Button(
//                 height: 49.h,
//                 width: 289.w,
//                 buttonText: _isSaved ? 'JSON Saved' : 'Save JSON to File',
//                 shadows: [shadow(blurRadius: 15, opacity: 0.1)],
//                 onPressed: _isSaved ? null : _saveJsonToFile,
//                 color: _isSaved ? Colors.grey : AppPallete.secondaryColor,
//               ),
//               if (_isSaved) ...[
//                 SizedBox(height: 10),
//                 Text(
//                   'File saved at:',
//                   style: TextStyle(fontSize: 14),
//                 ),
//                 Text(
//                   _filePath,
//                   style: TextStyle(fontSize: 12, color: Colors.grey),
//                   textAlign: TextAlign.center,
//                 ),
//               ],
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// } 