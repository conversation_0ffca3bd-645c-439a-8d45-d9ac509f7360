import 'package:class_z/core/imports.dart';


class OrdersDetailsPending extends StatefulWidget {
  const OrdersDetailsPending({super.key});

  @override
  State<OrdersDetailsPending> createState() => _OrdersDetailsPendingState();
}

class _OrdersDetailsPendingState extends State<OrdersDetailsPending> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: "My Classes", leading: customBackButton()),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 36.h,
            ),
            _oneTwoThree(context),
            SizedBox(
              height: 15.h,
            ),
            _prepare(context: context)
          ],
        ),
      ),
    );
  }

  Widget _oneTwoThree(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 12.w, right: 11.w),
      child: <PERSON><PERSON><PERSON><PERSON>(
        height: 55.h,
        width: 407.w,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(
                left: 24.w,
              ),
              child: Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(20.r),
                    child: Container(
                      height: 34.h,
                      width: 34.w,
                      color: AppPallete.secondaryColor,
                      child: Center(
                        child: customtext(
                            context: context,
                            newYear: "1",
                            font: 20.sp,
                            weight: FontWeight.w400,
                            color: Colors.white),
                      ),
                    ),
                  ),
                  Container(
                    width: 138.w,
                    height: 1.h,
                    color: AppPallete.dividerTime,
                  ),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(20.r),
                    child: Container(
                      height: 34.h,
                      width: 34.w,
                      color: AppPallete.secondaryColor,
                      child: Center(
                        child: customtext(
                            context: context,
                            newYear: "2",
                            font: 20.sp,
                            weight: FontWeight.w400,
                            color: Colors.white),
                      ),
                    ),
                  ),
                  Container(
                    width: 128.w,
                    height: 1.h,
                    color: AppPallete.dividerTime,
                  ),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(20.r),
                    child: Container(
                      height: 34.h,
                      width: 34.w,
                      color: AppPallete.dividerTime,
                      child: Center(
                        child: customtext(
                            context: context,
                            newYear: "3",
                            font: 20.sp,
                            weight: FontWeight.w400,
                            color: Colors.black),
                      ),
                    ),
                  )
                ],
              ),
            ),
            Row(
              children: [
                SizedBox(
                  width: 85.w,
                  child: customtext(
                      context: context,
                      newYear: "choose class",
                      font: 15.sp,
                      weight: FontWeight.w400),
                ),
                SizedBox(
                  width: 77.w,
                ),
                SizedBox(
                  width: 84.w,
                  child: customtext(
                      context: context,
                      newYear: "send request",
                      font: 15.sp,
                      weight: FontWeight.w400),
                ),
                SizedBox(
                  width: 79.w,
                ),
                SizedBox(
                  width: 82.w,
                  child: customtext(
                      context: context,
                      newYear: "confirmation",
                      font: 15.sp,
                      weight: FontWeight.w400),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _prepare({required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.only(left: 23.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 36.h,
          ),
          Padding(
              padding: EdgeInsets.only(left: 2.w),
              child: timeTableCardFull(
                  context: context,
                  user: "Tommy Fung",
                  confirmed: false,
                  date: "01/01/2024",
                  location: "Upon Request",
                  course: "Watercolour (intermediate)",
                  time: "14:30 - 15:15",
                  classTime: "45mins",
                  special: "Special request: SEN service",
                  coach: "by Charlie Own",
                  participantName: "Tomy Fung",
                  contact: "01521503815",
                  email: "<EMAIL>",
                  coachingAddress:
                      "Flat ABC, Block AVC, ABC Estate, Hong Kong")),
          SizedBox(
            height: 20.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 3.5.w),
            child: customtext(
                context: context,
                newYear:
                    "Please arrive the coaching address on time to attend the lesson",
                font: 13.sp,
                weight: FontWeight.w300),
          ),
          SizedBox(
            height: 10.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 3.5.w),
            child: customtext(
                context: context,
                newYear: "No compensation time will be granted upon lateness",
                font: 13.sp,
                weight: FontWeight.w300),
          ),
          SizedBox(
            height: 10.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 3.5.w),
            child: customtext(
                context: context,
                newYear: "No-show may be subject to a service charge",
                font: 13.sp,
                weight: FontWeight.w300),
          ),
          SizedBox(
            height: 20.h,
          ),
          billBreakdownPage(context: context),
          SizedBox(
            height: 33.h,
          ),
          Center(
            child: Button(
                buttonText: "Cancel Slot",
                color: AppPallete.secondaryColor,
                width: 174.w,
                height: 49.h),
          ),
          SizedBox(
            height: 2.h,
          ),
        ],
      ),
    );
  }
}
