import 'package:class_z/core/imports.dart';
import 'package:dio/dio.dart';
import 'package:class_z/core/config/app_config.dart';
import 'package:flutter/services.dart';

class TopUp extends StatefulWidget {
  final int zCoin;
  final int usd;
  final int discount;
  final String? planId;
  final bool isSubscription;
  const TopUp(
      {this.discount = 10,
      required this.usd,
      required this.zCoin,
      this.planId,
      this.isSubscription = false,
      super.key});

  @override
  State<TopUp> createState() => _TopUpState();
}

class _TopUpState extends State<TopUp> {
  bool _isLoading = false;
  final Dio _dio = Dio();
  late int _quantity;

  @override
  void initState() {
    super.initState();
    _quantity = widget.zCoin;
  }

  // Calculate price per ZCoin in HKD
  int get _pricePerZCoinHKD {
    return AppConfig.pricePerZCoinHKD ??
        25; // Use configured price or default (25 HKD)
  }

  // Calculate total price based on quantity in HKD
  int get _totalPrice => _pricePerZCoinHKD * _quantity;

  // Calculate the discount amount (10% of the total price)
  int get _discountAmount {
    // Only apply discount if discount parameter is greater than 0
    if (widget.discount <= 0) return 0;
    return (_quantity * _pricePerZCoinHKD * 0.1).round(); // 10% discount
  }

  // Get the discount percentage label
  String get _discountLabel {
    if (widget.discount <= 0) return "";
    return "10% OFF";
  }

  // Calculate the final total after discount in HKD
  int get _finalTotal {
    return (_quantity * _pricePerZCoinHKD) - _discountAmount;
  }

  // Get discount display text
  String get _discountText {
    return "${widget.discount}%";
  }

  // Increase quantity
  void _increaseQuantity() {
    setState(() {
      _quantity++;
    });
  }

  // Decrease quantity
  void _decreaseQuantity() {
    if (_quantity > 1) {
      setState(() {
        _quantity--;
      });
    }
  }

  // Method to handle the payment process
  Future<void> _processPayment() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Show loading indicator
      loadingState(context: context);

      // Get the user data and token
      final userData = locator<SharedRepository>().getUserData();
      final userId = userData?.data?.parent?.id;
      final token = locator<SharedRepository>().getToken();

      if (userId == null) {
        throw Exception("User not logged in");
      }

      // Try to get saved card information
      try {
        final cardResponse = await _dio.get(
          "${AppText.device}/api/payment/getcard/$userId",
          options: Options(headers: {"auth-token": token}),
        );

        print("Card API response: ${cardResponse.data}");

        if (cardResponse.statusCode != 200 ||
            cardResponse.data == null ||
            cardResponse.data["last4"] == null) {
          // No saved card or invalid card data, redirect to add card
          hideLoadingDialog(context);
          NavigatorService.pushNamed(AppRoutes.creditCard);
          return;
        }

        // Redirect to Credit Card screen to create a new token for the payment
        // This is needed because we don't have direct access to the card token from the saved card
        hideLoadingDialog(context);

        // Show user a dialog explaining they need to confirm card details
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text("Confirm Payment"),
            content: Text(
                "You have a saved card ending in ${cardResponse.data["last4"]}. You'll need to confirm your card details to proceed with payment."),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  // Navigate to credit card screen with amount to pay
                  final paymentDetails = {
                    'amount': _finalTotal,
                    'zcoins': _quantity,
                    'planId': widget.planId,
                    'isSubscription': widget.isSubscription
                  };
                  print(
                      "Navigating to credit card screen with payment details: $paymentDetails");

                  NavigatorService.pushNamed(AppRoutes.creditCard,
                      arguments: paymentDetails);
                },
                child: Text("Continue"),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text("Cancel"),
              ),
            ],
          ),
        );
        return;
      } catch (e) {
        print("Card API error: ${e.toString()}");
        // No saved card, redirect to add card
        hideLoadingDialog(context);

        // Show error dialog with more specific message
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text("Card Required"),
            content: Text(
                "You need to add a payment card before making a purchase."),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  NavigatorService.pushNamed(AppRoutes.creditCard);
                },
                child: Text("Add Card"),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text("Cancel"),
              ),
            ],
          ),
        );
        return;
      }
    } catch (e) {
      hideLoadingDialog(context);
      // Show error dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text("Payment Failed"),
          content: Text(
              "An error occurred while processing your payment. Please try again."),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text("OK"),
            ),
          ],
        ),
      );
      print("Payment error: $e");
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Method to update user balance
  Future<void> _updateBalance(String userId, int amount) async {
    try {
      final token = locator<SharedRepository>().getToken();
      final response = await _dio.post(
        "${AppText.device}/api/balance/add/$userId",
        data: {"amount": amount},
        options: Options(headers: {"auth-token": token}),
      );

      if (response.statusCode != 200) {
        throw Exception("Failed to update balance");
      } else {
        print("Balance successfully updated, added $amount ZCoins");
      }
    } catch (e) {
      print("Error updating balance: $e");
      // We don't show an error here as the payment was already successful
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.only(left: 22.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomAppBar(
              title: 'Top Up',
              leading: customBackButton(),
              actions: [
                Padding(
                  padding: EdgeInsets.only(right: 15.w),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          // Navigate to notifications
                          final parentId =
                              locator<SharedRepository>().getParentData()?.id;
                          NavigatorService.pushNamed(AppRoutes.notification,
                              arguments: parentId);
                        },
                        child: notification_badge(
                          context: context,
                          icon: Icons.notifications,
                          badgeCount: 0,
                        ),
                      ),
                      SizedBox(width: 9.w),
                      GestureDetector(
                        onTap: () {
                          // Navigate to messages
                          NavigatorService.pushNamed(AppRoutes.centerMessage,
                              arguments: 'user');
                        },
                        child: notification_badge(
                          context: context,
                          icon: Icons.messenger_outline_sharp,
                          badgeCount: 0,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 40.h),
            customtext(
                context: context,
                newYear: 'Zcoins to top-up',
                font: 20.sp,
                weight: FontWeight.w600),
            SizedBox(
              height: 50.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 20.5.w, right: 42.5.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: customtext(
                        context: context,
                        newYear: 'HKD $_pricePerZCoinHKD/ Zcoin',
                        font: 20.sp,
                        weight: FontWeight.w500),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Quantity selector with - and + buttons
                      GestureDetector(
                        onTap: _decreaseQuantity,
                        child: Container(
                          width: 30.w,
                          height: 30.h,
                          decoration: BoxDecoration(
                            color: AppPallete.secondaryColor,
                            borderRadius: BorderRadius.circular(15.r),
                          ),
                          child: Icon(
                            Icons.remove,
                            color: Colors.white,
                            size: 20.sp,
                          ),
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 15.w, vertical: 5.h),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppPallete.secondaryColor),
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        child: SizedBox(
                          width: 60.w,
                          height: 40.h,
                          child: TextField(
                            controller: TextEditingController(
                                text: _quantity.toString()),
                            keyboardType: TextInputType.number,
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 28.sp),
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              isDense: true,
                              contentPadding: EdgeInsets.zero,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            onChanged: (value) {
                              final intValue = int.tryParse(value);
                              if (intValue != null && intValue > 0) {
                                setState(() {
                                  _quantity = intValue;
                                });
                              }
                            },
                          ),
                        ),
                      ),
                      SizedBox(width: 10.w),
                      GestureDetector(
                        onTap: _increaseQuantity,
                        child: Container(
                          width: 30.w,
                          height: 30.h,
                          decoration: BoxDecoration(
                            color: AppPallete.secondaryColor,
                            borderRadius: BorderRadius.circular(15.r),
                          ),
                          child: Icon(
                            Icons.add,
                            color: Colors.white,
                            size: 20.sp,
                          ),
                        ),
                      ),
                      SizedBox(width: 12.w),
                      customSvgPicture(
                        imagePath: ImagePath.zSvg,
                        height: 45.h,
                        width: 45.w,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 50.h,
            ),
            customtext(
                context: context,
                newYear: 'Bill BreakDown',
                font: 20.sp,
                weight: FontWeight.w600),
            SizedBox(
              height: 18.h,
            ),
            CustomBill(
              rows: [
                BillRow(
                  item: "Z Coin",
                  qty: _quantity.toString(),
                  rate: "HKD $_pricePerZCoinHKD",
                  subtotal: "HKD ${_quantity * _pricePerZCoinHKD}",
                )
              ],
              subtotal: _quantity * _pricePerZCoinHKD,
              discount: _discountAmount,
              discountLabel: _discountLabel,
              zcoin: false,
            ),
            SizedBox(height: 40.h),
            // Add payment button
            Padding(
              padding: EdgeInsets.only(right: 22.w),
              child: Button(
                buttonText: "Confirm Purchase",
                color: AppPallete.secondaryColor,
                onPressed: _isLoading ? null : _processPayment,
                height: 50.h,
                width: double.infinity,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
