import 'package:class_z/core/imports.dart';

class Reserved extends StatelessWidget {
  const Reserved({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          // crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            customSvgPicture(
                imagePath: ImagePath.reservedSvg,
                height: 72.h,
                width: 63.w,
                color: AppPallete.secondaryColor),
            SizedBox(
              height: 40.h,
            ),
            customtext(
                context: context,
                newYear: "All set!",
                font: 20.sp,
                weight: FontWeight.w700),
            SizedBox(
              height: 24.h,
            ),
            customtext(
                context: context,
                newYear:
                    "Your child is successfully enrolled.\nPlease attend on time!",
                textAlign: TextAlign.center,
                font: 15.sp,
                weight: FontWeight.w500),
            SizedBox(
              height: 43.h,
            ),
            GestureDetector(
              onTap: () {
                NavigatorService.pushNamedAndRemoveUntil(AppRoutes.timeTableUp);
              },
              child: customtext(
                  context: context,
                  newYear: "Check my order",
                  font: 15.sp,
                  weight: FontWeight.w500,
                  color: AppPallete.secondaryColor,
                  underline: true,
                  underlineColor: AppPallete.secondaryColor),
            ),
            SizedBox(
              height: 40.h,
            ),
            Button(
                onPressed: () {
                  NavigatorService.pushNamed(AppRoutes.homePage);
                },
                buttonText: "Continue Exploring",
                color: AppPallete.secondaryColor,
                height: 49.h,
                width: 289.w)
          ],
        ),
      ),
    );
  }
}
