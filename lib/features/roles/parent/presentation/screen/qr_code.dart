import 'package:class_z/core/imports.dart';

class QrCode extends StatefulWidget {
  final EventElement eventElement;
  const QrCode({required this.eventElement, super.key});

  @override
  State<QrCode> createState() => _QrCodeState();
}

class _QrCodeState extends State<QrCode> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<UserBloc>().add(QrCodeGenerateForUserEvent(
        classId: widget.eventElement.event?.classId?.id ?? "",
        studentId: widget.eventElement.childId?.id ?? "",
        classDate: widget.eventElement.event?.date ?? DateTime.now()));
  }

  QrCodeModel? code;

  Uint8List? decodedBytes;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: BlocListener<UserBloc, UserState>(
      listener: (context, state) {
        if (state is UserLoadingState)
          loadingState(context: context);
        else
          hideLoadingDialog(context);
        if (state is UserErrorState)
          errorState(context: context, error: state.message);
        if (state is QrCodeGenerateForUserSuccessState) {
          setState(() {
            code = state.code;
            final base64Str = code?.qrCodeData?.split(',').last;

            decodedBytes = base64Decode(base64Str ?? "");
          });
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomAppBarDouble(
            title: 'Verification Code',
            title2: 'Notify the coach at start',
            leading: customBackButton(),
          ),
          Padding(
            padding: EdgeInsets.only(left: 29.w, right: 29.w, top: 104.h),
            child: Container(
              height: 480.h,
              width: 372.w,
              padding: EdgeInsets.only(left: 14.w, right: 17.w),
              decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [shadow()],
                  borderRadius: BorderRadius.circular(20.r)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 25.h,
                  ),
                  SizedBox(
                    width: double
                        .infinity, // Ensures the Stack spans the entire Row
                    child: Stack(
                      alignment:
                          Alignment.center, // Centers the name within the Stack
                      children: [
                        // Date at the start (left-aligned)
                        Positioned(
                          left: 0, // Align to the start of the Stack
                          child: customtext(
                            context: context,
                            newYear: dateGenerator(
                                date: widget.eventElement.event?.date,
                                format: 'dd/MM/yyyy'),
                            font: 12.sp,
                            weight: FontWeight.w500,
                          ),
                        ),
                        // Name in the center
                        Center(
                          child: customtext(
                            context: context,
                            newYear: widget.eventElement.event?.classId?.center
                                    ?.displayName ??
                                '',
                            font: 17.sp,
                            weight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Center(
                    child: customtext(
                        context: context,
                        newYear: widget
                                .eventElement.event?.classId?.classProviding ??
                            '',
                        font: 15.sp,
                        weight: FontWeight.w500),
                  ),
                  SizedBox(
                    height: 35.h,
                  ),
                  customtext(
                      context: context,
                      newYear:
                          "Please prepare the class code or display the QR code to the coach before the class begins",
                      font: 15.sp,
                      weight: FontWeight.w400),
                  Padding(
                    padding: EdgeInsets.only(left: 52.w, top: 33.h),
                    child: Row(
                      children: [
                        customtext(
                            context: context,
                            newYear: "Your Code:",
                            font: 20.sp,
                            color: AppPallete.secondaryColor,
                            weight: FontWeight.w600),
                        SizedBox(
                          width: 18.w,
                        ),
                        customtext(
                            context: context,
                            newYear: code?.code ?? "",
                            font: 30.sp,
                            color: AppPallete.secondaryColor,
                            weight: FontWeight.w600),
                      ],
                    ),
                  ),
                  if (decodedBytes != null)
                    Center(
                        child: SizedBox(
                      height: 201.h,
                      width: 201.w,
                      child: Image.memory(
                        decodedBytes!,
                        fit: BoxFit.cover,
                      ),
                    ))
                ],
              ),
            ),
          )
        ],
      ),
    ));
  }
}
