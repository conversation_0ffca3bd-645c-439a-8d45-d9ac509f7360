import 'package:class_z/core/imports.dart';
import 'package:shimmer/shimmer.dart';

class MiddleProfileSwitch extends StatefulWidget {
  const MiddleProfileSwitch({super.key});

  @override
  State<MiddleProfileSwitch> createState() => _MiddleProfileSwitchState();
}

class _MiddleProfileSwitchState extends State<MiddleProfileSwitch>
    with WidgetsBindingObserver {
  bool _activities = true;
  bool _promotion = false;
  String? imagePath;
  String? fullUrl;
  bool isLoggedIn = false;
  UserModel? userData;
  bool isLoading = true;
  bool hasActiveSubscription = false;
  bool _balanceRequested = false;

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    // Set initial loading state
    isLoading = true;
    _refreshUserData();
    if (locator<SharedRepository>().getUserId().isNotEmpty) isLoggedIn = true;

    // Initialize profile data
    _initializeProfileData();
    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Refresh data when app is resumed
      _refreshUserData();
    }
  }

  // Add a new method to load data at startup
  Future<void> _initializeProfileData() async {
    try {
      // Delay to allow UI to render first
      await Future.delayed(const Duration(milliseconds: 300));

      // Ensure the widget is still mounted before using context
      if (!mounted) return;

      // Check if the user is logged in before calling the API
      if (isLoggedIn) {
        print("Loading balance data...");
        // Get balance information
        context.read<UserBloc>().add(GetBalanceEvent());

        // Get subscription information
        context.read<SubscriptionBloc>().add(GetSubscriptionPlanEvent());

        // Load notification preferences from user data
        final parentData =
            locator<SharedRepository>().getUserData()?.data?.parent;
        if (parentData != null) {
          setState(() {
            _activities = parentData.activitiesNotification ?? true;
            _promotion = parentData.promotionNotification ?? false;
          });
        }
      }
    } catch (e) {
      print("Error initializing profile data: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading profile data: $e'),
          duration: Duration(seconds: 3),
        ),
      );
    } finally {
      // Set loading to false regardless of success or failure
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Refresh user data when dependencies change
    _refreshUserData();

    // Check if we need to reload balance data
    if (isLoggedIn && !_balanceRequested && balance == 0) {
      _balanceRequested = true;
      print("Reloading balance data (first time only)...");
      context.read<UserBloc>().add(GetBalanceEvent());
    }
  }

  void _refreshUserData() {
    try {
      final sharedRepository = locator<SharedRepository>();
      final newUserData = sharedRepository.getUserData();

      // Check if we have valid data
      if (newUserData != null) {
        // Log detailed information about the user data structure
        print("============ USER DATA REFRESH ============");
        print(
            "Token: ${newUserData.token.length > 0 ? 'Valid (${newUserData.token.substring(0, 10)}...)' : 'Empty'}");
        print("User ID: ${sharedRepository.getUserId()}");
        print("Has Data object: ${newUserData.data != null}");
        print("Has Parent data: ${newUserData.data?.parent != null}");
        if (newUserData.data?.parent != null) {
          print("Parent name: ${newUserData.data?.parent?.fullname}");
          print("Parent ID: ${newUserData.data?.parent?.id}");
          // Set notification preferences from refreshed data
          setState(() {
            _activities =
                newUserData.data?.parent?.activitiesNotification ?? true;
            _promotion =
                newUserData.data?.parent?.promotionNotification ?? false;
          });
        }
        print("=========================================");

        // Update login status
        final bool wasLoggedIn = isLoggedIn;
        final bool nowLoggedIn = sharedRepository.getUserId().isNotEmpty;

        if (!wasLoggedIn && nowLoggedIn) {
          print("User just logged in - refreshing data");
        }

        // Update state with new data
        setState(() {
          userData = newUserData;
          isLoggedIn = nowLoggedIn;

          // Update profile image
          if (userData?.data?.parent?.image?.url != null) {
            imagePath = userData?.data?.parent?.image?.url;
            fullUrl = "${AppText.device}$imagePath";
            print("Profile image URL: $fullUrl");
          } else {
            fullUrl = '';
            print("No profile image available");
          }
        });
      } else {
        print("No user data available during refresh");
        setState(() {
          userData = null;
          isLoggedIn = false;
          fullUrl = '';
        });
      }
    } catch (e) {
      print("Error refreshing user data: $e");
      // Don't update state on error to prevent UI issues
    }
  }

  Future<void> _updateNotificationPreference(String key, bool value) async {
    if (!isLoggedIn) return;

    final userId = locator<SharedRepository>().getUserId();
    if (userId.isEmpty) return;

    try {
      context.read<AuthBloc>().add(
            UpdateUserEvent(
              id: userId,
              data: {key: value},
            ),
          );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update notification preference: $e'),
        ),
      );
    }
  }

  void toggleActivities(bool value) {
    setState(() {
      _activities = value;
    });
    _updateNotificationPreference('activitiesNotification', value);
  }

  void togglePromotion(bool value) {
    setState(() {
      _promotion = value;
    });
    _updateNotificationPreference('promotionNotification', value);
  }

  // Force image refresh
  void _forceImageRefresh() {
    // Create a new timestamp-based key to force rebuild
    setState(() {
      // This will trigger a rebuild with a new image
      _refreshUserData();
    });
  }

  int balance = 0;

  // Add a method to handle balance API errors
  void _handleBalanceError(String message) {
    // Log the error
    print("Balance API error handling: $message");

    // Set default balance value
    setState(() {
      balance = 0;
      isLoading = false;
    });

    // Only show snackbar for critical errors, not for common "Failed to fetch balance"
    if (!message.contains("Failed to fetch balance")) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Balance will be shown as 0 until the next update'),
          duration: Duration(seconds: 2),
        ),
      );
    }

    // Ensure loading dialog is dismissed
    hideLoadingDialog(context);
  }

  // Update the method to handle missing parent data
  void _navigateToProfile() {
    try {
      // Check if userData is valid before navigation
      if (userData == null || userData?.data?.parent == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Error: User data not available. Please login again.'),
            duration: Duration(seconds: 2),
          ),
        );
        return;
      }

      // Don't show loading indicator before navigation - it can cause issues
      // Instead, navigate directly using Navigator.push which is more reliable
      try {
        // Use direct navigation instead of NavigatorService
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MyProfile(userData: userData!),
          ),
        ).then((_) {
          // Force refresh of the data when returning
          _refreshUserData();
        });
      } catch (navigationError) {
        print("Navigation error: $navigationError");

        // Show a helpful error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error accessing profile: $navigationError'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print("Profile navigation error: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error accessing profile. Please try again.'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final sharedRepository = Provider.of<SharedRepository>(context);

    // Listen to auth state changes
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthSignInSuccess || state is UserLoaded) {
          print("Auth state changed: ${state.runtimeType}");

          // Force delay to ensure data is saved before we try to read it
          Future.delayed(Duration(milliseconds: 500), () {
            if (mounted) {
              // Make sure we get the latest user data
              final refreshedUserData =
                  locator<SharedRepository>().getUserData();

              print(
                  "User data after login: ${refreshedUserData?.data?.parent?.fullname ?? 'No name'}");

              setState(() {
                _refreshUserData();
                _forceImageRefresh();
                isLoggedIn = locator<SharedRepository>().getUserId().isNotEmpty;

                // Reload the user data one more time to be sure
                userData = refreshedUserData;
              });

              // Reload balance and subscription on login
              if (isLoggedIn) {
                print("Loading balance and subscription after login");
                context.read<UserBloc>().add(GetBalanceEvent());
                context
                    .read<SubscriptionBloc>()
                    .add(GetSubscriptionPlanEvent());
              }
            }
          });
        }
      },
      child: Scaffold(
        body: MultiBlocListener(
          listeners: [
            BlocListener<UserBloc, UserState>(
              listener: (context, state) {
                if (state is UserLoadingState) {
                  // We're already handling loading state elsewhere, so no need to show additional loading
                } else if (state is UserErrorState) {
                  // Handle error state gracefully
                  _handleBalanceError(state.message);
                  // Dismiss loading dialog if it's showing
                  hideLoadingDialog(context);
                } else if (state is GetBalanceSuccessState) {
                  setState(() {
                    balance =
                        state.balance.balance ?? 0; // Use null-safe access
                    isLoading = false;
                    _balanceRequested = true;
                  });
                  // Make sure to dismiss any loading dialog
                  hideLoadingDialog(context);
                }
              },
            ),
            BlocListener<SubscriptionBloc, SubscriptionState>(
              listener: (context, state) {
                if (state is GetSubscribeSuccessState) {
                  // Check if there's actually an active subscription
                  final hasActiveSub = state.subscriptionModel.current != null;
                  setState(() {
                    hasActiveSubscription = hasActiveSub;
                  });
                  print(
                      "🔔 Profile subscription status updated: hasActiveSubscription = $hasActiveSub");
                } else if (state is SubscriptionCancelledState) {
                  setState(() {
                    hasActiveSubscription = false;
                  });
                  print(
                      "🔔 Profile subscription cancelled: hasActiveSubscription = false");
                } else if (state is SubscriptionErrorState) {
                  setState(() {
                    hasActiveSubscription = false;
                  });
                  print(
                      "🔔 Profile subscription error: hasActiveSubscription = false");
                }
              },
            ),
          ],
          child: isLoading
              ? _buildProfileSkeleton()
              : SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      topBar(
                        context: context,
                        name: userData?.data?.parent?.nickname ??
                            "Login/ Register",
                        notification: 29,
                        message: 30,
                        imagePath: fullUrl ?? '',
                        rating: "5.0",
                      ),
                      Padding(
                          padding: const EdgeInsets.only(
                              left: 17, right: 18, top: 19),
                          child: walletCard(
                              context: context,
                              money: balance,
                              isLoggedIn: isLoggedIn,
                              hasActiveSubscription: hasActiveSubscription)),
                      sectionHeader("Profile"),
                      const SizedBox(
                        height: 9,
                      ),
                      profile(
                        context: context,
                        name: "My Profile",
                        iconData: Icons.person,
                        onTap: () {
                          _navigateToProfile();
                        },
                      ),
                      customDivider(padding: 24.w),
                      profile(
                        context: context,
                        name: "Child",
                        iconData: Icons.boy,
                        onTap: () {
                          String? parentId = userData?.data?.parent?.id;
                          if (parentId != null && parentId.isNotEmpty) {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    ChildInfo(parentId: parentId),
                              ),
                            ).catchError((error) {
                              print("Child info navigation error: $error");
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                      'Error accessing child info: $error'),
                                  duration: Duration(seconds: 3),
                                ),
                              );
                            });
                          } else {
                            // Handle missing parent ID case
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Parent ID not found. Please refresh or login again.'),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }
                        },
                      ),
                      customDivider(padding: 24.w),
                      profile(
                        context: context,
                        name: "My Address",
                        iconData: Icons.location_on,
                        onTap: () {
                          Navigator.pushNamed(context, AppRoutes.myAddress);
                        },
                      ),
                      customDivider(padding: 24.w),
                      profile(
                        context: context,
                        name: "Saved Center",
                        iconData: Icons.favorite,
                        onTap: () {
                          try {
                            NavigatorService.pushNamed(AppRoutes.savedCenters);
                          } catch (e) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content:
                                    Text('Error accessing saved centers: $e'),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }
                        },
                      ),
                      customDivider(padding: 24.w),
                      profile(
                        context: context,
                        name: "My Coupons",
                        iconData: Icons.local_offer,
                        onTap: () {
                          Navigator.pushNamed(context, AppRoutes.myCoupn);
                        },
                      ),
                      customDivider(padding: 24.w),
                      profile(
                        context: context,
                        name: "My Reviews",
                        iconData: Icons.rate_review,
                        onTap: () {
                          NavigatorService.pushNamed(AppRoutes.myReviews);
                        },
                      ),
                      customDivider(padding: 24.w),
                      profile(
                        context: context,
                        name: "My Subscription",
                        iconData: Icons.subscriptions,
                        onTap: () {
                          NavigatorService.pushNamed(AppRoutes.mySubscriptions);
                        },
                      ),
                      customDivider(padding: 24.w),
                      profile(
                        context: context,
                        name: "Refund",
                        iconData: Icons.monetization_on,
                        onTap: () {
                          String? parentId = userData?.data?.parent?.id;
                          if (parentId != null && parentId.isNotEmpty) {
                            NavigatorService.pushNamed(AppRoutes.refund);
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Parent ID not found. Please refresh or login again.'),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }
                        },
                      ),
                      customDivider(padding: 24.w),
                      sectionHeader("Preference"),
                      const SizedBox(
                        height: 9,
                      ),
                      profile(
                        context: context,
                        name: "Language",
                        iconData: Icons.language,
                      ),
                      customDivider(padding: 24.w),
                      notification(
                        context: context,
                        name: "Activities Notification",
                        iconData: Icons.notifications,
                        onChanged: toggleActivities,
                        switchValue: _activities,
                      ),
                      customDivider(padding: 24.w),
                      notification(
                        context: context,
                        name: "Promotion Notification",
                        iconData: Icons.telegram,
                        onChanged: togglePromotion,
                        switchValue: _promotion,
                      ),
                      customDivider(padding: 24.w),
                      sectionHeader("Privacy"),
                      const SizedBox(
                        height: 9,
                      ),
                      profile(
                        context: context,
                        name: "Change Password",
                        iconData: Icons.fingerprint,
                        onTap: () {
                          Navigator.pushNamed(context, AppRoutes.resetPassword,
                              arguments:
                                  sharedRepository.getParentData()?.email);
                        },
                      ),
                      customDivider(padding: 24.w),
                      profile(
                        context: context,
                        name: "Terms and Condition",
                        iconData: Icons.description,
                        onTap: () {
                          Navigator.pushNamed(context, AppRoutes.terms);
                        },
                      ),
                      customDivider(padding: 24.w),
                      sectionHeader("Assistance"),
                      const SizedBox(
                        height: 9,
                      ),
                      profile(
                        context: context,
                        name: "Help Centre",
                        iconData: Icons.help_outlined,
                        onTap: () {
                          Navigator.pushNamed(context, AppRoutes.faq);
                        },
                      ),
                      customDivider(padding: 24.w),
                      profile(
                        context: context,
                        name: "Contact Us",
                        iconData: Icons.chat,
                        onTap: () {
                          Navigator.pushNamed(context, AppRoutes.contactUs,
                              arguments: userData?.data?.parent?.email ?? '');
                        },
                      ),
                      // customDivider(padding: 24.w),
                      // profile(
                      //   context: context,
                      //   name: "Report",
                      //   iconData: Icons.report,
                      //   onTap: () {
                      //     Navigator.pushNamed(context, AppRoutes.childReport);
                      //   },
                      // ),
                      customDivider(padding: 24.w),
                      SizedBox(
                        height: 80.h,
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 31.w, right: 31.w),
                        child: Button(
                          buttonText: isLoggedIn == true ? "Log Out" : "Log In",
                          color: AppPallete.secondaryColor,
                          width: double.infinity,
                          height: 49.h,
                          onPressed: isLoggedIn == true
                              ? () async {
                                  try {
                                    await sharedRepository.logout();
                                    Navigator.pushNamedAndRemoveUntil(context,
                                        AppRoutes.logIn, (route) => false);
                                  } catch (e) {
                                    print("Error during logout: $e");
                                  }
                                }
                              : () {
                                  Navigator.pushNamed(context, AppRoutes.logIn);
                                },
                        ),
                      ),
                      SizedBox(height: 5.h),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  Widget topBar({
    required BuildContext context,
    required String name,
    required int notification,
    required String imagePath,
    required int message,
    required String rating,
  }) {
    // Generate a unique key each time to force UI rebuild
    final uniqueKey = UniqueKey();

    return Container(
      width: double.infinity,
      height: 205,
      color: AppPallete.secondaryColor,
      child: Stack(
        children: [
          Positioned(
            right: 0,
            top: 0,
            left: 0,
            child: customTopBarOnlyIcon(
              context: context,
              badgeCount1: 0,
              badgeCount2: 0,
              onTap1: () {
                Navigator.pushNamed(context, AppRoutes.notification,
                    arguments: locator<SharedRepository>().getParentData()?.id);
              },
              onTap2: () {
                Navigator.pushNamed(context, AppRoutes.centerMessage,
                    arguments: 'user');
              },
            ),
          ),
          Positioned(
            top: 148,
            left: 163,
            child: customtext(
              context: context,
              newYear: name,
              font: 25.sp,
              weight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          Positioned(
            top: 70,
            left: 28,
            child: CustomImageBuilder(
              key:
                  Key("profile_image_${DateTime.now().millisecondsSinceEpoch}"),
              imagePath: imagePath,
              height: 125.h,
              width: 125.w,
              borderRadius: 125.w,
            ),
          ),
        ],
      ),
    );
  }

  Widget profile({
    required BuildContext context,
    required String name,
    required IconData iconData,
    VoidCallback? onTap,
  }) {
    return Padding(
      padding: EdgeInsets.only(left: 24, right: 24),
      child: InkWell(
        onTap: () {
          if (isLoggedIn == true ||
              name == 'Terms and Condition' ||
              name == 'Contact Us') {
            print(name);
            if (onTap != null) onTap();
          } else {
            Navigator.pushNamed(context, AppRoutes.logIn);
          }
        },
        child: SizedBox(
          height: 33.h,
          width: double.infinity,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    iconData,
                    color: AppPallete.greyWord,
                    size: 20,
                  ),
                  SizedBox(width: 26.w),
                  customtext(
                    context: context,
                    newYear: name,
                    font: 15,
                    weight: FontWeight.w500,
                  ),
                ],
              ),
              const Icon(
                Icons.arrow_forward_ios,
                color: AppPallete.greyWord,
                size: 17,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget sectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.only(left: 25.w, top: 20.h),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 15,
          fontWeight: FontWeight.w400,
          color: AppPallete.darkGrey,
        ),
      ),
    );
  }

  Widget customDivider({required double padding}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: padding),
      child: Divider(color: AppPallete.greyWord),
    );
  }

  Widget notification({
    required BuildContext context,
    required String name,
    required IconData iconData,
    required bool switchValue,
    required Function(bool) onChanged,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(
                iconData,
                color: AppPallete.greyWord,
                size: 20,
              ),
              SizedBox(width: 26.w),
              customtext(
                context: context,
                newYear: name,
                font: 15.sp,
                weight: FontWeight.w500,
              ),
            ],
          ),
          Switch(
            value: switchValue,
            onChanged: onChanged,
            activeColor: AppPallete.secondaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildProfileSkeleton() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SingleChildScrollView(
        physics: const NeverScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top bar placeholder
            Container(
              width: double.infinity,
              height: 205.h,
              color: Colors.white,
            ),
            SizedBox(height: 19.h),
            // Wallet card placeholder
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 17.w),
              child: Container(
                height: 100.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ),
            SizedBox(height: 25.h),
            // Section header placeholder
            Padding(
              padding: EdgeInsets.only(left: 25.w),
              child: Container(width: 100.w, height: 14.h, color: Colors.white),
            ),
            SizedBox(height: 9.h),
            // List items placeholders
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 8,
              itemBuilder: (context, index) {
                return Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 24.w, vertical: 10.h),
                  child: Row(
                    children: [
                      Container(width: 20, height: 20, color: Colors.white),
                      SizedBox(width: 26.w),
                      Container(
                          width: 150.w, height: 14.h, color: Colors.white),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
