import 'package:class_z/core/common/presentation/blocs/savedCenterBloc/saved_center_bloc.dart';
import 'package:class_z/core/imports.dart';
import 'package:share_plus/share_plus.dart';
import 'package:class_z/services/map_service.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:flutter/foundation.dart';

class CentreView extends StatefulWidget {
  final CenterData? center;
  final bool? isSaved;
  final bool? bottomView;
  const CentreView({this.bottomView, this.center, this.isSaved, super.key});

  @override
  State<CentreView> createState() => _CentreViewState();
}

class _CentreViewState extends State<CentreView> {
  bool isSaved = false;
  // Map to track saved status of other locations
  Map<String, bool> savedCentersMap = {};

  bool _isPageLoading = true;
  bool _isLoadingCoaches = true;
  bool _isLoadingClasses = true;
  bool _isLoadingReviews = true;
  bool _isLoadingBranches = true;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    isSaved = widget.isSaved ?? false;
    _fetchData();
  }

  Future<void> _fetchData() async {
    print('centerId :${widget.center?.id}');
    await Future.microtask(() {
      context.read<CenterBloc>().add(
            GetCoachesByCenterIdEvent(centerId: widget.center?.id ?? ""),
          );
      context.read<CenterBloc>().add(
            GetAllClassesEvent(centerId: widget.center?.id ?? ""),
          );
    });

    await Future.microtask(() {
      context.read<ReviewBloc>().add(
            GetReviewByIdEvent(id: widget.center?.id ?? "", type: "center"),
          );
    });
    await Future.microtask(() {
      context
          .read<OwnerBloc>()
          .add(GetBranchsEvent(ownerId: widget.center?.ownerId ?? ''));
    });
  }

  void _checkIfAllDataIsLoaded() {
    if (!_isLoadingCoaches &&
        !_isLoadingClasses &&
        !_isLoadingReviews &&
        !_isLoadingBranches) {
      if (mounted) {
        setState(() {
          _isPageLoading = false;
        });
      }
    }
  }

  // Share center information
  void _shareCenter() async {
    if (widget.center == null) return;

    final centerName = widget.center?.legalName ?? "Center";
    final description = widget.center?.description ?? "Check out this center!";
    final address =
        addressGenerator(address: widget.center?.address, condition: 'both');

    final shareText = """
Check out $centerName on ClassZ!

$description

Located at: $address

Download ClassZ app to learn more.
""";

    try {
      // Check if there's a main image to share
      final mainImageUrl = widget.center?.mainImage?.url;

      if (mainImageUrl != null && mainImageUrl.isNotEmpty) {
        // There's an image, but we need to handle both relative and absolute URLs
        final fullImageUrl = mainImageUrl.startsWith('http')
            ? mainImageUrl
            : "${AppText.device}$mainImageUrl";

        // Note: Sharing files requires downloading the image first, which would
        // require additional code. For now, we'll just share the text.
        // Logging removed as requested
      }

      // Share text only for now
      await Share.share(
        shareText,
        subject: 'Check out this center!',
      );

      // Logging removed as requested
    } catch (e) {
      // Logging removed as requested

      // More user-friendly error message
      String errorMessage = 'Could not share center';

      // Provide more specific error messages for known issues
      if (e.toString().contains('MissingPluginException')) {
        errorMessage =
            'Sharing requires app restart. Please restart the app and try again.';
      }

      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text(errorMessage)));
    }
  }

  // Navigate to chat with the center
  void _navigateToChat() async {
    try {
      if (widget.center == null || widget.center?.id == null) {
        ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Cannot message this center')));
        return;
      }

      // Show loading indicator while preparing chat data
      // Use a safer loading approach - don't show full-screen loader for this action
      final scaffold = ScaffoldMessenger.of(context);
      scaffold.showSnackBar(const SnackBar(
        content: Text('Opening chat...'),
        duration: Duration(seconds: 1),
      ));

      // Get required data for chat
      final sharedRepo = locator<SharedRepository>();
      final parentData = sharedRepo.getParentData();

      // Validate that user is logged in
      if (parentData == null ||
          parentData.id == null ||
          parentData.id!.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('Please log in to message this center')));
        return;
      }

      final String? userImage = parentData.image?.url;
      final String userId = sharedRepo.getUserId();

      // Process image paths - sanitize URLs
      final centerImagePath =
          AppText.sanitizeUrl(widget.center?.mainImage?.url ?? '');
      final userImagePath = AppText.sanitizeUrl(userImage ?? '');

      // Set up chat data
      final Map<String, dynamic> messagesData = {
        "title": widget.center?.displayName ?? "Center",
        "imagePath": centerImagePath,
        "senderImage": userImagePath,
        "id": widget.center?.id ?? '',
        "senderId": userId,
        "senderType": "user",
        "oppositeModel": "center",
      };

      // Log the chat data for debugging
      // Logging removed as requested

      // Use pushNamed instead of NavigatorService to have more control
      Navigator.of(context).pushNamed(
        AppRoutes.chat,
        arguments: messagesData,
      );
    } catch (e) {
      // Logging removed as requested
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open chat. Please try again.')));
    }
  }

  List<CoachModel> coachs = [];
  List<ReviewModel> reviews = [];
  List<CenterData> branches = [];
  List<ClassModel> classes = [];
  // Handle authentication errors in the build method with BlocListener

  void _onFavoriteToggle(String centerId, bool newValue) {
    // Only log in debug mode
    if (kDebugMode) {
      // Logging removed as requested
    }

    // Immediately update UI state for instant feedback
    if (centerId == widget.center?.id) {
      // Main center
      setState(() {
        isSaved = newValue;
      });
    } else {
      // Branch center
      setState(() {
        savedCentersMap[centerId] = newValue;
      });
    }

    // Perform the save/unsave action
    if (newValue) {
      context.read<SavedCenterBloc>().add(SaveCenterEvent(centerId));
    } else {
      context.read<SavedCenterBloc>().add(UnsaveCenterEvent(centerId));
    }
  }

  void _checkSavedStatusForBranches(List<CenterData> branches) {
    // Check saved status for each branch
    for (var branch in branches) {
      if (branch.id != null && mounted) {
        context.read<SavedCenterBloc>().add(CheckCenterSavedEvent(branch.id!));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    List<String> languages = widget.center?.languages?.toList() ?? [];
    // Removed excessive height logging to reduce console spam
    return BlocListener<SavedCenterBloc, SavedCenterState>(
      listener: (context, state) {
        if (state is SavedCenterAuthError) {
          // Show login dialog when authentication fails
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return AlertDialog(
                title: Text('Session Expired'),
                content: Text('Your session has expired. Please log in again.'),
                actions: <Widget>[
                  TextButton(
                    child: Text('Login'),
                    onPressed: () {
                      // Clear token and navigate to login screen
                      locator<SharedRepository>().deleteToken();
                      Navigator.of(context).popUntil((route) => route.isFirst);
                      // Navigate to login screen - use the appropriate route
                      Navigator.of(context).pushReplacementNamed('/login');
                    },
                  ),
                ],
              );
            },
          );
        } else if (state is CenterSavedStatus) {
          // Handle saved status check results for branches
          if (kDebugMode) {
            // Logging removed as requested
          }
          setState(() {
            savedCentersMap[state.centerId] = state.isSaved;
          });
        } else if (state is CenterSavedSuccess) {
          if (kDebugMode) {
            // Logging removed as requested
          }

          // Update UI state for main center
          if (state.centerId == widget.center?.id) {
            setState(() {
              isSaved = true;
            });
          } else {
            // Update saved status for branch centers
            setState(() {
              savedCentersMap[state.centerId] = true;
            });
          }

          // Don't show SnackBar here to avoid duplicate messages from other screens
        } else if (state is CenterUnsavedSuccess) {
          if (kDebugMode) {
            // Logging removed as requested
          }

          // Update UI state for main center
          if (state.centerId == widget.center?.id) {
            setState(() {
              isSaved = false;
            });
          } else {
            // Update saved status for branch centers
            setState(() {
              savedCentersMap[state.centerId] = false;
            });
          }

          // Don't show SnackBar here to avoid duplicate messages from other screens
        } else if (state is SavedCenterError) {
          if (kDebugMode) {
            // Logging removed as requested
          }
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      child: Scaffold(
          bottomNavigationBar: widget.bottomView != null && widget.bottomView!
              ? SizedBox(
                  height: 76.h,
                  width: 430.w,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      customDivider(width: 430.w, padding: 0),
                      SizedBox(
                        height: 13.h,
                      ),
                      Button(
                          height: 49.h,
                          width: 289.w,
                          buttonText: "Programs",
                          shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                          onPressed: () {
                            NavigatorService.pushNamed(AppRoutes.programs,
                                arguments: widget.center?.id);
                          },
                          color: AppPallete.secondaryColor),
                    ],
                  ))
              : null,
          body: MultiBlocListener(
            listeners: [
              BlocListener<CenterBloc, CenterState>(
                listener: (context, state) {
                  if (state is CoachListFetchSuccess) {
                    setState(() {
                      coachs = state.coaches;
                      _isLoadingCoaches = false;
                    });
                  } else if (state is ClassListFetchSuccess) {
                    setState(() {
                      classes = state.classes;
                      _isLoadingClasses = false;
                    });
                  } else if (state is CenterErrorState) {
                    setState(() {
                      _isLoadingCoaches = false;
                      _isLoadingClasses = false;
                    });
                    errorState(context: context, error: state.message);
                  }
                  _checkIfAllDataIsLoaded();
                },
              ),
              BlocListener<ReviewBloc, ReviewState>(
                listener: (context, state) {
                  if (state is GetReviewByCenterIdSuccessState) {
                    setState(() {
                      reviews = state.reviews;
                      _isLoadingReviews = false;
                    });
                  } else if (state is ReviewErrorState) {
                    setState(() {
                      _isLoadingReviews = false;
                    });
                    errorState(context: context, error: state.message);
                  }
                  _checkIfAllDataIsLoaded();
                },
              ),
              BlocListener<OwnerBloc, OwnerState>(
                listener: (context, state) {
                  if (state is GetBranchSuccessState) {
                    setState(() {
                      branches = state.branches
                          .where((branch) => branch.id != widget.center?.id)
                          .toList();
                      _isLoadingBranches = false;
                    });
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      _checkSavedStatusForBranches(branches);
                    });
                  } else if (state is OwnerErrorState) {
                    setState(() {
                      _isLoadingBranches = false;
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Could not load other locations')),
                    );
                  }
                  _checkIfAllDataIsLoaded();
                },
              )
            ],
            child: _isPageLoading
                ? _buildPageSkeleton()
                : SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(
                              left: 19.w, top: 85.h, right: 39.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              customBackButton(),
                              customTopBarOnlyIcon(
                                context: context,
                                icon1: Icons.share,
                                icon2: Icons.message,
                                badgeCount1: 0,
                                badgeCount2: 0,
                                topPadding: 0,
                                rightPadding: 0,
                                onTap1: _shareCenter,
                                onTap2: _navigateToChat,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 36.h),
                        Padding(
                          padding: EdgeInsets.only(left: 15.w),
                          child: customTitle(
                            context,
                            widget.center?.displayName ?? "",
                            30,
                          ),
                        ),
                        SizedBox(height: 17.h),
                        Padding(
                            padding: EdgeInsets.symmetric(horizontal: 14.w),
                            child: _showImages()),
                        SizedBox(height: 18.h),
                        descriptionRow(context),
                        SizedBox(height: 9.h),
                        Center(
                          child: Container(
                            margin: EdgeInsets.only(left: 24.w, right: 29.w),
                            child: Text(
                              widget.center?.description ?? "",
                              overflow: TextOverflow.ellipsis,
                              maxLines: 4,
                              style: TextStyle(
                                fontSize: 15.sp,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 17.h),
                        customDivider(width: 406.w, padding: 15.w),
                        SizedBox(height: 17.h),
                        Padding(
                          padding: EdgeInsets.only(left: 15.w),
                          child: customTitle(context, "Class Language", 20),
                        ),
                        SizedBox(height: 14.h),
                        for (var language in languages)
                          Padding(
                            padding: EdgeInsets.only(left: 15.w, bottom: 9.h),
                            child: _classLanguage(context, language),
                          ),
                        SizedBox(height: 17.h),
                        customDivider(width: 406.w, padding: 15.w),
                        SizedBox(height: 17.h),
                        Padding(
                          padding: EdgeInsets.only(left: 15.w),
                          child: customTitle(context, "Coach", 20.sp),
                        ),
                        SizedBox(height: 17.h),
                        _isLoadingCoaches
                            ? _buildHorizontalListSkeleton(
                                height: 197.h, itemWidth: 150.w)
                            : coachs.isNotEmpty
                                ? Container(
                                    height: 197.h,
                                    child: ListView.separated(
                                      padding: EdgeInsets.only(
                                          left: 9.w, right: 9.w),
                                      scrollDirection: Axis.horizontal,
                                      itemBuilder: (context, index) {
                                        var coach = coachs[index];
                                        String allSkillNames = coach.skill
                                                ?.map(
                                                    (skill) => skill.skillname)
                                                .join(', ') ??
                                            '';
                                        return centreViewCoachList(
                                          sen: coach.sen ?? false,
                                          context: context,
                                          ageGroup:
                                              "Age ${coach.ageFrom} to ${coach.ageTo}",
                                          name: coach.displayName ?? "",
                                          imageUrl:
                                              "${AppText.device}${coach.mainImage?.url}",
                                          rating: coach.rating ?? 0.0,
                                          skills: allSkillNames,
                                          onTap: () {
                                            NavigatorService.pushNamed(
                                              AppRoutes.coachView,
                                              arguments: coach.id,
                                            );
                                          },
                                        );
                                      },
                                      separatorBuilder: (context, index) =>
                                          SizedBox(width: 10.w),
                                      itemCount: coachs.length,
                                    ),
                                  )
                                : Padding(
                                    padding: EdgeInsets.only(left: 15.w),
                                    child: customtext(
                                        context: context,
                                        newYear: "No Coaches to Show",
                                        font: 15.sp),
                                  ),
                        SizedBox(height: 17.h),
                        customDivider(width: 406.w, padding: 15.w),
                        SizedBox(height: 17.h),
                        Padding(
                          padding: EdgeInsets.only(left: 15.w),
                          child: Row(
                            children: [
                              customTitle(context, "Review", 20),
                              SizedBox(width: 15.w),
                              Row(
                                children: [
                                  customSvgPicture(
                                    imagePath: ImagePath.starSvg,
                                    height: 24.h,
                                    width: 27.w,
                                    color: AppPallete.rating,
                                  ),
                                  SizedBox(width: 3.w),
                                  if (widget.center?.reviewCount != null &&
                                      widget.center!.reviewCount! > 0) ...[
                                    customtext(
                                      context: context,
                                      newYear: widget.center?.rating
                                              ?.toStringAsFixed(1) ??
                                          "0.0",
                                      font: 30.sp,
                                      weight: FontWeight.bold,
                                    ),
                                    Padding(
                                      padding: EdgeInsets.only(top: 16.h),
                                      child: customtext(
                                        context: context,
                                        newYear:
                                            "(${widget.center?.reviewCount})",
                                        font: 12.sp,
                                        weight: FontWeight.w400,
                                      ),
                                    ),
                                  ]
                                ],
                              )
                            ],
                          ),
                        ),
                        SizedBox(height: 17.h),
                        _isLoadingReviews
                            ? _buildHorizontalListSkeleton(
                                height: 104.h, itemWidth: 280.w)
                            : reviews.isNotEmpty
                                ? SizedBox(
                                    height: 104.h,
                                    child: ListView.separated(
                                        scrollDirection: Axis.horizontal,
                                        padding: EdgeInsets.only(
                                            left: 9.w, right: 9.w),
                                        itemBuilder: (context, index) {
                                          final review = reviews[index];
                                          String formattedDate = review.date !=
                                                  null
                                              ? DateFormat('dd/MM/yyyy')
                                                  .format(review.date!)
                                              : 'N/A'; // Handle potential null date

                                          return reviewCard(
                                              context: context,
                                              title: review.title ?? "",
                                              rating: review.rating.toString(),
                                              comment: review.comment ?? "",
                                              dateTime: formattedDate);
                                        },
                                        separatorBuilder: (context, index) =>
                                            SizedBox(width: 10.w),
                                        itemCount: reviews.length),
                                  )
                                : Padding(
                                    padding:
                                        EdgeInsets.only(left: 9.w, right: 9.w),
                                    child: Text('No Review to Show'),
                                  ),
                        SizedBox(height: 17.h),
                        customDivider(width: 406.w, padding: 15.w),
                        SizedBox(height: 17.h),
                        Padding(
                          padding: EdgeInsets.only(left: 15.w),
                          child:
                              customTitle(context, "More Information", 20.sp),
                        ),
                        SizedBox(height: 17.h),
                        Padding(
                          padding: EdgeInsets.only(left: 15.w),
                          child: _moreInfo(context),
                        ),
                        SizedBox(height: 17.h),
                        customDivider(width: 406.w, padding: 15.w),
                        SizedBox(height: 17.h),
                        Padding(
                          padding: EdgeInsets.only(left: 15.w),
                          child: customTitle(context, "Other Locations", 20.sp),
                        ),
                        SizedBox(height: 17.h),
                        _isLoadingBranches
                            ? _buildHorizontalListSkeleton(
                                height: 163.h, itemWidth: 331.w)
                            : branches.isNotEmpty
                                ? Container(
                                    height: 163.h,
                                    padding:
                                        EdgeInsets.only(left: 9.w, right: 9.w),
                                    child: ListView.separated(
                                      scrollDirection: Axis.horizontal,
                                      itemBuilder: (context, index) {
                                        final branch = branches[index];
                                        final isBranchSaved =
                                            savedCentersMap[branch.id] ?? false;

                                        return buildRowGallery(
                                          imageHeight: 163.h,
                                          imageWidth: 331.w,
                                          context: context,
                                          startAge: branch.startAge ?? '',
                                          center: branch.displayName ?? '',
                                          location: addressGenerator(
                                              address: branch.address),
                                          category: branch.description ?? '',
                                          imagepath: imageStringGenerator(
                                              imagePath:
                                                  branch.mainImage?.url ?? ''),
                                          rating: branch.rating ?? 0,
                                          isSaved: isBranchSaved,
                                          centerId: branch.id,
                                          onFavoriteToggle: (newValue) {
                                            if (branch.id != null) {
                                              _onFavoriteToggle(
                                                  branch.id!, newValue);
                                            }
                                          },
                                          onTap: () {
                                            NavigatorService.pushNamed(
                                                AppRoutes.centreView,
                                                arguments: {
                                                  'center': branch,
                                                  'isSaved': isBranchSaved,
                                                  'bottomView': true
                                                });
                                          },
                                        );
                                      },
                                      separatorBuilder: (context, index) =>
                                          SizedBox(width: 10.w),
                                      itemCount: branches.length,
                                    ),
                                  )
                                : Padding(
                                    padding: EdgeInsets.only(
                                        left: 15.w, right: 15.w),
                                    child: Text(
                                      'No other locations available',
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  ),
                        SizedBox(height: 10.h),
                      ],
                    ),
                  ),
          )),
    );
  }

  Widget _showImages() {
    if (widget.center?.images == null)
      return Center(
        child: Text('No images to show'),
      );

    return widget.center!.images!.length > 0
        ? centreViewImageWidget(
            context: context,
            centerId: widget.center?.id ?? '',
            images: widget.center?.images ?? [],
            isSaved: isSaved,
            onFavoriteToggle: (newValue) {
              if (widget.center?.id != null) {
                _onFavoriteToggle(widget.center?.id ?? '', newValue);
              }
            },
            sen: widget.center?.sen ?? false,
          )
        : SizedBox();
  }

  Widget _classLanguage(BuildContext context, String text,
      {bool? check, Color? color, double? font, double? padding}) {
    return Row(
      children: [
        if (check != null)
          Icon(
            Icons.check_box,
            size: 16.w,
          )
        else
          Icon(
            Icons.check,
            size: 16.w,
          ),
        SizedBox(
          width: 7.w,
        ),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
                fontSize: 10.sp,
                fontWeight: FontWeight.w400,
                color: color ?? Colors.black),
          ),
        )
      ],
    );
  }

  Widget descriptionRow(BuildContext context) {
    final parentData = locator<SharedRepository>().getParentData();
    final userBalance = parentData?.balance ?? 0;

    double? priceFrom;
    double? priceTo;

    if (classes.isNotEmpty) {
      final charges = classes
          .map((c) => c.charge?.toDouble())
          .where((c) => c != null)
          .toList();
      if (charges.isNotEmpty) {
        charges.sort();
        priceFrom = charges.first;
        priceTo = charges.last;
      }
    }

    priceFrom ??= widget.center?.priceFrom?.toDouble();
    priceTo ??= widget.center?.priceTo?.toDouble();

    bool showZCoin = false;
    if (priceFrom != null) {
      showZCoin = userBalance >= priceFrom;
    }

    String priceText;
    if (_isLoadingClasses) {
      priceText = '...';
    } else if (priceFrom != null && priceTo != null) {
      if (priceFrom == 0 && priceTo == 0) {
        priceText = 'Price not available';
      } else if (priceFrom == priceTo) {
        // Convert to HKD if showing HKD currency (1 ZCoin = 25 HKD)
        final displayPrice = showZCoin ? priceFrom : (priceFrom * 25);
        priceText = displayPrice.toStringAsFixed(0);
      } else {
        // Convert to HKD if showing HKD currency (1 ZCoin = 25 HKD)
        final displayPriceFrom = showZCoin ? priceFrom : (priceFrom * 25);
        final displayPriceTo = showZCoin ? priceTo : (priceTo * 25);
        priceText =
            "From ${displayPriceFrom.toStringAsFixed(0)} - ${displayPriceTo.toStringAsFixed(0)}";
      }
    } else {
      priceText = "Price not available";
    }

    return Padding(
      padding: EdgeInsets.only(left: 24.w, right: 28.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 2.0),
            child: customTitle(context, "Description", 20),
          ),
          Flexible(
            child: Wrap(
              alignment: WrapAlignment.end,
              spacing: 6.0,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                if (priceText != 'Price not available')
                  if (showZCoin)
                    Image.asset(ImagePath.z, height: 20)
                  else
                    customTitle(context, "HKD", 20),
                customTitle(context, priceText, 20),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _moreInfo(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left column - Services
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.center?.services == null ||
                  widget.center!.services!.isEmpty)
                customtext(
                  context: context,
                  newYear: "No services available",
                  font: 14.sp,
                  color: Colors.grey,
                )
              else
                ListView.builder(
                  shrinkWrap: true,
                  padding: EdgeInsets.zero,
                  physics: NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    final service = widget.center?.services?[index];
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _classLanguage(context, service ?? "",
                            padding: 4.w, font: 10.sp),
                        const SizedBox(height: 10),
                      ],
                    );
                  },
                  itemCount: widget.center?.services?.length ?? 0,
                ),
            ],
          ),
        ),
        SizedBox(width: 20.w),
        // Right column - Location and Contact
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Map Button
              GestureDetector(
                onTap: _openInGoogleMaps,
                child: Container(
                  height: 172.2,
                  width: 171.2,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(10.r),
                    border: Border.all(color: Colors.grey.shade300),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Stack(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          image: DecorationImage(
                            image: AssetImage(ImagePath.locationInfo),
                            fit: BoxFit.cover,
                            colorFilter: ColorFilter.mode(
                              Colors.white.withOpacity(0.15),
                              BlendMode.lighten,
                            ),
                          ),
                        ),
                      ),
                      Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.map,
                              color: AppPallete.secondaryColor,
                              size: 40,
                            ),
                            SizedBox(height: 10),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                color: AppPallete.secondaryColor,
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.open_in_new,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                  SizedBox(width: 5),
                                  Text(
                                    "Open in Google Maps",
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12.sp,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 10.h),
              // Address Display
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  customSvgPicture(
                    imagePath: ImagePath.locationSvg,
                    height: 16.67.h,
                    width: 11.67.w,
                  ),
                  SizedBox(width: 4.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (widget.center?.address?.address1 != null)
                          customtext(
                            context: context,
                            newYear: widget.center!.address!.address1!,
                            font: 12.sp,
                            weight: FontWeight.w400,
                          ),
                        if (widget.center?.address?.city != null ||
                            widget.center?.address?.region != null)
                          customtext(
                            context: context,
                            newYear: [
                              widget.center?.address?.city,
                              widget.center?.address?.region,
                            ]
                                .where((e) => e != null && e.isNotEmpty)
                                .join(', '),
                            font: 12.sp,
                            weight: FontWeight.w400,
                          ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 10.h),
              // Phone Number
              Row(
                children: [
                  customSvgPicture(
                    imagePath: ImagePath.phoneSvg,
                    height: 16.67.h,
                    width: 11.67.w,
                  ),
                  SizedBox(width: 6.w),
                  customtext(
                    context: context,
                    newYear: widget.center?.isFreelanceEducator == true
                        ? "Individual Educator"
                        : widget.center?.companyNumber ?? "",
                    font: 12.sp,
                  ),
                ],
              ),
              SizedBox(height: 10.h),
              // Opening Hours
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 2.0),
                    child: customSvgPicture(
                      imagePath: ImagePath.timeShowingSvg,
                      height: 16.67.h,
                      width: 11.67.w,
                    ),
                  ),
                  SizedBox(width: 6.w),
                  Expanded(
                    child: OpeningHoursFormatter.createCollapsibleOpeningHours(
                        context, widget.center?.openingHours,
                        dayStyle: TextStyle(
                            fontSize: 12.sp, fontWeight: FontWeight.bold),
                        hoursStyle: TextStyle(fontSize: 12.sp)),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _openInGoogleMaps() async {
    if (widget.center?.address == null) {
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('No address available for this center')));
      return;
    }

    try {
      final mapService = locator<MapService>();
      final address2 = widget.center?.address?.address2;

      // First try to use Google Maps link if available
      if (address2 != null && mapService.isGoogleMapsLink(address2)) {
        // Special handling for directions URLs - launch them directly
        if (address2.contains('/maps/dir/')) {
          // Logging removed as requested
          final success = await launchUrlString(address2);
          if (!success) {
            ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Could not open maps application')));
          }
          return;
        }

        // Try to extract coordinates from the link first
        final coordinates = mapService.extractCoordinatesFromMapUrl(address2);
        if (coordinates != null) {
          // If we have coordinates, use them for a better map experience
          final centerName =
              Uri.encodeComponent(widget.center?.displayName ?? 'Center');

          final url = Platform.isIOS
              ? 'https://maps.apple.com/?ll=${coordinates.latitude},${coordinates.longitude}&q=$centerName'
              : 'https://www.google.com/maps/search/?api=1&query=${coordinates.latitude},${coordinates.longitude}';

          final success = await launchUrlString(url);
          if (!success) {
            ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Could not open maps application')));
          }
          return;
        }

        // If we couldn't extract coordinates, just launch the URL directly
        final success = await launchUrlString(address2);
        if (!success) {
          ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Could not open maps application')));
        }
        return;
      }

      // Then try coordinates
      if (widget.center?.address?.coordinates != null) {
        final coordinates = widget.center!.address!.coordinates;
        if (coordinates != null &&
            coordinates['lat'] != null &&
            coordinates['lng'] != null) {
          final lat = double.parse(coordinates['lat'].toString());
          final lng = double.parse(coordinates['lng'].toString());
          final centerName =
              Uri.encodeComponent(widget.center?.displayName ?? 'Center');

          final url = Platform.isIOS
              ? 'https://maps.apple.com/?ll=$lat,$lng&q=$centerName'
              : 'https://www.google.com/maps/search/?api=1&query=$lat,$lng';

          final success = await launchUrlString(url);
          if (!success) {
            ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Could not open maps application')));
          }
          return;
        }
      }

      // Next, try using only address2 if available
      if (address2 != null && address2.isNotEmpty) {
        final encodedAddress = Uri.encodeComponent(address2);
        final url = Platform.isIOS
            ? 'https://maps.apple.com/?q=$encodedAddress'
            : 'https://www.google.com/maps/search/?api=1&query=$encodedAddress';

        final success = await launchUrlString(url);
        if (!success) {
          ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Could not open maps application')));
        }
        return;
      }

      // Finally, use formatted address as fallback
      final address = _getFormattedAddress();
      if (address.isNotEmpty) {
        final encodedAddress = Uri.encodeComponent(address);
        final url = Platform.isIOS
            ? 'https://maps.apple.com/?q=$encodedAddress'
            : 'https://www.google.com/maps/search/?api=1&query=$encodedAddress';

        final success = await launchUrlString(url);
        if (!success) {
          ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Could not open maps application')));
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Could not determine center location')));
      }
    } catch (e) {
      // Logging removed as requested
      ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open maps application')));
    }
  }

  String _getFormattedAddress() {
    if (widget.center?.address == null) return '';

    final address = widget.center!.address!;
    final parts = [
      if (address.address1 != null && address.address1!.isNotEmpty)
        address.address1,
      if (address.city != null && address.city!.isNotEmpty) address.city,
      if (address.region != null && address.region!.isNotEmpty) address.region,
      if (address.country != null && address.country!.isNotEmpty)
        address.country
      else
        'Hong Kong'
    ];

    return parts.join(', ');
  }

  Widget _buildHorizontalListSkeleton({
    required double height,
    required double itemWidth,
    int itemCount = 3,
  }) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SizedBox(
        height: height,
        child: ListView.separated(
          padding: EdgeInsets.only(left: 9.w, right: 9.w),
          scrollDirection: Axis.horizontal,
          itemBuilder: (context, index) {
            return Container(
              width: itemWidth,
              height: height,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.r),
              ),
            );
          },
          separatorBuilder: (context, index) => SizedBox(width: 10.w),
          itemCount: itemCount,
        ),
      ),
    );
  }

  Widget _buildPageSkeleton() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SingleChildScrollView(
        physics: const NeverScrollableScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top bar skeleton
            Padding(
              padding: EdgeInsets.only(left: 19.w, top: 85.h, right: 39.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(width: 24.w, height: 24.h, color: Colors.white),
                  Row(
                    children: [
                      Container(width: 24.w, height: 24.h, color: Colors.white),
                      SizedBox(width: 16.w),
                      Container(width: 24.w, height: 24.h, color: Colors.white),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(height: 36.h),
            // Title skeleton
            Padding(
              padding: EdgeInsets.only(left: 15.w),
              child: Container(width: 200.w, height: 30.h, color: Colors.white),
            ),
            SizedBox(height: 17.h),
            // Image skeleton
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 14.w),
              child: Container(
                height: 218.h,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.r),
                ),
              ),
            ),
            SizedBox(height: 18.h),
            // Description skeleton
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(width: 150.w, height: 20.h, color: Colors.white),
                  SizedBox(height: 10.h),
                  Container(
                      width: double.infinity,
                      height: 14.h,
                      color: Colors.white),
                  SizedBox(height: 6.h),
                  Container(
                      width: double.infinity,
                      height: 14.h,
                      color: Colors.white),
                  SizedBox(height: 6.h),
                  Container(width: 250.w, height: 14.h, color: Colors.white),
                ],
              ),
            ),
            SizedBox(height: 17.h),
            customDivider(width: 406.w, padding: 15.w),
            SizedBox(height: 17.h),
            // "Coach" section skeleton
            Padding(
              padding: EdgeInsets.only(left: 15.w),
              child: Container(width: 100.w, height: 20.h, color: Colors.white),
            ),
            SizedBox(height: 17.h),
            _buildHorizontalListSkeleton(height: 197.h, itemWidth: 150.w),
            SizedBox(height: 17.h),
            customDivider(width: 406.w, padding: 15.w),
            SizedBox(height: 17.h),
            // "Review" section skeleton
            Padding(
              padding: EdgeInsets.only(left: 15.w),
              child: Container(width: 100.w, height: 20.h, color: Colors.white),
            ),
            SizedBox(height: 17.h),
            _buildHorizontalListSkeleton(height: 104.h, itemWidth: 280.w),
            SizedBox(height: 17.h),
            customDivider(width: 406.w, padding: 15.w),
            SizedBox(height: 17.h),
            // "Other Locations" section skeleton
            Padding(
              padding: EdgeInsets.only(left: 15.w),
              child: Container(width: 150.w, height: 20.h, color: Colors.white),
            ),
            SizedBox(height: 17.h),
            _buildHorizontalListSkeleton(height: 163.h, itemWidth: 331.w),
          ],
        ),
      ),
    );
  }
}
