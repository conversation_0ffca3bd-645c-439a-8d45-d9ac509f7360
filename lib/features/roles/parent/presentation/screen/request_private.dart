import 'package:class_z/core/imports.dart';

class Request extends StatefulWidget {
  const Request({super.key});

  @override
  State<Request> createState() => _RequestState();
}

class _RequestState extends State<Request> {
  int _mode = 1;
  int _class = 1;
  ClassModel? _selectedClass;
  List<EventModel>? _eventDetails;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        bottomNavigationBar: SizedBox(
            height: 76.h,
            width: 430.w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                customDivider(width: 430.w, padding: 0),
                <PERSON><PERSON><PERSON><PERSON>(
                  height: 13.h,
                ),
                <PERSON><PERSON>(
                    height: 49.h,
                    width: 289.w,
                    buttonText: "Pay",
                    onPressed: () {
                      if (_selectedClass != null) {
                        NavigatorService.pushNamed(
                          AppRoutes.request,
                          arguments: {
                            'classModel': _selectedClass,
                            'eventDetails': _eventDetails,
                          },
                        );
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                              content: Text('Please select a class first')),
                        );
                      }
                    },
                    color: AppPallete.secondaryColor),
              ],
            )),
        body: SingleChildScrollView(
          child: Column(
            children: [
              buildTopBar(context),
              SizedBox(height: 24.h),
              _buildProgressSteps(context),
              SizedBox(height: 24.h),
              _buildModeSelection(context),
              SizedBox(height: 7.h),
              customDivider(padding: 42.5, width: 343.w),
              SizedBox(height: 7.h),
              _buildClassDetails(
                  context: context,
                  date: "01/01/24",
                  time: "14:30",
                  course: "Watercolour (intermediate)",
                  minute: "45mins",
                  coach: "Charlie Own",
                  group: 1,
                  ageGroup: "Age 3-6",
                  price: 12),
              SizedBox(
                height: 18.h,
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProgressSteps(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 36.w),
          child: Row(
            children: [
              _buildStepCircle(context, "1", AppPallete.secondaryColor),
              _buildStepLine(AppPallete.secondaryColor),
              _buildStepCircle(context, "2", AppPallete.secondaryColor),
              _buildStepLine(AppPallete.dividerTime),
              _buildStepCircle(context, "3", AppPallete.dividerTime),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 20.w, top: 5.h),
          child: Row(
            children: [
              _buildStepLabel(context, "choose class", 82.w),
              const Spacer(),
              _buildStepLabel(context, "send request", 82.w),
              const Spacer(),
              _buildStepLabel(context, "confirmation", 79.w),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStepCircle(BuildContext context, String step, Color color) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(20.r),
      child: Container(
        height: 34.h,
        width: 34.w,
        color: color,
        child: Center(
          child: customtext(
            context: context,
            newYear: step,
            font: 20.sp,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildStepLine(Color color) {
    return Container(
      width: 128.w,
      height: 1.h,
      color: color,
    );
  }

  Widget _buildStepLabel(BuildContext context, String label, double width) {
    return SizedBox(
      height: 15.h,
      width: width,
      child: customtext(
        context: context,
        newYear: label,
        font: 15.sp,
      ),
    );
  }

  Widget _buildModeSelection(BuildContext context) {
    return SizedBox(
      height: 195.h,
      width: 387.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModeHeader(context),
          _buildModeOptions(context),
        ],
      ),
    );
  }

  Widget _buildModeHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            customtext(
              context: context,
              newYear: "Mode",
              font: 20.sp,
              weight: FontWeight.w500,
            ),
            customtext(
              context: context,
              newYear: "Please select your mode of coaching",
              font: 13.sp,
              weight: FontWeight.w500,
            ),
          ],
        ),
        Container(
          height: 72.h,
          width: 170.w,
          padding: EdgeInsets.only(left: 5.w, top: 10.h),
          color: AppPallete.paleGrey,
          child: Column(
            children: [
              customtext(
                context: context,
                newYear: AppText.requestModelocation,
                font: 13.sp,
                weight: FontWeight.w500,
              ),
              SizedBox(height: 5.h),
              customtext(
                context: context,
                newYear: AppText.requestModeinfo,
                font: 13.sp,
                weight: FontWeight.w500,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildModeOptions(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 42.5.w),
      child: SizedBox(
        width: 343.w,
        height: 123.h,
        child: Column(
          children: [
            _buildRadioOption(
              context: context,
              value: 0,
              selectedMode: _mode,
              title: "On-Site Coaching",
              subtitle: "Learn at the place arranged by the coach",
              onChanged: (int? newValue) {
                setState(() {
                  _mode = newValue!;
                });
              },
            ),
            SizedBox(height: 7.h),
            customDivider(padding: 0),
            SizedBox(height: 7.h),
            _buildRadioOption(
              context: context,
              value: 1,
              selectedMode: _mode,
              title: "In-Home Coaching",
              subtitle: "Private session, learn at your place",
              onChanged: (int? newValue) {
                setState(() {
                  _mode = newValue!;
                });
              },
            ),
            SizedBox(height: 7.h),
            customDivider(padding: 0),
            SizedBox(height: 7.h),
            _buildRadioOption(
              context: context,
              value: 2,
              selectedMode: _mode,
              title: "Online Coaching",
              subtitle: "Learn through video conferencing platform - Zoom",
              onChanged: (int? newValue) {
                setState(() {
                  _mode = newValue!;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRadioOption(
      {required BuildContext context,
      required int value,
      required int selectedMode,
      required String title,
      required String subtitle,
      required ValueChanged<int?> onChanged}) {
    return SizedBox(
      width: 343.w,
      height: 31.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Radio<int>(
              activeColor: AppPallete.darkGrey,
              value: value,
              groupValue: selectedMode,
              onChanged: onChanged),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              customtext(
                context: context,
                newYear: title,
                font: 15.sp,
                weight: FontWeight.w400,
              ),
              SizedBox(height: 3.h),
              customtext(
                context: context,
                newYear: subtitle,
                font: 12.sp,
                weight: FontWeight.w400,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRadioOptionForClassDetails(
      {required BuildContext context,
      required int value,
      required int selectedMode,
      required String title,
      required ValueChanged<int?> onChanged}) {
    return SizedBox(
      width: 306.w,
      height: 14.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Radio<int>(
              activeColor: AppPallete.darkGrey,
              value: value,
              groupValue: selectedMode,
              onChanged: onChanged),
          customtext(
            context: context,
            newYear: title,
            font: 15.sp,
            weight: FontWeight.w400,
          ),
        ],
      ),
    );
  }

  Widget _buildClassDetails(
      {required BuildContext context,
      required String date,
      required String time,
      required String course,
      required String minute,
      required String coach,
      required int group,
      required String ageGroup,
      required int price}) {
    return Padding(
      padding: EdgeInsets.only(left: 22.5.w, top: 17.h),
      child: SizedBox(
        width: 380.w,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            customtext(
              context: context,
              newYear: "Class Details",
              font: 20.sp,
              weight: FontWeight.w600,
            ),
            customtext(
              context: context,
              newYear: "The class is arranged as the following",
              font: 13.sp,
              weight: FontWeight.w500,
            ),
            SizedBox(
              height: 19.h,
            ),
            Container(
              width: 380.w,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.r),
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      spreadRadius: 0,
                      blurRadius: 15,
                      offset: const Offset(0, 0),
                    ),
                  ]),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    color: AppPallete.paleGrey,
                    padding:
                        EdgeInsets.only(left: 14.w, top: 13.h, right: 24.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        customtext(
                            context: context,
                            newYear: date,
                            font: 12.sp,
                            weight: FontWeight.w500),
                        customtext(
                            context: context,
                            newYear: time,
                            font: 20.sp,
                            weight: FontWeight.w500),
                      ],
                    ),
                  ),
                  Container(
                    color: AppPallete.paleGrey,
                    padding:
                        EdgeInsets.only(left: 14.w, top: 13.h, right: 24.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        customtext(
                            context: context,
                            newYear: course,
                            font: 20.sp,
                            weight: FontWeight.w500),
                        customtext(
                            context: context,
                            newYear: minute,
                            font: 14.sp,
                            weight: FontWeight.w500),
                      ],
                    ),
                  ),
                  Container(
                    color: AppPallete.paleGrey,
                    padding:
                        EdgeInsets.only(left: 14.w, top: 13.h, right: 24.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        customtext(
                            context: context,
                            newYear: coach,
                            font: 15.sp,
                            weight: FontWeight.w500),
                        Row(
                          children: [
                            customSvgPicture(
                                imagePath: ImagePath.groupSvg,
                                height: 15,
                                width: 15,
                                color: AppPallete.svgColorLocation),
                            Padding(
                                padding: EdgeInsets.only(left: 2.w),
                                child: customtext(
                                    context: context,
                                    newYear: group.toString(),
                                    font: 20.sp,
                                    weight: FontWeight.w700))
                          ],
                        ),
                        customtext(
                            context: context,
                            newYear: ageGroup,
                            font: 15.sp,
                            weight: FontWeight.w500),
                        Row(
                          children: [
                            customSvgPicture(
                                imagePath: ImagePath.zSvg,
                                height: 15,
                                width: 15,
                                color: AppPallete.svgColorLocation),
                            Padding(
                                padding: EdgeInsets.only(left: 2.w),
                                child: customtext(
                                    context: context,
                                    newYear: price.toString(),
                                    font: 20.sp,
                                    weight: FontWeight.w700))
                          ],
                        ),
                      ],
                    ),
                  ),
                  Container(
                    height: 7.h,
                    color: AppPallete.paleGrey,
                  ),
                  Padding(
                    padding:
                        EdgeInsets.only(left: 26.5.w, right: 28.5.w, top: 9.h),
                    child: Container(
                      height: 83.h,
                      width: 325.w,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20.r),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              spreadRadius: 0,
                              blurRadius: 15,
                              offset: const Offset(0, 0),
                            ),
                          ]),
                      child: Row(
                        children: [
                          Padding(
                            padding: EdgeInsets.only(left: 13.w),
                            child: Container(
                              height: 71.h,
                              width: 71.w,
                              decoration: BoxDecoration(
                                  color: AppPallete.paleGrey,
                                  borderRadius: BorderRadius.circular(99.r)),
                            ),
                          ),
                          SizedBox(
                            width: 9.5.w,
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: 10.h,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  SizedBox(
                                    width: 78.w,
                                    height: 14.h,
                                    child: customtext(
                                      context: context,
                                      newYear: "Tommy Fung",
                                      font: 14.sp,
                                      weight: FontWeight.w500,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 78.5.w,
                                  ),
                                  customtext(
                                    context: context,
                                    newYear: "change",
                                    font: 13.sp,
                                    color: AppPallete.change,
                                    weight: FontWeight.w500,
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: 10.h,
                              ),
                              SizedBox(
                                width: 105.w,
                                height: 14.h,
                                child: customtext(
                                    context: context,
                                    newYear: "+852 1234 5678",
                                    font: 14.sp,
                                    weight: FontWeight.w500),
                              ),
                              SizedBox(
                                height: 10.h,
                              ),
                              SizedBox(
                                height: 14.h,
                                width: 105.w,
                                child: customtext(
                                    context: context,
                                    newYear: "<EMAIL>",
                                    font: 14.sp,
                                    weight: FontWeight.w500),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 9.w, top: 9.h),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        customtext(
                            context: context,
                            newYear: "In-home address:",
                            font: 15.sp,
                            weight: FontWeight.w500),
                        Padding(
                          padding: EdgeInsets.only(left: 9.w),
                          child: SizedBox(
                            height: 18.h,
                            width: 233.w,
                            child: customtext(
                                context: context,
                                newYear:
                                    "Flat ABC, Block AVC, ABC Estate, Hong Kong",
                                font: 15.sp,
                                weight: FontWeight.w500),
                          ),
                        )
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 24.w, top: 18.h),
                    child: _buildRadioOptionForClassDetails(
                      context: context,
                      value: 0,
                      selectedMode: _class,
                      title: "Change coaching address",
                      onChanged: (int? newValue) {
                        setState(() {
                          _class = newValue!;
                        });
                      },
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsets.only(left: 37.w, top: 18.h, right: 37.w),
                    child: SizedBox(
                      width: 319.w,
                      child: Column(
                        children: [
                          _buildRadioOptionForClassDetails(
                            context: context,
                            value: 1,
                            selectedMode: _class,
                            title:
                                "Flat A, 1/F, Block A, ABC Estate, Tseung Kwan ...",
                            onChanged: (int? newValue) {
                              setState(() {
                                _class = newValue!;
                              });
                            },
                          ),
                          SizedBox(height: 10.h),
                          customDivider(padding: 0),
                          SizedBox(height: 10.h),
                          _buildRadioOptionForClassDetails(
                            context: context,
                            value: 2,
                            selectedMode: _class,
                            title:
                                "Flat A, 1/F, Block A, ABC Estate, Tseung Kwan ...",
                            onChanged: (int? newValue) {
                              setState(() {
                                _class = newValue!;
                              });
                            },
                          ),
                          SizedBox(height: 10.h),
                          customDivider(padding: 0),
                          SizedBox(height: 10.h),
                          _buildRadioOptionForClassDetails(
                            context: context,
                            value: 3,
                            selectedMode: _class,
                            title:
                                "Flat A, 1/F, Block A, ABC Estate, Tseung Kwan ...",
                            onChanged: (int? newValue) {
                              setState(() {
                                _class = newValue!;
                              });
                            },
                          ),
                          SizedBox(height: 10.h),
                          customDivider(padding: 0),
                          SizedBox(height: 10.h),
                          _buildRadioOptionForClassDetails(
                            context: context,
                            value: 4,
                            selectedMode: _class,
                            title:
                                "Flat A, 1/F, Block A, ABC Estate, Tseung Kwan ...",
                            onChanged: (int? newValue) {
                              setState(() {
                                _class = newValue!;
                              });
                            },
                          ),
                          SizedBox(height: 10.h),
                          customDivider(padding: 0),
                          SizedBox(height: 10.h),
                          _buildRadioOptionForClassDetails(
                            context: context,
                            value: 5,
                            selectedMode: _class,
                            title:
                                "Flat A, 1/F, Block A, ABC Estate, Tseung Kwan ...",
                            onChanged: (int? newValue) {
                              setState(() {
                                _class = newValue!;
                              });
                            },
                          ),
                          SizedBox(height: 10.h),
                          customDivider(padding: 0),
                        ],
                      ),
                    ),
                  ),
                  _addressForm(context)
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Widget _addressForm(BuildContext context) {
  final formKey = GlobalKey<FormState>();
  return Padding(
    padding: const EdgeInsets.all(16.0),
    child: Form(
      key: formKey,
      child: Column(
        children: [
          SizedBox(
            width: 332.w,
            height: 32.h,
            child: TextFormField(
              decoration: const InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(
                      Radius.circular(5.0)), // Set border radius
                  borderSide: BorderSide.none, // Hide the border
                ),
                labelText: 'Address line 1',
                filled: true,
                fillColor: AppPallete.paleGrey,
              ),
            ),
          ),
          SizedBox(height: 19.h),
          SizedBox(
            width: 332.w,
            height: 32.h,
            child: TextFormField(
              decoration: const InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(
                      Radius.circular(5.0)), // Set border radius
                  borderSide: BorderSide.none, // Hide the border
                ),
                labelText: 'Address line 2',
                filled: true,
                fillColor: AppPallete.paleGrey,
              ),
            ),
          ),
          SizedBox(height: 19.h),
          Row(
            children: [
              SizedBox(
                width: 157.w,
                height: 30.h,
                child: TextFormField(
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.all(
                          Radius.circular(5.0)), // Set border radius
                      borderSide: BorderSide.none, // Hide the border
                    ),
                    labelText: 'City',
                    filled: true,
                    fillColor: AppPallete.paleGrey,
                  ),
                ),
              ),
              SizedBox(width: 18.w),
              SizedBox(
                width: 157.w,
                height: 30.h,
                child: TextFormField(
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.all(
                          Radius.circular(5.0)), // Set border radius
                      borderSide: BorderSide.none, // Hide the border
                    ),
                    labelText: 'Region',
                    filled: true,
                    fillColor: AppPallete.paleGrey,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    ),
  );
}
