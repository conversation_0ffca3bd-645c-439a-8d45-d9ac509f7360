import 'package:class_z/core/common/presentation/blocs/savedCenterBloc/saved_center_bloc.dart';
import 'package:class_z/core/common/presentation/widgets/campaign_highlight_widget.dart';
import 'package:class_z/core/imports.dart';
import 'package:flutter/foundation.dart';
import 'package:shimmer/shimmer.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  final ScrollController _scrollController = ScrollController();
  int _currentPage = 1;
  final int _limit = 10;
  bool _isLoadingMore = false;
  bool _hasMoreData = true; // Track if more data available
  bool _isInitialLoading = true;
  List<CenterData> _centers = [];
  Map<String, bool> savedCentersMap = {};
  bool _hasCentersBeenChecked = false;
  final notificationservices = locator<Notificationservice>();
  late final SavedCenterBloc _savedCenterBloc;
  late final CampaignBloc _campaignBloc;
  bool _isPermissionGranted = true;
  @override
  void initState() {
    super.initState();
    _savedCenterBloc = context.read<SavedCenterBloc>();
    _campaignBloc = context.read<CampaignBloc>();
    // Fetch first page
    _fetchCenters(page: _currentPage);
    _initialize();
    _checkSavedCenters();
    // Fetch campaigns
    print("🎯 Dispatching GetActiveCampaignsEvent...");
    _campaignBloc.add(GetActiveCampaignsEvent());
    // Setup scroll listener for pagination
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 100 &&
          !_isLoadingMore &&
          _hasMoreData) {
        _loadMoreCenters();
      }
    });
  }

  void _checkNotificationPermission() async {
    NotificationSettings settings =
        await FirebaseMessaging.instance.getNotificationSettings();

    // Check if the widget is still mounted before calling setState
    if (!mounted) return;

    setState(() {
      _isPermissionGranted =
          settings.authorizationStatus == AuthorizationStatus.authorized;
      if (!_isPermissionGranted) {
        notificationservices.requestNotificationPermission(context);
      }
    });
  }

  UserModel? userData = locator<SharedRepository>().getUserData();
  Future<void> _initialize() async {
    // Add delay to allow Firebase to fully initialize
    await Future.delayed(Duration(seconds: 3));

    String? deviceToken = await locator<FirebaseService>().getDeviceToken();
    print(
        'Device token retrieved in user_main_page: ${deviceToken?.isNotEmpty == true ? "${deviceToken!.substring(0, 10)}..." : "empty"}');

    // Check if the widget is still mounted before using context
    if (!mounted) return;

    // Only register if we have a valid token and user ID
    if (deviceToken != null &&
        deviceToken.isNotEmpty &&
        userData?.data?.parent?.id != null) {
      context.read<NotificationBloc>().add(CheckDeviceTokenEvent(
          deviceToken: deviceToken, id: userData!.data!.parent!.id!));
    } else {
      print(
          'Skipping device token registration - token: ${deviceToken?.isEmpty != false ? "empty" : "valid"}, userId: ${userData?.data?.parent?.id ?? "null"}');
    }

    // _checkNotificationPermission itself now has a mounted check for its setState
    _checkNotificationPermission();

    // It's good practice to check mounted before context-dependent calls after awaits
    if (!mounted) return;
    notificationservices.FirebaseInit(context);

    if (!mounted) return;
    notificationservices.handleTerminateMessage(context);

    notificationservices.getDeviceToken().then((value) {
      // Logging removed as requested
    });
  }

  void _fetchCenters({required int page}) {
    setState(() {
      _isLoadingMore = true;
    });

    context
        .read<CenterBloc>()
        .add(GetAllCenterEvent(page: page, limit: _limit));
  }

  void _loadMoreCenters() {
    if (_isLoadingMore) return;

    _currentPage++;
    _fetchCenters(page: _currentPage);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // This method merges new fetched centers with old ones
  void _updateCenters(List<CenterData> newCenters) {
    setState(() {
      _isLoadingMore = false;

      if (newCenters.length < _limit) {
        _hasMoreData = false; // No more data from backend
      }

      _centers.addAll(newCenters);
      _checkSavedCenters();
    });
  }

  void _checkSavedCenters() {
    // Logging removed as requested
    for (var center in _centers) {
      if (center.id != null) {
        // Logging removed as requested
        _savedCenterBloc.add(CheckCenterSavedEvent(center.id!));
      }
    }

    // Mark centers as checked to prevent infinite loop
    _hasCentersBeenChecked = true;
  }

  void _onFavoriteToggle(String centerId, bool newValue) {
    // Logging removed as requested
    if (newValue) {
      _savedCenterBloc.add(SaveCenterEvent(centerId));
    } else {
      _savedCenterBloc.add(UnsaveCenterEvent(centerId));
    }
  }

  String getExpTier(int exp) {
    if (exp <= 799) {
      return "Beginner";
    } else if (exp <= 3299) {
      return "Learner";
    } else if (exp <= 7199) {
      return "Proficient";
    } else if (exp <= 46899) {
      return "Master";
    } else {
      return "Expert";
    }
  }

  double getExpRatio(int exp, [int maxExp = 85329]) {
    return (exp / maxExp).clamp(0, 1).toDouble();
  }

  @override
  Widget build(BuildContext context) {
    SizeConfig.init(context);

    int exp = userData?.data?.parent?.exp ?? 0;
    String tier = getExpTier(exp);
    double expRatio = getExpRatio(exp);

    return Scaffold(
      body: MultiBlocListener(
        listeners: [
          BlocListener<SavedCenterBloc, SavedCenterState>(
            listener: (context, state) {
              // Logging removed as requested
              if (state is SavedCenterLoading) {
                errorState(context: context, error: 'Saving....');
              }
              if (state is UnSavedCenterLoading) {
                errorState(context: context, error: 'Removing....');
              }
              if (state is CenterSavedStatus) {
                setState(() {
                  savedCentersMap[state.centerId] = state.isSaved;
                });
              } else if (state is CenterSavedSuccess) {
                setState(() {
                  savedCentersMap[state.centerId] = true;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Center saved successfully')),
                );
              } else if (state is CenterUnsavedSuccess) {
                setState(() {
                  savedCentersMap[state.centerId] = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Center removed from saved')),
                );
              } else if (state is SavedCenterError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(state.message)),
                );
              }
            },
          ),
          BlocListener<CenterBloc, CenterState>(
            listener: (context, state) {
              if (state is CenterLoadingState && _currentPage == 1) {
                setState(() {
                  _isInitialLoading = true;
                });
              } else {
                setState(() {
                  _isInitialLoading = false;
                });
              }

              if (state is CenterListFetchSuccess) {
                _updateCenters(state.centers);
              }
              if (state is CenterErrorState) {
                setState(() {
                  _isLoadingMore = false;
                  _isInitialLoading = false;
                });
                errorState(context: context, error: state.message);
              }
            },
          ),
        ],
        child: RefreshIndicator(
          onRefresh: () async {
            _centers.clear();
            _currentPage = 1;
            _hasMoreData = true;
            _fetchCenters(page: _currentPage);
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            scrollDirection: Axis.vertical,
            child: Padding(
              padding: EdgeInsets.only(left: 9.w),
              child: _isInitialLoading
                  ? _buildLoadingSkeleton()
                  : _centers.isEmpty
                      ? _buildEmptyState()
                      : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(top: 82.h),
                                  child: customtext(
                                      context: context,
                                      newYear:
                                          "Hello, ${userData?.data?.parent?.nickname ?? 'user'}!",
                                      font: 25.sp,
                                      weight: FontWeight.w600),
                                ),
                                customTopBarOnlyIcon(
                                  context: context,
                                  badgeCount1: 0,
                                  badgeCount2: 0,
                                  onTap1: () {
                                    if (userData?.data?.parent?.id != null) {
                                      NavigatorService.pushNamed(
                                          AppRoutes.notification,
                                          arguments:
                                              userData?.data?.parent?.id);
                                    } else
                                      errorState(
                                          context: context,
                                          error: 'please login');
                                  },
                                  onTap2: () {
                                    if (userData?.data?.parent?.id != null) {
                                      NavigatorService.pushNamed(
                                          AppRoutes.centerMessage,
                                          arguments: 'user');
                                    } else
                                      errorState(
                                          context: context,
                                          error: 'please login');
                                  },
                                ),
                              ],
                            ),
                            SizedBox(height: 29.h),
                            Padding(
                              padding: EdgeInsets.only(right: 24.w),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  customtext(
                                      context: context,
                                      newYear: "Centre Spotlight",
                                      font: 20.sp,
                                      weight: FontWeight.w600),
                                  InkWell(
                                    onTap: () {
                                      NavigatorService.pushNamed(
                                          AppRoutes.centreSpotlight,
                                          arguments: _centers);
                                    },
                                    child: customtext(
                                        context: context,
                                        newYear: "see all",
                                        font: 15.sp,
                                        weight: FontWeight.w400),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 14.h),
                            SizedBox(
                              height: 163.h,
                              child: ListView.separated(
                                controller: _scrollController,
                                padding: EdgeInsets.only(right: 9.w),
                                scrollDirection: Axis.horizontal,
                                itemCount:
                                    _centers.length + (_isLoadingMore ? 1 : 0),
                                separatorBuilder: (context, index) =>
                                    SizedBox(width: 10.w),
                                itemBuilder: (context, index) {
                                  if (index < _centers.length) {
                                    var center = _centers[index];
                                    final isSaved =
                                        savedCentersMap[center.id] ?? false;
                                    // Logging removed as requested
                                    return buildCenterRowGallery(
                                      context: context,
                                      imageHeight: 163.h,
                                      imageWidth: 331.w,
                                      center: center,
                                      isSaved: isSaved,
                                      onFavoriteToggle: (newValue) {
                                        if (center.id != null) {
                                          _onFavoriteToggle(
                                              center.id!, newValue);
                                        }
                                      },
                                      onTap: () {
                                        NavigatorService.pushNamed(
                                            AppRoutes.centreView,
                                            arguments: {
                                              'center': center,
                                              "isSaved": isSaved,
                                              'bottomView': true
                                            });
                                      },
                                    );
                                  } else {
                                    // Loading indicator item
                                    return const Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: Center(
                                          child: CircularProgressIndicator()),
                                    );
                                  }
                                },
                              ),
                            ),
                            SizedBox(
                              height: 16.h,
                            ),
                            customtext(
                                context: context,
                                newYear: "Highlights",
                                font: 20.sp,
                                weight: FontWeight.w600),
                            SizedBox(
                              height: 16.h,
                            ),
                            BlocBuilder<CampaignBloc, CampaignState>(
                              bloc: _campaignBloc,
                              builder: (context, state) {
                                if (state is CampaignLoadedState) {
                                  return campaignHighlightsSection(
                                    context: context,
                                    campaigns: state.campaigns
                                        .map((e) => CampaignModel.fromEntity(e))
                                        .toList(),
                                    onCampaignTap:
                                        null, // Disable clicking - campaigns are view-only
                                  );
                                } else if (state is CampaignError) {
                                  // Fallback to default widget on error
                                  return newYear(
                                    context: context,
                                    newYear: "NEW YEAR",
                                    newClass: "NEW CLASSES",
                                    offer: "From 50% off",
                                    date: "21 - 27 Feb",
                                  );
                                } else {
                                  // Loading state
                                  return Container(
                                    height: 120.h,
                                    width: double.infinity,
                                    decoration: BoxDecoration(
                                      color: Colors.grey[200],
                                      borderRadius: BorderRadius.circular(20.r),
                                    ),
                                    child: Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  );
                                }
                              },
                            ),
                            SizedBox(
                              height: 16.h,
                            ),
                            customtext(
                                context: context,
                                newYear: "Trending Interest",
                                font: 20.sp,
                                weight: FontWeight.w600),
                            SizedBox(
                              height: 16.h,
                            ),
                            Padding(
                              padding: EdgeInsets.only(left: 4.w, right: 13.w),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  _trending(
                                    context: context,
                                    imagePath: ImagePath.musicSvg,
                                    name: "Music",
                                    onTap: () {
                                      NavigatorService.pushNamed(
                                          AppRoutes.searchGenre,
                                          arguments: {'title': 'Music'});
                                    },
                                  ),
                                  _trending(
                                    context: context,
                                    imagePath: ImagePath.artSvg,
                                    name: "Art",
                                    onTap: () {
                                      NavigatorService.pushNamed(
                                          AppRoutes.searchGenre,
                                          arguments: {'title': 'Art'});
                                    },
                                  ),
                                  _trending(
                                    context: context,
                                    imagePath: ImagePath.sportsSvg,
                                    name: "Sports",
                                    onTap: () {
                                      NavigatorService.pushNamed(
                                          AppRoutes.searchGenre,
                                          arguments: {'title': 'Sports'});
                                    },
                                  )
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 12.h,
                            ),
                            Padding(
                              padding: EdgeInsets.only(left: 4.w, right: 13.w),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  _trending(
                                    context: context,
                                    imagePath: ImagePath.steamSvg,
                                    name: "Steam",
                                    onTap: () {
                                      NavigatorService.pushNamed(
                                          AppRoutes.searchGenre,
                                          arguments: {'title': 'Steam'});
                                    },
                                  ),
                                  _trending(
                                    context: context,
                                    imagePath: ImagePath.techSvg,
                                    name: "Technology",
                                    onTap: () {
                                      NavigatorService.pushNamed(
                                          AppRoutes.searchGenre,
                                          arguments: {'title': 'Technology'});
                                    },
                                  ),
                                  _trending(
                                    context: context,
                                    imagePath: ImagePath.languageSvg,
                                    name: "Language",
                                    onTap: () {
                                      NavigatorService.pushNamed(
                                          AppRoutes.searchGenre,
                                          arguments: {'title': 'Language'});
                                    },
                                  )
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 16.h,
                            ),
                            customtext(
                                context: context,
                                newYear: "Experience",
                                font: 20.sp,
                                weight: FontWeight.w600),
                            SizedBox(
                              height: 16.h,
                            ),
                            Padding(
                              padding: EdgeInsets.only(left: 4.w, right: 13.w),
                              child: GestureDetector(
                                onTap: () {
                                  NavigatorService.pushNamed(
                                      AppRoutes.experience,
                                      arguments: {
                                        'tier': tier,
                                        'exp': exp,
                                        'ratio': expRatio
                                      });
                                },
                                child: _experience(
                                    context: context,
                                    exp: userData?.data?.parent?.exp ?? 0,
                                    tier: tier,
                                    progress: expRatio),
                              ),
                            ),
                            SizedBox(
                              height: 2.h,
                            )
                          ],
                        ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 80.h),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              FontAwesomeIcons.schoolCircleXmark,
              size: 80.sp,
              color: Colors.grey[400],
            ),
            SizedBox(height: 24.h),
            Text(
              "No centers available",
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 22.sp,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              "There are currently no centers to display. Please check back later or try refreshing.",
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[500],
              ),
            ),
            SizedBox(height: 32.h),
            ElevatedButton.icon(
              onPressed: () {
                _centers.clear();
                _currentPage = 1;
                _hasMoreData = true;
                _fetchCenters(page: _currentPage);
              },
              icon: const Icon(Icons.refresh),
              label: const Text("Refresh"),
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: AppPallete.buttonColor,
                padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 12.h),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingSkeleton() {
    return Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: SingleChildScrollView(
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.only(right: 9.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Placeholder
              SizedBox(height: 82.h),
              Padding(
                padding: EdgeInsets.only(left: 4.w),
                child: Container(
                  height: 30.h,
                  width: 200.w,
                  color: Colors.white,
                ),
              ),
              SizedBox(height: 29.h),
              Padding(
                padding: EdgeInsets.only(right: 24.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      height: 24.h,
                      width: 180.w,
                      color: Colors.white,
                    ),
                    Container(
                      height: 20.h,
                      width: 60.w,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
              SizedBox(height: 14.h),
              // Center Spotlight list placeholder
              SizedBox(
                height: 163.h,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: 3,
                  separatorBuilder: (context, index) => SizedBox(width: 10.w),
                  itemBuilder: (context, index) => Container(
                    height: 163.h,
                    width: 331.w,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(15.r),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 16.h),
              // Highlights placeholder
              Container(
                margin: EdgeInsets.only(left: 4.w),
                height: 24.h,
                width: 120.w,
                color: Colors.white,
              ),
              SizedBox(height: 16.h),
              Container(
                margin: EdgeInsets.only(left: 4.w, right: 13.w),
                height: 100.h,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.r),
                ),
              ),
              SizedBox(height: 16.h),
              // Trending Interest placeholder
              Container(
                margin: EdgeInsets.only(left: 4.w),
                height: 24.h,
                width: 200.w,
                color: Colors.white,
              ),
              SizedBox(height: 16.h),
              Padding(
                padding: EdgeInsets.only(left: 4.w, right: 13.w),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: List.generate(
                      3,
                      (index) => Expanded(
                        child: Container(
                          height: 80.h,
                          margin: EdgeInsets.symmetric(horizontal: 5.w),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10.r),
                          ),
                        ),
                      ),
                    )),
              ),
              SizedBox(height: 12.h),
              Padding(
                padding: EdgeInsets.only(left: 4.w, right: 13.w),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: List.generate(
                      3,
                      (index) => Expanded(
                        child: Container(
                          height: 80.h,
                          margin: EdgeInsets.symmetric(horizontal: 5.w),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10.r),
                          ),
                        ),
                      ),
                    )),
              ),
            ],
          ),
        ));
  }

  Widget _trending(
      {required BuildContext context,
      required String imagePath,
      required String name,
      required VoidCallback onTap}) {
    double height = getHeight(context: context) * 0.125;
    double width = getawidth(context: context) * 0.3;
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: height * 0.14,
            ),
            customSvgPicture(
                imagePath: imagePath,
                height: height * 0.5,
                width: width * 0.375,
                color: AppPallete.secondaryColor),
            SizedBox(
              height: 9.h,
            ),
            customtext(
                context: context,
                newYear: name,
                font: 15.sp,
                weight: FontWeight.w400,
                color: AppPallete.secondaryColor)
          ],
        ),
      ),
    );
  }

  Widget _experience(
      {required BuildContext context,
      required int exp,
      required String tier,
      required double progress}) {
    double height = getHeight(context: context);
    double width = getawidth(context: context);
    if (kDebugMode) {
      // Logging removed as requested
    }
    return Container(
      decoration: BoxDecoration(
          gradient: LinearGradient(colors: [
            AppPallete.secondaryColor.withOpacity(0.7),
            AppPallete.color128.withOpacity(0.7),
          ]),
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 40.h,
          ),
          circleWithExp(
              context: context, progress: progress, tier: tier, exp: exp),
          Padding(
            padding: EdgeInsets.only(
                top: 30.h, left: 21.w, right: 12.w, bottom: 14.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customtext(
                    context: context,
                    newYear: "Learn More to Earn More",
                    font: 20.sp,
                    weight: FontWeight.w500,
                    color: Colors.white),
                CustomIconButton(
                  color: Colors.white,
                  iconSize: 24.w,
                  icon: Icons.arrow_forward_ios_sharp,
                  onPressed: () {},
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
