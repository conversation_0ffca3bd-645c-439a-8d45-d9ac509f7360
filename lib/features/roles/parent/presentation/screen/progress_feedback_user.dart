import 'package:class_z/core/imports.dart';
import 'package:class_z/features/roles/parent/presentation/widgets/images_for_moment.dart';

class ProgressUser extends StatefulWidget {
  final ReviewOfChildModel reviewOfChildModel;
  const ProgressUser({required this.reviewOfChildModel, super.key});

  @override
  State<ProgressUser> createState() => _ProgressUserState();
}

class _ProgressUserState extends State<ProgressUser> {
  TextEditingController additionalInfoController = TextEditingController();
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    additionalInfoController.dispose();
    // TODO: implement dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print(widget.reviewOfChildModel);
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 19.w, top: 85.h),
              child: customBackButton(),
            ),
            Padding(
              padding: EdgeInsets.only(left: 19.w, top: 13.h),
              child: customtext(
                  context: context,
                  newYear: "FeedBack",
                  font: 30.sp,
                  weight: FontWeight.w500),
            ),
            Padding(
              padding: EdgeInsets.only(left: 21.w, right: 21.w, top: 31.h),
              child: Container(
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.r),
                    boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // lastClassCard(
                    //   context: context,
                    //   date: dateGenerator(date: widget.pending.event?.date),
                    //   course:
                    //       widget.pending.plainClassDetails?.classProviding ??
                    //           "",
                    //   coach:
                    //       widget.pending.plainClassDetails?.coach?.displayName,
                    //   center:
                    //       widget.pending.plainClassDetails?.center?.displayName,
                    //   roundBottomCorners: false,
                    //   image: widget.pending.plainClassDetails?.mainImage?.url ??
                    //       "",
                    // ),
                    _progress(
                        context: context,
                        rating: widget.reviewOfChildModel.rating.toString()),
                    SizedBox(
                      height: 20.h,
                    ),
                    RadarChartWithLabels(
                      data: [
                        [
                          (widget.reviewOfChildModel.questions?.q1 ?? 0) * 10,
                          (widget.reviewOfChildModel.questions?.q2 ?? 0) * 10,
                          (widget.reviewOfChildModel.questions?.q3 ?? 0) * 10,
                          (widget.reviewOfChildModel.questions?.q4 ?? 0) * 10,
                          (widget.reviewOfChildModel.questions?.q5 ?? 0) * 10,
                          (widget.reviewOfChildModel.questions?.q6 ?? 0) * 10,
                        ]
                      ],
                    ),
                    SizedBox(
                      height: 26.h,
                    ),
                    widget.reviewOfChildModel.questions != null
                        ? _progressBar()
                        : SizedBox.shrink(),
                    SizedBox(
                      height: 20.h,
                    ),
                    imagesForMoments(
                        images: widget.reviewOfChildModel.images ?? [],
                        context: context),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 20.h,
            )
          ],
        ),
      ),
    );
  }

  Widget _progressBar() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 26.h,
        ),
        CustomProgressSlider(
          title: "Learning Progress",
          leftLabel: "Emerging",
          centerLabel: "Steady",
          rightLabel: "Accelerating",
          initialValue: widget.reviewOfChildModel.questions?.q1,
        ),
        SizedBox(
          height: 26.h,
        ),
        CustomProgressSlider(
          title: "Active Participation",
          leftLabel: "Minimal",
          centerLabel: "Engaged",
          rightLabel: "Exemplary",
          initialValue: widget.reviewOfChildModel.questions?.q2,
        ),
        SizedBox(
          height: 26.h,
        ),
        CustomProgressSlider(
          title: "Social Inventory",
          leftLabel: "Limited",
          centerLabel: "Developing",
          rightLabel: "Extensive",
          initialValue: widget.reviewOfChildModel.questions?.q3,
        ),
        SizedBox(
          height: 26.h,
        ),
        CustomProgressSlider(
          title: "Readiness",
          leftLabel: "Unprepared",
          centerLabel: "Preparing",
          rightLabel: "Ready",
          initialValue: widget.reviewOfChildModel.questions?.q4,
        ),
        SizedBox(
          height: 26.h,
        ),
        CustomProgressSlider(
          title: "Enjoyment",
          leftLabel: "Disinterested",
          centerLabel: "Engaged",
          rightLabel: "Immersive",
          initialValue: widget.reviewOfChildModel.questions?.q5,
        ),
        SizedBox(
          height: 26.h,
        ),
        CustomProgressSlider(
          title: "Distinctive Conduct",
          leftLabel: "Typical",
          centerLabel: "Noteworthy",
          rightLabel: "Inspirational",
          initialValue: widget.reviewOfChildModel.questions?.q6,
        ),
        SizedBox(
          height: 26.h,
        ),
        _additionalInfo(
            context: context,
            title: "What topics were covered during today's class?",
            controller: additionalInfoController,
            text: widget.reviewOfChildModel.topic),
        SizedBox(
          height: 26.h,
        ),
        _additionalInfo(
            context: context,
            title: "Additional Comment",
            text: widget.reviewOfChildModel.comment,
            controller: additionalInfoController),
      ],
    );
  }

  Widget _progress({required BuildContext context, required String rating}) {
    return Padding(
      padding: EdgeInsets.only(left: 14.w, top: 13.h, right: 0.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            flex: 2, // Adjust the flex value as needed
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                customtext(
                  context: context,
                  newYear: "Progress check",
                  font: 20.sp,
                  weight: FontWeight.w600,
                ),
                SizedBox(height: 6.h),
                customtext(
                  context: context,
                  newYear: "Here’s the class review for Charlie!",
                  font: 15.sp,
                  weight: FontWeight.w500,
                ),
              ],
            ),
          ),
          SizedBox(width: 10.w),
          Flexible(
            child: Container(
              height: 56.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10.r),
                boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
              ),
              child: Row(
                children: [
                  Row(
                    children: [
                      customSvgPicture(
                        imagePath: ImagePath.starSvg,
                        height: 25.97.h,
                        width: 27.w,
                        color: AppPallete.rating,
                      ),
                      SizedBox(width: 3.w),
                      customtext(
                        context: context,
                        newYear: rating,
                        font: 30.sp,
                        weight: FontWeight.w700,
                      ),
                    ],
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 16.h, left: 3.w),
                    child: customtext(
                      context: context,
                      newYear: "/10",
                      font: 10.sp,
                      weight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            width: 5.w,
          )
        ],
      ),
    );
  }

  Widget _additionalInfo(
      {required BuildContext context,
      required String title,
      String? text,
      required TextEditingController controller}) {
    return SizedBox(
      height: 135.h,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 12.5.w),
            child: customtext(
                context: context,
                newYear: title,
                font: 14.sp,
                weight: FontWeight.w600),
          ),
          SizedBox(
            height: 11.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.5.w, right: 16.5),
            child: Container(
              width: MediaQuery.of(context).size.width,
              height: 100.h,
              color: AppPallete.inputBox,
              padding: EdgeInsets.only(left: 5.w, top: 5),
              child: customtext(
                  context: context,
                  newYear: text ?? 'Nothing',
                  font: 15.sp,
                  weight: FontWeight.w400),
            ),
          ),
        ],
      ),
    );
  }
}

// class ProgressBarPainter extends CustomPainter {
//   final double progress;

//   ProgressBarPainter(this.progress);

//   @override
//   void paint(Canvas canvas, Size size) {
//     final Paint trackPaint = Paint()
//       ..color = AppPallete.dividerTime
//       ..strokeWidth = 2.0;

//     final Paint progressPaint = Paint()
//       ..color = AppPallete.secondaryColor
//       ..strokeWidth = 4.0;

//     final Paint circlePaint = Paint()
//       ..color = AppPallete.secondaryColor
//       ..style = PaintingStyle.fill;

//     final Paint emptyCirclePaint = Paint()
//       ..color = Colors.white
//       ..style = PaintingStyle.fill;

//     final double circleRadius = 11.r;

//     // Draw track line
//     canvas.drawLine(Offset(0, size.height / 2),
//         Offset(size.width, size.height / 2), trackPaint);

//     // Draw progress line
//     canvas.drawLine(Offset(0, size.height / 2),
//         Offset(size.width * progress, size.height / 2), progressPaint);

//     // Draw progress circle
//     canvas.drawCircle(Offset(size.width * progress, size.height / 2),
//         circleRadius, circlePaint);

//     // Draw empty circle inside the progress circle
//     canvas.drawCircle(Offset(size.width * progress, size.height / 2),
//         circleRadius - 2, emptyCirclePaint);

//     // Draw hash marks on the track line
//     for (int i = 0; i <= 10; i++) {
//       double x = size.width * i / 10;
//       canvas.drawLine(Offset(x, size.height / 2 - 4),
//           Offset(x, size.height / 2 + 4), trackPaint);
//     }
//   }

//   @override
//   bool shouldRepaint(CustomPainter oldDelegate) {
//     return false;
//   }
// }

class CustomProgressSlider extends StatefulWidget {
  final String title;
  final String leftLabel;
  final String centerLabel;
  final String rightLabel;
  final double? initialValue; // New parameter

  const CustomProgressSlider({
    Key? key,
    required this.title,
    required this.leftLabel,
    required this.centerLabel,
    required this.rightLabel,
    this.initialValue, // Optional
  }) : super(key: key);

  @override
  _CustomProgressSliderState createState() => _CustomProgressSliderState();
}

class _CustomProgressSliderState extends State<CustomProgressSlider> {
  late double _progressValue;

  @override
  void initState() {
    super.initState();
    _progressValue = widget.initialValue ?? 0.0; // Use initialValue if provided
    if (_progressValue > 1) {
      _progressValue = 1.0; // Cap the value to 1 if it's greater
    } else if (_progressValue < 0) {
      _progressValue = 0.0; // Cap the value to 0 if it's less
    } else {
      _progressValue = widget.initialValue ?? 0.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isMaxValue = _progressValue == 1.0; // Check if the value is 1
    print(_progressValue);
    return Padding(
      padding: EdgeInsets.only(left: 8.w, right: 14.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 9.w),
            child: customtext(
              context: context,
              newYear: widget.title,
              font: 14.sp,
              weight: FontWeight.w500,
            ),
          ),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              trackHeight: 2.0,
              activeTrackColor:
                  isMaxValue ? Colors.green : Colors.blue, // Green if max value
              inactiveTrackColor: Colors.grey.shade300,
              thumbColor: isMaxValue
                  ? Colors.green
                  : Colors.white, // Change thumb color if max
              thumbShape: const RoundSliderThumbShape(
                enabledThumbRadius: 10.0,
                elevation: 4.0,
              ),
              overlayColor: isMaxValue
                  ? Colors.green.withOpacity(0.4)
                  : Colors.blue.withOpacity(0.2), // Overlay changes when max
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 20.0),
              tickMarkShape: const RoundSliderTickMarkShape(),
              activeTickMarkColor: Colors.grey.shade400,
              inactiveTickMarkColor: Colors.grey.shade400,
            ),
            child: Slider(
              value: _progressValue,
              min: 0.0,
              max: 1.0,
              divisions: 10,
              onChanged: widget.initialValue != null
                  ? null // Disable sliding if initialValue is provided
                  : (value) {
                      setState(() {
                        _progressValue = value;
                      });
                    },
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 12.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Center(
                  child: customtext(
                    context: context,
                    newYear: widget.leftLabel,
                    font: 14.sp,
                    weight: FontWeight.w400,
                  ),
                ),
                customtext(
                  context: context,
                  newYear: widget.centerLabel,
                  font: 14.sp,
                  weight: FontWeight.w400,
                ),
                customtext(
                  context: context,
                  newYear: widget.rightLabel,
                  font: 14.sp,
                  weight: FontWeight.w400,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}

class RadarChartWithLabels extends StatelessWidget {
  final List<List<double>> data;

  const RadarChartWithLabels({super.key, required this.data});
  @override
  Widget build(BuildContext context) {
    final features = [
      'Learning Progress',
      'Active Participation',
      'Social Inventory',
      'Readiness',
      'Enjoyment',
      'Distinctive Conduct',
    ];

    return SizedBox(
      height: 284.h,
      width: 358.w,
      child: Stack(
        alignment: Alignment.center,
        children: [
          RadarChart(
            sides: 6,
            features: features,
            data: data,
            ticks: [2, 4, 6, 8, 10],
            featuresTextStyle: TextStyle(fontSize: 14.sp),
            graphColors: [Colors.blueAccent],
            outlineColor: Colors.grey.shade300,
            axisColor: Colors.blue.withOpacity(0.4),
          ),
          ..._buildLabels(features, 284.h, 358.w),
        ],
      ),
    );
  }

  List<Widget> _buildLabels(
      List<String> features, double height, double width) {
    // Define specific positions for each of the six corners
    final positions = [
      Offset(width / 2 - 40.w, 0), // learning
      Offset(width / 1.4, height / 1.5), // active
      Offset(width - width / 4.5, height / 4), // social
      Offset(width / 2 - 30.w, height - 20.h), // Bottom Center
      Offset(width / 20.w, height / 1.5), // enjoy
      Offset(width / 40.w, height / 3.6), // dis
    ];

    return List.generate(features.length, (index) {
      return Positioned(
        left: positions[index].dx,
        top: positions[index].dy,
        child: Container(
          constraints: BoxConstraints(
              maxWidth: width / 4), // Adjust the width to fit text
          child: Text(
            features[index],
            style: TextStyle(fontSize: 12.sp, color: Colors.black),
            textAlign: TextAlign.center,
          ),
        ),
      );
    });
  }
}
