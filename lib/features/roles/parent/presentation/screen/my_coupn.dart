import 'package:class_z/core/imports.dart';

class MyCoupn extends StatefulWidget {
  const MyCoupn({super.key});

  @override
  State<MyCoupn> createState() => _MyCoupnState();
}

class _MyCoupnState extends State<MyCoupn> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<UserBloc>().add(GetDiscountEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            middleSwitchTopBar(context: context, title: "My Coupon"),
            Si<PERSON><PERSON><PERSON>(
              height: 20.h,
            ),
            customDivider(
              width: 430.w,
              padding: 0,
            ),
            Si<PERSON><PERSON><PERSON>(
              height: 22.h,
            ),
            customtext(
                context: context,
                newYear: "Available Coupon",
                padding: 22.w,
                font: 17.sp,
                weight: FontWeight.w400),
            <PERSON><PERSON><PERSON><PERSON>(
              height: 21.h,
            ),
            BlocConsumer<UserBloc, UserState>(
              listener: (context, state) {
                if (state is UserLoadingState)
                  loadingState(context: context);
                else
                  hideLoadingDialog(context);
                if (state is UserErrorState)
                  errorState(context: context, error: state.message);
              },
              builder: (context, state) {
                print(state);
                if (state is GetDiscountSuccessState) {
                  print('DONE');
                  return ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: state.discounts.length,
                    itemBuilder: (context, index) {
                      final discount = state.discounts[index];
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _coupn(context: context, discount: discount),
                          SizedBox(
                            height: 12,
                          )
                        ],
                      );
                    },
                  );
                }
                return Align(
                    alignment: Alignment.topCenter,
                    child: customtext(
                        context: context,
                        newYear: "No available Discount",
                        font: 20.sp));
              },
            ),
            SizedBox(
              height: 16.h,
            ),
          ],
        ),
      ),
    );
  }

  Widget _coupn(
      {required BuildContext context, required DiscountEntity discount}) {
    print('${discount.discountType} as ${discount.discountPercentage}');
    return Padding(
      padding: EdgeInsets.only(left: 19.w, right: 19.w),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20.r),
          onTap: () {
            // Directly apply the coupon on tap
            final result = {
              'codeName': discount.code,
              'discount': discount.discountType == 'fixed'
                  ? '${discount.discountPercentage} HKD'
                  : '${discount.discountPercentage}%',
              'discountType': discount.discountType,
              'discountValue': discount.discountPercentage,
            };
            Navigator.pop(context, result);
          },
          onLongPress: () {
            // Show detailed coupon information in a dialog with an "Apply" option
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: Text('Coupon Details'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Code: ${discount.code ?? 'Unknown'}'),
                    SizedBox(height: 8),
                    Text('Type: ${discount.discountType ?? 'Unknown'}'),
                    SizedBox(height: 8),
                    Text(
                        'Value: ${discount.discountType == 'fixed' ? "${discount.discountPercentage} HKD" : "${discount.discountPercentage}%"}'),
                    SizedBox(height: 8),
                    Text(
                        'Valid until: ${dateGenerator(date: discount.validUntil!)}'),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text('Close'),
                  ),
                  TextButton(
                    onPressed: () {
                      final result = {
                        'codeName': discount.code,
                        'discount': discount.discountType == 'fixed'
                            ? '${discount.discountPercentage} HKD'
                            : '${discount.discountPercentage}%',
                        'discountType': discount.discountType,
                        'discountValue': discount.discountPercentage,
                      };
                      // Pop the dialog and then the screen with the result
                      Navigator.pop(context); // Close the dialog
                      Navigator.pop(context, result); // Go back with data
                    },
                    child: Text('Apply'),
                  ),
                ],
              ),
            );
          },
          child: Container(
            padding: EdgeInsets.only(left: 19.w, right: 19.w),
            decoration: BoxDecoration(
                color: AppPallete.secondaryColor,
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  shadow(blurRadius: 15, opacity: 0.1, xoffset: 0, yoffset: 0)
                ]),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 9.h,
                ),
                SizedBox(
                  child: customtext(
                      context: context,
                      newYear: discount.discountType == 'fixed'
                          ? "${discount.discountPercentage} HKD"
                          : "${discount.discountPercentage}%",
                      font: 35.sp,
                      weight: FontWeight.w600,
                      shadows: [shadow()],
                      color: Colors.white),
                ),
                SizedBox(
                  height: 11.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    customtext(
                        context: context,
                        newYear: discount.code ?? 'Unknown',
                        font: 15.sp,
                        weight: FontWeight.w400,
                        shadows: [
                          shadow(
                            opacity: 0.25,
                            blurRadius: 4.0,
                            xoffset: 0.0,
                            yoffset: 4.0,
                          )
                        ],
                        color: Colors.white),
                    customtext(
                        context: context,
                        newYear:
                            "valid until ${dateGenerator(date: discount.validUntil!)}",
                        font: 15.sp,
                        weight: FontWeight.w600,
                        shadows: [
                          shadow(
                            opacity: 0.25,
                            blurRadius: 4.0,
                            xoffset: 0.0,
                            yoffset: 4.0,
                          )
                        ],
                        color: Colors.white),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
