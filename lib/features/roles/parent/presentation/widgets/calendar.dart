import 'package:class_z/core/imports.dart';
import 'package:table_calendar/table_calendar.dart';

class CustomCalendar extends StatelessWidget {
  final DateTime focusedDay;
  final DateTime? selectedDay;
  final Function(DateTime, DateTime) onDaySelected;
  final Function(DateTime)? onPageChanged;
  final List<String>? eventDates;
  final List<EventModel>? events;

  const CustomCalendar({
    Key? key,
    required this.focusedDay,
    required this.selectedDay,
    required this.onDaySelected,
    this.onPageChanged,
    this.eventDates,
    this.events,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.only(left: 34.w, right: 34.w),
        child: SizedBox(
          child: TableCalendar(
            firstDay: DateTime.utc(2020, 1, 1),
            lastDay: DateTime.utc(2030, 12, 31),
            focusedDay: focusedDay,
            selectedDayPredicate: (day) => isSameDay(selectedDay, day),
            onDaySelected: onDaySelected,
            onPageChanged: onPageChanged,

            // Modified eventLoader logic
            eventLoader: (day) {
              if (eventDates != null) {
                // Convert eventDates (List<String>) to events (List<DateTime>)
                return eventDates!.where((dateString) {
                  DateTime? eventDate = DateTime.tryParse(dateString);
                  return eventDate != null && isSameDay(eventDate, day);
                }).toList();
              } else if (events != null) {
                // Use events if eventDates is null
                return events!.where((event) {
                  return isSameDay(event.date, day);
                }).toList();
              } else {
                return [];
              }
            },

            daysOfWeekHeight: 60.h,
            rowHeight: 30.w,
            calendarStyle: CalendarStyle(
              selectedDecoration: BoxDecoration(
                color: Colors.grey[300],
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(5.r),
              ),
              todayDecoration: BoxDecoration(
                color: Colors.grey[300],
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(5.r),
              ),
              weekendTextStyle: const TextStyle(color: AppPallete.red),
              holidayTextStyle: const TextStyle(color: Colors.red),
              outsideDaysVisible: true,
            ),
            headerStyle: HeaderStyle(
              formatButtonVisible: false,
              titleCentered: true,
              leftChevronVisible: false,
              rightChevronVisible: false,
              headerPadding: EdgeInsets.zero,
              titleTextStyle: const TextStyle(fontSize: 0),
            ),
            calendarBuilders: CalendarBuilders(
              headerTitleBuilder: (context, date) {
                return const SizedBox.shrink();
              },
              selectedBuilder: (context, date, _) {
                return _buildDateContainer(date);
              },
              todayBuilder: (context, date, _) {
                return _buildDateContainer(date);
              },
              markerBuilder: (context, date, events) {
                if (events.isEmpty) return const SizedBox.shrink();

                return Positioned(
                  bottom: 5.0,
                  right: 5.0,
                  child: Container(
                    width: 6.0,
                    height: 6.0,
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build date container
  Widget _buildDateContainer(DateTime date) {
    return Container(
      margin: const EdgeInsets.all(2.0),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: Text(
        '${date.day}',
        style: const TextStyle(color: Colors.black),
      ),
    );
  }
}

// Helper function to normalize date
DateTime normalizeDate(DateTime date) {
  return DateTime(date.year, date.month, date.day);
}
