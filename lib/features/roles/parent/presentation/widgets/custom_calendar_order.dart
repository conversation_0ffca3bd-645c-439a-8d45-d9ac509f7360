import 'package:class_z/core/imports.dart';
import 'package:table_calendar/table_calendar.dart';

class CustomCalendarOrder extends StatelessWidget {
  final DateTime focusedDay;
  final DateTime? selectedDay;
  final Function(DateTime, DateTime) onDaySelected;
  final List<EventElement>? timeTables;

  const CustomCalendarOrder({
    Key? key,
    required this.focusedDay,
    required this.selectedDay,
    required this.onDaySelected,
    this.timeTables,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Pre-process timeTables into a map for efficient lookup
    final Map<DateTime, List<EventElement>> eventsMap = {};
    if (timeTables != null) {
      for (var eventElement in timeTables!) {
        final eventDate = eventElement.event?.date;
        if (eventDate != null) {
          final normalizedEventDate = normalizeDate(eventDate);
          if (eventsMap.containsKey(normalizedEventDate)) {
            eventsMap[normalizedEventDate]!.add(eventElement);
          } else {
            eventsMap[normalizedEventDate] = [eventElement];
          }
        }
      }
    }

    return Center(
      child: Padding(
        padding: EdgeInsets.only(left: 9.w, right: 9.w),
        child: SizedBox(
          child: TableCalendar(
            firstDay: DateTime.utc(2020, 1, 1),
            lastDay: DateTime.utc(2030, 12, 31),
            focusedDay: focusedDay,
            selectedDayPredicate: (day) => isSameDay(selectedDay, day),
            onDaySelected: onDaySelected,

            // Use the pre-processed map in eventLoader
            eventLoader: (day) {
              final normalizedDay = normalizeDate(day);
              return eventsMap[normalizedDay] ?? [];
            },

            daysOfWeekHeight: 60.h,
            rowHeight: 30.w,
            calendarStyle: CalendarStyle(
              selectedDecoration: BoxDecoration(
                color: Colors.grey[300],
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(5.r),
              ),
              todayDecoration: BoxDecoration(
                color: Colors.grey[300],
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(5.r),
              ),
              weekendTextStyle:
                  const TextStyle(color: AppPallete.secondaryColor),
              holidayTextStyle: const TextStyle(color: Colors.red),
              outsideDaysVisible: true,
            ),
            headerStyle: const HeaderStyle(
              formatButtonVisible: false,
              titleCentered: true,
              leftChevronVisible: false,
              rightChevronVisible: false,
            ),
            calendarBuilders: CalendarBuilders(
              selectedBuilder: (context, date, _) {
                return _buildDateContainer(date);
              },
              todayBuilder: (context, date, _) {
                return _buildDateContainer(date);
              },
              markerBuilder: (context, date, events) {
                if (events.isEmpty) return const SizedBox.shrink();

                return Positioned(
                  bottom: 5.0,
                  right: 5.0,
                  child: Container(
                    width: 6.0,
                    height: 6.0,
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build date container
  Widget _buildDateContainer(DateTime date) {
    return Container(
      margin: const EdgeInsets.all(2.0),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        shape: BoxShape.rectangle,
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: Text(
        '${date.day}',
        style: const TextStyle(color: Colors.black),
      ),
    );
  }
}

// Helper function to normalize date
DateTime normalizeDate(DateTime date) {
  return DateTime(date.year, date.month, date.day);
}
