import 'package:class_z/core/imports.dart';

Widget upcomingClassCard(
    {required BuildContext context,
    required String imagePath,
    bool? course,
    required String title,
    required String category,
    required String location,
    required String ageGroup,
    required String rate,
    required String time,
    required VoidCallback onTap,
    bool? noPadding}) {
  String check = "Course";
  if (course == false) {
    check = "Single";
  }

  // Convert Zcoin rate to HKD for dual currency display
  final int zcoinRate = int.tryParse(rate) ?? 0;
  final double hkdRate = zcoinRate * 25.0; // 1 Zcoin = 25 HKD

  // Handle display text for rates
  final String displayRate = zcoinRate == 0 ? "Free" : rate;
  final String displayHkd =
      zcoinRate == 0 ? "Free" : hkdRate.toStringAsFixed(0);

  return GestureDetector(
    onTap: onTap,
    child: Padding(
      padding: noPadding == true
          ? EdgeInsets.zero
          : EdgeInsets.only(left: 28.w, right: 27.w),
      child: Container(
          height: 107.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Stack(children: [
            CustomImageBuilder(
              imagePath: imageStringGenerator(imagePath: imagePath),
              height: 107.h,
              width: double.infinity,
              borderRadius: 20.r,
            ),
            if (course != null)
              Positioned(
                  top: 10.h,
                  left: 14.w,
                  child: Container(
                    width: 59.w,
                    height: 22.h,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20.r)),
                    child: Center(
                      child: customtext(
                          context: context,
                          newYear: check,
                          font: 15.sp,
                          color: AppPallete.secondaryColor,
                          weight: FontWeight.w700),
                    ),
                  )),
            Positioned(
                top: 65.h,
                left: 14.w,
                child: customtext(
                    context: context,
                    newYear: title,
                    font: 17.sp,
                    shadows: [
                      shadow(blurRadius: 15, opacity: 0.1),
                    ],
                    color: Colors.white,
                    weight: FontWeight.w700)),
            Positioned(
                bottom: 5.h,
                left: 14.w,
                child: Row(children: [
                  customtext(
                      context: context,
                      newYear: "($category)",
                      font: 12.sp,
                      color: Colors.white,
                      weight: FontWeight.w500),
                  SizedBox(
                    width: 13.w,
                  ),
                  customtext(
                    context: context,
                    newYear: ageGroup,
                    font: 12.sp,
                    weight: FontWeight.w600,
                    color: Colors.white,
                  ),
                  SizedBox(
                    width: 13.w,
                  ),
                  customtext(
                      context: context,
                      newYear: "($location)",
                      font: 12.sp,
                      color: Colors.white,
                      weight: FontWeight.w600),
                ])),
            Positioned(
              top: 10.h,
              right: 19.w,
              child: Container(
                width: 59.w,
                height: 22.h,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.r)),
                child: Center(
                  child: customtext(
                      context: context,
                      newYear: time,
                      font: 15.sp,
                      color: AppPallete.secondaryColor,
                      weight: FontWeight.w700),
                ),
              ),
            ),
            // Updated pricing section with dual currency display
            Positioned(
                bottom: 10.h,
                right: 19.w,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // Zcoin pricing row
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        customSvgPicture(
                            imagePath: ImagePath.zSvg,
                            height: 15.h,
                            width: 15.w,
                            color: Colors.white,
                            boxShadow: [
                              shadow(blurRadius: 15, opacity: 0.1),
                            ]),
                        customtext(
                          context: context,
                          newYear: displayRate,
                          color: Colors.white,
                          font: 15.sp,
                          weight: FontWeight.w700,
                          shadows: [
                            shadow(blurRadius: 15, opacity: 0.1),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(height: 2.h),
                    // HKD pricing row
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        customtext(
                          context: context,
                          newYear: 'HKD ',
                          color: Colors.white,
                          font: 12.sp,
                          weight: FontWeight.w500,
                          shadows: [
                            shadow(blurRadius: 15, opacity: 0.1),
                          ],
                        ),
                        customtext(
                          context: context,
                          newYear: displayHkd,
                          color: Colors.white,
                          font: 12.sp,
                          weight: FontWeight.w500,
                          shadows: [
                            shadow(blurRadius: 15, opacity: 0.1),
                          ],
                        ),
                      ],
                    ),
                  ],
                ))
          ])),
    ),
  );
}
