import 'package:class_z/core/imports.dart';

Widget schduleTimeSlotCenterCourse(
    {required BuildContext context,
    required int number,
    required DateTime dateTime,
    required String start,
    required String finish,
    required String duration,
    required VoidCallback onTap,
    bool isJoinable = true}) {
// Get the weekday as an integer (1 = Monday, ..., 7 = Sunday)
  String date = DateFormat('dd/MM/yyyy').format(dateTime);
  int dayOfWeek = dateTime.weekday;

// Convert the integer to a readable day name
  String dayName = _getDayName(dayOfWeek);
  return Padding(
    padding: EdgeInsets.only(left: 52.w, right: 53.w),
    child: GestureDetector(
      onTap: isJoinable ? onTap : null,
      child: Opacity(
        opacity: isJoinable ? 1.0 : 0.5,
        child: Container(
          height: 83.h,
          padding: EdgeInsets.only(left: 16.w, right: 0.w),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20.r),
              boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
          child: Row(
            children: [
              _number(context: context, number: number),
              SizedBox(
                width: 22.w,
              ),
              _dateAndDuration(
                  context: context,
                  date: date,
                  day: dayName,
                  start: start,
                  finish: finish),
              SizedBox(
                width: 15.w,
              ),
              Expanded(
                child: Stack(
                  children: [
                    Positioned(
                      top: 48.h,
                      child: customtext(
                          context: context,
                          newYear: duration,
                          font: 14.sp,
                          weight: FontWeight.w400),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    ),
  );
}

Container _number({required BuildContext context, required int number}) {
  return Container(
    height: 41.h,
    width: 41.w,
    decoration: BoxDecoration(
        color: AppPallete.secondaryColor,
        borderRadius: BorderRadius.circular(10.r)),
    child: Center(
      child: customtext(
          context: context,
          newYear: number.toString(),
          font: 30.sp,
          weight: FontWeight.w600,
          color: Colors.white),
    ),
  );
}

Column _dateAndDuration(
    {required BuildContext context,
    required String date,
    required String day,
    required String start,
    required String finish}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      SizedBox(
        height: 20.h,
      ),
      customtext(
          context: context,
          newYear: "$date ($day)",
          font: 15.sp,
          weight: FontWeight.w500),
      SizedBox(
        height: 8.h,
      ),
      customtext(
          context: context,
          newYear: "$start - $finish",
          font: 20.sp,
          weight: FontWeight.w500)
    ],
  );
}

String _getDayName(int weekday) {
  switch (weekday) {
    case 1:
      return "Mon";
    case 2:
      return "Tue";
    case 3:
      return "Wed";
    case 4:
      return "Thu";
    case 5:
      return "Fri";
    case 6:
      return "Sat";
    case 7:
      return "Sun";
    default:
      return "";
  }
}
