  import 'package:class_z/core/imports.dart';

Widget imagesForMoments({required List<CenterImage> images,required BuildContext context}) {
    if (images.isEmpty) {
      return const SizedBox();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 12.5.w),
          child: customtext(
              context: context,
              newYear: "Images",
              font: 14.sp,
              weight: FontWeight.w600),
        ),
        SizedBox(
          height: 11.h,
        ),
        ...List.generate(
            images.length,
            (index) => Padding(
                  padding:
                      EdgeInsets.only(left: 10.5.w, right: 16.5, bottom: 10.h),
                  child: CustomImageBuilder(
                    height: 204,
                    borderRadius: 20.w,
                    imagePath: imageStringGenerator(
                      imagePath: images[index].url ?? '',
                    ),
                  ),
                )),
      ],
    );
  }