import 'package:class_z/core/common/data/models/pending_for_parent.dart';

import 'package:class_z/core/imports.dart';
import 'package:dio/dio.dart' as dioClient;

import 'package:http/http.dart' as http;

abstract class UserDataSource {
  Future<ChildModel> createChild({
    required String parentId,
    required Map<String, dynamic> data,
  });

  Future<List<ChildModel>> getChildByParentId({required String parentId});
  Future<ChildModel> updateChild({
    required String childId,
    required Map<String, dynamic> data,
  });
  Future<bool> deleteChildById({required String id});
  Future<bool> createOrder({required Map<String, dynamic> orderData});
  Future<OrderModel?> getByIdOrder({required String orderId});
  Future<List<OrderModel>> getAllOrder(
      {required int skip, required int limit, bool? paid});
  Future<OrderModel> updateOrder(
      {required String orderId, required OrderModel order});
  Future<void> deleteOrder({required String orderId});
  Future<List<GetOrderByUserModel>> getByUserOrder();

  Future<QrCodeModel> qrCodeGenerateForUser(
      {required String classId,
      required String studentId,
      required DateTime classDate});
  Future<ReviewResponseModel> getHistoryOfChildId({required String childId});
  Future<Map<String, dynamic>> getChildPerformanceMetrics(
      {required String childId});
  Future<Map<String, dynamic>> getChildParticipationStats(
      {required String childId});
  Future<List<PurchasedHistoryModel>> getPurchasedHistory();
  Future<BalanceModel> getBalance();
  Future<CardModel> getCard();
  Future<List<DiscountModel>> getDiscount();
  Future<bool> updateAddress(
      {required Map<String, dynamic> data, required String id});
  Future<bool> deleteAddress({required String addressId, required String id});
  Future<bool> changeDefaultAddress(
      {required String addressId, required String id});
  Future<bool> contactUs(
      {required Map<String, dynamic> data, required String email});
  Future<ReviewResponse> getParentPendingReviews(String parentId);
  Future<List<ReviewOfChildModel>> getParentReviewHistory(String parentId,
      {String? childId});
  Future<void> deleteCard();
  Future<List<RefundModel>> getRefunds({required String parentId});
  Future<TimetableUpModel> getTimeTableByParentId({required String parentId});
}

class UserDataSourceImpl implements UserDataSource {
  final String device = AppText.device;
  final SharedRepository sharedRepository;
  final http.Client client;
  final dioClient.Dio dio;
  UserDataSourceImpl(
      {required this.sharedRepository,
      required this.client,
      required this.dio});
  final apiService = locator<ApiService>();
  @override
  Future<ChildModel> createChild({
    required String parentId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final formData = await buildFormData(data);
      // Send POST request

      var response = await dio.post(
        '$device/api/children/',
        data: formData,
        options: dioClient.Options(
          headers: {
            'Content-Type': 'multipart/form-data', // Ensure Content-Type is set
          },
        ),
        onSendProgress: (sent, total) {
          print('Upload progress: ${(sent / total * 100).toStringAsFixed(0)}%');
        },
      );
      // Check response
      if (response.statusCode != null &&
          response.statusCode! >= 200 &&
          response.statusCode! <= 300) {
        return ChildModel.fromJson(response.data);
      } else {
        throw Exception(
            'Failed to create child. Status Code: ${response.statusCode}');
      }
    } catch (e) {
      print('Error occurred in createChild: $e');
      throw Exception('Failed to create child: $e');
    }
  }

  @override
  Future<List<ChildModel>> getChildByParentId(
      {required String parentId}) async {
    UserModel? user = sharedRepository.getUserData();
    parentId = user?.data?.parent?.id ?? "";
    String uri = "${AppText.device}/api/children/parent/$parentId";
    print("Fetching child information...");

    try {
      final response = await client.get(Uri.parse(uri));

      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Decode the response body as a list of dynamic objects
        final List<dynamic> jsonData = json.decode(response.body);

        // Convert each item in the list to a ChildModel object
        List<ChildModel> children = jsonData.map((childJson) {
          return ChildModel.fromJson(childJson);
        }).toList();

        return children;
      } else {
        // Handle the error for unsuccessful response status
        throw Exception(
            'Failed to load child information: ${response.reasonPhrase}');
      }
    } catch (e) {
      // Catch any other errors that might occur
      throw Exception('Error fetching child information: $e');
    }
  }

  @override
  Future<ChildModel> updateChild({
    required String childId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final formData = await buildFormData(data);

      var response = await dio.put(
        '$device/api/children/$childId',
        data: formData,
        options: dioClient.Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
        onSendProgress: (sent, total) {
          print('Update progress: ${(sent / total * 100).toStringAsFixed(0)}%');
        },
      );

      if (response.statusCode != null &&
          response.statusCode! >= 200 &&
          response.statusCode! <= 300) {
        return ChildModel.fromJson(response.data);
      } else {
        throw Exception(
            'Failed to update child. Status Code: ${response.statusCode}');
      }
    } catch (e) {
      print('Error occurred in updateChild: $e');
      throw Exception('Failed to update child: $e');
    }
  }

  @override
  Future<bool> deleteChildById({required String id}) async {
    try {
      // Construct the URI for the delete request
      String uri = "${AppText.device}/api/children/$id";

      // Perform the delete request
      final response = await http.delete(Uri.parse(uri));

      // Check if the response indicates success
      if (response.statusCode == 200) {
        // Return true if the deletion was successful
        return true;
      } else {
        // Handle different status codes or throw an exception
        throw Exception('Failed to delete child: ${response.statusCode}');
      }
    } catch (e) {
      // Throw a custom exception with the error message
      throw Exception('Error deleting child information: $e');
    }
  }

  @override
  Future<bool> createOrder({required Map<String, dynamic> orderData}) async {
    try {
      print("Creating order..."); // Debugging print
      // Debugging print
      print(orderData);
      String uri = "$device/api/orders/";

      // Serialize the order data
      var response = await http.post(
        Uri.parse(uri),
        headers: {
          'Content-Type': 'application/json', // Specify content type
        },
        body: jsonEncode(orderData), // Serialize orderData
      );

      // Check the response status code
      if (response.statusCode >= 200 && response.statusCode < 300) {
        print("Order created successfully!"); // Debugging print
        return true;
      } else {
        var responseBody = jsonDecode(response.body);

        throw Exception(responseBody);
      }
    } catch (e) {
      print('Error creating order: $e'); // Log the error
      throw Exception('Error creating order: $e');
    }
  }

  @override
  Future<void> deleteOrder({required String orderId}) {
    // TODO: implement deleteOrder
    throw UnimplementedError();
  }

  @override
  Future<List<OrderModel>> getAllOrder(
      {required int skip, required int limit, bool? paid}) async {
    try {
      print("paid");
      print(paid);
      final queryParameters = {
        'skip': skip.toString(),
        'limit': limit.toString(),
        if (paid != null)
          'paid': paid.toString(), // Only add 'paid' if it's not null
      };

      final uri = Uri.parse('$device/api/orders')
          .replace(queryParameters: queryParameters);
      var response = await http.get(uri);
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        final List<dynamic> jsonData = jsonDecode(response.body);

        List<OrderModel> orders = jsonData.map(
          (jsonData) {
            return OrderModel.fromJson(jsonData);
          },
        ).toList();
        return orders;
      } else {
        throw Exception('Failed to load orders');
      }
    } catch (e) {
      throw Exception('Error creating order: $e');
    }
  }

  @override
  Future<OrderModel?> getByIdOrder({required String orderId}) {
    // TODO: implement getByIdOrder
    throw UnimplementedError();
  }

  @override
  Future<List<GetOrderByUserModel>> getByUserOrder() async {
    try {
      UserModel? user = sharedRepository.getUserData();

      // If user is not logged in or parent id is unavailable, return empty list instead of throwing.
      final String? id = user?.data?.parent?.id;
      if (id == null || id.isEmpty) {
        print('No logged-in user – returning empty orders list');
        return [];
      }
      final String nonNullId = id; // safe to cast after previous check
      print("HI $nonNullId");
      print("Fetching orders for user: $nonNullId");
      String uri = "$device/api/orders/user/$nonNullId";
      var response = await http.get(Uri.parse(uri));

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final List<dynamic> jsonData = jsonDecode(response.body);
        print("ASASASASAAAAAAAAAAAAAAAAAA");
        print(jsonData);
        List<GetOrderByUserModel> orders =
            jsonData.map((e) => GetOrderByUserModel.fromJson(e)).toList();

        if (orders.isEmpty) {
          print("No orders found for user $nonNullId");
        } else {
          print("${orders.length} orders fetched for user $nonNullId");
        }

        return orders;
      } else {
        print("Failed to fetch orders: ${response.body}");
        throw Exception('Failed to fetch orders. Please try again');
      }
    } catch (e) {
      print('Error fetching orders: $e');
      throw Exception('Error getting Order: $e');
    }
  }

  @override
  Future<OrderModel> updateOrder(
      {required String orderId, required OrderModel order}) {
    // TODO: implement updateOrder
    throw UnimplementedError();
  }

  @override
  Future<QrCodeModel> qrCodeGenerateForUser(
      {required String classId,
      required String studentId,
      required DateTime classDate}) async {
    try {
      Map<String, String> payload = {
        "classId": classId,
        "studentId": studentId,
        "classDate": classDate.toIso8601String(),
      };
      String uri = "$device/api/attendance/generate";
      var response = await http.post(Uri.parse(uri),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode(payload));
      print('response: ${response.body}');
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        print(response.body);
        Map<String, dynamic> jsonData = jsonDecode(response.body);
        print(jsonData);
        QrCodeModel code = QrCodeModel.fromJson(jsonData);
        return code;
      } else {
        throw Exception('Failed to generate code ');
      }
    } catch (e) {
      throw Exception('Error Generating qr code: $e');
    }
  }

  @override
  Future<ReviewResponseModel> getHistoryOfChildId({
    required String childId,
  }) async {
    try {
      print('her is am');
      String uri = "$device/api/review/$childId/child";
      var response = await http.get(Uri.parse(uri));

      if (response.statusCode >= 200 && response.statusCode < 300) {
        Map<String, dynamic> jsonData = jsonDecode(response.body);
        print(jsonData);

        // Parse the full response into ReviewResponseModel
        return ReviewResponseModel.fromJson(jsonData);
      } else {
        throw Exception('Failed to fetch reviews: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching child review history: $e');
    }
  }

  @override
  Future<List<PurchasedHistoryModel>> getPurchasedHistory() async {
    try {
      UserModel? user = sharedRepository.getUserData();
      String? id = user?.data?.parent?.id;
      String uri = "$device/api/purchasedHistory/get/$id";
      var response = await http.get(Uri.parse(uri));
      if (response.statusCode >= 200 && response.statusCode < 300) {
        List<dynamic> jsonData = jsonDecode(response.body);
        List<PurchasedHistoryModel> purchased =
            jsonData.map((e) => PurchasedHistoryModel.fromJson(e)).toList();
        return purchased;
      } else {
        throw Exception("Something went wrong please try again");
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<BalanceModel> getBalance() async {
    try {
      UserModel? user = sharedRepository.getUserData();

      // Check if user or parent data is null
      if (user?.data?.parent?.id == null) {
        // Return default balance model with 0 balance if user not properly logged in
        print("Cannot fetch balance: User ID not available");
        return BalanceModel(balance: 0);
      }

      String? id = user?.data?.parent?.id;
      String uri = "$device/api/balance/$id";

      try {
        var response = await http.get(Uri.parse(uri));
        if (response.statusCode >= 200 && response.statusCode < 300) {
          final jsonData = jsonDecode(response.body);
          BalanceModel balance = BalanceModel.fromJson(jsonData);
          return balance;
        } else {
          // Log the error but return a default model
          print("Balance API error: ${response.statusCode}, ${response.body}");
          // Return a default balance model
          return BalanceModel(balance: 0);
        }
      } catch (networkError) {
        // Handle network errors specifically
        print("Network error fetching balance: $networkError");
        return BalanceModel(balance: 0);
      }
    } catch (e) {
      // Return default balance model on any other error
      print("Error fetching balance: $e");
      return BalanceModel(balance: 0);
    }
  }

  @override
  Future<CardModel> getCard() async {
    // Get token from SharedRepository
    final token = sharedRepository.getToken();
    final response = await apiService.get('/api/cards/', token: token);

    // The apiService returns the decoded JSON (Map<String, dynamic>)
    // Convert it safely to a CardModel before returning
    try {
      if (response == null) {
        return CardModel();
      }

      // If apiService already decoded to Map
      if (response is Map<String, dynamic>) {
        return CardModel.fromJson(response);
      }

      // If apiService returned a JSON string
      if (response is String) {
        return cardModelFromJson(response);
      }

      // If response is unexpected type, log and return empty model
      print('Unexpected card response type: \\${response.runtimeType}');
      return CardModel();
    } catch (e) {
      print('Error parsing card data: \\$e');
      return CardModel();
    }
  }

  @override
  Future<void> deleteCard() async {
    await apiService.delete('/api/cards/');
  }

  @override
  Future<List<DiscountModel>> getDiscount() async {
    try {
      String? userId =
          locator<SharedRepository>().getUserData()?.data?.parent?.id;

      // Check if user ID is null
      if (userId == null) {
        print("Cannot fetch discounts: User ID not available");
        return []; // Return empty list if user ID is not available
      }

      try {
        final List<dynamic> jsonData =
            await apiService.get('/api/discount/user/$userId');
        List<DiscountModel> discount =
            jsonData.map((e) => DiscountModel.fromJson(e)).toList();
        return discount;
      } catch (apiError) {
        // Handle API errors
        print("Discount API error: $apiError");
        return []; // Return empty list on API error
      }
    } catch (e) {
      // Handle any other errors
      print("Error fetching discounts: $e");
      return []; // Return empty list on any other error
    }
  }

  @override
  Future<bool> updateAddress(
      {required Map<String, dynamic> data, required String id}) async {
    try {
      print('data: $data, id: $id');

      // Get token for authentication
      String? token = sharedRepository.getToken();
      String uri = "$device/api/auth/update/$id";

      // Print the data being sent for debugging
      print('Sending JSON data: $data');

      // Use JSON request instead of form data
      var response = await dio.put(
        uri,
        data: data, // Send the raw data directly
        options: dioClient.Options(
          headers: {
            'auth-token': token,
            'Content-Type': 'application/json', // Change to JSON content type
          },
          sendTimeout: const Duration(minutes: 2),
          receiveTimeout: const Duration(minutes: 2),
        ),
      );

      if (response.statusCode! >= 200 && response.statusCode! <= 300) {
        print("Address update successful: ${response.data}");
        UserModel user = UserModel.fromJson(response.data);
        await sharedRepository.updateUserData(user.data);
        return true;
      } else {
        print("Address update failed with status code: ${response.statusCode}");
        print("Response data: ${response.data}");
        throw Exception("Failed to update address: ${response.statusCode}");
      }
    } catch (e) {
      if (e is dioClient.DioException) {
        print("DioError during address update: ${e.message}");
        print("DioError type: ${e.type}");
        if (e.response != null) {
          print("Response status code: ${e.response?.statusCode}");
          print("Response data: ${e.response?.data}");
        }
      } else {
        print('Error updating address: ${e.toString()}');
      }
      throw Exception(e.toString());
    }
  }

  @override
  Future<bool> deleteAddress(
      {required String addressId, required String id}) async {
    try {
      var response =
          await apiService.delete("/api/auth/deleteAddress/$id/$addressId");

      if (response == null) {
        throw Exception("Failed to delete address");
      }
      Data userData = Data.fromJson(response['data']);
      print('new address: ${userData.parent?.location}');
      print(
          'previous address: ${sharedRepository.getUserData()?.data?.parent?.location?.length}');
      await sharedRepository.updateParentData(userData.parent);
      return true;
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<bool> changeDefaultAddress(
      {required String addressId, required String id}) async {
    try {
      print('addressId: $addressId, id: $id');
      var response =
          await apiService.put("/api/auth/defaultAddress/$id/$addressId", {});
      if (response == null) {
        throw Exception("Failed to change default address");
      }
      Data userData = Data.fromJson(response['data']);
      print('new address: ${userData.parent?.location}');
      print(
          'previous address: ${sharedRepository.getUserData()?.data?.parent?.location?.length}');
      await sharedRepository.updateParentData(userData.parent);
      return true;
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<bool> contactUs(
      {required Map<String, dynamic> data, required String email}) async {
    try {
      print('data: $data, email: $email');
      var response = await apiService.post("/api/parent/contact-us", data);
      if (response == null) {
        throw Exception("Failed to send contact us message");
      }
      print('Contact Us response: $response');
      if (response['success'] == true) {
        return true;
      }
      return false;
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<Map<String, dynamic>> getChildPerformanceMetrics(
      {required String childId}) async {
    try {
      String uri = "$device/api/children/metrics/$childId";
      var response = await http.get(Uri.parse(uri));

      if (response.statusCode >= 200 && response.statusCode < 300) {
        Map<String, dynamic> jsonData = jsonDecode(response.body);
        return jsonData;
      } else {
        throw Exception(
            'Failed to fetch child performance metrics: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching child performance metrics: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getChildParticipationStats(
      {required String childId}) async {
    try {
      String uri = "$device/api/children/stats/$childId";
      var response = await http.get(Uri.parse(uri));

      if (response.statusCode >= 200 && response.statusCode < 300) {
        Map<String, dynamic> jsonData = jsonDecode(response.body);
        return jsonData;
      } else {
        throw Exception(
            'Failed to fetch child participation stats: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching child participation stats: $e');
    }
  }

  @override
  Future<ReviewResponse> getParentPendingReviews(String parentId) async {
    try {
      // Fix the endpoint to match the backend route
      String uri = "$device/api/review/parent/$parentId/pending";
      var response = await http.get(Uri.parse(uri));
      print("Pending reviews response status: ${response.statusCode}");
      //  print("Pending reviews response body: ${response.body}");

      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Parse the response which now includes pagination data
        Map<String, dynamic> jsonDataMap = jsonDecode(response.body);

        // Extract the reviews array from the response
        ReviewResponse reviewsList = ReviewResponse.fromJson(jsonDataMap);

        return reviewsList;
      } else {
        throw Exception(
            'Failed to fetch pending reviews: ${response.statusCode}');
      }
    } catch (e) {
      print("Error in getParentPendingReviews: $e");
      throw Exception('Error fetching pending reviews: $e');
    }
  }

  @override
  Future<List<ReviewOfChildModel>> getParentReviewHistory(String parentId,
      {String? childId}) async {
    try {
      // Use the correct endpoint
      String uri = "$device/api/review/parent/$parentId/history";

      // Add childId as a query parameter if provided
      if (childId != null) {
        uri += "?childId=$childId";
      }

      print("Review history URI: $uri");
      var response = await http.get(Uri.parse(uri));
      print("Review history response status: ${response.statusCode}");
      print("Review history response body: ${response.body}");

      if (response.statusCode >= 200 && response.statusCode < 300) {
        List<dynamic> jsonData = jsonDecode(response.body);
        print("Review history parsed JSON data count: ${jsonData.length}");

        if (jsonData.isNotEmpty) {
          print("Review history first item sample: ${jsonData[0]}");
        }

        List<ReviewOfChildModel> reviewHistory = [];

        for (int i = 0; i < jsonData.length; i++) {
          try {
            final review = ReviewOfChildModel.fromJson(jsonData[i]);
            // print(
            //     "Successfully parsed review $i: ${review.revieweeType} - ${review.revieweeId}");
            reviewHistory.add(review);
          } catch (parseError) {
            print("Error parsing review at index $i: $parseError");
            print("Problematic JSON: ${jsonData[i]}");
          }
        }

        print(
            "Successfully parsed ${reviewHistory.length} reviews out of ${jsonData.length} total");
        return reviewHistory;
      } else {
        throw Exception(
            'Failed to fetch review history: ${response.statusCode}');
      }
    } catch (e) {
      print("Error fetching review history: $e");
      throw Exception('Error fetching review history: $e');
    }
  }

  @override
  Future<List<RefundModel>> getRefunds({required String parentId}) async {
    try {
      String uri = "$device/api/refund/parent/$parentId";
      var response = await http.get(Uri.parse(uri));
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final Map<String, dynamic> jsonMap = jsonDecode(response.body);
        final List<dynamic> refundsList = jsonMap['refunds'] ?? [];
        return refundsList.map((e) => RefundModel.fromJson(e)).toList();
      } else {
        throw Exception('Failed to fetch refunds: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching refunds: $e');
    }
  }

  @override
  Future<TimetableUpModel> getTimeTableByParentId(
      {required String parentId}) async {
    String uri = "$device/api/events/parent/$parentId";
    try {
      final response = await client.get(Uri.parse(uri));
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final jsonData = json.decode(response.body);
        return TimetableUpModel.fromJson(jsonData);
      } else {
        throw Exception('Failed to load timetable: \\${response.reasonPhrase}');
      }
    } catch (e) {
      throw Exception('Error fetching timetable: $e');
    }
  }
}
