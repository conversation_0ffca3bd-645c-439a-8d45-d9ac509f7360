import 'package:class_z/core/imports.dart';
import 'package:dartz/dartz.dart';

class UserRepoImpl implements UserRepoDomain {
  final UserDataSource userDataSource;

  UserRepoImpl({required this.userDataSource});
  @override
  Future<ChildModel> createChild({
    required String parentId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final childModel =
          await userDataSource.createChild(parentId: parentId, data: data);
      return childModel;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<ChildModel>> getChildByParentId(
      {required String parentId}) async {
    try {
      final List<ChildModel> child =
          await userDataSource.getChildByParentId(parentId: parentId);
      print(child);
      return child;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ChildModel> updateChild({
    required String childId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final childModel =
          await userDataSource.updateChild(childId: childId, data: data);
      return childModel;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> deleteChildById({required String id}) async {
    try {
      final child = await userDataSource.deleteChildById(id: id);
      return child;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> createOrder({required Map<String, dynamic> order}) async {
    try {
      final orders = await userDataSource.createOrder(orderData: order);
      return orders;
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> deleteOrder({required String orderId}) {
    // TODO: implement deleteOrder
    throw UnimplementedError();
  }

  @override
  Future<List<OrderModel>> getAllOrder(
      {required int skip, required int limit, bool? paid}) async {
    try {
      return await userDataSource.getAllOrder(
          skip: skip, limit: limit, paid: paid);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<OrderModel?> getByIdOrder({required String orderId}) {
    // TODO: implement getByIdOrder
    throw UnimplementedError();
  }

  @override
  Future<List<GetOrderByUserModel>> getByUserOrder() async {
    try {
      return await userDataSource.getByUserOrder();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<OrderModel> updateOrder(
      {required String orderId, required OrderModel order}) {
    // TODO: implement updateOrder
    throw UnimplementedError();
  }

  @override
  Future<QrCodeModel> generateCodeForUser(
      {required String classId,
      required String studentId,
      required DateTime classDate}) async {
    try {
      return await userDataSource.qrCodeGenerateForUser(
          classId: classId, studentId: studentId, classDate: classDate);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<ReviewResponseModel> getHistoryOfChildId(
      {required String childId}) async {
    try {
      return await userDataSource.getHistoryOfChildId(childId: childId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<PurchasedHistoryModel>> getPurchasedHistory() async {
    try {
      return await userDataSource.getPurchasedHistory();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<BalanceModel> getBalance() async {
    try {
      return await userDataSource.getBalance();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<CardModel> getCard() async {
    try {
      return await userDataSource.getCard();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<void> deleteCard() async {
    try {
      return await userDataSource.deleteCard();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<Either<Failure, List<DiscountEntity>>> getDiscount() async {
    try {
      List<DiscountModel> discounts = await userDataSource.getDiscount();
      final List<DiscountEntity> discountEntity = discounts;
      return Right(discountEntity);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> deleteAddress(
      {required String addressId, required String id}) async {
    try {
      final bool success =
          await userDataSource.deleteAddress(addressId: addressId, id: id);
      return Right(success);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> updateAddress(
      {required Map<String, dynamic> data, required String id}) async {
    try {
      final bool success =
          await userDataSource.updateAddress(data: data, id: id);
      return Right(success);
    } catch (e) {
      print('use case');
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> changeDefaultAddress(
      {required String addressId, required String id}) async {
    try {
      final bool success = await userDataSource.changeDefaultAddress(
          addressId: addressId, id: id);
      return Right(success);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> contactUs(
      {required Map<String, dynamic> data, required String email}) async {
    try {
      final bool success =
          await userDataSource.contactUs(data: data, email: email);
      return Right(success);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<RefundModel>>> getRefunds(
      {required String parentId}) async {
    try {
      final refunds = await userDataSource.getRefunds(parentId: parentId);
      return Right(refunds);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<TimetableUpModel> getTimeTableByParentId(
      {required String parentId}) async {
    try {
      return await userDataSource.getTimeTableByParentId(parentId: parentId);
    } catch (e) {
      rethrow;
    }
  }
}
