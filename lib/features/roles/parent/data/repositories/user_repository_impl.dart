import 'package:class_z/core/common/data/models/pending_for_parent.dart';
import 'package:class_z/core/error/exception.dart';
import 'package:class_z/core/error/failure.dart';
import 'package:class_z/features/roles/parent/data/dataSources/user_data_source.dart';
import 'package:class_z/features/roles/parent/data/repositories/user_repo.dart';
import 'package:class_z/features/roles/parent/domain/repositories/user_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:class_z/core/common/data/models/review_of_child.dart';
import 'package:class_z/core/common/data/models/refund_model.dart';

class UserRepositoryImpl extends UserRepoImpl implements UserRepository {
  final UserDataSource remoteDataSource;

  UserRepositoryImpl({required this.remoteDataSource})
      : super(userDataSource: remoteDataSource);

  @override
  Future<Either<Failure, bool>> contactUs(
      {required Map<String, dynamic> data, required String email}) async {
    try {
      final result = await remoteDataSource.contactUs(data: data, email: email);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getChildParticipationStats(
      {required String childId}) async {
    try {
      final stats =
          await remoteDataSource.getChildParticipationStats(childId: childId);
      return Right(stats);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getChildPerformanceMetrics(
      {required String childId}) async {
    try {
      final metrics =
          await remoteDataSource.getChildPerformanceMetrics(childId: childId);
      return Right(metrics);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, ReviewResponse>> getParentPendingReviews(
      String parentId) async {
    try {
      final pendingReviews =
          await remoteDataSource.getParentPendingReviews(parentId);
      return Right(pendingReviews);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<ReviewOfChildModel>>> getParentReviewHistory(
      String parentId,
      {String? childId}) async {
    try {
      final reviewHistory = await remoteDataSource
          .getParentReviewHistory(parentId, childId: childId);
      return Right(reviewHistory);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<RefundModel>>> getRefunds(
      {required String parentId}) async {
    try {
      final refunds = await remoteDataSource.getRefunds(parentId: parentId);
      return Right(refunds);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
