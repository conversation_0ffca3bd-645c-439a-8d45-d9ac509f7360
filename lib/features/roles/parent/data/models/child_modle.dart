// To parse this JSON data, do
//
//     final childModel = childModelFromJson(jsonString);

import 'package:class_z/core/imports.dart';

ChildModel childModelFromJson(String str) =>
    ChildModel.fromJson(json.decode(str));

String childModelToJson(ChildModel data) => json.encode(data.toJson());

class ChildModel {
  BusinessCertificate? mainImage;
  String? id;
  String? fullname;
  String? age;
  String? idcard;
  String? birthday;
  String? school;
  bool? sen;
  String? phone;
  String? parent;
  DateTime? createdAt;
  DateTime? updatedAt;
  double? rating;
  int? v;
  double? outstandingQuality;
  double? keyCompetency;
  String? distinctiveConduct;
  String? learningProgress;
  DateTime? metricsLastUpdated;
  String? classZId;

  ChildModel(
      {this.mainImage,
      this.id,
      this.fullname,
      this.age,
      this.idcard,
      this.birthday,
      this.school,
      this.sen,
      this.phone,
      this.parent,
      this.createdAt,
      this.updatedAt,
      this.v,
      this.rating,
      this.outstandingQuality,
      this.keyCompetency,
      this.distinctiveConduct,
      this.learningProgress,
      this.metricsLastUpdated,
      this.classZId});

  factory ChildModel.fromJson(Map<String, dynamic> json) => ChildModel(
      mainImage: json["mainImage"] == null
          ? null
          : BusinessCertificate.fromJson(json["mainImage"]),
      id: json["_id"],
      fullname: json["fullname"],
      age: json["age"],
      idcard: json["idcard"],
      birthday: json["birthday"],
      school: json["school"],
      sen: json["sen"],
      phone: json["phone"],
      parent: json["parent"],
      createdAt:
          json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
      updatedAt:
          json["updatedAt"] == null ? null : DateTime.parse(json["updatedAt"]),
      v: json["__v"],
      rating: json["rating"]?.toDouble(),
      outstandingQuality: json["outstandingQuality"]?.toDouble(),
      keyCompetency: json["keyCompetency"]?.toDouble(),
      distinctiveConduct: json["distinctiveConduct"],
      learningProgress: json["learningProgress"],
      metricsLastUpdated: json["metricsLastUpdated"] == null
          ? null
          : DateTime.parse(json["metricsLastUpdated"]),
      classZId: json["classZId"]);

  Map<String, dynamic> toJson() => {
        "mainImage": mainImage?.toJson(),
        "_id": id,
        "fullname": fullname,
        "age": age,
        "idcard": idcard,
        "birthday": birthday,
        "school": school,
        "sen": sen,
        "phone": phone,
        "parent": parent,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
        "rating": rating,
        "outstandingQuality": outstandingQuality,
        "keyCompetency": keyCompetency,
        "distinctiveConduct": distinctiveConduct,
        "learningProgress": learningProgress,
        "metricsLastUpdated": metricsLastUpdated?.toIso8601String(),
        "classZId": classZId
      };
}
