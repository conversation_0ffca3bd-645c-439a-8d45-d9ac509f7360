import 'package:class_z/core/common/data/models/user_address_model.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'parent_model.g.dart'; // Ensure this line is included for generated code

@HiveType(typeId: 12) // Updated typeId to 12
class ParentData1 extends HiveObject {
  @HiveField(0)
  String? id;

  @HiveField(1)
  String? fullname;

  @HiveField(2)
  ParentImage? image;

  @HiveField(3)
  String? nickname;

  @HiveField(4)
  String? phone;

  @HiveField(5)
  List<UserAddress>? location;
  @HiveField(6)
  String? referal;

  @HiveField(7)
  bool? isComplete;

  @HiveField(8)
  int? exp;
  @HiveField(9)
  String? email;
  @HiveField(10)
  final DateTime? createdAt;

  @HiveField(11)
  final DateTime? updatedAt;
  @HiveField(12)
  final String? baseUser;
  @HiveField(13)
  int? balance;
  @HiveField(14)
  String? classZId;
  @HiveField(15)
  bool? activitiesNotification;
  @HiveField(16)
  bool? promotionNotification;

  ParentData1(
      {this.id,
      this.fullname,
      this.image,
      this.nickname,
      this.phone,
      this.location,
      this.referal,
      this.exp,
      this.isComplete,
      this.email,
      this.createdAt,
      this.updatedAt,
      this.baseUser,
      this.balance,
      this.classZId,
      this.activitiesNotification,
      this.promotionNotification});

  factory ParentData1.fromJson(Map<String, dynamic> json) => ParentData1(
      id: json["_id"],
      fullname: json["fullname"],
      image: json["mainImage"] == null
          ? null
          : ParentImage.fromJson(json["mainImage"]),
      nickname: json["nickname"],
      phone: json["phone"],
      location: json["location"] == null
          ? null
          : (json["location"] as List)
              .map((e) => UserAddress.fromJson(e))
              .toList(),
      referal: json["referal"],
      exp: json["exp"],
      isComplete: json["isCompleted"],
      email: json["email"] != null ? json["email"] : null,
      createdAt:
          json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      baseUser: json["baseUser"],
      balance: json["balance"],
      classZId: json["classzId"],
      activitiesNotification: json["activitiesNotification"],
      promotionNotification: json["promotionNotification"]);

  Map<String, dynamic> toJson() => {
        "_id": id,
        "fullname": fullname,
        "mainImage": image?.toJson(),
        "nickname": nickname,
        "phone": phone,
        "location": location,
        "referal": referal,
        'exp': exp,
        "isComplete": isComplete,
        "email": email,
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
        'baseUser': baseUser,
        "balance": balance,
        "classzId": classZId,
        "activitiesNotification": activitiesNotification,
        "promotionNotification": promotionNotification
      };

  @override
  String toString() {
    return 'ParentData(id: $id, fullname: $fullname, image: $image, nickname: $nickname, phone: $phone, location: $location, referal: $referal, isComplete: $isComplete)';
  }
}

@HiveType(typeId: 13) // Update typeId for ParentImage
class ParentImage extends HiveObject {
  @HiveField(0)
  String? url;

  @HiveField(1)
  String? contentType;

  ParentImage({
    this.url,
    this.contentType,
  });

  factory ParentImage.fromJson(Map<String, dynamic> json) => ParentImage(
        url: json["url"],
        contentType: json["contentType"],
      );

  Map<String, dynamic> toJson() => {
        "url": url,
        "contentType": contentType,
      };

  @override
  String toString() {
    return 'ParentImage(url: $url, contentType: $contentType)';
  }
}
