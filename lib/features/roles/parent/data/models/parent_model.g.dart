// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'parent_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ParentData1Adapter extends TypeAdapter<ParentData1> {
  @override
  final int typeId = 12;

  @override
  ParentData1 read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ParentData1(
      id: fields[0] as String?,
      fullname: fields[1] as String?,
      image: fields[2] as ParentImage?,
      nickname: fields[3] as String?,
      phone: fields[4] as String?,
      location: (fields[5] as List?)?.cast<UserAddress>(),
      referal: fields[6] as String?,
      exp: fields[8] as int?,
      isComplete: fields[7] as bool?,
      email: fields[9] as String?,
      createdAt: fields[10] as DateTime?,
      updatedAt: fields[11] as DateTime?,
      baseUser: fields[12] as String?,
      balance: fields[13] as int?,
      classZId: fields[14] as String?,
      activitiesNotification: fields[15] as bool?,
      promotionNotification: fields[16] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, ParentData1 obj) {
    writer
      ..writeByte(17)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.fullname)
      ..writeByte(2)
      ..write(obj.image)
      ..writeByte(3)
      ..write(obj.nickname)
      ..writeByte(4)
      ..write(obj.phone)
      ..writeByte(5)
      ..write(obj.location)
      ..writeByte(6)
      ..write(obj.referal)
      ..writeByte(7)
      ..write(obj.isComplete)
      ..writeByte(8)
      ..write(obj.exp)
      ..writeByte(9)
      ..write(obj.email)
      ..writeByte(10)
      ..write(obj.createdAt)
      ..writeByte(11)
      ..write(obj.updatedAt)
      ..writeByte(12)
      ..write(obj.baseUser)
      ..writeByte(13)
      ..write(obj.balance)
      ..writeByte(14)
      ..write(obj.classZId)
      ..writeByte(15)
      ..write(obj.activitiesNotification)
      ..writeByte(16)
      ..write(obj.promotionNotification);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ParentData1Adapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ParentImageAdapter extends TypeAdapter<ParentImage> {
  @override
  final int typeId = 13;

  @override
  ParentImage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ParentImage(
      url: fields[0] as String?,
      contentType: fields[1] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ParentImage obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.url)
      ..writeByte(1)
      ..write(obj.contentType);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ParentImageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
