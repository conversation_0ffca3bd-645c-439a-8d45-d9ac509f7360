import 'package:class_z/core/imports.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';

class CreateChildUseCase {
  final UserRepoDomain userRepoDomain;

  CreateChildUseCase({required this.userRepoDomain});
  Future<ChildModel> call({
    required String parentId,
    required Map<String, dynamic> data,
  }) {
    return userRepoDomain.createChild(
      parentId: parentId,
      data: data,
    );
  }
}

class GetChildByParentIdUseCase {
  final UserRepoDomain userRepoDomain;

  GetChildByParentIdUseCase({required this.userRepoDomain});

  Future<List<ChildModel>> call(String parentId) async {
    if (kDebugMode) {
      print("Fetching children for parent: $parentId");
    }
    // Assuming that userRepoDomain has a method to fetch child data by ID.
    final child = await userRepoDomain.getChildByParentId(parentId: parentId);
    return child;
  }
}

class UpdateChildUseCase {
  final UserRepoDomain userRepoDomain;

  UpdateChildUseCase({required this.userRepoDomain});
  Future<ChildModel> call({
    required String childId,
    required Map<String, dynamic> data,
  }) {
    return userRepoDomain.updateChild(
      childId: childId,
      data: data,
    );
  }
}

class DeleteChildByIdUseCase {
  final UserRepoDomain userRepoDomain;

  DeleteChildByIdUseCase({required this.userRepoDomain});
  Future<bool> call(String id) async {
    final child = await userRepoDomain.deleteChildById(id: id);
    return child;
  }
}

class OrderUseCase {
  final UserRepoDomain userRepoDomain;

  OrderUseCase({required this.userRepoDomain});

  Future<bool> createOrder({required Map<String, dynamic> orderData}) async {
    return await userRepoDomain.createOrder(order: orderData);
  }

  Future<OrderModel?> getByIdOrder({required String orderId}) async {
    return await userRepoDomain.getByIdOrder(orderId: orderId);
  }

  Future<List<OrderModel>> getAllOrder(
      {required int skip, required int limit, bool? paid}) async {
    return await userRepoDomain.getAllOrder(
        skip: skip, limit: limit, paid: paid);
  }

  Future<OrderModel> updateOrder(
      {required String orderId, required OrderModel orderData}) async {
    return await userRepoDomain.updateOrder(orderId: orderId, order: orderData);
  }

  Future<void> deleteOrder({required String orderId}) async {
    return await userRepoDomain.deleteOrder(orderId: orderId);
  }

  Future<List<GetOrderByUserModel>> getByUserOrder() async {
    return await userRepoDomain.getByUserOrder();
  }
}

class QrCodeUseCase {
  final UserRepoDomain userRepoDomain;

  QrCodeUseCase({required this.userRepoDomain});
  Future<QrCodeModel> generateQrCodeForUser(
      {required String classId,
      required String studentId,
      required DateTime classDate}) async {
    return await userRepoDomain.generateCodeForUser(
        classId: classId, studentId: studentId, classDate: classDate);
  }
}

class GetHistoryOfChildIdUseCase {
  final UserRepoDomain userRepoDomain;

  GetHistoryOfChildIdUseCase({required this.userRepoDomain});
  Future<ReviewResponseModel> call({required String childId}) async {
    return await userRepoDomain.getHistoryOfChildId(childId: childId);
  }
}

class GetPurchasedHistoryUseCase {
  final UserRepoDomain userRepoDomain;

  GetPurchasedHistoryUseCase({required this.userRepoDomain});
  Future<List<PurchasedHistoryModel>> call() async {
    return await userRepoDomain.getPurchasedHistory();
  }
}

class GetBalanceUseCase {
  final UserRepoDomain userRepoDomain;

  GetBalanceUseCase({required this.userRepoDomain});
  Future<BalanceModel> call() async {
    return await userRepoDomain.getBalance();
  }
}

class GetCardUseCase {
  final UserRepoDomain userRepoDomain;

  GetCardUseCase({required this.userRepoDomain});
  Future<CardModel> call() async {
    return await userRepoDomain.getCard();
  }
}

class DeleteCardUseCase {
  final UserRepoDomain userRepoDomain;

  DeleteCardUseCase({required this.userRepoDomain});
  Future<void> call() async {
    return await userRepoDomain.deleteCard();
  }
}

class GetDiscountUseCase extends UseCase<List<DiscountEntity>, NoParams> {
  final UserRepoImpl _userRepoImpl;
  GetDiscountUseCase(this._userRepoImpl);
  @override
  Future<Either<Failure, List<DiscountEntity>>> call(NoParams params) {
    return _userRepoImpl.getDiscount();
  }
}

class UpdateAddressUseCase {
  final UserRepoDomain userRepoDomain;

  UpdateAddressUseCase({required this.userRepoDomain});
  Future<Either<Failure, bool>> call(
      {required Map<String, dynamic> data, required String id}) async {
    return await userRepoDomain.updateAddress(data: data, id: id);
  }
}

class DeleteAddressUseCase {
  final UserRepoDomain userRepoDomain;

  DeleteAddressUseCase({required this.userRepoDomain});
  Future<Either<Failure, bool>> call(
      {required String addressId, required String id}) async {
    return await userRepoDomain.deleteAddress(addressId: addressId, id: id);
  }
}

class ChangeDefaultAddressUseCase {
  final UserRepoDomain userRepoDomain;

  ChangeDefaultAddressUseCase({required this.userRepoDomain});
  Future<Either<Failure, bool>> call(
      {required String addressId, required String id}) async {
    return await userRepoDomain.changeDefaultAddress(
        addressId: addressId, id: id);
  }
}

class ContactUsUseCase {
  final UserRepoDomain userRepoDomain;

  ContactUsUseCase({required this.userRepoDomain});
  Future<Either<Failure, bool>> call(
      {required Map<String, dynamic> data, required String email}) async {
    return await userRepoDomain.contactUs(data: data, email: email);
  }
}

class RefundUseCase {
  final UserRepoDomain userRepoDomain;
  RefundUseCase({required this.userRepoDomain});
  Future<Either<Failure, List<RefundModel>>> call(
      {required String parentId}) async {
    return await userRepoDomain.getRefunds(parentId: parentId);
  }
}

class GetTimeTableByParentIdUseCase {
  final UserRepoDomain userRepoDomain;
  GetTimeTableByParentIdUseCase({required this.userRepoDomain});
  Future<TimetableUpModel> call({required String parentId}) async {
    return await userRepoDomain.getTimeTableByParentId(parentId: parentId);
  }
}
