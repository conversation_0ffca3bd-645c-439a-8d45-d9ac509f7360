import 'package:class_z/core/common/data/models/pending_for_parent.dart';
import 'package:class_z/core/error/failure.dart';
import 'package:class_z/features/roles/parent/domain/repositories/user_repository.dart';
import 'package:dartz/dartz.dart';

class GetParentPendingReviewsUseCase {
  final UserRepository userRepository;

  GetParentPendingReviewsUseCase({required this.userRepository});

  Future<Either<Failure, ReviewResponse>> call(String parentId) async {
    return await userRepository.getParentPendingReviews(parentId);
  }
}
