import 'package:class_z/core/error/failure.dart';
import 'package:class_z/core/use_cases/use_case.dart';
import 'package:class_z/features/roles/parent/domain/repositories/user_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

class GetChildPerformanceMetricsUseCase
    implements UseCase<Map<String, dynamic>, Params> {
  final UserRepository repository;

  GetChildPerformanceMetricsUseCase(this.repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(Params params) async {
    return await repository.getChildPerformanceMetrics(childId: params.childId);
  }
}

class Params extends Equatable {
  final String childId;

  const Params({required this.childId});

  @override
  List<Object> get props => [childId];
}
