import 'package:class_z/core/error/failure.dart';
import 'package:class_z/core/common/data/models/review_of_child.dart';
import 'package:class_z/features/roles/parent/domain/repositories/user_repository.dart';
import 'package:dartz/dartz.dart';

class GetParentReviewHistoryUseCase {
  final UserRepository userRepository;

  GetParentReviewHistoryUseCase({required this.userRepository});

  Future<Either<Failure, List<ReviewOfChildModel>>> call(
      String parentId, {String? childId}) async {
    return await userRepository.getParentReviewHistory(parentId, childId: childId);
  }
}
