class ChildEntity {
  String? id;
  String? fullname;
  final String? idcard;
  final DateTime? birthday;
  final String? school;
  final bool? sen;
  final String? phone;
  final String? parent;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? v;
  final Image? image;

  ChildEntity({
    this.id,
    this.fullname,
    this.idcard,
    this.birthday,
    this.school,
    this.sen,
    this.phone,
    this.parent,
    this.createdAt,
    this.updatedAt,
    this.v,
    this.image,
  });

  factory ChildEntity.fromJson(Map<String, dynamic> json) {
    return ChildEntity(
      id: json["_id"],
      fullname: json["fullname"],
      idcard: json["idcard"],
      birthday:
          json["birthday"] != null ? DateTime.parse(json["birthday"]) : null,
      school: json["school"],
      sen: json["sen"],
      phone: json["phone"],
      parent: json["parent"],
      createdAt:
          json["createdAt"] != null ? DateTime.parse(json["createdAt"]) : null,
      updatedAt:
          json["updatedAt"] != null ? DateTime.parse(json["updatedAt"]) : null,
      v: json["__v"],
      image: json["image"] != null ? Image.fromJson(json["image"]) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "_id": id,
      "fullname": fullname,
      "idcard": idcard,
      "birthday": birthday?.toIso8601String(),
      "school": school,
      "sen": sen,
      "phone": phone,
      "parent": parent,
      "createdAt": createdAt?.toIso8601String(),
      "updatedAt": updatedAt?.toIso8601String(),
      "__v": v,
      "image": image?.toJson(),
    };
  }
}

class Image {
  final String? url;
  final String? contentType;

  Image({
    this.url,
    this.contentType,
  });

  factory Image.fromJson(Map<String, dynamic> json) {
    return Image(
      url: json["url"],
      contentType: json["contentType"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "url": url,
      "contentType": contentType,
    };
  }
}
