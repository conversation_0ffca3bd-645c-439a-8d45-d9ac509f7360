import 'package:class_z/core/imports.dart';

import 'package:dartz/dartz.dart';

abstract class UserRepoDomain {
  Future<ChildModel> createChild({
    required String parentId,
    required Map<String, dynamic> data,
  });

  Future<List<ChildModel>> getChildByParentId({required String parentId});

  Future<ChildModel> updateChild({
    required String childId,
    required Map<String, dynamic> data,
  });

  Future<bool> deleteChildById({required String id});

  ///orders
  Future<bool> createOrder({required Map<String, dynamic> order});
  Future<OrderModel?> getByIdOrder({required String orderId});
  Future<List<OrderModel>> getAllOrder(
      {required int skip, required int limit, bool? paid});
  Future<OrderModel> updateOrder(
      {required String orderId, required OrderModel order});
  Future<void> deleteOrder({required String orderId});
  Future<List<GetOrderByUserModel>> getByUserOrder();
  Future<QrCodeModel> generateCodeForUser(
      {required String classId,
      required String studentId,
      required DateTime classDate});
  Future<ReviewResponseModel> getHistoryOfChildId({required String childId});
  Future<List<PurchasedHistoryModel>> getPurchasedHistory();
  Future<BalanceModel> getBalance();
  Future<CardModel> getCard();
  Future<void> deleteCard();
  Future<Either<Failure, List<DiscountEntity>>> getDiscount();
  Future<Either<Failure, bool>> updateAddress(
      {required Map<String, dynamic> data, required String id});
  Future<Either<Failure, bool>> deleteAddress(
      {required String addressId, required String id});
  Future<Either<Failure, bool>> changeDefaultAddress(
      {required String addressId, required String id});
  Future<Either<Failure, bool>> contactUs(
      {required Map<String, dynamic> data, required String email});
  Future<Either<Failure, List<RefundModel>>> getRefunds(
      {required String parentId});
  Future<TimetableUpModel> getTimeTableByParentId({required String parentId});
}
