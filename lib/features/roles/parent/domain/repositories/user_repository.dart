import 'package:class_z/core/common/data/models/pending_for_parent.dart';
import 'package:class_z/core/error/failure.dart';
import 'package:class_z/features/roles/parent/domain/repositories/user_repo_domain.dart';
import 'package:dartz/dartz.dart';
import 'package:class_z/core/common/data/models/review_of_child.dart';
import 'package:class_z/core/common/data/models/refund_model.dart';

abstract class UserRepository extends UserRepoDomain {
  Future<Either<Failure, bool>> contactUs(
      {required Map<String, dynamic> data, required String email});
  Future<Either<Failure, Map<String, dynamic>>> getChildParticipationStats(
      {required String childId});
  Future<Either<Failure, Map<String, dynamic>>> getChildPerformanceMetrics(
      {required String childId});

  Future<Either<Failure, ReviewResponse>> getParentPendingReviews(
      String parentId);

  Future<Either<Failure, List<ReviewOfChildModel>>> getParentReviewHistory(
      String parentId,
      {String? childId});

  Future<Either<Failure, List<RefundModel>>> getRefunds(
      {required String parentId});
}
