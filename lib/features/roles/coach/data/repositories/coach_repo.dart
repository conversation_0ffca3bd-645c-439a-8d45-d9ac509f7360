import 'package:class_z/core/imports.dart';

import 'package:dartz/dartz.dart';

class CoachRepoImpl extends CoachRepoDomain {
  final CoachDataSource coachDataSource;

  CoachRepoImpl({required this.coachDataSource});

  @override
  Future<bool> createCoach({required Map<String, dynamic> data}) async {
    try {
      return await coachDataSource.createCoach(data: data);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> updateCoach(
      {required String coachId, required Map<String, dynamic> data}) async {
    try {
      return await coachDataSource.updateCoach(coachId: coachId, data: data);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<CoachModel>> getAllCoach() async {
    try {
      return await coachDataSource.getAllCoach();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<Either<Failure, bool>> assignCenter(
      {required String centerId,
      required String coachId,
      required String type,
      String? notificationId,
      bool? saveData}) async {
    try {
      final bool success = await coachDataSource.assignCenter(
          centerId: centerId,
          coachId: coachId,
          type: type,
          saveData: saveData,
          notificationId: notificationId);
      return Right(success);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> removeCenter(
      {required String centerId,
      required String coachId,
      required String type,
      bool? saveData}) async {
    try {
      final bool success = await coachDataSource.removeCenter(
          centerId: centerId, coachId: coachId, type: type, saveData: saveData);
      return Right(success);
    } on ServerException catch (e) {
      print(e.toString());
      return Left(ServerFailure(message: e.toString()));
    } on UnimplementedError catch (_) {
      return Left(
          ServerFailure(message: "This feature is not yet implemented."));
    }
  }

  @override
  Future<Either<Failure, List<ClassModel>?>> getAllPrograms(
      {required String centerId}) async {
    try {
      List<ClassModel>? classes =
          await coachDataSource.getAllPrograms(centerId: centerId);
      return Right(classes);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, CoachModel?>> getCoachInfoById(
      {required String coachId}) async {
    try {
      var response = await coachDataSource.getCoachInfoById(coachId: coachId);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
