import 'dart:convert';
import 'package:class_z/features/roles/center/data/models/center_model.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'coach_model.g.dart'; // Ensure this line is included for generated code

CoachModel coachDataFromJson(String str) =>
    CoachModel.fromJson(json.decode(str));

String coachDataToJson(CoachModel data) => json.encode(data.toJson());

@HiveType(typeId: 14) // Unique typeId for CoachData
class CoachModel extends HiveObject {
  @HiveField(0)
  BusinessCertificate? mainImage;

  @HiveField(1)
  String? displayName;

  @HiveField(2)
  Address? address;

  @HiveField(3)
  List<String>? languages;

  @HiveField(4)
  String? description;

  @HiveField(5)
  bool? sen;

  @HiveField(6)
  String? ageFrom;

  @HiveField(7)
  String? ageTo;

  @HiveField(8)
  List<Experience>? experience;

  @HiveField(9)
  List<Skill>? skill;

  @HiveField(10)
  List<Accredation>? accredation;

  @HiveField(11)
  dynamic center;
  @HiveField(12)
  String? id;
  @HiveField(13)
  final DateTime? createdAt;

  @HiveField(14)
  final DateTime? updatedAt;
  @HiveField(15)
  final String? baseUser;
  @HiveField(16)
  final double? rating;
  @HiveField(17)
  final double? reviewCount;
  @HiveField(18)
  final String? managerId;
  @HiveField(19)
  final String? email;
  @HiveField(20)
  final String? legalName;
  @HiveField(21)
  final List<CenterImage>? images;
  @HiveField(22)
  final String? classZId;

  CoachModel(
      {this.mainImage,
      this.displayName,
      this.address,
      this.languages,
      this.description,
      this.sen,
      this.ageFrom,
      this.ageTo,
      this.experience,
      this.skill,
      this.accredation,
      this.center,
      this.id,
      this.createdAt,
      this.updatedAt,
      this.baseUser,
      this.rating,
      this.reviewCount,
      this.managerId,
      this.email,
      this.legalName,
      this.images,
      this.classZId});

  factory CoachModel.fromJson(Map<String, dynamic> json) {
    print("🛠️ CoachModel.fromJson CALLED with: $json");
    return CoachModel(
        mainImage: json['mainImage'] != null
            ? BusinessCertificate.fromJson(json['mainImage'])
            : null,
        displayName: json["displayName"] != null ? json["displayName"] : null,
        address:
            json["address"] == null ? null : Address.fromJson(json["address"]),
        languages: json["languages"] == null
            ? []
            : List<String>.from(json["languages"]),
        description: json["description"] != null ? json["description"] : null,
        sen: json["sen"] != null ? json["sen"] : null,
        ageFrom: json["ageFrom"] != null ? json["ageFrom"] : null,
        ageTo: json["ageTo"] != null ? json["ageTo"] : null,
        experience: json["experience"] == null
            ? []
            : List<Experience>.from(
                json["experience"].map((x) => Experience.fromJson(x))),
        skill: json["skill"] == null
            ? []
            : List<Skill>.from(
                json["skill"]
                    .where((x) => x != null)
                    .map((x) => Skill.fromJson(x)),
              ),
        accredation: json["accredation"] == null
            ? []
            : List<Accredation>.from(
                json["accredation"].map((x) => Accredation.fromJson(x))),
        center: () {
          final centerJson = json["center"];

          if (centerJson == null) {
            return CenterData(); // or `null` if CenterData is nullable
          }

          if (centerJson is String) {
            return CenterData(id: centerJson); // Handle string ID case
          }

          if (centerJson is Map<String, dynamic>) {
            return CenterData.fromJson(centerJson); // Handle nested object
          }

          // Fallback if unexpected type
          return CenterData();
        }(),
        id: json["_id"],
        createdAt: json['createdAt'] != null
            ? DateTime.parse(json['createdAt'])
            : null,
        updatedAt: json['updatedAt'] != null
            ? DateTime.parse(json['updatedAt'])
            : null,
        baseUser: json["baseUser"],
        rating:
            json['rating'] != null ? (json['rating'] as num).toDouble() : null,
        reviewCount: json["reviewCount"] != null
            ? (json['reviewCount'] as num).toDouble()
            : null,
        managerId: json["manager"] != null ? json["manager"] : null,
        email: json["email"] != null ? json["email"] : null,
        legalName: json['legalName'] != null ? json["legalName"] : null,
        images: json['images'] != null
            ? List<CenterImage>.from(
                json['images'].map((x) => CenterImage.fromJson(x)))
            : null,
        classZId: json["classzId"] != null ? json["classzId"] : null);
  }
  Map<String, dynamic> toJson() => {
        "mainImage": mainImage?.toJson(),
        "displayName": displayName,
        "address": address?.toJson(),
        "languages": languages == null ? [] : List<dynamic>.from(languages!),
        "description": description,
        "sen": sen,
        "ageFrom": ageFrom,
        "ageTo": ageTo,
        "experience": experience == null
            ? []
            : List<dynamic>.from(experience!.map((x) => x.toJson())),
        "skill": skill == null
            ? []
            : List<dynamic>.from(skill!.map((x) => x.toJson())),
        "accredation": accredation == null
            ? []
            : List<dynamic>.from(accredation!.map((x) => x.toJson())),
        "centers": center,
        "_id": id,
        'createdAt': createdAt?.toIso8601String(),
        'updatedAt': updatedAt?.toIso8601String(),
        'baseUser': baseUser,
        "rating": rating,
        "reviewCount": reviewCount,
        "manager": managerId,
        "email": email,
        'legalName': legalName,
        'images': images?.map((x) => x.toJson()).toList(),
        "classzId": classZId
      };

  // @override
  // String toString() {
  //   return 'CoachData(displayName: $displayName, mainImage: $mainImage, address: $address, languages: $languages, description: $description, sen: $sen, ageFrom: $ageFrom, ageTo: $ageTo, experience: $experience, skill: $skill, accredation: $accredation, centers: $centers)';
  // }
}

// Nested models for CoachImage, CoachAddress, Experience, Skill, and Accredation

@HiveType(typeId: 15)
class CoachAddress {
  @HiveField(0)
  String? address1;

  @HiveField(1)
  String? address2;

  @HiveField(2)
  String? city;

  @HiveField(3)
  String? region;

  CoachAddress({this.address1, this.address2, this.city, this.region});

  factory CoachAddress.fromJson(Map<String, dynamic> json) => CoachAddress(
        address1: json["address1"],
        address2: json["address2"],
        city: json["city"],
        region: json["region"],
      );

  Map<String, dynamic> toJson() => {
        "address1": address1,
        "address2": address2,
        "city": city,
        "region": region,
      };
}

@HiveType(typeId: 16)
class Experience {
  @HiveField(0)
  String? title;

  @HiveField(1)
  String? organization;

  @HiveField(2)
  String? from;

  @HiveField(3)
  String? to;

  Experience({this.title, this.organization, this.from, this.to});

  factory Experience.fromJson(Map<String, dynamic> json) => Experience(
        title: json["title"],
        organization: json["organization"],
        from: json["from"],
        to: json["to"],
      );

  Map<String, dynamic> toJson() => {
        "title": title,
        "organization": organization,
        "from": from,
        "to": to,
      };
}

@HiveType(typeId: 17)
class Skill {
  @HiveField(0)
  String? skillname;

  @HiveField(1)
  String? skillsince;

  @HiveField(2)
  String? skilllevel;

  @HiveField(3)
  String? skillnameofassociation;

  Skill(
      {this.skillname,
      this.skillsince,
      this.skilllevel,
      this.skillnameofassociation});
  @override
  String toString() {
    return ' $skillname';
  }

  factory Skill.fromJson(Map<String, dynamic> json) => Skill(
        skillname: json["skillname"],
        skillsince: json["skillsince"],
        skilllevel: json["skilllevel"],
        skillnameofassociation: json["skillnameofassociation"],
      );

  Map<String, dynamic> toJson() => {
        "skillname": skillname,
        "skillsince": skillsince,
        "skilllevel": skilllevel,
        "skillnameofassociation": skillnameofassociation,
      };
}

@HiveType(typeId: 18)
class Accredation {
  @HiveField(0)
  String? name;

  @HiveField(1)
  String? result;

  @HiveField(2)
  String? year;

  Accredation({this.name, this.result, this.year});

  factory Accredation.fromJson(Map<String, dynamic> json) => Accredation(
        name: json["name"],
        result: json["result"],
        year: json["year"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "result": result,
        "year": year,
      };
}
