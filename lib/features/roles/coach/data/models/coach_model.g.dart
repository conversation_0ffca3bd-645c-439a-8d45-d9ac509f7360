// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'coach_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CoachModelAdapter extends TypeAdapter<CoachModel> {
  @override
  final int typeId = 14;

  @override
  CoachModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CoachModel(
      mainImage: fields[0] as BusinessCertificate?,
      displayName: fields[1] as String?,
      address: fields[2] as Address?,
      languages: (fields[3] as List?)?.cast<String>(),
      description: fields[4] as String?,
      sen: fields[5] as bool?,
      ageFrom: fields[6] as String?,
      ageTo: fields[7] as String?,
      experience: (fields[8] as List?)?.cast<Experience>(),
      skill: (fields[9] as List?)?.cast<Skill>(),
      accredation: (fields[10] as List?)?.cast<Accredation>(),
      center: fields[11] as dynamic,
      id: fields[12] as String?,
      createdAt: fields[13] as DateTime?,
      updatedAt: fields[14] as DateTime?,
      baseUser: fields[15] as String?,
      rating: fields[16] as double?,
      reviewCount: fields[17] as double?,
      managerId: fields[18] as String?,
      email: fields[19] as String?,
      legalName: fields[20] as String?,
      images: (fields[21] as List?)?.cast<CenterImage>(),
      classZId: fields[22] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, CoachModel obj) {
    writer
      ..writeByte(23)
      ..writeByte(0)
      ..write(obj.mainImage)
      ..writeByte(1)
      ..write(obj.displayName)
      ..writeByte(2)
      ..write(obj.address)
      ..writeByte(3)
      ..write(obj.languages)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.sen)
      ..writeByte(6)
      ..write(obj.ageFrom)
      ..writeByte(7)
      ..write(obj.ageTo)
      ..writeByte(8)
      ..write(obj.experience)
      ..writeByte(9)
      ..write(obj.skill)
      ..writeByte(10)
      ..write(obj.accredation)
      ..writeByte(11)
      ..write(obj.center)
      ..writeByte(12)
      ..write(obj.id)
      ..writeByte(13)
      ..write(obj.createdAt)
      ..writeByte(14)
      ..write(obj.updatedAt)
      ..writeByte(15)
      ..write(obj.baseUser)
      ..writeByte(16)
      ..write(obj.rating)
      ..writeByte(17)
      ..write(obj.reviewCount)
      ..writeByte(18)
      ..write(obj.managerId)
      ..writeByte(19)
      ..write(obj.email)
      ..writeByte(20)
      ..write(obj.legalName)
      ..writeByte(21)
      ..write(obj.images)
      ..writeByte(22)
      ..write(obj.classZId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CoachModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CoachAddressAdapter extends TypeAdapter<CoachAddress> {
  @override
  final int typeId = 15;

  @override
  CoachAddress read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CoachAddress(
      address1: fields[0] as String?,
      address2: fields[1] as String?,
      city: fields[2] as String?,
      region: fields[3] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, CoachAddress obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.address1)
      ..writeByte(1)
      ..write(obj.address2)
      ..writeByte(2)
      ..write(obj.city)
      ..writeByte(3)
      ..write(obj.region);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CoachAddressAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ExperienceAdapter extends TypeAdapter<Experience> {
  @override
  final int typeId = 16;

  @override
  Experience read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Experience(
      title: fields[0] as String?,
      organization: fields[1] as String?,
      from: fields[2] as String?,
      to: fields[3] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Experience obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.title)
      ..writeByte(1)
      ..write(obj.organization)
      ..writeByte(2)
      ..write(obj.from)
      ..writeByte(3)
      ..write(obj.to);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExperienceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SkillAdapter extends TypeAdapter<Skill> {
  @override
  final int typeId = 17;

  @override
  Skill read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Skill(
      skillname: fields[0] as String?,
      skillsince: fields[1] as String?,
      skilllevel: fields[2] as String?,
      skillnameofassociation: fields[3] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Skill obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.skillname)
      ..writeByte(1)
      ..write(obj.skillsince)
      ..writeByte(2)
      ..write(obj.skilllevel)
      ..writeByte(3)
      ..write(obj.skillnameofassociation);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SkillAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AccredationAdapter extends TypeAdapter<Accredation> {
  @override
  final int typeId = 18;

  @override
  Accredation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Accredation(
      name: fields[0] as String?,
      result: fields[1] as String?,
      year: fields[2] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Accredation obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.result)
      ..writeByte(2)
      ..write(obj.year);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AccredationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
