import 'package:class_z/core/imports.dart';
import 'package:dio/dio.dart';

import 'package:http/http.dart' as http;

abstract class CoachDataSource {
  Future<List<CoachModel>> getAllCoach();
  Future<bool> createCoach({required Map<String, dynamic> data});
  Future<bool> updateCoach(
      {required String coachId, required Map<String, dynamic> data});
  Future<bool> assignCenter(
      {required String centerId,
      required String coachId,
      required String type,
      String? notificationId,
      bool? saveData});
  Future<bool> removeCenter(
      {required String centerId,
      required String coachId,
      required String type,
      bool? saveData});
  Future<List<ClassModel>?> getAllPrograms({required String centerId});
  Future<CoachModel?> getCoachInfoById({required String coachId});
}

class CoachDataSourceImpl extends CoachDataSource {
  final http.Client client;
  final Dio dio;
  final SharedRepository sharedRepository;
  late final String device = AppText.device;
  final ApiService apiService;
  CoachDataSourceImpl(
      {required this.client,
      required this.dio,
      required this.apiService,
      required this.sharedRepository});

  @override
  Future<bool> createCoach({required Map<String, dynamic> data}) async {
    try {
      final String? token = sharedRepository.getToken();
      if (token == null || token.isEmpty) {
        print("Error: Auth token is null or empty");
        throw Exception("Authentication token is missing");
      }

      final String url = "$device/api/coach/create";
      print("Creating coach at URL: $url");

      // Debug the data being sent
      if (data.containsKey('mainImage') && data['mainImage'] is File) {
        final File imageFile = data['mainImage'];
        print("Image file path: ${imageFile.path}");
        print("Image file exists: ${imageFile.existsSync()}");
        print("Image file size: ${imageFile.lengthSync() ~/ 1024} KB");
      }

      final formData = await buildFormData(data);
      print("FormData created with ${formData.files.length} files");

      // Send the request
      var response = await dio.post(
        url,
        data: formData,
        options: Options(
          headers: {
            'auth-token': token,
            'Content-Type': 'multipart/form-data',
          },
          receiveTimeout: const Duration(seconds: 30),
          sendTimeout: const Duration(seconds: 30),
        ),
        onSendProgress: (sent, total) {
          print('Upload progress: ${(sent / total * 100).toStringAsFixed(0)}%');
        },
      );

      print("Response status code: ${response.statusCode}");
      print("Response data type: ${response.data.runtimeType}");

      if (response.statusCode! >= 200 && response.statusCode! <= 300) {
        final decoded = response.data;
        print('Coach creation successful: ${decoded != null}');

        if (decoded != null) {
          try {
            // The response should contain both token and coach data
            if (decoded['coach'] != null) {
              CoachModel createdCoach = CoachModel.fromJson(decoded['coach']);
              print("Coach model created successfully");
              await sharedRepository.updateCoachData(createdCoach);
              print("Coach data saved in shared repository");
            }
          } catch (e) {
            print("Error parsing coach data: $e");
            // Continue anyway since the API call was successful
          }
        }

        return true;
      } else {
        print('Failed to create coach. Status code: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print("Create coach error: $e");
      throw Exception("Failed to create coach: $e");
    }
  }

  @override
  Future<bool> updateCoach(
      {required String coachId, required Map<String, dynamic> data}) async {
    try {
      final String? token = sharedRepository.getToken();
      if (token == null || token.isEmpty) {
        print("Error: Auth token is null or empty");
        throw Exception("Authentication token is missing");
      }

      final String url = "$device/api/coach/$coachId";
      print("Updating coach at URL: $url");

      // Debug the data being sent
      if (data.containsKey('mainImage') && data['mainImage'] is File) {
        final File imageFile = data['mainImage'];
        print("Image file path: ${imageFile.path}");
        print("Image file exists: ${imageFile.existsSync()}");
        print("Image file size: ${imageFile.lengthSync() ~/ 1024} KB");
      }

      final formData = await buildFormData(data);
      print("FormData created with ${formData.files.length} files");

      // Send the request
      var response = await dio.put(
        url,
        data: formData,
        options: Options(
          headers: {
            'auth-token': token,
            'Content-Type': 'multipart/form-data',
          },
          receiveTimeout: const Duration(seconds: 30),
          sendTimeout: const Duration(seconds: 30),
        ),
        onSendProgress: (sent, total) {
          print('Upload progress: ${(sent / total * 100).toStringAsFixed(0)}%');
        },
      );

      print("Response status code: ${response.statusCode}");
      print("Response data type: ${response.data.runtimeType}");

      if (response.statusCode! >= 200 && response.statusCode! <= 300) {
        final decoded = response.data;
        print('Coach update successful: ${decoded != null}');

        if (decoded != null) {
          try {
            CoachModel updatedCoach = CoachModel.fromJson(decoded);
            print("Coach model created successfully");

            if (sharedRepository.getCoachData() != null) {
              await sharedRepository.updateCoachData(updatedCoach);
              print("Coach data updated in shared repository");
            }
          } catch (e) {
            print("Error parsing coach data: $e");
            // Continue anyway since the API call was successful
          }
        }

        return true;
      } else {
        print('Failed to update coach. Status code: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print("Update coach error: $e");
      throw Exception("Failed to update coach: $e");
    }
  }

  @override
  Future<List<CoachModel>> getAllCoach() async {
    try {
      String uri = "$device/api/coach";
      var response = await http.get(Uri.parse(uri));

      if (response.statusCode >= 200 && response.statusCode <= 300) {
        final List<dynamic> jsonData = jsonDecode(response.body);
        print(jsonData);
        List<CoachModel> coach =
            jsonData.map((e) => CoachModel.fromJson(e)).toList();
        print(coach);
        return coach;
      } else {
        throw Exception('Failed to load classes: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<bool> assignCenter(
      {required String centerId,
      required String coachId,
      required String type,
      String? notificationId,
      bool? saveData}) async {
    try {
      final response = await apiService.post("/api/coach/assign", {
        "centerId": centerId,
        "coachId": coachId,
        "type": type,
        "notificationId": notificationId
      });
      print('done $response');
      Data data = Data.fromJson(response);
      print(saveData);
      if (saveData == true) {
        print('updating $type');
        if (type == 'manager')
          await sharedRepository.updateUserData(data);
        else {
          print("udpating coach ${data.coach}");
          await sharedRepository.updateCoachData(data.coach);
        }
      }

      return true;
    } catch (e) {
      throw ServerException(e.toString());
    }
  }

  @override
  Future<bool> removeCenter(
      {required String centerId,
      required String coachId,
      required String type,
      bool? saveData}) async {
    try {
      print('here i am');
      final response = await apiService.post("/api/coach/remove",
          {"centerId": centerId, "coachId": coachId, "type": type});
      print('done $response');
      Data data = Data.fromJson(response);

      if (saveData == true) {
        if (type == 'manager')
          await sharedRepository.updateUserData(data);
        else {
          print("udpating coach ${data.coach}");
          await sharedRepository.updateCoachData(data.coach);
        }
      }

      return true;
    } catch (e) {
      throw ServerException(
          'Failed to remove coach from center:${e.toString()}');
    }
  }

  @override
  Future<List<ClassModel>?> getAllPrograms({required String centerId}) async {
    try {
      final response = await this.apiService.get('/api/class/center/$centerId');
      if (response != null) {
        List<ClassModel> classes = response
            .map<ClassModel>(
                (e) => ClassModel.fromJson(e as Map<String, dynamic>))
            .toList();
        return classes;
      } else {
        return null;
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<CoachModel?> getCoachInfoById({required String coachId}) async {
    var response = await apiService.get('/api/coach/$coachId');
    if (response != null) {
      CoachModel coach = CoachModel.fromJson(response as Map<String, dynamic>);
      if (sharedRepository.getCoachData() != null)
        await sharedRepository.updateCoachData(coach);
      return coach;
    } else {
      return null;
    }
  }
}
