import 'package:cached_network_image/cached_network_image.dart';
import 'package:class_z/core/imports.dart';

Widget coachStudentListCard(
    {required BuildContext context,
    required String imagePath,
    required String name,
    required String age,
    required String rating,
    required bool sen,
    required String id,
    required bool message,
    VoidCallback? onMessagePressed}) {
  print(message);
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image + Rating
          Stack(
            children: [
              Container(
                height: 100,
                width: 100,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(imagePath),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Positioned(
                top: 8,
                left: 7,
                child: customRating(context: context, rating: rating),
              ),
            ],
          ),
          const SizedBox(width: 8),

          // Info and button
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name + SEN
                  Row(
                    children: [
                      Expanded(
                        child: customtext(
                          context: context,
                          newYear: name,
                          font: 18.sp,
                          weight: FontWeight.w700,
                        ),
                      ),
                      if (sen) customSen(),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Age
                  customtext(
                    context: context,
                    newYear: "Age $age",
                    font: 15.sp,
                    weight: FontWeight.w400,
                  ),
                  const SizedBox(height: 8),

                  // ID + Message Button
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: customtext(
                            context: context,
                            newYear: id,
                            font: 15.sp,
                            weight: FontWeight.w400,
                          ),
                        ),
                        if (message)
                          Padding(
                            padding: const EdgeInsets.only(right: 17.0),
                            child: Button(
                              buttonText: 'message',
                              color: AppPallete.secondaryColor,
                              width: 100,
                              height: 28,
                              borderRadius: 5,
                              onPressed: onMessagePressed,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      const SizedBox(height: 11),
      customDivider(padding: 11, right: 11),
    ],
  );
}
