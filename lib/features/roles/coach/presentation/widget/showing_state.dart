import 'package:flutter/material.dart';

void errorState({required BuildContext context, required String error}) {
  WidgetsBinding.instance.addPostFrameCallback((_) {
    if (context.mounted) {
      try {
        final scaffoldMessenger = ScaffoldMessenger.of(context);
        scaffoldMessenger.clearSnackBars(); // Clear any existing SnackBars
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(error),
            backgroundColor: Colors.red, // 🔴 Show red for error
            duration: Duration(seconds: 2),
          ),
        );
      } catch (e) {
        print('Error showing snackbar: $error');
      }
    }
  });
}

void greenState({required BuildContext context, required String message}) {
  WidgetsBinding.instance.addPostFrameCallback((_) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green, // ✅ Show green for success
          duration: Duration(seconds: 2),
        ),
      );
    }
  });
}
