import 'package:class_z/core/imports.dart';

Widget addNewOnProfile(
    {required BuildContext context,
    required String title,
     VoidCallback? onTap}) {
  return Column(
    children: [
      Center(
        child: InkWell(
          onTap: onTap,
          child: Container(
              width: 100.w,
              height: 100.h,
              decoration: BoxDecoration(
                  color: AppPallete.secondaryColor,
                  borderRadius: BorderRadius.circular(99.r)),
              child: Icon(
                Icons.add,
                color: Colors.white,
                size: 40.w,
              )),
        ),
      ),
      SizedBox(
        height: 29.h,
      ),
      Center(
        child: customtext(
            context: context,
            newYear: title,
            font: 20.sp,
            weight: FontWeight.w400,
            color: AppPallete.secondaryColor),
      )
    ],
  );
}
