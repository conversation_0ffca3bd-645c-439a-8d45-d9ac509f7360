import 'package:class_z/core/common/widgets/custom_loading_widget.dart';
import 'package:flutter/material.dart';
import 'dart:async';

// Tracks whether the loading dialog is currently shown
bool _isLoadingDialogShowing = false;

// Add a short lock to prevent spamming the dialog
bool _isOperationInProgress = false;
Timer? _lockTimer;
const _lockDuration = Duration(milliseconds: 500);

/// Shows a non-dismissible loading dialog
void loadingState({required BuildContext context}) {
  if (_isLoadingDialogShowing || _isOperationInProgress) {
    print('[loadingState] Skipped: Already showing or locked');
    return;
  }

  _isOperationInProgress = true;
  _isLoadingDialogShowing = true;

  print('[loadingState] Showing dialog');

  try {
    showDialog(
      context: context,
      barrierDismissible: false,
      useRootNavigator: true,
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async => false,
          child: const Dialog(
            backgroundColor: Colors.transparent,
            elevation: 0,
            child: CustomLoadingWidget(),
          ),
        );
      },
    ).then((_) {
      _isLoadingDialogShowing = false;
      print('[loadingState] Dialog closed');
    });
  } catch (e) {
    _isLoadingDialogShowing = false;
    print('[loadingState] Error showing dialog: $e');
  }

  _lockTimer?.cancel();
  _lockTimer = Timer(_lockDuration, () {
    _isOperationInProgress = false;
  });
}

/// Dismisses the loading dialog
void hideLoadingDialog(BuildContext context) {
  print('[hideLoadingDialog] Called');
  print('  - Showing: $_isLoadingDialogShowing');
  print('  - Lock: $_isOperationInProgress');

  if (!_isLoadingDialogShowing) {
    print('[hideLoadingDialog] No loading dialog to dismiss.');
    return;
  }

  try {
    if (Navigator.of(context, rootNavigator: true).canPop()) {
      Navigator.of(context, rootNavigator: true).pop();
      print('[hideLoadingDialog] Dialog dismissed');
    } else {
      print('[hideLoadingDialog] Nothing to pop in navigator');
    }
  } catch (e) {
    print('[hideLoadingDialog] Error dismissing dialog: $e');
  } finally {
    _isLoadingDialogShowing = false;
    _isOperationInProgress = false;
    _lockTimer?.cancel();
  }
}
