import 'package:class_z/core/imports.dart';

class CoachPrograms extends StatefulWidget {
  final String? centerId;
  const CoachPrograms({this.centerId, super.key});

  @override
  State<CoachPrograms> createState() => _CoachProgramsState();
}

class _CoachProgramsState extends State<CoachPrograms> {
  List<ClassModel>? _cachedClasses;

  @override
  void initState() {
    super.initState();
    _fetchData();
  }

  void _fetchData() {
    context
        .read<CenterBloc>()
        .add(GetAllClassesEvent(centerId: widget.centerId ?? ''));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          _fetchData();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomAppBarDouble(
                title: "Saved Program",
                title2: "Setup the program offering",
                leading: customBackButton(),
              ),
              BlocConsumer<CenterBloc, CenterState>(
                listener: (context, state) {
                  if (state is CenterLoadingState) {
                    loadingState(context: context);
                  } else
                    hideLoadingDialog(context);
                  if (state is CenterErrorState) {
                    errorState(context: context, error: state.message);
                  }
                  if (state is ClassListFetchSuccess) {
                    // Cache the classes data when successfully fetched
                    _cachedClasses = state.classes;
                  }
                },
                builder: (context, state) {
                  // Show cached data if available, regardless of current state
                  final classesToShow = (state is ClassListFetchSuccess)
                      ? state.classes
                      : _cachedClasses;

                  if (classesToShow != null && classesToShow.isNotEmpty) {
                    return ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: classesToShow.length,
                      itemBuilder: (context, index) {
                        final classModel = classesToShow[index];
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            savedClassCard(
                                context: context,
                                edit: false,
                                locationType:
                                    getLocationType(classModel.address) ?? '',
                                imagePath: imageStringGenerator(
                                    imagePath: classModel.mainImage?.url ?? ''),
                                title: classModel.classProviding ??
                                    'Untitled Class',
                                category: classModel.level ?? 'Level TBD',
                                coach: classModel.coach?.displayName ??
                                    'Coach TBD',
                                ageGroup: _getCoachAgeGroup(classModel),
                                rate: _getCoachRate(classModel),
                                time: _getCoachTime(classModel),
                                onDelete: () {},
                                onTap: () {
                                  NavigatorService.pushNamed(
                                      AppRoutes.classDetails,
                                      arguments: {
                                        'classModel': classModel,
                                        'edit': false,
                                        'currentSessionIndex': classModel
                                                        .dates !=
                                                    null &&
                                                classModel.dates!.isNotEmpty
                                            ? classModel.dates!.length -
                                                1 // For testing, use course completion
                                            : 0,
                                      });
                                }),
                            const SizedBox(height: 15),
                          ],
                        );
                      },
                    );
                  } else if (state is CenterLoadingState &&
                      _cachedClasses == null) {
                    // Only show loading if we don't have cached data
                    return SizedBox(
                      height: getHeight(context: context) / 2,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  } else {
                    return SizedBox(
                      height: getHeight(context: context) / 2,
                      child: const Center(
                        child: Text('No Programs available'),
                      ),
                    );
                  }
                },
              )
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods for coach programs
  String _getCoachAgeGroup(ClassModel classModel) {
    final ageFrom = classModel.ageFrom;
    final ageTo = classModel.ageTo;

    if (ageFrom == null && ageTo == null) return "Age TBD";
    if (ageFrom == null) return "Up to $ageTo";
    if (ageTo == null) return "$ageFrom+";
    return "Age $ageFrom - $ageTo";
  }

  String _getCoachRate(ClassModel classModel) {
    // First check the main class charge
    final mainCharge = classModel.charge;
    if (mainCharge != null && mainCharge > 0) {
      return mainCharge.toString();
    }

    // If main charge is null/0, check the dates for charge information
    if (classModel.dates?.isNotEmpty == true) {
      final dateCharge = classModel.dates?.first.charge;
      if (dateCharge != null && dateCharge > 0) {
        return dateCharge.toString();
      }
    }

    // Fallback to "0" if no charge found
    return "0";
  }

  String _getCoachTime(ClassModel classModel) {
    if (classModel.dates != null && classModel.dates!.isNotEmpty) {
      final duration = classModel.dates![0].durationMinutes;
      if (duration != null && duration.toString().isNotEmpty) {
        if (int.tryParse(duration.toString()) != null) {
          return "${duration}min";
        }
        return duration.toString();
      }
    }
    return "45min";
  }
}
