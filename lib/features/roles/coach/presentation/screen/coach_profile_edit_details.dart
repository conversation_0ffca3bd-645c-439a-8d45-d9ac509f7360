import 'package:class_z/core/imports.dart';

class CoachProfileEditDetails extends StatefulWidget {
  final String coachId;
  const CoachProfileEditDetails({required this.coachId, super.key});

  @override
  State<CoachProfileEditDetails> createState() =>
      _CoachProfileEditDetailsState();
}

class _CoachProfileEditDetailsState extends State<CoachProfileEditDetails> {
  final displaynameController = TextEditingController();
  final fullnameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  File? _selectedImage; // Variable to hold the selected image
  CoachModel? _currentCoach; // Store current coach data
  bool _isDataLoaded = false; // Track if data has been loaded

  @override
  void initState() {
    super.initState();
    _fetchCoachData();
  }

  void _fetchCoachData() {
    final coachBloc = context.read<CoachBloc>();
    coachBloc.add(GetCoachInfoByIdEvent(coachId: widget.coachId));
  }

  void _populateFields(CoachModel coach) {
    setState(() {
      _currentCoach = coach;
      displaynameController.text = coach.displayName ?? '';
      fullnameController.text = coach.legalName ?? '';
      _isDataLoaded = true;
    });
  }

  @override
  void dispose() {
    displaynameController.dispose();
    fullnameController.dispose();
    // TODO: implement dispose
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final ImagePicker _picker = ImagePicker();
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80, // Reduce quality to ensure smaller file size
      );

      if (pickedFile != null) {
        print("Image picked: ${pickedFile.path}");

        // Create a File object from the XFile
        final File imageFile = File(pickedFile.path);

        // Check if file exists and is readable
        if (!imageFile.existsSync()) {
          print("Error: Selected image file does not exist");
          errorState(context: context, error: 'Selected image not found');
          return;
        }

        // Check file size
        final fileSize = await imageFile.length();
        print("Image file size: ${fileSize ~/ 1024} KB");

        // If file is too large (>5MB), show error
        if (fileSize > 5 * 1024 * 1024) {
          print(
              "Error: Image file is too large (${fileSize ~/ 1024 ~/ 1024} MB)");
          errorState(context: context, error: 'Image is too large (max 5MB)');
          return;
        }

        setState(() {
          _selectedImage = imageFile;
          print("Image set to state: ${_selectedImage!.path}");
        });
      }
    } catch (e) {
      print("Error picking image: $e");
      errorState(context: context, error: 'Failed to select image: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final coachBloc = context.read<CoachBloc>();

    return BlocListener(
        bloc: coachBloc,
        listener: (context, state) {
          if (state is LoadingCoachState) {
            loadingState(context: context);
          } else
            hideLoadingDialog(context);
          if (state is ErrorCoachState) {
            errorState(context: context, error: state.message);
          }
          if (state is CoachUpdateSuccessState) {
            NavigatorService.goBack();
          }
          if (state is GetCoachInfoByIdSuccessState && state.coach != null) {
            _populateFields(state.coach!);
          }
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: CustomAppBar(
            title: "Coach Details",
            leading: customBackButton(),
            actions: [
              Padding(
                padding: EdgeInsets.only(
                    right: 20.w), // Add some padding to the right
                child: InkWell(
                  onTap: () {
                    if (fullnameController.text.isEmpty &&
                        displaynameController.text.isEmpty &&
                        _selectedImage == null) {
                      errorState(context: context, error: 'Nothing to Update');
                    } else {
                      // //   if (_formKey.currentState?.validate() ?? false) {
                      // // Form is valid, proceed with saving

                      coachBloc
                          .add(CoachUpdateEvent(coachId: widget.coachId, data: {
                        if (displaynameController.text.isNotEmpty)
                          'displayName': displaynameController.text,
                        if (fullnameController.text.isNotEmpty)
                          'legalName': fullnameController.text,
                        if (_selectedImage != null) 'mainImage': _selectedImage
                      }));
                    }
                    //    } else {
                    // Show an error if the form is invalid

                    //  }
                  },
                  child: Center(
                      child: customtext(
                          context: context,
                          newYear: "save",
                          font: 17.sp,
                          weight: FontWeight.w500,
                          color: AppPallete.change)),
                ), // Text on the right
              ),
            ],
          ),
          body: SingleChildScrollView(
            child: Form(
              key: _formKey, // Wrap the form fields with Form widget
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _centerImage(),
                  Padding(
                    padding:
                        EdgeInsets.only(top: 23.h, left: 21.w, right: 21.w),
                    child: _customForm(context: context),
                  )
                ],
              ),
            ),
          ),
        ));
  }

  Widget _centerImage() {
    return SizedBox(
      height: 125.w,
      child: Stack(
        children: [
          Positioned(
              left: 0,
              top: 0,
              right: 0,
              child: Align(
                alignment: Alignment.center,
                child: _selectedImage != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(99.r),
                        child: Image.file(
                          _selectedImage!,
                          height: 125.h,
                          width: 125.r,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            print("Error displaying selected image: $error");
                            return _buildExistingOrDefaultImage();
                          },
                        ),
                      )
                    : _buildExistingOrDefaultImage(),
              )),
          Positioned(
              top: 97.h,
              right: 0,
              left: 110.w,
              child: Align(
                alignment: Alignment.center,
                child: CustomIconButton(
                  icon: Icons.camera_alt_rounded,
                  onPressed: _pickImage,
                  height: 28.55.h,
                  width: 32.41.w,
                  color: AppPallete.darkGrey,
                ),
              ))
        ],
      ),
    );
  }

  Widget _buildExistingOrDefaultImage() {
    // Show existing coach image if available, otherwise show empty container
    if (_currentCoach?.mainImage?.url != null &&
        _currentCoach!.mainImage!.url!.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(99.r),
        child: Image.network(
          _currentCoach!.mainImage!.url!,
          height: 125.h,
          width: 125.r,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              height: 125.h,
              width: 125.r,
              decoration: BoxDecoration(
                color: AppPallete.paleGrey,
                borderRadius: BorderRadius.circular(99.r),
              ),
            );
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              height: 125.h,
              width: 125.r,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(99.r),
              ),
              child: const Center(child: CircularProgressIndicator()),
            );
          },
        ),
      );
    } else {
      return Container(
        height: 125.h,
        width: 125.r,
        decoration: BoxDecoration(
          color: AppPallete.paleGrey,
          borderRadius: BorderRadius.circular(99.r),
        ),
      );
    }
  }

  Widget _customForm({required BuildContext context}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customtext(
            context: context,
            newYear: "Coach information",
            font: 17.sp,
            weight: FontWeight.w500),
        SizedBox(
          height: 20.h,
        ),
        _textbox(
          context: context,
          title: "Full name",
          controller: fullnameController,
          valid: true,
        ),
        SizedBox(
          height: 28.h,
        ),
        _textbox(
          context: context,
          title: "Display name",
          controller: displaynameController,
          valid: true,
        ),
      ],
    );
  }

  Widget _textbox({
    required BuildContext context,
    required String title,
    double? width,
    double? height,
    required bool valid,
    required TextEditingController controller,
  }) {
    // Define the validator function based on the `valid` flag
    String? Function(String?)? validator;

    if (valid == true) {
      validator = (value) {
        if (value == null || value.isEmpty) {
          return 'This field is required';
        }
        return null;
      };
    } else {
      validator = null; // No validation if `valid` is not true
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
            context: context,
            title: title,
            font: 15.sp,
            weight: FontWeight.w400),
        SizedBox(
          height: 20.h,
        ),
        AuthField(
          controller: controller,
          height: height ?? 30.h,
          width: width,
          color: AppPallete.paleGrey,
          validator: validator, // Pass the validator to AuthField
        ),
      ],
    );
  }
}
