import 'package:class_z/core/imports.dart';

class CoachProfileEdit extends StatelessWidget {
  final String coachId;

  const CoachProfileEdit({super.key, required this.coachId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "Coach Profile",
        leading: customBackButton(),
      ),
      body: Padding(
        padding: EdgeInsets.only(top: 44.h, left: 20.w, right: 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _container(
                context: context,
                title: "Personal details",
                onTap: () {
                  NavigatorService.pushNamed(
                    AppRoutes.coachProfileEditDetails,
                    arguments: coachId,
                  );
                }),
            SizedBox(
              height: 20.h,
            ),
            _container(
                context: context,
                title: "Personal description",
                onTap: () {
                  NavigatorService.pushNamed(
                    AppRoutes.coachProfileEditDescription,
                    arguments: coachId,
                  );
                }),
            SizedBox(
              height: 20.h,
            ),
            _container(
                context: context,
                title: "Skills",
                onTap: () {
                  NavigatorService.pushNamed(
                    AppRoutes.coachProfileEditSkills,
                    arguments: coachId,
                  );
                }),
            SizedBox(
              height: 20.h,
            ),
            _container(
                context: context,
                title: "Accreditation",
                onTap: () {
                  NavigatorService.pushNamed(
                    AppRoutes.coachProfileEditAccredation,
                    arguments: coachId,
                  );
                }),
            SizedBox(
              height: 20.h,
            ),
            _container(
                context: context,
                title: "Experience",
                onTap: () {
                  NavigatorService.pushNamed(
                    AppRoutes.coachProfileEditExperience,
                    arguments: coachId,
                  );
                }),
          ],
        ),
      ),
    );
  }

  Widget _container(
      {required BuildContext context,
      required String title,
      required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 60.h,
        padding: EdgeInsets.only(left: 12.w, right: 18.w),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.r),
            boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            customtext(
                context: context,
                newYear: title,
                font: 17.sp,
                weight: FontWeight.w500),
            Center(
              child: CustomIconButton(
                icon: Icons.arrow_forward_ios,
                onPressed: onTap,
                color: Colors.black,
                height: 24.h,
                width: 24.w,
              ),
            )
          ],
        ),
      ),
    );
  }
}
