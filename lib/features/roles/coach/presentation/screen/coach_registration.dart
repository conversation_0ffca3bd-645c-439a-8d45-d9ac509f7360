import 'package:class_z/core/imports.dart';

class CoachRegistration extends StatefulWidget {
  const CoachRegistration({super.key});

  @override
  State<CoachRegistration> createState() => _CoachRegistrationState();
}

class _CoachRegistrationState extends State<CoachRegistration> {
  final formKey = GlobalKey<FormState>();

  final displaynameController = TextEditingController();
  final legalnameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final referalController = TextEditingController();
  final locationController = TextEditingController();
  final labelController = TextEditingController();
  final hkidMainController = TextEditingController();
  final hkidCheckDigitController = TextEditingController();

  final List<String> countryCodes = ['+852', '+99'];
  final List<String> locations = ['Hong Kong', 'BanglaDesh'];

  final ImagePicker _picker = ImagePicker();
  XFile? _image;
  XFile? _hkidImage;

  @override
  void initState() {
    super.initState();
    _checkAuthentication();
  }

  void _checkAuthentication() {
    final sharedRepo = locator<SharedRepository>();
    final token = sharedRepo.getToken();
    final userData = sharedRepo.getUserData();
    final baseUserId = sharedRepo.getBaseUserId();

    print("Authentication check:");
    print("Token: ${token?.isNotEmpty == true ? 'Present' : 'Missing'}");
    print("User data: ${userData != null ? 'Present' : 'Missing'}");
    print("Base user ID: ${baseUserId.isNotEmpty ? baseUserId : 'Missing'}");

    // Check if user is authenticated
    if (token == null ||
        token.isEmpty ||
        userData == null ||
        baseUserId.isEmpty) {
      print("User is not properly authenticated. Redirecting to login.");

      // Show error and redirect to login
      WidgetsBinding.instance.addPostFrameCallback((_) {
        errorState(
            context: context, error: 'Please log in to create a coach profile');

        // Redirect to login after a short delay
        Future.delayed(Duration(seconds: 2), () {
          NavigatorService.pushNamedAndRemoveUntil(AppRoutes.logIn);
        });
      });
      return;
    }

    print(
        "Authentication check passed. User can proceed with coach registration.");
  }

  @override
  void dispose() {
    displaynameController.dispose();
    legalnameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    referalController.dispose();
    locationController.dispose();
    labelController.dispose();
    hkidMainController.dispose();
    hkidCheckDigitController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final pickedImage = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedImage != null) {
      setState(() => _image = pickedImage);
    }
  }

  Future<void> _pickHKIDImage() async {
    final pickedHKID = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedHKID != null) {
      setState(() => _hkidImage = pickedHKID);
    }
  }

  void _submitForm() {
    // Double-check authentication before submitting
    final sharedRepo = locator<SharedRepository>();
    final token = sharedRepo.getToken();
    final baseUserId = sharedRepo.getBaseUserId();

    if (token == null || token.isEmpty || baseUserId.isEmpty) {
      errorState(
          context: context,
          error: 'Authentication required. Please log in and try again.');
      NavigatorService.pushNamedAndRemoveUntil(AppRoutes.logIn);
      return;
    }

    if (formKey.currentState!.validate()) {
      final payload = {
        "fullName": legalnameController.text,
        "displayName": displaynameController.text,
        "phoneNumber": phoneController.text,
        "hkid": "${hkidMainController.text}(${hkidCheckDigitController.text})",
        "isCoach": true,
        "baseUser": baseUserId, // Use the validated baseUserId
      };

      if (_hkidImage != null) {
        payload["hkidCard"] = File(_hkidImage!.path);
      }

      if (_image != null) {
        payload["mainImage"] = File(_image!.path);
      }

      print("Submitting coach registration with payload: $payload");
      print("Using baseUser ID: $baseUserId");
      print("Using token: ${token.substring(0, 10)}...");

      // Dispatch the create event to the CoachBloc
      context.read<CoachBloc>().add(CoachCreateEvent(data: payload));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBarDouble(
        title: "Registration",
        title2: "Coach",
        leading: CustomIconButton(
          icon: Icons.arrow_back_ios,
          onPressed: () => NavigatorService.goBack(),
        ),
      ),
      body: BlocListener<CoachBloc, CoachState>(
        listener: (context, state) {
          if (state is LoadingCoachState) {
            loadingState(context: context);
          } else {
            hideLoadingDialog(context);
          }

          if (state is CoachCreateSuccessState) {
            if (state.success == true)
              NavigatorService.pushNamed(AppRoutes.openProfile);
            else
              errorState(context: context, error: 'please try again');
          } else if (state is ErrorCoachState) {
            errorState(context: context, error: state.message);
          }
        },
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 21.w),
            child: Form(
              key: formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionTitle("Your Information"),
                  _buildTextFieldWithLabel(
                      "Legal Name", legalnameController, "legal name"),
                  _buildTextFieldWithLabel(
                      "Display Name", displaynameController, "display name"),
                  buildStaticEmail(context,
                      locator<SharedRepository>().getCoachData()?.email ?? ''),
                  _buildPhoneNumberFields(),
                  _buildHKIDLabel(),
                  _buildHKIDCARDIMAGE(),
                  const SizedBox(height: 72),
                  Button(
                    buttonText: 'Create',
                    color: AppPallete.buttonColor,
                    onPressed: () {
                      _submitForm();
                    },
                  ),
                  SizedBox(height: 20.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileImage() {
    return SizedBox(
      height: 125.h,
      child: Stack(
        children: [
          PositionedItemWidget(
            top: 65.h,
            left: 0,
            right: 0,
            child: const Divider(height: 12, color: Colors.grey),
          ),
          PositionedItemWidget(
            top: 0.h,
            left: 0,
            right: 0.w,
            child: Center(
              child: Container(
                height: 125.h,
                width: 125.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(125.w),
                  color: AppPallete.paleGrey,
                ),
                child: _image != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(125.w),
                        child: Image.file(
                          File(_image!.path),
                          height: 125.h,
                          width: 125.w,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Container(),
              ),
            ),
          ),
          PositionedItemWidget(
            left: 90.w,
            bottom: 0.h,
            child: CustomIconButton(
              color: AppPallete.greyColor,
              icon: Icons.camera_alt,
              height: 29.h,
              width: 33.w,
              onPressed: _pickImage,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.only(top: 22.h, bottom: 20.h),
      child: customtext(
        context: context,
        newYear: title,
        font: 17.sp,
        weight: FontWeight.w500,
      ),
    );
  }

  Widget _buildTextFieldWithLabel(
      String label, TextEditingController controller, String hint) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
          context: context,
          title: label,
          font: 17.sp,
          weight: FontWeight.w500,
        ),
        SizedBox(height: 20.h),
        AuthField(
          hintText: hint,
          controller: controller,
          width: 387.w,
          height: 30.h,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '$label is required';
            }
            return null;
          },
        ),
        SizedBox(height: 20.h),
      ],
    );
  }

  Widget _buildPhoneNumberFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
          context: context,
          title: 'Phone Number',
          font: 17.sp,
          weight: FontWeight.w500,
        ),
        SizedBox(height: 20.h),
        Row(
          children: [
            SizedBox(
              width: 65,
              child: DropDown(
                label: "+852",
                times: countryCodes,
                color: AppPallete.paleGrey,
                controller: labelController,
                validator: (value) => value == null || value.isEmpty
                    ? 'Country Code is required'
                    : null,
              ),
            ),
            SizedBox(width: 6.w),
            Expanded(
              child: AuthField(
                hintText: "Phone Number",
                controller: phoneController,
                keyboard: TextInputType.number,
                height: 30.h,
                validator: (value) => value == null || value.isEmpty
                    ? 'Phone number is required'
                    : null,
              ),
            ),
          ],
        ),
        SizedBox(height: 20.h),
      ],
    );
  }

  Widget _buildHKIDLabel() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
          context: context,
          title: "HKID",
          font: 17.sp,
          weight: FontWeight.w500,
        ),
        const SizedBox(height: 20),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: AuthField(
                hintText: "e.g. A123456",
                controller: hkidMainController,
                width: 260.w,
                height: 30.h,
                validator: _validateHKIDMain,
              ),
            ),
            SizedBox(width: 10.w),
            Text(
              "(",
              style: TextStyle(fontSize: 18.sp),
            ),
            SizedBox(width: 5.w),
            AuthField(
              hintText: "7",
              controller: hkidCheckDigitController,
              width: 40.w,
              height: 30.h,
              validator: _validateHKIDCheckDigit,
            ),
            SizedBox(width: 5.w),
            Text(
              ")",
              style: TextStyle(fontSize: 18.sp),
            ),
          ],
        ),
        SizedBox(height: 20),
      ],
    );
  }

  String? _validateHKIDMain(String? value) {
    print(value);
    final regex = RegExp(r'^[A-Z]{1,2}[0-9]{6}$');
    if (value == null || value.isEmpty) {
      return 'Main HKID is required';
    } else if (!regex.hasMatch(value)) {
      return 'Invalid HKID format';
    }
    return null;
  }

  String? _validateHKIDCheckDigit(String? value) {
    final regex = RegExp(r'^[0-9A]$');
    if (value == null || value.isEmpty) {
      return 'Check digit required';
    } else if (!regex.hasMatch(value)) {
      return 'Invalid check digit';
    }
    return null;
  }

  Widget _buildHKIDCARDIMAGE() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
          context: context,
          title: 'Please upload your HKID card',
          font: 17.sp,
          weight: FontWeight.w500,
        ),
        const SizedBox(height: 20),
        InkWell(
          onTap: _pickHKIDImage,
          child: Container(
            color: AppPallete.paleGrey,
            height: 103,
            width: 107,
            child: _hkidImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child:
                        Image.file(File(_hkidImage!.path), fit: BoxFit.cover))
                : customSvgPicture(
                    imagePath: ImagePath.plusSvg,
                    height: 103,
                    width: 107,
                    color: AppPallete.white,
                  ),
          ),
        ),
      ],
    );
  }
}
