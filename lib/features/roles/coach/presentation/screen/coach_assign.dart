import 'package:class_z/core/imports.dart';

void showAlertDialogWithAcceptReject(
    BuildContext context, Map<String, dynamic> data,
    {String? id}) {
  print(data);
  print(id);
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return MultiBlocListener(
        listeners: [
          BlocListener<CoachBloc, CoachState>(
            listener: (context, state) {
              if (state is LoadingCoachState) {
                loadingState(context: context);
              } else {
                hideLoadingDialog(context);
              }

              if (state is ErrorCoachState) {
                NavigatorService.goBack();
                errorState(context: context, error: state.message);
                if (state.message
                    .contains('Coach already assigned to this center')) {
                  context.read<NotificationBloc>().add(DeleteNotificationEvent(
                      notificationId: id ?? data['notificationId'] ?? ''));
                }
              }

              if (state is AssignCenterSuccessState) {
                errorState(context: context, error: 'You have been assigned');
                NavigatorService.goBack();
              }
            },
          ),
          BlocListener<NotificationBloc, NotificationState>(
            listener: (context, state) {
              if (state is NotificationLoading)
                loadingState(context: context);
              else
                hideLoadingDialog(context);
              if (state is NotificationError)
                errorState(context: context, error: state.message);
              if (state is NotificationDeleted) {
                errorState(context: context, error: 'Data deleted');
              }
            },
          ),
        ],
        child: AlertDialog(
          backgroundColor: Colors.white,

          // title: Text('Confirmation'),
          // content: Text('Do you want to accept or reject?'),

          actions: <Widget>[
            const SizedBox(height: 20),
            _centerCard(context, data),
            const SizedBox(height: 20),
            _showText(context, data),
            customtext(
                context: context,
                textAlign: TextAlign.center,
                newYear:
                    'Please take attendance, and provide learning feedback after each session',
                font: 12.sp,
                fontWeight: FontWeight.w300,
                color: AppPallete.greyWord),
            const SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 41.w),
              child: Row(
                children: [
                  Expanded(
                    child: Button(
                        height: 40.h,
                        buttonText: "Accept",
                        color: AppPallete.secondaryColor,
                        onPressed: () {
                          print(
                              'Accepting request with notification id: $id and data: $data');
                          context.read<CoachBloc>().add(AssignCenterEvent(
                              centerId: data['centerId'],
                              coachId: locator<SharedRepository>().getCoachId(),
                              type: data['status'] ?? 'coach',
                              notificationId:
                                  id ?? data['notificationId'] ?? '',
                              saveData: true));
                          // Navigator.of(context).pop();
                        }),
                  ),
                  SizedBox(
                    width: 24,
                  ),
                  Expanded(
                    child: Button(
                        height: 40.h,
                        buttonText: "Reject",
                        color: AppPallete.greyWord,
                        textColorFinal: AppPallete.white,
                        onPressed: () {
                          context.read<NotificationBloc>().add(
                              DeleteNotificationEvent(
                                  notificationId:
                                      id ?? data['notificationId'] ?? ''));
                        }),
                  ),
                ],
              ),
            )
          ],
        ),
      );
    },
  );
}

Widget _centerCard(BuildContext context, Map<String, dynamic> data) {
  return Container(
    padding: EdgeInsets.all(20),
    decoration: BoxDecoration(
        color: AppPallete.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
    child: Row(
      children: [
        CustomImageBuilder(
          borderRadius: 20,
          imagePath: imageStringGenerator(imagePath: data['centerMainImage']),
          width: 88.w,
          height: 88.h,
        ),
        SizedBox(width: 20.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            customtext(
                context: context,
                newYear: data['centerDisplayName'],
                color: AppPallete.secondaryColor,
                fontWeight: FontWeight.w700,
                font: 16.sp),
            textWithSvg(
                context: context,
                title: data['centerAddress'],
                imagePath: ImagePath.locationSvg,
                font: 15.sp,
                weight: FontWeight.w400),
            customtext(
                context: context,
                newYear: "Example Center",
                fontWeight: FontWeight.w500,
                font: 15.sp),
          ],
        )
      ],
    ),
  );
}

Widget _showText(BuildContext context, Map<String, dynamic> data) {
  String title = '';
  if (data['status'] == 'manager')
    title = 'Manager';
  else
    title = 'Coach';
  return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(children: [
        customSpanText(
            text: "You have been assigned as a ",
            fontSize: 16.sp,
            fontWeight: FontWeight.w400),
        customSpanText(
            text: title, fontSize: 16.sp, fontWeight: FontWeight.w600),
        customSpanText(
          text: " for the above centre",
          fontSize: 16.sp,
          fontWeight: FontWeight.w400,
        ),
      ]));
}
