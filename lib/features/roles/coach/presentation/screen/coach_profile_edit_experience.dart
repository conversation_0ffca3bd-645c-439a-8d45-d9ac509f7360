import 'package:class_z/core/imports.dart';

class CoachProfileEditExperience extends StatefulWidget {
  final String coachId;
  const CoachProfileEditExperience({required this.coachId, super.key});

  @override
  State<CoachProfileEditExperience> createState() => _CoachProfileEditExperienceState();
}

class _CoachProfileEditExperienceState extends State<CoachProfileEditExperience> {
  final _formKey = GlobalKey<FormState>();

  List<Map<String, TextEditingController>> experienceControllers = [];

  List<String> past20Years =
      List.generate(20, (index) => (DateTime.now().year - index).toString());

  @override
  void dispose() {
    for (var item in experienceControllers) {
      item['title']?.dispose();
      item['organization']?.dispose();
      item['from']?.dispose();
      item['to']?.dispose();
    }
    super.dispose();
  }

  void _addExperienceForm() {
    setState(() {
      experienceControllers.add({
        'title': TextEditingController(),
        'organization': TextEditingController(),
        'from': TextEditingController(),
        'to': TextEditingController(),
      });
    });
  }

  void _submitExperience() {
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please fill all required fields.")),
      );
      return;
    }

    List<Map<String, dynamic>> experiences = experienceControllers.map((entry) {
      return {
        'title': entry['title']!.text,
        'organization': entry['organization']!.text,
        'from': entry['from']!.text,
        'to': entry['to']!.text,
      };
    }).toList();

    if (experiences.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please add at least one experience.")),
      );
      return;
    }
    print(experiences);

    // Send experience data to the backend or the Bloc
    BlocProvider.of<CoachBloc>(context).add(
      CoachUpdateEvent(
        coachId: widget.coachId,
        data: {'experience': experiences},
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final coachBloc = BlocProvider.of<CoachBloc>(context);

    return BlocListener<CoachBloc, CoachState>(
      bloc: coachBloc,
      listener: (context, state) {
        if (state is LoadingCoachState) {
          loadingState(context: context);
        } else {
          hideLoadingDialog(context);
        }

        if (state is ErrorCoachState) {
          errorState(context: context, error: state.message);
        }

        if (state is CoachUpdateSuccessState) {
          errorState(context: context, error: 'Update done');
          NavigatorService.goBack();
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: CustomAppBar(
          title: "Experience",
          leading: customBackButton(),
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 20.w),
              child: GestureDetector(
                onTap: _submitExperience,
                child: Center(
                  child: customtext(
                    context: context,
                    newYear: "save",
                    font: 17.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.change,
                  ),
                ),
              ),
            ),
          ],
        ),
        body: Padding(
          padding: EdgeInsets.all(20.w),
          child: Form(
            key: _formKey,
            child: ListView(
              children: [
                customtext(
                  context: context,
                  newYear: "Experience",
                  font: 17.sp,
                  weight: FontWeight.w500,
                ),
                SizedBox(height: 20.h),
                ...List.generate(experienceControllers.length, (index) {
                  final controllers = experienceControllers[index];
                  return Padding(
                    padding: EdgeInsets.only(bottom: 30.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        customRequiredText(
                          context: context,
                          title: "Experience Title",
                          font: 14.sp,
                          weight: FontWeight.w400,
                        ),
                        SizedBox(height: 10.h),
                        AuthField(
                          controller: controllers['title']!,
                          height: 30.h,
                          validator: (value) => value!.isEmpty ? 'Required' : null,
                        ),
                        SizedBox(height: 20.h),
                        customRequiredText(
                          context: context,
                          title: "Organization",
                          font: 14.sp,
                          weight: FontWeight.w400,
                        ),
                        SizedBox(height: 10.h),
                        AuthField(
                          controller: controllers['organization']!,
                          height: 30.h,
                          validator: (value) => value!.isEmpty ? 'Required' : null,
                        ),
                        SizedBox(height: 20.h),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  customRequiredText(
                                    context: context,
                                    title: "From Year",
                                    font: 14.sp,
                                    weight: FontWeight.w400,
                                  ),
                                  SizedBox(height: 10.h),
                                  DropDown(
                                    height: 30.h,
                                    label: "From",
                                    controller: controllers['from'],
                                    times: past20Years,
                                    color: AppPallete.inputBox,
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 18.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  customRequiredText(
                                    context: context,
                                    title: "To Year",
                                    font: 14.sp,
                                    weight: FontWeight.w400,
                                  ),
                                  SizedBox(height: 10.h),
                                  DropDown(
                                    height: 30.h,
                                    label: "To",
                                    controller: controllers['to'],
                                    times: past20Years,
                                    color: AppPallete.inputBox,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  );
                }),
                SizedBox(height: 20.h),
                Center(
                  child: addNewOnProfile(
                    context: context,
                    title: "Add Experience",
                    onTap: _addExperienceForm,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
