import 'package:class_z/core/imports.dart';

class CoachProfileEditDescription extends StatefulWidget {
  final String coachId;
  const CoachProfileEditDescription({required this.coachId, super.key});

  @override
  State<CoachProfileEditDescription> createState() =>
      _CoachProfileEditDescriptionState();
}

class _CoachProfileEditDescriptionState
    extends State<CoachProfileEditDescription> {
  final descriptionController = TextEditingController();
  List<String> selectedLanguages = [];
  List<File>? photos = [];
  CoachModel? _currentCoach; // Store current coach data
  bool _isDataLoaded = false; // Track if data has been loaded

  @override
  void initState() {
    super.initState();
    _fetchCoachData();
  }

  void _fetchCoachData() {
    print(
        "🔄 [COACH_DESCRIPTION] _fetchCoachData called for coachId: ${widget.coachId}");
    final coachBloc = context.read<CoachBloc>();
    print(
        "🚀 [COACH_DESCRIPTION] Dispatching GetCoachInfoByIdEvent for ID: ${widget.coachId}");
    coachBloc.add(GetCoachInfoByIdEvent(coachId: widget.coachId));
  }

  void _populateFields(CoachModel coach) {
    print("📝 [COACH_DESCRIPTION] _populateFields called");
    print(
        "📊 [COACH_DESCRIPTION] Coach data: description='${coach.description}', languages=${coach.languages}");

    setState(() {
      _currentCoach = coach;
      descriptionController.text = coach.description ?? '';
      selectedLanguages = List<String>.from(coach.languages ?? []);
      // Note: photos from server (coach.images) are URLs, not Files
      // We'll keep the photos list empty for new uploads
      _isDataLoaded = true;
    });

    print(
        "✅ [COACH_DESCRIPTION] Fields populated: description='${descriptionController.text}', languages=$selectedLanguages");
  }

  @override
  void dispose() {
    descriptionController.dispose();
    super.dispose();
  }

  void _handleLanguagesChanged(List<String> languages) {
    setState(() {
      selectedLanguages = languages;
    });
  }

  @override
  Widget build(BuildContext context) {
    final coachBloc = BlocProvider.of<CoachBloc>(context);
    return BlocConsumer(
      bloc: coachBloc,
      listener: (context, state) {
        if (state is LoadingCoachState) {
          loadingState(context: context);
        } else
          hideLoadingDialog(context);
        if (state is ErrorCoachState) {
          errorState(context: context, error: state.message);
        }
        if (state is CoachUpdateSuccessState) {
          errorState(context: context, error: 'Update Done');
          NavigatorService.goBack();
        }
        if (state is GetCoachInfoByIdSuccessState && state.coach != null) {
          print(
              "🎉 [COACH_DESCRIPTION] GetCoachInfoByIdSuccessState received!");
          print(
              "📦 [COACH_DESCRIPTION] Coach data from API: ${state.coach!.toString()}");
          _populateFields(state.coach!);
        }
      },
      builder: (context, state) {
        return Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: CustomAppBar(
            title: "Coach Profile",
            leading: customBackButton(),
            actions: [
              Padding(
                padding: EdgeInsets.only(
                    right: 20.w), // Add some padding to the right
                child: GestureDetector(
                  onTap: () {
                    if (descriptionController.text.isEmpty &&
                        selectedLanguages.length == 0 &&
                        photos?.length == 0)
                      errorState(context: context, error: 'Nothing to Update');
                    else {
                      Map<String, dynamic> data = {
                        if (descriptionController.text.isNotEmpty)
                          'description': descriptionController.text,
                        if (selectedLanguages.length > 0)
                          'languages': selectedLanguages,
                        if (photos!.length > 0) 'images': photos
                      };
                      coachBloc.add(CoachUpdateEvent(
                          coachId: widget.coachId, data: data));
                    }
                    // final String description = descriptionController.text;
                    // coachBloc.add(CoachUpdateEvent(
                    //     description: description,
                    //     languages: selectedLanguages));
                  },
                  child: Center(
                    child: Text(
                      "save",
                      style: TextStyle(
                          color: AppPallete.change,
                          fontSize: 17.sp,
                          fontWeight: FontWeight.w400),
                    ),
                  ),
                ), // Text on the right
              ),
            ],
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.only(left: 21.w, right: 21.w, top: 47.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  customRequiredText(
                      context: context,
                      title: "Teaching language",
                      font: 17.sp,
                      weight: FontWeight.w500),
                  SizedBox(
                    height: 20.h,
                  ),
                  LanguageSelector(
                      selectedLanguages: selectedLanguages,
                      onChanged: _handleLanguagesChanged),
                  SizedBox(
                    height: 20.h,
                  ),
                  customRequiredText(
                      context: context,
                      title: "Description",
                      font: 17.sp,
                      weight: FontWeight.w500),
                  SizedBox(
                    height: 20.h,
                  ),
                  AuthField(
                    controller: descriptionController,
                    height: 132.h,
                    hintText: "No more than 250 words",
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  customtext(
                      context: context,
                      newYear: "Class Photo(if applicable)",
                      font: 17.sp,
                      weight: FontWeight.w500),
                  SizedBox(
                    height: 20.h,
                  ),
                  customRequiredText(
                      context: context,
                      title: "Album (up to 10 photos)",
                      font: 15.sp,
                      weight: FontWeight.w500),
                  SizedBox(
                    height: 20.h,
                  ),
                  AlbumCard(
                    onImagesSelected: (images) {
                      photos = images;
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
