import 'package:class_z/core/imports.dart';

class CoachMyProfile extends StatelessWidget {
  CoachMyProfile({super.key});
  late final CoachModel coachData;
  late final UserModel? userData;
  @override
  Widget build(BuildContext context) {
    final sharedRepository = Provider.of<SharedRepository>(context);

    userData = sharedRepository.getUserData();
    coachData = userData!.data!.coach!;
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
              padding: EdgeInsets.only(left: 19.w, top: 85.h),
              child: customBackButton()),
          Padding(
            padding: EdgeInsets.only(top: 13.w, left: 20.w),
            child: SizedBox(
              height: 36.h,
              child: customtext(
                  context: context,
                  newYear: "Coach Profile",
                  font: 30.sp,
                  weight: FontWeight.w500),
            ),
          ),
          SizedBox(
            height: 35.h,
          ),
          Padding(
            padding: EdgeInsets.only(top: 28.h, left: 37.w, right: 38.w),
            child: _profile(context: context),
          )
        ],
      ),
    );
  }

  Widget _profile({required BuildContext context}) {
    // String date = centerModel.centre?.createdAt != null
    //     ? DateFormat('dd/MM/yyyy').format(centerModel.centre!.createdAt!)
    //     : "N/A";
    return Container(
      height: 303.h,
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            shadow(blurRadius: 15, opacity: 0.1, xoffset: 0, yoffset: 0),
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 150.h,
            child: Stack(
              children: [
                Positioned(
                    top: 22.h,
                    right: 19.w,
                    child: GestureDetector(
                      onTap: () {
                        if (coachData.id != null) {
                          NavigatorService.pushNamed(
                            AppRoutes.coachProfileEdit,
                            arguments: coachData.id!,
                          );
                        } else {
                          // Handle case where coach ID is not available
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content:
                                    Text('Unable to load coach information')),
                          );
                        }
                      },
                      child: Text(
                        "edit",
                        style: TextStyle(
                            color: AppPallete.change,
                            fontSize: 17.sp,
                            decoration: TextDecoration.underline,
                            fontWeight: FontWeight.w400),
                      ),
                    )),
                Positioned(
                    top: 22.h,
                    left: 0.w,
                    right: 0,
                    child: Align(
                      alignment: Alignment.center,
                      child: CustomImageBuilder(
                          imagePath:
                              "${AppText.device}${coachData.mainImage?.url}",
                          height: 125.h,
                          width: 125.w,
                          borderRadius: 99.r),
                    )),
              ],
            ),
          ),
          SizedBox(
            height: 31.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 14.w, right: 17.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customtext(
                    context: context,
                    newYear: "Coach Information",
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.darkGrey),
                customtext(
                    context: context,
                    newYear: coachData.classZId ?? '',
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.darkGrey)
              ],
            ),
          ),
          SizedBox(
            height: 26.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 14.w, right: 17.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customtext(
                    context: context,
                    newYear: coachData.displayName!,
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.darkGrey),
                customtext(
                    context: context,
                    newYear: coachData.address?.address1 ?? "",
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.darkGrey)
              ],
            ),
          ),
          SizedBox(
            height: 26.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 14.w, right: 17.w),
            child: customtext(
                context: context,
                newYear: "Joined Since ${userData?.data?.coach?.createdAt}",
                font: 15.sp,
                weight: FontWeight.w500,
                color: AppPallete.darkGrey),
          ),
        ],
      ),
    );
  }
}
