import 'package:class_z/core/imports.dart';

class CoachProfileEditSkills extends StatefulWidget {
  final String coachId;
  const CoachProfileEditSkills({required this.coachId, super.key});

  @override
  State<CoachProfileEditSkills> createState() => _CoachProfileEditSkillsState();
}

class _CoachProfileEditSkillsState extends State<CoachProfileEditSkills> {
  final _formKey = GlobalKey<FormState>();

  List<Map<String, TextEditingController>> skillControllers = [];

  List<String> past20Years =
      List.generate(20, (index) => (DateTime.now().year - index).toString());

  CoachModel? _currentCoach; // Store current coach data
  bool _isDataLoaded = false; // Track if data has been loaded

  @override
  void initState() {
    super.initState();
    _fetchCoachData();
  }

  void _fetchCoachData() {
    final coachBloc = context.read<CoachBloc>();
    coachBloc.add(GetCoachInfoByIdEvent(coachId: widget.coachId));
  }

  void _populateFields(CoachModel coach) {
    setState(() {
      _currentCoach = coach;

      // Clear existing controllers
      for (var item in skillControllers) {
        item['skillname']?.dispose();
        item['skilllevel']?.dispose();
        item['skillsince']?.dispose();
        item['skillnameofassociation']?.dispose();
      }
      skillControllers.clear();

      // Populate with existing skills
      if (coach.skill != null && coach.skill!.isNotEmpty) {
        for (var skill in coach.skill!) {
          skillControllers.add({
            'skillname': TextEditingController(text: skill.skillname ?? ''),
            'skilllevel': TextEditingController(text: skill.skilllevel ?? ''),
            'skillsince': TextEditingController(text: skill.skillsince ?? ''),
            'skillnameofassociation':
                TextEditingController(text: skill.skillnameofassociation ?? ''),
          });
        }
      } else {
        // Add one empty skill form if no existing skills
        _addSkillForm();
      }

      _isDataLoaded = true;
    });
  }

  @override
  void dispose() {
    for (var item in skillControllers) {
      item['skillname']?.dispose();
      item['skilllevel']?.dispose();
      item['skillsince']?.dispose();
      item['skillnameofassociation']?.dispose();
    }
    super.dispose();
  }

  void _addSkillForm() {
    setState(() {
      skillControllers.add({
        'skillname': TextEditingController(),
        'skilllevel': TextEditingController(),
        'skillsince': TextEditingController(),
        'skillnameofassociation': TextEditingController()
      });
    });
  }

  void _submitSkills() {
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please fill all required fields.")),
      );
      return;
    }

    List<Map<String, dynamic>> skills = skillControllers.map((entry) {
      return {
        'skillname': entry['skillname']!.text,
        'skilllevel': entry['skilllevel']!.text,
        'skillsince': entry['skillsince']!.text,
        'skillnameofassociation': entry['skillnameofassociation']!.text
      };
    }).toList();

    if (skills.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please add at least one skill.")),
      );
      return;
    }
    print(skills);

    // Send skills data to the backend or the Bloc
    BlocProvider.of<CoachBloc>(context).add(
      CoachUpdateEvent(
        coachId: widget.coachId,
        data: {'skill': skills},
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final coachBloc = BlocProvider.of<CoachBloc>(context);

    return BlocListener<CoachBloc, CoachState>(
        bloc: coachBloc,
        listener: (context, state) {
          if (state is LoadingCoachState) {
            loadingState(context: context);
          } else {
            hideLoadingDialog(context);
          }

          if (state is ErrorCoachState) {
            errorState(context: context, error: state.message);
          }

          if (state is CoachUpdateSuccessState) {
            errorState(context: context, error: 'Update done');
            NavigatorService.goBack();
          }

          if (state is GetCoachInfoByIdSuccessState && state.coach != null) {
            _populateFields(state.coach!);
          }
        },
        child: Scaffold(
          resizeToAvoidBottomInset: true,
          appBar: CustomAppBar(
            title: "Skills",
            leading: customBackButton(),
            actions: [
              Padding(
                padding: EdgeInsets.only(right: 20.w),
                child: GestureDetector(
                  onTap: _submitSkills,
                  child: Center(
                    child: customtext(
                      context: context,
                      newYear: "save",
                      font: 17.sp,
                      weight: FontWeight.w500,
                      color: AppPallete.change,
                    ),
                  ),
                ),
              ),
            ],
          ),
          body: Padding(
            padding: EdgeInsets.all(20.w),
            child: Form(
              key: _formKey,
              child: ListView(
                children: [
                  customtext(
                    context: context,
                    newYear: "Skills",
                    font: 17.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(height: 20.h),
                  ...List.generate(skillControllers.length, (index) {
                    final controllers = skillControllers[index];
                    return Padding(
                      padding: EdgeInsets.only(bottom: 30.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          customRequiredText(
                            context: context,
                            title: "Skill Name",
                            font: 14.sp,
                            weight: FontWeight.w400,
                          ),
                          SizedBox(height: 10.h),
                          AuthField(
                            controller: controllers['skillname']!,
                            height: 30.h,
                            validator: (value) =>
                                value!.isEmpty ? 'Required' : null,
                          ),
                          SizedBox(height: 20.h),
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    customRequiredText(
                                      context: context,
                                      title: "Skill Level",
                                      font: 14.sp,
                                      weight: FontWeight.w400,
                                    ),
                                    SizedBox(
                                      height: 20.h,
                                    ),
                                    AuthField(
                                      controller: controllers['skilllevel']!,
                                      height: 30.h,
                                      hintText: "e.g. Intermediate",
                                      validator: (value) =>
                                          value!.isEmpty ? 'Required' : null,
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(width: 18.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    customRequiredText(
                                      context: context,
                                      title: "Skill Since",
                                      font: 14.sp,
                                      weight: FontWeight.w400,
                                    ),
                                    SizedBox(height: 10.h),
                                    DropDown(
                                      height: 30.h,
                                      label: "Year",
                                      controller: controllers['skillsince'],
                                      times: past20Years,
                                      color: AppPallete.inputBox,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 20.h,
                          ),
                          customRequiredText(
                            context: context,
                            title: "Member of Association",
                            font: 15.sp,
                            weight: FontWeight.w400,
                          ),
                          SizedBox(
                            height: 20.h,
                          ),
                          AuthField(
                            controller: controllers['skillnameofassociation']!,
                            hintText:
                                'e.g. Registered member at Play Therapy Hong Kong',
                            height: 30.h,
                            validator: (value) =>
                                value!.isEmpty ? 'Required' : null,
                          ),
                        ],
                      ),
                    );
                  }),
                  SizedBox(height: 20.h),
                  Center(
                    child: addNewOnProfile(
                      context: context,
                      title: "Add Skill",
                      onTap: _addSkillForm,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }
}
