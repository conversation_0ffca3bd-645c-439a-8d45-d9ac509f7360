import 'package:class_z/core/imports.dart';

class CoachProfileEditAccredation extends StatefulWidget {
  final String coachId;
  const CoachProfileEditAccredation({required this.coachId, super.key});

  @override
  State<CoachProfileEditAccredation> createState() =>
      _CoachProfileEditAccredationState();
}

class _CoachProfileEditAccredationState
    extends State<CoachProfileEditAccredation> {
  final _formKey = GlobalKey<FormState>();

  List<Map<String, TextEditingController>> accredationControllers = [];

  List<String> past20Years =
      List.generate(20, (index) => (DateTime.now().year - index).toString());

  @override
  void dispose() {
    for (var item in accredationControllers) {
      item['name']?.dispose();
      item['result']?.dispose();
      item['year']?.dispose();
    }
    super.dispose();
  }

  void _addAccreditationForm() {
    setState(() {
      accredationControllers.add({
        'title': TextEditingController(),
        'result': TextEditingController(),
        'year': TextEditingController(),
      });
    });
  }

  void _submitAccreditation() {
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please fill all required fields.")),
      );
      return;
    }

    List<Map<String, dynamic>> accredations =
        accredationControllers.map((entry) {
      return {
        'title': entry['title']!.text,
        'result': entry['result']!.text,
        'year': entry['year']!.text,
      };
    }).toList();

    if (accredations.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please add at least one accreditation.")),
      );
      return;
    }
    print(accredations);
    BlocProvider.of<CoachBloc>(context).add(
      CoachUpdateEvent(
        coachId: widget.coachId,
        data: {'accredation': accredations},
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final coachBloc = BlocProvider.of<CoachBloc>(context);

    return BlocListener<CoachBloc, CoachState>(
        bloc: coachBloc,
        listener: (context, state) {
          if (state is LoadingCoachState) {
            loadingState(context: context);
          } else {
            hideLoadingDialog(context);
          }

          if (state is ErrorCoachState) {
            errorState(context: context, error: state.message);
          }

          if (state is CoachUpdateSuccessState) {
            errorState(context: context, error: 'Update done');
            NavigatorService.goBack();
          }
        },
        child: Scaffold(
          resizeToAvoidBottomInset: true,
          appBar: CustomAppBar(
            title: "Accreditation",
            leading: customBackButton(),
            actions: [
              Padding(
                padding: EdgeInsets.only(right: 20.w),
                child: GestureDetector(
                  onTap: _submitAccreditation,
                  child: Center(
                    child: customtext(
                      context: context,
                      newYear: "save",
                      font: 17.sp,
                      weight: FontWeight.w500,
                      color: AppPallete.change,
                    ),
                  ),
                ),
              ),
            ],
          ),
          body: Padding(
            padding: EdgeInsets.all(20.w),
            child: Form(
              key: _formKey,
              child: ListView(
                children: [
                  customtext(
                    context: context,
                    newYear: "Accreditation",
                    font: 17.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(height: 20.h),
                  ...List.generate(accredationControllers.length, (index) {
                    final controllers = accredationControllers[index];
                    return Padding(
                      padding: EdgeInsets.only(bottom: 30.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          customRequiredText(
                            context: context,
                            title: "Competition name",
                            font: 14.sp,
                            weight: FontWeight.w400,
                          ),
                          SizedBox(height: 10.h),
                          AuthField(
                            controller: controllers['title']!,
                            height: 30.h,
                            validator: (value) =>
                                value!.isEmpty ? 'Required' : null,
                          ),
                          SizedBox(height: 20.h),
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    customRequiredText(
                                      context: context,
                                      title: "Result",
                                      font: 14.sp,
                                      weight: FontWeight.w400,
                                    ),
                                    SizedBox(height: 10.h),
                                    AuthField(
                                      controller: controllers['result']!,
                                      height: 30.h,
                                      hintText: "e.g. First-runner up",
                                      validator: (value) =>
                                          value!.isEmpty ? 'Required' : null,
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(width: 18.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    customRequiredText(
                                      context: context,
                                      title: "Year of competition",
                                      font: 14.sp,
                                      weight: FontWeight.w400,
                                    ),
                                    SizedBox(height: 10.h),
                                    DropDown(
                                      height: 30.h,
                                      label: "Year",
                                      controller: controllers['year'],
                                      times: past20Years,
                                      color: AppPallete.inputBox,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  }),
                  SizedBox(height: 20.h),
                  Center(
                    child: addNewOnProfile(
                      context: context,
                      title: "Add Accreditation",
                      onTap: _addAccreditationForm,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }
}
