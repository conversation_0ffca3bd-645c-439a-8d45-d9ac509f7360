import 'package:class_z/core/imports.dart';

class CoachPressSlot extends StatelessWidget {
  final EventModel? event;
  const CoachPressSlot({required this.event, super.key});

  void _handleCancelSlot(BuildContext context) {
    print('🔥 COACH CANCEL BUTTON PRESSED!');
    final studentCount = event?.classId?.student.length ?? 0;
    print('🔥 Coach - Student count: $studentCount');
    print('🔥 Coach - Event ID: ${event?.id}');
    print('🔥 Coach - Class ID: ${event?.classId?.id}');

    if (studentCount > 0) {
      // Students are enrolled - show message that coaches can't cancel with students
      _showCoachCancelNotAllowedDialog(context);
    } else {
      // No students enrolled - show simple cancellation confirmation
      print('🔥 Coach - Showing simple cancel dialog for no students');
      _showSimpleCancelDialog(context);
    }
  }

  void _showCoachCancelNotAllowedDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Container(
            padding: EdgeInsets.all(20.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                customtext(
                  context: context,
                  newYear: "Cannot Cancel Slot",
                  font: 20.sp,
                  weight: FontWeight.w600,
                ),
                SizedBox(height: 20.h),
                customtext(
                  context: context,
                  newYear:
                      "This slot has students enrolled. As a coach, you cannot cancel slots with enrolled students. Please contact the center to handle cancellation.",
                  font: 16.sp,
                  weight: FontWeight.w400,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 30.h),
                Button(
                  buttonText: "Understood",
                  onPressed: () => NavigatorService.goBack(),
                  color: AppPallete.secondaryColor,
                  fontWeight: FontWeight.w600,
                  textSize: 15.sp,
                  width: double.infinity,
                  height: 45.h,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showSimpleCancelDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Container(
            padding: EdgeInsets.all(20.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                customtext(
                  context: context,
                  newYear: "Cancel Slot",
                  font: 20.sp,
                  weight: FontWeight.w600,
                ),
                SizedBox(height: 20.h),
                customtext(
                  context: context,
                  newYear:
                      "Are you sure you want to cancel this slot? No students are enrolled, so this action can be completed immediately.",
                  font: 16.sp,
                  weight: FontWeight.w400,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 30.h),
                Row(
                  children: [
                    Expanded(
                      child: Button(
                        buttonText: "Keep Slot",
                        onPressed: () => NavigatorService.goBack(),
                        color: AppPallete.greyColor,
                        fontWeight: FontWeight.w600,
                        textSize: 15.sp,
                        height: 45.h,
                      ),
                    ),
                    SizedBox(width: 15.w),
                    Expanded(
                      child: Button(
                        buttonText: "Cancel Slot",
                        onPressed: () {
                          NavigatorService.goBack();
                          // Note: Coaches might not have access to CenterBloc
                          // This might need to be handled differently
                          try {
                            context.read<CenterBloc>().add(
                                  ClassSlotDeleteEvent(
                                    event?.classId?.id ?? '',
                                    cancelType: null,
                                    eventId: event?.id,
                                  ),
                                );
                          } catch (e) {
                            print('Error: Coach cannot access CenterBloc: $e');
                            // Show error message
                            errorState(
                              context: context,
                              error:
                                  'Unable to cancel slot. Please contact the center.',
                            );
                          }
                        },
                        color: AppPallete.secondaryColor,
                        fontWeight: FontWeight.w600,
                        textSize: 15.sp,
                        height: 45.h,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    CoachModel? coach = locator<SharedRepository>().getCoachData();
    String firstText = "Fulled";
    Color firstColor = AppPallete.secondaryColor;
    if ((event?.classId?.numberOfStudent ?? 0) >
        (event?.classId?.student.length ?? 0)) {
      firstText = "Recruiting";
      firstColor = AppPallete.color185;
    }

    // Extract time information from class dates
    String startTime = "Unknown";
    String endTime = "Unknown";
    String duration = "Unknown duration";

    if (event?.classId?.dates != null && event!.classId!.dates!.isNotEmpty) {
      final classDate = event!.classId!.dates!.first;
      startTime = classDate.startTime ?? "Unknown";
      endTime = classDate.endTime ?? "Unknown";
      duration = classDate.durationMinutes ?? "Unknown duration";
    }

    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomAppBarDouble(
              title: "My Slot",
              title2: "Class details",
              leading: customBackButton(),
            ),
            SizedBox(
              height: 53.h,
            ),
            coachSlotConfirmationCard(
              context: context,
              firstColor: firstColor,
              firstTextColor: firstColor == AppPallete.color185
                  ? Colors.black
                  : Colors.white,
              firstText: firstText,
              address: () {
                final classAddress = event?.classId?.address ?? '';
                // Check if it's a placeholder text or empty
                if (classAddress.isEmpty ||
                    classAddress.toLowerCase().contains('full address') ||
                    classAddress
                        .toLowerCase()
                        .contains('corresponding program')) {
                  return 'center'; // This will show "On-site coaching: [center name]"
                }
                return classAddress;
              }(),
              center: coach?.center?.displayName ?? "Center",
              date: dateGenerator(date: event?.date ?? DateTime.now()),
              course:
                  "${event?.classId?.classProviding} (${event?.classId?.level})",
              classTime: duration,
              time: "$startTime - $endTime",
              numberOfStudent: "${event?.classId?.student.length}",
              numberOfStudentMax: "${event?.classId?.numberOfStudent}",
              ageStart: (event?.classId?.ageFrom?.toString() ?? "0"),
              agefinish: (event?.classId?.ageTo?.toString() ?? "0"),
              coach: coach?.displayName ?? "Unknown coach",
              charges:
                  "${event?.scheduleCharge ?? event?.classId?.charge ?? 0}",
              sen: event?.classId?.sen ?? true,
            ),
            SizedBox(
              height: 67.h,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 22),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Button(
                        onPressed: () {
                          // Validate that we have a valid slot ID before navigating
                          final slotId = event?.dateId?.toString() ?? '';
                          if (slotId.isEmpty) {
                            errorState(
                                context: context,
                                error:
                                    'Cannot access student list: No slot ID available. Please ensure this class has scheduled dates.');
                            return;
                          }

                          NavigatorService.pushNamed(AppRoutes.studentList,
                              arguments: {
                                'classId': event?.classId?.id,
                                'slotId': slotId,
                                'title':
                                    '${event?.classId?.classProviding} (${event?.classId?.level})',
                                'numberOfStudent':
                                    event?.classId?.numberOfStudent,
                                'totalStudent': event?.classId?.student.length,
                                'message': false
                              });
                        },
                        buttonText: "Student list",
                        color: AppPallete.secondaryColor),
                  ),
                  const SizedBox(
                    width: 29,
                  ),
                  Expanded(
                    child: Button(
                        onPressed: () {
                          print('🔥 Coach cancel button onPressed triggered');
                          _handleCancelSlot(context);
                        },
                        shadows: [shadow()],
                        buttonText: "Cancel slot",
                        color: AppPallete.white,
                        textColorFinal: AppPallete.secondaryColor),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 5,
            )
          ],
        ),
      ),
    );
  }
}
