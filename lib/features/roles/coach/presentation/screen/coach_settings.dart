import 'package:class_z/core/imports.dart';

class CoachSettings extends StatefulWidget {
  const CoachSettings({super.key});

  @override
  State<CoachSettings> createState() => _CoachSettingsState();
}

class _CoachSettingsState extends State<CoachSettings> {
  bool _activities = true;
  bool _promotion = false;
  void toggleActivities(bool value) {
    setState(() {
      _activities = value;
    });
  }

  void togglePromotion(bool value) {
    setState(() {
      _promotion = value;
    });
  }

  UserModel? userData;
  @override
  Widget build(BuildContext context) {
    final sharedRepository = Provider.of<SharedRepository>(context);

    userData = sharedRepository.getUserData();
    return Scaffold(
        body: SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _stack(),
          Padding(
            padding: EdgeInsets.only(left: 24.w, top: 10.h),
            child: customtext(
                context: context,
                newYear: "Profile",
                font: 15.sp,
                color: AppPallete.darkGrey,
                weight: FontWeight.w600),
          ),
          const SizedBox(
            height: 14,
          ),
          profile(
              context: context,
              name: "My Profile",
              iconData: Icons.person,
              onTap: () {
                NavigatorService.pushNamed(
                  AppRoutes.coachMyProfile,
                );
              }),
          customDivider(width: 379.w, padding: 24.w),

          // SizedBox(
          //   height: 17.h,
          // ),
          Padding(
            padding: EdgeInsets.only(left: 24.w),
            child: customtext(
                context: context,
                newYear: "Preference",
                font: 15.sp,
                color: AppPallete.darkGrey,
                weight: FontWeight.w600),
          ),

          ///Preference

          const SizedBox(
            height: 14,
          ),
          profile(context: context, name: "Language", iconData: Icons.language),
          customDivider(width: 379.w, padding: 24.w),

          const SizedBox(
            height: 9,
          ),
          notification(
            context: context,
            name: "Activities Notification",
            iconData: Icons.notifications,
            onChanged: toggleActivities,
            switchValue: _activities,
          ),
          const SizedBox(
            height: 9,
          ),
          customDivider(width: 379.w, padding: 24.w),
          const SizedBox(
            height: 9,
          ),

          notification(
              context: context,
              name: "Promotion Notification",
              iconData: Icons.telegram,
              onChanged: togglePromotion,
              switchValue: _promotion),
          const SizedBox(
            height: 9,
          ),
          customDivider(width: 379.w, padding: 24.w),

          ///Privacy

          const SizedBox(
            height: 17,
          ),
          Padding(
            padding: EdgeInsets.only(left: 24.w),
            child: customtext(
                context: context,
                newYear: "Privacy",
                font: 15.sp,
                color: AppPallete.darkGrey,
                weight: FontWeight.w600),
          ),
          const SizedBox(
            height: 12,
          ),
          profile(
              context: context,
              name: "Biometrics & passwords",
              iconData: Icons.fingerprint,
              onTap: () {
                final coachEmail = userData?.data?.coach?.email;
                if (coachEmail != null && coachEmail.isNotEmpty) {
                  NavigatorService.pushNamed(AppRoutes.resetPassword,
                      arguments: coachEmail);
                } else {
                  // Handle missing email: Show error or navigate to a page where email can be entered first.
                  // For now, let's show an error if the email isn't available for reset password.
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text(
                            'Your email is not available to reset the password.')),
                  );
                  print("Coach email not available for Reset Password.");
                }
              }),
          customDivider(width: 379.w, padding: 24.w),
          const SizedBox(
            height: 9,
          ),
          profile(
              context: context,
              name: "Terms and Condition",
              iconData: Icons.description,
              onTap: () {
                NavigatorService.pushNamed(AppRoutes.terms);
              }),
          customDivider(width: 379.w, padding: 24.w),
          const SizedBox(
            height: 17,
          ),

          ///Developer Tools (new section)
          // Padding(
          //   padding: EdgeInsets.only(left: 24.w),
          //   child: customtext(
          //       context: context,
          //       newYear: "Developer Tools",
          //       font: 15.sp,
          //       color: AppPallete.darkGrey,
          //       weight: FontWeight.w600),
          // ),
          const SizedBox(
            height: 12,
          ),
          const SizedBox(
            height: 17,
          ),

          ///Assistance

          Padding(
            padding: EdgeInsets.only(left: 24.w),
            child: customtext(
                context: context,
                newYear: "Assistance",
                font: 15.sp,
                color: AppPallete.darkGrey,
                weight: FontWeight.w600),
          ),
          const SizedBox(
            height: 12,
          ),
          profile(
              context: context,
              name: "Help Centre",
              iconData: Icons.help_outlined,
              onTap: () {
                NavigatorService.pushNamed(AppRoutes.faq);
              }),
          customDivider(width: 379.w, padding: 24.w),
          const SizedBox(
            height: 9,
          ),

          profile(
              context: context,
              name: "Contact Us",
              iconData: Icons.chat,
              onTap: () {
                // Passing null or an empty string will be handled by the ContactUs widget and routes.dart
                final coachEmail = userData?.data?.coach?.email;
                NavigatorService.pushNamed(AppRoutes.contactUs,
                    arguments: coachEmail);
              }),
          customDivider(width: 379.w, padding: 24.w),
          const SizedBox(
            height: 9,
          ),

          Padding(
            padding: EdgeInsets.only(left: 31.w, right: 31.w),
            child: Button(
                buttonText: "Log Out",
                color: AppPallete.secondaryColor,
                height: 49.h,
                onPressed: () async {
                  await sharedRepository.logout();

                  // Navigate to login screen
                  NavigatorService.pushNamedAndRemoveUntil(AppRoutes.logIn);
                }),
          ),
          const SizedBox(
            height: 9,
          ),
        ],
      ),
    ));
  }

  Widget _stack() {
    return SizedBox(
        height: 190.h,
        child: Stack(
          children: [
            Container(
              height: 156.h,
              decoration:
                  BoxDecoration(gradient: GradientProvider.getLinearGradient()),
            ),
            Positioned(
                top: 85.h,
                left: 19.w,
                child: customBackButton(color: Colors.white)),
            Positioned(
                top: 57.h,
                left: 0,
                right: 0,
                child: Align(
                  alignment: Alignment.center,
                  child: CustomImageBuilder(
                    imagePath:
                        "${AppText.device}${userData?.data?.coach?.mainImage?.url}",
                    height: 132.h,
                    width: 132.w,
                    borderRadius: 99.r,
                    boxshadow: [shadow(blurRadius: 15, opacity: 0.1)],
                  ),
                )),
          ],
        ));
  }

  Widget profile(
      {required BuildContext context,
      required String name,
      required IconData iconData,
      VoidCallback? onTap}) {
    return Padding(
      padding: EdgeInsets.only(left: 24.w, right: 27.w),
      child: GestureDetector(
        onTap: onTap,
        child: SizedBox(
          height: 33.h,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    iconData,
                    color: AppPallete.darkGrey,
                    size: 30,
                  ),
                  SizedBox(
                    width: 26.w,
                  ),
                  customtext(
                      context: context,
                      newYear: name,
                      font: 15.sp,
                      weight: FontWeight.w500),
                ],
              ),
              const Icon(
                Icons.arrow_forward_ios,
                color: AppPallete.darkGrey,
                size: 17,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget notification(
      {required BuildContext context,
      required String name,
      required IconData iconData,
      required Function(bool) onChanged,
      required bool switchValue}) {
    return Padding(
      padding: EdgeInsets.only(left: 24.w, right: 27.w),
      child: GestureDetector(
        onTap: () {},
        child: SizedBox(
          height: 33.h,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    iconData,
                    color: AppPallete.darkGrey,
                    size: 30,
                  ),
                  SizedBox(
                    width: 26.w,
                  ),
                  customtext(
                      context: context,
                      newYear: name,
                      font: 15.sp,
                      weight: FontWeight.w500),
                ],
              ),
              Switch(
                  value: switchValue,
                  activeColor: AppPallete.secondaryColor,
                  onChanged: onChanged),
            ],
          ),
        ),
      ),
    );
  }
}
