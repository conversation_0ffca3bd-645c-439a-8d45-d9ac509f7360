part of 'coach_bloc.dart';

abstract class CoachEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class CoachCreateEvent extends CoachEvent {
  final Map<String, dynamic> data;
  CoachCreateEvent({required this.data});
}

class CoachUpdateEvent extends CoachEvent {
  final String coachId;
  final Map<String, dynamic> data;
  CoachUpdateEvent({required this.coachId, required this.data});
}

class GetAllCoachEvent extends CoachEvent {}

class AssignCenterEvent extends CoachEvent {
  final String centerId;
  final String coachId;
  final String type;
  final bool? saveData;
  final String notificationId;

  AssignCenterEvent(
      {required this.centerId,
      required this.coachId,
      required this.type,
      this.saveData,
      required this.notificationId});
}

class RemoveCenterEvent extends CoachEvent {
  final String centerId;
  final String coachId;
  final String type;
  final bool? saveData;
  RemoveCenterEvent(
      {required this.centerId,
      required this.coachId,
      required this.type,
      this.saveData});
}

class GetAllProgramsByCenterIdEvent extends CoachEvent {
  final String centerId;
  GetAllProgramsByCenterIdEvent({required this.centerId});
  @override
  List<Object?> get props => [centerId];
}

class GetCoachInfoByIdEvent extends CoachEvent {
  final String coachId;
  GetCoachInfoByIdEvent({required this.coachId});
  @override
  List<Object?> get props => [coachId];
}
