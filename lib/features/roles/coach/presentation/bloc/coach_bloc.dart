import 'package:class_z/core/imports.dart';

part 'coach_event.dart';
part 'coach_state.dart';

class <PERSON><PERSON><PERSON> extends Bloc<CoachEvent, CoachState> {
  final CreateCoachUseCase createCoachUseCase;
  final UpdateCoachUseCase updateCoachUseCase;
  final GetAllCoachUseCase getAllCoachUseCase;
  final AssignCenterUseCase assignCenterUseCase;
  final RemoveCenterUseCase removeCenterUseCase;
  final GetAllProgramsUseCase getAllProgramsUseCase;
  final GetCoachInfoByIdUseCase getCoachInfoByIdUseCase;
  // Pass coachUseCase through the constructor
  CoachBloc(
      {required this.createCoachUseCase,
      required this.updateCoachUseCase,
      required this.getAllCoachUseCase,
      required this.assignCenterUseCase,
      required this.removeCenterUseCase,
      required this.getAllProgramsUseCase,
      required this.getCoachInfoByIdUseCase})
      : super(CoachInitial()) {
    on<CoachCreateEvent>(coachCreateEvent);
    on<CoachUpdateEvent>(coachUpdateEvent);
    on<GetAllCoachEvent>(getAllCoachEvent);
    on<AssignCenterEvent>(_assignCenterEvent);
    on<RemoveCenterEvent>(_removeCenterEvent);
    on<GetAllProgramsByCenterIdEvent>(_getAllProgramsByCenterId);
    on<GetCoachInfoByIdEvent>(_getCoachInfoByIdEvent);
  }

  FutureOr<void> coachCreateEvent(
      CoachCreateEvent event, Emitter<CoachState> emit) async {
    try {
      emit(LoadingCoachState());

      bool success = await createCoachUseCase.call(data: event.data);
      print('Coach created successfully');
      emit(CoachCreateSuccessState(success: success));
    } catch (e) {
      print(e.toString());
      emit(ErrorCoachState(message: e.toString()));
    }
  }

  FutureOr<void> coachUpdateEvent(
      CoachUpdateEvent event, Emitter<CoachState> emit) async {
    try {
      emit(LoadingCoachState());

      bool success = await updateCoachUseCase.call(
          coachId: event.coachId,
          data: event.data); // Ensure coachUseCase is initialized
      print('e');
      emit(CoachUpdateSuccessState(success: success));
    } catch (e) {
      print(e.toString());
      emit(ErrorCoachState(message: e.toString()));
    }
  }

  FutureOr<void> getAllCoachEvent(
      GetAllCoachEvent event, Emitter<CoachState> emit) async {
    try {
      emit(LoadingCoachState());
      List<CoachModel> coachs = await getAllCoachUseCase.call();
      emit(GetAllCoachSuccessState(coachs: coachs));
    } catch (e) {
      emit(ErrorCoachState(message: e.toString()));
    }
  }

  FutureOr<void> _assignCenterEvent(
      AssignCenterEvent event, Emitter<CoachState> emit) async {
    emit(LoadingCoachState());
    final success = await assignCenterUseCase.call(AssignCoachEntity(
        centerId: event.centerId,
        coachId: event.coachId,
        type: event.type,
        notificationId: event.notificationId,
        saveData: event.saveData));
    success.fold((failure) => emit(ErrorCoachState(message: failure.message)),
        (success) => emit(AssignCenterSuccessState(success: success)));
  }

  FutureOr<void> _removeCenterEvent(
      RemoveCenterEvent event, Emitter<CoachState> emit) async {
    emit(LoadingCoachState());
    final success = await removeCenterUseCase.call(
      RemoveCenterParams(
          centerId: event.centerId,
          coachId: event.coachId,
          type: event.type,
          saveData: event.saveData),
    );
    print(success);
    success.fold((failure) => emit(ErrorCoachState(message: failure.message)),
        (success) => emit(RemoveCenterSuccessState(success: success)));
  }

  FutureOr<void> _getAllProgramsByCenterId(
      GetAllProgramsByCenterIdEvent event, Emitter<CoachState> emit) async {
    emit(LoadingCoachState());
    final success = await getAllProgramsUseCase.call(event.centerId);
    success.fold(
        (failure) => emit(ErrorCoachState(message: failure.message)),
        (success) =>
            emit(GetAllProgramsByCenterIdSuccessState(programs: success)));
  }

  FutureOr<void> _getCoachInfoByIdEvent(
      GetCoachInfoByIdEvent event, Emitter<CoachState> emit) async {
    emit(LoadingCoachState());
    final success = await getCoachInfoByIdUseCase.call(event.coachId);
    success.fold((failure) => emit(ErrorCoachState(message: failure.message)),
        (success) => emit(GetCoachInfoByIdSuccessState(coach: success)));
  }
}
