part of 'coach_bloc.dart';

abstract class CoachState {}

class <PERSON><PERSON><PERSON><PERSON> extends Coach<PERSON>tate {}

class LoadingCoachState extends Coach<PERSON>tate {}

class ErrorCoachState extends CoachState {
  final String message;

  ErrorCoachState({required this.message});
}

class CoachCreateSuccessState extends Coach<PERSON>tate {
  final bool success;

  CoachCreateSuccessState({required this.success});
}

class CoachUpdateSuccessState extends Coach<PERSON><PERSON> {
  final bool success;

  CoachUpdateSuccessState({required this.success});
}

class GetAllCoachSuccessState extends CoachState {
  final List<CoachModel> coachs;

  GetAllCoachSuccessState({required this.coachs});
}

class AssignCenterSuccessState extends CoachState {
  final bool success;

  AssignCenterSuccessState({required this.success});
}

class RemoveCenterSuccessState extends CoachState {
  final bool success;

  RemoveCenterSuccessState({required this.success});
}

class GetAllProgramsByCenterIdSuccessState extends CoachState {
  final List<ClassModel>? programs;

  GetAllProgramsByCenterIdSuccessState({required this.programs});
}

class GetCoachInfoByIdSuccessState extends CoachState {
  final CoachModel? coach;

  GetCoachInfoByIdSuccessState({required this.coach});
}
