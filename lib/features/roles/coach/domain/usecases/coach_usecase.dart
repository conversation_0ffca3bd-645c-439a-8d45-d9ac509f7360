import 'package:class_z/core/imports.dart';

import 'package:dartz/dartz.dart';

class CreateCoachUseCase {
  final CoachRepoDomain coachRepoDomain;

  CreateCoachUseCase({required this.coachRepoDomain});
  Future<bool> call({required Map<String, dynamic> data}) async {
    print('creating coach');
    return await coachRepoDomain.createCoach(data: data);
  }
}

class UpdateCoachUseCase {
  final CoachRepoDomain coachRepoDomain;

  UpdateCoachUseCase({required this.coachRepoDomain});
  Future<bool> call(
      {required String coachId, required Map<String, dynamic> data}) async {
    print('repo');
    return await coachRepoDomain.updateCoach(coachId: coachId, data: data);
  }
}

class GetAllCoachUseCase {
  final CoachRepoDomain coachRepoDomain;

  GetAllCoachUseCase({required this.coachRepoDomain});

  Future<List<CoachModel>> call() async {
    return await coachRepoDomain.getAllCoach();
  }
}

class AssignCenterUseCase extends UseCase<bool, AssignCoachEntity> {
  final CoachRepoImpl _coachRepoImpl;
  AssignCenterUseCase(this._coachRepoImpl);
  Future<Either<Failure, bool>> call(AssignCoachEntity entity) async {
    return await _coachRepoImpl.assignCenter(
        centerId: entity.centerId,
        coachId: entity.coachId,
        type: entity.type,
        notificationId: entity.notificationId,
        saveData: entity.saveData);
  }
}

class RemoveCenterUseCase extends UseCase<bool, RemoveCenterParams> {
  final CoachRepoImpl _coachRepoImpl;
  RemoveCenterUseCase(this._coachRepoImpl);
  Future<Either<Failure, bool>> call(RemoveCenterParams params) async {
    return await _coachRepoImpl.removeCenter(
        centerId: params.centerId,
        coachId: params.coachId,
        type: params.type,
        saveData: params.saveData);
  }
}

class GetAllProgramsUseCase extends UseCase<List<ClassModel>?, String> {
  final CoachRepoImpl _coachRepoImpl;
  GetAllProgramsUseCase(this._coachRepoImpl);
  Future<Either<Failure, List<ClassModel>?>> call(centerId) {
    return _coachRepoImpl.getAllPrograms(centerId: centerId);
  }
}

class GetCoachInfoByIdUseCase extends UseCase<CoachModel?, String> {
  final CoachRepoImpl _coachRepoImpl;
  GetCoachInfoByIdUseCase(this._coachRepoImpl);
  Future<Either<Failure, CoachModel?>> call(coachId) {
    return _coachRepoImpl.getCoachInfoById(coachId: coachId);
  }
}
