import 'package:class_z/core/imports.dart';
import 'package:dartz/dartz.dart';

abstract class CoachRepoDomain {
  Future<bool> createCoach({required Map<String, dynamic> data});
  Future<bool> updateCoach(
      {required String coachId, required Map<String, dynamic> data});

  Future<List<CoachModel>> getAllCoach();
  Future<Either<Failure, bool>> assignCenter(
      {required String centerId,
      required String coachId,
      required String type,
      String? notificationId,
      bool? saveData});
  Future<Either<Failure, bool>> removeCenter(
      {required String centerId,
      required String coachId,
      required String type,
      bool? saveData});
  Future<Either<Failure, List<ClassModel>?>> getAllPrograms(
      {required String centerId});
  Future<Either<Failure, CoachModel?>> getCoachInfoById(
      {required String coachId});
}
