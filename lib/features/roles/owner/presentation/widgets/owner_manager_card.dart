import 'package:class_z/core/imports.dart';

class CenterManagerCard extends StatefulWidget {
  final String coach;
  final String location;
  final String image;
  final String classzId;
  final String coachId;
  final double rating;
  final bool added;
  final bool? pending;
  final VoidCallback onTap;

  const CenterManagerCard({
    super.key,
    required this.coach,
    required this.location,
    required this.image,
    required this.classzId,
    required this.coachId,
    required this.rating,
    required this.added,
    this.pending,
    required this.onTap,
  });

  @override
  State<CenterManagerCard> createState() => _CenterManagerCardState();
}

class _CenterManagerCardState extends State<CenterManagerCard> {
  String buttonState =
      'notAdded'; // Instead of using an enum, we use a simple string.

  @override
  void initState() {
    super.initState();
    buttonState = widget.added
        ? 'added'
        : 'notAdded'; // Initialize based on the `added` value.
  }

  void _setPendingState() {
    setState(() {
      buttonState = 'pending';
    });
  }

  void _setNotAddedState() {
    setState(() {
      buttonState = 'notAdded';
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachBloc, CoachState>(
      listener: (context, state) {
        if (state is LoadingCoachState) CircularProgressIndicator();
        if (state is ErrorCoachState) {
          errorState(context: context, error: state.message);
        } else if (state is RemoveCenterSuccessState) {
          
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCoachDetails(),
          SizedBox(
            height: 10.h,
          ),
          customDivider()
        ],
      ),
    );
  }

  Widget _buildCoachDetails() {
    return Row(
      children: [
        Container(
          height: 100.h,
          width: 100.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Stack(
            children: [
              CustomImageBuilder(
                borderRadius: 20,
                imagePath: widget.image,
                height: 100.h,
                width: 100.w,
              ),
              Positioned(
                top: 8.h,
                left: 7.w,
                child: customRating(
                  context: context,
                  rating: widget.rating.toString(),
                  fontSize: 12.sp,
                  weight: FontWeight.w700,
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 11.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  customtext(
                    context: context,
                    newYear: widget.coach,
                    font: 18.sp,
                    weight: FontWeight.w700,
                  ),
                  widget.added == true
                      ? Row(
                          children: [
                            Icon(Icons.check),
                            customtext(
                                context: context,
                                newYear: 'manager',
                                font: 15.sp,
                                weight: FontWeight.w500),
                            const SizedBox(
                              width: 17,
                            )
                          ],
                        )
                      : SizedBox(),
                ],
              ),
              SizedBox(height: 14.h),
              Row(
                children: [
                  customSvgPicture(
                    imagePath: ImagePath.locationSvg,
                    height: 18.89.h,
                    width: 18.89.w,
                  ),
                  SizedBox(width: 4.w),
                  customtext(
                    context: context,
                    newYear: widget.location.isNotEmpty
                        ? widget.location
                        : "Unknown",
                    font: 15.sp,
                    weight: FontWeight.w500,
                  ),
                ],
              ),
              SizedBox(height: 14.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: 82.w,
                    child: customtext(
                      context: context,
                      newYear: widget.classzId,
                      font: 15.sp,
                      weight: FontWeight.w400,
                    ),
                  ),
                  widget.added == true
                      ? Padding(
                          padding: const EdgeInsets.only(right: 17),
                          child: Button(
                            borderRadius: 5,
                            buttonText: "Remove manager",
                            color: AppPallete.white,
                            shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                            width: 132.w,
                            height: 28.h,
                            textSize: 15.sp,
                            textColorFinal: Colors.black,
                            fontWeight: FontWeight.w500,
                            onPressed: () {
                              print('remove');
                              widget.onTap
                                  .call(); // Trigger the onTap callback from parent
                            },
                          ),
                        )
                      : SizedBox(),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
