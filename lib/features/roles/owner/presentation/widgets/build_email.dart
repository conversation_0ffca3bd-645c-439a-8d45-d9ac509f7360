import 'package:class_z/core/imports.dart';

Widget buildStaticEmail(BuildContext context,String email) {

  return Padding(
    padding: const EdgeInsets.only(bottom: 20),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
          context: context,
          title: 'Email Address',
          font: 17.sp,
          weight: FontWeight.w500,
        ),
        SizedBox(height: 20),
        customtext(
          context: context,
          newYear: email,
          font: 15,
          weight: FontWeight.w500,
        ),
      ],
    ),
  );
}
