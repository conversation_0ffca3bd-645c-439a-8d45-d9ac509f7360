import 'package:class_z/core/imports.dart';

Widget picAndEdit({required BuildContext context, required String imagePath,required VoidCallback onTap}) {
  return Stack(
    children: [
      Center(
        child: CustomImageBuilder(
          imagePath: imageStringGenerator(imagePath: imagePath),
          height: 101.h,
          width: 101.w,
          borderRadius: 101.w,
         
        ),
      ),
      Positioned(
        top: 0,
        right: 0,
        child: InkWell(
          onTap: onTap,
          child: customtext(
            context: context,
            newYear: "Edit",
            color: AppPallete.secondaryColor,
            font: 17.sp,
            weight: FontWeight.w500,
          ),
        ),
      ),
    ],
  );
}
