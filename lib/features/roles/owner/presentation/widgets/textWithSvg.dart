import 'package:class_z/core/imports.dart';
Widget textWithSvg(
    {required BuildContext context,
    required String title,
    double? font,
    FontWeight? weight,
    Color? fontColor,
    Color? svgColor,
    double? size,
    double? space,
    String? imagePath,
    IconData? icon,
    }) {
  return Row(
    children: [
      imagePath != null
          ? customSvgPicture(
              imagePath: imagePath,
              height: size ?? 19.h,
              width: size ?? 19.w,
              color: fontColor ?? AppPallete.darkGrey)
          : Icon(icon,
              size: size ?? 19.w, color: fontColor ?? AppPallete.darkGrey),
      SizedBox(
        width: space ?? 5,
      ),
      customtext(
          context: context,
          newYear: title,
          font: font ?? 15.sp,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
          color: fontColor ?? AppPallete.black,
          weight: weight ?? FontWeight.w400),
    ],
  );
}
