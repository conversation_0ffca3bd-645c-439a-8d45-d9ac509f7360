import 'package:class_z/core/imports.dart';

Widget editProfile(
    {required BuildContext context,
    required String title,
    required VoidCallback onTap}) {
  return Padding(
    padding: EdgeInsets.symmetric(horizontal: 17.w),
    child: InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.w),
        decoration: BoxDecoration(
          color: AppPallete.white,
          borderRadius: BorderRadius.circular(10.w),
          boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            customtext(
                context: context,
                newYear: title,
                font: 17.sp,
                weight: FontWeight.w500),
            const Icon(
              Icons.arrow_forward_ios,
              color: AppPallete.darkGrey,
              size: 17,
            ),
          ],
        ),
      ),
    ),
  );
}
