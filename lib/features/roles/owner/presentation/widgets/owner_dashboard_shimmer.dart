import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

class OwnerDashboardShimmer extends StatelessWidget {
  const OwnerDashboardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SingleChildScrollView(
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 19.w, vertical: 20.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 70.h), // Placeholder for top bar
            // Welcome message placeholder
            Container(
              width: 200.w,
              height: 24.h,
              color: Colors.white,
            ),
            Sized<PERSON>ox(height: 8.h),
            Container(
              width: 150.w,
              height: 16.h,
              color: Colors.white,
            ),
            SizedBox(height: 24.h),

            // Business Growth placeholder
            Container(
              width: 180.w,
              height: 20.h,
              color: Colors.white,
            ),
            SizedBox(height: 24.h),

            // Total Revenue card placeholder
            Container(
              width: double.infinity,
              height: 300.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            SizedBox(height: 24.h),

            // Info cards placeholder
            Row(
              children: [
                Expanded(child: _buildShimmerInfoCard()),
                SizedBox(width: 16.w),
                Expanded(child: _buildShimmerInfoCard()),
              ],
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(child: _buildShimmerInfoCard()),
                SizedBox(width: 16.w),
                Expanded(child: _buildShimmerInfoCard()),
              ],
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(child: _buildShimmerInfoCard()),
                SizedBox(width: 16.w),
                Expanded(child: _buildShimmerInfoCard()),
              ],
            ),
            SizedBox(height: 24.h),

            // Button placeholder
            Container(
              width: double.infinity,
              height: 50.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerInfoCard() {
    return Container(
      height: 120.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
    );
  }
}
