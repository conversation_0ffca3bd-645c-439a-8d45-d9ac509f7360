import 'package:bloc/bloc.dart';
import 'package:class_z/core/error/failures.dart';
import 'package:class_z/features/roles/owner/domain/mappers/owner_dashboard_mapper.dart';
import 'package:class_z/features/roles/owner/domain/usecases/get_owner_dashboard_data.dart';
import 'package:class_z/features/roles/owner/domain/entities/owner_dashboard.dart';
import 'package:equatable/equatable.dart';

part 'owner_dashboard_event.dart';
part 'owner_dashboard_state.dart';

class OwnerDashboardBloc extends Bloc<OwnerDashboardEvent, OwnerDashboardState> {
  final GetOwnerDashboardData getOwnerDashboardData;

  OwnerDashboardBloc({required this.getOwnerDashboardData}) : super(OwnerDashboardInitial()) {
    on<FetchOwnerDashboard>((event, emit) async {
      emit(OwnerDashboardLoading());
      final result = await getOwnerDashboardData(
        event.ownerId, 
        startDate: event.startDate, 
        endDate: event.endDate
      );
      
      result.fold(
        (failure) => emit(OwnerDashboardError(message: _mapFailureToMessage(failure))),
        (dashboardModel) {
          final dashboard = OwnerDashboardMapper.toEntity(dashboardModel);
          emit(OwnerDashboardLoaded(dashboardData: dashboard));
        },
      );
    });
  }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return 'Server error: ${(failure as ServerFailure).message}';
      case CacheFailure:
        return 'Cache error: ${(failure as CacheFailure).message}';
      default:
        return 'Unexpected error: $failure';
    }
  }
}
