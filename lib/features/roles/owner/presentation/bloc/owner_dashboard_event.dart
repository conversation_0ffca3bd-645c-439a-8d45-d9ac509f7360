part of 'owner_dashboard_bloc.dart';

abstract class OwnerDashboardEvent extends Equatable {
  const OwnerDashboardEvent();

  @override
  List<Object> get props => [];
}

class FetchOwnerDashboard extends OwnerDashboardEvent {
  final String ownerId;
  final DateTime? startDate;
  final DateTime? endDate;

  const FetchOwnerDashboard({
    required this.ownerId,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object> get props => [ownerId, if (startDate != null) startDate!, if (endDate != null) endDate!];
}
