part of 'owner_dashboard_bloc.dart';

abstract class OwnerDashboardState extends Equatable {
  const OwnerDashboardState();

  @override
  List<Object> get props => [];
}

class OwnerDashboardInitial extends OwnerDashboardState {}

class OwnerDashboardLoading extends OwnerDashboardState {}

class OwnerDashboardLoaded extends OwnerDashboardState {
  final OwnerDashboard dashboardData;

  const OwnerDashboardLoaded({required this.dashboardData});

  @override
  List<Object> get props => [dashboardData];
}

class OwnerDashboardError extends OwnerDashboardState {
  final String message;

  const OwnerDashboardError({required this.message});

  @override
  List<Object> get props => [message];
}
