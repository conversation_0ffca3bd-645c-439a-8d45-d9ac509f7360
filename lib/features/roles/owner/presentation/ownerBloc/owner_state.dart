part of 'owner_bloc.dart';

abstract class OwnerState {}

class OwnerInitial extends OwnerState {}

class OwnerLoadingState extends OwnerState {}

class OwnerErrorState extends OwnerState {
  final String message;

  OwnerErrorState({required this.message});
}

class GetOWnerSuccess extends OwnerState {}

class UpdateOwnerSuccessState extends OwnerState {
  final bool success;

  UpdateOwnerSuccessState({required this.success});
}

class GetBranchSuccessState extends OwnerState {
  final List<CenterData> branches;

  GetBranchSuccessState({required this.branches});
}

class DeleteBranchSuccessState extends OwnerState {
  final bool success;

  DeleteBranchSuccessState({required this.success});
}

class UpdateBranchSuccessStae extends OwnerState {
  final bool success;

  UpdateBranchSuccessStae({required this.success});
}

class AssignCoachSuccessState extends OwnerState {
  final bool success;
  AssignCoachSuccessState({required this.success});
}

class RemoveCoachSuccessState extends OwnerState {
  final bool success;
  RemoveCoachSuccessState({required this.success});
}

class AsssignManagerSuccessState extends OwnerState {
  final bool success;
  AsssignManagerSuccessState({required this.success});
}

class RemoveManagerSuccessState extends OwnerState {
  final bool success;
  RemoveManagerSuccessState({required this.success});
}
