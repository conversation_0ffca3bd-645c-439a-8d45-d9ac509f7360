import 'package:class_z/core/imports.dart';

part 'owner_event.dart';
part 'owner_state.dart';

class OwnerBloc extends Bloc<OwnerEvent, OwnerState> {
  final GetBranchesByOwnerIdUseCase _getBranchesByOwnerIdUseCase;
  final DeleteBranchUseCase _deleteBranchUseCase;
  final UpdateBranchUseCase _updateBranchUseCase;
  final UpdateOwnerUseCase _updateOwnerUseCase;
  final RequestCoachToJoinUseCase _requestCoachToJoinUseCase;
  final RemoveCoachUseCase _removeCoachUseCase;

  OwnerBloc(
    this._getBranchesByOwnerIdUseCase,
    this._deleteBranchUseCase,
    this._updateBranchUseCase,
    this._updateOwnerUseCase,
    this._requestCoachToJoinUseCase,
    this._removeCoachUseCase,
    // this._requestManagerToJoinUseCase,
    // this._removeManagerUseCase
  ) : super(OwnerInitial()) {
    on<UpdateOwnerEvent>(_updateOwnerEvent);
    on<GetBranchsEvent>(__getBranchsEvent);
    on<DeleteBranchEvent>(_deleteBranchEvent);
    on<UpdateBranchEvent>(_updateBranchEvent);
    on<RequestCoachtoJoinEvent>(_requestCoachtoJoinEvent);
    on<RemoveCoachEvent>(_removeCoachEvent);
  }
  List<CenterData>? _branches = [];
  List<CenterData>? get branches => _branches;
  FutureOr<void> _updateOwnerEvent(
      UpdateOwnerEvent event, Emitter<OwnerState> emit) async {
    try {
      emit(OwnerLoadingState());
      bool success = await _updateOwnerUseCase.call(
          ownerId: event.ownerId, data: event.payload);
      emit(UpdateOwnerSuccessState(success: success));
    } catch (e) {
      emit(OwnerErrorState(message: e.toString()));
    }
  }

  FutureOr<void> __getBranchsEvent(
      GetBranchsEvent event, Emitter<OwnerState> emit) async {
    try {
      emit(OwnerLoadingState());
      List<CenterData> branches =
          await _getBranchesByOwnerIdUseCase.call(event.ownerId);
      _branches = branches;
      emit(GetBranchSuccessState(branches: branches));
    } catch (e) {
      emit(OwnerErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _deleteBranchEvent(
      DeleteBranchEvent event, Emitter<OwnerState> emit) async {
    try {
      emit(OwnerLoadingState());
      bool success = await _deleteBranchUseCase.call(branchId: event.branchId);
      emit(DeleteBranchSuccessState(success: success));
    } catch (e) {
      emit(OwnerErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _updateBranchEvent(
      UpdateBranchEvent event, Emitter<OwnerState> emit) async {
    try {
      emit(OwnerLoadingState());
      bool success = await _updateBranchUseCase.call(
          branchId: event.branchId, data: event.data);
      emit(UpdateBranchSuccessStae(success: success));
    } catch (e) {
      emit(OwnerErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _requestCoachtoJoinEvent(
      RequestCoachtoJoinEvent event, Emitter<OwnerState> emit) async {
    emit(OwnerLoadingState());
    final success = await _requestCoachToJoinUseCase.call(AssignCoachEntity(
        centerId: event.centerId, coachId: event.coachId, type: event.type));

    success.fold((failure) => emit(OwnerErrorState(message: failure.message)),
        (success) => emit(AssignCoachSuccessState(success: success)));
  }

  FutureOr<void> _removeCoachEvent(
      RemoveCoachEvent event, Emitter<OwnerState> emit) async {
    emit(OwnerLoadingState());
    final success = await _removeCoachUseCase.call(AssignCoachEntity(
        centerId: event.centerId, coachId: event.coachId, type: event.type));
    success.fold((failure) => emit(OwnerErrorState(message: failure.message)),
        (success) => emit(RemoveCoachSuccessState(success: success)));
  }
}
