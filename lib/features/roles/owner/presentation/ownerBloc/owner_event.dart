part of 'owner_bloc.dart';

abstract class OwnerEvent extends Equatable {
  const OwnerEvent();
  @override
  List<Object> get props => [];
}

class GetOwnerEvent extends OwnerEvent {}

class UpdateOwnerEvent extends OwnerEvent {
  final String ownerId;
  final Map<String, dynamic> payload;

  UpdateOwnerEvent(this.ownerId, this.payload);
}

class GetBaseUserData extends OwnerEvent {
  final String baseUserId;

  GetBaseUserData(this.baseUserId);
}

class GetBranchsEvent extends OwnerEvent {
  final String ownerId;

  GetBranchsEvent({required this.ownerId});
}

class DeleteBranchEvent extends OwnerEvent {
  final String branchId;

  DeleteBranchEvent({required this.branchId});
  @override
  // TODO: implement props
  List<Object> get props => [branchId];
}

class UpdateBranchEvent extends OwnerEvent {
  final String branchId;
  final Map<String, dynamic> data;

  UpdateBranchEvent({required this.branchId, required this.data});
  @override
  // TODO: implement props
  List<Object> get props => [branchId, data];
}

class RequestCoachtoJoinEvent extends OwnerEvent {
  final String centerId;
  final String coachId;
  final String type;

  RequestCoachtoJoinEvent(
      {required this.centerId, required this.coachId, required this.type});
}

class RemoveCoachEvent extends OwnerEvent {
  final String centerId;
  final String coachId;
  final String type;

  RemoveCoachEvent(
      {required this.centerId, required this.coachId, required this.type});
}

// class RequestManagerToJoinEvent extends OwnerEvent {
//   final String centerId;
//   final String coachId;

//   RequestManagerToJoinEvent({required this.centerId, required this.coachId});
// }

// class RemoveManagerEvent extends OwnerEvent {
//   final String centerId;
//   final String coachId;

//   RemoveManagerEvent({required this.centerId, required this.coachId});
// }
