import 'package:class_z/core/imports.dart';

class OwnerCoachprofile extends StatefulWidget {
  final CoachModel? coach;
  final String? coachId;
  const OwnerCoachprofile({this.coach, this.coachId, super.key});

  @override
  State<OwnerCoachprofile> createState() => _OwnerCoachprofileState();
}

class _OwnerCoachprofileState extends State<OwnerCoachprofile> {
  CoachModel? currentCoach;

  @override
  void initState() {
    super.initState();
    currentCoach = widget.coach;
    if (currentCoach == null) {
      context
          .read<CoachBloc>()
          .add(GetCoachInfoByIdEvent(coachId: widget.coachId ?? ''));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CoachBloc, CoachState>(
      listener: (context, state) {
        if (state is LoadingCoachState)
          loadingState(context: context);
        else
          hideLoadingDialog(context);
        if (state is ErrorCoachState)
          errorState(context: context, error: state.message);
        if (state is GetCoachInfoByIdSuccessState && state.coach != null) {
          setState(() {
            currentCoach = state.coach;
          });
        }
      },
      child: Scaffold(
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 85.h,
                ),
                customBackButton(),
                SizedBox(
                  height: 8.h,
                ),
                customtext(
                    context: context,
                    newYear: "Coach Profile",
                    font: 30.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 49.h,
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 17.w),
                  child: _coachProfile(context: context),
                ),
                SizedBox(
                  height: 20.h,
                ),
                editProfile(
                  context: context,
                  title: "Coach description",
                  onTap: () {
                    final String? currentCoachId =
                        currentCoach?.id ?? widget.coachId;
                    if (currentCoachId != null && currentCoachId.isNotEmpty) {
                      NavigatorService.pushNamed(
                          AppRoutes.coachProfileEditDescription,
                          arguments: currentCoachId);
                    } else {
                      errorState(
                          context: context, error: 'Coach ID not available');
                    }
                  },
                ),
                SizedBox(
                  height: 20.h,
                ),
                editProfile(
                  context: context,
                  title: "My Skills",
                  onTap: () {
                    final String? currentCoachId =
                        currentCoach?.id ?? widget.coachId;
                    if (currentCoachId != null && currentCoachId.isNotEmpty) {
                      NavigatorService.pushNamed(
                          AppRoutes.coachProfileEditSkills,
                          arguments: currentCoachId);
                    } else {
                      errorState(
                          context: context, error: 'Coach ID not available');
                    }
                  },
                ),
                SizedBox(
                  height: 20.h,
                ),
                editProfile(
                  context: context,
                  title: "Accredation",
                  onTap: () {
                    final String? currentCoachId =
                        currentCoach?.id ?? widget.coachId;
                    if (currentCoachId != null && currentCoachId.isNotEmpty) {
                      NavigatorService.pushNamed(
                          AppRoutes.coachProfileEditAccredation,
                          arguments: currentCoachId);
                    } else {
                      errorState(
                          context: context, error: 'Coach ID not available');
                    }
                  },
                ),
                SizedBox(
                  height: 20.h,
                ),
                editProfile(
                  context: context,
                  title: "Experience",
                  onTap: () {
                    final String? currentCoachId =
                        currentCoach?.id ?? widget.coachId;
                    if (currentCoachId != null && currentCoachId.isNotEmpty) {
                      NavigatorService.pushNamed(
                          AppRoutes.coachProfileEditExperience,
                          arguments: currentCoachId);
                    } else {
                      errorState(
                          context: context, error: 'Coach ID not available');
                    }
                  },
                ),
                SizedBox(
                  height: 20.h,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _coachProfile({required BuildContext context}) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      decoration: BoxDecoration(
          color: AppPallete.white,
          borderRadius: BorderRadius.circular(
            20.w,
          ),
          boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 20.h,
          ),
          picAndEdit(
            context: context,
            imagePath: currentCoach?.mainImage?.url ?? '',
            onTap: () {
              final String? currentCoachId = currentCoach?.id ?? widget.coachId;
              if (currentCoachId != null && currentCoachId.isNotEmpty) {
                NavigatorService.pushNamed(AppRoutes.coachProfileEditDetails,
                    arguments: currentCoachId);
              } else {
                errorState(context: context, error: 'Coach ID not available');
              }
            },
          ),
          SizedBox(
            height: 22.h,
          ),
          customtext(
              context: context,
              newYear: "Coach Information",
              font: 20.sp,
              weight: FontWeight.w600),
          SizedBox(
            height: 15.h,
          ),
          titleAndSubtitle(
              context: context,
              title: "Full Name",
              subtitle: currentCoach?.legalName ?? 'Unknown Legal Name'),
          SizedBox(
            height: 15.h,
          ),
          titleAndSubtitle(
              context: context,
              title: "Display Name",
              subtitle: currentCoach?.displayName ?? 'Unknown display name'),
          SizedBox(
            height: 15.h,
          ),
          titleAndSubtitle(
              context: context,
              title: "Connected Centre",
              subtitle: currentCoach?.center?.displayName ?? 'No Centre'),
          SizedBox(
            height: 15.h,
          ),
        ],
      ),
    );
  }
}
