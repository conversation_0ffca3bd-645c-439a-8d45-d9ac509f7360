import 'package:class_z/core/imports.dart';

class OwnerManage extends StatefulWidget {
  const OwnerManage({super.key});

  @override
  State<OwnerManage> createState() => _OwnerManageState();
}

class _OwnerManageState extends State<OwnerManage> {
  String leftText = "Add Centre";
  String rightText = "Delete Centre";
  String ownerId = "";
  UserModel? user;
  CoachModel? coachData;

  @override
  void initState() {
    super.initState();
    ownerId = locator<SharedRepository>().getOwnerId();
    user = locator<SharedRepository>().getUserData();

    // Debug logging
    print("=== Owner Manage Debug ===");
    print("Initial user data: ${user?.data?.coach?.mainImage?.url}");
    print("Owner ID: $ownerId");
    print("User has coach: ${user?.data?.coach != null}");
    print("Owner is coach: ${user?.data?.owner?.isCoach}");
    print("Owner coach ID: ${user?.data?.owner?.coachId}");

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<OwnerBloc>().add(GetBranchsEvent(ownerId: ownerId));
      // Fetch fresh user data to get latest coach information
      context.read<AuthBloc>().add(GetUserEvent());
      // Check if we need to fetch coach data separately
      _fetchCoachDataIfNeeded();
    });
  }

  void _fetchCoachDataIfNeeded() {
    // If owner is a coach but coach data is missing, fetch it
    if (user?.data?.owner?.isCoach == true &&
        user?.data?.coach == null &&
        user?.data?.owner?.coachId != null) {
      print("=== Fetching Missing Coach Data ===");
      print("Coach ID: ${user?.data?.owner?.coachId}");
      context
          .read<CoachBloc>()
          .add(GetCoachInfoByIdEvent(coachId: user!.data!.owner!.coachId!));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomAppBar(
              title: "Centre Branch",
              leading: customBackButton(),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      NavigatorService.pushNamed(AppRoutes.centerProfilesetup1);
                    },
                    child: customtext(
                        context: context,
                        newYear: leftText,
                        font: 17.sp,
                        color: AppPallete.wordsOfRequest,
                        weight: FontWeight.w500),
                  ),
                  SizedBox(
                    height: 44.h,
                  ),
                  InkWell(
                    onTap: () {
                      setState(() {
                        if (rightText == "Done") {
                          rightText = "Delete Centre";
                          leftText = "Add Centre";
                        } else {
                          rightText = "Done";
                        }
                      });
                    },
                    child: customtext(
                        context: context,
                        newYear: rightText,
                        font: 17.sp,
                        color: AppPallete.wordsOfRequest,
                        weight: FontWeight.w500),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            Builder(
              builder: (context) {
                print("=== Rendering Coach Profile ===");
                print("User exists: ${user != null}");
                print("User data exists: ${user?.data != null}");
                print("Coach exists: ${user?.data?.coach != null}");
                print("Coach image URL: ${user?.data?.coach?.mainImage?.url}");
                print("Fetched coach data exists: ${coachData != null}");
                print("Fetched coach image URL: ${coachData?.mainImage?.url}");
                print("Owner exists: ${user?.data?.owner != null}");
                print("Owner is coach: ${user?.data?.owner?.isCoach}");
                print("Owner coach ID: ${user?.data?.owner?.coachId}");

                // Use coach data from user object if available, otherwise use separately fetched coach data
                final effectiveCoach = user?.data?.coach ?? coachData;
                final imageUrl = effectiveCoach?.mainImage?.url ?? '';

                print("=== Final coach data ===");
                print("Effective coach exists: ${effectiveCoach != null}");
                print("Final image URL: $imageUrl");

                return _coachProfile(
                  context: context,
                  imageUrl: imageUrl,
                  onTap: () {
                    print("=== Coach Profile Tap ===");
                    print("Current image URL: $imageUrl");
                    print("Owner is coach: ${user?.data?.owner?.isCoach}");
                    if (user?.data?.owner?.isCoach == false) {
                      _showAlertForCoach(
                        onNo: () {
                          NavigatorService.goBack();
                        },
                        onYes: () {
                          context.read<OwnerBloc>().add(UpdateOwnerEvent(
                              user?.data?.owner?.id ?? '', {'isCoach': true}));
                        },
                      );
                    } else {
                      NavigatorService.pushNamed(AppRoutes.ownerCoachProfile,
                          arguments: {
                            if (effectiveCoach != null) "coach": effectiveCoach,
                            if (effectiveCoach == null)
                              "coachId": user?.data?.owner?.coachId
                          });
                    }
                  },
                );
              },
            ),
            SizedBox(
              height: 24.h,
            ),
            BlocListener<AuthBloc, AuthState>(
              listener: (context, authState) {
                print("=== Auth State Change ===");
                print("State type: ${authState.runtimeType}");
                if (authState is UserLoaded) {
                  print("UserLoaded received!");
                  print(
                      "Coach data exists: ${authState.user.data?.coach != null}");
                  print(
                      "Coach image URL: ${authState.user.data?.coach?.mainImage?.url}");
                  print(
                      "Owner is coach: ${authState.user.data?.owner?.isCoach}");
                  print(
                      "Owner coach ID: ${authState.user.data?.owner?.coachId}");
                  setState(() {
                    user = authState.user;
                  });
                  print("State updated with new user data");
                  // Check if we need to fetch coach data after user data update
                  _fetchCoachDataIfNeeded();
                }
              },
              child: BlocListener<CoachBloc, CoachState>(
                listener: (context, coachState) {
                  print("=== Coach State Change ===");
                  print("State type: ${coachState.runtimeType}");
                  if (coachState is GetCoachInfoByIdSuccessState) {
                    print("GetCoachInfoByIdSuccessState received!");
                    print(
                        "Coach image URL: ${coachState.coach?.mainImage?.url}");
                    setState(() {
                      coachData = coachState.coach;
                    });
                    print("Coach data updated!");
                  }
                },
                child: BlocConsumer<OwnerBloc, OwnerState>(
                  listener: (context, state) {
                    if (state is OwnerLoadingState) {
                      loadingState(context: context);
                    } else
                      hideLoadingDialog(context);
                    if (state is OwnerErrorState) {
                      errorState(context: context, error: state.message);
                    }
                    if (state is DeleteBranchSuccessState) {
                      print('done');
                      context
                          .read<OwnerBloc>()
                          .add(GetBranchsEvent(ownerId: ownerId));
                    }
                    if (state is UpdateOwnerSuccessState) {
                      NavigatorService.goBack();
                      errorState(context: context, error: 'Update done');
                      // Refresh user data after owner update
                      context.read<AuthBloc>().add(GetUserEvent());
                    }
                  },
                  builder: (context, state) {
                    return Column(
                      children: [
                        if (state is GetBranchSuccessState)
                          ListView.builder(
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: state.branches.length,
                            itemBuilder: (context, index) {
                              final branch = state.branches[index];
                              return Padding(
                                padding: EdgeInsets.symmetric(horizontal: 21.w),
                                child: Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        _branchCenter(
                                          context: context,
                                          imageUrl: branch.mainImage?.url ?? "",
                                          centerName:
                                              branch.displayName ?? 'Undefined',
                                          managerName:
                                              branch.managers!.length > 0
                                                  ? "Assigned"
                                                  : "UnAssigned",
                                          coachName: branch.coachs!.length > 0
                                              ? "Assigned"
                                              : "UnAssigned",
                                          location: branch.address?.city ??
                                              'Undefined',
                                          verified: branch.verified ?? false,
                                          onTap: () {
                                            NavigatorService.pushNamed(
                                                AppRoutes.ownerCenterProfile,
                                                arguments: branch);
                                          },
                                        ),
                                        _addOrEdit(
                                          icon: rightText == "Delete Centre"
                                              ? Icons.mode_edit_outlined
                                              : Icons.remove,
                                          color: rightText == "Delete Centre"
                                              ? AppPallete.lightGrey
                                              : AppPallete.red,
                                          onTap: () {
                                            if (rightText == "Delete Centre") {
                                              NavigatorService.pushNamed(
                                                  AppRoutes.ownerCenterProfile,
                                                  arguments: branch);
                                            } else {
                                              context.read<OwnerBloc>().add(
                                                  DeleteBranchEvent(
                                                      branchId: branch.id!));
                                            }
                                          },
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 16,
                                    )
                                  ],
                                ),
                              );
                            },
                          )
                        else
                          const Center(child: Text("No branch available")),

                        // Individual Creator Warning
                        if (user?.data?.owner?.isIndividualCreator == true)
                          _buildIndividualCreatorWarning(),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _coachProfile(
      {required BuildContext context,
      required String imageUrl,
      required VoidCallback onTap}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 21.w),
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 21.h),
          decoration: BoxDecoration(
              color: AppPallete.white,
              borderRadius: BorderRadius.circular(50.w),
              boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
          child: Row(
            children: [
              CustomImageBuilder(
                imagePath: imageStringGenerator(imagePath: imageUrl),
                height: 50.w,
                width: 50.w,
                borderRadius: 50.w,
              ),
              SizedBox(
                width: 34.w,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  customtext(
                      context: context,
                      newYear: "Coach Profile",
                      font: 20.sp,
                      weight: FontWeight.w700),
                  customtext(
                      context: context,
                      newYear:
                          "For dual-role users, showcase your coach\ninformation in your centre",
                      font: 12.sp,
                      weight: FontWeight.w300),
                  //  showAlertDialogWithAcceptReject(context, data)
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _showAlertForCoach(
      {required VoidCallback onYes, required VoidCallback onNo}) {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppPallete.backgroundColor,
          title: Text('Become a Coach'),
          content: Text('Do you want to be a coach?'),
          actions: [
            Row(
              children: [
                Expanded(
                  child: Button(
                      height: 40.h,
                      buttonText: "Accept",
                      color: AppPallete.secondaryColor,
                      onPressed: onYes),
                ),
                SizedBox(
                  width: 24,
                ),
                Expanded(
                  child: Button(
                      height: 40.h,
                      buttonText: "Reject",
                      color: AppPallete.greyWord,
                      textColorFinal: AppPallete.white,
                      onPressed: onNo),
                ),
              ],
            )
            // TextButton(
            //   onPressed: () {
            //     Navigator.of(context).pop();
            //     onNo();
            //   },
            //   style: TextButton.styleFrom(
            //     foregroundColor: Colors.grey, // gray color
            //   ),
            //   child: const Text('No'),
            // ),
            // ElevatedButton(
            //   onPressed: () {
            //     Navigator.of(context).pop();
            //     onYes();
            //   },
            //   style: ElevatedButton.styleFrom(
            //     backgroundColor: Colors.blue, // blue color
            //   ),
            //   child: const Text(
            //     'Yes',
            //     style: TextStyle(color: AppPallete.backgroundColor),
            //   ),
            // ),
          ],
        );
      },
    );
  }

  Widget _branchCenter(
      {required BuildContext context,
      required String imageUrl,
      required String centerName,
      required String managerName,
      required String coachName,
      required bool verified,
      required String location,
      required VoidCallback onTap}) {
    return InkWell(
      onTap: onTap,
      child: Stack(children: [
        ///main card
        _mainCardBranch(
          context: context,
          imageUrl: imageUrl,
          centerName: centerName,
          managerName: managerName,
          coachName: coachName,
          location: location,
          verified: false,
        ),
        verified == false
            ? Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                      color: AppPallete.darkGrey.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(20.w),
                      boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
                  child: Center(
                    child: customtext(
                        context: context,
                        newYear: "Reviewing. . .",
                        font: 20.sp,
                        weight: FontWeight.w500,
                        color: AppPallete.white),
                  ),
                ),
              )
            : Container(),
      ]),
    );
  }

  Widget _mainCardBranch({
    required BuildContext context,
    required String imageUrl,
    required String centerName,
    required String managerName,
    required String coachName,
    required bool verified,
    required String location,
  }) {
    return Container(
      padding: EdgeInsets.all(7.w),
      decoration: BoxDecoration(
          color: AppPallete.white,
          borderRadius: BorderRadius.circular(20.w),
          boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          CustomImageBuilder(
            imagePath: imageStringGenerator(imagePath: imageUrl),
            height: 126.w,
            width: 126.w,
            borderRadius: 20.w,
          ),
          SizedBox(
            width: 7.w,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              customtext(
                  context: context,
                  newYear: centerName,
                  font: 20.sp,
                  weight: FontWeight.w700,
                  color: AppPallete.secondaryColor),
              Row(
                children: [
                  customSvgPicture(
                      imagePath: ImagePath.locationSvg,
                      height: 19.w,
                      width: 19.w),
                  customtext(
                      context: context,
                      newYear: location,
                      font: 15.sp,
                      weight: FontWeight.w400)
                ],
              ),
              customtext(
                  context: context,
                  newYear: "Manager :$managerName",
                  font: 15.sp,
                  weight: FontWeight.w500),
              customtext(
                  context: context,
                  newYear: "Coach :$coachName",
                  font: 15.sp,
                  weight: FontWeight.w500),
            ],
          )
        ],
      ),
    );
  }

  Widget _addOrEdit(
      {required IconData icon,
      required Color color,
      required VoidCallback onTap}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
            color: color, borderRadius: BorderRadius.circular(34.w)),
        child: Icon(
          icon,
          color: color == AppPallete.red ? AppPallete.white : AppPallete.black,
        ),
      ),
    );
  }

  Widget _buildIndividualCreatorWarning() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 21.w, vertical: 20.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppPallete.rating.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.w),
        border: Border.all(
          color: AppPallete.rating.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: AppPallete.rating,
            size: 24.w,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: customtext(
              context: context,
              newYear:
                  "You can only add one center because you are an individual creator",
              font: 14.sp,
              weight: FontWeight.w500,
              color: AppPallete.rating,
            ),
          ),
        ],
      ),
    );
  }
}
