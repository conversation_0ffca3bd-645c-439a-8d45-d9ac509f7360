import 'package:class_z/core/imports.dart';
import 'package:class_z/features/roles/owner/domain/entities/owner_dashboard.dart';
import 'package:class_z/features/roles/owner/domain/usecases/get_owner_dashboard_data.dart'
    as usecase;
import 'package:class_z/features/roles/owner/presentation/bloc/owner_dashboard_bloc.dart';
import 'package:class_z/features/roles/owner/presentation/widgets/owner_dashboard_shimmer.dart';

class OwnerMain extends StatefulWidget {
  const OwnerMain({super.key, required this.user});

  final UserModel user;

  @override
  State<OwnerMain> createState() => _OwnerMainState();
}

// Custom painter for the revenue chart
class RevenueChartPainter extends CustomPainter {
  final List<MonthlyRevenue> monthlyData;
  final double maxValue;

  RevenueChartPainter({
    required this.monthlyData,
    required this.maxValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (monthlyData.isEmpty) return;

    final Paint linePaint = Paint()
      ..color = Colors.green.shade300
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final Paint fillPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Colors.green.shade200.withAlpha(128),
          Colors.green.shade50.withAlpha(25),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.fill;

    final Paint dotPaint = Paint()
      ..color = Colors.green.shade400
      ..style = PaintingStyle.fill;

    final Paint dotStrokePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final double width = size.width;
    final double height = size.height;
    final int dataPoints = monthlyData.length;

    // Skip if not enough data points
    if (dataPoints < 2) return;

    final double horizontalStep = width / (dataPoints - 1);

    // Create path for the line
    final Path linePath = Path();
    final Path fillPath = Path();

    // Start at the first point
    final double firstX = 0;
    final double firstY = height - (monthlyData[0].revenue / maxValue * height);
    linePath.moveTo(firstX, firstY);
    fillPath.moveTo(firstX, height); // Start at bottom left for fill
    fillPath.lineTo(firstX, firstY); // Go to first data point

    // Add points to the paths
    for (int i = 0; i < dataPoints; i++) {
      final double x = i * horizontalStep;
      final double y = height - (monthlyData[i].revenue / maxValue * height);

      if (i > 0) {
        // For smooth curve, use quadratic bezier
        final double prevX = (i - 1) * horizontalStep;
        final double prevY =
            height - (monthlyData[i - 1].revenue / maxValue * height);

        final double controlX = (prevX + x) / 2;

        linePath.quadraticBezierTo(controlX, prevY, x, y);
        fillPath.quadraticBezierTo(controlX, prevY, x, y);
      }
    }

    // Complete the fill path
    fillPath.lineTo(width, height); // Bottom right
    fillPath.lineTo(0, height); // Bottom left
    fillPath.close();

    // Draw the paths
    canvas.drawPath(fillPath, fillPaint);
    canvas.drawPath(linePath, linePaint);

    // Draw dots at each data point
    for (int i = 0; i < dataPoints; i++) {
      final double x = i * horizontalStep;
      final double y = height - (monthlyData[i].revenue / maxValue * height);

      // Draw highlight dot for the last point
      if (i == dataPoints - 1) {
        canvas.drawCircle(Offset(x, y), 6, dotPaint);
        canvas.drawCircle(Offset(x, y), 6, dotStrokePaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class _OwnerMainState extends State<OwnerMain> {
  late final OwnerDashboardBloc _dashboardBloc;
  late final NotificationBloc _notificationBloc;
  final notificationservices = locator<Notificationservice>();
  bool _isPermissionGranted = true;
  OwnerModel? owner;
  bool _hasInitializedData = false;
  bool _showZCoin = true; // Toggle between HKD and Z coin - default to Z coin

  // Currency conversion rate: 1 Z coin = 25 HKD
  static const double zCoinToHkdRate = 25.0;

  @override
  void initState() {
    super.initState();
    _checkNotificationPermission();
    // Initialize blocs
    _dashboardBloc = OwnerDashboardBloc(
      getOwnerDashboardData: GetIt.I<usecase.GetOwnerDashboardData>(),
    );
    _notificationBloc = NotificationBloc(
      getNotifications: GetIt.I<GetNotifications>(),
      readNotification: GetIt.I<ReadNotification>(),
      deleteNotification: GetIt.I<DeleteNotification>(),
      checkDeviceToken: GetIt.I<CheckDeviceToken>(),
    );
    _initializeOwnerData();
  }

  void _initializeOwnerData() {
    owner = locator<SharedRepository>().getOwnerData();
    print(
        'Owner data initialization: ${owner?.id != null ? 'Success' : 'Failed'}');
    if (owner?.id != null) {
      print('Owner ID: ${owner!.id}');
      _fetchDashboardData();
      _notificationBloc.add(
          GetNotificationsEvent(userId: owner!.id!)); // Fetch notifications
      _hasInitializedData = true;
    } else {
      // Handle case where owner data is not available
      print('Owner data not found in SharedRepository');
      _hasInitializedData = false;
    }
  }

  void _fetchDashboardData() {
    if (owner?.id != null && owner!.id!.isNotEmpty) {
      print('Fetching dashboard data for owner: ${owner!.id}');
      _dashboardBloc.add(FetchOwnerDashboard(ownerId: owner!.id!));
    } else {
      print('Cannot fetch dashboard data: Owner ID is null or empty');
    }
  }

  void _retryDataFetch() {
    // Re-initialize owner data in case it was updated
    _initializeOwnerData();
  }

  @override
  void dispose() {
    _dashboardBloc.close();
    _notificationBloc.close();
    super.dispose();
  }

  void _checkNotificationPermission() async {
    NotificationSettings settings =
        await FirebaseMessaging.instance.getNotificationSettings();
    setState(() {
      _isPermissionGranted =
          settings.authorizationStatus == AuthorizationStatus.authorized;
      if (!_isPermissionGranted) {
        notificationservices.requestNotificationPermission(context);
      }
    });
  }

  // Currency conversion methods
  // Base data is in Z coins, convert to HKD when needed
  double _convertZCoinToHkd(double zCoinAmount) {
    return zCoinAmount * zCoinToHkdRate;
  }

  String _formatCurrency(double zCoinAmount) {
    if (_showZCoin) {
      return '${zCoinAmount.toInt()} Z';
    } else {
      return '${_convertZCoinToHkd(zCoinAmount).toInt()} HKD';
    }
  }

  String _getCurrencySymbol() {
    return _showZCoin ? 'Z' : '\$';
  }

  void _toggleCurrency() {
    setState(() {
      _showZCoin = !_showZCoin;
    });
  }

  Color _getIconBackgroundColor(IconData iconData) {
    if (iconData == Icons.school) return Colors.purple.withAlpha(51);
    if (iconData == Icons.people) return Colors.blue.withAlpha(51);
    if (iconData == Icons.attach_money) return Colors.green.withAlpha(51);
    if (iconData == Icons.monetization_on) return Colors.purple.withAlpha(51);
    if (iconData == Icons.trending_up) return Colors.green.withAlpha(51);
    if (iconData == Icons.trending_down) return Colors.red.withAlpha(51);
    return Colors.grey.withAlpha(51);
  }

  Color _getIconColor(IconData iconData) {
    if (iconData == Icons.school) return Colors.purple;
    if (iconData == Icons.people) return Colors.blue;
    if (iconData == Icons.attach_money) return Colors.green;
    if (iconData == Icons.monetization_on) return Colors.purple;
    if (iconData == Icons.trending_up) return Colors.green;
    if (iconData == Icons.trending_down) return Colors.red;
    return Colors.grey;
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: _dashboardBloc),
        BlocProvider.value(value: _notificationBloc),
      ],
      child: Scaffold(
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    // Handle case where owner data is not available
    if (!_hasInitializedData || owner?.id == null) {
      return _buildOwnerDataMissingState();
    }

    return BlocBuilder<NotificationBloc, NotificationState>(
      builder: (context, notificationState) {
        int notificationCount = 0;
        if (notificationState is NotificationLoaded) {
          notificationCount = notificationState.notifications
              .where((n) => n.isRead == false)
              .length;
        }
        return BlocConsumer<OwnerDashboardBloc, OwnerDashboardState>(
          bloc: _dashboardBloc,
          listener: (context, state) {
            if (state is OwnerDashboardError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  action: SnackBarAction(
                    label: 'Retry',
                    onPressed: _retryDataFetch,
                  ),
                ),
              );
            }
          },
          builder: (context, state) {
            return SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 19.w, vertical: 20.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  customTopBarOnlyIcon(
                    context: context,
                    badgeCount1: notificationCount, // Use dynamic count
                    badgeCount2: 0,
                    icon2: Icons.settings,
                    onTap1: () {
                      NavigatorService.pushNamed(
                        AppRoutes.notification,
                        arguments: owner?.id,
                      );
                    },
                    onTap2: () {
                      NavigatorService.pushNamed(AppRoutes.ownerSettings);
                    },
                  ),
                  SizedBox(height: 24.h),
                  _buildDashboardContent(state),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildOwnerDataMissingState() {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 19.w, vertical: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          customTopBarOnlyIcon(
            context: context,
            badgeCount1: 0, // Remains 0 as no user data
            badgeCount2: 0,
            icon2: Icons.settings,
            onTap1: () {
              NavigatorService.pushNamed(AppRoutes.notification);
            },
            onTap2: () {
              NavigatorService.pushNamed(AppRoutes.ownerSettings);
            },
          ),
          SizedBox(height: 24.h),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64.sp,
                  color: Colors.grey,
                ),
                SizedBox(height: 16.h),
                Text(
                  'Owner Data Not Available',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  'Unable to load owner information.\nPlease try logging in again.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 24.h),
                ElevatedButton(
                  onPressed: _retryDataFetch,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppPallete.buttonColor,
                    padding:
                        EdgeInsets.symmetric(horizontal: 32.w, vertical: 12.h),
                  ),
                  child: Text(
                    'Retry',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardContent(OwnerDashboardState state) {
    if (state is OwnerDashboardLoading) {
      return const OwnerDashboardShimmer();
    }

    if (state is OwnerDashboardError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.sp,
              color: Colors.red,
            ),
            SizedBox(height: 16.h),
            Text(
              'Failed to Load Dashboard',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: Colors.red,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              state.message,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 24.h),
            ElevatedButton(
              onPressed: _retryDataFetch,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppPallete.buttonColor,
                padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 12.h),
              ),
              child: Text(
                'Retry',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (state is OwnerDashboardLoaded) {
      final dashboardData = state.dashboardData;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: "Hello, ${owner?.displayName ?? 'User'}!\n",
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextSpan(
                  text: "Nice to have you back!",
                  style: TextStyle(
                    color: AppPallete.color136,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w300,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 24.h),
          Text(
            "Business Growth",
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 24.h),
          _totalRevenue(dashboardData: dashboardData),
          SizedBox(height: 24.h),
          _incomeAndStudentRow(dashboardData: dashboardData),
          SizedBox(height: 24.h),
          Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(
                    AppPallete.secondaryColor.r.toInt(),
                    AppPallete.secondaryColor.g.toInt(),
                    AppPallete.secondaryColor.b.toInt(),
                    0.3,
                  ),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Button(
              buttonText: "Manage my centre branch",
              fontWeight: FontWeight.w600,
              textSize: 20.sp,
              color: AppPallete.secondaryColor,
              onPressed: () {
                NavigatorService.pushNamed(AppRoutes.ownerManage);
              },
            ),
          ),
          const SizedBox(height: 55),
        ],
      );
    }

    // Handle initial state
    return const OwnerDashboardShimmer();
  }

  Widget _totalRevenue({required OwnerDashboard dashboardData}) {
    final startMonth = _getMonthName(dashboardData.startDate.month);
    final endMonth = _getMonthName(dashboardData.endDate.month);
    final year = dashboardData.endDate.year;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppPallete.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Total Revenue",
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                Row(
                  children: [
                    // Currency toggle button
                    GestureDetector(
                      onTap: _toggleCurrency,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 8.w, vertical: 4.h),
                        decoration: BoxDecoration(
                          color: _showZCoin
                              ? Colors.purple.withAlpha(51)
                              : Colors.green.withAlpha(51),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: _showZCoin ? Colors.purple : Colors.green,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _showZCoin
                                  ? Icons.monetization_on
                                  : Icons.attach_money,
                              size: 14.sp,
                              color: _showZCoin ? Colors.purple : Colors.green,
                            ),
                            SizedBox(width: 2.w),
                            Text(
                              _showZCoin ? 'Z' : 'HKD',
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w600,
                                color:
                                    _showZCoin ? Colors.purple : Colors.green,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: 8.w),
                    // Date range selector
                    GestureDetector(
                      onTap: () => _selectDateRange(context, dashboardData),
                      child: Row(
                        children: [
                          Icon(Icons.calendar_today,
                              size: 16.sp, color: Colors.grey),
                          SizedBox(width: 4.w),
                          Text(
                            "$startMonth - $endMonth $year",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(
            height: 200.h,
            child: _buildRevenueChart(dashboardData: dashboardData),
          ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }

  Future<void> _selectDateRange(
      BuildContext context, OwnerDashboard dashboardData) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      initialDateRange: DateTimeRange(
        start: dashboardData.startDate,
        end: dashboardData.endDate,
      ),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            primaryColor: AppPallete.buttonColor,
            colorScheme: ColorScheme.light(primary: AppPallete.buttonColor),
            buttonTheme:
                const ButtonThemeData(textTheme: ButtonTextTheme.primary),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && owner?.id != null && owner!.id!.isNotEmpty) {
      // Update the dashboard data with new date range
      _dashboardBloc.add(FetchOwnerDashboard(
        ownerId: owner!.id!,
        startDate: picked.start,
        endDate: picked.end,
      ));
    } else if (picked != null) {
      // Show error if owner ID is not available
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content:
              Text('Unable to update date range: Owner data not available'),
        ),
      );
    }
  }

  Widget _incomeAndStudentRow({required OwnerDashboard dashboardData}) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildInfoCard(
                title: 'Total Revenue\n(${_showZCoin ? 'Z Coin' : 'HKD'})',
                value: _formatCurrency(dashboardData.totalRevenue),
                change: dashboardData.monthlyIncomeChange,
                isPositive: dashboardData.monthlyIncomeChange >= 0,
                iconData: null,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildInfoCard(
                title: 'Revenue\n(${_showZCoin ? 'HKD' : 'Z Coin'})',
                value: _showZCoin
                    ? '${_convertZCoinToHkd(dashboardData.totalRevenue).toInt()} HKD'
                    : '${dashboardData.totalRevenue.toInt()} Z',
                change: 0, // No change indicator for alternate currency
                isPositive: true,
                iconData:
                    _showZCoin ? Icons.attach_money : Icons.monetization_on,
                showChangeIndicator: false,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildInfoCard(
                title: 'Total Monthly\nStudent Activity',
                value: '${dashboardData.monthlyStudentActivity}',
                change: dashboardData.studentActivityChange, // Use real data
                isPositive: dashboardData.studentActivityChange >=
                    0, // Dynamic indicator
                iconData: null,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildInfoCard(
                title: 'Monthly\nIncome Change',
                value:
                    '${dashboardData.monthlyIncomeChange.toStringAsFixed(1)}%',
                change: 0, // No nested change indicator
                isPositive: dashboardData.monthlyIncomeChange >= 0,
                iconData: dashboardData.monthlyIncomeChange >= 0
                    ? Icons.trending_up
                    : Icons.trending_down,
                showChangeIndicator: false,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildInfoCard(
                title: 'Total Programs',
                value: '${dashboardData.totalPrograms}',
                change: 0,
                isPositive: true,
                iconData: Icons.school,
                showChangeIndicator: false,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildInfoCard(
                title: 'Total Students',
                value: '${dashboardData.totalStudents}',
                change: 0,
                isPositive: true,
                iconData: Icons.people,
                showChangeIndicator: false,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoCard({
    required String title,
    required String value,
    required double change,
    required bool isPositive,
    IconData? iconData,
    bool showChangeIndicator = true,
  }) {
    // Validate change value
    final validChange = change.isNaN || change.isInfinite ? 0.0 : change;
    return Container(
      padding: EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: AppPallete.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 18.2.sp,
                    fontWeight: FontWeight.w600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (iconData != null)
                Container(
                  width: 32.w,
                  height: 32.h,
                  decoration: BoxDecoration(
                    color: _getIconBackgroundColor(iconData),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Icon(
                      iconData,
                      color: _getIconColor(iconData),
                      size: 18.sp,
                    ),
                  ),
                ),
            ],
          ),
          if (showChangeIndicator) ...[
            SizedBox(height: 8.h),
            Wrap(
              spacing: 8.w,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isPositive
                        ? Color.fromRGBO(
                            AppPallete.color111.r.toInt(),
                            AppPallete.color111.g.toInt(),
                            AppPallete.color111.b.toInt(),
                            0.1,
                          )
                        : Color.fromRGBO(255, 100, 100, 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize:
                        MainAxisSize.min, // Make row take minimum space
                    children: [
                      Icon(
                        isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                        color: isPositive ? AppPallete.color111 : Colors.red,
                        size: 16,
                      ),
                      SizedBox(width: 4),
                      Text(
                        '${validChange.toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: isPositive ? AppPallete.color111 : Colors.red,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  'vs last month',
                  style: TextStyle(
                    fontSize: 11.sp, // Slightly smaller font
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRevenueChart({required OwnerDashboard dashboardData}) {
    // Get monthly revenue data from dashboard
    final monthlyData = dashboardData.monthlyRevenues;

    // If no data, show placeholder
    if (monthlyData.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text(
            'No revenue data available',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    // Validate data integrity
    final validData = monthlyData
        .where((data) => data.month.isNotEmpty && data.revenue >= 0)
        .toList();

    if (validData.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text(
            'Invalid revenue data',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    // Find max value for scaling using validated data
    double maxRevenue = 0;
    for (var item in validData) {
      double revenue =
          _showZCoin ? item.revenue : _convertZCoinToHkd(item.revenue);
      if (revenue > maxRevenue) {
        maxRevenue = revenue;
      }
    }

    // Round up to nearest appropriate value based on currency
    if (_showZCoin) {
      maxRevenue = ((maxRevenue ~/ 5000) + 1) *
          5000.0; // For Z coins, use 5000 intervals
    } else {
      maxRevenue =
          ((maxRevenue ~/ 500) + 1) * 500.0; // For HKD, use 500 intervals
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Y-axis labels
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${maxRevenue.toInt()} ${_getCurrencySymbol()}',
                    style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                  ),
                  SizedBox(height: 40.h),
                  Text(
                    '${(maxRevenue / 2).toInt()} ${_getCurrencySymbol()}',
                    style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                  ),
                  SizedBox(height: 40.h),
                  Text(
                    '0 ${_getCurrencySymbol()}',
                    style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                  ),
                ],
              ),
              SizedBox(width: 8.w),
              // Chart area
              Expanded(
                child: SizedBox(
                  height: 120.h,
                  child: CustomPaint(
                    size: Size.infinite,
                    painter: RevenueChartPainter(
                      monthlyData: validData
                          .map((data) => MonthlyRevenue(
                                month: data.month,
                                revenue: _showZCoin
                                    ? data.revenue
                                    : _convertZCoinToHkd(data.revenue),
                              ))
                          .toList(),
                      maxValue: maxRevenue,
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          // X-axis labels
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: validData.map((data) {
              return Text(
                data.month,
                style: TextStyle(fontSize: 12.sp, color: Colors.grey),
              );
            }).toList(),
          ),
          // Current value indicator
          if (validData.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: 8.h, right: 16.w),
              child: Align(
                alignment: Alignment.centerRight,
                child: Text(
                  _showZCoin
                      ? '${validData.last.revenue.toInt()} Z'
                      : '${_convertZCoinToHkd(validData.last.revenue).toInt()} \$',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
