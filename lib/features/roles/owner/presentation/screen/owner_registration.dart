import 'package:class_z/core/imports.dart';

class OwnerRegistration extends StatefulWidget {
  const OwnerRegistration({super.key});

  @override
  State<OwnerRegistration> createState() => _OwnerRegistrationState();
}

class _OwnerRegistrationState extends State<OwnerRegistration> {
  final formKey = GlobalKey<FormState>();

  final displaynameController = TextEditingController();
  final legalnameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final referalController = TextEditingController();
  final locationController = TextEditingController();
  final labelController = TextEditingController();
  final hkidMainController = TextEditingController();
  final hkidCheckDigitController = TextEditingController();

  final List<String> countryCodes = ['+852', '+99'];
  final List<String> locations = ['Hong Kong', 'BanglaDesh'];

  final ImagePicker _picker = ImagePicker();
  XFile? _image;
  XFile? _hkidImage;
  File? _selectedImageMain;
  List<File>? _selectedImageHKID;
  bool isIndividualCreator = false;
  @override
  void dispose() {
    displaynameController.dispose();
    legalnameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    referalController.dispose();
    locationController.dispose();
    labelController.dispose();
    hkidMainController.dispose();
    hkidCheckDigitController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final pickedImage = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedImage != null) {
      setState(() {
        _selectedImageMain = File(pickedImage.path);
      });
    }
  }

  // Future<void> _pickHKIDImage() async {
  //   final pickedHKID = await _picker.pickImage(source: ImageSource.gallery);
  //   if (pickedHKID != null) {
  //     setState(() => _selectedImageHKID = File(pickedHKID.path));
  //   }
  // }

  void _submitForm() {
    if (formKey.currentState!.validate()) {
      final payload = {
        "fullName": legalnameController.text,
        "displayName": displaynameController.text,
        "phoneNumber": phoneController.text,
        "hkid": "${hkidMainController.text}(${hkidCheckDigitController.text})",
        if (_selectedImageHKID != null) "hkidCard": _selectedImageHKID,
        if (_selectedImageMain != null) "mainImage": _selectedImageMain,
        "isCoach": false,
        "isIndividualCreator": isIndividualCreator,
      };
      print(payload);
      context.read<OwnerBloc>().add(
          UpdateOwnerEvent(locator<SharedRepository>().getOwnerId(), payload));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBarDouble(
        title: "Registration",
        title2: "Business Owner",
        leading: CustomIconButton(
          icon: Icons.arrow_back_ios,
          onPressed: () => NavigatorService.goBack(),
        ),
      ),
      body: BlocListener<OwnerBloc, OwnerState>(
        listener: (context, state) {
          if (state is OwnerLoadingState) {
            loadingState(context: context);
          } else {
            hideLoadingDialog(context);
          }

          if (state is UpdateOwnerSuccessState) {
            errorState(context: context, error: 'Update Done');
            NavigatorService.popAndPushNamed(AppRoutes.ownerMain);
          } else if (state is OwnerErrorState) {
            errorState(context: context, error: state.message);
          }
        },
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 21.w),
            child: Form(
              key: formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildProfileImage(),
                  _buildSectionTitle("Your Information"),
                  _buildTextFieldWithLabel(
                      "Legal Name", legalnameController, "legal name"),
                  _buildTextFieldWithLabel(
                      "Display Name", displaynameController, "display name"),
                  buildStaticEmail(context,
                      locator<SharedRepository>().getOwnerData()?.email ?? ''),
                  _buildPhoneNumberFields(),
                  _buildHKIDLabel(),
                  AlbumCard(
                    onImagesSelected: (images) {
                      setState(() {
                        _selectedImageHKID = images;
                      });
                    },
                  ),
                  _buildIndividualCreatorCheckbox(),
                  const SizedBox(height: 72),
                  Button(
                    buttonText: 'Create',
                    color: AppPallete.buttonColor,
                    onPressed: () {
                      _submitForm();
                    },
                  ),
                  SizedBox(height: 20.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileImage() {
    return SizedBox(
      height: 125.h,
      child: Stack(
        children: [
          PositionedItemWidget(
            top: 65.h,
            left: 0,
            right: 0,
            child: const Divider(height: 12, color: Colors.grey),
          ),
          PositionedItemWidget(
            top: 0.h,
            left: 0,
            right: 0.w,
            child: Center(
              child: buildImage(
                imagePath: _selectedImageMain?.path ?? ImagePath.coach,
                height: 125.h,
                width: 125.w,
                borderRadius: 125.w,
              ),
            ),
          ),
          PositionedItemWidget(
            right: 0,
            bottom: 0.h,
            left: 90.w,
            child: CustomIconButton(
              color: AppPallete.greyColor,
              icon: Icons.camera_alt,
              height: 29.h,
              width: 33.w,
              onPressed: _pickImage,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.only(top: 22.h, bottom: 20.h),
      child: customtext(
        context: context,
        newYear: title,
        font: 17.sp,
        weight: FontWeight.w500,
      ),
    );
  }

  Widget _buildTextFieldWithLabel(
      String label, TextEditingController controller, String hint) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
          context: context,
          title: label,
          font: 17.sp,
          weight: FontWeight.w500,
        ),
        SizedBox(height: 20.h),
        AuthField(
          hintText: hint,
          controller: controller,
          width: 387.w,
          height: 30.h,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '$label is required';
            }
            return null;
          },
        ),
        SizedBox(height: 20.h),
      ],
    );
  }

  Widget _buildPhoneNumberFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
          context: context,
          title: 'Phone Number',
          font: 17.sp,
          weight: FontWeight.w500,
        ),
        SizedBox(height: 20.h),
        Row(
          children: [
            SizedBox(
              width: 65,
              child: DropDown(
                label: "+852",
                times: countryCodes,
                color: AppPallete.paleGrey,
                controller: labelController,
                validator: (value) => value == null || value.isEmpty
                    ? 'Country Code is required'
                    : null,
              ),
            ),
            SizedBox(width: 6.w),
            Expanded(
              child: AuthField(
                hintText: "Phone Number",
                controller: phoneController,
                keyboard: TextInputType.number,
                height: 30.h,
                validator: (value) => value == null || value.isEmpty
                    ? 'Phone number is required'
                    : null,
              ),
            ),
          ],
        ),
        SizedBox(height: 20.h),
      ],
    );
  }

  Widget _buildHKIDLabel() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customRequiredText(
          context: context,
          title: "HKID",
          font: 17.sp,
          weight: FontWeight.w500,
        ),
        const SizedBox(height: 20),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: AuthField(
                hintText: "e.g. A123456",
                controller: hkidMainController,
                width: 260.w,
                height: 30.h,
                validator: _validateHKIDMain,
              ),
            ),
            SizedBox(width: 10.w),
            Text(
              "(",
              style: TextStyle(fontSize: 18.sp),
            ),
            SizedBox(width: 5.w),
            AuthField(
              hintText: "7",
              controller: hkidCheckDigitController,
              width: 40.w,
              height: 30.h,
              validator: _validateHKIDCheckDigit,
            ),
            SizedBox(width: 5.w),
            Text(
              ")",
              style: TextStyle(fontSize: 18.sp),
            ),
          ],
        ),
        SizedBox(height: 20),
      ],
    );
  }

  String? _validateHKIDMain(String? value) {
    print(value);
    final regex = RegExp(r'^[A-Z]{1,2}[0-9]{6}$');
    if (value == null || value.isEmpty) {
      return 'Main HKID is required';
    } else if (!regex.hasMatch(value)) {
      return 'Invalid HKID format';
    }
    return null;
  }

  String? _validateHKIDCheckDigit(String? value) {
    final regex = RegExp(r'^[0-9A]$');
    if (value == null || value.isEmpty) {
      return 'Check digit required';
    } else if (!regex.hasMatch(value)) {
      return 'Invalid check digit';
    }
    return null;
  }

  Widget _buildIndividualCreatorCheckbox() {
    return Padding(
      padding: EdgeInsets.only(top: 20.h, bottom: 20.h),
      child: Row(
        children: [
          Checkbox(
            value: isIndividualCreator,
            onChanged: (value) {
              setState(() {
                isIndividualCreator = value ?? false;
              });
            },
            activeColor: AppPallete.buttonColor,
          ),
          Expanded(
            child: customtext(
              context: context,
              newYear: "I am not an owner, I am an individual creator",
              font: 14.sp,
              weight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  // Widget _buildHKIDCARDIMAGE() {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       customRequiredText(
  //         context: context,
  //         title: 'Please upload your HKID card',
  //         font: 17.sp,
  //         weight: FontWeight.w500,
  //       ),
  //       const SizedBox(height: 20),
  //       InkWell(
  //         onTap: _pickHKIDImage,
  //         child: Container(
  //           color: AppPallete.paleGrey,
  //           height: 103,
  //           width: 107,
  //           child: _hkidImage != null
  //               ? ClipRRect(
  //                   borderRadius: BorderRadius.circular(8),
  //                   child:
  //                       Image.file(File(_hkidImage!.path), fit: BoxFit.cover))
  //               : customSvgPicture(
  //                   imagePath: ImagePath.plusSvg,
  //                   height: 103,
  //                   width: 107,
  //                   color: AppPallete.white,
  //                 ),
  //         ),
  //       ),
  //     ],
  //   );
  // }
}
