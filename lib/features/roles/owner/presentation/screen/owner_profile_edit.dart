import '../../../../../core/imports.dart';

class OwnerProfileEdit extends StatefulWidget {
  final String imagePath;
  final String ownerId;
  const OwnerProfileEdit(
      {required this.ownerId, required this.imagePath, super.key});

  @override
  State<OwnerProfileEdit> createState() => _OwnerProfileEditState();
}

class _OwnerProfileEditState extends State<OwnerProfileEdit> {
  final displaynameController = TextEditingController();
  final fullnameController = TextEditingController();
  final phoneNumberController = TextEditingController();
  final hkidController = TextEditingController();
  final emailaddressController = TextEditingController();
  File? _selectedImage; // Variable to hold the selected image
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _loadOwnerData();
  }

  void _loadOwnerData() {
    print(
        "🔄 [OWNER_PROFILE_EDIT] _loadOwnerData called for ownerId: ${widget.ownerId}");

    // First try to get data from local storage
    final localOwnerData = locator<SharedRepository>().getOwnerData();
    print(
        "🗄️ [OWNER_PROFILE_EDIT] Local owner data: ${localOwnerData?.toString()}");

    if (localOwnerData != null) {
      print("✅ [OWNER_PROFILE_EDIT] Using local owner data for pre-filling");
      _populateFields(localOwnerData);
    } else {
      print("⚠️ [OWNER_PROFILE_EDIT] No local owner data available");
      // Note: GetOwnerEvent is not implemented in the bloc, so we'll rely on local data
      // In a production app, you would implement the API call here
    }
  }

  void _populateFields(OwnerModel owner) {
    print("📝 [OWNER_PROFILE_EDIT] _populateFields called");
    print(
        "📊 [OWNER_PROFILE_EDIT] Owner data: fullName='${owner.fullName}', displayName='${owner.displayName}', email='${owner.email}'");

    setState(() {
      fullnameController.text = owner.fullName ?? '';
      displaynameController.text = owner.displayName ?? '';
      phoneNumberController.text = owner.phoneNumber ?? '';
      emailaddressController.text = owner.email ?? '';
      hkidController.text = owner.hkid ?? '';
    });

    print(
        "✅ [OWNER_PROFILE_EDIT] Fields populated: fullName='${fullnameController.text}', displayName='${displaynameController.text}'");
  }

  Future<void> _pickImage() async {
    final ImagePicker _picker = ImagePicker();
    final XFile? pickedFile =
        await _picker.pickImage(source: ImageSource.gallery);

    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<OwnerBloc, OwnerState>(
      listener: (context, state) {
        if (state is OwnerLoadingState)
          loadingState(context: context);
        else
          hideLoadingDialog(context);
        if (state is OwnerErrorState)
          errorState(context: context, error: state.message);
        if (state is UpdateOwnerSuccessState) {
          errorState(context: context, error: 'Update Done');
          NavigatorService.goBack();
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: CustomAppBar(
          title: "Centre Details",
          leading: customBackButton(),
          actions: [
            Padding(
              padding:
                  EdgeInsets.only(right: 20.w), // Add some padding to the right
              child: GestureDetector(
                onTap: () {
                  Map<String, dynamic> data = {
                    if (_selectedImage != null) "mainImage": _selectedImage,
                    if (displaynameController.text.isNotEmpty)
                      if (displaynameController.text.isNotEmpty)
                        "displayName": displaynameController.text,
                    if (emailaddressController.text.isNotEmpty)
                      "email": emailaddressController.text,
                    if (fullnameController.text.isNotEmpty)
                      "fullName": fullnameController.text,
                    if (phoneNumberController.text.isNotEmpty)
                      "phoneNumber": phoneNumberController.text,
                    if (hkidController.text.isNotEmpty)
                      "hkid": hkidController.text
                  };
                  context
                      .read<OwnerBloc>()
                      .add(UpdateOwnerEvent(widget.ownerId, data));
                },
                child: Center(
                    child: customtext(
                        context: context,
                        newYear: "save",
                        font: 17.sp,
                        weight: FontWeight.w500,
                        color: AppPallete.change)),
              ), // Text on the right
            ),
          ],
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.only(top: 57.h, left: 21.w, right: 21.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(child: _centerImage()),
                SizedBox(
                  height: 20.h,
                ),
                customtext(
                    context: context,
                    newYear: 'Account Details',
                    font: 17.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 20.h,
                ),
                _textbox(
                    context: context,
                    title: 'Full Name',
                    controller: fullnameController),
                SizedBox(
                  height: 20.h,
                ),
                _textbox(
                    context: context,
                    title: 'Display Name',
                    controller: displaynameController),
                SizedBox(
                  height: 20.h,
                ),
                _textbox(
                    context: context,
                    title: 'Phone Number',
                    controller: phoneNumberController),
                SizedBox(
                  height: 20.h,
                ),
                _textbox(
                    context: context,
                    title: 'Email Address',
                    controller: emailaddressController),
                SizedBox(
                  height: 20.h,
                ),
                _textbox(
                    context: context,
                    title: 'HKID',
                    controller: hkidController),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _centerImage() {
    return SizedBox(
      height: 125.h,
      child: Stack(
        children: [
          Positioned(
              left: 0,
              right: 0,
              child: Align(
                alignment: Alignment.center,
                child: CustomImageBuilder(
                    imagePath: _selectedImage != null
                        ? _selectedImage!.path
                        : imageStringGenerator(imagePath: widget.imagePath),
                    height: 125.h,
                    width: 125.r,
                    borderRadius: 99.r),
              )),
          Positioned(
              top: 97.h,
              right: 0.w,
              left: 110.w,
              child: Align(
                alignment: Alignment.center,
                child: CustomIconButton(
                  icon: Icons.camera_alt_rounded,
                  onPressed: _pickImage,
                  height: 28.55.h,
                  width: 32.41.w,
                  color: AppPallete.darkGrey,
                ),
              ))
        ],
      ),
    );
  }

  Widget _textbox({
    required BuildContext context,
    required String title,
    double? width,
    double? height,
    required TextEditingController controller,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            customtext(
                context: context,
                newYear: title,
                font: 15.sp,
                weight: FontWeight.w400),
            customtext(
                context: context,
                newYear: "*",
                font: 15.sp,
                color: AppPallete.red,
                weight: FontWeight.w400),
          ],
        ),
        SizedBox(
          height: 20.h,
        ),
        AuthField(
          controller: controller,
          height: height ?? 30.h,
          width: width ?? double.infinity,
          color: AppPallete.paleGrey,
        ),
      ],
    );
  }
}
