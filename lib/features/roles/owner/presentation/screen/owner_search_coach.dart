import 'package:class_z/core/imports.dart';

class OwnerSearchCoach extends StatefulWidget {
  final Map<String, dynamic> data;
  const OwnerSearchCoach({required this.data, super.key});

  @override
  State<OwnerSearchCoach> createState() => _OwnerSearchCoachState();
}

class _OwnerSearchCoachState extends State<OwnerSearchCoach> {
  late TextEditingController searchController;
  String? lastRequestedCoachId;
  List<String> pendingCoachIds = [];
  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();

    // Validate center ID before making API calls
    final centerId = widget.data['center'];
    if (centerId == null || centerId.isEmpty) {
      print("Error: Center ID is null or empty");
      setState(() {
        pendingCoachIds = [];
      });
      return;
    }

    // Fetch coaches for the center
    try {
      context
          .read<CenterBloc>()
          .add(GetCoachesByCenterIdEvent(centerId: centerId));
    } catch (e) {
      print("Error fetching coaches: $e");
    }

    // Safely handle the request for pending coaches
    try {
      context.read<CenterBloc>().add(GetRequestSendedByCenterToCoachEvent(
          centerId: centerId, type: 'coach'));
    } catch (e) {
      print("Error getting pending requests: $e");
      // Set empty pending list as fallback
      if (mounted) {
        setState(() {
          pendingCoachIds = [];
        });
      }
    }
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  bool _checkCoachIdAndReply({required String checkId}) {
    if (widget.data['manager'] == true) {
      return (widget.data['managerId'] as List<String>).contains(checkId);
    } else {
      return (widget.data['coach'] as List<String>).contains(checkId);
    }
  }

  bool _checkForPendingId(String coachId) {
    // Check for null or empty ID
    if (coachId.isEmpty) {
      return false;
    }

    // Check if it's in the pending list
    return pendingCoachIds.contains(coachId);
  }

  // Add a new method to handle request API errors more gracefully
  void _handleRequestError(String message) {
    // Don't show error message for "No request found" errors
    if (message.contains("No request found") ||
        message.contains("Error getting request by type")) {
      print("No pending requests found - this is normal for a new center");
      if (mounted) {
        setState(() {
          pendingCoachIds = [];
        });
      }
    } else {
      // For other errors, show an error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading pending requests: $message'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomAppBar(
              title: widget.data['name'],
              subtitle: "Center coach",
              leading: customBackButton(),
            ),
            SizedBox(height: 20.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 11.w),
              child: _coach(context: context),
            ),
            SizedBox(
              height: 26.h,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: AuthField(
                controller: searchController,
                hintText: "Coach name, ID",
                height: 30.h,
                suffixIcon: Icon(
                  Icons.search,
                  size: 24.w,
                  color: AppPallete.greyWord,
                ),
                onTap: () {
                  context.read<SearchBloc>().add(SearchQueryEvent(
                      query: searchController.text, isSearchingCoach: true));
                },
              ),
            ),
            SizedBox(
              height: 26.h,
            ),
            BlocConsumer<CenterBloc, CenterState>(
              listener: (context, state) {
                if (state is CenterLoadingState) {
                  // Show loading indicator
                } else if (state is CenterErrorState) {
                  // Handle specific error cases
                  if (state.message.contains("No request found") ||
                      state.message.contains("Error getting request by type")) {
                    // Silently handle "No request found" errors
                    _handleRequestError(state.message);
                  } else {
                    // Show other errors normally
                    errorState(context: context, error: state.message);
                  }
                } else if (state is RequestSendedByCenterToCoachFetchSuccess) {
                  setState(() {
                    // Safely handle null or invalid requestList
                    try {
                      pendingCoachIds = state.requestList != null
                          ? List<String>.from(state.requestList)
                          : [];
                    } catch (e) {
                      print("Error processing request list: $e");
                      pendingCoachIds = [];
                    }
                  });
                }
              },
              builder: (context, state) {
                if (state is CoachListFetchSuccess) {
                  // Check if coaches list is null or empty
                  if (state.coaches.isEmpty) {
                    return Center(
                      child: Padding(
                        padding: EdgeInsets.all(20.h),
                        child: Text(
                          'No coaches found for this center',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: AppPallete.greyWord,
                          ),
                        ),
                      ),
                    );
                  }

                  return ListView.builder(
                      shrinkWrap: true,
                      padding: EdgeInsets.symmetric(horizontal: 11),
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        // Add bounds checking
                        if (index >= state.coaches.length) {
                          return SizedBox.shrink();
                        }

                        final coach = state.coaches[index];

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CenterCoachCard(
                                manager: false,
                                added: _checkCoachIdAndReply(
                                    checkId: coach.center?.id ?? ''),
                                image: imageStringGenerator(
                                    imagePath: coach.mainImage?.url ?? ''),
                                coach: coach.displayName ?? 'Unknown name',
                                location: addressGenerator(
                                    address: coach.address, condition: 'city'),
                                id: coach.classZId ?? '',
                                rating: coach.rating ?? 0,
                                onTap: () {
                                  print('Remove coach');
                                  context.read<OwnerBloc>().add(
                                        RemoveCoachEvent(
                                          coachId: coach.id ?? '',
                                          centerId: widget.data['center'],
                                          type: 'coach',
                                        ),
                                      );
                                }),
                            SizedBox(
                              height: 16.h,
                            )
                          ],
                        );
                      },
                      itemCount: state.coaches.length);
                }
                return SizedBox(
                  child: Center(
                    child: Text('something went wrong'),
                  ),
                );
              },
            ),
            BlocConsumer<SearchBloc, SearchState>(
              listener: (context, state) {
                if (state is SearchLoading) {
                  loadingState(context: context);
                } else
                  hideLoadingDialog(context);
                if (state is SearchError) {
                  // Don't show error message for server errors related to requests
                  if (state.error.contains("No request found")) {
                    print("Ignoring expected error: ${state.error}");
                  } else {
                    errorState(context: context, error: state.error);
                  }
                }
              },
              builder: (context, state) {
                if (state is SearchSuccessState) {
                  // Check if coaches is null or empty
                  if (state.search.coaches == null ||
                      state.search.coaches!.isEmpty) {
                    return Center(
                      child: Text('No coaches found'),
                    );
                  }

                  return ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: state.search.coaches!.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 11.w),
                        child: _searchResult(
                            context: context,
                            coach: state.search.coaches![index],
                            ownManager: widget.data['managerId'] ==
                                    state.search.coaches?[index].id
                                ? true
                                : false),
                      );
                    },
                  );
                }
                return SizedBox(
                  child: Center(
                    child: customtext(
                        context: context, newYear: "NO result here", font: 20),
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }

  Widget _coach({required BuildContext context}) {
    return RichText(
        text: TextSpan(
            style: TextStyle(
                color: AppPallete.darkGrey,
                fontWeight: FontWeight.w300,
                fontSize: 14.sp),
            children: [
          TextSpan(text: "Coaches can access the"),
          TextSpan(
              text: " Center Portal ",
              style: TextStyle(fontWeight: FontWeight.bold)),
          TextSpan(
              text:
                  "in their personal accounts to perform the following tasks:\n\n"),
          WidgetSpan(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _bulletPoint(text: "Take attendance for classes."),
              _bulletPoint(
                  text: "Provide learning performance feedback for students."),
              _bulletPoint(text: "Make announcements related to classes."),
            ],
          )),
          TextSpan(text: "\n\nCoaches"),
          TextSpan(
              text: " cannot access ",
              style: TextStyle(fontWeight: FontWeight.bold)),
          TextSpan(
              text:
                  "financial-related information, modify or set up class schedules, add additional coaches, or communicate with parent users.\ncoaches can be assigned, updated, or revoked by"),
          TextSpan(
              text: " you or the Centre Managers ",
              style: TextStyle(fontWeight: FontWeight.bold)),
          TextSpan(text: "at any time."),
        ]));
  }

  Widget _manager({required BuildContext context}) {
    return RichText(
        text: TextSpan(
            style: TextStyle(
                color: AppPallete.darkGrey,
                fontWeight: FontWeight.w300,
                fontSize: 14.sp),
            children: [
          TextSpan(text: "Managers can access the"),
          TextSpan(
              text: " Centre Portal ",
              style: TextStyle(fontWeight: FontWeight.bold)),
          TextSpan(text: "in their personal account and"),
          TextSpan(
              text: " assign Coaches ",
              style: TextStyle(fontWeight: FontWeight.w700)),
          TextSpan(text: "with full functionality except for:\n\n"),
          WidgetSpan(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _bulletPoint(text: "Deleting or deactivating the centre."),
              _bulletPoint(
                  text:
                      "Viewing or amending bank payout accounts or official business documents (CR/BR)."),
            ],
          )),
          TextSpan(
              text: "\n\nPermissions can be updated or revoked by you anytime.")
        ]));
  }

  Widget _bulletPoint({required String text}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("• ",
            style: TextStyle(
                color: AppPallete.darkGrey,
                fontSize: 14.sp,
                fontWeight: FontWeight.w600)),
        Expanded(
            child: Text(text,
                style: TextStyle(
                    color: AppPallete.darkGrey,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w300))),
      ],
    );
  }

  Widget _searchResult(
      {required BuildContext context,
      required bool ownManager,
      required CoachModel coach}) {
    return Column(
      children: [
        Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(7.0),
              child: Stack(
                children: [
                  CustomImageBuilder(
                    imagePath: imageStringGenerator(
                        imagePath: coach.mainImage?.url ?? ''),
                    height: 100.h,
                    width: 100.w,
                    borderRadius: 20.w,
                  ),
                  Positioned(
                      left: 7.w,
                      top: 8.h,
                      child: customRating(
                          context: context, rating: coach.rating.toString()))
                ],
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(right: 17.w),
                    child: ownManager == true
                        ? _ownManagerOrCoachName(
                            context: context, name: "Athena Young")
                        : customtext(
                            context: context,
                            newYear: coach.displayName ?? 'Unknown',
                            font: 18.sp,
                            weight: FontWeight.w700),
                  ),
                  textWithSvg(
                      context: context,
                      title: addressGenerator(address: coach.address!),
                      imagePath: ImagePath.locationSvg),
                  SizedBox(
                    height: 14.h,
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: 17.w),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        customtext(
                            context: context,
                            newYear: coach.classZId ?? '',
                            font: 15.sp,
                            color: AppPallete.darkGrey,
                            weight: FontWeight.w400),
                        ManagerButton(
                          coachId: coach.id ?? '',
                          centerId: widget.data['center'],
                          type: widget.data['type'],
                          initialTitle:
                              "Assign ${widget.data['manager'] == true ? "manager" : "coach"}",
                          isPending: coach.id != null &&
                              pendingCoachIds.contains(coach.id),
                          onPending: () {
                            if (coach.id != null) {
                              setState(() {
                                pendingCoachIds.add(coach.id!);
                              });
                            }
                          },
                          ownManager: ownManager,
                          initialColor: ownManager
                              ? AppPallete.white
                              : AppPallete.secondaryColor,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        customDivider()
      ],
    );
  }

  Widget _ownManagerOrCoachName(
      {required BuildContext context, required String name}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        customtext(
            context: context,
            newYear: name,
            font: 18.sp,
            weight: FontWeight.w700),
        textWithSvg(
            context: context,
            title: widget.data['manager'] == true ? "manager" : "coach",
            icon: Icons.check)
      ],
    );
  }
}

class ManagerButton extends StatefulWidget {
  final String initialTitle;
  final bool ownManager;
  final Color initialColor;
  final String coachId;
  final String centerId;
  final String type;
  final bool isPending;
  final VoidCallback onPending;

  const ManagerButton(
      {Key? key,
      required this.initialTitle,
      required this.ownManager,
      required this.initialColor,
      required this.coachId,
      required this.centerId,
      required this.type,
      required this.isPending,
      required this.onPending})
      : super(key: key);

  @override
  _ManagerButtonState createState() => _ManagerButtonState();
}

class _ManagerButtonState extends State<ManagerButton> {
  late String title;
  late Color color;

  @override
  void initState() {
    super.initState();
    title = widget.initialTitle;
    color = widget.initialColor;
  }

  void _toggleManager() {
    // if (title.contains('Assign')) {
    //   setState(() {
    //     title = 'Pending Request';
    //     color = AppPallete.greyWord;
    //   });
    // }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        context.read<OwnerBloc>().add(
              RequestCoachtoJoinEvent(
                  coachId: widget.coachId,
                  centerId: widget.centerId,
                  type: widget.type),
            );
        widget.onPending();
      },
      child: BlocConsumer<OwnerBloc, OwnerState>(
        listener: (context, state) {
          print(state);
          if (state is OwnerLoadingState) {
            loadingState(context: context);
          } else {
            hideLoadingDialog(context);
          }
          if (state is OwnerErrorState) {
            // Handle specific errors gracefully
            if (state.message.contains("No request found")) {
              print("Ignoring expected error: ${state.message}");
            } else {
              errorState(context: context, error: state.message);
            }
          }
          if (state is AssignCoachSuccessState) {
            _toggleManager();
          }
        },
        builder: (context, state) {
          print('isPending ${widget.isPending}');
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 5.w),
            decoration: BoxDecoration(
              color: widget.isPending
                  ? AppPallete.greyWord
                  : AppPallete.secondaryColor,
              borderRadius: BorderRadius.circular(5.w),
              boxShadow: [shadow(blurRadius: 15)],
            ),
            child: customtext(
              context: context,
              newYear: widget.isPending
                  ? "Pending Request"
                  : "Assign ${widget.type == "manager" ? "manager" : "coach"}",
              font: 15.sp,
              color: widget.isPending ? AppPallete.black : AppPallete.white,
              weight: FontWeight.w500,
            ),
          );
        },
      ),
    );
  }
}
