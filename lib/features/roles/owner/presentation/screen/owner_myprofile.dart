import 'package:class_z/core/imports.dart';

class OwnerMyProfile extends StatelessWidget {
  const OwnerMyProfile({super.key});

  @override
  Widget build(BuildContext context) {
    OwnerModel? owner = locator<SharedRepository>().getOwnerData();
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
              padding: EdgeInsets.only(left: 19.w, top: 85.h),
              child: customBackButton()),
          Padding(
            padding: EdgeInsets.only(top: 13.w, left: 20.w),
            child: SizedBox(
              height: 36.h,
              child: customtext(
                  context: context,
                  newYear: "My Profile",
                  font: 30.sp,
                  weight: FontWeight.w500),
            ),
          ),
          SizedBox(
            height: 35.h,
          ),
          //Text(userData.user.createdAt, cast as String)
          Padding(
              padding: EdgeInsets.only(top: 28.h, left: 37.w, right: 38.w),
              child: _profile(
                  context: context,
                  ownerId: owner?.id ?? '',
                  info: "referal will added to the new ones",
                  fullName: owner?.fullName ?? "",
                  displayName: owner?.displayName ?? '',
                  email: owner?.email ?? "",
                  number: owner?.phoneNumber ?? "",
                  imagepath: owner?.mainImage?.url ?? '',
                  dateTime: null))
        ],
      ),
    );
  }

  Widget _profile(
      {required BuildContext context,
      required String ownerId,
      required String info,
      required String fullName,
      required String displayName,
      required String email,
      required String number,
      required String imagepath,
      String? dateTime}) {
    String date = dateTime ?? "information will be added soon";
    return Center(
      child: Container(
        //  height: 349.h,
        //  width: 357.w,
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              shadow(blurRadius: 15, opacity: 0.1, xoffset: 0, yoffset: 0),
            ]),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: 150.h,
              // width: 357.w,
              child: Stack(
                children: [
                  Positioned(
                      top: 22.h,
                      right: 19.w,
                      child: InkWell(
                        onTap: () {
                          NavigatorService.pushNamed(AppRoutes.ownerProfieEdit,
                              arguments: {
                                "imagepath": imagepath,
                                "ownerId": ownerId
                              });
                        },
                        child: Text(
                          "edit",
                          style: TextStyle(
                              color: AppPallete.change,
                              fontSize: 17.sp,
                              decoration: TextDecoration.underline,
                              fontWeight: FontWeight.w400),
                        ),
                      )),
                  Align(
                    alignment: Alignment.center,
                    child: CustomImageBuilder(
                      imagePath: imageStringGenerator(imagePath: imagepath),
                      height: 125.w,
                      width: 125.w,
                      borderRadius: 125.w,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 31.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 14.w, right: 17.w),
              child: customtext(
                  context: context,
                  newYear: "Your Information",
                  font: 15.sp,
                  weight: FontWeight.w600,
                  color: AppPallete.darkGrey),
            ),
            SizedBox(
              height: 20.h,
            ),
            _textWithHeader(
                context: context, title: 'Referal Code', subtitle: info),
            SizedBox(
              height: 20.h,
            ),
            _textWithHeader(
                context: context, title: 'Full Name', subtitle: fullName),
            SizedBox(
              height: 20.h,
            ),
            _textWithHeader(
                context: context, title: 'Display Name', subtitle: displayName),
            SizedBox(
              height: 20.h,
            ),
            _textWithHeader(
                context: context, title: 'Email Address', subtitle: email),
            SizedBox(
              height: 20.h,
            ),
            _textWithHeader(
                context: context, title: 'Phone Number', subtitle: number),
            SizedBox(
              height: 20.h,
            ),
            Padding(
              padding: EdgeInsets.only(left: 14.w, right: 17.w),
              child: customtext(
                  context: context,
                  newYear: "Joined ClassZ since $date",
                  font: 15.sp,
                  weight: FontWeight.w500,
                  color: AppPallete.darkGrey),
            ),
            SizedBox(
              height: 31.h,
            )
          ],
        ),
      ),
    );
  }

  Widget _textWithHeader(
      {required BuildContext context,
      required String title,
      required String subtitle}) {
    return Padding(
      padding: const EdgeInsets.only(left: 14, right: 17),
      child: Row(
        children: [
          customtext(
              context: context,
              newYear: "$title:  ",
              font: 15.sp,
              weight: FontWeight.w600,
              color: AppPallete.darkGrey),
          customtext(
              context: context,
              newYear: subtitle,
              font: 15.sp,
              weight: FontWeight.w400,
              color: AppPallete.darkGrey),
        ],
      ),
    );
  }
}
