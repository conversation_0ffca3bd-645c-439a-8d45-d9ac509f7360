import 'package:class_z/core/imports.dart';

class OwnerSearchManager extends StatefulWidget {
  final Map<String, dynamic> data;
  const OwnerSearchManager({required this.data, super.key});

  @override
  State<OwnerSearchManager> createState() => _OwnerSearchManagerState();
}

class _OwnerSearchManagerState extends State<OwnerSearchManager> {
  late TextEditingController searchController;

  @override
  void initState() {
    super.initState();
    searchController = TextEditingController();
    context
        .read<CenterBloc>()
        .add(GetManagersByCenterIdEvent(centerId: widget.data['center']));

    context.read<CenterBloc>().add(GetRequestSendedByCenterToCoachEvent(
        centerId: widget.data['center'], type: 'manager'));
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  String? lastRequestedCoachId;
  List<String> pendingCoachIds = [];
  String removingCoachId = "";
  bool _checkCoachIdAndReply({required String checkId}) {
    print(checkId);
    return (widget.data['managerId'] as List<String>).contains(checkId);
  }

  bool _checkForPendingId(String coachId) {
    // print(pendingCoachIds);
    // print('check id:$coachId');
    print(pendingCoachIds.contains(coachId));
    return pendingCoachIds.contains(coachId);
  }

  List<CoachModel> managers = [];
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomAppBar(
              title: widget.data['name'],
              subtitle: "Center manager",
              leading: customBackButton(),
            ),
            SizedBox(height: 20.h),
            Padding(
                padding: EdgeInsets.symmetric(horizontal: 11.w),
                child: _manager(context: context)),
            SizedBox(
              height: 26.h,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: AuthField(
                controller: searchController,
                hintText: "Coach name, ID",
                height: 30.h,
                suffixIcon: Icon(
                  Icons.search,
                  size: 24.w,
                  color: AppPallete.greyWord,
                ),
                onTap: () {
                  context.read<SearchBloc>().add(SearchQueryEvent(
                      query: searchController.text, isSearchingCoach: true));
                },
              ),
            ),
            SizedBox(
              height: 26.h,
            ),
            BlocConsumer<CenterBloc, CenterState>(
              listener: (context, state) {
                if (state is CenterLoadingState)
                  CircularProgressIndicator();
                else if (state is CenterErrorState)
                  errorState(context: context, error: state.message);
              },
              builder: (context, state) {
                if (state is RequestSendedByCenterToCoachFetchSuccess) {
                  pendingCoachIds = state.requestList;
                }
                if (state is CoachListFetchSuccess) {
                  managers = state.coaches;
                  return BlocListener<CoachBloc, CoachState>(
                    listener: (context, coachState) {
                      if (coachState is LoadingCoachState)
                        loadingState(context: context);
                      else
                        hideLoadingDialog(context);
                      if (coachState is ErrorCoachState) {
                        errorState(context: context, error: coachState.message);
                      } else if (coachState is RemoveCenterSuccessState) {
                        setState(() {
                          print(' hi $removingCoachId');
                          // Assuming you have a coaches list in the parent widget or
                          // in the state management, update it to exclude the removed coach
                          state.coaches.removeWhere(
                              (coach) => coach.id == removingCoachId);
                        });
                      }
                    },
                    child: ListView.builder(
                      shrinkWrap: true,
                      padding: EdgeInsets.symmetric(horizontal: 11),
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.coaches.length,
                      itemBuilder: (context, index) {
                        final coach = state.coaches[index];

                        if (widget.data['managerId'].contains(coach.id)) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CenterManagerCard(
                                added: _checkCoachIdAndReply(
                                    checkId: coach.id ?? ''),
                                image: imageStringGenerator(
                                    imagePath: coach.mainImage?.url ?? ''),
                                coach: coach.displayName ?? 'Unknown name',
                                location: addressGenerator(
                                    address: coach.address, condition: 'city'),
                                classzId: coach.classZId ?? '',
                                coachId: coach.id ?? '',
                                rating: coach.rating ?? 0,
                                onTap: () {
                                  setState(() {
                                    removingCoachId = coach.id ?? '';
                                  });
                                  context.read<CoachBloc>().add(
                                        RemoveCenterEvent(
                                          centerId: widget.data['center'],
                                          coachId: coach.id ?? '',
                                          type: 'manager',
                                          saveData: false,
                                        ),
                                      );
                                },
                              ),
                              SizedBox(height: 16.h),
                            ],
                          );
                        } else {
                          return Container(); // Just return an empty container instead of SizedBox()
                        }
                      },
                    ),
                  );
                }
                return SizedBox(
                  child: Center(
                    child: Text('something went wrong'),
                  ),
                );
              },
            ),
            BlocConsumer<SearchBloc, SearchState>(
              listener: (context, state) {
                if (state is SearchLoading) {
                  loadingState(context: context);
                } else
                  hideLoadingDialog(context);
                if (state is SearchError) {
                  errorState(context: context, error: state.error);
                }
              },
              builder: (context, state) {
                if (state is SearchSuccessState) {
                  if (state.search.coaches?.length == 0) {
                    return Center(
                      child: Text('No coaches on this name'),
                    );
                  }

                  return ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: state.search.coaches!.length,
                    itemBuilder: (context, index) {
                      final coach = state.search.coaches![index];
                      final isOwnManager =
                          widget.data['managerId'].contains(coach.id);

                      if (isOwnManager) {
                        return SizedBox
                            .shrink(); // Don't show anything if already a manager
                      }
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 11.w),
                        child: _searchResult(
                          context: context,
                          coach: state.search.coaches![index],
                        ),
                      );
                    },
                  );
                }
                return SizedBox(
                  child: Center(
                    child: customtext(
                        context: context, newYear: "NO result here", font: 20),
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }

  Widget _manager({required BuildContext context}) {
    return RichText(
        text: TextSpan(
            style: TextStyle(
                color: AppPallete.darkGrey,
                fontWeight: FontWeight.w300,
                fontSize: 14.sp),
            children: [
          TextSpan(text: "Managers can access the"),
          TextSpan(
              text: " Centre Portal ",
              style: TextStyle(fontWeight: FontWeight.bold)),
          TextSpan(text: "in their personal account and"),
          TextSpan(
              text: " assign Coaches ",
              style: TextStyle(fontWeight: FontWeight.w700)),
          TextSpan(text: "with full functionality except for:\n\n"),
          WidgetSpan(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _bulletPoint(text: "Deleting or deactivating the centre."),
              _bulletPoint(
                  text:
                      "Viewing or amending bank payout accounts or official business documents (CR/BR)."),
            ],
          )),
          TextSpan(
              text: "\n\nPermissions can be updated or revoked by you anytime.")
        ]));
  }

  Widget _bulletPoint({required String text}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text("• ",
            style: TextStyle(
                color: AppPallete.darkGrey,
                fontSize: 14.sp,
                fontWeight: FontWeight.w600)),
        Expanded(
            child: Text(text,
                style: TextStyle(
                    color: AppPallete.darkGrey,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w300))),
      ],
    );
  }

  Widget _searchResult({
    required BuildContext context,
    required CoachModel coach,
  }) {
    return BlocConsumer<OwnerBloc, OwnerState>(
      listener: (context, state) {
        if (state is OwnerLoadingState) {
          loadingState(context: context);
        } else {
          hideLoadingDialog(context);
        }

        if (state is OwnerErrorState) {
          errorState(context: context, error: state.message);
        }

        if (state is AssignCoachSuccessState) {
          print('success sending req');
          setState(() {
            if (pendingCoachIds.contains(lastRequestedCoachId)) {
            } else
              pendingCoachIds
                  .add(lastRequestedCoachId ?? ''); // use coachId from state
          });
        }
      },
      builder: (context, state) {
        final bool isPending = _checkForPendingId(coach.id ?? '');

        return Column(
          children: [
            Row(
              children: [
                Padding(
                  padding: const EdgeInsets.all(7.0),
                  child: Stack(
                    children: [
                      CustomImageBuilder(
                        imagePath: imageStringGenerator(
                            imagePath: coach.mainImage?.url ?? ''),
                        height: 100.h,
                        width: 100.w,
                        borderRadius: 20.w,
                      ),
                      Positioned(
                        left: 7.w,
                        top: 8.h,
                        child: customRating(
                            context: context, rating: coach.rating.toString()),
                      )
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(right: 17.w),
                        child: customtext(
                            context: context,
                            newYear: coach.displayName ?? 'Unknown',
                            font: 18.sp,
                            weight: FontWeight.w700),
                      ),
                      textWithSvg(
                          context: context,
                          title: addressGenerator(address: coach.address!),
                          imagePath: ImagePath.locationSvg),
                      SizedBox(height: 14.h),
                      Padding(
                        padding: EdgeInsets.only(right: 17.w),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            customtext(
                                context: context,
                                newYear: coach.classZId ?? '',
                                font: 15.sp,
                                color: AppPallete.darkGrey,
                                weight: FontWeight.w400),
                            ManagerButton(
                              title: "Assign manager",
                              color: AppPallete.secondaryColor,
                              isPending: isPending,
                              onTap: () {
                                lastRequestedCoachId = coach.id;
                                if (!isPending) {
                                  context.read<OwnerBloc>().add(
                                        RequestCoachtoJoinEvent(
                                          coachId: coach.id ?? '',
                                          centerId: widget.data['center'],
                                          type: 'manager',
                                        ),
                                      );
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            customDivider(),
          ],
        );
      },
    );
  }
}

class ManagerButton extends StatelessWidget {
  final String title;
  final Color color;
  final bool isPending;
  final VoidCallback onTap;

  const ManagerButton({
    Key? key,
    required this.title,
    required this.color,
    required this.isPending,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: isPending ? null : onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 5.w),
        decoration: BoxDecoration(
          color: isPending ? AppPallete.greyWord : color,
          borderRadius: BorderRadius.circular(5.w),
          boxShadow: [shadow(blurRadius: 15)],
        ),
        child: customtext(
          context: context,
          newYear: isPending ? "Pending Request" : title,
          font: 15.sp,
          color: isPending ? AppPallete.black : AppPallete.white,
          weight: FontWeight.w500,
        ),
      ),
    );
  }
}

// class ManagerButton extends StatefulWidget {
//   final String initialTitle;
//   final Color initialColor;
//   final String coachId;
//   final String centerId;
//   final String type;
//   final bool isPending;
//   final VoidCallback onPending;

//   const ManagerButton(
//       {Key? key,
//       required this.initialTitle,
//       required this.initialColor,
//       required this.coachId,
//       required this.centerId,
//       required this.type,
//       required this.isPending,
//       required this.onPending})
//       : super(key: key);

//   @override
//   _ManagerButtonState createState() => _ManagerButtonState();
// }

// class _ManagerButtonState extends State<ManagerButton> {
//   late String title;
//   late Color color;
//   bool isPending = false;

//   @override
//   void initState() {
//     print('pending status:${widget.isPending} ${widget.coachId}');
//     super.initState();
//     title = widget.initialTitle;
//     color = widget.initialColor;
//     isPending = widget.isPending;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return BlocConsumer<OwnerBloc, OwnerState>(
//       listener: (context, state) {
//         if (state is OwnerLoadingState) {
//           loadingState(context: context);
//         } else {
//           hideLoadingDialog(context);
//         }

//         if (state is OwnerErrorState) {
//           errorState(context: context, error: state.message);
//         }

//         if (state is AssignCoachSuccessState) {
//           print('success sending req');
//           widget.onPending();
//         }
//       },
//       builder: (context, state) {
//         return InkWell(
//           onTap: () {
//             context.read<OwnerBloc>().add(
//                   RequestCoachtoJoinEvent(
//                     coachId: widget.coachId,
//                     centerId: widget.centerId,
//                     type: widget.type,
//                   ),
//                 );
//           },
//           child: Container(
//             padding: EdgeInsets.symmetric(horizontal: 5.w),
//             decoration: BoxDecoration(
//               color:
//                   isPending ? AppPallete.greyWord : AppPallete.secondaryColor,
//               borderRadius: BorderRadius.circular(5.w),
//               boxShadow: [shadow(blurRadius: 15)],
//             ),
//             child: customtext(
//               context: context,
//               newYear: isPending ? "Pending Request" : "Assign manager",
//               font: 15.sp,
//               color: isPending ? AppPallete.black : AppPallete.white,
//               weight: FontWeight.w500,
//             ),
//           ),
//         );
//       },
//     );
//   }
// }
