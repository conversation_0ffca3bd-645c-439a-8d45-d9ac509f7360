import 'package:class_z/core/imports.dart';

class OwnerAccountSettings extends StatefulWidget {
  const OwnerAccountSettings({super.key});

  @override
  State<OwnerAccountSettings> createState() => _OwnerAccountSettingsState();
}

class _OwnerAccountSettingsState extends State<OwnerAccountSettings> {
  bool _activities = true;
  bool _promotion = false;
  void toggleActivities(bool value) {
    setState(() {
      _activities = value;
    });
  }

  void togglePromotion(bool value) {
    setState(() {
      _promotion = value;
    });
  }

  @override
  Widget build(BuildContext context) {
    final GetIt locator = GetIt.instance;
    final sharedRepository = locator<SharedRepository>();
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _stack(),

          Padding(
            padding: EdgeInsets.only(left: 24.w, top: 18.h),
            child: customtext(
                context: context,
                newYear: "Profile",
                font: 12.sp,
                color: AppPallete.darkGrey,
                weight: FontWeight.w400),
          ),
          profile(
              context: context,
              name: "My Profile",
              iconData: Icons.person,
              onTap: () {
                NavigatorService.pushNamed(
                  AppRoutes.ownerMyProfile,
                );
              }),
          SizedBox(
            height: 6.h,
          ),
          customDivider(padding: 24.w),
          SizedBox(
            height: 14.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 24.w),
            child: customtext(
                context: context,
                newYear: "Preference",
                font: 15.sp,
                color: AppPallete.darkGrey,
                weight: FontWeight.w600),
          ),

          ///Preference

          profile(context: context, name: "Language", iconData: Icons.language),
          SizedBox(
            height: 6.h,
          ),
          customDivider(padding: 24.w, right: 24.w),

          SizedBox(
            height: 6.h,
          ),
          notification(
            context: context,
            name: "Activities Notification",
            iconData: Icons.notifications,
            onChanged: toggleActivities,
            switchValue: _activities,
          ),

          customDivider(padding: 24.w, right: 24.w),
          SizedBox(
            height: 6.h,
          ),
          notification(
            context: context,
            name: "Promotion Notification",
            iconData: Icons.telegram,
            onChanged: togglePromotion,
            switchValue: _promotion,
          ),

          customDivider(padding: 24.w, right: 24.w),

          ///Privacy

          SizedBox(
            height: 17.h,
          ),
          Padding(
            padding: EdgeInsets.only(left: 24.w),
            child: customtext(
                context: context,
                newYear: "Privacy",
                font: 15.sp,
                color: AppPallete.darkGrey,
                weight: FontWeight.w600),
          ),

          profile(
              context: context,
              name: "change password",
              iconData: Icons.fingerprint,
              onTap: () {
                // Get user email from shared repository
                final userData = locator<SharedRepository>().getUserData();
                final userEmail = userData?.data?.owner?.email;

                if (userEmail != null && userEmail.isNotEmpty) {
                  print("Navigating to reset password with email: $userEmail");
                  NavigatorService.pushNamed(AppRoutes.resetPassword,
                      arguments: userEmail);
                } else {
                  // Handle missing email
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content:
                          Text('Email not available. Please log in again.'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                  print("Owner email not available for Reset Password");
                }
              }),
          SizedBox(
            height: 6.h,
          ),
          customDivider(padding: 24.w, right: 24.w),

          profile(
              context: context,
              name: "Terms and Condition",
              iconData: Icons.description,
              onTap: () {
                NavigatorService.pushNamed(AppRoutes.terms);
              }),
          SizedBox(
            height: 6.h,
          ),
          customDivider(padding: 24.w, right: 24.w),
          SizedBox(
            height: 12.h,
          ),

          ///Assistance

          Padding(
            padding: EdgeInsets.only(left: 24.w),
            child: customtext(
                context: context,
                newYear: "Assistance",
                font: 15.sp,
                color: AppPallete.darkGrey,
                weight: FontWeight.w600),
          ),

          profile(
              context: context,
              name: "Help Centre",
              iconData: Icons.help_outlined,
              onTap: () {
                NavigatorService.pushNamed(AppRoutes.faq);
              }),
          SizedBox(
            height: 6.h,
          ),
          customDivider(padding: 24.w, right: 24.w),
          SizedBox(
            height: 6.h,
          ),
          profile(
              context: context,
              name: "Contact Us",
              iconData: Icons.chat,
              onTap: () {
                // Get owner email from shared repository
                final userData = locator<SharedRepository>().getUserData();
                final ownerEmail = userData?.data?.owner?.email;

                NavigatorService.pushNamed(
                  AppRoutes.contactUs,
                  arguments: ownerEmail,
                );
              }),
          SizedBox(
            height: 6.h,
          ),
          customDivider(padding: 24.w, right: 24.w),
          SizedBox(
            height: 60.h,
          ),
          Center(
            child: Button(
                buttonText: "Log Out",
                color: AppPallete.secondaryColor,
                width: 289.w,
                height: 49.h,
                onPressed: () async {
                  await sharedRepository.logout();

                  // Navigate to login screen
                  NavigatorService.pushNamedAndRemoveUntil(AppRoutes.logIn);
                }),
          ),
        ],
      ),
    );
  }

  Widget _stack() {
    return Container(
        height: 156, // Ensure ScreenUtil is initialized
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: GradientProvider.getLinearGradient(),
        ),
        child: Align(
          alignment: Alignment.topLeft,
          child: Padding(
            padding: EdgeInsets.only(top: 80, left: 19),
            child: customBackButton(color: Colors.white),
          ),
        ));
  }

  Widget profile(
      {required BuildContext context,
      required String name,
      required IconData iconData,
      VoidCallback? onTap}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: InkWell(
        onTap: onTap,
        child: SizedBox(
          // height: 33.h,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    iconData,
                    color: AppPallete.darkGrey,
                    size: 30.w,
                  ),
                  SizedBox(
                    width: 26.w,
                  ),
                  customtext(
                      context: context,
                      newYear: name,
                      font: 15.sp,
                      weight: FontWeight.w500),
                ],
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: AppPallete.darkGrey,
                size: 24.w,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget notification(
      {required BuildContext context,
      required String name,
      required IconData iconData,
      required Function(bool) onChanged,
      required bool switchValue}) {
    return Padding(
      padding: EdgeInsets.only(left: 24.w, right: 27.w),
      child: SizedBox(
        height: 33.h,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(
                  iconData,
                  color: AppPallete.darkGrey,
                  size: 30.w,
                ),
                SizedBox(
                  width: 26.w,
                ),
                customtext(
                    context: context,
                    newYear: name,
                    font: 15.sp,
                    weight: FontWeight.w500),
              ],
            ),
            Transform.scale(
              scale: 0.65, // Adjusted scale factor to get close to 32x18
              child: Switch(
                value: switchValue,
                activeColor: AppPallete.secondaryColor,
                onChanged: onChanged,
              ),
            )
          ],
        ),
      ),
    );
  }
}
