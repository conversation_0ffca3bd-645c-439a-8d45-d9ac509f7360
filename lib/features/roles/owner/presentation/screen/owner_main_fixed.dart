import 'package:class_z/core/imports.dart';
import 'package:class_z/features/roles/owner/domain/entities/owner_dashboard.dart';
import 'package:class_z/features/roles/owner/domain/usecases/get_owner_dashboard_data.dart' as usecase;
import 'package:class_z/features/roles/owner/presentation/bloc/owner_dashboard_bloc.dart';

class OwnerMain extends StatefulWidget {
  const OwnerMain({super.key, required this.user});

  final UserModel user;

  @override
  State<OwnerMain> createState() => _OwnerMainState();
}

// Custom painter for the revenue chart
class RevenueChartPainter extends CustomPainter {
  final List<MonthlyRevenue> monthlyData;
  final double maxValue;
  
  RevenueChartPainter({
    required this.monthlyData,
    required this.maxValue,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    if (monthlyData.isEmpty) return;
    
    final Paint linePaint = Paint()
      ..color = Colors.green.shade300
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    final Paint fillPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Colors.green.shade200.withAlpha(128),
          Colors.green.shade50.withAlpha(25),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
      ..style = PaintingStyle.fill;
    
    final Paint dotPaint = Paint()
      ..color = Colors.green.shade400
      ..style = PaintingStyle.fill;
    
    final Paint dotStrokePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    final double width = size.width;
    final double height = size.height;
    final int dataPoints = monthlyData.length;
    
    // Skip if not enough data points
    if (dataPoints < 2) return;
    
    final double horizontalStep = width / (dataPoints - 1);
    
    // Create path for the line
    final Path linePath = Path();
    final Path fillPath = Path();
    
    // Start at the first point
    final double firstX = 0;
    final double firstY = height - (monthlyData[0].revenue / maxValue * height);
    linePath.moveTo(firstX, firstY);
    fillPath.moveTo(firstX, height); // Start at bottom left for fill
    fillPath.lineTo(firstX, firstY); // Go to first data point
    
    // Add points to the paths
    for (int i = 0; i < dataPoints; i++) {
      final double x = i * horizontalStep;
      final double y = height - (monthlyData[i].revenue / maxValue * height);
      
      if (i > 0) {
        // For smooth curve, use quadratic bezier
        final double prevX = (i - 1) * horizontalStep;
        final double prevY = height - (monthlyData[i - 1].revenue / maxValue * height);
        
        final double controlX = (prevX + x) / 2;
        
        linePath.quadraticBezierTo(controlX, prevY, x, y);
        fillPath.quadraticBezierTo(controlX, prevY, x, y);
      }
    }
    
    // Complete the fill path
    fillPath.lineTo(width, height); // Bottom right
    fillPath.lineTo(0, height); // Bottom left
    fillPath.close();
    
    // Draw the paths
    canvas.drawPath(fillPath, fillPaint);
    canvas.drawPath(linePath, linePaint);
    
    // Draw dots at each data point
    for (int i = 0; i < dataPoints; i++) {
      final double x = i * horizontalStep;
      final double y = height - (monthlyData[i].revenue / maxValue * height);
      
      // Draw highlight dot for the last point
      if (i == dataPoints - 1) {
        canvas.drawCircle(Offset(x, y), 6, dotPaint);
        canvas.drawCircle(Offset(x, y), 6, dotStrokePaint);
      }
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class _OwnerMainState extends State<OwnerMain> {
  late final OwnerDashboardBloc _dashboardBloc;
  final notificationservices = locator<Notificationservice>();
  bool _isPermissionGranted = true;
  OwnerModel? owner;
  
  @override
  void initState() {
    super.initState();
    _checkNotificationPermission();
    owner = locator<SharedRepository>().getOwnerData();
    _dashboardBloc = OwnerDashboardBloc(
      getOwnerDashboardData: GetIt.I<usecase.GetOwnerDashboardData>(),
    );
    _fetchDashboardData();
  }

  void _fetchDashboardData() {
    if (owner?.id != null) {
      _dashboardBloc.add(FetchOwnerDashboard(ownerId: owner!.id!));
    }
  }

  @override
  void dispose() {
    _dashboardBloc.close();
    super.dispose();
  }

  void _checkNotificationPermission() async {
    NotificationSettings settings =
        await FirebaseMessaging.instance.getNotificationSettings();
    setState(() {
      _isPermissionGranted =
          settings.authorizationStatus == AuthorizationStatus.authorized;
      if (!_isPermissionGranted) {
        notificationservices.requestNotificationPermission(context);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return BlocConsumer<OwnerDashboardBloc, OwnerDashboardState>(
      bloc: _dashboardBloc,
      listener: (context, state) {
        if (state is OwnerDashboardError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      builder: (context, state) {
        return SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 19.w, vertical: 20.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              customTopBarOnlyIcon(
                context: context,
                badgeCount1: 0,
                badgeCount2: 0,
                icon2: Icons.settings,
                onTap1: () {
                  NavigatorService.pushNamed(
                    AppRoutes.notification,
                    arguments: owner?.id,
                  );
                },
                onTap2: () {
                  NavigatorService.pushNamed(AppRoutes.ownerSettings);
                },
              ),
              SizedBox(height: 24.h),
              _buildDashboardContent(state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDashboardContent(OwnerDashboardState state) {
    if (state is OwnerDashboardLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is OwnerDashboardError) {
      return Center(child: Text('Error: ${state.message}'));
    }

    if (state is OwnerDashboardLoaded) {
      final dashboardData = state.dashboardData;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: "Hello, ${owner?.displayName ?? 'User'}!\n",
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextSpan(
                  text: "Nice to have you back!",
                  style: TextStyle(
                    color: AppPallete.color136,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w300,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 24.h),
          Text(
            "Business Growth",
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 24.h),
          _totalRevenue(dashboardData: dashboardData),
          SizedBox(height: 24.h),
          _incomeAndStudentRow(dashboardData: dashboardData),
          SizedBox(height: 24.h),
          Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(
                    AppPallete.secondaryColor.r.toInt(),
                    AppPallete.secondaryColor.g.toInt(),
                    AppPallete.secondaryColor.b.toInt(),
                    0.3,
                  ),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Button(
              buttonText: "Manage my centre branch",
              fontWeight: FontWeight.w600,
              textSize: 20.sp,
              color: AppPallete.secondaryColor,
              onPressed: () {
                NavigatorService.pushNamed(AppRoutes.ownerManage);
              },
            ),
          ),
          const SizedBox(height: 55),
        ],
      );
    }

    return const SizedBox.shrink();
  }

  Widget _totalRevenue({required OwnerDashboard dashboardData}) {
    final now = DateTime.now();
    final startMonth = 'Jan';
    final endMonth = _getMonthName(now.month);
    final year = now.year;
    
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppPallete.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Total Revenue",
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                Row(
                  children: [
                    Icon(Icons.calendar_today, size: 16.sp, color: Colors.grey),
                    SizedBox(width: 4.w),
                    Text(
                      "$startMonth - $endMonth $year",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(
            height: 200.h,
            child: _buildRevenueChart(dashboardData: dashboardData),
          ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }
  
  String _getMonthName(int month) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[month - 1];
  }

  Widget _incomeAndStudentRow({required OwnerDashboard dashboardData}) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildInfoCard(
                title: 'Total Monthly\nIncome Change',
                value: '${dashboardData.totalRevenue.toInt()} HKD',
                change: dashboardData.monthlyIncomeChange,
                isPositive: dashboardData.monthlyIncomeChange >= 0,
                iconData: null,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildInfoCard(
                title: 'Total Monthly\nStudent Activity',
                value: '${dashboardData.monthlyStudentActivity}',
                change: 20.0, // Fixed value as per design
                isPositive: false, // Red indicator as per design
                iconData: null,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildInfoCard(
                title: 'Total Programs',
                value: '${dashboardData.totalPrograms}',
                change: 0,
                isPositive: true,
                iconData: Icons.school,
                showChangeIndicator: false,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: _buildInfoCard(
                title: 'Total Students',
                value: '${dashboardData.totalStudents}',
                change: 0,
                isPositive: true,
                iconData: Icons.people,
                showChangeIndicator: false,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoCard({
    required String title,
    required String value,
    required double change,
    required bool isPositive,
    IconData? iconData,
    bool showChangeIndicator = true,
  }) {
    return Container(
      padding: EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: AppPallete.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (iconData != null)
                Container(
                  width: 32.w,
                  height: 32.h,
                  decoration: BoxDecoration(
                    color: iconData == Icons.school
                        ? Colors.purple.withAlpha(51)
                        : Colors.blue.withAlpha(51),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Icon(
                      iconData,
                      color: iconData == Icons.school
                          ? Colors.purple
                          : Colors.blue,
                      size: 18.sp,
                    ),
                  ),
                ),
            ],
          ),
          if (showChangeIndicator) ...[  
            SizedBox(height: 8.h),
            Row(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isPositive
                        ? Color.fromRGBO(
                            AppPallete.color111.r.toInt(),
                            AppPallete.color111.g.toInt(),
                            AppPallete.color111.b.toInt(),
                            0.1,
                          )
                        : Color.fromRGBO(255, 100, 100, 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                        color: isPositive ? AppPallete.color111 : Colors.red,
                        size: 16,
                      ),
                      SizedBox(width: 4),
                      Text(
                        '${change.toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: isPositive ? AppPallete.color111 : Colors.red,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 8.w),
                Text(
                  'vs last month',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRevenueChart({required OwnerDashboard dashboardData}) {
    // Get monthly revenue data from dashboard
    final monthlyData = dashboardData.monthlyRevenues;
    
    // If no data, show placeholder
    if (monthlyData.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text(
            'Revenue chart will be displayed here',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }
    
    // Find max value for scaling
    double maxRevenue = 0;
    for (var item in monthlyData) {
      if (item.revenue > maxRevenue) {
        maxRevenue = item.revenue;
      }
    }
    
    // Round up to nearest 500 for nice y-axis labels
    maxRevenue = ((maxRevenue ~/ 500) + 1) * 500.0;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Y-axis labels
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${maxRevenue.toInt()} \$',
                    style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                  ),
                  SizedBox(height: 40.h),
                  Text(
                    '${(maxRevenue / 2).toInt()} \$',
                    style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                  ),
                  SizedBox(height: 40.h),
                  Text(
                    '0 \$',
                    style: TextStyle(fontSize: 12.sp, color: Colors.grey),
                  ),
                ],
              ),
              SizedBox(width: 8.w),
              // Chart area
              Expanded(
                child: SizedBox(
                  height: 120.h,
                  child: CustomPaint(
                    size: Size.infinite,
                    painter: RevenueChartPainter(
                      monthlyData: monthlyData,
                      maxValue: maxRevenue,
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          // X-axis labels
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: monthlyData.map((data) {
              return Text(
                data.month,
                style: TextStyle(fontSize: 12.sp, color: Colors.grey),
              );
            }).toList(),
          ),
          // Current value indicator
          if (monthlyData.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: 8.h, right: 16.w),
              child: Align(
                alignment: Alignment.centerRight,
                child: Text(
                  '${monthlyData.last.revenue.toInt()} \$',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
