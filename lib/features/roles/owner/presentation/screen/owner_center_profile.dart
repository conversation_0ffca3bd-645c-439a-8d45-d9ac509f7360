import 'package:class_z/core/imports.dart';

class OwnerCenterProfile extends StatelessWidget {
  final CenterData branch;
  const OwnerCenterProfile({required this.branch, super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 19),
        child: SingleChildScrollView(
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            SizedBox(
              height: 85.h,
            ),
            customBackButton(),
            SizedBox(
              height: 13.h,
            ),
            customtext(
                context: context,
                newYear: "Center Profile",
                font: 30.sp,
                weight: FontWeight.w500),
            SizedBox(
              height: 49.h,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 18.w),
              child: _centerProfile(
                  context: context,
                  verified: branch.verified ?? false,
                  centerId: branch.id ?? '',
                  imagePath: branch.mainImage?.url ?? "",
                  legalName: branch.legalName ?? 'Unknown',
                  displayName: branch.displayName ?? 'Unknown',
                  address: branch.address ?? Address(),
                  companyRegistrationNumber: branch.centerNumber ?? "Unknown",
                  businessRegistrationNumber:
                      branch.businessNumber ?? "Unknown",
                  centerPhoneNumber: branch.centerNumber ?? "Unknown",
                  centerEmailAddress: branch.email ?? "Unknown"),
            ),
            SizedBox(
              height: 24.h,
            ),
            editProfile(
              context: context,
              title: "Manager",
              onTap: () {
                print(branch.managers);
                Map<String, dynamic> data = {
                  "name": "${branch.displayName} - ${branch.address?.city}",
                  "manager": true,
                  "center": branch.id,
                  "managerId": branch.managers,
                  "type": "manager"
                };
                NavigatorService.pushNamed(AppRoutes.ownerManagerEdit,
                    arguments: data);
              },
            ),
            SizedBox(
              height: 20.h,
            ),
            editProfile(
              context: context,
              title: "Coach",
              onTap: () {
                Map<String, dynamic> data = {
                  "name": "${branch.displayName} - ${branch.address?.city}",
                  "center": branch.id,
                  "manager": false,
                  "coach": branch.coachs,
                  "type": "coach"
                };
                NavigatorService.pushNamed(AppRoutes.ownerCoachEdit,
                    arguments: data);
              },
            ),
            SizedBox(
              height: 20.h,
            ),
            editProfile(
              context: context,
              title: "Business",
              onTap: () {
                NavigatorService.pushNamed(AppRoutes.centerBusiness,
                    arguments: branch.id);
              },
            ),
            SizedBox(
              height: 20.h,
            ),
            editProfile(
              context: context,
              title: "Payout Details",
              onTap: () {
                NavigatorService.pushNamed(AppRoutes.centerPayout,
                    arguments: branch.id);
              },
            ),
            SizedBox(
              height: 20.h,
            ),
            editProfile(
              context: context,
              title: "Center Description",
              onTap: () {
                NavigatorService.pushNamed(AppRoutes.centerDescription,
                    arguments: branch.id);
              },
            ),
            SizedBox(
              height: 20.h,
            ),
          ]),
        ),
      ),
    );
  }

  Widget _centerProfile(
      {required BuildContext context,
      required String centerId,
      required bool verified,
      required String imagePath,
      required String legalName,
      required String displayName,
      required Address address,
      required String companyRegistrationNumber,
      required String businessRegistrationNumber,
      required String centerPhoneNumber,
      required String centerEmailAddress}) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
          color: AppPallete.white,
          boxShadow: [
            shadow(blurRadius: 15, opacity: 0.1),
          ],
          borderRadius: BorderRadius.circular(20.w)),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            picAndEdit(
              context: context,
              imagePath: imagePath,
              onTap: () {
                NavigatorService.pushNamed(AppRoutes.centerDetails, arguments: {
                  'imagePath': imagePath,
                  'centerId': centerId,
                  'verified': verified
                });
              },
            ),
            SizedBox(
              height: 22.h,
            ),
            customtext(
                context: context,
                newYear: "Center Information",
                font: 20.sp,
                weight: FontWeight.w500),
            SizedBox(
              height: 22.h,
            ),
            customtext(
                context: context,
                newYear: "Status",
                font: 16.sp,
                weight: FontWeight.w500),
            activeOrNot(context: context, active: false),
            SizedBox(
              height: 22.h,
            ),
            titleAndSubtitle(
                context: context, title: "Legal Name", subtitle: legalName),
            SizedBox(
              height: 22.h,
            ),
            titleAndSubtitle(
                context: context, title: "Display Name", subtitle: displayName),
            SizedBox(
              height: 22.h,
            ),
            _buildExpandableAddress(context: context, address: address),
            SizedBox(
              height: 22.h,
            ),
            titleAndSubtitle(
                context: context,
                title: "Company Registration Number",
                subtitle: branch.isFreelanceEducator == true
                    ? "Individual Educator"
                    : companyRegistrationNumber),
            SizedBox(
              height: 22.h,
            ),
            titleAndSubtitle(
                context: context,
                title: "Business Registration Number",
                subtitle: branch.isFreelanceEducator == true
                    ? "Individual Educator"
                    : businessRegistrationNumber),
            SizedBox(
              height: 22.h,
            ),
            titleAndSubtitle(
                context: context,
                title: "Center Phone number",
                subtitle: centerPhoneNumber),
            SizedBox(
              height: 22.h,
            ),
            titleAndSubtitle(
                context: context,
                title: "Center Email Address",
                subtitle: centerEmailAddress)
          ],
        ),
      ),
    );
  }

  Widget _buildExpandableAddress(
      {required BuildContext context, required Address address}) {
    String fullAddress = addressGenerator(address: address);

    // If the address is too long, show expandable version
    if (fullAddress.length > 100) {
      return _ExpandableAddressWidget(fullAddress: fullAddress);
    } else {
      // If address is short, show normally
      return titleAndSubtitle(
          context: context, title: "Address", subtitle: fullAddress);
    }
  }
}

class _ExpandableAddressWidget extends StatefulWidget {
  final String fullAddress;

  const _ExpandableAddressWidget({
    required this.fullAddress,
  });

  @override
  State<_ExpandableAddressWidget> createState() =>
      _ExpandableAddressWidgetState();
}

class _ExpandableAddressWidgetState extends State<_ExpandableAddressWidget> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customtext(
            context: context,
            newYear: "Address",
            font: 16.sp,
            weight: FontWeight.w500),
        SizedBox(height: 8.h),
        customtext(
            context: context,
            newYear: isExpanded
                ? widget.fullAddress
                : '${widget.fullAddress.substring(0, 100)}...',
            font: 14.sp,
            weight: FontWeight.w400,
            color: AppPallete.darkGrey),
        SizedBox(height: 8.h),
        InkWell(
          onTap: () {
            print("Show more/less tapped. Current state: $isExpanded");
            setState(() {
              isExpanded = !isExpanded;
            });
            print("New state: $isExpanded");
          },
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 4.h, horizontal: 8.w),
            child: Text(
              isExpanded ? 'Show less' : 'Show more',
              style: TextStyle(
                color: AppPallete.change,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
