import 'package:equatable/equatable.dart';

class OwnerDashboard extends Equatable {
  final double totalRevenue;
  final double monthlyIncomeChange;
  final int monthlyStudentActivity;
  final double studentActivityChange; // Added field for student activity change
  final int totalPrograms;
  final int totalStudents;
  final List<MonthlyRevenue> monthlyRevenues;
  final DateTime startDate; // Added for date range
  final DateTime endDate; // Added for date range

  OwnerDashboard({
    required this.totalRevenue,
    required this.monthlyIncomeChange,
    required this.monthlyStudentActivity,
    this.studentActivityChange = 0.0,
    required this.totalPrograms,
    required this.totalStudents,
    required this.monthlyRevenues,
    DateTime? startDate,
    DateTime? endDate,
  }) : this.startDate = startDate ?? DateTime(DateTime.now().year, 1, 1),
       this.endDate = endDate ?? DateTime.now();

  @override
  List<Object> get props => [
        totalRevenue,
        monthlyIncomeChange,
        monthlyStudentActivity,
        studentActivityChange,
        totalPrograms,
        totalStudents,
        monthlyRevenues,
        startDate,
        endDate,
      ];
}

class MonthlyRevenue extends Equatable {
  final String month;
  final double revenue;

  const MonthlyRevenue({
    required this.month,
    required this.revenue,
  });

  @override
  List<Object> get props => [month, revenue];
}
