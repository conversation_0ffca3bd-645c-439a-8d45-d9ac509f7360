import 'package:class_z/core/error/failures.dart';
import 'package:class_z/features/roles/owner/domain/repositories/owner_dashboard_repository.dart';
import 'package:class_z/features/roles/owner/data/models/owner_dashboard_model.dart';
import 'package:dartz/dartz.dart';

class GetOwnerDashboardData {
  final OwnerDashboardRepository repository;

  GetOwnerDashboardData(this.repository);

  Future<Either<Failure, OwnerDashboardModel>> call(
    String ownerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return await repository.getOwnerDashboardData(
      ownerId,
      startDate: startDate,
      endDate: endDate,
    );
  }
}
