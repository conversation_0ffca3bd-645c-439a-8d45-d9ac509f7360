import 'package:class_z/features/roles/owner/data/models/owner_dashboard_model.dart'
    as model;
import 'package:class_z/features/roles/owner/domain/entities/owner_dashboard.dart'
    as entity;

class OwnerDashboardMapper {
  static entity.OwnerDashboard toEntity(model.OwnerDashboardModel model) {
    final now = DateTime.now();
    return entity.OwnerDashboard(
      totalRevenue: model.totalRevenue,
      monthlyIncomeChange: model.monthlyIncomeChange,
      monthlyStudentActivity: model.monthlyStudentActivity,
      studentActivityChange: model.studentActivityChange,
      totalPrograms: model.totalPrograms,
      totalStudents: model.totalStudents,
      monthlyRevenues: model.monthlyRevenues
          .map((e) => entity.MonthlyRevenue(month: e.month, revenue: e.revenue))
          .toList(),
      startDate: model.startDate ??
          DateTime(now.year, 1,
              1), // Use model date or default to start of current year
      endDate:
          model.endDate ?? now, // Use model date or default to current date
    );
  }
}
