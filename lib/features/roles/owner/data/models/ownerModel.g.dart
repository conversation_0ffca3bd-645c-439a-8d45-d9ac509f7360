// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ownerModel.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class OwnerModelAdapter extends TypeAdapter<OwnerModel> {
  @override
  final int typeId = 30;

  @override
  OwnerModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return OwnerModel(
      baseUser: fields[0] as String,
      fullName: fields[1] as String?,
      displayName: fields[2] as String?,
      email: fields[3] as String?,
      phoneNumber: fields[4] as String?,
      hkid: fields[5] as String?,
      mainImage: fields[6] as BusinessCertificate?,
      isCoach: fields[7] as bool?,
      id: fields[8] as String?,
      coachId: fields[9] as String?,
      classZId: fields[10] as String?,
      isIndividualCreator: fields[11] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, OwnerModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.baseUser)
      ..writeByte(1)
      ..write(obj.fullName)
      ..writeByte(2)
      ..write(obj.displayName)
      ..writeByte(3)
      ..write(obj.email)
      ..writeByte(4)
      ..write(obj.phoneNumber)
      ..writeByte(5)
      ..write(obj.hkid)
      ..writeByte(6)
      ..write(obj.mainImage)
      ..writeByte(7)
      ..write(obj.isCoach)
      ..writeByte(8)
      ..write(obj.id)
      ..writeByte(9)
      ..write(obj.coachId)
      ..writeByte(10)
      ..write(obj.classZId)
      ..writeByte(11)
      ..write(obj.isIndividualCreator);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OwnerModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
