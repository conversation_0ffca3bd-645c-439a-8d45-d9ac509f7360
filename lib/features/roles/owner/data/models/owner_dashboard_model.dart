class OwnerDashboardModel {
  final double totalRevenue;
  final double monthlyIncomeChange;
  final int monthlyStudentActivity;
  final double studentActivityChange;
  final int totalPrograms;
  final int totalStudents;
  final List<MonthlyRevenue> monthlyRevenues;
  final DateTime? startDate;
  final DateTime? endDate;

  OwnerDashboardModel({
    required this.totalRevenue,
    required this.monthlyIncomeChange,
    required this.monthlyStudentActivity,
    this.studentActivityChange = 0.0,
    required this.totalPrograms,
    required this.totalStudents,
    required this.monthlyRevenues,
    this.startDate,
    this.endDate,
  });

  factory OwnerDashboardModel.fromJson(Map<String, dynamic> json) {
    return OwnerDashboardModel(
      totalRevenue: (json['totalRevenue'] as num).toDouble(),
      monthlyIncomeChange: (json['monthlyIncomeChange'] as num).toDouble(),
      monthlyStudentActivity: json['monthlyStudentActivity'] as int,
      studentActivityChange: json.containsKey('studentActivityChange')
          ? (json['studentActivityChange'] as num).toDouble()
          : 0.0,
      totalPrograms: json['totalPrograms'] as int,
      totalStudents: json['totalStudents'] as int,
      monthlyRevenues: (json['monthlyRevenues'] as List)
          .map((e) => MonthlyRevenue.fromJson(e))
          .toList(),
      startDate: json['startDate'] != null
          ? DateTime.tryParse(json['startDate'])
          : null,
      endDate:
          json['endDate'] != null ? DateTime.tryParse(json['endDate']) : null,
    );
  }
}

class MonthlyRevenue {
  final String month;
  final double revenue;

  MonthlyRevenue({required this.month, required this.revenue});

  factory MonthlyRevenue.fromJson(Map<String, dynamic> json) {
    return MonthlyRevenue(
      month: json['month'] as String,
      revenue: (json['revenue'] as num).toDouble(),
    );
  }
}
