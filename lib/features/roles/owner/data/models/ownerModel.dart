// To parse this JSON data, do
//
//     final ownerModel = ownerModelFromJson(jsonString);

import 'package:class_z/core/imports.dart';

part 'ownerModel.g.dart';

OwnerModel ownerModelFromJson(String str) =>
    OwnerModel.fromJson(json.decode(str));

String ownerModelToJson(OwnerModel data) => json.encode(data.toJson());

@HiveType(typeId: 30)
class OwnerModel extends HiveObject {
  @HiveField(0)
  String baseUser;

  @HiveField(1)
  String? fullName;

  @HiveField(2)
  String? displayName;

  @HiveField(3)
  String? email;

  @HiveField(4)
  String? phoneNumber;

  @HiveField(5)
  String? hkid;

  @HiveField(6)
  BusinessCertificate? mainImage;

  @HiveField(7)
  bool? isCoach;
  @HiveField(8)
  String? id;
  @HiveField(9)
  String? coachId;
  @HiveField(10)
  String? classZId;
  @HiveField(11)
  bool? isIndividualCreator;

  OwnerModel(
      {required this.baseUser,
      this.fullName,
      this.displayName,
      this.email,
      this.phoneNumber,
      this.hkid,
      this.mainImage,
      this.isCoach,
      this.id,
      this.coachId,
      this.classZId,
      this.isIndividualCreator});

  factory OwnerModel.fromJson(Map<String, dynamic> json) => OwnerModel(
      baseUser: json["baseUser"] != null ? json["baseUser"] : '',
      fullName: json["fullName"] != null ? json["fullName"] : '',
      displayName: json["displayName"] != null ? json["displayName"] : '',
      email: json["email"] != null ? json["email"] : '',
      phoneNumber: json["phoneNumber"] != null ? json["phoneNumber"] : '',
      hkid: json["hkid"],
      mainImage: json["mainImage"] == null
          ? null
          : BusinessCertificate.fromJson(json["mainImage"]),
      isCoach: json["isCoach"] == null ? null : json["isCoach"],
      id: json["_id"] == null ? null : json["_id"],
      coachId: json['coach'] != null ? json['coach'] : null,
      classZId: json['classzId'] != null ? json['classzId'] : null,
      isIndividualCreator: json['isIndividualCreator'] != null
          ? json['isIndividualCreator']
          : false);

  Map<String, dynamic> toJson() => {
        "baseUser": baseUser,
        "fullName": fullName,
        "displayName": displayName,
        "email": email,
        "phoneNumber": phoneNumber,
        "hkid": hkid,
        "mainImage": mainImage?.toJson(),
        "isCoach": isCoach,
        "_id": id,
        'coach': coachId,
        'classzId': classZId,
        'isIndividualCreator': isIndividualCreator
      };
}
