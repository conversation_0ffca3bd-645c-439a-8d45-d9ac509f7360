import 'package:class_z/core/error/exceptions.dart';
import 'package:class_z/core/network/api_service.dart';
import 'package:class_z/features/roles/owner/data/models/owner_dashboard_model.dart';
import 'package:class_z/core/utils/shared_repo.dart';

abstract class OwnerDashboardRemoteDataSource {
  Future<OwnerDashboardModel> getOwnerDashboardData(
    String ownerId, {
    DateTime? startDate,
    DateTime? endDate,
  });
}

class OwnerDashboardRemoteDataSourceImpl
    implements OwnerDashboardRemoteDataSource {
  final ApiService apiService;
  final SharedRepository sharedRepository;

  OwnerDashboardRemoteDataSourceImpl({
    required this.apiService,
    required this.sharedRepository,
  });

  @override
  Future<OwnerDashboardModel> getOwnerDashboardData(
    String ownerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      // Validate owner ID
      if (ownerId.isEmpty) {
        throw ServerException(message: 'Owner ID cannot be empty');
      }

      // Get the current user's token from SharedRepository
      final token = sharedRepository.getToken();

      if (token == null || token.isEmpty) {
        throw ServerException(message: 'Authentication token not found');
      }

      // Prepare query parameters for date range if provided
      Map<String, dynamic> queryParams = {};
      if (startDate != null) {
        queryParams['startDate'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['endDate'] = endDate.toIso8601String();
      }

      // The backend expects the endpoint as /api/owner/:id/dashboard with auth token
      final response = await apiService.get(
        '/api/owner/$ownerId/dashboard',
        token: token,
        queryParameters: queryParams,
      );

      // Validate response
      if (response == null) {
        throw ServerException(message: 'Received null response from server');
      }

      if (response is! Map<String, dynamic>) {
        throw ServerException(message: 'Invalid response format from server');
      }

      // Validate required fields
      final requiredFields = [
        'totalRevenue',
        'monthlyIncomeChange',
        'monthlyStudentActivity',
        'totalPrograms',
        'totalStudents',
        'monthlyRevenues'
      ];
      for (final field in requiredFields) {
        if (!response.containsKey(field)) {
          throw ServerException(message: 'Missing required field: $field');
        }
      }

      return OwnerDashboardModel.fromJson(response);
    } catch (e) {
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException(
          message: 'Failed to load dashboard data: ${e.toString()}');
    }
  }
}
