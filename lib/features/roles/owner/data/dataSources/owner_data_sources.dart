import 'package:class_z/core/imports.dart';
import 'package:dio/dio.dart' as dio;

abstract class OwnerDataSources {
  Future<List<CenterData>> getBranchesByOwner(String ownerId);
  Future<bool> updateOwner(
      {required String ownerId, required Map<String, dynamic> data});
  Future<bool> deleteBranch({required String branchId});
  Future<bool> updateBranch({
    required String branchId,
    required Map<String, dynamic> data,
  });
  Future<bool> requestCoachToJoin(
      {required String centerId,
      required String coachId,
      required String type});
  Future<bool> removeCoach(
      {required String centerId,
      required String coachId,
      required String type});
  // Future<bool> requestManagerToJoin(
  //     {required String centerId, required String managerId});
  // Future<bool> removeManager(
  //     {required String centerId, required String managerId});
}

class OwnerDataSourcesImpl extends OwnerDataSources {
  final ApiService apiService;
  final dio.Dio dioClient;
  OwnerDataSourcesImpl({required this.apiService, required this.dioClient});
  final sharedRepository = locator<SharedRepository>();
  final device = AppText.device;
  @override
  Future<List<CenterData>> getBranchesByOwner(String ownerId) async {
    try {
      // Use the ApiService instead of direct HTTP request for better error handling
      final token = sharedRepository.getToken();
      if (token == null || token.isEmpty) {
        throw Exception('Authentication token not found');
      }

      final response = await apiService.get(
        '/api/owner/branch/$ownerId',
        token: token,
      );

      if (response is List) {
        List<CenterData> branches = response
            .map((e) => CenterData.fromJson(e as Map<String, dynamic>))
            .toList();
        return branches;
      } else {
        // Handle case where response is not a list
        print('Unexpected response format: $response');
        return [];
      }
    } catch (e) {
      print('Error fetching branches: ${e.toString()}');
      // Return empty list instead of throwing to avoid UI errors
      return [];
    }
  }

  @override
  Future<bool> updateOwner(
      {required String ownerId, required Map<String, dynamic> data}) async {
    final formData = await buildFormData(data);
    final token = sharedRepository.getToken();

    if (token == null || token.isEmpty) {
      throw Exception('Authentication token not found');
    }

    print('r u here');
    String url = '$device/api/owner/$ownerId';
    var response = await dioClient.put(
      url,
      data: formData,
      options: dio.Options(
        headers: {
          'auth-token': token,
          'Content-Type': 'multipart/form-data', // Ensure Content-Type is set
        },
      ),
    );
    print(response.statusCode!);
    if (response.statusCode! >= 200 && response.statusCode! <= 300) {
      print('response $response');
      OwnerModel updatedOwner = OwnerModel.fromJson(response.data);
      locator<SharedRepository>().updateOwnerData(updatedOwner);
      return true;
    }

    throw Exception();
  }

  @override
  Future<bool> deleteBranch({required String branchId}) async {
    try {
      print(branchId);
      final response = await apiService.delete('/api/owner/branch/$branchId');

      if (response == true ||
          (response is Map && response['success'] == true)) {
        return true;
      } else {
        return true; // Assume success if we got a response without error
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<bool> updateBranch({
    required String branchId,
    required Map<String, dynamic> data,
  }) {
    // TODO: implement updateBranch
    throw UnimplementedError();
  }

  @override
  Future<bool> requestCoachToJoin({
    required String centerId,
    required String coachId,
    required String type,
  }) async {
    try {
      print('/api/coach/assign');
      final response = await apiService.post(
        '/api/coach/assign',
        {
          'centerId': centerId,
          'coachId': coachId,
          'type': type,
          'notificationId': ''
        },
      );

      print("Me");
      print(response);

      if (response == true ||
          (response is Map && response['success'] == true)) {
        return true;
      } else if (response is Map && response.containsKey('message')) {
        throw ServerException(response['message']);
      } else {
        throw ServerException("Unknown error occurred");
      }
    } catch (e) {
      // If the exception is already a ServerException, just rethrow
      if (e is ServerException) rethrow;

      // If it's a generic exception, wrap it
      throw ServerException(e.toString());
    }
  }

  @override
  Future<bool> removeCoach(
      {required String centerId,
      required String coachId,
      required String type}) async {
    final response = await apiService.post('/api/center/removeCoach',
        {'centerId': centerId, 'coachId': coachId, 'status': type});
    print("Me");
    print(response);
    if (response == true) {
      return true;
    } else
      throw ServerException();
  }

  // @override
  // Future<bool> removeManager(
  //     {required String centerId, required String managerId}) {
  //   // TODO: implement removeManager
  //   throw UnimplementedError();
  // }
  //   @override
  // Future<bool> requestManagerToJoin(
  //     {required String centerId,
  //     required String managerId,
  //     required String type}) {
  //   // TODO: implement assignManager
  //   throw UnimplementedError();
  // }
}
