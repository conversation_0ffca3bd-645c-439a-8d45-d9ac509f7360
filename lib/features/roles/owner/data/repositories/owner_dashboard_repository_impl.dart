import 'package:class_z/core/error/failures.dart';
import 'package:class_z/core/error/exceptions.dart';
import 'package:class_z/features/roles/owner/data/datasources/owner_dashboard_remote_data_source.dart';
import 'package:class_z/features/roles/owner/domain/repositories/owner_dashboard_repository.dart';
import 'package:class_z/features/roles/owner/data/models/owner_dashboard_model.dart';
import 'package:dartz/dartz.dart';

class OwnerDashboardRepositoryImpl implements OwnerDashboardRepository {
  final OwnerDashboardRemoteDataSource remoteDataSource;

  OwnerDashboardRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, OwnerDashboardModel>> getOwnerDashboardData(
    String ownerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final dashboardData = await remoteDataSource.getOwnerDashboardData(
        ownerId,
        startDate: startDate,
        endDate: endDate,
      );
      return Right(dashboardData);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on Exception catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }
}
