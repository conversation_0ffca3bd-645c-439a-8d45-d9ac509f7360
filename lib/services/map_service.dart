import 'dart:async';
import 'dart:io' show Platform;
import 'package:class_z/features/roles/center/data/models/center_model.dart' show Address;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:flutter/material.dart';

class MapService {
  // Default coordinates for Hong Kong
  static const LatLng defaultHongKongCoordinates = LatLng(22.3193, 114.1694);
  
  /// Check if a string is a Google Maps link
  bool isGoogleMapsLink(String? text) {
    if (text == null || text.isEmpty) return false;
    
    return text.contains('maps.google.com') || 
           text.contains('goo.gl/maps') || 
           text.contains('google.com/maps');
  }
  
  /// Extracts coordinates from a Google Maps URL if available
  /// Returns null if no coordinates can be found
  LatLng? extractCoordinatesFromMapUrl(String? url) {
    if (url == null || url.isEmpty) return null;
    
    try {
      // Handle directions format: https://www.google.com/maps/dir//.../@lat,lng,zoom/data=...
      if (url.contains('/maps/dir/')) {
        // Try to extract from the end part of the URL
        RegExp dirCoordinatesPattern = RegExp(r'@(-?\d+\.\d+),(-?\d+\.\d+)');
        final dirMatches = dirCoordinatesPattern.firstMatch(url);
        
        if (dirMatches != null && dirMatches.groupCount >= 2) {
          double lat = double.parse(dirMatches.group(1)!);
          double lng = double.parse(dirMatches.group(2)!);
          return LatLng(lat, lng);
        }
        
        // If coordinates weren't in the URL path, check for data parameters
        RegExp dataLatPattern = RegExp(r'!2d(-?\d+\.\d+)');
        RegExp dataLngPattern = RegExp(r'!3d(-?\d+\.\d+)');
        
        final latMatch = dataLatPattern.firstMatch(url);
        final lngMatch = dataLngPattern.firstMatch(url);
        
        if (latMatch != null && lngMatch != null) {
          double lng = double.parse(latMatch.group(1)!); // Note: In !2d is actually longitude
          double lat = double.parse(lngMatch.group(1)!); // And !3d is latitude
          return LatLng(lat, lng);
        }
      }
      
      // Handle format: https://www.google.com/maps/@lat,lng,zoom
      RegExp atCoordinatesPattern = RegExp(r'@(-?\d+\.\d+),(-?\d+\.\d+)');
      final atMatches = atCoordinatesPattern.firstMatch(url);
      
      if (atMatches != null && atMatches.groupCount >= 2) {
        double lat = double.parse(atMatches.group(1)!);
        double lng = double.parse(atMatches.group(2)!);
        return LatLng(lat, lng);
      }
      
      // Handle format: https://www.google.com/maps/search/?api=1&query=lat,lng
      RegExp queryCoordinatesPattern = RegExp(r'query=(-?\d+\.\d+),(-?\d+\.\d+)');
      final queryMatches = queryCoordinatesPattern.firstMatch(url);
      
      if (queryMatches != null && queryMatches.groupCount >= 2) {
        double lat = double.parse(queryMatches.group(1)!);
        double lng = double.parse(queryMatches.group(2)!);
        return LatLng(lat, lng);
      }
      
      // Handle format: https://www.google.com/maps/place/.../@lat,lng,zoom
      RegExp placeCoordinatesPattern = RegExp(r'/place/[^@]*@(-?\d+\.\d+),(-?\d+\.\d+)');
      final placeMatches = placeCoordinatesPattern.firstMatch(url);
      
      if (placeMatches != null && placeMatches.groupCount >= 2) {
        double lat = double.parse(placeMatches.group(1)!);
        double lng = double.parse(placeMatches.group(2)!);
        return LatLng(lat, lng);
      }
    } catch (e) {
      print('Error extracting coordinates from URL: $e');
    }
    
    return null;
  }
  
  /// Get coordinates for an address
  Future<LatLng> getCoordinatesForAddress(Address? address) async {
    if (address == null) {
      return defaultHongKongCoordinates;
    }
    
    // Check if address2 contains a Google Maps link
    if (isGoogleMapsLink(address.address2)) {
      print('Address contains Google Maps link in address2');
      
      // Try to extract coordinates from the Google Maps link
      final coordinates = extractCoordinatesFromMapUrl(address.address2);
      if (coordinates != null) {
        print('Successfully extracted coordinates from Google Maps link: ${coordinates.latitude}, ${coordinates.longitude}');
        return coordinates;
      }
      
      print('Could not extract coordinates from Google Maps link, using default coordinates for display');
      return defaultHongKongCoordinates;
    }
    
    // Check if the address already has coordinates
    if (address.coordinates != null) {
      try {
        final coordinates = address.coordinates;
        if (coordinates != null && coordinates['lat'] != null && coordinates['lng'] != null) {
          double lat = double.parse(coordinates['lat'].toString());
          double lng = double.parse(coordinates['lng'].toString());
          return LatLng(lat, lng);
        }
      } catch (e) {
        print('Error parsing coordinates from address: $e');
      }
    }
    
    // If no coordinates, use default
    return defaultHongKongCoordinates;
  }
  
  /// Generate a formatted address string
  String getAddressString(Address? address) {
    if (address == null) return '';
    
    // Check if address2 is a Google Maps link
    final bool address2IsMapLink = isGoogleMapsLink(address.address2);
    
    final parts = [
      if (address.address1 != null && address.address1!.isNotEmpty) address.address1,
      // Only include address2 if it's not a Google Maps link
      if (!address2IsMapLink && address.address2 != null && address.address2!.isNotEmpty) address.address2,
      if (address.city != null && address.city!.isNotEmpty) address.city,
      if (address.region != null && address.region!.isNotEmpty) address.region,
      if (address.country != null && address.country!.isNotEmpty) address.country else 'Hong Kong'
    ];
    
    return parts.join(', ');
  }

  /// Get address2 only string
  String getAddress2String(Address? address) {
    if (address == null || address.address2 == null || address.address2!.isEmpty) {
      return '';
    }
    return address.address2!;
  }
  
  /// Get map markers for a center's address
  Set<Marker> getMarkerForCenter(String centerId, Address? address, LatLng position) {
    return {
      Marker(
        markerId: MarkerId(centerId),
        position: position,
        infoWindow: InfoWindow(
          title: address?.city ?? 'Location',
          snippet: getAddressString(address),
        ),
      ),
    };
  }
  
  /// Launch Google Maps with the specified coordinates
  Future<bool> launchGoogleMaps(LatLng coordinates, {String? label, BuildContext? context}) async {
    try {
      String url;
      
      if (Platform.isIOS) {
        // iOS-specific format
        url = 'https://maps.apple.com/?ll=${coordinates.latitude},${coordinates.longitude}';
        if (label != null && label.isNotEmpty) {
          url += '&q=${Uri.encodeComponent(label)}';
        }
      } else {
        // Android and other platforms
        url = 'https://www.google.com/maps/search/?api=1&query=${coordinates.latitude},${coordinates.longitude}';
      }
      
      final success = await launchUrlString(url);
      if (!success && context != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open maps application'))
        );
      }
      return success;
    } catch (e) {
      print('Error launching maps: $e');
      if (context != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error opening maps: $e'))
        );
      }
      return false;
    }
  }
  
  /// Launch Google Maps with an address query
  /// If useOnlyAddress2 is true, it will use only the address2 field (if available)
  Future<bool> launchGoogleMapsWithAddress(String address, {BuildContext? context, bool useOnlyAddress2 = false, Address? addressObj}) async {
    try {
      // First, check if the address2 is a Google Maps link with coordinates
      if (useOnlyAddress2 && addressObj != null && addressObj.address2 != null) {
        if (isGoogleMapsLink(addressObj.address2)) {
          // Special handling for directions URLs
          if (addressObj.address2!.contains('/maps/dir/')) {
            print('Direct launching directions URL: ${addressObj.address2}');
            final success = await launchUrlString(addressObj.address2!);
            if (!success && context != null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Could not open maps application'))
              );
            }
            return success;
          }
          
          // Try to extract coordinates from the link
          final coordinates = extractCoordinatesFromMapUrl(addressObj.address2);
          if (coordinates != null) {
            // Use the extracted coordinates
            return launchGoogleMaps(coordinates, context: context);
          }
          
          // If coordinates extraction failed, just try to launch the URL directly
          final success = await launchUrlString(addressObj.address2!);
          if (!success && context != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Could not open maps application'))
            );
          }
          return success;
        }
      }
      
      String queryAddress = address;
      
      // If useOnlyAddress2 is true and addressObj is provided, use only address2
      if (useOnlyAddress2 && addressObj != null && addressObj.address2 != null && addressObj.address2!.isNotEmpty) {
        queryAddress = addressObj.address2!;
      }
      
      final encodedAddress = Uri.encodeComponent(queryAddress);
      String url;
      
      if (Platform.isIOS) {
        // iOS-specific format
        url = 'https://maps.apple.com/?q=$encodedAddress';
      } else {
        // Android and other platforms
        url = 'https://www.google.com/maps/search/?api=1&query=$encodedAddress';
      }
      
      final success = await launchUrlString(url);
      if (!success && context != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open maps application'))
        );
      }
      return success;
    } catch (e) {
      print('Error launching maps with address: $e');
      if (context != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error opening maps: $e'))
        );
      }
      return false;
    }
  }
} 