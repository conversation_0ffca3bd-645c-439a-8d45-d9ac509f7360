import 'package:class_z/core/utils/auth_helper.dart';
import 'package:class_z/core/common/data/models/user_address_model.dart';
import 'package:class_z/core/imports.dart';
import 'package:class_z/core/common/domain/repositories/saved_center_repository.dart';
import 'package:class_z/core/common/data/repositories/saved_center_repository_impl.dart';
import 'package:class_z/core/common/presentation/blocs/savedCenterBloc/saved_center_bloc.dart';
import 'package:class_z/core/services/course_progress_service.dart';
import 'package:class_z/features/roles/parent/domain/usecases/get_child_performance_metrics_usecase.dart';
import 'package:class_z/features/roles/parent/domain/usecases/get_parent_pending_reviews_usecase.dart';
import 'package:class_z/features/roles/parent/domain/usecases/get_parent_review_history_usecase.dart';

import 'package:class_z/features/roles/parent/data/repositories/user_repository_impl.dart';
import 'package:class_z/services/location_service.dart';
import 'package:class_z/services/map_service.dart';
import 'package:dio/dio.dart';

import 'package:http/http.dart' as http;

import 'package:class_z/features/chats/data/repositories/chat_local_repository.dart';
import 'package:class_z/features/roles/owner/data/datasources/owner_dashboard_remote_data_source.dart';
import 'package:class_z/features/roles/owner/data/repositories/owner_dashboard_repository_impl.dart';
import 'package:class_z/features/roles/owner/domain/repositories/owner_dashboard_repository.dart';
import 'package:class_z/features/roles/owner/domain/usecases/get_owner_dashboard_data.dart';

import 'package:class_z/core/common/domain/usecase/cancel_join_request_use_case.dart';

// Campaign imports
import 'package:class_z/core/common/data/data_sources/campaign_data_source.dart';
import 'package:class_z/core/common/data/repos/campaign_repo.dart';

// Initialize the service locator
final GetIt locator = GetIt.instance;

Future<void> setupLocator() async {
  locator.registerLazySingleton<FirebaseService>(() => FirebaseService());

  // Register the badge count service as a singleton
  locator.registerLazySingleton<BadgeCountService>(() => BadgeCountService());

  // Register location service
  locator.registerLazySingleton<LocationService>(() => LocationService());

  // Register map service
  locator.registerLazySingleton<MapService>(() => MapService());

  // Register course progress service
  locator.registerLazySingleton<CourseProgressService>(
      () => CourseProgressService());

  // Initialize Hive
  if (!Hive.isAdapterRegistered(UserModelAdapter().typeId)) {
    await Hive.initFlutter();

    Hive.registerAdapter(UserModelAdapter());
    Hive.registerAdapter(DataAdapter());
    Hive.registerAdapter(ParentData1Adapter());
    Hive.registerAdapter(ParentImageAdapter());
    Hive.registerAdapter(UserAddressAdapter());
    Hive.registerAdapter(CoachModelAdapter());
    Hive.registerAdapter(CoachAddressAdapter());
    Hive.registerAdapter(CenterDataAdapter());
    Hive.registerAdapter(CenterImageAdapter());
    Hive.registerAdapter(BusinessCertificateAdapter());
    Hive.registerAdapter(AddressAdapter());
    Hive.registerAdapter(BankDetailsAdapter());
    Hive.registerAdapter(OpeningHourAdapter());
    Hive.registerAdapter(ExperienceAdapter());
    Hive.registerAdapter(SkillAdapter());
    Hive.registerAdapter(AccredationAdapter());
    Hive.registerAdapter(OwnerModelAdapter());
    Hive.registerAdapter(ChatModelAdapter());
  }

  // Initialize SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();
  locator.registerSingleton<SharedPreferences>(sharedPreferences);

  // Initialize HTTP client
  final client = http.Client();
  locator.registerSingleton<http.Client>(client);

  ///Service

  locator
      .registerLazySingleton<Notificationservice>(() => Notificationservice());
  // Initialize repositories and data sources
  locator.registerSingletonAsync<SharedRepository>(() async {
    return await SharedRepository.create(sharedPreferences: sharedPreferences);
  });

  // Register AuthHelper for token validation and auth error handling
  locator.registerLazySingleton<AuthHelper>(() {
    if (!locator.isReadySync<SharedRepository>()) {
      print(
          "Attempted to access AuthHelper before SharedRepository is ready. Ensure locator.allReady() is awaited in main.dart.");
      throw Exception("AuthHelper created before SharedRepository is ready.");
    }
    return AuthHelper(locator<SharedRepository>());
  });

  locator.registerSingleton<Dio>(Dio());
  locator.registerLazySingleton<ApiService>(
      () => ApiService(baseUrl: '${AppText.device}'));
  locator.registerLazySingleton<AuthDataSource>(() => AuthDataSourceImpl(
      client: client,
      sharedRepository: locator<SharedRepository>(),
      dioClient: locator<Dio>()));
  locator.registerLazySingleton<AuthRepositoryImpl>(
      () => AuthRepositoryImpl(locator<AuthDataSource>()));

  locator.registerLazySingleton<CenterDataSource>(() => CenterDataSourceImpl(
      client: client,
      dioClient: locator<Dio>(),
      sharedRepository: locator<SharedRepository>(),
      apiService: locator<ApiService>()));
  locator.registerLazySingleton<CenterRepoImpl>(
      () => CenterRepoImpl(centerDataSource: locator<CenterDataSource>()));

  locator.registerLazySingleton<UserDataSource>(() => UserDataSourceImpl(
      sharedRepository: locator<SharedRepository>(),
      client: client,
      dio: locator<Dio>()));
  locator.registerLazySingleton<UserRepoImpl>(
      () => UserRepoImpl(userDataSource: locator<UserDataSource>()));
  locator.registerLazySingleton<UserRepositoryImpl>(
      () => UserRepositoryImpl(remoteDataSource: locator<UserDataSource>()));

  locator.registerLazySingleton<CoachDataSource>(() => CoachDataSourceImpl(
      client: client,
      apiService: locator<ApiService>(),
      sharedRepository: locator<SharedRepository>(),
      dio: locator<Dio>()));
  locator.registerLazySingleton<CoachRepoImpl>(
      () => CoachRepoImpl(coachDataSource: locator<CoachDataSource>()));

  locator.registerLazySingleton<ReviewDataSources>(() => ReviewDataSourcesImpl(
      apiService: locator<ApiService>(),
      dioClient: locator<Dio>(),
      client: client,
      sharedRepository: locator<SharedRepository>()));
  locator.registerLazySingleton<ReviewRepoImpl>(
      () => ReviewRepoImpl(reviewDataSources: locator<ReviewDataSources>()));
  locator.registerLazySingleton<SubscriptionDataSources>(() =>
      SubscriptionDataSourcesImpl(
          client: client, sharedRepository: locator<SharedRepository>()));
  locator.registerLazySingleton<SubscriptionRepoImpl>(() =>
      SubscriptionRepoImpl(
          subscribeDataSources: locator<SubscriptionDataSources>()));
  // locator.registerLazySingleton<ChatDataSource>(() => ChatDataSourceImpl());

// // Register the ChatRepoImpl
//   locator.registerLazySingleton<ChatRepository>(
//       () => ChatRepositoryImpl(locator<ChatDataSource>()));
// // Register Use Cases

//   locator.registerLazySingleton<SendMessage>(
//       () => SendMessage(locator<ChatRepository>()));
//   locator.registerLazySingleton<ConnectSocket>(
//       () => ConnectSocket(locator<ChatRepository>()));
//   locator.registerLazySingleton<DisconnectSocket>(
//       () => DisconnectSocket(locator<ChatRepository>()));
//   locator.registerLazySingleton<FetchMessages>(
//       () => FetchMessages(locator<ChatRepository>()));
  locator
      .registerLazySingleton<SearchDataSources>(() => SearchDataSourcesImpl());
  locator.registerLazySingleton<SearchRepoDomain>(
    () => SearchRepoImpl(locator<SearchDataSources>()),
  );
  locator.registerLazySingleton<OwnerDataSources>(
    () => OwnerDataSourcesImpl(
        apiService: locator<ApiService>(), dioClient: locator<Dio>()),
  );
  locator.registerLazySingleton<OwnerRepoImpl>(
      () => OwnerRepoImpl(locator<OwnerDataSources>()));

  locator.registerLazySingleton<NotificationRemoteDataSource>(() =>
      NotificationRemoteDataSourceImpl(apiService: locator<ApiService>()));
  locator.registerLazySingleton<NotificationRepository>(
      () => NotificationRepository(locator<NotificationRemoteDataSource>()));
  // Register SavedCenterRepository
  locator.registerLazySingleton<SavedCenterRepository>(
    () => SavedCenterRepositoryImpl(),
  );

  // Register SavedCenterBloc
  locator.registerFactory(
    () => SavedCenterBloc(
      savedCenterRepository: locator<SavedCenterRepository>(),
      sharedRepository: locator<SharedRepository>(),
    ),
  );

  // Register Campaign dependencies
  locator.registerLazySingleton<CampaignDataSource>(
    () => CampaignDataSourceImpl(locator<ApiService>()),
  );

  locator.registerLazySingleton<CampaignRepoImpl>(
    () => CampaignRepoImpl(locator<CampaignDataSource>()),
  );

  locator.registerLazySingleton<GetActiveCampaignsUseCase>(
    () => GetActiveCampaignsUseCase(locator<CampaignRepoImpl>()),
  );

  locator.registerLazySingleton<RecordCampaignClickUseCase>(
    () => RecordCampaignClickUseCase(locator<CampaignRepoImpl>()),
  );

  locator.registerFactory<CampaignBloc>(
    () => CampaignBloc(
      getActiveCampaigns: locator<GetActiveCampaignsUseCase>(),
      recordCampaignClick: locator<RecordCampaignClickUseCase>(),
    ),
  );

  // Initialize use cases
  locator.registerLazySingleton<SignInUseCase>(
      () => SignInUseCase(locator<AuthRepositoryImpl>()));
  locator.registerLazySingleton<SignUpUseCase>(
      () => SignUpUseCase(locator<AuthRepositoryImpl>()));
  locator.registerLazySingleton<GetDiscountUseCase>(
      () => GetDiscountUseCase(locator<UserRepoImpl>()));
  locator.registerLazySingleton<UpdateAddressUseCase>(
      () => UpdateAddressUseCase(userRepoDomain: locator<UserRepoImpl>()));
  locator.registerLazySingleton<DeleteAddressUseCase>(
      () => DeleteAddressUseCase(userRepoDomain: locator<UserRepoImpl>()));
  locator.registerLazySingleton<ChangeDefaultAddressUseCase>(() =>
      ChangeDefaultAddressUseCase(userRepoDomain: locator<UserRepoImpl>()));
  locator.registerLazySingleton<ContactUsUseCase>(
      () => ContactUsUseCase(userRepoDomain: locator<UserRepoImpl>()));

  // Register GetChildPerformanceMetricsUseCase with UserRepositoryImpl
  locator.registerLazySingleton<GetChildPerformanceMetricsUseCase>(
    () => GetChildPerformanceMetricsUseCase(locator<UserRepositoryImpl>()),
  );

  locator.registerLazySingleton<GetParentPendingReviewsUseCase>(
    () => GetParentPendingReviewsUseCase(
        userRepository: locator<UserRepositoryImpl>()),
  );

  locator.registerLazySingleton<GetParentReviewHistoryUseCase>(
    () => GetParentReviewHistoryUseCase(
        userRepository: locator<UserRepositoryImpl>()),
  );
  locator.registerLazySingleton<RefundUseCase>(
      () => RefundUseCase(userRepoDomain: locator<UserRepositoryImpl>()));

  locator.registerLazySingleton<GetTimeTableByParentIdUseCase>(() =>
      GetTimeTableByParentIdUseCase(
          userRepoDomain: locator<UserRepositoryImpl>()));
  // Register owner dashboard dependencies
  locator.registerLazySingleton<OwnerDashboardRemoteDataSource>(
    () => OwnerDashboardRemoteDataSourceImpl(
      apiService: locator<ApiService>(),
      sharedRepository: locator<SharedRepository>(),
    ),
  );

  locator.registerLazySingleton<OwnerDashboardRepository>(
    () => OwnerDashboardRepositoryImpl(
        remoteDataSource: locator<OwnerDashboardRemoteDataSource>()),
  );

  locator.registerLazySingleton<GetOwnerDashboardData>(
    () => GetOwnerDashboardData(locator<OwnerDashboardRepository>()),
  );

////Coach Use case

  locator.registerLazySingleton<CreateCoachUseCase>(
      () => CreateCoachUseCase(coachRepoDomain: locator<CoachRepoImpl>()));
  locator.registerLazySingleton<UpdateCoachUseCase>(
      () => UpdateCoachUseCase(coachRepoDomain: locator<CoachRepoImpl>()));
  locator.registerLazySingleton<GetAllCoachUseCase>(
      () => GetAllCoachUseCase(coachRepoDomain: locator<CoachRepoImpl>()));

  locator.registerLazySingleton<AssignCenterUseCase>(
      () => AssignCenterUseCase(locator<CoachRepoImpl>()));
  locator.registerLazySingleton<RemoveCenterUseCase>(
      () => RemoveCenterUseCase(locator<CoachRepoImpl>()));
  locator.registerLazySingleton<GetAllProgramsUseCase>(
      () => GetAllProgramsUseCase(locator<CoachRepoImpl>()));
  locator.registerLazySingleton<GetCoachInfoByIdUseCase>(
      () => GetCoachInfoByIdUseCase(locator<CoachRepoImpl>()));
  locator.registerLazySingleton<GetUserUseCase>(
      () => GetUserUseCase(locator<SharedRepository>()));
  locator.registerLazySingleton<TokenValidationUseCase>(
      () => TokenValidationUseCase(locator<AuthRepositoryImpl>()));

  locator.registerLazySingleton<CreateChildUseCase>(
      () => CreateChildUseCase(userRepoDomain: locator<UserRepoImpl>()));
  locator.registerLazySingleton<UpdateChildUseCase>(
      () => UpdateChildUseCase(userRepoDomain: locator<UserRepoImpl>()));
  locator.registerLazySingleton<GetChildByParentIdUseCase>(
      () => GetChildByParentIdUseCase(userRepoDomain: locator<UserRepoImpl>()));
  locator.registerLazySingleton<DeleteChildByIdUseCase>(
      () => DeleteChildByIdUseCase(userRepoDomain: locator<UserRepoImpl>()));

  locator.registerLazySingleton<CreateCenterBranchUseCase>(
    () =>
        CreateCenterBranchUseCase(centerRepoDomain: locator<CenterRepoImpl>()),
  );
  locator.registerLazySingleton<CenterInfoCompleteUseCase>(() =>
      CenterInfoCompleteUseCase(centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetCenterUseCase>(
      () => GetCenterUseCase(centerRepoDomain: locator<CenterRepoImpl>()));

  locator.registerLazySingleton<UpdateCenterUseCase>(
      () => UpdateCenterUseCase(centerRepoDomain: locator<CenterRepoImpl>()));

  locator.registerLazySingleton<GetAllCenterUseCase>(
      () => GetAllCenterUseCase(centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetCoachsByCenterIdUseCase>(() =>
      GetCoachsByCenterIdUseCase(centerRepoImpl: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<AddClassUseCase>(
      () => AddClassUseCase(centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<DeleteClassUseCase>(
      () => DeleteClassUseCase(centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetAllClassUseCase>(
      () => GetAllClassUseCase(centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetAllClassesForParentUseCase>(() =>
      GetAllClassesForParentUseCase(
          centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetAllClassByCoachIdUseCase>(() =>
      GetAllClassByCoachIdUseCase(centerRepoDomain: locator<CenterRepoImpl>()));

  locator.registerLazySingleton<UpdateClassUseCase>(
      () => UpdateClassUseCase(centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetStudentsByClassIdUseCase>(() =>
      GetStudentsByClassIdUseCase(centerRepoImpl: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetCoachAndCenterByClassIdUseCase>(() =>
      GetCoachAndCenterByClassIdUseCase(
          centerRepoImpl: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetManagerssByCenterIdUseCase>(() =>
      GetManagerssByCenterIdUseCase(centerRepoImpl: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<ClassSlotDeleteUseCase>(
      () => ClassSlotDeleteUseCase(centerRepoImpl: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetRequestSendedByCenterToCoachUseCase>(() =>
      GetRequestSendedByCenterToCoachUseCase(
          centerRepoImpl: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<UpdateCoachInClassUseCase>(
      () => UpdateCoachInClassUseCase(locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetAllSlotsByClassIdUseCase>(
    () => GetAllSlotsByClassIdUseCase(repo: locator<CenterRepoImpl>()),
  );
  locator.registerLazySingleton<DeleteSlotByIdUseCase>(
      () => DeleteSlotByIdUseCase(repo: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<DeleteEventByIdUseCase>(
      () => DeleteEventByIdUseCase(repo: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetAllEventsUseCase>(
      () => GetAllEventsUseCase(centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetEventDatesUseCase>(
      () => GetEventDatesUseCase(centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetEventsByClassIdUseCase>(() =>
      GetEventsByClassIdUseCase(centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetEventsByEventIdUseCase>(() =>
      GetEventsByEventIdUseCase(centerRepoDomain: locator<CenterRepoImpl>()));

  locator.registerLazySingleton<GetEventsByDateUseCase>(() =>
      GetEventsByDateUseCase(centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<DeleteEventsByEventIdUseCase>(() =>
      DeleteEventsByEventIdUseCase(
          centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<OrderUseCase>(
      () => OrderUseCase(userRepoDomain: locator<UserRepoImpl>()));
  locator.registerLazySingleton<ParentInfoCompleterUseCase>(() =>
      ParentInfoCompleterUseCase(repository: locator<AuthRepositoryImpl>()));
  locator.registerLazySingleton<SendOTPUseCase>(() =>
      SendOTPUseCase(authRepositoryDomain: locator<AuthRepositoryImpl>()));
  locator.registerLazySingleton<VerifyOTPUseCase>(() =>
      VerifyOTPUseCase(authRepositoryDomain: locator<AuthRepositoryImpl>()));
  locator.registerLazySingleton<ResetPasswordUseCase>(() =>
      ResetPasswordUseCase(
          authRepositoryDomain: locator<AuthRepositoryImpl>()));
  locator.registerLazySingleton<QrCodeUseCase>(
      () => QrCodeUseCase(userRepoDomain: locator<UserRepoImpl>()));

  locator.registerLazySingleton<PostAttendanceUseCase>(
      () => PostAttendanceUseCase(centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetPresentAttendanceUseCase>(() =>
      GetPresentAttendanceUseCase(centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<ReviewUseCase>(
      () => ReviewUseCase(reviewRepoDomain: locator<ReviewRepoImpl>()));
  locator.registerLazySingleton<PostReviewByParentUseCase>(() =>
      PostReviewByParentUseCase(reviewRepoDomain: locator<ReviewRepoImpl>()));
  locator.registerLazySingleton<GetPendingReviewUseCase>(() =>
      GetPendingReviewUseCase(centerRepoDomain: locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetPendingReviewByClassIdUseCase>(
      () => GetPendingReviewByClassIdUseCase(locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetPendingReviewByCoachIdUseCase>(
      () => GetPendingReviewByCoachIdUseCase(locator<CenterRepoImpl>()));
  locator.registerLazySingleton<GetMomentsUseCase>(
      () => GetMomentsUseCase(reviewRepoDomain: locator<ReviewRepoImpl>()));
  locator.registerLazySingleton<GetHistoryOfChildIdUseCase>(() =>
      GetHistoryOfChildIdUseCase(userRepoDomain: locator<UserRepoImpl>()));
  locator.registerLazySingleton<GetPurchasedHistoryUseCase>(() =>
      GetPurchasedHistoryUseCase(userRepoDomain: locator<UserRepoImpl>()));
  locator.registerLazySingleton<GetBalanceUseCase>(
      () => GetBalanceUseCase(userRepoDomain: locator<UserRepoImpl>()));
  locator.registerLazySingleton<GetSubscriptionUseCase>(() =>
      GetSubscriptionUseCase(
          subscriptionRepoDomain: locator<SubscriptionRepoImpl>()));
  locator.registerLazySingleton<CancelSubscriptionUseCase>(() =>
      CancelSubscriptionUseCase(
          subscriptionRepoDomain: locator<SubscriptionRepoImpl>()));
  locator.registerLazySingleton<GetCardUseCase>(
      () => GetCardUseCase(userRepoDomain: locator<UserRepoImpl>()));
  locator.registerLazySingleton<DeleteCardUseCase>(
      () => DeleteCardUseCase(userRepoDomain: locator<UserRepoImpl>()));
  // locator.registerLazySingleton<ChatBloc>(() => ChatBloc(
  //       connectSocket: locator<ConnectSocket>(),
  //       disconnectSocket: locator<DisconnectSocket>(),
  //       sendMessage: locator<SendMessage>(),
  //       fetchMessages: locator<FetchMessages>(),
  //     ));

  // Register SocketDataSource with proper URL configuration
  locator.registerLazySingleton<SocketDataSource>(() {
    // Use a websocket-compatible URL
    final socketUrl = AppText.device.contains('https')
        ? AppText.device.replaceFirst('https', 'wss')
        : AppText.device.contains('http')
            ? AppText.device.replaceFirst('http', 'ws')
            : AppText.device;

    print('⚡ Initializing socket with URL: $socketUrl');
    return SocketDataSource(socketUrl);
  });

  locator.registerLazySingleton<HttpDataSource>(
    () => HttpDataSource(),
  );
  // Register ChatRepository
  locator.registerLazySingleton<ChatRepository>(
    () => ChatRepositoryImpl(
        locator<SocketDataSource>(), locator<HttpDataSource>()),
  );

  // Register Use Cases
  locator.registerLazySingleton<ConnectSocket>(
    () => ConnectSocket(locator<ChatRepository>()),
  );

  locator.registerLazySingleton<DisconnectSocket>(
    () => DisconnectSocket(locator<ChatRepository>()),
  );

  locator.registerLazySingleton<SendMessage>(
    () => SendMessage(locator<ChatRepository>()),
  );

  locator.registerLazySingleton<FetchMessages>(
    () => FetchMessages(locator<ChatRepository>()),
  );

  locator.registerLazySingleton<ReceiveMessage>(
    () => ReceiveMessage(locator<ChatRepository>()),
  );
  locator.registerLazySingleton<GetLastMessages>(
      () => GetLastMessages(locator<ChatRepository>()));
  locator.registerLazySingleton<MarkMessagesAsRead>(
      () => MarkMessagesAsRead(locator<ChatRepository>()));
  // Register ChatBloc
  locator.registerFactory<ChatBloc>(
    () => ChatBloc(
        connectSocket: locator<ConnectSocket>(),
        disconnectSocket: locator<DisconnectSocket>(),
        sendMessage: locator<SendMessage>(),
        fetchMessages: locator<FetchMessages>(),
        receiveMessage: locator<ReceiveMessage>(),
        getLastMessages: locator<GetLastMessages>(),
        markMessagesAsRead: locator<MarkMessagesAsRead>()),
  );
  locator.registerLazySingleton<SearchQueryUseCase>(
      () => SearchQueryUseCase(locator<SearchRepoDomain>()));
  locator.registerLazySingleton<SearchQueryByCategoryUseCase>(
      () => SearchQueryByCategoryUseCase(locator<SearchRepoDomain>()));

  ///Owner Use Case

  locator.registerLazySingleton<GetBranchesByOwnerIdUseCase>(
    () => GetBranchesByOwnerIdUseCase(locator<OwnerRepoImpl>()),
  );
  locator.registerLazySingleton<DeleteBranchUseCase>(
    () => DeleteBranchUseCase(locator<OwnerRepoImpl>()),
  );
  locator.registerLazySingleton<UpdateBranchUseCase>(
    () => UpdateBranchUseCase(locator<OwnerRepoImpl>()),
  );
  locator.registerLazySingleton<UpdateOwnerUseCase>(
    () => UpdateOwnerUseCase(locator<OwnerRepoImpl>()),
  );
  locator.registerLazySingleton<RequestCoachToJoinUseCase>(
      () => RequestCoachToJoinUseCase(locator<OwnerRepoImpl>()));
  locator.registerLazySingleton<RemoveCoachUseCase>(
      () => RemoveCoachUseCase(locator<OwnerRepoImpl>()));
  // locator.registerLazySingleton<RequestManagerToJoinUseCase>(
  //     () => RequestManagerToJoinUseCase(locator<OwnerRepoImpl>()));
  // locator.registerLazySingleton<RemoveManagerUseCase>(
  //     () => RemoveManagerUseCase(locator<OwnerRepoImpl>()));

  ///Notification Use Case

  locator.registerLazySingleton<GetNotifications>(
    () => GetNotifications(locator<NotificationRepository>()),
  );
  locator.registerLazySingleton<ReadNotification>(
    () => ReadNotification(locator<NotificationRepository>()),
  );
  locator.registerLazySingleton<DeleteNotification>(
      () => DeleteNotification(locator<NotificationRepository>()));
  locator.registerLazySingleton<CheckDeviceToken>(
      () => CheckDeviceToken(locator<NotificationRepository>()));

  /// Request

  locator.registerLazySingleton<RequestDataSources>(
      () => RequestDataSourcesImpl(locator<ApiService>()));
  locator.registerLazySingleton<RequestRepoImpl>(
      () => RequestRepoImpl(locator<RequestDataSources>()));
  locator.registerLazySingleton<SendJoinRequestUseCase>(
      () => SendJoinRequestUseCase(locator<RequestRepoImpl>()));
  locator.registerLazySingleton<UpdateRequestStatusUseCase>(
      () => UpdateRequestStatusUseCase(locator<RequestRepoImpl>()));
  locator.registerLazySingleton<GetRequestsByCenterUseCase>(
      () => GetRequestsByCenterUseCase(locator<RequestRepoImpl>()));
  locator.registerLazySingleton<GetRequestsByCoachUseCase>(
      () => GetRequestsByCoachUseCase(locator<RequestRepoImpl>()));
  locator.registerLazySingleton<CancelJoinRequestUseCase>(
      () => CancelJoinRequestUseCase(locator<RequestRepoImpl>()));
  locator.registerLazySingleton<CheckExistingRequestUseCase>(
      () => CheckExistingRequestUseCase(locator<RequestRepoImpl>()));

  locator.registerFactory(() => RequestBloc(
      sendJoinRequest: locator<SendJoinRequestUseCase>(),
      getByCenter: locator<GetRequestsByCenterUseCase>(),
      getByCoach: locator<GetRequestsByCoachUseCase>(),
      updateStatus: locator<UpdateRequestStatusUseCase>(),
      cancelJoinRequest: locator<CancelJoinRequestUseCase>(),
      checkExistingRequest: locator<CheckExistingRequestUseCase>()));

  ///Announcement

  locator.registerLazySingleton<AnnouncementDataSource>(
      () => AnnouncementDataSourceImpl(locator<ApiService>()));

  locator.registerLazySingleton<AnnouncementRepoImpl>(
      () => AnnouncementRepoImpl(locator<AnnouncementDataSource>()));
  locator.registerLazySingleton<GetAnnouncementUseCase>(
      () => GetAnnouncementUseCase(locator<AnnouncementRepoImpl>()));
  locator.registerLazySingleton<PostAnnouncementUseCase>(
      () => PostAnnouncementUseCase(locator<AnnouncementRepoImpl>()));

  locator.registerFactory(() => AuthBloc(
        signInUseCase: locator<SignInUseCase>(),
        signUpUseCase:
            locator<SignUpUseCase>(), // Use locator for sign up use case
        getUserUseCase:
            locator<GetUserUseCase>(), // Use locator for get user use case
        tokenValidationUseCase: locator<
            TokenValidationUseCase>(), // Use locator for token validation
        authRepository:
            locator<AuthRepositoryImpl>(), // Use locator for auth repository
        sharedRepository:
            locator<SharedRepository>(), // Use locator for shared repository
        parentInfoCompleterUseCase: locator<ParentInfoCompleterUseCase>(),
        sendOTPUseCase: locator<SendOTPUseCase>(),
        verifyOTPUseCase: locator<VerifyOTPUseCase>(),
        resetPasswordUseCase: locator<ResetPasswordUseCase>(),
      ));

  locator.registerFactory(
    () => UserBloc(
        createChildUseCase: locator<
            CreateChildUseCase>(), // Use locator for create child use case
        updateChildUseCase: locator<
            UpdateChildUseCase>(), // Use locator for update child use case
        getChildByParentIdUseCase: locator<
            GetChildByParentIdUseCase>(), // Use locator for get child by parent id use case
        deleteChildByIdUseCase: locator<DeleteChildByIdUseCase>(),
        orderUseCase: locator<OrderUseCase>(),
        qrCodeUseCase: locator<QrCodeUseCase>(),
        getHistoryOfChildIdUseCase: locator<GetHistoryOfChildIdUseCase>(),
        getPurchasedHistoryUseCase: locator<GetPurchasedHistoryUseCase>(),
        getBalanceUseCase: locator<GetBalanceUseCase>(),
        getCardUseCase: locator<GetCardUseCase>(),
        deleteCardUseCase: locator<DeleteCardUseCase>(),
        getDiscountUseCase: locator<GetDiscountUseCase>(),
        deleteAddressUseCase: locator<DeleteAddressUseCase>(),
        updateAddressUseCase: locator<UpdateAddressUseCase>(),
        changeDefaultAddressUseCase: locator<
            ChangeDefaultAddressUseCase>(), // Use locator for change default address use case
        contactUsUseCase:
            locator<ContactUsUseCase>(), // Use locator for contact us use case
        getChildPerformanceMetricsUseCase:
            locator<GetChildPerformanceMetricsUseCase>(),
        getParentPendingReviewsUseCase:
            locator<GetParentPendingReviewsUseCase>(),
        getParentReviewHistoryUseCase: locator<GetParentReviewHistoryUseCase>(),
        refundUseCase: locator<RefundUseCase>(),
        getTimeTableByParentIdUseCase:
            locator<GetTimeTableByParentIdUseCase>()),
  );
  locator.registerFactory(() => CenterBloc(
      createCenterBranchUseCase: locator<CreateCenterBranchUseCase>(),
      getPendingReviewUseCase: locator<GetPendingReviewUseCase>(),
      centerInfoCompleteUseCase: locator<CenterInfoCompleteUseCase>(),
      getCenterUseCase: locator<GetCenterUseCase>(),
      updateCenterUseCase: locator<UpdateCenterUseCase>(),
      getAllCenterUseCase: locator<GetAllCenterUseCase>(),
      addClassUseCase: locator<AddClassUseCase>(),
      deleteClassUseCase: locator<DeleteClassUseCase>(),
      getAllClassUseCase: locator<GetAllClassUseCase>(),
      getAllClassesForParentUseCase: locator<GetAllClassesForParentUseCase>(),
      getAllClassByCoachIdUseCase: locator<GetAllClassByCoachIdUseCase>(),
      updateClassUseCase: locator<UpdateClassUseCase>(),
      getAllEventsUseCase: locator<GetAllEventsUseCase>(),
      getEventsByClassIdUseCase: locator<GetEventsByClassIdUseCase>(),
      getEventsByEventIdUseCase: locator<GetEventsByEventIdUseCase>(),
      deleteEventsByEventIdUseCase: locator<DeleteEventsByEventIdUseCase>(),
      getCoachsByCenterIdUseCase: locator<GetCoachsByCenterIdUseCase>(),
      getEventsByDateUseCase: locator<GetEventsByDateUseCase>(),
      postAttendanceUseCase: locator<PostAttendanceUseCase>(),
      getPresentAttendanceUseCase: locator<GetPresentAttendanceUseCase>(),
      getEventDatesUseCase: locator<GetEventDatesUseCase>(),
      getStudentsByClassIdUseCase: locator<GetStudentsByClassIdUseCase>(),
      getCoachAndCenterByClassIdUseCase:
          locator<GetCoachAndCenterByClassIdUseCase>(),
      getPendingReviewByClassIdUseCase:
          locator<GetPendingReviewByClassIdUseCase>(),
      getPendingReviewByCoachIdUseCase:
          locator<GetPendingReviewByCoachIdUseCase>(),
      getManagerssByCenterIdUseCase: locator<GetManagerssByCenterIdUseCase>(),
      classSlotDeleteUseCase: locator<ClassSlotDeleteUseCase>(),
      getRequestSendedByCenterToCoachUseCase:
          locator<GetRequestSendedByCenterToCoachUseCase>(),
      updateCoachInClassUseCase: locator<UpdateCoachInClassUseCase>(),
      getAllSlotsByClassIdUseCase: locator<GetAllSlotsByClassIdUseCase>(),
      deleteSlotByIdUseCase: locator<DeleteSlotByIdUseCase>(),
      deleteEventByIdUseCase: locator<DeleteEventByIdUseCase>()));
  locator.registerFactory(
    () => CoachBloc(
        createCoachUseCase: locator<CreateCoachUseCase>(),
        updateCoachUseCase: locator<UpdateCoachUseCase>(),
        getAllCoachUseCase: locator<GetAllCoachUseCase>(),
        assignCenterUseCase: locator<AssignCenterUseCase>(),
        removeCenterUseCase: locator<RemoveCenterUseCase>(),
        getAllProgramsUseCase: locator<GetAllProgramsUseCase>(),
        getCoachInfoByIdUseCase: locator<GetCoachInfoByIdUseCase>()),
  );
  locator.registerFactory(
    () => ReviewBloc(
        reviewUseCase: locator<ReviewUseCase>(),
        postReviewByParentUseCase: locator<PostReviewByParentUseCase>(),
        getMomentsUseCase: locator<GetMomentsUseCase>()),
  );
  locator.registerFactory(
    () => SubscriptionBloc(
      getSubscriptionUseCase: locator<GetSubscriptionUseCase>(),
      cancelSubscriptionUseCase: locator<CancelSubscriptionUseCase>(),
    ),
  );

  locator.registerFactory(
    () => SearchBloc(locator<SearchQueryUseCase>(),
        locator<SearchQueryByCategoryUseCase>(), locator<SearchRepoDomain>()),
  );
  locator.registerFactory(
    () => OwnerBloc(
      locator<GetBranchesByOwnerIdUseCase>(),
      locator<DeleteBranchUseCase>(),
      locator<UpdateBranchUseCase>(),
      locator<UpdateOwnerUseCase>(),
      locator<RequestCoachToJoinUseCase>(),
      locator<RemoveCoachUseCase>(),
      // locator<RequestManagerToJoinUseCase>(),
      // locator<RemoveManagerUseCase>()
    ),
  );
  locator.registerFactory(() => NotificationBloc(
        getNotifications: locator<GetNotifications>(),
        readNotification: locator<
            ReadNotification>(), // Use locator for read notification use case
        deleteNotification: locator<
            DeleteNotification>(), // Use locator for delete notification use case
        checkDeviceToken: locator<
            CheckDeviceToken>(), // Use locator for check device token use case
      ));
  locator.registerFactory(() => AnnouncementBloc(
      getAnnouncementUseCase: locator<GetAnnouncementUseCase>(),
      postAnnouncementUseCase: locator<PostAnnouncementUseCase>()));

  // Initialize chat local repository
  try {
    print('Setting up ChatLocalRepository...');
    final chatLocalRepository = ChatLocalRepository.instance;
    await chatLocalRepository.initialize();
    locator.registerSingleton<ChatLocalRepository>(chatLocalRepository);
    print('ChatLocalRepository registered successfully');
  } catch (e) {
    print('Error setting up ChatLocalRepository: $e');
    // Register anyway to avoid null errors
    locator
        .registerSingleton<ChatLocalRepository>(ChatLocalRepository.instance);
  }
}
