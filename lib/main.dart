import 'package:class_z/config/themes/app_theme.dart';
import 'package:class_z/core/imports.dart';
import 'package:class_z/core/config/app_config.dart';

import 'package:class_z/core/service/firebase_initializer.dart';
import 'package:class_z/core/common/presentation/blocs/savedCenterBloc/saved_center_bloc.dart';

import 'package:flutter_stripe/flutter_stripe.dart';
import 'dart:math';
import 'package:class_z/firebase_options.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

//
// Top-level function for handling background messages
@pragma('vm:entry-point')
Future<void> _firebaseMessageBackgroundHandler(RemoteMessage message) async {
  // Need to initialize Firebase for background handlers
  await FirebaseInitializer.initialize();
  print("Handling a background message: ${message.messageId}");

  // You can add custom logic here to handle background messages
  if (message.notification != null) {
    print('Message Title: ${message.notification?.title}');
    print('Message Body: ${message.notification?.body}');
  }

  if (message.data.isNotEmpty) {
    print('Message Data: ${message.data}');
  }
}

Future<void> main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize AppConfig with default values
  AppConfig.initialize();

  try {
    // Initialize Firebase using our utility class with proper options
    final firebaseInitialized = await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    ).then((_) {
      print('Firebase core initialized successfully in main()');
      return true;
    }).catchError((error) {
      print('Firebase initialization error in main(): $error');
      return false;
    });

    if (firebaseInitialized) {
      // Set the initialized flag in our FirebaseInitializer
      FirebaseInitializer.initialized = true;

      // Register the background message handler
      FirebaseMessaging.onBackgroundMessage(_firebaseMessageBackgroundHandler);

      if (Platform.isIOS) {
        // For iOS, request permissions and ensure APNS token is set up
        final messaging = FirebaseMessaging.instance;

        // Request notification permissions
        final settings = await messaging.requestPermission(
          alert: true,
          badge: true,
          sound: true,
          provisional: false,
          criticalAlert: false,
          announcement: false,
          carPlay: false,
        );

        print(
            'User notification permission status: ${settings.authorizationStatus}');

        // Set foreground notification presentation options
        await messaging.setForegroundNotificationPresentationOptions(
          alert: true,
          badge: true,
          sound: true,
        );

        // Try to get APNS token (may not be available immediately)
        // Note: APNS token is often not available immediately at startup
        // The enhanced retry logic in FirebaseService will handle this properly
        try {
          final apnsToken = await messaging.getAPNSToken();
          if (apnsToken != null) {
            print(
                'APNS token available at startup: ${apnsToken.substring(0, 10)}...');
          } else {
            print(
                'APNS token not available at startup, will be requested later with retry logic');
          }
        } catch (e) {
          print('Expected: APNS token not available at startup: $e');
          print(
              'This is normal - the enhanced retry logic will handle token retrieval');
        }
      }
    } else {
      print('Firebase initialization failed in main()');
    }

    // Dependency Injection Setup - must happen after Firebase initialization attempt
    await setupLocator();
    await locator.allReady(); // Ensure all async singletons are ready

    // Initialize notification channels for Android
    if (Platform.isAndroid) {
      await _setupNotificationChannel();
    }

    // Initialize Stripe
    setupStripe();

    // Load configuration from server (prices, etc.)
    await AppConfig.loadFromServer();
  } catch (e) {
    print('Error during initialization: $e');
    // Continue with app startup even if Firebase fails
    // This way the app can still be used without Firebase features
    await setupLocator();
    await locator.allReady(); // Ensure all async singletons are ready here too
  }

  // Run the app
  runApp(
    const MyApp(),
  );
}

// Setup notification channel for Android
Future<void> _setupNotificationChannel() async {
  const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'high_importance_channel', // id
    'High Importance Notifications', // title
    description:
        'This channel is used for important notifications.', // description
    importance: Importance.high,
  );

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);
}

// Function to setup Stripe
void setupStripe() {
  try {
    const String publishableKey =
        "pk_test_51QPJJUExbFZkDEtDwJ9xaCUwWEZOcM7hUsgWP0bOCyNF6GXl83RneNIZnfwtWR6GOnpvCaiWbBw4jGITDpL1fPp80096y7XQ5Y";
    Stripe.publishableKey = publishableKey;
    print("Stripe initialized successfully with publishable key");
  } catch (e) {
    print("Error initializing Stripe: $e");
  }
}

@pragma('vm:entry-point')
class MyApp extends StatefulWidget {
  const MyApp({
    super.key,
  });

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late Future<String> _initialRouteFuture;
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  late Notificationservice _notificationService;
  bool _firebaseInitialized = false;

  @override
  void initState() {
    super.initState();
    _initialRouteFuture = _determineInitialRoute();

    // First, check if Firebase is already initialized from main()
    _firebaseInitialized = FirebaseInitializer.isInitialized;

    if (_firebaseInitialized) {
      print(
          'Firebase already initialized from main(), proceeding with notification services');
      _initNotificationServices();
    } else {
      // Try to initialize if not already done
      _initializeFirebase().then((_) {
        _initNotificationServices();
      });
    }
  }

  // Initialize Firebase first, then continue with other initialization
  Future<void> _initializeFirebase() async {
    try {
      if (!FirebaseInitializer.isInitialized) {
        _firebaseInitialized = await FirebaseInitializer.initialize();
        if (_firebaseInitialized) {
          print('Firebase initialized successfully in MyAppState');
        } else {
          print('Firebase initialization failed in MyAppState');
        }
      } else {
        _firebaseInitialized = true;
        print('Firebase was already initialized');
      }
    } catch (e) {
      print('Error initializing Firebase: $e');
    }
  }

  // Initialize notification-related services after Firebase is ready
  void _initNotificationServices() {
    // Don't continue with notification setup if Firebase failed to initialize
    if (!_firebaseInitialized) {
      print(
          'Skipping notification setup due to Firebase initialization failure');
      return;
    }

    try {
      // Initialize the notification service only after Firebase is ready
      _notificationService = Notificationservice();
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _notificationService.initialize(context);
        }
      });

      // Only set up notifications if Firebase is initialized
      _initializeNotifications();
      _setupFirebaseMessagingListeners();
    } catch (e) {
      print('Error initializing notification services: $e');
    }
  }

  // Initialize local notifications
  Future<void> _initializeNotifications() async {
    try {
      // Initialize settings for Android
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // Initialize settings for iOS
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // Combine platform-specific settings
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // Initialize the plugin with settings
      await flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (NotificationResponse response) {
          // Handle notification tap
          print('Notification tapped: ${response.payload}');
          // You can add navigation logic here
        },
      );

      // Request permission for iOS using FirebaseInitializer
      if (Platform.isIOS) {
        await FirebaseInitializer.requestPermissions();
      }

      print('Notification setup complete');
    } catch (e) {
      print('Error initializing notifications: $e');
    }
  }

  // Setup Firebase messaging listeners
  void _setupFirebaseMessagingListeners() {
    try {
      // Verify Firebase is initialized before accessing FirebaseMessaging
      if (!FirebaseInitializer.isInitialized) {
        print('Firebase not initialized, skipping messaging listeners setup');
        return;
      }

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        print('Got a message whilst in the foreground!');
        print('Message data: ${message.data}');

        if (message.notification != null) {
          print(
              'Message also contained a notification: ${message.notification}');

          // Show local notification
          _showNotification(message);
        }
      });

      // Handle messages when app is opened from terminated state
      FirebaseMessaging.instance
          .getInitialMessage()
          .then((RemoteMessage? message) {
        if (message != null) {
          print(
              'App opened from terminated state with message: ${message.messageId}');
          // Handle the message, e.g., navigate to a specific screen
        }
      });

      // Handle message when app is in background but opened
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        print(
            'App opened from background state with message: ${message.messageId}');
        // Handle the message, e.g., navigate to a specific screen
      });

      print('Firebase messaging listeners setup complete');
    } catch (e) {
      print('Error setting up Firebase messaging listeners: $e');
    }
  }

  // Show local notification
  Future<void> _showNotification(RemoteMessage message) async {
    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'high_importance_channel',
      'High Importance Notifications',
      channelDescription: 'This channel is used for important notifications.',
      importance: Importance.high,
      priority: Priority.high,
    );

    final DarwinNotificationDetails iOSPlatformChannelSpecifics =
        const DarwinNotificationDetails(
            presentAlert: true, presentBadge: true, presentSound: true);

    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await flutterLocalNotificationsPlugin.show(
      Random().nextInt(100000), // Random ID to avoid overwriting
      message.notification?.title ?? 'New Notification',
      message.notification?.body ?? '',
      platformChannelSpecifics,
      payload: message.data.isNotEmpty
          ? json.encode(message.data)
          : 'notification_payload', // Store data as payload
    );
  }

  Future<String> _determineInitialRoute() async {
    // final token = locator<SharedRepository>().getToken();
    // print(token);
    // if (token != null && token.isNotEmpty) {
    //  print("object");
    // final isValid =
    //     await locator<TokenValidationUseCase>().call(token: token);
    //    print("valid: $isValid");
    //    if (isValid) {
    UserModel? user = locator<SharedRepository>().getUserData();
    if (user?.data?.parent != null)
      return AppRoutes.homePage;
    else if (user?.data?.owner != null)
      return AppRoutes.ownerMain;
    else if (user?.data?.center != null || user?.data?.coach != null)
      return AppRoutes.openProfile;
    return AppRoutes.homePage;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<String>(
        future: _initialRouteFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Directionality(
              textDirection: TextDirection.ltr,
              child: Center(child: Text('Error: ${snapshot.error}')),
            );
          }
          print(" snapshot data ${snapshot.data}");
          final initialRoute = snapshot.data ?? AppRoutes.homePage;
          return ScreenUtilInit(
            designSize: const Size(360, 690),
            minTextAdapt: true,
            splitScreenMode: true,
            builder: (context, child) => MultiBlocProvider(
              providers: [
                ChangeNotifierProvider(create: (_) => ScreenSizeProvider()),
                BlocProvider<AuthBloc>(create: (_) => locator<AuthBloc>()),
                BlocProvider<UserBloc>(create: (_) => locator<UserBloc>()),
                BlocProvider<CenterBloc>(create: (_) => locator<CenterBloc>()),
                BlocProvider<CoachBloc>(create: (_) => locator<CoachBloc>()),
                BlocProvider<ReviewBloc>(create: (_) => locator<ReviewBloc>()),
                BlocProvider<SubscriptionBloc>(
                    create: (_) => locator<SubscriptionBloc>()),
                BlocProvider<ChatBloc>(create: (_) => locator<ChatBloc>()),
                Provider<SharedRepository>.value(
                    value: locator<SharedRepository>()),
                BlocProvider<SearchBloc>(create: (_) => locator<SearchBloc>()),
                BlocProvider<OwnerBloc>(create: (_) => locator<OwnerBloc>()),
                BlocProvider(create: (_) => locator<NotificationBloc>()),
                BlocProvider(create: (_) => locator<AnnouncementBloc>()),
                BlocProvider(create: (_) => locator<RequestBloc>()),
                BlocProvider<SavedCenterBloc>(
                    create: (_) => locator<SavedCenterBloc>()),
                BlocProvider<CampaignBloc>(
                    create: (_) => locator<CampaignBloc>()),
              ],
              child: MaterialApp(
                debugShowCheckedModeBanner: false,
                title: 'ClassZ',
                theme: AppTheme.lightThemeMode,
                navigatorKey: NavigatorService.navigatorKey,
                onGenerateRoute: AppRoutes.generateRoute,
                initialRoute: initialRoute,
              ),
            ),
          );
        });
  }
}
