import 'package:flutter/material.dart';

class AppPallete {
  static const Color backgroundColor = Colors.white;
  static const Color red = Color.fromRGBO(218, 33, 39, 1);
  static const Color secondaryColor = Color.fromRGBO(63, 145, 241, 1);
  static const Color buttonColor = Color.fromRGBO(63, 145, 241, 1);
  static const Color splashButtonColor = Color.fromRGBO(102, 167, 243, 1);
  static const Color currentColor = Color.fromRGBO(255, 233, 190, 1);
  static const Color redWordColor = Color.fromRGBO(63, 145, 241, 1);
  static const Color svgColorLocation = Color.fromRGBO(66, 66, 66, 1);
  static const Color border = Color.fromRGBO(66, 66, 66, 1);
  static const Color wordsOfRequest = Color.fromRGBO(88, 116, 171, 1);
  static const Color center = Color(0xFFF4F4F4);
  static const Color rating = Color.fromRGBO(255, 175, 54, 1);
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static const Color inputBox = Color(0xFFF4F4F4);
  static const Color forgetButton = Color(0xFFD9D9D9);
  static const Color green = Color(0xFF0D9912);
  static const Color greyWord = Color(0xFF898787);

  static const Color darkGrey = Color.fromRGBO(66, 66, 66, 1);
  static const Color rateGray = Color.fromRGBO(212, 212, 212, 1);

  static const Color moreHide = Color.fromRGBO(137, 135, 135, 1);

  static const Color lightGrey = Color(0xFFE6E6E6);
  static const Color iconColor = Color.fromRGBO(63, 145, 241, 1);
  static const Color gradient1 = Color(0x00971200);
  static const Color gradient2 = Color.fromRGBO(251, 109, 169, 1);
  static const Color gradient3 = Color.fromRGBO(255, 159, 124, 1);
  static const Color borderColor = Color.fromRGBO(52, 51, 67, 1);
  static const Color whiteColor = Colors.white;
  static const Color greyColor = Color.fromRGBO(93, 93, 93, 1);
  static const Color errorColor = Colors.redAccent;
  static const Color transparentColor = Colors.transparent;
  static const Color divider = Color(0xFFd9d9d9);
  static const Color heartSpot = Color.fromRGBO(66, 66, 66, 0.6);
  static const Color heartBorder = Color.fromRGBO(255, 255, 255, 0.4);
  static const Color dividerTime = Color.fromRGBO(217, 217, 217, 1);
  static const Color lightGreyReal = Color.fromRGBO(230, 230, 230, 1);
  static const Color scheduleColor2 = Color.fromRGBO(216, 255, 203, 1);
  static const Color scheduleColor3 = Color.fromRGBO(255, 222, 173, 1);
  static const Color scheduleColor4 = Color.fromRGBO(202, 226, 255, 1);
  static const Color scheduleColor5 = Color.fromRGBO(196, 255, 241, 1);
  static const Color paleGrey = Color.fromRGBO(244, 244, 244, 1);
  static const Color color34 = Color.fromRGBO(34, 34, 34, 1);
  static const Color change = Color.fromRGBO(88, 116, 171, 1);
  static const Color dashboard = Color.fromRGBO(252, 252, 240, 1);
  static const Color color234 = Color.fromRGBO(234, 142, 5, 1);
  static const Color color128 = Color.fromRGBO(128, 255, 232, 1);
  static const Color color255 = Color.fromRGBO(255, 241, 208, 1);
  static const Color color76 = Color.fromRGBO(76, 201, 240, 1);

  static const Color color185 = Color.fromRGBO(185, 228, 245, 1);
  static const Color hexagon = Color.fromRGBO(241, 241, 241, 1);
  static const Color color136 = Color.fromRGBO(136, 140, 141, 1);
  static const Color color249 = Color.fromRGBO(249, 111, 111, 1);
  static const Color color111 = Color.fromRGBO(111, 208, 249, 1);
  static const Color color255163 = Color.fromRGBO(255, 163, 245, 1);
  static const Color color194 = Color.fromRGBO(194, 241, 227, 1);
  static const Color colorFreeSlot = Color.fromRGBO(185, 228, 245, 1);
}
