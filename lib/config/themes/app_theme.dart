import 'package:class_z/config/themes/app_pallate.dart';
import 'package:flutter/material.dart';

class AppTheme {
  static final lightThemeMode = ThemeData.light().copyWith(
    scaffoldBackgroundColor: AppPallete.backgroundColor,
    buttonTheme: ButtonThemeData(
      buttonColor: AppPallete.splashButtonColor, // Set the button color
      textTheme: ButtonTextTheme.primary, // Use primary color for button text
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0), // Set button border radius
      ),
    ),
  );
}
