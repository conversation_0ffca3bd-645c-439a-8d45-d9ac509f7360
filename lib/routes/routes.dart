// lib/presentation/routes/app_routes.dart
import 'package:class_z/core/imports.dart';
import 'package:class_z/features/roles/center/presentation/screen/available_coaches_screen.dart';
import 'package:class_z/features/roles/center/presentation/screen/change_coach_class.dart';
import 'package:class_z/features/roles/center/presentation/screen/coach_management_grid.dart';
import 'package:class_z/features/roles/parent/presentation/screen/edit_child.dart';
import 'package:class_z/features/authentications/presentation/screen/reset_password_verify.dart';
import 'package:class_z/features/roles/owner/presentation/screen/owner_search_manager.dart';
import 'package:class_z/features/roles/parent/presentation/screen/event_details.dart';
import 'package:class_z/features/roles/parent/presentation/screen/review_screen_nn.dart';
import 'package:class_z/features/roles/parent/presentation/screen/search_result.dart';
import 'package:class_z/features/roles/parent/presentation/screen/filter_screen.dart';
import 'package:class_z/features/roles/parent/presentation/screen/saved_centers_screen.dart';
import 'package:class_z/features/roles/parent/presentation/screen/child_report_screen.dart';
import 'package:class_z/features/transactions/data/models/daily_transaction_model.dart';
import 'package:class_z/features/roles/parent/presentation/screen/faq_screen.dart';
import 'package:class_z/features/roles/center/presentation/screen/change_coach_screen.dart';
import 'package:class_z/features/roles/parent/presentation/screen/refund_screen.dart';
import 'package:class_z/features/roles/center/presentation/screen/center_class_details.dart';

class AppRoutes {
  static const String homePage = "/homePage";
  static const String splashScreen = "/splash";
  static const String signUp = "/signUp";
  static const String logIn = "/logIn";
  static const String firstTimelogIn = "/firstTimelogIn";
  static const String chat = "/chat";
  static const String notification = "/notification";
  //static const String userProfil = "/firstTimelogIn";
  static const String forgetPassword = "/forgetPassword";
  static const String verify = "/verify";
  static const String resetPassword = "/reset";
  static const String resetPasswordVerify = "/resetPasswordVerify";
  static const String openProfile = "/profile";
  static const String coachRegistration = "/coachRegistration";
  static const String userProfile = "/useprofile";
  static const String userMain = "/usermain";
  static const String coachSpotlight = "/coach";
  static const String centreSpotlight = "/centre";
  static const String centreView = "/centreView";
  static const String coachView = "/coachView";
  static const String timeslotCentre = "/timeslotCentre";
  static const String request = "/request";
  static const String timeslotCoach = "/timeslotCoach";
  static const String freeslot = "/freeslot";
  static const String pending = "/pending";
  static const String search = "/search";
  static const String searchGenre = "/searchGenre";
  static const String centreGenre = "/centreGenre";
  static const String searchResult = "/searchResult";
  static const String filterScreen = "/filterScreen";
  static const String middleProfileSwitch = "/middleProfileSwitch";
  static const String myProfile = "/myProfile";
  static const String myProfileEdit = "/myProfileEdit";
  static const String childInfo = "/childInfo";
  static const String addChild = "/addChild";
  static const String editChild = "/editChild";
  static const String myAddress = "/myAddress";
  static const String myCoupn = "/myCoupn";
  static const String terms = "/terms";
  static const String contactUs = "/contactus";
  static const String faq = "/faq";
  static const String contactUsSubmitted = "/contactussubmitted";
  static const String myWallet = "/wallet";
  static const String mySubscriptions = "/subscriptions";
  static const String creditCard = "/creditCard";
  static const String unsubscribe = "/unsubscribe";
  static const String unsubscribeMessage = "/unsubscribeMessage";
  static const String topUp = "/topUp";
  static const String timeTableUp = "/timeTableUp";
  static const String viewall = "/viewall";
  static const String ordersDetailsConTimetable = "/orderscon";
  static const String eventDetailsConTimetable = "/eventDetailsConTimetable";
  static const String ordersDetailsPending = "/ordersDetailsPending";
  static const String qrcode = "/qrcode";
  static const String progressFeedbackUserDashboard = "/progressFeedBack";
  static const String progressUser = "/progressUser";
  static const String classDetails = "/classDetails";
  static const String privateClass = "/privateClass";
  static const String privateRequestIndividual = "/privateRequestIndividual";
  static const String changeChild = "/changeChild";
  static const String experience = "/experience";
  static const String programs = "/upcomingClass";
  static const String timeslotCentreCourse = "/timeslotCentreCoutse";
  static const String reserved = "/reserved";
  static const String announcementMessage = "/announcementMessage";
  static const String moments = "/moments";
  static const String savedCenters = "/saved-centers";
  static const String childReport = "/child-report";
  static const String myReviews = "/myReviews";
  static const String refund = "/refund";

  ///center

  static const String centerProfilesetup1 = "/centerProfileSetup1";
  static const String centerProfilesetup2 = "/centerProfileSetup2";
  static const String centerProfilesetup3 = "/centerProfileSetup3";
  static const String centerProfilesetup4 = "/centerProfileSetup4";
  static const String centerProfilesetup5 = "/centerProfileSetup5";
  static const String centerProfilesetup6 = "/centerProfileSetup6";
  static const String centerMain = "/centermain";
  static const String centerSettings = "/centerSetting";
  static const String centerMessage = "/centerMessage";
  static const String centerProfile = "/centerProfile";
  static const String centerProfileEdit = "/centerProfileEdit";
  static const String centerDetails = "/centerDetails";
  static const String centerDescription = "/centerDescription";
  static const String centerBusiness = "/centerBusiness";
  static const String centerPayout = "/centerPayout";
  static const String centerBranch = "/centerBranch";
  static const String checkin = "/checkin";
  static const String verificationCode = "/verificationCode";
  static const String pendingReview = "/pendingReview";
  static const String pendingReviewStudent = "/pendingReviewStudent";
  static const String pendingReviewsbyClass = "/pendingReviewsbyClass";
  static const String centerProgressCheck = "/centerProgressCheck";
  static const String centerCoach = "/centerCoach";
  static const String coachManagementGrid = "/coachManagementGrid";
  static const String availableCoaches = "/availableCoaches";
  static const String centerSavedClass = "/centerSavedClass";
  static const String centerAddClass = "/centerAddClass";
  static const String transaction = "/transaction";
  static const String intracsaction = "/intransaction";
  static const String centerCoachRequest = "/centerCoachRequest";
  static const String centerCoachSearch = "/centerCoachSearch";
  static const String centerTimeSlotEdit = "/centerTimeSlotEdit";
  static const String centerAddSlot = "/centerAddSlot";
  static const String centerAnnouncement = "/centerAnnouncement";
  static const String centerSendAnnouncement = "/centerSendAnnouncement";
  static const String centerSelectCoach = "/centerSelectCoach";
  static const String centerUpdateCoachForClass = "/centerUpdateCoachForClass";
  static const String centerTimeSlotSetup = "/centerTimeSlotSetup";
  static const String centerSlotConfirmation = "/centerSlotConfirmation";
  static const String centerRequestPrivate = "/centerRequestPrivate";
  static const String centerPressSlot = "/centerPressSlot";
  static const String studentList = "/studentList";
  static const String centerClassDetails = '/centerClassDetails';

  ///Coach

  static const String coachMain = "/coachMain";
  static const String coachSettings = "/coachSettings";
  static const String coachSlot = "/coachSlot";
  static const String coachPrograms = "/CoachPrograms";
  static const String coachPressSlot = "/coachPressSlot";
  static const String coachStudentList = "/coachStudentList";
  static const String coachMyProfile = "/coachMyProfile";
  static const String coachProfileEdit = "/coachProfileEdit";
  static const String coachProfileEditDetails = "/coachProfileEditDetails";
  static const String coachProfileEditDescription =
      "/coachProfileEditDescription";
  static const String coachProfileEditSkills = "/coachProfileEditSkills";
  static const String coachProfileEditAccredation =
      "/coachProfileEditAccredation";
  static const String coachProfileEditExperience =
      "/coachProfileEditExperience";
  static const String scan = "/scan";
  static const String review = "/review";
  static const String connectToYourCenter = "/connectToYourCenter";

  ///Owner

  static const String ownerMain = "/ownerMain";
  static const String ownerRegistration = "/ownerRegistration";
  static const String ownerSettings = "/ownerSettings";
  static const String ownerMyProfile = "/ownerMyProfile";
  static const String ownerManage = "/ownerMange";
  static const String ownerCoachProfile = "/ownerCoachProfile";
  static const String ownerCenterProfile = "/ownerCenterProfile";
  static const String ownerCoachEdit = "/ownerManagerOrCoachEdit";
  static const String ownerManagerEdit = "/ownerManagerEdit";
  static const String ownerProfieEdit = "/ownerProfieEdit";

  static const String reviewTest = '/review-test';

  static const String changeCoach = '/changeCoach';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case homePage:
        return MaterialPageRoute(builder: (_) => HomeScreen());
      case splashScreen:
        return MaterialPageRoute(builder: (_) => const SplashScreen());
      case signUp:
        if (settings.arguments is String) {
          final userType = settings.arguments as String?;
          return MaterialPageRoute(
              builder: (_) => SignUp(
                    userType: userType,
                  ));
        }
        return _errorRoute();

      case logIn:
        return MaterialPageRoute(builder: (_) => const LogIn());
      case firstTimelogIn:
        if (settings.arguments is String) {
          final portalType = settings.arguments as String?;
          return MaterialPageRoute(
              builder: (_) => FirstTimeLogIn(
                    portalType: portalType,
                  ));
        }
        return MaterialPageRoute(builder: (_) => const FirstTimeLogIn());
      case forgetPassword:
        return MaterialPageRoute(builder: (_) => const ForgetPassword());
      case verify:
        if (settings.arguments is Map<String, dynamic>) {
          final args = settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => Verify(
                    data: args,
                  ));
        }
        return _errorRoute();
      case notification:
        if (settings.arguments is String) {
          String userId = settings.arguments as String;
          return MaterialPageRoute(
              builder: (_) => NotificationPage(userId: userId));
        }
        return _errorRoute();
      case resetPassword:
        if (settings.arguments is String) {
          final email = settings.arguments as String;
          return MaterialPageRoute(
              builder: (_) => ResetPassword(
                    email: email,
                  ));
        }
        return _errorRoute();
      case resetPasswordVerify:
        if (settings.arguments is String) {
          final email = settings.arguments as String;
          return MaterialPageRoute(
              builder: (_) => ResetPasswordVerify(
                    email: email,
                  ));
        }
        return _errorRoute();
      case openProfile:
        try {
          // First check if the user has a coach profile
          final sharedRepository = locator<SharedRepository>();
          final userData = sharedRepository.getUserData();

          if (userData != null && userData.data?.coach != null) {
            return MaterialPageRoute(builder: (_) => const UserProfile());
          } else {
            // Return an error route with a more descriptive message
            print("Error: User doesn't have a coach profile");
            return _errorRoute(
                message:
                    "This feature is only available for coach users. Please switch to a coach account or contact support.");
          }
        } catch (e) {
          print("Error loading UserProfile: $e");
          return _errorRoute(message: "Unable to load profile: $e");
        }

      case userProfile:
        return MaterialPageRoute(builder: (_) => const UserNewProfile());
      case userMain:
        return MaterialPageRoute(builder: (_) => HomeScreen());
      case coachSpotlight:
        return MaterialPageRoute(builder: (_) => const CoachSpotlight());
      case centreSpotlight:
        if (settings.arguments is List<CenterData>) {
          final List<CenterData> centerData =
              settings.arguments as List<CenterData>;
          return MaterialPageRoute(
            builder: (_) => CentreSpotlight(centerModel: centerData),
          );
        }
        return _errorRoute();
      case AppRoutes.chat:
        if (settings.arguments is Map<String, dynamic>) {
          final data = settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(builder: (_) => Chat(data: data));
        }
        return _errorRoute();
      case AppRoutes.centreView:
        if (settings.arguments is Map<String, dynamic>) {
          final args = settings.arguments as Map<String, dynamic>;
          final CenterData? centerData = args['center'] as CenterData?;
          final bool? bottomView = args['bottomView'] as bool?;
          final bool? isSaved = args['isSaved'] as bool?;
          print(centerData);
          return MaterialPageRoute(
            builder: (_) => CentreView(
              center: centerData,
              bottomView: bottomView,
              isSaved: isSaved,
            ),
          );
        }
        return _errorRoute();
      case coachView:
        if (settings.arguments is String) {
          final coachId = settings.arguments as String?;
          return MaterialPageRoute(
              builder: (_) => CoachView(
                    coachId: coachId,
                  ));
        }
        return _errorRoute();
      case timeslotCentre:
        if (settings.arguments is String) {
          final String classId = settings.arguments as String;
          return MaterialPageRoute(
              builder: (_) => TimeSloCentre(
                    classId: classId,
                  ));
        }
        return _errorRoute();
      case AppRoutes.request:
        if (settings.arguments is Map<String, dynamic>) {
          final args = settings.arguments as Map<String, dynamic>;
          final classModel = args['classModel'] as ClassModel;
          final eventDetails = args['eventDetails'] != null
              ? args['eventDetails'] as EventDate
              : null;

          return MaterialPageRoute(
            builder: (_) => RequestSTL(
              classModel: classModel,
              eventDetails: eventDetails,
            ),
          );
        }
        return _errorRoute();

      case timeslotCoach:
        return MaterialPageRoute(builder: (_) => TimeSlotCoach());
      case freeslot:
        return MaterialPageRoute(builder: (_) => FreeSlot());
      case pending:
        return MaterialPageRoute(builder: (_) => Pending());
      case search:
        return MaterialPageRoute(builder: (_) => Search());
      case searchGenre:
        if (settings.arguments is Map<String, dynamic>) {
          print('Route');
          final args = settings.arguments as Map<String, dynamic>;
          final title = args['title'] != null ? args['title'] as String : null;
          final result =
              args['result'] != null ? args['result'] as SearchModel : null;
          print(title);
          print(result);
          return MaterialPageRoute(
              builder: (_) => SearchGenre(
                    result: result,
                    title: title,
                  ));
        }

        return _errorRoute();
      case searchResult:
        return MaterialPageRoute(builder: (_) => SearchResult());

      case centreGenre:
        return MaterialPageRoute(builder: (_) => CentreGenre());
      case middleProfileSwitch:
        return MaterialPageRoute(builder: (_) => MiddleProfileSwitch());
      case myProfile:
        try {
          if (settings.arguments is UserModel) {
            final UserModel userData = settings.arguments as UserModel;
            // Debug the userData to see what's missing
            print("MyProfile route: userData exists, checking parent data");
            print("Token: ${userData.token.isNotEmpty ? 'Valid' : 'Empty'}");
            print(
                "Parent data: ${userData.data?.parent != null ? 'Exists' : 'Missing'}");

            if (userData.data?.parent != null) {
              print("Parent name: ${userData.data?.parent?.fullname}");
              // Don't use the builder pattern with context here
              return MaterialPageRoute(
                builder: (_) => MyProfile(userData: userData),
              );
            } else {
              print("Error: Parent data is null in UserModel");
              return _errorRoute(
                  message:
                      "Parent profile data is not available. Please log out and log in again.");
            }
          } else if (settings.arguments == null) {
            // Handle case when no arguments are provided
            print("No arguments provided to myProfile route");
            // Try to get the user data from the shared repository
            final userData = locator<SharedRepository>().getUserData();
            if (userData != null && userData.data?.parent != null) {
              print("Retrieved user data from shared repository");
              return MaterialPageRoute(
                builder: (_) => MyProfile(userData: userData),
              );
            } else {
              return _errorRoute(
                  message: "User data not available. Please log in again.");
            }
          } else {
            print(
                "Error: User data is not a UserModel type: ${settings.arguments?.runtimeType}");
            return _errorRoute(
                message:
                    "Invalid data provided for profile page. Please try again.");
          }
        } catch (e) {
          print("Error in myProfile route: $e");
          return _errorRoute(message: "Error loading profile: $e");
        }
      case myProfileEdit:
        try {
          if (settings.arguments is UserModel) {
            final UserModel userData = settings.arguments as UserModel;
            if (userData.data?.parent != null) {
              return MaterialPageRoute(
                builder: (_) => MyProfileEdit(userData: userData),
              );
            } else {
              return _errorRoute(
                  message: "Parent profile data is not available for editing.");
            }
          } else {
            print("Error: Required arguments for MyProfileEdit not provided");
            return _errorRoute(
                message:
                    "Invalid data provided for profile editing. Please try again.");
          }
        } catch (e) {
          print("Error navigating to MyProfileEdit: $e");
          return _errorRoute(message: "Error loading profile editor: $e");
        }
      case childInfo:
        try {
          // Check if we have a parentId from arguments
          if (settings.arguments is String) {
            final String parentId = settings.arguments as String;
            return MaterialPageRoute(
              builder: (_) => ChildInfo(parentId: parentId),
            );
          } else {
            // Just load the ChildInfo page without specific parent ID
            return MaterialPageRoute(
              builder: (_) => ChildInfo(),
            );
          }
        } catch (e) {
          print("Error navigating to ChildInfo: $e");
          return _errorRoute(message: "Error loading child information: $e");
        }

      case addChild:
        return MaterialPageRoute(builder: (_) => AddChild());
      case editChild:
        if (settings.arguments is ChildModel) {
          final child = settings.arguments as ChildModel;
          return MaterialPageRoute(builder: (_) => EditChild(child: child));
        } else {
          return _errorRoute(message: "Child data is required for editing");
        }
      case myAddress:
        return MaterialPageRoute(builder: (_) => MyAddress());
      case myCoupn:
        return MaterialPageRoute(builder: (_) => MyCoupn());
      case terms:
        return MaterialPageRoute(builder: (_) => TermsAndCondition());
      case contactUs:
        return MaterialPageRoute(
            builder: (_) => ContactUs(email: settings.arguments as String?));
      case faq:
        return MaterialPageRoute(builder: (_) => const FAQScreen());
      case contactUsSubmitted:
        return MaterialPageRoute(builder: (_) => ContactUsSubmitted());
      case myWallet:
        if (settings.arguments is int) {
          final zCoin = settings.arguments as int;
          return MaterialPageRoute(
              builder: (_) => MyWallet(
                    zCoin: zCoin,
                  ));
        }
        return _errorRoute();
      case mySubscriptions:
        return MaterialPageRoute(builder: (_) => MySubscriptionPage());
      case creditCard:
        // Pass the payment details to the CreditCardScreen if they exist
        print("Credit Card Route - Arguments: ${settings.arguments}");
        return MaterialPageRoute(
            builder: (_) =>
                CreditCardScreen(paymentDetails: settings.arguments));
      case unsubscribe:
        return MaterialPageRoute(builder: (_) => Unsubscribe());
      case unsubscribeMessage:
        return MaterialPageRoute(builder: (_) => UnsubscribeMessage());
      case topUp:
        if (settings.arguments is Map<String, dynamic>) {
          final args = settings.arguments as Map<String, dynamic>;
          int zCoin = args['zCoin'];
          // Use the provided discount value (which might be 0)
          int discount = args['discount'] ?? 0;
          int usd = args['usd'];
          String? planId = args['planId']; // Add plan ID parameter
          bool isSubscription = args['isSubscription'] ??
              false; // Add subscription flag parameter
          print(
              '$zCoin $usd with discount: $discount, planId: $planId, isSubscription: $isSubscription');
          return MaterialPageRoute(
              builder: (_) => TopUp(
                    usd: usd,
                    discount: discount,
                    zCoin: zCoin,
                    planId: planId,
                    isSubscription: isSubscription,
                  ));
        }
        return _errorRoute();
      case timeTableUp:
        return MaterialPageRoute(builder: (_) => TimetableUp());
      case viewall:
        return MaterialPageRoute(builder: (_) => Orders());
      case ordersDetailsConTimetable:
        if (settings.arguments is GetOrderByUserModel) {
          final order = settings.arguments as GetOrderByUserModel;
          return MaterialPageRoute(
              builder: (_) => OrdersDetailsConTimetable(
                    order: order,
                  ));
        }
        return _errorRoute();
      case eventDetailsConTimetable:
        if (settings.arguments is EventElement) {
          final event = settings.arguments as EventElement;
          return MaterialPageRoute(
              builder: (_) => EventDetailsConTimetable(
                    event: event,
                  ));
        }
        return _errorRoute();
      case qrcode:
        if (settings.arguments is EventElement) {
          final eventElement = settings.arguments as EventElement;
          return MaterialPageRoute(
              builder: (_) => QrCode(eventElement: eventElement));
        }
        return _errorRoute();
      case progressFeedbackUserDashboard:
        return MaterialPageRoute(
            builder: (_) => ProgressFeedBackUserDashbboard());
      case progressUser:
        if (settings.arguments is ReviewOfChildModel) {
          final review = settings.arguments as ReviewOfChildModel;
          return MaterialPageRoute(
              builder: (_) => ProgressUser(
                    reviewOfChildModel: review,
                  ));
        }
        return _errorRoute();
      case classDetails:
        if (settings.arguments is Map<String, dynamic>) {
          final args = settings.arguments as Map<String, dynamic>;
          final ClassModel? classModel = args['classModel'];
          final bool edit = args['edit'] as bool;
          final int? currentSessionIndex = args['currentSessionIndex'];

          return MaterialPageRoute(
              builder: (_) => ClassDetails(
                    classModel: classModel,
                    edit: edit,
                    currentSessionIndex: currentSessionIndex,
                  ));
        }
        return _errorRoute();

      case privateRequestIndividual:
        return MaterialPageRoute(builder: (_) => PrivateRequestIndividual());
      case ordersDetailsPending:
        return MaterialPageRoute(builder: (_) => OrdersDetailsPending());
      case changeChild:
        return MaterialPageRoute(builder: (_) => ChangeChild());
      case experience:
        if (settings.arguments is Map<String, dynamic>) {
          final args = settings.arguments as Map<String, dynamic>;
          String tier = args['tier'];
          int exp = args['exp'];
          double ratio = args['ratio'];
          return MaterialPageRoute(
              builder: (_) => MyExperience(
                    tier: tier,
                    exp: exp,
                    ratio: ratio,
                  ));
        }
        return _errorRoute();
      case programs:
        if (settings.arguments is String) {
          final id = settings.arguments as String?;
          return MaterialPageRoute(
              builder: (_) => UpcomingClass(
                    centerId: id,
                  ));
        }
        return _errorRoute();
      case timeslotCentreCourse:
        if (settings.arguments is Map<String, dynamic>) {
          final args = settings.arguments as Map<String, dynamic>;

          final ClassModel? classModel = args['classModel'];
          final CenterData? centerName = args['center'];
          final String? coachName = args['coachName'];
          return MaterialPageRoute(
            builder: (_) => TimeSlotCentreCourse(
              classModel: classModel,
              center: centerName,
              coachName: coachName,
            ),
          );
        }
        return _errorRoute();
      case reserved:
        return MaterialPageRoute(builder: (_) => Reserved());

      // Center setup routes with parameters

      case centerProfilesetup1:
        return MaterialPageRoute(builder: (_) => CenterProfileSetup1());
      case centerProfilesetup2:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => CenterProfileSetup2(data: data));
        }
        return _errorRoute();
      case centerProfilesetup3:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => CenterProfileSetup3(data: data));
        }
        return _errorRoute();

      case centerProfilesetup4:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => CenterProfileSetup4(data: data));
        }
        return _errorRoute();
      case centerProfilesetup5:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => CenterProfileSetup5(data: data));
        }
        return _errorRoute();
      case centerProfilesetup6:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => CenterProfileSetup6(data: data));
        }
        return _errorRoute();
      case centerMain:
        if (settings.arguments is CenterData) {
          final CenterData center = settings.arguments as CenterData;
          return MaterialPageRoute(builder: (_) => CenterMain(center: center));
        }
        return _errorRoute();

      case centerSettings:
        if (settings.arguments is CenterData) {
          final CenterData centerData = settings.arguments as CenterData;
          return MaterialPageRoute(
              builder: (_) => CenterSettings(center: centerData));
        }
        return _errorRoute();
      case centerMessage:
        if (settings.arguments is String) {
          final String userType = settings.arguments as String;
          return MaterialPageRoute(
              builder: (_) => CenterMessages(
                    userType: userType,
                  ));
        }
        return _errorRoute();
      case centerProfile:
        if (settings.arguments is CenterData) {
          final CenterData centerData = settings.arguments as CenterData;
          return MaterialPageRoute(
              builder: (_) => CenterProfile(centerModel: centerData));
        }
        return _errorRoute();
      case centerProfileEdit:
        if (settings.arguments is Map<String, dynamic>) {
          final args = settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => CenterProfileEdit(
                    imagePath: args['imagePath'],
                    centerId: args['centerId'] ?? '',
                    verified: args['verified'] ?? false,
                  ));
        }
        return _errorRoute();
      case centerDetails:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => BlocProvider(
                    create: (context) => locator<CenterBloc>(),
                    child: CenterDetails(
                        verified: data['verified'],
                        centerId: data['centerId'],
                        imagePath: data['imagePath']),
                  ));
        }
        return _errorRoute();

      case centerDescription:
        if (settings.arguments is String) {
          final centerId = settings.arguments as String;
          return MaterialPageRoute(
              builder: (_) => BlocProvider(
                    create: (context) => locator<CenterBloc>(),
                    child: CenterDescription(
                      centerId: centerId,
                    ),
                  ));
        } else if (settings.arguments is Map<String, dynamic>) {
          final args = settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => BlocProvider(
                    create: (context) => locator<CenterBloc>(),
                    child: CenterDescription(
                      centerId: args['centerId'] ?? '',
                    ),
                  ));
        }
        return _errorRoute();
      case centerBusiness:
        if (settings.arguments is String) {
          final String centerId = settings.arguments as String;
          return MaterialPageRoute(
              builder: (_) => BlocProvider(
                    create: (context) => locator<CenterBloc>(),
                    child: CenterBusiness(centerId: centerId),
                  ));
        }
        return _errorRoute();

      case centerPayout:
        if (settings.arguments is String) {
          final String centerId = settings.arguments as String;
          return MaterialPageRoute(
              builder: (_) => BlocProvider(
                    create: (context) => locator<CenterBloc>(),
                    child: CenterPayoutDetails(centerId: centerId),
                  ));
        }
        return _errorRoute();

      case centerBranch:
        return MaterialPageRoute(builder: (_) => CenterBranch());
      case checkin:
        final events = settings.arguments as List<EventModel>?;
        return MaterialPageRoute(builder: (_) => CheckIn(events: events ?? []));
      case verificationCode:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;

          return MaterialPageRoute(
            builder: (_) => VerificationCode(
              data: data,
            ),
          );
        }

        return _errorRoute();
      case pendingReview:
        return MaterialPageRoute(builder: (_) => PendingReview());
      case pendingReviewStudent:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => PendingReviewStudent(
                    classData: data,
                  ));
        }
        return _errorRoute();
      case pendingReviewsbyClass:
        if (settings.arguments is Map<String, dynamic>) {
          final data = settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => CenterPendingReviewClass(
                    data: data,
                  ));
        }
        return _errorRoute();
      case centerProgressCheck:
        if (settings.arguments is PendingModel) {
          final PendingModel pending = settings.arguments as PendingModel;
          return MaterialPageRoute(
              builder: (_) => CenterProgressCheck(
                    pending: pending,
                  ));
        }
        return _errorRoute();
      case coachManagementGrid:
        return MaterialPageRoute(builder: (_) => CoachManagementGrid());
      case availableCoaches:
        return MaterialPageRoute(
            builder: (_) => const AvailableCoachesScreen());
      case centerCoach:
        return MaterialPageRoute(builder: (_) => CenterCoach());
      case centerSavedClass:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => CenterSavedClass(
                    centerId: data['centerId'],
                  ));
        }
        return _errorRoute();
      case centerAddClass:
        return MaterialPageRoute(builder: (_) => CenterAddClass());
      case transaction:
        return MaterialPageRoute(builder: (_) => CenterTransaction());
      case intracsaction:
        if (settings.arguments is DailyTransactionModel) {
          final DailyTransactionModel transaction =
              settings.arguments as DailyTransactionModel;
          return MaterialPageRoute(
              builder: (_) =>
                  CenterIntracsaction(transactionDetail: transaction));
        } else if (settings.arguments is Map<String, dynamic>) {
          print(
              'Warning: intracsaction received a Map argument, expected DailyTransactionModel. Attempting to parse.');
          try {
            final DailyTransactionModel transaction =
                DailyTransactionModel.fromJson(
                    settings.arguments as Map<String, dynamic>);
            return MaterialPageRoute(
                builder: (_) =>
                    CenterIntracsaction(transactionDetail: transaction));
          } catch (e) {
            print('Error: Could not parse Map argument for intracsaction: $e');
            return _errorRoute(
                message: "Invalid data for transaction details.");
          }
        }
        return _errorRoute(message: "Missing or invalid transaction details.");
      case centerCoachRequest:
        return MaterialPageRoute(builder: (_) => CenterCoachRequest());
      case centerCoachSearch:
        return MaterialPageRoute(builder: (_) => CenterCoachSearch());
      case centerTimeSlotEdit:
        return MaterialPageRoute(builder: (_) => CenterTimeSlotEdit());
      case centerAddSlot:
        return MaterialPageRoute(builder: (_) => CenterAddSlot());
      case centerAnnouncement:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          final String filterType = data['filterType'] as String;
          final String filterValue = data['filterValue'] as String;
          return MaterialPageRoute(
              builder: (_) => CenterAnnouncement(
                    filterType: filterType,
                    filterValue: filterValue,
                  ));
        }
        return _errorRoute();
      case centerSendAnnouncement:
        if (settings.arguments is EventModel) {
          final event = settings.arguments as EventModel;
          return MaterialPageRoute(
              builder: (_) => CenterSendAnnouncement(
                    event: event,
                  ));
        }
        return _errorRoute();
      case centerSelectCoach:
        if (settings.arguments is Map<String, String>) {
          final Map<String, String> args =
              settings.arguments as Map<String, String>;
          final String classId = args['classId'] as String;
          final String coachId = args['coachId'] as String;
          final String className = args['className'] as String;
          return MaterialPageRoute(
              builder: (_) => CenterSelectCoach(
                    classId: classId,
                    coachId: coachId,
                    className: className,
                  ));
        }
        return _errorRoute();
      case centerUpdateCoachForClass:
        if (settings.arguments is Map<String, String>) {
          print('here cvoming');
          final Map<String, String> args =
              settings.arguments as Map<String, String>;
          final String classId = args['classId'] as String;
          final String className = args['className'] as String;
          final String coachId = args['coachId'] as String;
          return MaterialPageRoute(
              builder: (_) => CenterUpdateCoachForClass(
                    classId: classId,
                    className: className,
                    coachId: coachId,
                  ));
        }
        return _errorRoute();
      case centerTimeSlotSetup:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> args =
              settings.arguments as Map<String, dynamic>;
          final String coachId = args['coachId'] as String;
          final String classId =
              args['classId'] as String; // Assuming classId is also passed
          final String className = args['className'] as String;
          final String coachName = args['coachName'] as String;
          return MaterialPageRoute(
            builder: (_) => CenterTimeSlotSetup(
              classId: classId,
              coachId: coachId,
              className: className,
              coachName: coachName,
            ),
          );
        }
        return _errorRoute();

      case AppRoutes.centerSlotConfirmation:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> payload =
              settings.arguments as Map<String, dynamic>;

          return MaterialPageRoute(
            builder: (_) => CenterSlotConfirmation(
              payload: payload,
            ),
          );
        }
        return _errorRoute();

      case centerRequestPrivate:
        return MaterialPageRoute(builder: (_) => CenterRequestPrivate());
      case centerPressSlot:
        if (settings.arguments is EventModel) {
          final EventModel eventModel = settings.arguments as EventModel;
          return MaterialPageRoute(
              builder: (_) => CenterPressSlot(
                    event: eventModel,
                  ));
        }
        return _errorRoute();
      case studentList:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;

          return MaterialPageRoute(
              builder: (_) => StudentList(classData: data));
        }
        return _errorRoute();

      ////Coach
      ///
      ///

      case coachRegistration:
        return MaterialPageRoute(builder: (_) => CoachRegistration());
      case coachMain:
        if (settings.arguments is CoachModel) {
          final CoachModel coach = settings.arguments as CoachModel;
          return MaterialPageRoute(
              builder: (_) => CoachMain(
                    coach: coach,
                  ));
        }
        return _errorRoute();
      case coachSettings:
        return MaterialPageRoute(builder: (_) => CoachSettings());
      case coachSlot:
        return MaterialPageRoute(builder: (_) => CoachSlot());
      case coachPrograms:
        if (settings.arguments is Map<String, dynamic>) {
          Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => CoachPrograms(
                    centerId: data['centerId'],
                  ));
        }
        return _errorRoute();
      case coachPressSlot:
        if (settings.arguments is EventModel) {
          final EventModel event = settings.arguments as EventModel;
          return MaterialPageRoute(
              builder: (_) => CoachPressSlot(event: event));
        }
        return _errorRoute();
      // case coachStudentList:
      //   if (settings.arguments is Map<String, dynamic>) {
      //     final Map<String, dynamic> classData =
      //         settings.arguments as Map<String, dynamic>;
      //     return MaterialPageRoute(
      //         builder: (_) => CoachStudentList(
      //               classData: classData,
      //             ));
      //   }
      //   return _errorRoute();
      case coachMyProfile:
        return MaterialPageRoute(builder: (_) => CoachMyProfile());
      case coachProfileEdit:
        if (settings.arguments is String) {
          final String coachId = settings.arguments as String;
          return MaterialPageRoute(
            builder: (_) => CoachProfileEdit(coachId: coachId),
          );
        }
        return _errorRoute();
      case coachProfileEditDetails:
        if (settings.arguments is String) {
          return MaterialPageRoute(
              builder: (_) => BlocProvider(
                    create: (context) => locator<CoachBloc>(),
                    child: CoachProfileEditDetails(
                      coachId: settings.arguments as String,
                    ),
                  ));
        }
        return _errorRoute();
      case coachProfileEditDescription:
        if (settings.arguments is String) {
          return MaterialPageRoute(
              builder: (_) => BlocProvider(
                    create: (context) => locator<CoachBloc>(),
                    child: CoachProfileEditDescription(
                      coachId: settings.arguments as String,
                    ),
                  ));
        }
        return _errorRoute();
      case coachProfileEditSkills:
        if (settings.arguments is String) {
          return MaterialPageRoute(
              builder: (_) => BlocProvider(
                    create: (context) => locator<CoachBloc>(),
                    child: CoachProfileEditSkills(
                      coachId: settings.arguments as String,
                    ),
                  ));
        }
        return _errorRoute();
      case coachProfileEditAccredation:
        if (settings.arguments is String) {
          return MaterialPageRoute(
              builder: (_) => BlocProvider(
                    create: (context) => locator<CoachBloc>(),
                    child: CoachProfileEditAccredation(
                      coachId: settings.arguments as String,
                    ),
                  ));
        }
        return _errorRoute();
      case coachProfileEditExperience:
        if (settings.arguments is String) {
          return MaterialPageRoute(
              builder: (_) => BlocProvider(
                    create: (context) => locator<CoachBloc>(),
                    child: CoachProfileEditExperience(
                      coachId: settings.arguments as String,
                    ),
                  ));
        }
        return _errorRoute();
      case scan:
        if (settings.arguments is Function(String)) {
          final onScanned = settings.arguments as Function(String);
          return MaterialPageRoute(
            builder: (context) => QRScannerPage(onScanned: onScanned),
          );
        }
        return _errorRoute();

      case connectToYourCenter:
        return MaterialPageRoute(builder: (_) => ConnectToYourCenter());

      case announcementMessage:
        if (settings.arguments is Map<String, dynamic>) {
          final announcement = settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (context) => AnnouncementMessage(
                    announcement: announcement,
                  ));
        }
        return _errorRoute();

      case moments:
        try {
          print("Navigating to Moments with arguments: ${settings.arguments}");
          // If arguments is String and not empty, use it as childId
          if (settings.arguments is String &&
              (settings.arguments as String).isNotEmpty) {
            final childId = settings.arguments as String;
            print("Valid childId detected: $childId");
            return MaterialPageRoute(
                builder: (context) => Moments(
                      childId: childId,
                    ));
          } else {
            // If arguments is null or empty, show error dialog
            print("Invalid or missing childId for Moments route");
            return _errorRoute(message: "Please select a child first");
          }
        } catch (e) {
          print("Error navigating to Moments: $e");
          return _errorRoute(message: "Error loading moments: $e");
        }
      // case review:

      ///Owner

      case ownerMain:
        if (settings.arguments is UserModel) {
          final user = settings.arguments as UserModel;
          return MaterialPageRoute(builder: (_) => OwnerMain(user: user));
        } else {
          // Handle the case when no user is provided
          // Get the user from the shared repository if available
          try {
            final sharedRepo = locator<SharedRepository>();
            final userData = sharedRepo.getUserData();
            if (userData != null) {
              return MaterialPageRoute(
                  builder: (_) => OwnerMain(user: userData));
            }
          } catch (e) {
            print('Error getting user data: $e');
          }
          // If we can't get the user, navigate to login
          return MaterialPageRoute(builder: (_) => const LogIn());
        }
      case ownerRegistration:
        return MaterialPageRoute(builder: (_) => OwnerRegistration());
      case ownerSettings:
        return MaterialPageRoute(builder: (_) => OwnerAccountSettings());
      case ownerMyProfile:
        return MaterialPageRoute(builder: (_) => OwnerMyProfile());
      case ownerManage:
        return MaterialPageRoute(builder: (_) => OwnerManage());
      case ownerCoachProfile:
        if (settings.arguments is Map<String, dynamic>) {
          final data = settings.arguments as Map<String, dynamic>;
          if (data['coach'] != null) {
            final CoachModel coach = data['coach'];
            return MaterialPageRoute(
                builder: (_) => OwnerCoachprofile(
                      coach: coach,
                    ));
          } else {
            final coachId = data['coachId'];
            return MaterialPageRoute(
                builder: (_) => OwnerCoachprofile(
                      coachId: coachId,
                    ));
          }
        }
        return _errorRoute();
      case ownerCenterProfile:
        if (settings.arguments is CenterData) {
          final CenterData centerData = settings.arguments as CenterData;
          return MaterialPageRoute(
              builder: (_) => OwnerCenterProfile(branch: centerData));
        }
        return _errorRoute();
      case ownerManagerEdit:
        if (settings.arguments is Map<String, dynamic>) {
          Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => OwnerSearchManager(data: data));
        }
        return _errorRoute();
      case ownerCoachEdit:
        if (settings.arguments is Map<String, dynamic>) {
          Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => OwnerSearchCoach(
                    data: data,
                  ));
        }
        return _errorRoute();
      case ownerProfieEdit:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => BlocProvider(
                    create: (context) => locator<OwnerBloc>(),
                    child: OwnerProfileEdit(
                      imagePath: data['imagepath'],
                      ownerId: data['ownerId'],
                    ),
                  ));
        }
        return _errorRoute();
      case filterScreen:
        if (settings.arguments is Map<String, dynamic>) {
          final Map<String, dynamic> data =
              settings.arguments as Map<String, dynamic>;
          return MaterialPageRoute(
              builder: (_) => FilterScreen(
                    title: data['title'],
                    minPrice: data['minPrice'],
                    maxPrice: data['maxPrice'],
                    minAge: data['minAge'],
                    maxAge: data['maxAge'],
                    location: data['location'],
                    sortBy: data['sortBy'],
                    rating: data['rating'],
                    senService: data['senService'],
                  ));
        }
        return _errorRoute();
      case savedCenters:
        return MaterialPageRoute(
          builder: (_) => const SavedCentersScreen(),
        );

      case childReport:
        return MaterialPageRoute(builder: (_) => const ChildReportScreen());
      case myReviews:
        return MaterialPageRoute(builder: (_) => const MyReviewsScreenNN());
      case changeCoach:
        return MaterialPageRoute(builder: (_) => const ChangeCoachScreen());
      case refund:
        final parentId =
            locator<SharedRepository>().getUserData()?.data?.parent?.id;
        if (parentId != null && parentId.isNotEmpty) {
          return MaterialPageRoute(
            builder: (_) => RefundScreen(parentId: parentId),
          );
        } else {
          return _errorRoute(
              message: "Parent ID not found. Please login again.");
        }
      case centerClassDetails:
        if (settings.arguments is ClassModel) {
          final classModel = settings.arguments as ClassModel;
          return MaterialPageRoute(
            builder: (_) => CenterClassDetailsPage(classModel: classModel),
          );
        }
        return _errorRoute();
      default:
        return _errorRoute();
    }
  }

  static Route<dynamic> _errorRoute({String? message}) {
    return MaterialPageRoute(
      builder: (context) => Scaffold(
        appBar: AppBar(
          title: Text('Error'),
          backgroundColor: Colors.blue,
          leading: IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 60,
                ),
                SizedBox(height: 20),
                Text(
                  'Page not found',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 10),
                Text(
                  message ??
                      'The requested page could not be loaded. Please try again or go back.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(height: 30),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                  ),
                  child: Text('Go Back'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
