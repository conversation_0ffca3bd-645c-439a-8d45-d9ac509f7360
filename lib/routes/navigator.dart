import 'package:flutter/material.dart';

class NavigatorService {
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  static Future<dynamic> pushNamed(
    String routeName, {
    dynamic arguments,
  }) async {
    print('NavigatorService.pushNamed called for route: $routeName');
    
    try {
      // Check if we have a valid navigator state first
      if (navigatorKey.currentState == null) {
        print('Error: No valid navigator state available for pushNamed');
        return null;
      }
      
      return navigatorKey.currentState!
          .pushNamed(routeName, arguments: arguments);
    } catch (e) {
      print('Error in pushNamed: $e');
      return null;
    }
  }

  static void goBack([dynamic result]) {
    print('NavigatorService.goBack called');
    
    try {
      // Check if we have a current navigator state
      if (navigatorKey.currentState == null) {
        print('Error: No navigator state available for goBack');
        return;
      }
      
      // Check if we can pop before attempting to
      if (navigatorKey.currentState!.canPop()) {
        print('Can pop - proceeding with pop');
        navigatorKey.currentState!.pop(result);
        print('Pop successful');
      } else {
        print('Cannot pop - at root route already');
      }
    } catch (e) {
      print('Error in goBack: $e');
    }
  }

  static Future<dynamic> pushNamedAndRemoveUntil(
    String routeName, {
    dynamic arguments,
  }) async {
    print('NavigatorService.pushNamedAndRemoveUntil called for route: $routeName');
    
    try {
      // Check if we have a valid navigator state first
      if (navigatorKey.currentState == null) {
        print('Error: No valid navigator state available for pushNamedAndRemoveUntil');
        return null;
      }
      
      return navigatorKey.currentState!.pushNamedAndRemoveUntil(
        routeName,
        (route) => false, // Remove all routes
        arguments: arguments,
      );
    } catch (e) {
      print('Error in pushNamedAndRemoveUntil: $e');
      return null;
    }
  }

  static Future<dynamic> popAndPushNamed(
    String routeName, {
    dynamic arguments,
  }) async {
    print('NavigatorService.popAndPushNamed called for route: $routeName');
    
    try {
      // Check if we have a valid navigator state first
      if (navigatorKey.currentState == null) {
        print('Error: No valid navigator state available for popAndPushNamed');
        return null;
      }
      
      return navigatorKey.currentState!
          .popAndPushNamed(routeName, arguments: arguments);
    } catch (e) {
      print('Error in popAndPushNamed: $e');
      return null;
    }
  }

  static void removePagesAndNavigate(String routeName, int number,
      {dynamic arguments}) {
    print('NavigatorService.removePagesAndNavigate called');
    
    try {
      if (navigatorKey.currentState == null) {
        print('Error: No navigator state available for removePagesAndNavigate');
        return;
      }
      
      // Pop specified number of routes
      for (int i = 0; i < number; i++) {
        if (navigatorKey.currentState!.canPop()) {
          navigatorKey.currentState!.pop();
        } else {
          break; // Stop if we can't pop anymore
        }
      }

      // Navigate to the specified route
      pushNamedAndRemoveUntil(routeName, arguments: arguments);
    } catch (e) {
      print('Error in removePagesAndNavigate: $e');
    }
  }

  static void removePages(int number) {
    print('NavigatorService.removePages called for $number pages');
    
    try {
      if (navigatorKey.currentState == null) {
        print('Error: No navigator state available for removePages');
        return;
      }
      
      int count = 0;
      while (navigatorKey.currentState!.canPop() == true && count < number) {
        navigatorKey.currentState!.pop();
        count++;
      }
      
      print('Successfully removed $count pages');
    } catch (e) {
      print('Error in removePages: $e');
    }
  }

  static void popUntil(String targetRouteName) {
    print('NavigatorService.popUntil called for route: $targetRouteName');
    
    try {
      if (navigatorKey.currentState == null) {
        print('Error: No navigator state available for popUntil');
        return;
      }
      
      navigatorKey.currentState!.popUntil(
        (route) => route.settings.name == targetRouteName,
      );
    } catch (e) {
      print('Error in popUntil: $e');
    }
  }
}
