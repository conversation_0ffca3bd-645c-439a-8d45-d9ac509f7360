// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_methods
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can create these using the Firebase console.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can create these using the Firebase console.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can create these using the Firebase console.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can create these using the Firebase console.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  // The android and iOS options will be populated with values from your
  // google-services.json and GoogleService-Info.plist files respectively
  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAutPSaBfBYKpCJK_bu0QMBcxVRhU-w1rY',
    appId: '1:621865971153:android:bd0f56140b64acc4d804f0',
    messagingSenderId: '621865971153',
    projectId: 'classz-97acc',
    storageBucket: 'classz-97acc.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAz_mze3ymUZuogu5FPNYWh3haaCXJxGWM',
    appId: '1:621865971153:ios:35503c81c87cd1cbd804f0',
    messagingSenderId: '621865971153',
    projectId: 'classz-97acc',
    storageBucket: 'classz-97acc.firebasestorage.app',
  );
} 