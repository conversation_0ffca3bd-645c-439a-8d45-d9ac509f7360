// Helper method to validate if a file is an image
import 'dart:io';

import 'package:dio/dio.dart' as dio;
import 'package:path/path.dart' as path;
bool isImageFile(String filePath) {
  final validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
  final extension = path.extension(filePath).toLowerCase();
  return validExtensions.contains(extension);
}

// Helper method to create a MultipartFile with proper image content type
dio.MultipartFile createImageMultipartFile(File file) {
  final extension = path.extension(file.path).toLowerCase();
  String contentType;

  switch (extension) {
    case '.jpg':
    case '.jpeg':
      contentType = 'image/jpeg';
      break;
    case '.png':
      contentType = 'image/png';
      break;
    case '.gif':
      contentType = 'image/gif';
      break;
    case '.webp':
      contentType = 'image/webp';
      break;
    case '.bmp':
      contentType = 'image/bmp';
      break;
    default:
      contentType = 'image/jpeg'; // Default fallback
  }

  return dio.MultipartFile.fromFileSync(
    file.path,
    filename: path.basename(file.path),
    contentType: dio.DioMediaType.parse(contentType),
  );
}
