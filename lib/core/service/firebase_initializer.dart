import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:class_z/firebase_options.dart';

/// A utility class for initializing Firebase
class FirebaseInitializer {
  static bool _initialized = false;
  static bool _initializing = false;

  /// Check if Firebase has been initialized
  static bool get isInitialized => _initialized;

  /// Set Firebase initialization status
  static set initialized(bool value) {
    _initialized = value;
  }

  /// Initialize Firebase with appropriate platform-specific configurations
  static Future<bool> initialize() async {
    if (_initialized) {
      print('Firebase is already initialized');
      return true;
    }

    if (_initializing) {
      print('Firebase initialization already in progress');
      return false;
    }

    _initializing = true;

    try {
      // Initialize Firebase with platform-specific options
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      _initialized = true;
      _initializing = false;
      print('Firebase initialized successfully');
      return true;
    } catch (e) {
      _initializing = false;
      print('Firebase initialization failed: $e');
      return false;
    }
  }

  /// Request notification permissions (particularly important for iOS)
  static Future<NotificationSettings?> requestPermissions() async {
    if (!_initialized) {
      print('Firebase not initialized, cannot request permissions');
      return null;
    }

    try {
      final messaging = FirebaseMessaging.instance;
      final settings = await messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      print('User granted permission: ${settings.authorizationStatus}');
      return settings;
    } catch (e) {
      print('Error requesting notification permissions: $e');
      return null;
    }
  }
}
