import 'dart:math';
import 'package:class_z/core/imports.dart';
import 'package:class_z/core/service/firebase_initializer.dart';

class Notificationservice {
  static final Notificationservice _instance = Notificationservice._internal();

  factory Notificationservice() => _instance;

  Notificationservice._internal();

  // Use late initialization to avoid accessing FirebaseMessaging before Firebase is initialized
  late FirebaseMessaging messaging;
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // Initialize the notification service
  Future<void> initialize(BuildContext context) async {
    // Check if Firebase is already initialized
    bool isFirebaseInitialized = FirebaseInitializer.isInitialized;

    if (!isFirebaseInitialized) {
      // Try to initialize Firebase if not already done
      isFirebaseInitialized = await FirebaseInitializer.initialize();
      if (!isFirebaseInitialized) {
        print('Firebase initialization failed in NotificationService');
        // Show a message to the user or handle the error
        return;
      }
    }

    // Now it's safe to access FirebaseMessaging
    messaging = FirebaseMessaging.instance;

    // For iOS, ensure APNS token is available before proceeding
    if (Platform.isIOS) {
      await ensureAPNSToken();
    }

    await _initNotificationChannels();
    await requestNotificationPermission(context);
    await _initLocalNotifications(context);
    await handleTerminateMessage(context);
    FirebaseInit(context);
  }

  // Initialize notification channels
  Future<void> _initNotificationChannels() async {
    if (Platform.isAndroid) {
      // Create high importance channel
      AndroidNotificationChannel highChannel = const AndroidNotificationChannel(
        'high_importance_channel',
        'High Importance Notifications',
        description: 'This channel is used for important notifications.',
        importance: Importance.high,
      );

      // Create default channel
      AndroidNotificationChannel defaultChannel =
          const AndroidNotificationChannel(
        'default_channel',
        'Default Notifications',
        description: 'This channel is used for default notifications.',
        importance: Importance.defaultImportance,
      );

      // Create chat channel
      AndroidNotificationChannel chatChannel = const AndroidNotificationChannel(
        'chat_channel',
        'Chat Notifications',
        description: 'This channel is used for chat notifications.',
        importance: Importance.high,
        enableVibration: true,
      );

      // Register each channel individually
      final AndroidFlutterLocalNotificationsPlugin? androidPlugin =
          _flutterLocalNotificationsPlugin
              .resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin>();

      if (androidPlugin != null) {
        await androidPlugin.createNotificationChannel(highChannel);
        await androidPlugin.createNotificationChannel(defaultChannel);
        await androidPlugin.createNotificationChannel(chatChannel);
      }
    }
  }

  // Request notification permission
  Future<void> requestNotificationPermission(BuildContext context) async {
    try {
      if (!FirebaseInitializer.isInitialized) {
        print(
            'Firebase not initialized, cannot request notification permissions');
        return;
      }

      NotificationSettings notificationSettings =
          await messaging.requestPermission(
              alert: true,
              announcement: true,
              badge: true,
              carPlay: true,
              criticalAlert: true,
              provisional: true,
              sound: true);

      if (notificationSettings.authorizationStatus ==
          AuthorizationStatus.authorized) {
        print("Permission granted");
      } else if (notificationSettings.authorizationStatus ==
          AuthorizationStatus.provisional) {
        print("Provisional permission granted");
      } else if (notificationSettings.authorizationStatus ==
          AuthorizationStatus.denied) {
        print("Permission denied");
        // Show dialog to prompt user
        _showSettingsDialog(context);
      } else {
        print("Permission not granted");
      }
    } catch (e) {
      print('Error requesting notification permissions: $e');
    }
  }

  // Initialize local notifications
  Future<void> _initLocalNotifications(BuildContext context) async {
    var androidInitializationSettings =
        const AndroidInitializationSettings('@mipmap/ic_launcher');
    final iosInitializationSettings = const DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    var initializationSettings = InitializationSettings(
        android: androidInitializationSettings, iOS: iosInitializationSettings);

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        // Handle notification tap
        final String? payload = response.payload;
        if (payload != null && payload.isNotEmpty) {
          // Parse payload and navigate accordingly
          _handleNotificationTap(context, payload);
        }
      },
    );
  }

  // Handle notification tap
  void _handleNotificationTap(BuildContext context, String payload) {
    try {
      final Map<String, dynamic> data = json.decode(payload);
      if (data.containsKey('url')) {
        handleMessage(context, RemoteMessage(data: data));
      }
    } catch (e) {
      print('Error handling notification tap: $e');
    }
  }

  // Show notification
  Future<void> showNotification(RemoteMessage message) async {
    String channelId = 'default_channel';

    // Determine which notification channel to use based on message type
    if (message.data.containsKey('type')) {
      if (message.data['type'] == 'chat') {
        channelId = 'chat_channel';
      } else if (message.data['type'] == 'important') {
        channelId = 'high_importance_channel';
      }
    }

    // Create notification details
    AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
            channelId,
            channelId == 'chat_channel'
                ? 'Chat Notifications'
                : (channelId == 'high_importance_channel'
                    ? 'High Importance Notifications'
                    : 'Default Notifications'),
            channelDescription: 'Notification channel',
            importance: Importance.high,
            priority: Priority.high,
            ticker: 'ticker');

    DarwinNotificationDetails darwinNotificationDetails =
        const DarwinNotificationDetails(
            presentAlert: true, presentBadge: true, presentSound: true);

    NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails, iOS: darwinNotificationDetails);

    // Show notification
    Future.delayed(
      Duration.zero,
      () {
        if (message.notification != null) {
          _flutterLocalNotificationsPlugin.show(
            Random.secure().nextInt(100000), // Random ID to avoid overwriting
            message.notification!.title ?? 'New Notification',
            message.notification!.body ?? '',
            notificationDetails,
            payload: json.encode(message.data), // Store data as payload
          );
        } else if (message.data.isNotEmpty) {
          // If no notification but has data, create one from data
          _flutterLocalNotificationsPlugin.show(
            Random.secure().nextInt(100000),
            message.data['title'] ?? 'New Notification',
            message.data['body'] ?? '',
            notificationDetails,
            payload: json.encode(message.data),
          );
        }
      },
    );
  }

  // Initialize Firebase messaging
  void FirebaseInit(BuildContext context) {
    try {
      if (!FirebaseInitializer.isInitialized) {
        print('Firebase not initialized, cannot set up messaging listeners');
        return;
      }

      // Listen for messages when app is in foreground
      FirebaseMessaging.onMessage.listen((message) {
        print('Got a message whilst in the foreground!');
        print('Message data: ${message.data}');

        if (Platform.isAndroid) {
          showNotification(message);
        } else if (Platform.isIOS) {
          // For iOS, we let the system handle foreground notifications
          // unless we specifically want to customize them
          showNotification(message);
        }
      });
    } catch (e) {
      print('Error initializing Firebase messaging: $e');
    }
  }

  // Get device token with enhanced retry logic
  Future<String> getDeviceToken() async {
    if (!FirebaseInitializer.isInitialized) {
      print('Firebase not initialized, cannot get device token');
      await FirebaseInitializer.initialize();
      if (!FirebaseInitializer.isInitialized) {
        return '';
      }
    }

    try {
      // For iOS, we need to make sure the APNS token is available first
      if (Platform.isIOS) {
        // Request notification permissions first to ensure APNS token is generated
        await messaging.requestPermission(
          alert: true,
          badge: true,
          sound: true,
        );

        // Use the enhanced APNS token retrieval method
        String? apnsToken = await ensureAPNSToken();
        if (apnsToken == null) {
          print(
              'APNS token not available after enhanced retry - continuing anyway');
          // Continue to try FCM token anyway, but it might not work reliably
        } else {
          print(
              'APNS token successfully obtained: ${apnsToken.substring(0, 10)}...');
        }
      }

      // Now try to get the FCM token with retry logic
      String? token = await _getFCMTokenWithRetry();
      if (token != null && token.isNotEmpty) {
        print('FCM token retrieved: ${token.substring(0, 10)}...');
        return token;
      } else {
        print('FCM token is null or empty after all retries');
        return '';
      }
    } catch (e) {
      print('Error getting device token: $e');
      return '';
    }
  }

  // Helper method to get FCM token with retry logic
  Future<String?> _getFCMTokenWithRetry() async {
    const maxRetries = 4;
    const baseDelay = Duration(seconds: 2);

    for (int attempt = 0; attempt < maxRetries; attempt++) {
      try {
        String? token = await messaging.getToken();
        if (token != null && token.isNotEmpty) {
          print('FCM token retrieved successfully on attempt ${attempt + 1}');
          return token;
        }

        if (attempt < maxRetries - 1) {
          // Exponential backoff: 2s, 4s, 8s
          final delay = Duration(seconds: baseDelay.inSeconds * (1 << attempt));
          print(
              'FCM token empty, retrying in ${delay.inSeconds}s (attempt ${attempt + 2})...');
          await Future.delayed(delay);
        }
      } catch (e) {
        print('Error getting FCM token on attempt ${attempt + 1}: $e');
        if (attempt < maxRetries - 1) {
          final delay = Duration(seconds: baseDelay.inSeconds * (1 << attempt));
          await Future.delayed(delay);
        }
      }
    }

    return null;
  }

  // Delete token when user logs out
  Future<void> deleteToken() async {
    if (!FirebaseInitializer.isInitialized) {
      print('Firebase not initialized, cannot delete token');
      return;
    }

    try {
      await messaging.deleteToken();
      print('FCM token deleted');
    } catch (e) {
      print('Error deleting FCM token: $e');
    }
  }

  // Handle app open from terminated state
  Future<void> handleTerminateMessage(BuildContext context) async {
    if (!FirebaseInitializer.isInitialized) {
      print('Firebase not initialized, cannot handle terminate message');
      return;
    }

    try {
      // Get any messages which caused the application to open
      RemoteMessage? initialMessage =
          await FirebaseMessaging.instance.getInitialMessage();

      if (initialMessage != null) {
        print(
            'App opened from terminated state with message: ${initialMessage.messageId}');
        handleMessage(context, initialMessage);
      }

      // Listen for when the app is opened from a background state
      FirebaseMessaging.onMessageOpenedApp.listen((event) {
        print(
            'App opened from background state with message: ${event.messageId}');
        handleMessage(context, event);
      });
    } catch (e) {
      print('Error handling terminate message: $e');
    }
  }

  // Handle message based on message data
  void handleMessage(BuildContext context, RemoteMessage message) {
    if (message.data['url'] != null) {
      print('nn is here');
      print(message.data.toString());
      final data = message.data;
      String url = message.data['url'];
      print("Handling navigation for URLA: $url");

      // Ensure the route exists before navigating
      if (url == '/assign') {
        print("Handling assign request");
        showAlertDialogWithAcceptReject(context, message.data);
      } else if (url == "/announcement") {
        print('Navigating to announcement message');
        NavigatorService.pushNamed(
          AppRoutes.announcementMessage,
          arguments: {
            "id": data['announcementId'],
            "className": message.notification?.title ?? 'Unknown',
          },
        );
      } else if (url == "/chat") {
        // Example of how to navigate to chat with specific parameters
        if (message.data.containsKey('chatId')) {
          print('Navigating to chat with ID: ${message.data['chatId']}');
          // Navigate to chat screen with chatId
          // Example: NavigatorService.pushNamed(AppRoutes.chatScreen, arguments: {'chatId': message.data['chatId']});
        }
      } else {
        print("Invalid or unhandled route: $url");
      }
    }
  }

  // Show dialog for notification permission
  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Permission Needed"),
          content: Text(
              "This app needs notification permissions to function properly. Do you want to allow them?"),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
              child: Text("Don't Allow"),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
                AppSettings.openAppSettings(); // Open app settings
              },
              child: Text("Allow"),
            ),
          ],
        );
      },
    );
  }

  // Show dialog with accept/reject options
  void showAlertDialogWithAcceptReject(
      BuildContext context, Map<String, dynamic> data) {
    // Implementation stays the same...
    // This is likely specific to your application logic
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("New Request"),
          content: Text("You have received a new assignment request."),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
                // Handle reject logic
              },
              child: Text("Reject"),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
                // Handle accept logic
              },
              child: Text("Accept"),
            ),
          ],
        );
      },
    );
  }

  // Ensure APNS token is available (iOS only) with enhanced retry logic
  Future<String?> ensureAPNSToken() async {
    if (!Platform.isIOS) return null;

    if (!FirebaseInitializer.isInitialized) {
      print('Firebase not initialized, cannot get APNS token');
      return null;
    }

    try {
      // Request permissions first
      await messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      // Enhanced retry logic with exponential backoff
      const maxRetries = 6;
      const baseDelay = Duration(seconds: 1);

      for (int attempt = 0; attempt < maxRetries; attempt++) {
        try {
          String? apnsToken = await messaging.getAPNSToken();

          if (apnsToken != null) {
            print('APNS token available on attempt ${attempt + 1}');
            return apnsToken;
          }

          if (attempt < maxRetries - 1) {
            // Exponential backoff: 1s, 2s, 4s, 8s, 16s
            final delay =
                Duration(seconds: baseDelay.inSeconds * (1 << attempt));
            print(
                'APNS token not available, waiting ${delay.inSeconds}s before retry ${attempt + 2}...');
            await Future.delayed(delay);
          }
        } catch (e) {
          print('Error getting APNS token on attempt ${attempt + 1}: $e');
          if (attempt < maxRetries - 1) {
            final delay =
                Duration(seconds: baseDelay.inSeconds * (1 << attempt));
            await Future.delayed(delay);
          }
        }
      }

      print('APNS token still not available after all retries');
      return null;
    } catch (e) {
      print('Error ensuring APNS token: $e');
      return null;
    }
  }
}
