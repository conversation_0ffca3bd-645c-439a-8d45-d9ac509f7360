import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:class_z/core/service/firebase_initializer.dart';

class FirebaseService {
  late FirebaseMessaging _firebaseMessaging;
  bool _initialized = false;

  // Initialize Firebase
  Future<void> initializeFirebase() async {
    // If already initialized, don't do it again
    if (_initialized) {
      print('FirebaseService already initialized - skipping');
      return;
    }

    // Check if Firebase is already initialized from elsewhere
    if (FirebaseInitializer.isInitialized) {
      _initialized = true;
      print(
          'Firebase was already initialized elsewhere, FirebaseService is now ready');

      try {
        _firebaseMessaging = FirebaseMessaging.instance;

        // Request permission for iOS
        if (!kIsWeb && defaultTargetPlatform == TargetPlatform.iOS) {
          await FirebaseInitializer.requestPermissions();
        }

        // Get the FCM token
        String? token = await getDeviceToken();
        if (token != null && token.isNotEmpty) {
          print('FCM Token initialized successfully');
        }

        // Subscribe to topics if needed
        await _firebaseMessaging.subscribeToTopic('all_users');
      } catch (e) {
        print('Firebase Messaging setup failed: $e');
        print('Some notification features may not work');
      }
      return;
    }

    try {
      // Use the FirebaseInitializer to ensure Firebase is initialized
      final initialized = await FirebaseInitializer.initialize();

      if (!initialized) {
        print('FirebaseService: Firebase initialization failed');
        return;
      }

      _initialized = true;
      print('FirebaseService: Firebase is ready');

      try {
        _firebaseMessaging = FirebaseMessaging.instance;

        // Request permission for iOS
        if (!kIsWeb && defaultTargetPlatform == TargetPlatform.iOS) {
          await FirebaseInitializer.requestPermissions();
        }

        // Get the FCM token
        String? token = await getDeviceToken();
        if (token != null && token.isNotEmpty) {
          print('FCM Token initialized successfully');
        }

        // Subscribe to topics if needed
        await _firebaseMessaging.subscribeToTopic('all_users');
      } catch (e) {
        print('Firebase Messaging setup failed: $e');
        print('Some notification features may not work');
      }
    } catch (e) {
      print("Error in FirebaseService initialization: $e");
    }
  }

  // Retrieve device token with improved retry logic
  Future<String?> getDeviceToken() async {
    if (!_initialized) {
      print('Trying to get device token before Firebase is initialized');
      await initializeFirebase();
      if (!_initialized) {
        print('Firebase initialization failed, cannot get device token');
        return null;
      }
    }

    try {
      // For iOS, we need to make sure the APNS token is available first
      if (!kIsWeb && defaultTargetPlatform == TargetPlatform.iOS) {
        // Request permissions to ensure APNS token is generated
        final settings = await _firebaseMessaging.requestPermission(
          alert: true,
          badge: true,
          sound: true,
          provisional: false,
        );

        print('User granted permission: ${settings.authorizationStatus}');

        // Enhanced APNS token retrieval with multiple retries
        String? apnsToken = await _waitForAPNSToken();
        if (apnsToken == null) {
          print('APNS token still not available after all retries');
          // Continue anyway, but FCM token might not work reliably
        } else {
          print(
              'APNS token successfully retrieved: ${apnsToken.substring(0, 10)}...');
        }
      }

      // Now try to get the FCM token with enhanced retry logic
      String? token = await _getFCMTokenWithRetry();

      if (token == null || token.isEmpty) {
        print('Error: Unable to retrieve FCM token after all retries');
        return null;
      }

      // Log a portion of the token for debugging (avoid logging full token for security)
      if (token.length > 10) {
        print('FCM token retrieved successfully: ${token.substring(0, 10)}...');
      } else {
        print('FCM token retrieved successfully (short token)');
      }

      return token;
    } catch (e) {
      print('Error getting device token: $e');
      return null;
    }
  }

  // Enhanced APNS token waiting with exponential backoff
  Future<String?> _waitForAPNSToken() async {
    const maxRetries = 5;
    const baseDelay = Duration(seconds: 2);

    for (int attempt = 0; attempt < maxRetries; attempt++) {
      try {
        String? apnsToken = await _firebaseMessaging.getAPNSToken();
        if (apnsToken != null) {
          print('APNS token available on attempt ${attempt + 1}');
          return apnsToken;
        }

        if (attempt < maxRetries - 1) {
          // Exponential backoff: 2s, 4s, 8s, 16s
          final delay = Duration(seconds: baseDelay.inSeconds * (1 << attempt));
          print(
              'APNS token not available, waiting ${delay.inSeconds}s before retry ${attempt + 2}...');
          await Future.delayed(delay);
        }
      } catch (e) {
        print('Error getting APNS token on attempt ${attempt + 1}: $e');
        if (attempt < maxRetries - 1) {
          final delay = Duration(seconds: baseDelay.inSeconds * (1 << attempt));
          await Future.delayed(delay);
        }
      }
    }

    return null;
  }

  // Enhanced FCM token retrieval with retry logic
  Future<String?> _getFCMTokenWithRetry() async {
    const maxRetries = 3;
    const delay = Duration(seconds: 3);

    for (int attempt = 0; attempt < maxRetries; attempt++) {
      try {
        String? token = await _firebaseMessaging.getToken();
        if (token != null && token.isNotEmpty) {
          print('FCM token retrieved on attempt ${attempt + 1}');
          return token;
        }

        if (attempt < maxRetries - 1) {
          print(
              'FCM token empty, retrying in ${delay.inSeconds}s (attempt ${attempt + 2})...');
          await Future.delayed(delay);
        }
      } catch (e) {
        print('Error getting FCM token on attempt ${attempt + 1}: $e');
        if (attempt < maxRetries - 1) {
          await Future.delayed(delay);
        }
      }
    }

    return null;
  }

  // Method to subscribe to a specific topic
  Future<void> subscribeToTopic(String topic) async {
    if (!_initialized) {
      await initializeFirebase();
      if (!_initialized) return;
    }

    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      print('Subscribed to topic: $topic');
    } catch (e) {
      print('Error subscribing to topic: $e');
    }
  }

  // Method to unsubscribe from a specific topic
  Future<void> unsubscribeFromTopic(String topic) async {
    if (!_initialized) {
      await initializeFirebase();
      if (!_initialized) return;
    }

    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      print('Unsubscribed from topic: $topic');
    } catch (e) {
      print('Error unsubscribing from topic: $e');
    }
  }

  // Method to handle FCM token refresh
  void setupTokenRefreshListener(Function(String) onTokenRefresh) {
    if (!_initialized) {
      print(
          'Warning: Setting up token refresh listener before Firebase is initialized');
      // Will initialize on next method call
      return;
    }

    try {
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        print('FCM token refreshed');
        onTokenRefresh(newToken);
      });
    } catch (e) {
      print('Error setting up token refresh listener: $e');
    }
  }
}
