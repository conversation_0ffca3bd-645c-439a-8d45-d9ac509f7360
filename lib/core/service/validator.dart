      import 'dart:io';

void validateFile(String field, dynamic value) {
        if (value is File) {
          // Check if the file exists and is readable
          if (!value.existsSync()) {
            throw Exception('File does not exist: ${value.path}');
          }
          try {
            final length = value.lengthSync();
            print(
                'File validated: $field, path: ${value.path}, size: $length bytes');
          } catch (e) {
            throw Exception('Failed to read file: ${value.path}, error: $e');
          }
        } else if (value is List && value.isNotEmpty && value.first is File) {
          for (var file in value) {
            validateFile(field, file);
          }
        }
      }