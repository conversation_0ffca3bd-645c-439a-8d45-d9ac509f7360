import 'dart:convert';
import 'package:class_z/core/utils/auth_helper.dart';
import 'package:class_z/core/utils/shared_repo.dart';
import 'package:class_z/dependency_injection/injection.dart';
import 'package:http/http.dart' as http;

class ApiService {
  final String baseUrl;

  ApiService({required this.baseUrl});

  Future<dynamic> get(
    String endpoint, {
    String? token,
    Map<String, dynamic>? queryParameters,
  }) async {
    // Create the URI with query parameters if provided
    Uri uri = Uri.parse('$baseUrl$endpoint');
    if (queryParameters != null && queryParameters.isNotEmpty) {
      uri = uri.replace(
          queryParameters: queryParameters
              .map((key, value) => MapEntry(key, value.toString())));
    }

    print('Request URI: $uri');

    // Set headers
    Map<String, String> headers = {};

    // If the token is provided, validate it before using
    if (token != null && token.isNotEmpty) {
      // Validate token using AuthHelper
      final authHelper = locator<AuthHelper>();
      if (authHelper.isTokenExpired(token)) {
        throw Exception('Authentication failed - token expired');
      }
      headers['auth-token'] = token;
    }

    print('Headers: $headers');
    try {
      final response = await http.get(
        uri,
        headers: headers.isNotEmpty ? headers : null,
      );

      // Handle 401 errors specifically
      if (response.statusCode == 401) {
        throw Exception('Authentication failed - please log in again');
      }

      return _processResponse(response);
    } catch (e) {
      print('API Error: $e');
      rethrow;
    }
  }

  Future<dynamic> post(String endpoint, Map<String, dynamic> data) async {
    print(endpoint);
    print(data);
    final response = await http.post(
      Uri.parse('$baseUrl$endpoint'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode(data),
    );
    return _processResponse(response);
  }

  Future<dynamic> postWithFiles(String endpoint, Map<String, String> fields,
      List<http.MultipartFile> files) async {
    var uri = Uri.parse('$baseUrl$endpoint');
    var request = http.MultipartRequest('POST', uri)
      ..fields.addAll(fields)
      ..files.addAll(files);
    var streamedResponse = await request.send();
    var response = await http.Response.fromStream(streamedResponse);
    return _processResponse(response);
  }

  Future<dynamic> put(String endpoint, Map<String, dynamic> data,
      {String? token}) async {
    print('$baseUrl$endpoint');
    // Set headers
    Map<String, String> headers = {
      'Content-Type': 'application/json',
    };

    // If the token is provided, validate it before using
    if (token != null && token.isNotEmpty) {
      // Validate token using AuthHelper
      final authHelper = locator<AuthHelper>();
      if (authHelper.isTokenExpired(token)) {
        throw Exception('Authentication failed - token expired');
      }
      headers['auth-token'] = '$token';
    }
    print(headers);

    try {
      final response = await http.put(
        Uri.parse('$baseUrl$endpoint'),
        headers: headers,
        body: jsonEncode(data),
      );

      // Handle 401 errors specifically
      if (response.statusCode == 401) {
        throw Exception('Authentication failed - please log in again');
      }

      return _processResponse(response);
    } catch (e) {
      print('API Error: $e');
      rethrow;
    }
  }

  Future<dynamic> delete(String endpoint) async {
    print(endpoint);

    // Get token from SharedRepository
    final token = locator<SharedRepository>().getToken();

    // Set headers with auth token
    Map<String, String> headers = {};
    if (token != null && token.isNotEmpty) {
      headers['auth-token'] = token;
    }

    final response = await http.delete(Uri.parse('$baseUrl$endpoint'),
        headers: headers.isNotEmpty ? headers : null);

    return _processResponse(response);
  }

  Future<dynamic> patch(String endpoint, Map<String, dynamic> data) async {
    print(endpoint);
    print(data);
    final response = await http.patch(Uri.parse('$baseUrl$endpoint'),
        headers: {'Content-Type': 'application/json'}, body: jsonEncode(data));
    return _processResponse(response);
  }

  dynamic _processResponse(http.Response response) {
    print('Response status code: ${response.statusCode}');

    if (response.statusCode >= 200 && response.statusCode < 300) {
      print('Response body: ${response.body}');
      return jsonDecode(response.body);
    } else if (response.statusCode == 401) {
      print('Authentication error: ${response.body}');
      // Clear the token since it's invalid
      locator<SharedRepository>().deleteToken();
      throw Exception('Authentication failed - please log in again');
    } else {
      print('API error: ${response.body}');
      throw Exception('${response.statusCode}: ${response.body}');
    }
  }
}
