import 'package:class_z/core/constants/string.dart';

class Endpoints {
  // Base URL - Using the device URL from AppText with /api prefix
  static String get baseUrl => '${AppText.device}/api';

  // Auth endpoints
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String refreshToken = '/auth/refresh-token';

  // User endpoints
  static const String users = '/users';
  static const String userProfile = '/users/profile';
  
  // Owner endpoints
  static const String owner = '/owners';
  
  // Center endpoints
  static const String centers = '/centers';
  
  // Program endpoints
  static const String programs = '/programs';
  
  // Booking endpoints
  static const String bookings = '/bookings';
  
  // Payment endpoints
  static const String payments = '/payments';
  
  // Transaction endpoints
  static const String transactions = '/transactions';
  
  // Notification endpoints
  static const String notifications = '/notifications';
  
  // Helper method to get full URL
  static String getFullUrl(String endpoint) {
    return baseUrl + endpoint;
  }
}
