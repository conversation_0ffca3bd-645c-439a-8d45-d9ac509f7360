import 'package:flutter/material.dart';
import 'package:class_z/features/roles/parent/presentation/screen/saved_centers_screen.dart';

class AppRoutes {
  static const String search = '/search';
  static const String savedCenters = '/saved-centers';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case savedCenters:
        return MaterialPageRoute(
          builder: (_) => const SavedCentersScreen(),
        );
      default:
        return MaterialPageRoute(
          builder: (_) => const Scaffold(
            body: Center(child: Text('Route not found')),
          ),
        );
    }
  }
}
