import 'package:class_z/core/imports.dart';

class ScheduleSelector extends StatefulWidget {
  final Map<String, Map<String, String>> schedule;
  final ValueChanged<Map<String, Map<String, String>>> onChanged;

  const ScheduleSelector({
    super.key,
    required this.schedule,
    required this.onChanged,
  });

  @override
  State<ScheduleSelector> createState() => _ScheduleSelectorState();
}

class _ScheduleSelectorState extends State<ScheduleSelector> {
  late Map<String, bool> _activeDays;

  static const List<String> days = [
    'Mon',
    'Tue',
    'Wed',
    'Thu',
    'Fri',
    'Sat',
    'Sun',
  ];

  static const List<String> fullDayNames = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  static const List<String> timeOptions = [
    '12:00 AM',
    '01:00 AM',
    '02:00 AM',
    '03:00 AM',
    '04:00 AM',
    '05:00 AM',
    '06:00 AM',
    '07:00 AM',
    '08:00 AM',
    '09:00 AM',
    '10:00 AM',
    '11:00 AM',
    '12:00 PM',
    '01:00 PM',
    '02:00 PM',
    '03:00 PM',
    '04:00 PM',
    '05:00 PM',
    '06:00 PM',
    '07:00 PM',
    '08:00 PM',
    '09:00 PM',
    '10:00 PM',
    '11:00 PM',
  ];

  @override
  void initState() {
    super.initState();

    // Initialize active days based on existing schedule data
    _activeDays = {};
    for (var i = 0; i < fullDayNames.length; i++) {
      String day = fullDayNames[i];
      // A day is active if it has both from and to times set
      bool hasFrom = widget.schedule[day]?['from']?.isNotEmpty ?? false;
      bool hasTo = widget.schedule[day]?['to']?.isNotEmpty ?? false;
      _activeDays[day] = hasFrom && hasTo;
    }
  }

  void _updateSchedule(String day, String type, String? value) {
    if (!_activeDays[day]!) return; // Don't update if day is inactive

    final updatedSchedule =
        Map<String, Map<String, String>>.from(widget.schedule);
    updatedSchedule[day] = Map<String, String>.from(updatedSchedule[day] ?? {});
    updatedSchedule[day]![type] = value ?? '';
    widget.onChanged(updatedSchedule);
  }

  void _toggleDay(String day, bool value) {
    setState(() {
      _activeDays[day] = value;

      final updatedSchedule =
          Map<String, Map<String, String>>.from(widget.schedule);
      updatedSchedule[day] =
          Map<String, String>.from(updatedSchedule[day] ?? {});

      if (value) {
        // If toggled on, set default times if they're not already set
        if (updatedSchedule[day]!['from']?.isEmpty ?? true) {
          updatedSchedule[day]!['from'] = '09:00 AM';
        }
        if (updatedSchedule[day]!['to']?.isEmpty ?? true) {
          updatedSchedule[day]!['to'] = '06:00 PM';
        }
      } else {
        // If toggled off, clear the times
        updatedSchedule[day]!['from'] = '';
        updatedSchedule[day]!['to'] = '';
      }

      widget.onChanged(updatedSchedule);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(fullDayNames.length,
          (index) => _buildDayRow(context, fullDayNames[index], days[index])),
    );
  }

  Widget _buildDayRow(
      BuildContext context, String fullDayName, String shortDayName) {
    final bool isActive = _activeDays[fullDayName] ?? false;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Day toggle section - fixed width with no overflow
          SizedBox(
            width: 75.w, // Reduced width
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Toggle switch with smaller scale
                Transform.scale(
                  scale: 0.8,
                  child: Switch(
                    value: isActive,
                    onChanged: (value) => _toggleDay(fullDayName, value),
                    activeColor: Colors.blue,
                  ),
                ),
                // Day name (shortened)
                Flexible(
                  child: Text(
                    shortDayName,
                    style: TextStyle(
                      fontSize: 14.sp, // Smaller font size
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis, // Prevent text overflow
                  ),
                ),
              ],
            ),
          ),

          // Time selectors or Day off text
          Expanded(
            child: isActive
                ? _buildTimeSelectors(context, fullDayName)
                : _buildDayOff(context),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSelectors(BuildContext context, String day) {
    return Row(
      children: [
        Expanded(
          child: _buildDropdown(
            context,
            day: day,
            type: 'from',
            hint: 'From',
            value: _getValidValue(widget.schedule[day]?['from'], '09:00 AM'),
          ),
        ),
        SizedBox(width: 8.w), // Reduced spacing
        Text(
          '-',
          style: TextStyle(
            fontSize: 14.sp, // Smaller font
          ),
        ),
        SizedBox(width: 8.w), // Reduced spacing
        Expanded(
          child: _buildDropdown(
            context,
            day: day,
            type: 'to',
            hint: 'To',
            value: _getValidValue(widget.schedule[day]?['to'], '06:00 PM'),
          ),
        ),
      ],
    );
  }

  Widget _buildDayOff(BuildContext context) {
    return Center(
      child: Text(
        'Day off',
        style: TextStyle(
          fontSize: 14.sp, // Smaller font size
          color: Colors.grey,
          fontStyle: FontStyle.italic,
        ),
      ),
    );
  }

  // Get a valid value or return a default
  String _getValidValue(String? value, String defaultValue) {
    if (value == null || value.isEmpty) return defaultValue;
    if (timeOptions.contains(value)) return value;
    return defaultValue;
  }

  Widget _buildDropdown(
    BuildContext context, {
    required String day,
    required String type,
    required String hint,
    required String value,
  }) {
    return Container(
      height: 36, // Smaller height
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          isExpanded: true,
          isDense: true, // Make dropdown more compact
          icon: Icon(Icons.keyboard_arrow_down, size: 18), // Smaller icon
          iconSize: 18, // Smaller icon size
          elevation: 8,
          style: TextStyle(
            color: Colors.black,
            fontSize: 13.sp, // Smaller font size
          ),
          onChanged: (newValue) {
            if (newValue != null) {
              _updateSchedule(day, type, newValue);
            }
          },
          items: timeOptions.map<DropdownMenuItem<String>>((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Padding(
                padding: const EdgeInsets.only(left: 8.0), // Less padding
                child: Text(
                  value,
                  overflow: TextOverflow.ellipsis, // Prevent text overflow
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
