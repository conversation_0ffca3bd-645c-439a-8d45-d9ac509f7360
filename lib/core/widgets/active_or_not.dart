  import 'package:class_z/core/imports.dart';


Widget activeOrNot({required BuildContext context, required bool active}) {
    return Row(
      children: [
        Container(
          height: 10.h,
          width: 10.w,
          decoration: BoxDecoration(
              color: active == true ? AppPallete.green : AppPallete.darkGrey,
              borderRadius: BorderRadius.circular(10.w)),
        ),
        SizedBox(
          width: 5.w,
        ),
        customtext(
            context: context,
            newYear: active == true ? "Active" : "InActive",
            color: AppPallete.darkGrey,
            font: 15.sp,
            weight: FontWeight.w300),
      ],
    );
  }