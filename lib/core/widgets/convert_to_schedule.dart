List<Map<String, String>> convertScheduleToJson(Map<String, Map<String, String>> schedule) {
  final List<Map<String, String>> result = schedule.entries
    .where((entry) {
      // Only include days that have both from and to times set (active days)
      final times = entry.value;
      final hasFrom = times["from"] != null && times["from"]!.isNotEmpty;
      final hasTo = times["to"] != null && times["to"]!.isNotEmpty;
      return hasFrom && hasTo;
    })
    .map((entry) {
      final day = entry.key;
      final times = entry.value;
      return {
        "day": day,
        "openingTime": times["from"] ?? "",
        "closingTime": times["to"] ?? "",
      };
    }).toList();
    
  // Return the result, which might be empty if all days are "Day off"
  return result;
}
