import 'package:class_z/core/imports.dart';

class SuccessDialog extends StatelessWidget {
  final Widget? title;
  final Widget subtitle;
  final VoidCallback onOkPressed;

  // Constructor to receive title, subtitle, and onOkPressed callback
  SuccessDialog({
    this.title,
    required this.subtitle,
    required this.onOkPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: title != null
          ? title
          : customtext(context: context, newYear: '', font: 15.sp),
      content: subtitle,
      actions: <Widget>[
        TextButton(
          onPressed: () {
            // Close the dialog and execute the onOkPressed callback
            Navigator.pop(context);
            onOkPressed(); // Calls the passed function
          },
          child: Text('OK'),
        ),
      ],
    );
  }
}
