import 'package:class_z/core/imports.dart';

class LanguageSelector extends StatelessWidget {
  final List<String> selectedLanguages; // Initial list of selected languages
  final ValueChanged<List<String>>
      onChanged; // Callback to notify parent of changes
  final List<String> availableLanguages = const [
    'Cantonese',
    'English',
    'Spanish',
    'French',
    'German',
    'Mandarin',
    'Japanese',
    'Korean',
  ];

  const LanguageSelector({
    super.key,
    required this.selectedLanguages,
    required this.onChanged,
  });

  // Add a language to the selected list
  void _addLanguage(String language) {
    if (!selectedLanguages.contains(language)) {
      final updatedLanguages = [...selectedLanguages, language];
      onChanged(updatedLanguages); // Notify parent
    }
  }

  // Remove a language from the selected list
  void _removeLanguage(String language) {
    final updatedLanguages = [...selectedLanguages]..remove(language);
    onChanged(updatedLanguages); // Notify parent
  }

  // Show the language list in a bottom sheet
  void _showLanguageList(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Select a Language',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: availableLanguages.length,
                  itemBuilder: (context, index) {
                    final language = availableLanguages[index];
                    final isSelected = selectedLanguages.contains(language);
                    return ListTile(
                      title: Text(language),
                      trailing: isSelected
                          ? const Icon(Icons.check, color: Colors.green)
                          : null,
                      onTap: () {
                        _addLanguage(language);
                        Navigator.pop(context); // Close the bottom sheet
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8.0,
      children: [
        ...selectedLanguages.map((language) => _buildLanguageChip(language)),
        _buildAddButton(context),
      ],
    );
  }

  // Build a language chip with a delete button
  Widget _buildLanguageChip(String language) {
    return Chip(
      label: Text(
        language,
        style: const TextStyle(
          color: Colors.black,
          fontSize: 14,
        ),
      ),
      backgroundColor: Colors.grey[200],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: const BorderSide(color: Colors.grey, width: 1),
      ),
      deleteIcon: const Icon(
        Icons.close,
        size: 16,
        color: Colors.black,
      ),
      onDeleted: () => _removeLanguage(language),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    );
  }

  // Build the "+" button
  Widget _buildAddButton(BuildContext context) {
    return ActionChip(
      label: const Icon(
        Icons.add,
        size: 16,
        color: Colors.white,
      ),
      backgroundColor: AppPallete.secondaryColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      onPressed: () => _showLanguageList(context),
      padding: const EdgeInsets.all(4),
    );
  }
}
