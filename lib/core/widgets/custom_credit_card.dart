import 'package:class_z/core/imports.dart';

class CustomCreditCard extends StatelessWidget {
  final String? brand;
  final String? last4;
  final String? cardholderName;
  final String? expiryDate;
  final bool showCvc;

  const CustomCreditCard({
    super.key,
    this.brand,
    this.last4,
    this.cardholderName,
    this.expiryDate,
    this.showCvc = false,
  });

  @override
  Widget build(BuildContext context) {
    String displayBrand = brand?.toLowerCase() ?? 'visa';
    String cardNumber =
        last4 != null ? "•••• •••• •••• $last4" : "•••• •••• •••• ••••";
    String cardName = cardholderName?.toUpperCase() ?? "CARDHOLDER NAME";
    String expiry = expiryDate ?? "MM/YY";

    bool isVisa = displayBrand == 'visa';

    return Center(
      child: Container(
        height: 200.h,
        width: 320.w,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [const Color(0xFF4C4C4C), const Color(0xFF1A1A1A)],
          ),
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Align(
              alignment: Alignment.topRight,
              child: customSvgPicture(
                imagePath: isVisa ? ImagePath.visaSvg : ImagePath.masterSvg,
                height: 40.h,
                width: 60.w,
              ),
            ),
            Text(
              cardNumber,
              style: TextStyle(
                color: Colors.white,
                fontSize: 20.sp,
                fontWeight: FontWeight.w500,
                letterSpacing: 2,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Cardholder Name",
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 10.sp,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        cardName,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Expires",
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 10.sp,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        expiry,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                if (showCvc)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "CVC",
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 10.sp,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          "***",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
