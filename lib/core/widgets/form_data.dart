import 'package:dio/dio.dart';
import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:mime/mime.dart' as mime;

Future<FormData> buildFormData(Map<String, dynamic> data) async {
  final FormData formData = FormData();
  int fileCounter = 0;

  // Determine MIME type based on file extension
  String getMimeType(String filePath) {
    final mimeType = mime.lookupMimeType(filePath);
    return mimeType ?? 'application/octet-stream';
  }

  // Special handling for mainImage field - the server expects this exact field name
  if (data.containsKey('mainImage') && data['mainImage'] is File) {
    final File file = data['mainImage'];
    if (file.existsSync()) {
      final fileSize = file.lengthSync();
      if (fileSize > 10 * 1024 * 1024) {
        print('Warning: mainImage file too large (${fileSize ~/ 1024 ~/ 1024}MB): ${file.path}');
      }
      
      String filename = path.basename(file.path);
      if (file.path.contains('/cache/')) {
        if (!path.extension(filename).isNotEmpty) {
          filename = '$filename.jpg';
        }
      }
      
      final contentType = getMimeType(file.path);
      print('Adding mainImage file: path=${file.path}, size=${fileSize ~/ 1024}KB, contentType=$contentType');
      
      try {
        // Use exactly 'mainImage' as the field name, which is what the server expects
        formData.files.add(MapEntry(
          'mainImage',
          MultipartFile.fromFileSync(
            file.path,
            filename: filename,
            contentType: DioMediaType.parse(contentType),
          ),
        ));
        print('Successfully added mainImage file');
        
        // Remove mainImage from data to prevent double processing
        data.remove('mainImage');
      } catch (e) {
        print('Error adding mainImage file: $e');
      }
    } else {
      print('Warning: mainImage file does not exist: ${file.path}');
    }
  }

  void addFile(String key, dynamic value) {
    if (value is File) {
      // Check if file exists
      if (!value.existsSync()) {
        print('Warning: File does not exist: ${value.path}');
        return;
      }
      
      final fileSize = value.lengthSync();
      // Check file size - 10MB limit
      if (fileSize > 10 * 1024 * 1024) {
        print('Warning: File too large (${fileSize ~/ 1024 ~/ 1024}MB): ${value.path}');
      }
      
      // Get proper filename, handling cache paths specially
      String filename = path.basename(value.path);
      if (value.path.contains('/cache/')) {
        // For cache files, ensure we have a proper extension
        if (!path.extension(filename).isNotEmpty) {
          filename = '$filename.jpg'; // Default to jpg for cache files without extension
        }
      }
      
      final contentType = getMimeType(value.path);
      
      print('Adding file: $key, path: ${value.path}, filename: $filename, contentType: $contentType, size: ${fileSize ~/ 1024}KB');
      
      try {
        formData.files.add(MapEntry(
          key,
          MultipartFile.fromFileSync(
            value.path,
            filename: filename,
            contentType: DioMediaType.parse(contentType),
          ),
        ));
        print('Successfully added file: $key');
        fileCounter++;
      } catch (e) {
        print('Error adding file $key: $e');
      }
    } else if (value is List) {
      // For lists of files, add each with the same field name (no indices)
      for (var item in value) {
        if (item is File) {
          if (!item.existsSync()) {
            print('Warning: File does not exist: ${item.path}');
            continue;
          }
          
          final fileSize = item.lengthSync();
          if (fileSize > 10 * 1024 * 1024) {
            print('Warning: File too large (${fileSize ~/ 1024 ~/ 1024}MB): ${item.path}');
            continue;
          }
          
          String filename = path.basename(item.path);
          if (item.path.contains('/cache/')) {
            if (!path.extension(filename).isNotEmpty) {
              filename = '$filename.jpg';
            }
          }
          
          final contentType = getMimeType(item.path);
          
          try {
            formData.files.add(MapEntry(
              key,
              MultipartFile.fromFileSync(
                item.path,
                filename: filename,
                contentType: DioMediaType.parse(contentType),
              ),
            ));
            print('Successfully added file from list: $key');
            fileCounter++;
          } catch (e) {
            print('Error adding file from list $key: $e');
          }
        }
      }
    }
  }

  try {
    data.forEach((key, value) {
      if (value is File || (value is List && value.isNotEmpty && value.first is File)) {
        addFile(key, value);
      } else if (value is List) {
        value.asMap().forEach((index, item) {
          if (item is Map) {
            item.forEach((subKey, subValue) {
              if (subValue != null) {
                formData.fields.add(MapEntry('$key[$index][$subKey]', subValue.toString()));
              }
            });
          } else if (item != null) {
            formData.fields.add(MapEntry('$key[$index]', item.toString()));
          }
        });
      } else if (value is Map) {
        value.forEach((subKey, subValue) {
          if (subValue != null) {
            formData.fields.add(MapEntry('$key[$subKey]', subValue.toString()));
          }
        });
      } else if (value != null) {
        formData.fields.add(MapEntry(key, value.toString()));
      }
    });
  } catch (e) {
    print('Error building form data: $e');
  }

  print('FormData fields count: ${formData.fields.length}');
  print('FormData files count: ${formData.files.length}');
  return formData;
}