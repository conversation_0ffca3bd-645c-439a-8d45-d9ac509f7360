import 'package:class_z/core/config/app_config.dart';

/// Service to handle currency conversions between HKD and Zcoin
class CurrencyService {
  /// Private constructor to prevent instantiation
  CurrencyService._();

  /// Singleton instance
  static final CurrencyService _instance = CurrencyService._();
  static CurrencyService get instance => _instance;

  /// Convert HKD amount to Zcoin for centre pricing
  /// Uses the rule: 25 HKD = 1 Zcoin, rounding up from 3 HKD
  /// Examples:
  /// - 1-25 HKD = 1 Zcoin
  /// - 26-50 HKD = 2 Zcoin
  /// - 23 HKD = 1 Zcoin (no rounding needed)
  /// - 28 HKD = 2 Zcoin (rounds up because 28-25=3, which is >= 3)
  /// - 27 HKD = 1 Zcoin (rounds down because 27-25=2, which is < 3)
  int convertHKDToZcoin(double hkdAmount) {
    return AppConfig.convertHKDToZcoin(hkdAmount);
  }

  /// Convert Zcoin amount back to HKD equivalent
  double convertZcoinToHKD(int zcoinAmount) {
    return AppConfig.convertZcoinToHKD(zcoinAmount);
  }

  /// Format HKD amount for display in centre forms
  String formatHKDForDisplay(double amount) {
    return AppConfig.formatHKDAmount(amount);
  }

  /// Format Zcoin amount for display
  String formatZcoinForDisplay(int amount) {
    return AppConfig.formatZcoinAmount(amount);
  }

  /// Calculate Zcoin equivalent preview for centre pricing input
  /// Returns a formatted string showing the conversion
  String getZcoinPreview(String hkdInput) {
    if (hkdInput.isEmpty) return "";

    try {
      final double hkdAmount = double.parse(hkdInput);
      final int zcoinAmount = convertHKDToZcoin(hkdAmount);
      return "≈ $zcoinAmount Zcoin${zcoinAmount == 1 ? '' : 's'}";
    } catch (e) {
      return "";
    }
  }

  /// Validate HKD input for centre pricing
  String? validateHKDInput(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter the amount in HKD';
    }

    try {
      final double amount = double.parse(value);
      if (amount <= 0) {
        return 'Amount must be greater than 0';
      }
      if (amount > 10000) {
        return 'Amount cannot exceed HKD 10,000 per class';
      }
      return null;
    } catch (e) {
      return 'Please enter a valid number';
    }
  }
}
