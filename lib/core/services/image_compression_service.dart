import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class ImageCompressionService {
  // Configuration constants
  static const int MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  static const int TARGET_FILE_SIZE = 2 * 1024 * 1024; // 2MB target
  static const int DEFAULT_QUALITY = 85;
  static const int COMPRESSED_QUALITY = 70;
  static const int HEAVY_COMPRESSED_QUALITY = 50;
  static const int MAX_WIDTH = 1920;
  static const int MAX_HEIGHT = 1920;
  static const int MEDIUM_WIDTH = 1280;
  static const int MEDIUM_HEIGHT = 1280;
  static const int SMALL_WIDTH = 800;
  static const int SMALL_HEIGHT = 800;

  /// Compress an image file with automatic quality and size adjustment
  static Future<File?> compressImage(
    File originalFile, {
    int? targetSizeBytes,
    int? maxWidth,
    int? maxHeight,
    int? quality,
    bool showProgress = false,
  }) async {
    try {
      if (kDebugMode) {
        final originalSize = await originalFile.length();
        print('🖼️ Starting image compression...');
        print('📁 Original file: ${path.basename(originalFile.path)}');
        print('📏 Original size: ${(originalSize / 1024).toStringAsFixed(1)}KB');
      }

      // Check if file exists
      if (!await originalFile.exists()) {
        if (kDebugMode) print('❌ Original file does not exist');
        return null;
      }

      // Check if it's a valid image file
      if (!_isValidImageFile(originalFile.path)) {
        if (kDebugMode) print('❌ Invalid image file format');
        return null;
      }

      // Get original file size
      final originalSize = await originalFile.length();
      final targetSize = targetSizeBytes ?? TARGET_FILE_SIZE;

      // If file is already small enough, return original
      if (originalSize <= targetSize && targetSizeBytes == null) {
        if (kDebugMode) print('✅ File already optimal size, no compression needed');
        return originalFile;
      }

      // Create temp directory for compressed image
      final tempDir = await getTemporaryDirectory();
      final fileExt = path.extension(originalFile.path).toLowerCase();
      final compressedPath = path.join(
        tempDir.path,
        'compressed_${DateTime.now().millisecondsSinceEpoch}$fileExt',
      );

      // Determine compression parameters based on original size
      final compressionParams = _getCompressionParams(
        originalSize,
        targetSize,
        quality,
        maxWidth,
        maxHeight,
      );

      if (kDebugMode) {
        print('🔧 Compression params:');
        print('   Quality: ${compressionParams.quality}');
        print('   Max dimensions: ${compressionParams.maxWidth}x${compressionParams.maxHeight}');
      }

      // Perform compression
      final compressedFile = await FlutterImageCompress.compressAndGetFile(
        originalFile.path,
        compressedPath,
        quality: compressionParams.quality,
        minWidth: compressionParams.maxWidth,
        minHeight: compressionParams.maxHeight,
        format: _getCompressFormat(fileExt),
      );

      if (compressedFile == null) {
        if (kDebugMode) print('❌ Compression failed');
        return null;
      }

      final compressedSize = await File(compressedFile.path).length();
      
      if (kDebugMode) {
        print('✅ Compression completed!');
        print('📏 Compressed size: ${(compressedSize / 1024).toStringAsFixed(1)}KB');
        print('📉 Size reduction: ${((originalSize - compressedSize) / originalSize * 100).toStringAsFixed(1)}%');
      }

      // If still too large, try more aggressive compression
      if (compressedSize > targetSize && targetSizeBytes != null) {
        if (kDebugMode) print('🔄 File still too large, applying aggressive compression...');
        return await _aggressiveCompress(
          File(compressedFile.path),
          targetSize,
          compressedPath,
        );
      }

      return File(compressedFile.path);
    } catch (e) {
      if (kDebugMode) print('❌ Error compressing image: $e');
      return null;
    }
  }

  /// Compress multiple images
  static Future<List<File>> compressImages(
    List<File> originalFiles, {
    int? targetSizeBytes,
    int? maxWidth,
    int? maxHeight,
    int? quality,
    bool showProgress = false,
  }) async {
    final compressedFiles = <File>[];
    
    for (int i = 0; i < originalFiles.length; i++) {
      if (kDebugMode && showProgress) {
        print('🔄 Compressing image ${i + 1}/${originalFiles.length}');
      }
      
      final compressed = await compressImage(
        originalFiles[i],
        targetSizeBytes: targetSizeBytes,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
        quality: quality,
      );
      
      if (compressed != null) {
        compressedFiles.add(compressed);
      } else {
        // If compression fails, use original file
        compressedFiles.add(originalFiles[i]);
      }
    }
    
    return compressedFiles;
  }

  /// Quick compress for profile images (smaller size)
  static Future<File?> compressProfileImage(File originalFile) {
    return compressImage(
      originalFile,
      maxWidth: SMALL_WIDTH,
      maxHeight: SMALL_HEIGHT,
      quality: DEFAULT_QUALITY,
    );
  }

  /// Compress for class/center main images (medium size)
  static Future<File?> compressMainImage(File originalFile) {
    return compressImage(
      originalFile,
      maxWidth: MEDIUM_WIDTH,
      maxHeight: MEDIUM_HEIGHT,
      quality: DEFAULT_QUALITY,
    );
  }

  /// Compress for gallery images (larger size)
  static Future<File?> compressGalleryImage(File originalFile) {
    return compressImage(
      originalFile,
      maxWidth: MAX_WIDTH,
      maxHeight: MAX_HEIGHT,
      quality: DEFAULT_QUALITY,
    );
  }

  // Private helper methods
  static bool _isValidImageFile(String filePath) {
    final validExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
    final extension = path.extension(filePath).toLowerCase();
    return validExtensions.contains(extension);
  }

  static _CompressionParams _getCompressionParams(
    int originalSize,
    int targetSize,
    int? quality,
    int? maxWidth,
    int? maxHeight,
  ) {
    // Calculate compression ratio needed
    final ratio = targetSize / originalSize;
    
    int finalQuality = quality ?? DEFAULT_QUALITY;
    int finalMaxWidth = maxWidth ?? MAX_WIDTH;
    int finalMaxHeight = maxHeight ?? MAX_HEIGHT;

    if (ratio < 0.3) {
      // Need heavy compression
      finalQuality = HEAVY_COMPRESSED_QUALITY;
      finalMaxWidth = SMALL_WIDTH;
      finalMaxHeight = SMALL_HEIGHT;
    } else if (ratio < 0.6) {
      // Need medium compression
      finalQuality = COMPRESSED_QUALITY;
      finalMaxWidth = MEDIUM_WIDTH;
      finalMaxHeight = MEDIUM_HEIGHT;
    }

    return _CompressionParams(
      quality: finalQuality,
      maxWidth: finalMaxWidth,
      maxHeight: finalMaxHeight,
    );
  }

  static CompressFormat _getCompressFormat(String fileExtension) {
    switch (fileExtension.toLowerCase()) {
      case '.png':
        return CompressFormat.png;
      case '.webp':
        return CompressFormat.webp;
      default:
        return CompressFormat.jpeg;
    }
  }

  static Future<File?> _aggressiveCompress(
    File file,
    int targetSize,
    String outputPath,
  ) async {
    try {
      // Try with very low quality and small dimensions
      final result = await FlutterImageCompress.compressAndGetFile(
        file.path,
        outputPath.replaceAll('.', '_aggressive.'),
        quality: HEAVY_COMPRESSED_QUALITY,
        minWidth: SMALL_WIDTH ~/ 2,
        minHeight: SMALL_HEIGHT ~/ 2,
      );

      if (result != null) {
        final size = await File(result.path).length();
        if (kDebugMode) {
          print('🔥 Aggressive compression result: ${(size / 1024).toStringAsFixed(1)}KB');
        }
        return File(result.path);
      }
      return null;
    } catch (e) {
      if (kDebugMode) print('❌ Aggressive compression failed: $e');
      return null;
    }
  }

  /// Get estimated compressed size (for UI feedback)
  static Future<int?> getEstimatedCompressedSize(File file) async {
    try {
      final originalSize = await file.length();
      // Rough estimation based on typical compression ratios
      return (originalSize * 0.3).round(); // Assume 70% reduction
    } catch (e) {
      return null;
    }
  }

  /// Check if file needs compression
  static Future<bool> needsCompression(File file, {int? maxSize}) async {
    try {
      final size = await file.length();
      final limit = maxSize ?? TARGET_FILE_SIZE;
      return size > limit;
    } catch (e) {
      return false;
    }
  }
}

class _CompressionParams {
  final int quality;
  final int maxWidth;
  final int maxHeight;

  _CompressionParams({
    required this.quality,
    required this.maxWidth,
    required this.maxHeight,
  });
}
