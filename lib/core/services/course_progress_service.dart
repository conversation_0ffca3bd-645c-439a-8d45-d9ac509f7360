import 'package:class_z/core/imports.dart';
import 'package:class_z/core/common/data/models/reviewable_item_model.dart';
// Added import for DateFormat

class CourseProgressService {
  static const String _firstLessonReviewPrefixKey = 'first_lesson_review_';
  static const String _completionReviewPrefixKey = 'completion_review_';

  // Check if a review should be shown for the current class session
  Future<bool> shouldShowReview({
    required BuildContext context,
    required ClassModel classModel,
    required PendingModel pending,
    required int currentSessionIndex,
    required int totalSessions,
  }) async {
    // Get the logged in user ID
    final parentData = locator<SharedRepository>().getParentData();
    final userId = parentData?.id ?? "";
    if (userId.isEmpty) return false;

    // Get the class ID
    final classId = classModel.id ??
        ""; // Assuming ClassModel has an 'id' getter that maps from '_id'
    if (classId.isEmpty) return false;

    final prefs = await SharedPreferences.getInstance();

    // Check if the first lesson review was already shown
    String firstLessonKey = "$_firstLessonReviewPrefixKey${userId}_$classId";
    bool firstLessonReviewShown = prefs.getBool(firstLessonKey) == true;

    // Check if we're at the end of all lessons
    bool isLastLesson = currentSessionIndex == totalSessions - 1;

    // Check if first lesson has been completed
    // Note: currentSessionIndex is 0-based, so index 1 means we're after the first lesson
    bool isAfterFirstLesson = currentSessionIndex > 0;

    // If we're after the first lesson and review hasn't been shown yet
    if (isAfterFirstLesson && !firstLessonReviewShown) {
      // Check if there's a performance review from the coach for this class
      bool hasCoachReview = _hasCoachPerformanceReview(pending);

      if (hasCoachReview) {
        // Mark first lesson review as shown
        await prefs.setBool(firstLessonKey, true);
        return true;
      }
    }

    // If we're at the last lesson and first lesson review wasn't shown
    if (isLastLesson && !firstLessonReviewShown) {
      // Mark completion review as shown
      String completionKey = "$_completionReviewPrefixKey${userId}_$classId";
      await prefs.setBool(completionKey, true);
      return true;
    }

    return false;
  }

  // Helper method to check if coach has given a performance review
  bool _hasCoachPerformanceReview(PendingModel pending) {
    // Check if there are any reviews associated with this class
    // This is a more specific check than just checking if event exists
    if (pending.event == null) {
      print(
          "[CourseProgressService] _hasCoachPerformanceReview: pending.event is null, returning false.");
      return false;
    }
    // For now, returning true if event exists. This might need refinement later
    // if a more direct way to check for coach reviews specific to this event is available.
    print(
        "[CourseProgressService] _hasCoachPerformanceReview: pending.event exists, returning true.");
    return true;
  }

  // Show the appropriate review popup
  void showReviewPopup({
    required BuildContext context,
    required ClassModel classModel,
    required EventModel? sessionEvent,
    required bool
        isHalfCourse, // true for first lesson review, false for course completion review
    DateTime? sessionDate, // Added optional sessionDate
  }) {
    final parentData = locator<SharedRepository>().getParentData();
    final reviewerId = parentData?.id ?? "";
    final classId = classModel.id ?? "";

    String? centerId;
    if (classModel.center is CenterData) {
      centerId = (classModel.center as CenterData).id;
    } else if (classModel.center is Map) {
      centerId = classModel.center["_id"];
    } else if (classModel.center is String) {
      centerId = classModel.center;
    }
    // Ensure centerId is not empty string if it was null or became empty
    if (centerId != null && centerId.isEmpty) centerId = null;

    final String? coachId = sessionEvent?.coach?.id;

    final bool hasValidCenter = centerId != null && centerId.isNotEmpty;
    final bool hasValidCoach = coachId != null && coachId.isNotEmpty;

    if (hasValidCenter && hasValidCoach) {
      showCombinedCourseReviewBottomSheet(
        context: context,
        classModel: classModel, // Contains center info
        // sessionEvent: sessionEvent, // Pass sessionEvent if needed by combined sheet for coach
        isHalfCourse: isHalfCourse,
        reviewerId: reviewerId,
        classId: classId,
        sessionDate: sessionDate,
        // Assuming showCombinedCourseReviewBottomSheet internally uses classModel.center and potentially a way to get coachId if needed.
        // If it needs explicit coachId/centerId, they should be passed.
        // For now, aligning with its likely existing structure.
        // The crucial part is that `hasValidCoach` is now correctly determined by `sessionEvent.coach`.
      );
    } else if (hasValidCenter) {
      showCourseReviewBottomSheet(
        context: context,
        classModel: classModel,
        isHalfCourse: isHalfCourse,
        revieweeType: "Center",
        revieweeId: centerId,
        reviewerId: reviewerId,
        classId: classId,
        sessionDate: sessionDate,
      );
    } else if (hasValidCoach) {
      showCourseReviewBottomSheet(
        context: context,
        classModel: classModel, // Passed for context
        isHalfCourse: isHalfCourse,
        revieweeType: "Coach",
        revieweeId: coachId,
        reviewerId: reviewerId,
        classId: classId,
        sessionDate: sessionDate,
      );
    } else {
      print(
          "[CourseProgressService] showReviewPopup: Neither valid center nor coach ID found to show a review popup.");
      // Consider showing a SnackBar or some user feedback here if appropriate
      // ScaffoldMessenger.of(context).showSnackBar(
      //   const SnackBar(content: Text("Review details are currently unavailable.")),
      // );
    }
  }

  Future<List<ReviewableItem>> getReviewableItemsForParent({
    required List<PendingModel> pendingModels,
    required String userId,
    // required BuildContext context, // Not needed if we only return data
  }) async {
    print(
        "[CourseProgressService] getReviewableItemsForParent called with ${pendingModels.length} pendingModels for userId: $userId");
    final List<ReviewableItem> reviewableItems = [];
    final prefs = await SharedPreferences.getInstance();
    final DateFormat classDateFormat =
        DateFormat('dd/MM/yy'); // For parsing '23/04/25'

    for (int idx = 0; idx < pendingModels.length; idx++) {
      final pendingModel = pendingModels[idx];
      print(
          "[CourseProgressService] Processing pendingModel ${idx + 1}/${pendingModels.length}: ${pendingModel.toJson()}");

      if (pendingModel.plainClassDetails == null ||
          pendingModel.event == null) {
        print(
            "[CourseProgressService] Skipping pendingModel ${idx + 1} due to missing plainClassDetails or event.");
        continue;
      }

      final classDetails = pendingModel.plainClassDetails!;
      final sessionEvent = pendingModel.event!;

      final classId = classDetails.id ?? "";
      print(
          "[CourseProgressService] PendingModel ${idx + 1}: classId from classDetails.id = '$classId'");

      if (classId.isEmpty) {
        print(
            "[CourseProgressService] Skipping pendingModel ${idx + 1} due to empty classId (from classDetails.id).");
        continue;
      }

      final totalSessions = classDetails.dates?.length ?? 0;
      print(
          "[CourseProgressService] PendingModel ${idx + 1}: classId '$classId', totalSessions = $totalSessions");

      int currentSessionIndex = -1;
      if (classDetails.dates != null && classDetails.dates!.isNotEmpty) {
        print(
            "[CourseProgressService] PendingModel ${idx + 1}: classId '$classId', sessionEvent.id = ${sessionEvent.id}, sessionEvent.dateId = ${sessionEvent.dateId}, sessionEvent.date = ${sessionEvent.date}");
        for (int i = 0; i < classDetails.dates!.length; i++) {
          final classSessionDateEntry = classDetails.dates![i];
          print(
              "[CourseProgressService]  - Checking class session date entry $i: id=${classSessionDateEntry.id}, date=${classSessionDateEntry.date}");

          if (classSessionDateEntry.id != null &&
              classSessionDateEntry.id == sessionEvent.dateId) {
            currentSessionIndex = i;
            print(
                "[CourseProgressService]  - Match found by sessionEvent.dateId ('${sessionEvent.dateId}') with classDetails.dates[$i].id. currentSessionIndex = $currentSessionIndex");
            break;
          }

          // Fallback: Compare by date if dateId is not matching or not present
          if (sessionEvent.date != null && classSessionDateEntry.date != null) {
            print(
                "[CourseProgressService]  - Attempting date string comparison: sessionEvent.date=${sessionEvent.date}, classDetails.dates[$i].date='${classSessionDateEntry.date}'");
            try {
              // First, ensure we have the session event date as a DateTime
              DateTime actualSessionEventDate;
              if (sessionEvent.date is String) {
                actualSessionEventDate =
                    DateTime.parse(sessionEvent.date as String);
              } else if (sessionEvent.date is DateTime) {
                actualSessionEventDate = sessionEvent.date as DateTime;
              } else {
                print(
                    "[CourseProgressService]  - sessionEvent.date is not a String or DateTime. Skipping date comparison.");
                continue; // Skip this date comparison if sessionEvent.date is not a parsable type
              }

              // Now try to parse the class date which is in dd/MM/yy format
              DateTime? parsedClassDate;
              try {
                // Try parsing 'dd/MM/yy' format (e.g., "23/04/25")
                List<String> parts = classSessionDateEntry.date!.split('/');
                if (parts.length == 3) {
                  int day = int.parse(parts[0]);
                  int month = int.parse(parts[1]);
                  int year = int.parse(parts[2]);

                  // Handle 2-digit year (assuming 20xx for simplicity)
                  if (year < 100) {
                    year += 2000;
                  }

                  parsedClassDate = DateTime(year, month, day);
                  print(
                      "[CourseProgressService]  - Successfully parsed date: $parsedClassDate");
                }
              } catch (e) {
                print(
                    "[CourseProgressService]  - Failed to parse classDetails.dates[$i].date ('${classSessionDateEntry.date}') with dd/MM/yy: $e");

                // Try parsing as ISO if the first attempt fails
                try {
                  parsedClassDate = DateTime.parse(classSessionDateEntry.date!);
                } catch (isoError) {
                  print(
                      "[CourseProgressService]  - Failed to parse classDetails.dates[$i].date ('${classSessionDateEntry.date}') as ISO8601: $isoError");
                }
              }

              // Compare the dates (only day, month, year - ignore time)
              if (parsedClassDate != null) {
                print(
                    "[CourseProgressService]  - Comparing dates: parsedClassDate=$parsedClassDate, actualSessionEventDate=$actualSessionEventDate");
                if (parsedClassDate.year == actualSessionEventDate.year &&
                    parsedClassDate.month == actualSessionEventDate.month &&
                    parsedClassDate.day == actualSessionEventDate.day) {
                  currentSessionIndex = i;
                  print(
                      "[CourseProgressService]  - Match found by date comparison. currentSessionIndex = $currentSessionIndex");
                  break;
                }
              }
            } catch (e) {
              print(
                  "[CourseProgressService]  - Error during date parsing/comparison for classDetails.dates[$i].date ('${classSessionDateEntry.date}'): $e");
            }
          }
        }
      }

      if (currentSessionIndex == -1) {
        print(
            "[CourseProgressService] Skipping pendingModel ${idx + 1} (class $classId, event ${sessionEvent.id}): Could not determine session index.");
        continue;
      }
      print(
          "[CourseProgressService] PendingModel ${idx + 1}: classId '$classId', determined currentSessionIndex = $currentSessionIndex");

      // Check shared preferences for review status
      String firstLessonKey = "$_firstLessonReviewPrefixKey${userId}_$classId";
      bool firstLessonReviewShown = prefs.getBool(firstLessonKey) ?? false;

      String completionKey = "$_completionReviewPrefixKey${userId}_$classId";
      bool completionReviewShown = prefs.getBool(completionKey) ?? false;

      print(
          "[CourseProgressService] PendingModel ${idx + 1}: classId '$classId', firstLessonReviewShown = $firstLessonReviewShown, completionReviewShown = $completionReviewShown");

      bool isAfterFirstLesson = currentSessionIndex > 0;
      bool isLastLesson =
          totalSessions > 0 && currentSessionIndex == totalSessions - 1;
      print(
          "[CourseProgressService] PendingModel ${idx + 1}: classId '$classId', isAfterFirstLesson = $isAfterFirstLesson, isLastLesson = $isLastLesson");

      // Logic for first lesson review
      bool coachHasReviewed = _hasCoachPerformanceReview(pendingModel);
      print(
          "[CourseProgressService] PendingModel ${idx + 1}: classId '$classId', coachHasReviewed = $coachHasReviewed");

      if (isAfterFirstLesson && !firstLessonReviewShown && coachHasReviewed) {
        print(
            "[CourseProgressService] Adding 'First Lesson Review' for pendingModel ${idx + 1} (class $classId)");
        reviewableItems.add(ReviewableItem(
          classModel: classDetails,
          sessionEvent: sessionEvent,
          currentSessionIndex: currentSessionIndex,
          totalSessions: totalSessions,
          isFirstLessonReview: true,
          reviewType: "First Lesson Review",
          childId: pendingModel.studentId?.id,
        ));
      }

      // Logic for completion review
      if (isLastLesson && !completionReviewShown) {
        bool alreadyAddedAsFirstLesson = reviewableItems.any((item) =>
            (item.classModel.id ?? "") == classId &&
            item.isFirstLessonReview == true &&
            item.sessionEvent.id == sessionEvent.id);

        print(
            "[CourseProgressService] PendingModel ${idx + 1} (class $classId): isLastLesson=true, completionReviewShown=false, alreadyAddedAsFirstLessonForThisEvent=$alreadyAddedAsFirstLesson");

        if (!alreadyAddedAsFirstLesson) {
          print(
              "[CourseProgressService] Adding 'Course Completion Review' for pendingModel ${idx + 1} (class $classId)");
          reviewableItems.add(ReviewableItem(
            classModel: classDetails,
            sessionEvent: sessionEvent,
            currentSessionIndex: currentSessionIndex,
            totalSessions: totalSessions,
            isFirstLessonReview: false, // This is a completion review
            reviewType: "Course Completion Review",
            childId: pendingModel.studentId?.id,
          ));
        } else {
          print(
              "[CourseProgressService] Skipping 'Course Completion Review' for pendingModel ${idx + 1} (class $classId) as it was already added as a first lesson review for this specific event.");
        }
      }
    }
    print(
        "[CourseProgressService] getReviewableItemsForParent finished. Returning ${reviewableItems.length} reviewable items.");
    return reviewableItems;
  }
}
