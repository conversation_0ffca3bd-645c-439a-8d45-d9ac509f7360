import 'package:class_z/core/imports.dart';
import 'package:class_z/core/utils/print_long_string.dart';
import 'package:dio/dio.dart' as dio;
import 'package:http/http.dart' as http;
//import 'package:path/path.dart' as path;

abstract class ReviewDataSources {
  Future<bool> postReview({required Map<String, dynamic> payload});
  Future<bool> postReviewByParent({required Map<String, dynamic> payload});
  Future<List<ReviewModel>> getReviewById(
      {required String id, required String type});
  Future<ReviewModel> getReviewByIdandClass(
      {required String id, required String type, required String classId});
  Future<List<ReviewModel>> getMoments({required String childId});
}

class ReviewDataSourcesImpl extends ReviewDataSources {
  final http.Client client;
  final dio.Dio dioClient;
  final SharedRepository sharedRepository;
  final String device = AppText.device;
  final ApiService apiService;
  ReviewDataSourcesImpl(
      {required this.client,
      required this.dioClient,
      required this.sharedRepository,
      required this.apiService});

  @override
  Future<bool> postReview({required Map<String, dynamic> payload}) async {
    try {
      final formData = dio.FormData();
      payload.forEach((key, value) {
        if (key != 'images' && key != 'questions') {
          formData.fields.add(MapEntry(key, value.toString()));
        }
        // Assume 'questions' is a Map<String, dynamic>
        if (key == 'questions' && value is Map<String, dynamic>) {
          value.forEach((qKey, qValue) {
            formData.fields.add(
              MapEntry('questions[$qKey]', qValue.toString()),
            );
          });
        }
      });
      if (payload['images'] is List<File>) {
        List<File> files = payload['images'];
        for (var file in files) {
          if (isImageFile(file.path)) {
            print("Adding images file: ${file.path}");
            formData.files.add(
              MapEntry(
                'images',
                createImageMultipartFile(file),
              ),
            );
          } else {
            print(
                "WARNING: Skipping images file as it's not a valid image: ${file.path}");
          }
        }
      }
      String uri = "$device/api/review/";
      String token = sharedRepository.getToken() ?? '';
      print('formData: ${formData.fields}');
      var response = await dioClient.post(
        uri,
        data: formData,
        options: dio.Options(
          headers: {
            'auth-token': token,
            // Let Dio set the Content-Type header automatically for multipart/form-data
          },
          receiveTimeout: const Duration(minutes: 2),
          sendTimeout: const Duration(minutes: 2),
        ),
        onSendProgress: (sent, total) {
          if (total > 0) {
            print(
                'Upload progress: ${(sent / total * 100).toStringAsFixed(0)}%');
          }
        },
      );

      // Check response
      if (response.statusCode! >= 200 && response.statusCode! <= 300) {
        print('Branch center created successfully: ${response.data}');
        return true; // Success
      } else {
        print(
            'Failed to create branch center. Status Code: ${response.statusCode}, Response: ${response.data}');
        return false; // Failure
      }
    } catch (e) {
      throw Exception("Error giving review: $e");
    }
  }

  @override
  Future<bool> postReviewByParent(
      {required Map<String, dynamic> payload}) async {
    try {
      final response = await apiService.post("/api/review/parent", payload);

      return response;
    } catch (e) {
      rethrow; // Just rethrow the same error
    }
  }

  @override
  Future<List<ReviewModel>> getReviewById(
      {required String id, required String type}) async {
    print('is it here');
    try {
      String uri = "$device/api/review/$id/$type";
      print(uri);
      var response = await http.get(Uri.parse(uri));
      print("Response status code: ${response.statusCode}");
      print("Response body for getReviewById: ${response.body}");

      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Decode the response body as a Map
        Map<String, dynamic> jsonDataMap = jsonDecode(response.body);

        // Access the list of reviews from the 'reviews' key
        List<dynamic> reviewsList =
            jsonDataMap['reviews'] as List<dynamic>? ?? [];

        List<ReviewModel> reviews = reviewsList
            .map((e) => ReviewModel.fromJson(e as Map<String, dynamic>))
            .toList();
        return reviews;
      } else {
        print(
            "Error in getReviewById: Status code ${response.statusCode}, Body: ${response.body}");
        throw Exception("Something is wrong. Please try again");
      }
    } catch (e) {
      print(
          "Exception in getReviewById: $e, Response Body: Might have been printed above if request succeeded.");
      throw Exception("Error fetching review: $e");
    }
  }

  @override
  Future<ReviewModel> getReviewByIdandClass(
      {required String id,
      required String type,
      required String classId}) async {
    try {
      String uri = "$device/api/review/$classId/$id/$type";
      print(uri);
      var response = await http.get(Uri.parse(uri));
      print("response");
      if (response.statusCode >= 200 && response.statusCode < 300) {
        print(response.statusCode);
        final jsonData = jsonDecode(response.body);
        printLongString(jsonData.toString());
        ReviewModel review = ReviewModel.fromJson(jsonData);
        print(review);
        return review;
      } else
        throw Exception("Something is wrong. Please try again");
    } catch (e) {
      throw Exception("Error fetching review: $e");
    }
  }

  @override
  Future<List<ReviewModel>> getMoments({required String childId}) async {
    try {
      print(childId);
      String uri = "$device/api/review/moments?reviewId=$childId";

      var response = await http.get(Uri.parse(uri));
      print(response.statusCode);
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        List<dynamic> jsonData = jsonDecode(response.body);
        List<ReviewModel> moments = jsonData
            .map((e) => ReviewModel.fromJson(e as Map<String, dynamic>))
            .toList();
        return moments;
      } else
        throw Exception("Something is wrong. Please try again");
    } catch (e) {
      throw Exception("Error fetching review: $e");
    }
  }
}
