import 'package:class_z/core/imports.dart';

abstract class RequestDataSources {
  Future<bool> sendJoinRequest(String coachId, String centerId);
  Future<bool> updateStatus(String requestId, String status);
  Future<List<RequestModel>?> getRequestByCenter(String centerId);
  Future<List<RequestModel>?> getRequestByCoach(String coachId);
  Future<bool> cancelJoinRequest(String coachId, String centerId);
  Future<Map<String, dynamic>> checkExistingRequest(
      String coachId, String centerId);
}

class RequestDataSourcesImpl extends RequestDataSources {
  final ApiService _apiService;

  RequestDataSourcesImpl(this._apiService);

  @override
  Future<List<RequestModel>?> getRequestByCenter(String centerId) async {
    try {
      final response = await _apiService.get("/api/request/center/$centerId");
      if (response != null) {
        List<RequestModel> requests = response
            .map<RequestModel>(
                (e) => RequestModel.fromJson(e as Map<String, dynamic>))
            .toList();
        return requests;
      } else
        return null;
    } catch (e) {
      throw ServerException(e.toString());
    }
  }

  @override
  Future<List<RequestModel>?> getRequestByCoach(String coachId) async {
    try {
      final response = await _apiService.get("/api/request/coach/$coachId");
      if (response != null) {
        List<RequestModel> requests = response
            .map<RequestModel>(
                (e) => RequestModel.fromJson(e as Map<String, dynamic>))
            .toList();
        return requests;
      } else
        return null;
    } catch (e) {
      throw ServerException(e.toString());
    }
  }

  @override
  Future<bool> sendJoinRequest(String coachId, String centerId) async {
    try {
      final response = await _apiService.post("/api/request/coach-to-center", {
        'centerId': centerId,
        'coachId': coachId,
      });

      // Handle the new response format from backend
      if (response is Map<String, dynamic>) {
        // Check if request was successful
        return response['success'] == true;
      }

      // Fallback for backward compatibility
      return response == true;
    } catch (e) {
      throw ServerException(e.toString());
    }
  }

  @override
  Future<bool> updateStatus(String requestId, String status) async {
    try {
      final response = await this
          ._apiService
          .patch("/api/request/$requestId/status", {"status": status});
      return response;
    } catch (e) {
      throw ServerException(e.toString());
    }
  }

  @override
  Future<bool> cancelJoinRequest(String coachId, String centerId) async {
    try {
      final response = await _apiService
          .delete("/api/request/coach-to-center/$coachId/$centerId");
      if (response is Map && response['success'] == true) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      throw ServerException(e.toString());
    }
  }

  @override
  Future<Map<String, dynamic>> checkExistingRequest(
      String coachId, String centerId) async {
    try {
      final response =
          await _apiService.get("/api/request/check/$coachId/$centerId");

      if (response is Map<String, dynamic>) {
        return {
          'exists': response['exists'] ?? false,
          'status': response['status'] ?? 'initial',
          'request': response['request']
        };
      }

      // Fallback
      return {'exists': false, 'status': 'initial', 'request': null};
    } catch (e) {
      throw ServerException(e.toString());
    }
  }
}
