import 'dart:convert';

import 'package:class_z/core/error/failure.dart';
import 'package:class_z/core/network/api_service.dart';
import 'package:class_z/core/common/data/models/announementModel.dart';

abstract class AnnouncementDataSource {
  Future<bool> postAnnouncement(Map<String, dynamic> payload);
  Future<AnnouncementModel?> getAnnouncement(String id, String? type);
}

class AnnouncementDataSourceImpl extends AnnouncementDataSource {
  final ApiService _apiService;

  AnnouncementDataSourceImpl(this._apiService);

  @override
  Future<AnnouncementModel?> getAnnouncement(String id, String? type) async {
    try {
      late final dynamic response;
      print(type);

      if (type == 'class') {
        response = await _apiService.get("/api/announcement/class/$id");
      } else if (type == 'slot') {
        response = await _apiService.get("/api/announcement/slot/$id");
      } else {
        response = await _apiService.get("/api/announcement/$id");
      }

      print(response);

      if (response == null) return null;

      if (response is Map<String, dynamic>) {
        return AnnouncementModel.fromJson(response);
      } else {
        final parsed = jsonDecode(response);
        if (parsed is Map<String, dynamic>) {
          return AnnouncementModel.fromJson(parsed);
        }
      }

      return null; // fallback if response is not in expected format
    } catch (e) {
      throw ServerFailure(message: e.toString());
    }
  }

  @override
  Future<bool> postAnnouncement(Map<String, dynamic> payload) async {
    try {
      final response = await _apiService.post("/api/announcement/", payload);
      return response;
    } catch (e) {
      throw ServerFailure(message: e.toString());
    }
  }
}
