import 'dart:convert';
import 'package:class_z/core/error/failure.dart';
import 'package:class_z/core/network/api_service.dart';
import 'package:class_z/core/common/data/models/campaign_model.dart';

abstract class CampaignDataSource {
  Future<List<CampaignModel>> getActiveCampaigns();
  Future<bool> recordCampaignClick(String campaignId);
}

class CampaignDataSourceImpl extends CampaignDataSource {
  final ApiService _apiService;

  CampaignDataSourceImpl(this._apiService);

  @override
  Future<List<CampaignModel>> getActiveCampaigns() async {
    try {
      print("🚀 CampaignDataSource: Making API call to /api/campaigns/active");
      final response = await _apiService.get("/api/campaigns/active");
      print("📡 CampaignDataSource: Response received: $response");

      if (response == null) return [];

      Map<String, dynamic> responseData;
      if (response is Map<String, dynamic>) {
        responseData = response;
      } else {
        responseData = jsonDecode(response);
      }

      if (responseData['success'] == true && responseData['data'] != null) {
        final List<dynamic> campaignsJson = responseData['data'];
        return campaignsJson
            .map((json) => CampaignModel.fromJson(json))
            .where(
                (campaign) => campaign.isValid) // Filter out invalid campaigns
            .toList();
      }

      return [];
    } catch (e) {
      throw ServerFailure(
          message: "Failed to fetch campaigns: ${e.toString()}");
    }
  }

  @override
  Future<bool> recordCampaignClick(String campaignId) async {
    try {
      final response =
          await _apiService.post("/api/campaigns/$campaignId/click", {});

      if (response == null) return false;

      Map<String, dynamic> responseData;
      if (response is Map<String, dynamic>) {
        responseData = response;
      } else {
        responseData = jsonDecode(response);
      }

      return responseData['success'] == true;
    } catch (e) {
      // Don't throw error for click tracking failures, just log and continue
      print("Failed to record campaign click: $e");
      return false;
    }
  }
}
