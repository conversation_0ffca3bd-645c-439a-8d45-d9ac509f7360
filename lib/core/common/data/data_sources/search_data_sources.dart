import 'package:http/http.dart' as http;
import 'package:class_z/core/imports.dart';

abstract class SearchDataSources {
  Future<SearchModel> getSearchResult(
      String query, int? ageFrom, int? ageTo, String? location, String? sortBy,
      {double? rating, bool? senService, bool? isSearchingCoach});

  Future<SearchModel> getCentersByCategory(
      {String? query,
      int? priceMax,
      int? priceMin,
      int? ageFrom,
      int? ageTo,
      String? location,
      String? sortBy,
      double? rating,
      bool? senService});

  Future<SearchModel> getNearbyCenters(
      {required double longitude,
      required double latitude,
      double? maxDistance,
      int? priceMin,
      int? priceMax,
      int? ageFrom,
      int? ageTo,
      String? sortBy,
      double? rating,
      bool? senService});
}

class SearchDataSourcesImpl extends SearchDataSources {
  String device = AppText.device;

  @override
  Future<SearchModel> getSearchResult(
      String query, int? ageFrom, int? ageTo, String? location, String? sortBy,
      {double? rating, bool? senService, bool? isSearchingCoach}) async {
    try {
      print('isSearchingCoach: $isSearchingCoach');
      // Build query parameters
      Map<String, String> queryParams = {};
      if (isSearchingCoach == true) {
        queryParams = {'coach': query};
      } else
        queryParams = {
          'q': query,
        };

      if (ageFrom != null) queryParams['ageFrom'] = ageFrom.toString();
      if (ageTo != null) queryParams['ageTo'] = ageTo.toString();
      if (location != null) queryParams['location'] = location;
      if (sortBy != null) queryParams['sortBy'] = sortBy;
      if (rating != null) queryParams['rating'] = rating.toString();
      if (senService != null) queryParams['senService'] = senService.toString();

      // Create URI with query parameters
      final uri =
          Uri.parse("$device/api/search").replace(queryParameters: queryParams);
      print('Search API request: $uri');

      final response = await http.get(uri);
      print('Search API response status: ${response.statusCode}');

      if (response.statusCode >= 200 && response.statusCode <= 300) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (isSearchingCoach == true) {
          print('Processing coach search results');

          // Safely create coaches list with error handling
          try {
            List<CoachModel> coaches = [];

            // Try different response formats
            var coachData = responseData['results']?['data'];
            if (coachData == null) {
              print('No coach data found in results');
              return SearchModel(coaches: []);
            }

            if (coachData is List) {
              for (var item in coachData) {
                if (item is Map<String, dynamic>) {
                  try {
                    coaches.add(CoachModel.fromJson(item));
                  } catch (e) {
                    print('Error parsing coach: $e');
                  }
                }
              }
            }

            return SearchModel(coaches: coaches);
          } catch (e) {
            print('Error processing coach results: $e');
            return SearchModel(coaches: []);
          }
        }

        // For regular search
        try {
          SearchModel search =
              SearchModel.fromJson(responseData['results']['results']);
          return search;
        } catch (e) {
          print('Error parsing search results: $e');
          return SearchModel(); // Return empty model
        }
      } else {
        print('Search API error: ${response.body}');
        throw Exception('API Error: ${response.statusCode}');
      }
    } catch (e) {
      print('Search exception: ${e.toString()}');
      // Return empty model instead of throwing
      return SearchModel();
    }
  }

  @override
  Future<SearchModel> getCentersByCategory(
      {String? query,
      int? priceMax,
      int? priceMin,
      int? ageFrom,
      int? ageTo,
      String? location,
      String? sortBy,
      double? rating,
      bool? senService}) async {
    try {
      // Build query parameters
      Map<String, String> queryParams = {};

      if (query != null) queryParams['category'] = query;
      if (priceMin != null) queryParams['priceMin'] = priceMin.toString();
      if (priceMax != null) queryParams['priceMax'] = priceMax.toString();
      if (ageFrom != null) queryParams['ageFrom'] = ageFrom.toString();
      if (ageTo != null) queryParams['ageTo'] = ageTo.toString();
      if (location != null) queryParams['location'] = location;
      if (sortBy != null) queryParams['sortBy'] = sortBy;
      if (rating != null) queryParams['rating'] = rating.toString();
      if (senService != null) queryParams['senService'] = senService.toString();

      // Create URI with query parameters
      final uri = Uri.parse("${AppText.device}/api/search/classes/filter")
          .replace(queryParameters: queryParams);

      print('Filter API request: $uri');

      final response = await http.get(uri);
      print('Filter API response status: ${response.statusCode}');

      if (response.statusCode >= 200 && response.statusCode <= 300) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true) {
          final List<dynamic> classData = responseData['data'];
          List<ClassModel> classes = classData
              .map((e) => ClassModel.fromJson(e as Map<String, dynamic>))
              .toList();
          SearchModel search = SearchModel(classes: classes);
          return search;
        } else {
          print('Filter API returned error: ${responseData['message']}');
          throw Exception(responseData['message'] ?? 'API Error');
        }
      } else {
        print('Filter API error: ${response.body}');
        throw Exception('API Error: ${response.statusCode}');
      }
    } catch (e) {
      print('Filter exception: ${e.toString()}');
      throw Exception(e.toString());
    }
  }

  @override
  Future<SearchModel> getNearbyCenters(
      {required double longitude,
      required double latitude,
      double? maxDistance,
      int? priceMin,
      int? priceMax,
      int? ageFrom,
      int? ageTo,
      String? sortBy,
      double? rating,
      bool? senService}) async {
    try {
      // Build query parameters
      Map<String, String> queryParams = {
        'longitude': longitude.toString(),
        'latitude': latitude.toString(),
      };

      if (maxDistance != null)
        queryParams['maxDistance'] = maxDistance.toString();
      if (priceMin != null) queryParams['priceMin'] = priceMin.toString();
      if (priceMax != null) queryParams['priceMax'] = priceMax.toString();
      if (ageFrom != null) queryParams['ageFrom'] = ageFrom.toString();
      if (ageTo != null) queryParams['ageTo'] = ageTo.toString();
      if (sortBy != null) queryParams['sortBy'] = sortBy;
      if (rating != null) queryParams['rating'] = rating.toString();
      if (senService != null) queryParams['senService'] = senService.toString();

      // Create URI with query parameters
      final uri = Uri.parse("${AppText.device}/api/search/centers/nearby")
          .replace(queryParameters: queryParams);

      print('Nearby API request: $uri');

      final response = await http.get(uri);
      print('Nearby API response status: ${response.statusCode}');

      if (response.statusCode >= 200 && response.statusCode <= 300) {
        final Map<String, dynamic> responseData = json.decode(response.body);

        if (responseData['success'] == true) {
          final List<dynamic> centersData = responseData['data'];
          List<CenterData> centers = centersData
              .map((e) => CenterData.fromJson(e as Map<String, dynamic>))
              .toList();
          SearchModel search = SearchModel(centers: centers);
          return search;
        } else {
          print('Nearby API returned error: ${responseData['message']}');
          throw Exception(responseData['message'] ?? 'API Error');
        }
      } else {
        print('Nearby API error: ${response.body}');
        throw Exception('API Error: ${response.statusCode}');
      }
    } catch (e) {
      print('Nearby exception: ${e.toString()}');
      throw Exception(e.toString());
    }
  }
}
