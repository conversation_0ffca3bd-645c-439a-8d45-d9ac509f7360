import 'dart:convert';

import 'package:class_z/core/common/data/models/user_model.dart';
import 'package:class_z/core/common/data/models/subscriptionModel.dart';
import 'package:class_z/core/constants/string.dart';
import 'package:class_z/core/utils/shared_repo.dart';
import 'package:http/http.dart' as http;

abstract class SubscriptionDataSources {
  Future<SubscriptionModel> getSubscription();
  Future<bool> cancelSubscription();
}

class SubscriptionDataSourcesImpl extends SubscriptionDataSources {
  final http.Client client;
  final SharedRepository sharedRepository;
  String device = AppText.device;

  SubscriptionDataSourcesImpl(
      {required this.client, required this.sharedRepository});
  @override
  Future<SubscriptionModel> getSubscription() async {
    try {
      UserModel? user = sharedRepository.getUserData();
      String userId = user!.data!.parent!.id!;
      String uri = "$device/api/subscriptionPlan?userId=$userId";
      final response = await http.get(
        Uri.parse(uri),
        headers: {
          'Content-Type': 'application/json',
        },
      );
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        print(response.body);
        final jsonData = jsonDecode(response.body);
        print(jsonData);
        SubscriptionModel subscribeModel = SubscriptionModel.fromJson(jsonData);

        return subscribeModel;
      } else {
        throw Exception("Something went Wrong");
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<bool> cancelSubscription() async {
    try {
      UserModel? user = sharedRepository.getUserData();
      String userId = user!.data!.parent!.id!;
      String uri = "$device/api/subscription/$userId";

      final response = await http.delete(
        Uri.parse(uri),
        headers: {
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode >= 200 && response.statusCode <= 300) {
        print("Subscription cancelled successfully: ${response.body}");
        return true;
      } else {
        throw Exception(
            "Failed to cancel subscription: ${response.statusCode}");
      }
    } catch (e) {
      throw Exception("Error cancelling subscription: ${e.toString()}");
    }
  }
}
