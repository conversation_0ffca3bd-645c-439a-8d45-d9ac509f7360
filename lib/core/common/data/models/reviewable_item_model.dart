import 'package:class_z/core/common/data/models/class_model.dart';
import 'package:class_z/core/common/data/models/event_model.dart';

class ReviewableItem {
  final ClassModel classModel;
  final EventModel
      sessionEvent; // The specific session that triggered the review
  final int currentSessionIndex;
  final int totalSessions;
  final bool
      isFirstLessonReview; // True if this is for the first lesson, false for completion
  final String reviewType; // e.g., "First Lesson" or "Course Completion"
  final String? childId; // Added to store the ID of the child this review is for
  // Consider adding childName if easily accessible during creation

  ReviewableItem({
    required this.classModel,
    required this.sessionEvent,
    required this.currentSessionIndex,
    required this.totalSessions,
    required this.isFirstLessonReview,
    required this.reviewType,
    this.childId, // Added to constructor
  });
}
