import 'package:class_z/core/imports.dart';

class AnnouncementModel extends AnnouncementEntity {
  int? v;
  AnnouncementModel({
    String? id,
    dynamic classId,
    ClassDate? slotId,
    List<MessageModel>? messages,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.v,
  }) : super(
          id: id,
          classId: classId,
          slotId: slotId,
          messages: messages,
          createdAt: createdAt,
          updatedAt: updatedAt,
          v: v,
        );

  factory AnnouncementModel.fromJson(Map<String, dynamic> json) =>
      AnnouncementModel(
        id: json["_id"],
        classId: json["classId"] == null
            ? null
            : (json["classId"] is String)
                ? json["classId"]
                : ClassModel.fromJson(json["classId"] as Map<String, dynamic>),
        slotId:
            json['slotId'] == null ? null : ClassDate.fromJson(json['slotId']),
        messages: json["messages"] == null
            ? []
            : List<MessageModel>.from(
                json["messages"]!.map((x) => MessageModel.fromJson(x))),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "classId": classId?.toJson(),
        "messages": messages == null
            ? []
            : List<dynamic>.from(
                messages!.map((x) => (x as MessageModel).toJson())),
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
      };
}
