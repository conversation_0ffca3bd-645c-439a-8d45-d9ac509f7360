// To parse this JSON data, do
//
//     final classDate = classDateFromJson(jsonString);

import 'dart:convert';

import 'package:class_z/features/roles/parent/data/models/child_modle.dart';

ClassDate classDateFromJson(String str) => ClassDate.fromJson(json.decode(str));

String classDateToJson(ClassDate data) => json.encode(data.toJson());

class ClassDate {
  String? id;
  String? classId;
  String? date;
  String? address;
  String? startTime;
  String? endTime;
  String? durationMinutes;
  String? numberOfClass;
  String? weekDay;
  String? repeat;
  List<dynamic>? students;
  int? minimumStudent;
  int? numberOfStudent;
  int? charge;
  List<String>? languageOptions;
  bool? buyAll;
  bool? joinNew;
  String? status;
  dynamic cancellationType;
  dynamic cancelledAt;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? v;

  ClassDate({
    this.id,
    this.classId,
    this.date,
    this.address,
    this.startTime,
    this.endTime,
    this.durationMinutes,
    this.numberOfClass,
    this.weekDay,
    this.repeat,
    this.students,
    this.minimumStudent,
    this.numberOfStudent,
    this.charge,
    this.languageOptions,
    this.buyAll,
    this.joinNew,
    this.status,
    this.cancellationType,
    this.cancelledAt,
    this.createdAt,
    this.updatedAt,
    this.v,
  });

  factory ClassDate.fromJson(Map<String, dynamic> json) => ClassDate(
        id: json["_id"],
        classId: json["classId"],
        date: json["date"],
        address: json["address"],
        startTime: json["startTime"],
        endTime: json["endTime"],
        durationMinutes: json["durationMinutes"],
        weekDay: json["weekDay"],
        repeat: json["repeat"],
        students: json["students"] == null
            ? []
            : (json["students"] is List)
                ? (json["students"].isNotEmpty &&
                        json["students"].every((x) => x is Map))
                    ? List<ChildModel>.from(json["students"].map(
                        (x) => ChildModel.fromJson(x as Map<String, dynamic>)))
                    : (json["students"].every((x) => x is String))
                        ? List<String>.from(json["students"])
                        : []
                : [],
        minimumStudent: json["minimumStudent"],
        numberOfStudent: json["numberOfStudent"],
        numberOfClass: json["numberOfClass"].toString(),
        charge: json["charge"],
        languageOptions: json["languageOptions"] == null
            ? []
            : List<String>.from(json["languageOptions"]!.map((x) => x)),
        buyAll: json["buyAll"],
        joinNew: json["joinNew"],
        status: json["status"],
        cancellationType: json["cancellationType"],
        cancelledAt: json["cancelledAt"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "classId": classId,
        "date": date,
        "address": address,
        "startTime": startTime,
        "endTime": endTime,
        "durationMinutes": durationMinutes,
        "weekDay": weekDay,
        "repeat": repeat,
        "students":
            students == null ? [] : List<dynamic>.from(students!.map((x) => x)),
        "minimumStudent": minimumStudent,
        "numberOfStudent": numberOfStudent,
        "numberOfClass": numberOfClass,
        "charge": charge,
        "languageOptions": languageOptions == null
            ? []
            : List<dynamic>.from(languageOptions!.map((x) => x)),
        "buyAll": buyAll,
        "joinNew": joinNew,
        "status": status,
        "cancellationType": cancellationType,
        "cancelledAt": cancelledAt,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
      };
}
