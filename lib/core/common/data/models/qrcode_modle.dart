// To parse this JSON data, do
//
//     final qrCodeModel = qrCodeModelFromJson(jsonString);

import 'dart:convert';

QrCodeModel qrCodeModelFromJson(String str) =>
    QrCodeModel.fromJson(json.decode(str));

String qrCodeModelToJson(QrCodeModel data) => json.encode(data.toJson());

class QrCodeModel {
  String? code;
  String? qrCodeData;

  QrCodeModel({
    this.code,
    this.qrCodeData,
  });

  factory QrCodeModel.fromJson(Map<String, dynamic> json) => QrCodeModel(
        code: json["code"],
        qrCodeData: json["qrCodeData"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "qrCodeData": qrCodeData,
      };
}
