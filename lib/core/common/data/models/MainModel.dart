// ignore_for_file: file_names

import 'package:class_z/core/common/data/models/mainPageModel.dart';

import 'package:equatable/equatable.dart';

// ignore: must_be_immutable
class MainModel extends Equatable {
  MainModel({this.mainPagemodel = const []});
  List<MainPageModel> mainPagemodel;

  MainModel copywith(List<MainPageModel>? mainPageModel) {
    return MainModel(mainPagemodel: mainPagemodel);
  }

  @override
  // ignore: todo
  // TODO: implement props
  List<Object?> get props => [mainPagemodel];
}
