// To parse this JSON data, do
//
//     final eventDatesModel = eventDatesModelFromJson(jsonString);

import 'dart:convert';

import 'package:class_z/core/common/data/models/classDate_model.dart';

EventDatesModel eventDatesModelFromJson(String str) =>
    EventDatesModel.fromJson(json.decode(str));

String eventDatesModelToJson(EventDatesModel data) =>
    json.encode(data.toJson());

class EventDatesModel {
  List<EventDate>? eventDates;

  EventDatesModel({
    this.eventDates,
  });

  factory EventDatesModel.fromJson(Map<String, dynamic> json) =>
      EventDatesModel(
        eventDates: json["eventDates"] == null
            ? []
            : List<EventDate>.from(
                json["eventDates"]!.map((x) => EventDate.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "eventDates": eventDates == null
            ? []
            : List<dynamic>.from(eventDates!.map((x) => x.toJson())),
      };
}

class EventDate {
  ClassDate? dateId;
  List<DateTime>? dates;

  EventDate({
    this.dateId,
    this.dates,
  });

  factory EventDate.fromJson(Map<String, dynamic> json) => EventDate(
        dateId:
            json["dateId"] == null ? null : ClassDate.fromJson(json["dateId"]),
        dates: json["dates"] == null
            ? []
            : List<DateTime>.from(json["dates"]!.map((x) => DateTime.parse(x))),
      );

  Map<String, dynamic> toJson() => {
        "dateId": dateId?.toJson(),
        "dates": dates == null
            ? []
            : List<dynamic>.from(dates!.map((x) => x.toIso8601String())),
      };
}

// class DateId {
//     String? id;
//     String? classId;
//     String? date;
//     String? startTime;
//     String? endTime;
//     String? durationMinutes;
//     String? weekDay;
//     String? repeat;
//     List<dynamic>? students;
//     String? status;
//     DateTime? createdAt;
//     DateTime? updatedAt;
//     int? v;

//     DateId({
//         this.id,
//         this.classId,
//         this.date,
//         this.startTime,
//         this.endTime,
//         this.durationMinutes,
//         this.weekDay,
//         this.repeat,
//         this.students,
//         this.status,
//         this.createdAt,
//         this.updatedAt,
//         this.v,
//     });

//     factory DateId.fromJson(Map<String, dynamic> json) => DateId(
//         id: json["_id"],
//         classId: json["classId"],
//         date: json["date"],
//         startTime: json["startTime"],
//         endTime: json["endTime"],
//         durationMinutes: json["durationMinutes"],
//         weekDay: json["weekDay"],
//         repeat: json["repeat"],
//         students: json["students"] == null ? [] : List<dynamic>.from(json["students"]!.map((x) => x)),
//         status: json["status"],
//         createdAt: json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
//         updatedAt: json["updatedAt"] == null ? null : DateTime.parse(json["updatedAt"]),
//         v: json["__v"],
//     );

//     Map<String, dynamic> toJson() => {
//         "_id": id,
//         "classId": classId,
//         "date": date,
//         "startTime": startTime,
//         "endTime": endTime,
//         "durationMinutes": durationMinutes,
//         "weekDay": weekDay,
//         "repeat": repeat,
//         "students": students == null ? [] : List<dynamic>.from(students!.map((x) => x)),
//         "status": status,
//         "createdAt": createdAt?.toIso8601String(),
//         "updatedAt": updatedAt?.toIso8601String(),
//         "__v": v,
//     };
// }
