// To parse this JSON data, do
//
//     final subscriptionModel = subscriptionModelFromJson(jsonString);

import 'dart:convert';

SubscriptionModel subscriptionModelFromJson(String str) => SubscriptionModel.fromJson(json.decode(str));

String subscriptionModelToJson(SubscriptionModel data) => json.encode(data.toJson());

class SubscriptionModel {
    Current? current;
    List<Plan>? plans;

    SubscriptionModel({
        this.current,
        this.plans,
    });

    factory SubscriptionModel.fromJson(Map<String, dynamic> json) => SubscriptionModel(
        current: json["current"] == null ? null : Current.fromJson(json["current"]),
        plans: json["plans"] == null ? [] : List<Plan>.from(json["plans"]!.map((x) => Plan.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "current": current?.toJson(),
        "plans": plans == null ? [] : List<dynamic>.from(plans!.map((x) => x.toJson())),
    };
}

class Current {
    String? id;
    String? userId;
    String? planId;
    DateTime? startDate;
    int? v;

    Current({
        this.id,
        this.userId,
        this.planId,
        this.startDate,
        this.v,
    });

    factory Current.fromJson(Map<String, dynamic> json) => Current(
        id: json["_id"],
        userId: json["userId"],
        planId: json["planId"],
        startDate: json["startDate"] == null ? null : DateTime.parse(json["startDate"]),
        v: json["__v"],
    );

    Map<String, dynamic> toJson() => {
        "_id": id,
        "userId": userId,
        "planId": planId,
        "startDate": startDate?.toIso8601String(),
        "__v": v,
    };
}

class Plan {
    String? id;
    String? name;
    int? amount;
    int? zCoin;
    String? interval;
    int? v;

    Plan({
        this.id,
        this.name,
        this.amount,
        this.zCoin,
        this.interval,
        this.v,
    });

    factory Plan.fromJson(Map<String, dynamic> json) => Plan(
        id: json["_id"],
        name: json["name"],
        amount: json["amount"],
        zCoin: json["zCoin"],
        interval: json["interval"],
        v: json["__v"],
    );

    Map<String, dynamic> toJson() => {
        "_id": id,
        "name": name,
        "amount": amount,
        "zCoin": zCoin,
        "interval": interval,
        "__v": v,
    };
}
