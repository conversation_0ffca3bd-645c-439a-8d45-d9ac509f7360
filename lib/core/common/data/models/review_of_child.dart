// To parse this JSON data, do
//
//     final reviewOfChildModel = reviewOfChildModelFromJson(jsonString);

import 'dart:convert';

import 'package:class_z/core/common/data/models/class_model.dart';
import 'package:class_z/features/roles/center/data/models/center_model.dart';

List<ReviewOfChildModel> reviewOfChildModelFromJson(String str) =>
    List<ReviewOfChildModel>.from(
        json.decode(str).map((x) => ReviewOfChildModel.fromJson(x)));

String reviewOfChildModelToJson(List<ReviewOfChildModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class ReviewOfChildModel {
  Questions? questions;
  String? id;
  String? reviewerId;
  String? reviewerType;
  ClassModel? classId;
  String? revieweeId;
  String? revieweeType;
  String? centerName;
  String? coachName;
  double? rating;
  String? title;
  String? comment;
  String? topic;
  List<CenterImage>? images;
  DateTime? date;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? v;
  String? bestQuestion;
  String? worstQuestion;

  ReviewOfChildModel(
      {this.questions,
      this.id,
      this.reviewerId,
      this.reviewerType,
      this.classId,
      this.revieweeId,
      this.revieweeType,
      this.centerName,
      this.coachName,
      this.rating,
      this.title,
      this.comment,
      this.topic,
      this.date,
      this.createdAt,
      this.updatedAt,
      this.v,
      this.images,
      this.bestQuestion,
      this.worstQuestion});

  factory ReviewOfChildModel.fromJson(Map<String, dynamic> json) {
    ClassModel? parsedClassId;
    if (json["classId"] != null) {
      if (json["classId"] is Map<String, dynamic>) {
        parsedClassId = ClassModel.fromJson(json["classId"]);
      } else if (json["classId"] is String) {
        // If classId is a String, create a ClassModel with only the id
        parsedClassId = ClassModel(id: json["classId"]);
      }
    }

    return ReviewOfChildModel(
      questions: json["questions"] == null
          ? null
          : Questions.fromJson(json["questions"]),
      id: json["_id"],
      reviewerId: json["reviewerId"],
      reviewerType: json["reviewerType"],
      classId: parsedClassId,
      revieweeId: json["revieweeId"],
      revieweeType: json["revieweeType"],
      centerName: json["centerName"],
      coachName: json["coachName"],
      rating: json["rating"]?.toDouble(),
      title: json["title"],
      comment: json["comment"],
      topic: json["topic"],
      images: json["images"] == null
          ? null
          : List<CenterImage>.from(
              json["images"].map((x) => CenterImage.fromJson(x))),
      date: json["date"] == null ? null : DateTime.parse(json["date"]),
      createdAt:
          json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
      updatedAt:
          json["updatedAt"] == null ? null : DateTime.parse(json["updatedAt"]),
      v: json["__v"],
      bestQuestion: json["bestQuestion"],
      worstQuestion: json["worstQuestion"],
    );
  }

  Map<String, dynamic> toJson() => {
        "questions": questions?.toJson(),
        "_id": id,
        "reviewerId": reviewerId,
        "reviewerType": reviewerType,
        "classId": classId == null
            ? null
            : classId is ClassModel
                ? classId?.toJson()
                : classId is String
                    ? classId
                    : classId.toString(),
        "revieweeId": revieweeId,
        "revieweeType": revieweeType,
        "centerName": centerName,
        "coachName": coachName,
        "rating": rating,
        "title": title,
        "comment": comment,
        "topic": topic,
        "date": date?.toIso8601String(),
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "images": images == null
            ? null
            : List<dynamic>.from(images!.map((x) => x.toJson())),
        "__v": v,
        "bestQuestion": bestQuestion,
        "worstQuestion": worstQuestion,
      };
}

class Questions {
  double? q1;
  double? q2;
  double? q3;
  double? q4;
  double? q5;
  double? q6;

  Questions({
    this.q1,
    this.q2,
    this.q3,
    this.q4,
    this.q5,
    this.q6,
  });

  factory Questions.fromJson(Map<String, dynamic> json) => Questions(
        q1: json["q1"]?.toDouble(),
        q2: json["q2"]?.toDouble(),
        q3: json["q3"]?.toDouble(),
        q4: json["q4"]?.toDouble(),
        q5: json["q5"]?.toDouble(),
        q6: json["q6"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "q1": q1,
        "q2": q2,
        "q3": q3,
        "q4": q4,
        "q5": q5,
        "q6": q6,
      };
}
