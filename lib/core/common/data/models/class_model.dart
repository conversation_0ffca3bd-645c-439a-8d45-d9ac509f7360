import 'package:class_z/core/imports.dart';

ClassModel classModelFromJson(String str) =>
    ClassModel.fromJson(json.decode(str));

String classModelToJson(ClassModel data) => json.encode(data.toJson());

class ClassModel {
  BusinessCertificate? mainImage;
  String? id;
  String? classProviding;
  String? level;
  String? description;
  bool? mode;
  bool? sen;
  String? initialCharge;
  int? charge;
  dynamic center;
  dynamic coach;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? v;
  bool? course;
  bool? newComer;
  int? ageFrom;
  int? ageTo;
  List<DateModel>? dates;
  int? numberOfStudent;
  int? numberOfClass;
  List<String>? language;
  String? address;
  bool? buyAll;
  bool? joinNew;
  dynamic student;

  ClassModel({
    this.mainImage,
    this.id = '',
    this.classProviding = '',
    this.level = '',
    this.description = '',
    this.mode = false,
    this.sen = false,
    this.initialCharge = '',
    this.charge,
    this.center,
    this.coach,
    this.createdAt,
    this.updatedAt,
    this.v,
    this.course = false,
    this.newComer = false,
    this.ageFrom,
    this.ageTo,
    this.dates = const [],
    this.numberOfStudent = 0,
    this.numberOfClass = 0,
    this.language = const [],
    this.address = '',
    this.buyAll = false,
    this.joinNew = false,
    this.student = const [],
  });

  factory ClassModel.fromJson(Map<String, dynamic> json) => ClassModel(
        mainImage: json["mainImage"] == null
            ? null
            : BusinessCertificate.fromJson(json["mainImage"]),
        id: json["_id"],
        classProviding: json["classProviding"],
        level: json["level"],
        description: json["description"],
        mode: json["mode"],
        sen: json["sen"],
        initialCharge: json["initialCharge"],
        charge: json["charge"],
        center: json['center'] is String
            ? CenterData(
                id: json['center']) // Store only the ID if it's a string
            : json['center'] is Map<String, dynamic>
                ? CenterData.fromJson(
                    json['center']) // Parse object if it's valid
                : null,
        coach: () {
          final coachJson = json['coach'];
          print("🔍 ClassModel.fromJson - Coach parsing:");
          print("  - Coach JSON type: ${coachJson.runtimeType}");
          print("  - Coach JSON value: $coachJson");

          if (coachJson == null) {
            print("  - Result: null (coach is null)");
            return null;
          }
          if (coachJson is String) {
            print("  - Result: CoachModel with ID only");
            return CoachModel(id: coachJson);
          }
          if (coachJson is Map<String, dynamic>) {
            print("  - Result: Parsing full CoachModel from JSON");
            final parsedCoach = CoachModel.fromJson(coachJson);
            print("  - Parsed coach ID: ${parsedCoach.id}");
            print("  - Parsed coach name: ${parsedCoach.displayName}");
            return parsedCoach;
          }
          print("  - Result: null (unknown type)");
          return null;
        }(),
// Assign `null` if it's missing

        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
        course: json["course"],
        newComer: json["newComer"],
        ageFrom: json["ageFrom"],
        ageTo: json["ageTo"],
        dates: json["dates"] == null
            ? []
            : List<DateModel>.from(
                json["dates"].map((x) => DateModel.fromJson(x))),
        numberOfStudent: json["numberOfStudent"] != null
            ? int.parse(json["numberOfStudent"].toString())
            : 0,
        numberOfClass: json["numberOfClass"] != null
            ? int.parse(json["numberOfClass"].toString())
            : 0,

        language: json["languageOptions"] == null
            ? []
            : List<String>.from(json["languageOptions"].map((x) => x)),
        address: json["address"],
        buyAll: json["buyAll"],
        joinNew: json["joinNew"],
        student: json["student"] == null
            ? []
            : json["student"] is List && json["student"].isNotEmpty
                ? (json["student"][0] is Map
                    ? List<ChildModel>.from(
                        json["student"].map((x) => ChildModel.fromJson(x)))
                    : List<String>.from(json["student"]))
                : [],
      );

  Map<String, dynamic> toJson() => {
        "mainImage": mainImage?.toJson(),
        "_id": id,
        "classProviding": classProviding,
        "level": level,
        "description": description,
        "mode": mode,
        "sen": sen,
        "initialCharge": initialCharge,
        "charge": charge,
        "center": center is CenterData ? center.toJson() : center,
        "coach": coach is CoachModel ? coach?.toJson() : coach,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
        "course": course,
        "newComer": newComer,
        "ageFrom": ageFrom,
        "ageTo": ageTo,
        "dates": dates == null
            ? []
            : List<dynamic>.from(dates!.map((x) => x.toJson())),
        "numberOfStudent": numberOfStudent,
        "languageOptions":
            language == null ? [] : List<dynamic>.from(language!.map((x) => x)),
        "address": address,
        "buyAll": buyAll,
        "joinNew": joinNew,
        "student": student == null
            ? []
            : List<dynamic>.from(student!.map((x) {
                // Handle the case when x might be a String or doesn't have toJson method
                if (x is String) {
                  return x;
                } else if (x is ChildModel) {
                  return x.toJson();
                } else if (x is Map) {
                  return x;
                } else {
                  // If it's some other type, convert to string to avoid errors
                  return x.toString();
                }
              })),
      };

  @override
  String toString() {
    return 'ClassModel(mainImage: $mainImage, id: $id, classProviding: $classProviding, level: $level, description: $description, mode: $mode, sen: $sen, initialCharge: $initialCharge, charge: $charge, center: $center, coach: $coach, createdAt: $createdAt, updatedAt: $updatedAt, v: $v, course: $course, newComer: $newComer, ageFrom: $ageFrom, ageTo: $ageTo, dates: $dates, numberOfStudent: $numberOfStudent, language: $language, address: $address, buyAll: $buyAll, joinNew: $joinNew, student: $student)';
  }
}

class Centre {
  String? id;
  CentreData? centerData;

  Centre({
    this.id,
    this.centerData,
  });

  factory Centre.fromJson(Map<String, dynamic> json) => Centre(
        id: json["_id"],
        centerData: json["centerData"] == null
            ? null
            : CentreData.fromJson(json["centerData"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "centerData": centerData?.toJson(),
      };

  @override
  String toString() {
    return 'Centre(id: $id, centerData: $centerData)';
  }
}

class CentreData {
  Address? address;
  String? companyNumber;

  CentreData({
    this.address,
    this.companyNumber,
  });

  factory CentreData.fromJson(Map<String, dynamic> json) => CentreData(
        address:
            json["address"] == null ? null : Address.fromJson(json["address"]),
        companyNumber: json["companyNumber"],
      );

  Map<String, dynamic> toJson() => {
        "address": address?.toJson(),
        "companyNumber": companyNumber,
      };

  @override
  String toString() {
    return 'CentreData(address: $address, companyNumber: $companyNumber)';
  }
}

class Address {
  String? address1;
  String? address2;
  String? city;
  String? region;
  String? id;

  Address({
    this.address1,
    this.address2,
    this.city,
    this.region,
    this.id,
  });

  factory Address.fromJson(Map<String, dynamic> json) => Address(
        address1: json["address1"],
        address2: json["address2"],
        city: json["city"],
        region: json["region"],
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "address1": address1,
        "address2": address2,
        "city": city,
        "region": region,
        "_id": id,
      };

  @override
  String toString() {
    // Concatenate all parts of the address with proper formatting
    return '$address1${address2 != null ? ', $address2' : ''}, $city, $region';
  }
}

class DateModel {
  String? date;
  String? startTime;
  String? endTime;
  String? durationMinutes;
  String? weekDay;
  String? repeat;
  String? id;
  int? charge;

  DateModel({
    this.date,
    this.startTime,
    this.endTime,
    this.durationMinutes,
    this.weekDay,
    this.repeat,
    this.id,
    this.charge,
  });

  factory DateModel.fromJson(Map<String, dynamic> json) => DateModel(
        date: json["date"],
        startTime: json["startTime"],
        endTime: json["endTime"],
        durationMinutes: json["durationMinutes"],
        weekDay: json["weekDay"],
        repeat: json["repeat"],
        id: json["_id"],
        charge: json["charge"],
      );

  Map<String, dynamic> toJson() => {
        "date": date,
        "startTime": startTime,
        "endTime": endTime,
        "durationMinutes": durationMinutes,
        "weekDay": weekDay,
        "repeat": repeat,
        "_id": id,
        "charge": charge,
      };

  @override
  String toString() {
    return 'DateModel(date: $date, startTime: $startTime, endTime: $endTime, durationMinutes: $durationMinutes, weekDay: $weekDay, repeat: $repeat, id: $id, charge: $charge)';
  }
}
