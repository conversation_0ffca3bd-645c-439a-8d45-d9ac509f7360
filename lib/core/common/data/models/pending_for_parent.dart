// To parse this JSON data, do
//
//     final reviewResponse = reviewResponseFromJson(jsonString);

import 'dart:convert';

import 'package:class_z/core/common/data/models/class_model.dart';
import 'package:class_z/features/roles/parent/data/models/child_modle.dart';

// To parse this JSON data, do
//
//     final reviewResponse = reviewResponseFromJson(jsonString);

ReviewResponse reviewResponseFromJson(String str) =>
    ReviewResponse.fromJson(json.decode(str));

String reviewResponseToJson(ReviewResponse data) => json.encode(data.toJson());

class ReviewResponse {
  String? message;
  AllReviews? allReviews;

  ReviewResponse({
    this.message,
    this.allReviews,
  });

  factory ReviewResponse.fromJson(Map<String, dynamic> json) => ReviewResponse(
        message: json["message"],
        allReviews: json["allReviews"] == null
            ? null
            : AllReviews.fromJson(json["allReviews"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "allReviews": allReviews?.toJson(),
      };
}

class AllReviews {
  List<ReviewNew>? reviews;
  List<PendingReviewNew>? pendingReviewsNew;

  AllReviews({
    this.reviews,
    this.pendingReviewsNew,
  });

  factory AllReviews.fromJson(Map<String, dynamic> json) => AllReviews(
        reviews: json["reviews"] == null
            ? []
            : List<ReviewNew>.from(
                json["reviews"]!.map((x) => ReviewNew.fromJson(x))),
        pendingReviewsNew: json["pendingReviews"] == null
            ? []
            : List<PendingReviewNew>.from(json["pendingReviews"]!
                .map((x) => PendingReviewNew.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "reviews": reviews == null
            ? []
            : List<dynamic>.from(reviews!.map((x) => x.toJson())),
        "pendingReviews": pendingReviewsNew == null
            ? []
            : List<dynamic>.from(pendingReviewsNew!.map((x) => x.toJson())),
      };
}

class PendingReviewNew {
  String? id;
  ClassModel? classId;
  ChildModel? childId;
  DateTime? createdAt;

  PendingReviewNew({
    this.id,
    this.classId,
    this.childId,
    this.createdAt,
  });

  factory PendingReviewNew.fromJson(Map<String, dynamic> json) =>
      PendingReviewNew(
        id: json["_id"],
        classId: json["classId"] == null
            ? null
            : ClassModel.fromJson(json["classId"]),
        childId: json["childId"] == null
            ? null
            : ChildModel.fromJson(json["childId"]),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "classId": classId?.toJson(),
        "childId": childId?.toJson(),
        "createdAt": createdAt?.toIso8601String(),
      };
}

class ReviewNew {
  String? id;
  String? reviewerId;
  String? reviewerType;
  ClassModel? classId;
  ChildModel? childId;
  String? revieweeId;
  String? revieweeType;
  String? centerName;
  String? coachName;
  int? rating;
  String? title;
  DateTime? date;
  List<dynamic>? images;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? v;

  ReviewNew({
    this.id,
    this.reviewerId,
    this.reviewerType,
    this.classId,
    this.childId,
    this.revieweeId,
    this.revieweeType,
    this.centerName,
    this.coachName,
    this.rating,
    this.title,
    this.date,
    this.images,
    this.createdAt,
    this.updatedAt,
    this.v,
  });

  factory ReviewNew.fromJson(Map<String, dynamic> json) => ReviewNew(
        id: json["_id"],
        reviewerId: json["reviewerId"],
        reviewerType: json["reviewerType"],
        classId: json["classId"] == null
            ? null
            : ClassModel.fromJson(json["classId"]),
        childId: json["childInfo"] == null
            ? null
            : ChildModel.fromJson(json['childInfo']),
        revieweeId: json["revieweeId"],
        revieweeType: json["revieweeType"],
        centerName: json["centerName"],
        coachName: json["coachName"],
        rating: json["rating"],
        title: json["title"],
        date: json["date"] == null ? null : DateTime.parse(json["date"]),
        images: json["images"] == null
            ? []
            : List<dynamic>.from(json["images"]!.map((x) => x)),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "reviewerId": reviewerId,
        "reviewerType": reviewerType,
        "classId": classId,
        "revieweeId": revieweeId,
        "revieweeType": revieweeType,
        "centerName": centerName,
        "coachName": coachName,
        "rating": rating,
        "title": title,
        "date": date?.toIso8601String(),
        "images":
            images == null ? [] : List<dynamic>.from(images!.map((x) => x)),
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
      };
}
