class Date {
  String? date;
  String? startTime;
  String? endTime;
  String? durationMinutes;
  String? weekDay;
  String? repeat;
  String? id;

  Date({
    this.date,
    this.startTime,
    this.endTime,
    this.durationMinutes,
    this.weekDay,
    this.repeat,
    this.id,
  });

  factory Date.fromJson(Map<String, dynamic> json) => Date(
        date: json["date"],
        startTime: json["startTime"],
        endTime: json["endTime"],
        durationMinutes: json["durationMinutes"],
        weekDay: json["weekDay"],
        repeat: json["repeat"],
        id: json["_id"],
      );

  Map<String, dynamic> toJson() => {
        "date": date,
        "startTime": startTime,
        "endTime": endTime,
        "durationMinutes": durationMinutes,
        "weekDay": weekDay,
        "repeat": repeat,
        "_id": id,
      };
}