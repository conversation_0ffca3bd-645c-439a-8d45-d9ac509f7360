// To parse this JSON data, do
//
//     final balanceModel = balanceModelFromJson(jsonString);

import 'dart:convert';

BalanceModel balanceModelFromJson(String str) => BalanceModel.fromJson(json.decode(str));

String balanceModelToJson(BalanceModel data) => json.encode(data.toJson());

class BalanceModel {
    String? id;
    String? userId;
    int? balance;
    int? v;

    BalanceModel({
        this.id,
        this.userId,
        this.balance,
        this.v,
    });

    factory BalanceModel.fromJson(Map<String, dynamic> json) => BalanceModel(
        id: json["_id"],
        userId: json["userId"],
        balance: json["balance"],
        v: json["__v"],
    );

    Map<String, dynamic> toJson() => {
        "_id": id,
        "userId": userId,
        "balance": balance,
        "__v": v,
    };
}
