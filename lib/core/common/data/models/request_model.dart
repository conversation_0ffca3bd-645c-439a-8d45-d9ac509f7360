import 'package:class_z/core/imports.dart';

RequestModel requestModelFromJson(String str) =>
    RequestModel.fromJson(json.decode(str));

String requestModelToJson(RequestModel data) => json.encode(data.toJson());

class RequestModel extends RequestEntity {
  const RequestModel({
    String? id,
    CenterData? centerId,
    CoachModel? coachId,
    String? status,
    DateTime? dateRequested,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? v,
  }) : super(
          id: id,
          centerId: centerId,
          coachId: coachId,
          status: status,
          dateRequested: dateRequested,
          createdAt: createdAt,
          updatedAt: updatedAt,
          v: v,
        );

  factory RequestModel.fromJson(Map<String, dynamic> json) {
    return RequestModel(
      id: json["_id"],
      centerId: json["centerId"] is String
          ? CenterData(id: json['center'])
          : json["centerId"] is Map<String, dynamic>
              ? CenterData.fromJson(json["centerId"])
              : null,
      coachId: json["coachId"] is String
          ? CoachModel(id: json['coach'])
          : json["coachId"] is Map<String, dynamic>
              ? CoachModel.fromJson(json["coachId"])
              : null,
      status: json["status"],
      dateRequested: json["dateRequested"] == null
          ? null
          : DateTime.parse(json["dateRequested"]),
      createdAt:
          json["createdAt"] == null ? null : DateTime.parse(json["createdAt"]),
      updatedAt:
          json["updatedAt"] == null ? null : DateTime.parse(json["updatedAt"]),
      v: json["__v"],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "_id": id,
      "centerId": centerId,
      "coachId": coachId,
      "status": status,
      "dateRequested": dateRequested?.toIso8601String(),
      "createdAt": createdAt?.toIso8601String(),
      "updatedAt": updatedAt?.toIso8601String(),
      "__v": v,
    };
  }
}
