import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:class_z/core/common/domain/entities/campaign_entity.dart';

CampaignModel campaignModelFromJson(String str) =>
    CampaignModel.fromJson(json.decode(str));

String campaignModelToJson(CampaignModel data) => json.encode(data.toJson());

class CampaignModel extends CampaignEntity {
  const CampaignModel({
    String? id,
    String? title,
    String? subtitle,
    String? description,
    String? imageUrl,
    String? backgroundColor,
    String? textColor,
    int? discountPercentage,
    String? discountCode,
    DateTime? validFrom,
    DateTime? validUntil,
    bool? isActive,
    int? priority,
    String? targetAudience,
    String? actionType,
    Map<String, dynamic>? actionData,
    int? clickCount,
    int? impressionCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : super(
          id: id,
          title: title,
          subtitle: subtitle,
          description: description,
          imageUrl: imageUrl,
          backgroundColor: backgroundColor,
          textColor: textColor,
          discountPercentage: discountPercentage,
          discountCode: discountCode,
          validFrom: validFrom,
          validUntil: validUntil,
          isActive: isActive,
          priority: priority,
          targetAudience: targetAudience,
          actionType: actionType,
          actionData: actionData,
          clickCount: clickCount,
          impressionCount: impressionCount,
          createdAt: createdAt,
          updatedAt: updatedAt,
        );

  factory CampaignModel.fromJson(Map<String, dynamic> json) => CampaignModel(
        id: json["_id"],
        title: json["title"],
        subtitle: json["subtitle"],
        description: json["description"],
        imageUrl: json["imageUrl"],
        backgroundColor: json["backgroundColor"],
        textColor: json["textColor"],
        discountPercentage: json["discountPercentage"],
        discountCode: json["discountCode"],
        validFrom: json["validFrom"] == null
            ? null
            : DateTime.parse(json["validFrom"]),
        validUntil: json["validUntil"] == null
            ? null
            : DateTime.parse(json["validUntil"]),
        isActive: json["isActive"],
        priority: json["priority"],
        targetAudience: json["targetAudience"],
        actionType: json["actionType"],
        actionData: json["actionData"] == null
            ? null
            : Map<String, dynamic>.from(json["actionData"]),
        clickCount: json["clickCount"],
        impressionCount: json["impressionCount"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
      );

  factory CampaignModel.fromEntity(CampaignEntity entity) => CampaignModel(
        id: entity.id,
        title: entity.title,
        subtitle: entity.subtitle,
        description: entity.description,
        imageUrl: entity.imageUrl,
        backgroundColor: entity.backgroundColor,
        textColor: entity.textColor,
        discountPercentage: entity.discountPercentage,
        discountCode: entity.discountCode,
        validFrom: entity.validFrom,
        validUntil: entity.validUntil,
        isActive: entity.isActive,
        priority: entity.priority,
        targetAudience: entity.targetAudience,
        actionType: entity.actionType,
        actionData: entity.actionData,
        clickCount: entity.clickCount,
        impressionCount: entity.impressionCount,
        createdAt: entity.createdAt,
        updatedAt: entity.updatedAt,
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "title": title,
        "subtitle": subtitle,
        "description": description,
        "imageUrl": imageUrl,
        "backgroundColor": backgroundColor,
        "textColor": textColor,
        "discountPercentage": discountPercentage,
        "discountCode": discountCode,
        "validFrom": validFrom?.toIso8601String(),
        "validUntil": validUntil?.toIso8601String(),
        "isActive": isActive,
        "priority": priority,
        "targetAudience": targetAudience,
        "actionType": actionType,
        "actionData": actionData,
        "clickCount": clickCount,
        "impressionCount": impressionCount,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
      };

  // Helper method to get gradient colors
  List<Color> getGradientColors() {
    try {
      final baseColor = Color(
          int.parse(backgroundColor?.replaceAll('#', '0xFF') ?? '0xFFFF6B6B'));
      return [
        baseColor.withOpacity(0.8),
        baseColor.withOpacity(0.6),
        baseColor.withOpacity(0.4),
      ];
    } catch (e) {
      // Fallback colors
      return [
        const Color(0xFFFF6B6B).withOpacity(0.8),
        const Color(0xFFFF8E8E).withOpacity(0.6),
        const Color(0xFFFFB3B3).withOpacity(0.4),
      ];
    }
  }

  // Helper method to get text color
  Color getTextColor() {
    try {
      return Color(
          int.parse(textColor?.replaceAll('#', '0xFF') ?? '0xFFFFFFFF'));
    } catch (e) {
      return const Color(0xFFFFFFFF); // Fallback to white
    }
  }

  // Helper method to check if campaign is valid
  bool get isValid {
    final now = DateTime.now();
    return isActive == true &&
        validFrom != null &&
        validUntil != null &&
        validFrom!.isBefore(now) &&
        validUntil!.isAfter(now);
  }

  // Helper method to format discount
  String get formattedDiscount {
    if (discountPercentage != null && discountPercentage! > 0) {
      return "From ${discountPercentage}% off";
    }
    return "";
  }
}
