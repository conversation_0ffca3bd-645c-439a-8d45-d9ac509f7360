// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_address_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserAddressAdapter extends TypeAdapter<UserAddress> {
  @override
  final int typeId = 101;

  @override
  UserAddress read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserAddress(
      flatFloorBlock: fields[0] as String?,
      buildingEstate: fields[1] as String?,
      district: fields[2] as String?,
      region: fields[3] as String?,
      country: fields[4] as String?,
      userAddressDefault: fields[5] as bool?,
      userAddressId: fields[6] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, UserAddress obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.flatFloorBlock)
      ..writeByte(1)
      ..write(obj.buildingEstate)
      ..writeByte(2)
      ..write(obj.district)
      ..writeByte(3)
      ..write(obj.region)
      ..writeByte(4)
      ..write(obj.country)
      ..writeByte(5)
      ..write(obj.userAddressDefault)
      ..writeByte(6)
      ..write(obj.userAddressId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserAddressAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
