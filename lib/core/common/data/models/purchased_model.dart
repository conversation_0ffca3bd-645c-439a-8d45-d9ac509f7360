// To parse this JSON data, do
//
//     final purchasedHistoryModel = purchasedHistoryModelFromJson(jsonString);



import 'package:class_z/core/imports.dart';

List<PurchasedHistoryModel> purchasedHistoryModelFromJson(String str) =>
    List<PurchasedHistoryModel>.from(
        json.decode(str).map((x) => PurchasedHistoryModel.fromJson(x)));

String purchasedHistoryModelToJson(List<PurchasedHistoryModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class PurchasedHistoryModel {
  String? id;
  String? user;
  Order? order;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? v;

  PurchasedHistoryModel({
    this.id,
    this.user,
    this.order,
    this.createdAt,
    this.updatedAt,
    this.v,
  });

  factory PurchasedHistoryModel.fromJson(Map<String, dynamic> json) =>
      PurchasedHistoryModel(
        id: json["_id"],
        user: json["user"],
        order: json["order"] == null ? null : Order.fromJson(json["order"]),
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "user": user,
        "order": order?.toJson(),
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
      };
}

class Order {
  String? id;
  ClassModel? classId;
  int? amount;
  int? quantity;

  Order({
    this.id,
    this.classId,
    this.amount,
    this.quantity,
  });

  factory Order.fromJson(Map<String, dynamic> json) => Order(
        id: json["_id"],
        classId: json["classId"] == null
            ? null
            : ClassModel.fromJson(json["classId"]),
        amount: json["amount"],
        quantity: json["quantity"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "classId": classId?.toJson(),
        "amount": amount,
        "quantity": quantity,
      };
}
