// To parse this JSON data, do
//
//     final userAddress = userAddressFromJson(jsonString);

import 'dart:convert';

import 'package:hive/hive.dart';
part 'user_address_model.g.dart';

UserAddress userAddressFromJson(String str) =>
    UserAddress.fromJson(json.decode(str));

String userAddressToJson(UserAddress data) => json.encode(data.toJson());

@HiveType(typeId: 101)
class UserAddress {
  @HiveField(0)
  String? flatFloorBlock;
  @HiveField(1)
  String? buildingEstate;
  @HiveField(2)
  String? district;
  @HiveField(3)
  String? region;
  @HiveField(4)
  String? country;
  @HiveField(5)
  bool? userAddressDefault;
  @HiveField(6)
  String? userAddressId;
  UserAddress({
    this.flatFloorBlock,
    this.buildingEstate,
    this.district,
    this.region,
    this.country,
    this.userAddressDefault,
    this.userAddressId,
  });

  factory UserAddress.fromJson(Map<String, dynamic> json) => UserAddress(
        flatFloorBlock: json["flatFloorBlock"],
        buildingEstate: json["buildingEstate"],
        district: json["district"],
        region: json["region"],
        country: json["country"],
        userAddressDefault: json["default"],
        userAddressId: json["_id"]
      );

  Map<String, dynamic> toJson() => {
        "flatFloorBlock": flatFloorBlock,
        "buildingEstate": buildingEstate,
        "district": district,
        "region": region,
        "country": country,
        "default": userAddressDefault,
        "_id":userAddressId
      };
}
