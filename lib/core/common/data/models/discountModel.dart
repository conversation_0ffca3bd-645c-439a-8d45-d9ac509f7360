// To parse this JSON data, do
//
//     final discountModel = discountModelFromJson(jsonString);

import 'dart:convert';

import 'package:class_z/core/common/domain/entities/discount_entity.dart';

List<DiscountModel> discountModelFromJson(String str) =>
    List<DiscountModel>.from(
        json.decode(str).map((x) => DiscountModel.fromJson(x)));

String discountModelToJson(List<DiscountModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class DiscountModel extends DiscountEntity {
  String? id;
  String? code;
  int? discountPercentage;
  String? discountType;
  DateTime? validUntil;
  bool? userSpecific;
  List<String>? allowedUsers;
  bool? isActive;
  int? maxUsage;
  int? usageCount;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? v;

  DiscountModel({
    this.id,
    this.code,
    this.discountPercentage,
    this.discountType,
    this.validUntil,
    this.userSpecific,
    this.allowedUsers,
    this.isActive,
    this.maxUsage,
    this.usageCount,
    this.createdAt,
    this.updatedAt,
    this.v,
  });

  factory DiscountModel.fromJson(Map<String, dynamic> json) => DiscountModel(
        id: json["_id"],
        code: json["code"],
        discountPercentage: json["discountPercentage"],
        discountType: json["discountType"],
        validUntil: json["validUntil"] == null
            ? null
            : DateTime.parse(json["validUntil"]),
        userSpecific: json["userSpecific"],
        allowedUsers: json["allowedUsers"] == null
            ? []
            : List<String>.from(json["allowedUsers"]!.map((x) => x)),
        isActive: json["isActive"],
        maxUsage: json["maxUsage"],
        usageCount: json["usageCount"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "code": code,
        "discountPercentage": discountPercentage,
        "discountType": discountType,
        "validUntil": validUntil?.toIso8601String(),
        "userSpecific": userSpecific,
        "allowedUsers": allowedUsers == null
            ? []
            : List<dynamic>.from(allowedUsers!.map((x) => x)),
        "isActive": isActive,
        "maxUsage": maxUsage,
        "usageCount": usageCount,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
      };
}
