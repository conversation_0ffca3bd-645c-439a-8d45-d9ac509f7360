import 'package:class_z/core/imports.dart';

class PendingModel extends PendingEntity {
  ChildModel? studentId;
  ClassModel? plainClassDetails;
  EventModel? event;
  dynamic student;

  PendingModel({
    this.studentId,
    this.plainClassDetails,
    this.event,
    this.student,
  });

  factory PendingModel.fromJson(Map<String, dynamic> json) => PendingModel(
        studentId: json["student"] == null
            ? null
            : ChildModel.fromJson(json['student']),
        plainClassDetails: json["plainClassDetails"] == null
            ? null
            : ClassModel.fromJson(json["plainClassDetails"]),
        event:
            json["event"] == null ? null : EventModel.fromJson(json["event"]),
        student: json["student"],
      );

  Map<String, dynamic> toJson() => {
        "studentId": studentId,
        "plainClassDetails": plainClassDetails?.toJson(),
        "event": event?.toJson(),
      };
}
