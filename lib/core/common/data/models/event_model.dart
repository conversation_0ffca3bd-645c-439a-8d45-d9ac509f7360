// To parse this JSON data, do
//
//     final eventModel = eventModelFromJson(jsonString);

import 'package:class_z/core/common/data/models/classDate_model.dart';
import 'package:class_z/core/imports.dart';

List<EventModel> eventModelFromJson(String str) =>
    List<EventModel>.from(json.decode(str).map((x) => EventModel.fromJson(x)));

String eventModelToJson(List<EventModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class EventModel {
  String? id;
  String? title;
  String? description;
  DateTime? date;
  String? startTime;
  String? endTime;
  String? durationMinutes;
  dynamic dateId; // Can be String or ClassDate
  String? centerId;
  CoachModel? coach;
  ClassModel? classId;
  DateTime? createdAt;
  int? v;

  // Schedule-specific fields from dateId
  int? scheduleNumberOfStudent;
  int? scheduleCharge;
  List<String>? scheduleLanguageOptions;
  int? scheduleMinimumStudent;

  EventModel({
    this.id,
    this.title,
    this.description,
    this.date,
    this.startTime,
    this.endTime,
    this.durationMinutes,
    this.dateId, // Can be String or ClassDate
    this.centerId,
    this.classId,
    this.coach,
    this.createdAt,
    this.v,
    this.scheduleNumberOfStudent,
    this.scheduleCharge,
    this.scheduleLanguageOptions,
    this.scheduleMinimumStudent,
  });

  /// Returns dateId as ClassDate if possible, else null
  ClassDate? get dateIdClassDate =>
      dateId is ClassDate ? dateId as ClassDate : null;

  factory EventModel.fromJson(Map<String, dynamic> json) {
    // Extract time information from populated dateId if available
    String? extractedStartTime = json["startTime"];
    String? extractedEndTime = json["endTime"];
    String? extractedDurationMinutes = json["durationMinutes"];
    dynamic extractedDateId;

    // Schedule-specific fields
    int? extractedScheduleNumberOfStudent;
    int? extractedScheduleCharge;
    List<String>? extractedScheduleLanguageOptions;
    int? extractedScheduleMinimumStudent;

    // Handle dateId - it can be either a string ID or a populated object
    if (json['dateId'] is String) {
      // dateId is just an ID string
      extractedDateId = json['dateId'];
    } else if (json['dateId'] is Map<String, dynamic>) {
      // dateId is a populated object with time and schedule-specific information
      final dateIdData = json['dateId'] as Map<String, dynamic>;
      extractedStartTime ??= dateIdData['startTime'];
      extractedEndTime ??= dateIdData['endTime'];
      extractedDurationMinutes ??= dateIdData['durationMinutes'];
      extractedDateId = ClassDate.fromJson(dateIdData);

      // Extract schedule-specific fields
      extractedScheduleNumberOfStudent = dateIdData['numberOfStudent'];
      extractedScheduleCharge = dateIdData['charge'];
      extractedScheduleMinimumStudent = dateIdData['minimumStudent'];

      if (dateIdData['languageOptions'] is List) {
        extractedScheduleLanguageOptions =
            (dateIdData['languageOptions'] as List)
                .map((e) => e.toString())
                .toList();
      }

      print(
          '🔍 DEBUG EventModel: Extracted schedule data - numberOfStudent: $extractedScheduleNumberOfStudent, charge: $extractedScheduleCharge, languages: $extractedScheduleLanguageOptions');
    } else {
      // dateId is null or some other type
      extractedDateId = null;
    }

    // Handle coach information more robustly
    CoachModel? extractedCoach;
    try {
      if (json["coachId"] != null) {
        if (json["coachId"] is Map<String, dynamic>) {
          extractedCoach = CoachModel.fromJson(json["coachId"]);
          print(
              '🔍 DEBUG EventModel: Successfully parsed coach - ${extractedCoach.displayName}');
        } else if (json["coachId"] is String) {
          // If coachId is just a string ID, create a minimal CoachModel
          extractedCoach = CoachModel(id: json["coachId"]);
          print(
              '🔍 DEBUG EventModel: Coach is just an ID string - ${json["coachId"]}');
        }
      }
    } catch (e) {
      print('🚨 ERROR EventModel: Failed to parse coach data: $e');
      extractedCoach = null;
    }

    return EventModel(
      id: json["_id"],
      title: json["title"],
      description: json["description"],
      date: json["date"] != null ? DateTime.parse(json["date"]) : null,
      startTime: extractedStartTime,
      endTime: extractedEndTime,
      durationMinutes: extractedDurationMinutes,
      dateId: extractedDateId, // Can be String or ClassDate
      centerId: json["centerId"],
      classId:
          json["classId"] != null ? ClassModel.fromJson(json["classId"]) : null,
      coach: extractedCoach,
      createdAt:
          json["createdAt"] != null ? DateTime.parse(json["createdAt"]) : null,
      v: json["__v"],
      scheduleNumberOfStudent: extractedScheduleNumberOfStudent,
      scheduleCharge: extractedScheduleCharge,
      scheduleLanguageOptions: extractedScheduleLanguageOptions,
      scheduleMinimumStudent: extractedScheduleMinimumStudent,
    );
  }

  Map<String, dynamic> toJson() => {
        "_id": id,
        "title": title,
        "description": description,
        "date": date?.toIso8601String(),
        "startTime": startTime,
        "endTime": endTime,
        "durationMinutes": durationMinutes,
        "coachId": coach?.toJson(),
        "dateId": dateId is ClassDate ? (dateId as ClassDate).toJson() : dateId,
        "centerId": centerId,
        "classId": classId?.toJson(),
        "createdAt": createdAt?.toIso8601String(),
        "__v": v,
      };
}
