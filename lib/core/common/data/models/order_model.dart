import 'package:class_z/core/imports.dart';

List<OrderModel> orderModelFromJson(String str) =>
    List<OrderModel>.from(json.decode(str).map((x) => OrderModel.fromJson(x)));

String orderModelToJson(List<OrderModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class OrderModel {
  String? id;
  String? userId;
  ClassModel? classId;
  ChildModel? childId;
  bool? course;
  bool? paid;
  bool? sen;
  int? discount;
  int? amount; // Added
  int? quantity; // Added
  List<DateTime>? date;
  String? startTime; // Added
  String? endTime; // Added
  String? durationMinutes; // Added
  DateTime? createdAt;
  DateTime? updatedAt;
  int? v;

  OrderModel({
    this.id,
    this.userId,
    this.classId,
    this.childId,
    this.course,
    this.paid,
    this.sen,
    this.discount,
    this.amount, // Added
    this.quantity, // Added
    this.date,
    this.startTime, // Added
    this.endTime, // Added
    this.durationMinutes, // Added
    this.createdAt,
    this.updatedAt,
    this.v,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) => OrderModel(
        id: json["_id"],
        userId: json["userId"],
        classId: json["classId"] == null
            ? null
            : ClassModel.fromJson(json["classId"]),
        childId:
            json["childId"] == null ? null : ChildModel.fromJson(json["childId"]),
        course: json["course"],
        paid: json["paid"],
        sen: json["sen"],
        discount: json["Discount"],
        amount: json["amount"], // Added
        quantity: json["quantity"], // Added
        date: json["date"] == null
            ? []
            : List<DateTime>.from(json["date"].map((x) => DateTime.parse(x))),
        startTime: json["startTime"], // Added
        endTime: json["endTime"], // Added
        durationMinutes: json["durationMinutes"], // Added
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "userId": userId,
        "classId": classId?.toJson(),
        "childId": childId?.toJson(),
        "course": course,
        "paid": paid,
        "sen": sen,
        "discount": discount,
        "amount": amount, // Added
        "quantity": quantity, // Added
        "date": date == null
            ? []
            : List<dynamic>.from(date!.map((x) => x.toString())),
        "startTime": startTime, // Added
        "endTime": endTime, // Added
        "durationMinutes": durationMinutes, // Added
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
      };
}


