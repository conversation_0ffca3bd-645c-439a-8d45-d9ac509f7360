// To parse this JSON data, do
//
//     final searchModel = searchModelFromJson(jsonString);



import '../../../imports.dart';


SearchModel searchModelFromJson(String str) =>
    SearchModel.fromJson(json.decode(str));

String searchModelToJson(SearchModel data) => json.encode(data.toJson());

class SearchModel {
  List<ClassModel>? classes;
  List<CenterData>? centers;
  List<CoachModel>? coaches;
  SearchModel({this.classes, this.centers, this.coaches});

  factory SearchModel.fromJson(Map<String, dynamic> json) => SearchModel(
        classes: json["classes"] == null
            ? []
            : List<ClassModel>.from(
                json["classes"]!.map((x) => ClassModel.fromJson(x))),
        centers: json["centers"] == null
            ? []
            : List<CenterData>.from(
                json["centers"]!.map((x) => CenterData.fromJson(x))),
        coaches: json["coaches"] == null
            ? []
            : List<CoachModel>.from(
                json["coaches"].map((x) => CoachModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "classes": classes == null
            ? []
            : List<dynamic>.from(classes!.map((x) => x.toJson())),
        "centers": centers == null
            ? []
            : List<dynamic>.from(centers!.map((x) => x.toJson())),
        "coaches": coaches == null
            ? []
            : List<dynamic>.from(coaches!.map((x) => x.toJson())),
      };
}
