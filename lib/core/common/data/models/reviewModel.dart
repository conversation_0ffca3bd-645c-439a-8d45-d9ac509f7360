// To parse this JSON data, do
//
//     final reviewModel = reviewModelFromJson(jsonString);

import '../../../imports.dart';

ReviewModel reviewModelFromJson(String str) =>
    ReviewModel.fromJson(json.decode(str));

String reviewModelToJson(ReviewModel data) => json.encode(data.toJson());

class ReviewModel {
  Questions? questions;
  String? title;
  String? id;
  String? reviewerId;
  String? reviewerType;
  String? classId;
  String? revieweeId;
  String? revieweeType;
  double? rating;
  String? comment;
  String? topic;
  DateTime? date;
  List<CenterImage>? images;
  int? v;

  ReviewModel(
      {this.questions,
      this.title,
      this.id,
      this.reviewerId,
      this.reviewerType,
      this.classId,
      this.revieweeId,
      this.revieweeType,
      this.rating,
      this.comment,
      this.topic,
      this.date,
      this.v,
      this.images});

  factory ReviewModel.fromJson(Map<String, dynamic> json) => ReviewModel(
        questions: json["questions"] == null
            ? null
            : Questions.fromJson(json["questions"]),
        title: json["title"],
        id: json["_id"],
        reviewerId: json["reviewerId"],
        reviewerType: json["reviewerType"],
        classId: json["classId"] != null && json["classId"] is Map
            ? json["classId"]["_id"] as String?
            : null,
        revieweeId: json["revieweeId"],
        revieweeType: json["revieweeType"],
        rating: json["rating"]?.toDouble(),
        comment: json["comment"],
        topic: json["topic"],
        date: json["date"] == null ? null : DateTime.parse(json["date"]),
        v: json["__v"],
        images: json['images'] != null
            ? List<CenterImage>.from(
                json['images'].map((x) => CenterImage.fromJson(x)))
            : null,
      );

  Map<String, dynamic> toJson() => {
        "questions": questions?.toJson(),
        "title": title,
        "_id": id,
        "reviewerId": reviewerId,
        "reviewerType": reviewerType,
        "classId": classId,
        "revieweeId": revieweeId,
        "revieweeType": revieweeType,
        "rating": rating,
        "comment": comment,
        "topic": topic,
        "date": date?.toIso8601String(),
        'images': images?.map((x) => x.toJson()).toList(),
        "__v": v,
      };
}

// class Questions {
//   double? q1;
//   double? q2;
//   double? q3;
//   double? q4;
//   double? q5;
//   double? q6;

//   Questions({
//     this.q1,
//     this.q2,
//     this.q3,
//     this.q4,
//     this.q5,
//     this.q6,
//   });

//   factory Questions.fromJson(Map<String, dynamic> json) => Questions(
//         q1: json["q1"] == null ? 0 : json["q1"],
//         q2: json["q2"] == null ? 0 : json["q2"],
//         q3: json["q3"] == null ? 0 : json["q3"],
//         q4: json["q4"] == null ? 0 : json["q4"],
//         q5: json["q5"] == null ? 0 : json["q5"],
//         q6: json["q6"] == null ? 0 : json["q6"],
//       );

//   Map<String, dynamic> toJson() => {
//         "q1": q1,
//         "q2": q2,
//         "q3": q3,
//         "q4": q4,
//         "q5": q5,
//         "q6": q6,
//       };
// }
