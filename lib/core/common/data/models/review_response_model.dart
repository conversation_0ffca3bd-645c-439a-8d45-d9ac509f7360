import 'package:class_z/core/common/data/models/review_of_child.dart';
import 'package:equatable/equatable.dart';

class ReviewResponseModel extends Equatable {
  final List<ReviewOfChildModel> reviews;
  final String? bestQuestion;
  final String? worstQuestion;

  const ReviewResponseModel({
    required this.reviews,
    required this.bestQuestion,
    required this.worstQuestion,
  });

  @override
  List<Object?> get props => [reviews, bestQuestion, worstQuestion];

  factory ReviewResponseModel.fromJson(Map<String, dynamic> json) {
    return ReviewResponseModel(
      reviews: (json['reviews'] as List)
          .map((e) => ReviewOfChildModel.fromJson(e))
          .toList(),
      bestQuestion: json['bestQuestion'],
      worstQuestion: json['worstQuestion'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'reviews': reviews.map((e) => e.toJson()).toList(),
      'bestQuestion': bestQuestion,
      'worstQuestion': worstQuestion,
    };
  }
}
