// To parse this JSON data, do
//
//     final refundModel = refundModelFromJson(jsonString);

import 'dart:convert';

import 'package:class_z/core/common/data/models/class_model.dart';

RefundModel refundModelFromJson(String str) =>
    RefundModel.fromJson(json.decode(str));

String refundModelToJson(RefundModel data) => json.encode(data.toJson());

class RefundModel {
  String? id;
  String? studentId;
  String? classId;
  String? eventId;
  int? amount;
  String? status;
  DateTime? processedAt;
  String? notes;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? v;
  String? parentId;
  DateTime? date;
  ClassModel? classInfo;

  RefundModel(
      {this.id,
      this.studentId,
      this.classId,
      this.eventId,
      this.amount,
      this.status,
      this.processedAt,
      this.notes,
      this.createdAt,
      this.updatedAt,
      this.v,
      this.parentId,
      this.date,
      this.classInfo});

  factory RefundModel.fromJson(Map<String, dynamic> json) => RefundModel(
        id: json["_id"],
        studentId: json["studentId"],
        classId: json["classId"],
        eventId: json["eventId"],
        amount: json["amount"],
        status: json["status"],
        processedAt: json["processedAt"] == null
            ? null
            : DateTime.parse(json["processedAt"]),
        notes: json["notes"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        updatedAt: json["updatedAt"] == null
            ? null
            : DateTime.parse(json["updatedAt"]),
        v: json["__v"],
        parentId: json["parentId"],
        date: json["date"] == null ? null : DateTime.parse(json["date"]),
        classInfo: json["classInfo"] == null
            ? null
            : ClassModel.fromJson(json["classInfo"]),
      );

  Map<String, dynamic> toJson() => {
        "_id": id,
        "studentId": studentId,
        "classId": classId,
        "eventId": eventId,
        "amount": amount,
        "status": status,
        "processedAt": processedAt?.toIso8601String(),
        "notes": notes,
        "createdAt": createdAt?.toIso8601String(),
        "updatedAt": updatedAt?.toIso8601String(),
        "__v": v,
        "parentId": parentId,
        "date": date?.toIso8601String(),
        "classInfo": classInfo?.toJson(),
      };
}
