import 'dart:convert';
import 'package:class_z/features/roles/center/data/models/center_model.dart';
import 'package:class_z/features/roles/coach/data/models/coach_model.dart';
import 'package:class_z/features/roles/owner/data/models/ownerModel.dart';
import 'package:class_z/features/roles/parent/data/models/parent_model.dart';
import 'package:hive/hive.dart';
import 'package:class_z/core/common/domain/entities/auth_response.dart';

part 'user_model.g.dart'; // Generated file

UserModel userModelFromJson(String str) => UserModel.fromJson(json.decode(str));
String userModelToJson(UserModel data) => json.encode(data.toJson());

@HiveType(typeId: 0)
class UserModel extends HiveObject implements AuthResponse {
  @HiveField(0)
  String token;
  @HiveField(1)
  Data? data;

  UserModel({required this.token, required this.data});

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        token: json["token"],
        data: json["data"] != null
            ? Data.fromJson(json["data"] as Map<String, dynamic>)
            : null,
      );

  Map<String, dynamic> toJson() => {"token": token, "data": data!.toJson()};

  // @override
  // String toString() {
  //   // Logging removed as requested
  // }

  @override
  // TODO: implement email
  String get email => throw UnimplementedError();

  @override
  // TODO: implement roles
  List<String>? get roles => throw UnimplementedError();
}

@HiveType(typeId: 1)
class Data extends HiveObject {
  @HiveField(0)
  ParentData1? parent;
  @HiveField(1)
  OwnerModel? owner;
  @HiveField(2)
  CenterData? center;
  @HiveField(3)
  CoachModel? coach;

  Data({
    this.parent,
    this.owner,
    this.center,
    this.coach,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        parent: (json["parent"] != null && json["parent"].isNotEmpty)
            ? ParentData1.fromJson(json["parent"])
            : null,
        owner: (json["owner"] != null && json["owner"].isNotEmpty)
            ? OwnerModel.fromJson(json["owner"])
            : null,
        center: (json["center"] != null && json["center"].isNotEmpty)
            ? CenterData.fromJson(json["center"])
            : null,
        coach: (json["coach"] != null && json["coach"].isNotEmpty)
            ? CoachModel.fromJson(json["coach"])
            : null,
      );

  Map<String, dynamic> toJson() => {
        "parent": parent?.toJson(),
        "owner": owner?.toJson(),
        "center": center?.toJson(),
        "coach": coach?.toJson(),
      };
}
