// To parse this JSON data, do
//
//     final cardModel = cardModelFromJson(jsonString);

import 'dart:convert';
import 'dart:developer';

CardModel cardModelFromJson(String str) {
  try {
    final json = jsonDecode(str);
    return CardModel.fromJson(json);
  } catch (e) {
    log("Error parsing CardModel from JSON: $e");
    log("JSON string: $str");
    return CardModel();
  }
}

String cardModelToJson(CardModel data) => json.encode(data.toJson());

class CardModel {
  String? name;
  String? last4;
  String? expiryDate;
  String? brand;

  CardModel({
    this.name,
    this.last4,
    this.expiryDate,
    this.brand,
  });

  factory CardModel.fromJson(Map<String, dynamic> json) {
    try {
      final model = CardModel(
        name: json["name"],
        last4: json["last4"],
        expiryDate: json["expiry_date"],
        brand: json["brand"],
      );

      log("CardModel created: brand=${model.brand}, last4=${model.last4}, name=${model.name}, expiry=${model.expiryDate}");
      return model;
    } catch (e) {
      log("Error creating CardModel from JSON: $e");
      log("JSON data: $json");
      return CardModel();
    }
  }

  Map<String, dynamic> toJson() => {
        "name": name,
        "last4": last4,
        "expiry_date": expiryDate,
        "brand": brand,
      };

  bool get isValid => last4 != null && last4!.isNotEmpty;
}
