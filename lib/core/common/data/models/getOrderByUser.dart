import 'package:class_z/core/imports.dart';

List<GetOrderByUserModel> getOrderByUserFromJson(String str) =>
    List<GetOrderByUserModel>.from(
        json.decode(str).map((x) => GetOrderByUserModel.fromJson(x)));

String getOrderByUserModelToJson(List<GetOrderByUserModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class GetOrderByUserModel {
  DateTime? date;
  OrderModel? order;
  ClassModel? classs; // Corrected naming issues
  ChildModel? child;

  GetOrderByUserModel({
    this.date,
    this.order,
    this.classs,
    this.child,
  });

  factory GetOrderByUserModel.fromJson(Map<String, dynamic> json) =>
      GetOrderByUserModel(
        date: json["date"] == null ? null : DateTime.parse(json["date"]),
        order:
            json["order"] == null ? null : OrderModel.fromJson(json['order']),
        classs:
            json["class"] == null ? null : ClassModel.fromJson(json["class"]),
        child:
            json["child"] == null ? null : ChildModel.fromJson(json["child"]),
      );

  Map<String, dynamic> toJson() => {
        "date": date?.toIso8601String(),
        "orderId": order?.toJson(),
        "class": classs?.toJson(), // Correctly referencing 'classs'
        "child": child?.toJson(),
      };
}
