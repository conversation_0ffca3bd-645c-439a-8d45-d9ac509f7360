// To parse this JSON data, do
//
//     final attendanceModel = attendanceModelFromJson(jsonString);

import 'dart:convert';

AttendanceModel attendanceModelFromJson(String str) => AttendanceModel.fromJson(json.decode(str));

String attendanceModelToJson(AttendanceModel data) => json.encode(data.toJson());

class AttendanceModel {
    IsVerified? isVerified;

    AttendanceModel({
        this.isVerified,
    });

    factory AttendanceModel.fromJson(Map<String, dynamic> json) => AttendanceModel(
        isVerified: json["isVerified"] == null ? null : IsVerified.fromJson(json["isVerified"]),
    );

    Map<String, dynamic> toJson() => {
        "isVerified": isVerified?.toJson(),
    };
}

class IsVerified {
    String? attendanceStatus;
    String? studentImage;
    String? studentName;
    String? id;

    IsVerified({
        this.attendanceStatus,
        this.studentImage,
        this.studentName,
        this.id
    });

    factory IsVerified.fromJson(Map<String, dynamic> json) => IsVerified(
        attendanceStatus: json["attendanceStatus"],
        studentImage: json["studentImage"],
        studentName: json["studentName"],
        id:json["id"]
    );

    Map<String, dynamic> toJson() => {
        "attendanceStatus": attendanceStatus,
        "studentImage": studentImage,
        "studentName": studentName,
        "id":id
    };
}
