// To parse this JSON data, do
//
//     final timetableUpModel = timetableUpModelFromJson(jsonString);

import 'package:class_z/core/common/data/models/classDate_model.dart';
import 'package:class_z/core/imports.dart';

TimetableUpModel timetableUpModelFromJson(String str) =>
    TimetableUpModel.fromJson(json.decode(str));

String timetableUpModelToJson(TimetableUpModel data) =>
    json.encode(data.toJson());

class TimetableUpModel {
  bool? success;
  List<EventElement>? events;

  TimetableUpModel({
    this.success,
    this.events,
  });

  factory TimetableUpModel.fromJson(Map<String, dynamic> json) =>
      TimetableUpModel(
        success: json["success"],
        events: json["events"] == null
            ? []
            : List<EventElement>.from(
                json["events"]!.map((x) => EventElement.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "events": events == null
            ? []
            : List<dynamic>.from(events!.map((x) => x.toJson())),
      };
}

class EventElement {
  EventEvent? event;
  ChildModel? childId;

  EventElement({
    this.event,
    this.childId,
  });

  factory EventElement.fromJson(Map<String, dynamic> json) => EventElement(
        event:
            json["event"] == null ? null : EventEvent.fromJson(json["event"]),
        childId: json["childId"] == null
            ? null
            : ChildModel.fromJson(json['childId']),
      );

  Map<String, dynamic> toJson() => {
        "event": event?.toJson(),
        "childId": childId?.toJson(),
      };
}

class EventEvent {
  EventRearrangementInfo? rearrangementInfo;
  RefundInfo? refundInfo;
  String? id;
  DateTime? date;
  String? centerId;
  String? coachId;
  ClassModel? classId;
  ClassDate? dateId;
  String? status;
  dynamic cancellationType;
  dynamic cancelledAt;
  DateTime? createdAt;
  int? v;

  EventEvent({
    this.rearrangementInfo,
    this.refundInfo,
    this.id,
    this.date,
    this.centerId,
    this.coachId,
    this.classId,
    this.dateId,
    this.status,
    this.cancellationType,
    this.cancelledAt,
    this.createdAt,
    this.v,
  });

  factory EventEvent.fromJson(Map<String, dynamic> json) => EventEvent(
        rearrangementInfo: json["rearrangementInfo"] == null
            ? null
            : EventRearrangementInfo.fromJson(json["rearrangementInfo"]),
        refundInfo: json["refundInfo"] == null
            ? null
            : RefundInfo.fromJson(json["refundInfo"]),
        id: json["_id"],
        date: json["date"] == null ? null : DateTime.parse(json["date"]),
        centerId: json["centerId"],
        coachId: json["coachId"],
        classId: json["classId"] == null
            ? null
            : ClassModel.fromJson(json["classId"]),
        dateId:
            json["dateId"] == null ? null : ClassDate.fromJson(json['dateId']),
        status: json["status"],
        cancellationType: json["cancellationType"],
        cancelledAt: json["cancelledAt"],
        createdAt: json["createdAt"] == null
            ? null
            : DateTime.parse(json["createdAt"]),
        v: json["__v"],
      );

  Map<String, dynamic> toJson() => {
        "rearrangementInfo": rearrangementInfo?.toJson(),
        "refundInfo": refundInfo?.toJson(),
        "_id": id,
        "date": date?.toIso8601String(),
        "centerId": centerId,
        "coachId": coachId,
        "classId": classId?.toJson(),
        "dateId": dateId,
        "status": status,
        "cancellationType": cancellationType,
        "cancelledAt": cancelledAt,
        "createdAt": createdAt?.toIso8601String(),
        "__v": v,
      };
}

class ClassIdRearrangementInfo {
  bool? isRearranging;
  List<dynamic>? originalDates;
  List<dynamic>? newDates;
  bool? studentsNotified;

  ClassIdRearrangementInfo({
    this.isRearranging,
    this.originalDates,
    this.newDates,
    this.studentsNotified,
  });

  factory ClassIdRearrangementInfo.fromJson(Map<String, dynamic> json) =>
      ClassIdRearrangementInfo(
        isRearranging: json["isRearranging"],
        originalDates: json["originalDates"] == null
            ? []
            : List<dynamic>.from(json["originalDates"]!.map((x) => x)),
        newDates: json["newDates"] == null
            ? []
            : List<dynamic>.from(json["newDates"]!.map((x) => x)),
        studentsNotified: json["studentsNotified"],
      );

  Map<String, dynamic> toJson() => {
        "isRearranging": isRearranging,
        "originalDates": originalDates == null
            ? []
            : List<dynamic>.from(originalDates!.map((x) => x)),
        "newDates":
            newDates == null ? [] : List<dynamic>.from(newDates!.map((x) => x)),
        "studentsNotified": studentsNotified,
      };
}

class EventRearrangementInfo {
  String? status;
  DateTime? newDate;
  DateTime? originalDate;
  DateTime? processedAt;

  EventRearrangementInfo({
    this.status,
    this.newDate,
    this.originalDate,
    this.processedAt,
  });

  factory EventRearrangementInfo.fromJson(Map<String, dynamic> json) =>
      EventRearrangementInfo(
        status: json["status"],
        newDate:
            json["newDate"] == null ? null : DateTime.parse(json["newDate"]),
        originalDate: json["originalDate"] == null
            ? null
            : DateTime.parse(json["originalDate"]),
        processedAt: json["processedAt"] == null
            ? null
            : DateTime.parse(json["processedAt"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "newDate": newDate?.toIso8601String(),
        "originalDate": originalDate?.toIso8601String(),
        "processedAt": processedAt?.toIso8601String(),
      };
}

class RefundInfo {
  List<dynamic>? refundIds;
  String? status;

  RefundInfo({
    this.refundIds,
    this.status,
  });

  factory RefundInfo.fromJson(Map<String, dynamic> json) => RefundInfo(
        refundIds: json["refundIds"] == null
            ? []
            : List<dynamic>.from(json["refundIds"]!.map((x) => x)),
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "refundIds": refundIds == null
            ? []
            : List<dynamic>.from(refundIds!.map((x) => x)),
        "status": status,
      };
}
