import 'package:class_z/core/imports.dart';
import 'package:dartz/dartz.dart';

class OwnerRepoImpl extends OwnerRepoDomain {
  final OwnerDataSources _ownerDataSources;

  OwnerRepoImpl(this._ownerDataSources);
  @override
  Future<List<CenterData>> getBranchesByOwner(String ownerId) async {
    try {
      return await _ownerDataSources.getBranchesByOwner(ownerId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> updateOwner(
      {required String ownerId, required Map<String, dynamic> data}) async {
    try {
      return await _ownerDataSources.updateOwner(ownerId: ownerId, data: data);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> deleteBranch({required String branchId}) async {
    try {
      return await _ownerDataSources.deleteBranch(branchId: branchId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> updateBranch(
      {required String branchId, required Map<String, dynamic> data}) async {
    try {
      return await _ownerDataSources.updateBranch(
          branchId: branchId, data: data);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<Either<Failure, bool>> requestCoachToJoin(
      {required String centerId,
      required String coachId,
      required String type}) async {
    try {
      final response = await _ownerDataSources.requestCoachToJoin(
          centerId: centerId, coachId: coachId, type: type);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> removeCoach(
      {required String centerId,
      required String coachId,
      required String type}) async {
    // TODO: implement removeCoach
    try {
      final response = await _ownerDataSources.removeCoach(
          centerId: centerId, coachId: coachId, type: type);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  // @override
  // Future<Either<Failure, bool>> requestManagerToJoin(
  //     {required String centerId,required String managerId}) async {
  //   // TODO: implement assignManager
  //   try {
  //     final response =
  //         await _ownerDataSources.requestManagerToJoin(centerId: centerId, managerId: managerId);
  //     return Right(response);
  //   } on ServerException catch (e) {
  //     return Left(ServerFailure(message: e.toString()));
  //   }
  // }

  // @override
  // Future<Either<Failure, bool>> removeManager(
  //     {required String centerId,required String managerId}) async {
  //   // TODO: implement assignManger
  //   try {
  //     final response =
  //         await _ownerDataSources.removeManager(centerId: centerId, managerId: managerId);
  //     return Right(response);
  //   } on ServerException catch (e) {
  //     return Left(ServerFailure(message: e.toString()));
  //   }
  // }
}
