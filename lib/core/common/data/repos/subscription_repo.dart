import 'package:class_z/core/common/data/data_sources/subscription_data_sources.dart';
import 'package:class_z/core/common/data/models/subscriptionModel.dart';
import 'package:class_z/core/common/domain/repo_domain/subscription_repo_domain.dart';

class SubscriptionRepoImpl extends SubscriptionRepoDomain {
  final SubscriptionDataSources subscribeDataSources;

  SubscriptionRepoImpl({required this.subscribeDataSources});
  @override
  Future<SubscriptionModel> getSubscription() async {
    try {
      return await this.subscribeDataSources.getSubscription();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<bool> cancelSubscription() async {
    try {
      return await this.subscribeDataSources.cancelSubscription();
    } catch (e) {
      rethrow;
    }
  }
}
