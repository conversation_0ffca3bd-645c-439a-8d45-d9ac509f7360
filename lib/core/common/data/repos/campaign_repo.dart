import 'package:class_z/core/error/exception.dart';
import 'package:class_z/core/error/failure.dart';
import 'package:class_z/core/common/data/data_sources/campaign_data_source.dart';
import 'package:class_z/core/common/data/models/campaign_model.dart';
import 'package:class_z/core/common/domain/entities/campaign_entity.dart';
import 'package:class_z/core/common/domain/repo_domain/campaign_repo_domain.dart';
import 'package:dartz/dartz.dart';

class CampaignRepoImpl extends ICampaignRepository {
  final CampaignDataSource _campaignDataSource;

  CampaignRepoImpl(this._campaignDataSource);

  @override
  Future<Either<Failure, List<CampaignEntity>>> getActiveCampaigns() async {
    try {
      List<CampaignModel> campaignsModel =
          await _campaignDataSource.getActiveCampaigns();
      List<CampaignEntity> campaigns = campaignsModel;
      return Right(campaigns);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    } catch (e) {
      return Left(
          ServerFailure(message: "Failed to fetch campaigns: ${e.toString()}"));
    }
  }

  @override
  Future<Either<Failure, bool>> recordCampaignClick(String campaignId) async {
    try {
      bool success = await _campaignDataSource.recordCampaignClick(campaignId);
      return Right(success);
    } catch (e) {
      return Left(ServerFailure(
          message: "Failed to record campaign click: ${e.toString()}"));
    }
  }
}
