// lib/shared/data/repositories/shared_repository_impl.dart

import 'package:class_z/core/common/data/models/user_model.dart';
import 'package:class_z/core/common/domain/repo_domain/shared_repo_domain.dart';
import 'package:class_z/features/roles/coach/data/models/coach_model.dart';
import 'package:hive_flutter/hive_flutter.dart';

class SharedRepositoryImpl implements SharedRepositoryDomain {
  final String userBoxName;
  final String coachBoxName;

  SharedRepositoryImpl({
    required this.userBoxName,
    required this.coachBoxName,
  });

  @override
  Future<void> storeUser(Data data) async {
    final box = await Hive.openBox<Data>(userBoxName);
    await box.put('currentUser', data);
  }

  @override
  Data? getUser() {
    final box = Hive.box<Data>(userBoxName);
    return box.get('currentUser');
  }

  @override
  Future<void> storeCoach(CoachModel coach) async {
    final box = await Hive.openBox<CoachModel>(coachBoxName);
    await box.put('currentCoach', coach);
  }

  @override
  CoachModel? getCoach() {
    final box = Hive.box<CoachModel>(coachBoxName);
    return box.get('currentCoach');
  }

  @override
  Future<void> deleteUser() async {
    final box = await Hive.openBox<Data>(userBoxName);
    await box.delete('currentUser');
  }

  @override
  Future<void> deleteCoach() async {
    final box = await Hive.openBox<CoachModel>(coachBoxName);
    await box.delete('currentCoach');
  }
}
