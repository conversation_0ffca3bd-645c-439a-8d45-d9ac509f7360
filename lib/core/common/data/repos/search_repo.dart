import 'package:class_z/core/imports.dart';


class SearchRepoImpl extends SearchRepoDomain {
  final SearchDataSources _searchDataSources;

  SearchRepoImpl(this._searchDataSources);
  @override
  Future<SearchModel> getSearchResult(
      String query, int? ageFrom, int? ageTo, String? location, String? sortBy, {double? rating, bool? senService,bool? isSearchingCoach}) async {
    return await _searchDataSources.getSearchResult(
        query, ageFrom, ageTo, location, sortBy, rating: rating, senService: senService, isSearchingCoach: isSearchingCoach); 
  }

  @override
  Future<SearchModel> getCentersByCategory({
    String? query,
    int? priceMax,
    int? priceMin,
    int? ageFrom,
    int? ageTo,
    String? location,
    String? sortBy,
    double? rating,
    bool? senService
  }) async {
    return await _searchDataSources.getCentersByCategory(
      query: query,
      priceMax: priceMax,
      priceMin: priceMin,
      ageFrom: ageFrom,
      ageTo: ageTo,
      location: location,
      sortBy: sortBy,
      rating: rating,
      senService: senService
    );
  }

  @override
  Future<SearchModel> getNearbyCenters({
    required double longitude,
    required double latitude,
    double? maxDistance,
    int? priceMin,
    int? priceMax,
    int? ageFrom,
    int? ageTo,
    String? sortBy,
    double? rating,
    bool? senService
  }) async {
    return await _searchDataSources.getNearbyCenters(
      longitude: longitude,
      latitude: latitude,
      maxDistance: maxDistance,
      priceMin: priceMin,
      priceMax: priceMax,
      ageFrom: ageFrom,
      ageTo: ageTo,
      sortBy: sortBy,
      rating: rating,
      senService: senService
    );
  }
}
