import 'package:class_z/core/error/exception.dart';
import 'package:class_z/core/error/failure.dart';
import 'package:class_z/core/common/data/data_sources/request_data_sources.dart';
import 'package:class_z/core/common/data/models/request_model.dart';
import 'package:class_z/core/common/domain/entities/request_entity.dart';
import 'package:class_z/core/common/domain/repo_domain/request_repo_domain.dart';
import 'package:dartz/dartz.dart';

class RequestRepoImpl extends RequestRepository {
  final RequestDataSources _requestDataSources;

  RequestRepoImpl(this._requestDataSources);

  @override
  Future<Either<Failure, List<RequestEntity>?>> getRequestByCenter(
      String centerId) async {
    try {
      List<RequestModel>? response =
          await _requestDataSources.getRequestByCenter(centerId);

      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<RequestEntity>?>> getRequestByCoach(
      String coachId) async {
    // TODO: implement getRequestByCoach
    throw UnimplementedError();
  }

  @override
  Future<Either<Failure, bool>> sendJoinRequest(
      String coachId, String centerId) async {
    try {
      bool response =
          await _requestDataSources.sendJoinRequest(coachId, centerId);

      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> updateStatus(
      String requestId, String status) async {
    try {
      bool response = await _requestDataSources.updateStatus(requestId, status);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> cancelJoinRequest(
      String coachId, String centerId) async {
    try {
      bool response =
          await _requestDataSources.cancelJoinRequest(coachId, centerId);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> checkExistingRequest(
      String coachId, String centerId) async {
    try {
      Map<String, dynamic> response =
          await _requestDataSources.checkExistingRequest(coachId, centerId);
      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
