import 'package:class_z/core/common/data/data_sources/review_data_sources.dart';
import 'package:class_z/core/common/data/models/reviewModel.dart';
import 'package:class_z/core/common/domain/repo_domain/review_repo_domain.dart';
import 'package:class_z/core/error/failure.dart';
import 'package:dartz/dartz.dart';

class ReviewRepoImpl extends ReviewRepoDomain {
  final ReviewDataSources reviewDataSources;

  ReviewRepoImpl({required this.reviewDataSources});
  @override
  Future<bool> postReview({required Map<String, dynamic> payload}) async {
    return await reviewDataSources.postReview(payload: payload);
  }

  @override
  Future<List<ReviewModel>> getReviewById(
      {required String id, required String type}) async {
    return await reviewDataSources.getReviewById(id: id, type: type);
  }

  @override
  Future<ReviewModel> getReviewByIdandClass(
      {required String id,
      required String type,
      required String classId}) async {
    try {
      return await reviewDataSources.getReviewByIdandClass(
          id: id, type: type, classId: classId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<ReviewModel>> getMoments({required String childId}) async {
    try {
      return await reviewDataSources.getMoments(childId: childId);
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<Either<Failure, bool>> postReviewByParent(
      {required Map<String, dynamic> payload}) async {
    try {
      final result =
          await reviewDataSources.postReviewByParent(payload: payload);

      return Right(result);
    } catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
