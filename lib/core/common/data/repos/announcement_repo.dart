import 'package:class_z/core/error/exception.dart';
import 'package:class_z/core/error/failure.dart';
import 'package:class_z/core/common/data/data_sources/announcement_data_source.dart';
import 'package:class_z/core/common/data/models/announementModel.dart';
import 'package:class_z/core/common/domain/entities/announcement_entity.dart';
import 'package:class_z/core/common/domain/repo_domain/announcement_repo_domain.dart';
import 'package:dartz/dartz.dart';

class AnnouncementRepoImpl extends IAnnouncementRepository {
  final AnnouncementDataSource _announcementDataSource;

  AnnouncementRepoImpl(this._announcementDataSource);
  @override
  Future<Either<Failure, AnnouncementEntity?>> getAnnouncement(
      String id, String? type) async {
    try {
      AnnouncementModel? announcementsModel =
          await this._announcementDataSource.getAnnouncement(id, type);
      AnnouncementEntity? announcements = announcementsModel;
      return Right(announcements);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> postAnnouncement(
      Map<String, dynamic> payload) async {
    try {
      bool success =
          await this._announcementDataSource.postAnnouncement(payload);
      return Right(success);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.toString()));
    }
  }
}
