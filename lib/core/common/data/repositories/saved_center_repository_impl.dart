import 'package:class_z/core/imports.dart';
import 'package:http/http.dart' as http;
import 'dart:math';
import 'package:class_z/core/common/domain/repositories/saved_center_repository.dart';
import 'package:class_z/core/utils/auth_helper.dart';

class SavedCenterRepositoryImpl implements SavedCenterRepository {
  final String baseUrl = AppText.device;
  final bool useMockData = false;

  // Global request manager to prevent duplicate calls
  static final Map<String, Future<bool>> _pendingRequests = {};
  static final Map<String, Future<Map<String, bool>>> _pendingBatchRequests =
      {};

  // Global cache for saved statuses
  static final Map<String, bool> _globalCache = {};
  static final Map<String, int> _cacheTimestamps = {};
  static const int _cacheExpiry = 5 * 60 * 1000; // 5 minutes

  // Get token and create headers with the correct format
  Map<String, String> get _headers {
    // Get the token from SharedRepository
    final sharedRepo = locator<SharedRepository>();
    final userModel = sharedRepo.getUserData();
    String? token;

    // First try to get token from UserModel
    if (userModel != null) {
      token = userModel.token;
      if (token.length > 10) {
        print('Using token from UserModel: ${token.substring(0, 10)}...');
      } else
        print('Using token from UserModel (short token)');
    }

    // Fallback to getting token from SharedPreferences if not in UserModel
    if (token == null || token.isEmpty) {
      token = sharedRepo.getToken();
      if (token != null && token.length > 10) {
        print(
            'Using token from SharedPreferences: ${token.substring(0, 10)}...');
      } else if (token != null) {
        print('Using token from SharedPreferences: $token');
      }
    }

    // Validate token if available
    if (token != null && token.isNotEmpty) {
      try {
        final authHelper = locator<AuthHelper>();
        if (authHelper.isTokenExpired(token)) {
          print('Token is expired, clearing it');
          sharedRepo.deleteToken();
          token = null;
        }
      } catch (e) {
        print('Error validating token: $e');
      }
    }

    // Create headers with only the expected format
    final headers = {
      'Content-Type': 'application/json',
    };

    if (token != null && token.isNotEmpty) {
      // Only use the auth-token header as expected by the backend
      headers['auth-token'] = token;
      print(
          'Added auth-token header: ${token.substring(0, min(10, token.length))}...');
    } else {
      print('No token available to add to headers');
    }

    return headers;
  }

  // Utility method to get user ID from token
  String? _getUserIdFromToken(String? token) {
    if (token == null || token.isEmpty) return null;

    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = json
          .decode(utf8.decode(base64Url.decode(base64Url.normalize(parts[1]))));

      return payload['id'] as String?;
    } catch (e) {
      print('Error extracting user ID from token: $e');
      return null;
    }
  }

  // Utility method to ensure we're using the correct parent ID
  String _ensureCorrectParentId(String parentId) {
    // If we already have a valid parent ID, use it
    if (parentId.isNotEmpty) {
      print('Using provided parent ID: $parentId');
      return parentId;
    }

    // Try to get parent ID from parent data
    final parentData = locator<SharedRepository>().getParentData();
    if (parentData != null &&
        parentData.id != null &&
        parentData.id!.isNotEmpty) {
      print('Using parent ID from parent data: ${parentData.id}');
      return parentData.id!;
    }

    // Try to get parent ID from token
    final token = locator<SharedRepository>().getToken();
    final tokenUserId = _getUserIdFromToken(token);

    if (tokenUserId != null) {
      print('Using parent ID from token: $tokenUserId');
      return tokenUserId;
    }

    // If all else fails, return the original ID
    print(
        'No valid parent ID found, using provided ID as last resort: $parentId');
    return parentId;
  }

  // Helper method to handle API response errors
  void _handleApiError(http.Response response, String operation) {
    print('$operation response: ${response.statusCode} - ${response.body}');

    if (response.statusCode == 500) {
      try {
        final errorBody = json.decode(response.body);
        if (errorBody['error'] != null) {
          final errorMessage = errorBody['error'].toString();

          if (errorMessage.contains('not found')) {
            print('Parent ID not found in database');
            throw Exception(
                'Parent ID not found in database. Please update your profile or contact support.');
          }

          throw Exception('Server error: $errorMessage');
        }
      } catch (e) {
        if (e is Exception) rethrow;
        throw Exception('Server error (500)');
      }
    }

    if (response.statusCode == 401) {
      throw Exception('Authentication failed - please log in again');
    }

    if (response.statusCode >= 400) {
      throw Exception(
          'API Error: Status ${response.statusCode}, Body: ${response.body}');
    }
  }

  @override
  Future<List<CenterData>> getSavedCenters({
    required String parentId,
  }) async {
    try {
      // Get token from UserModel first, then fall back to SharedPreferences
      final sharedRepo = locator<SharedRepository>();
      final userModel = sharedRepo.getUserData();
      String? token;

      // First try to get token from UserModel
      if (userModel != null) {
        token = userModel.token;
        print('Using token from UserModel for getSavedCenters');
      }

      // If token not available in UserModel, try SharedPreferences
      if (token == null || token.isEmpty) {
        token = sharedRepo.getToken();
        print('Using token from SharedPreferences for getSavedCenters');
      }

      // Validate the token
      if (token == null || token.isEmpty) {
        print('Authentication token is missing');
        throw Exception('Authentication failed - please log in again');
      }

      // Check if token is expired using AuthHelper
      try {
        final authHelper = locator<AuthHelper>();
        if (authHelper.isTokenExpired(token)) {
          print('Token is expired, clearing it');
          sharedRepo.deleteToken();
          throw Exception(
              'Authentication failed - token expired, please log in again');
        }
      } catch (e) {
        print('Error validating token: $e');
        if (e.toString().contains('Authentication failed')) {
          rethrow;
        }
      }

      // Get parent data from SharedRepository
      final parentData = sharedRepo.getParentData();
      if (parentData != null &&
          parentData.id != null &&
          parentData.id!.isNotEmpty) {
        parentId = parentData.id!;
        print('Using parent ID from parent data: $parentId');
      } else {
        // Ensure we're using the correct parent ID from token
        parentId = _ensureCorrectParentId(parentId);
      }

      print('Making API call to get saved centers');
      print('Parent ID: $parentId');
      print('Base URL: $baseUrl');
      final url = Uri.parse('$baseUrl/api/parent/saved-centers/$parentId');
      print('Full URL: $url');

      final response = await http.get(
        url,
        headers: _headers,
      );

      print('Response status code: ${response.statusCode}');

      // If token is invalid, prompt user to login again
      if (response.statusCode == 401) {
        print('Received 401 Unauthorized - Token is invalid or expired');
        // Clear the invalid token
        await sharedRepo.deleteToken();
        throw Exception('Authentication failed - please log in again');
      }

      // Handle 404 or 500 errors that indicate no saved centers exist
      if (response.statusCode == 404 || response.statusCode == 500) {
        try {
          final errorBody = json.decode(response.body);
          if (errorBody['error'] != null) {
            final errorMessage = errorBody['error'].toString().toLowerCase();
            if (errorMessage.contains('not found') ||
                errorMessage.contains('no saved') ||
                errorMessage.contains('empty') ||
                errorMessage.contains('no centers')) {
              print('No saved centers found, returning empty list');
              return [];
            }
          }
        } catch (e) {
          print('Error parsing error response: $e');
        }
        // For 404, always return empty list as it means no saved centers
        if (response.statusCode == 404) {
          print('404 error - no saved centers, returning empty list');
          return [];
        }
      }

      // Handle other errors
      if (response.statusCode != 200) {
        _handleApiError(response, 'getSavedCenters');
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final List<dynamic> centersData = data['data'];
          print('Received ${centersData.length} centers');
          return centersData
              .map((center) => CenterData.fromJson(center))
              .toList();
        }
        print('No centers found in response');
        return [];
      }

      throw Exception(
          'API Error: Status ${response.statusCode}, Body: ${response.body}');
    } catch (e, stackTrace) {
      print('Error in getSavedCenters: $e');
      print('Stack trace: $stackTrace');

      if (e.toString().contains('SocketException')) {
        print('Network error occurred, returning empty list');
        return []; // Return empty list instead of throwing for network issues
      }

      if (e.toString().contains('FormatException')) {
        print('Format error occurred, returning empty list');
        return []; // Return empty list instead of throwing for format issues
      }

      // For other errors, check if they're related to "no data" scenarios
      if (e.toString().toLowerCase().contains('no saved') ||
          e.toString().toLowerCase().contains('not found') ||
          e.toString().toLowerCase().contains('empty')) {
        print('No saved centers found (from exception), returning empty list');
        return [];
      }

      throw Exception('Failed to get saved centers: ${e.toString()}');
    }
  }

  @override
  Future<bool> saveCenter({
    required String parentId,
    required String centerId,
  }) async {
    try {
      // Get token from UserModel first, then fall back to SharedPreferences
      final sharedRepo = locator<SharedRepository>();
      final userModel = sharedRepo.getUserData();
      String? token;

      // First try to get token from UserModel - this is the most reliable source
      if (userModel != null) {
        token = userModel.token;
        print(
            'Using token from UserModel for saveCenter: ${userModel.toString()}');
      }

      // If token not available in UserModel, try SharedPreferences
      if (token == null || token.isEmpty) {
        token = sharedRepo.getToken();
        print('Using token from SharedPreferences for saveCenter');
      }

      // Validate the token
      if (token == null || token.isEmpty) {
        print('Authentication token is missing');
        await sharedRepo.deleteToken();
        throw Exception('Authentication failed - Please log in again');
      }

      // Check if token is expired using AuthHelper
      try {
        final authHelper = locator<AuthHelper>();
        if (authHelper.isTokenExpired(token)) {
          print('Token is expired, clearing it');
          await sharedRepo.deleteToken();
          throw Exception('Your session has expired. Please log in again.');
        }
      } catch (e) {
        print('Error validating token: $e');
        if (e.toString().contains('Authentication failed') ||
            e.toString().contains('session has expired')) {
          await sharedRepo.deleteToken();
          rethrow;
        }
      }

      // Get parent data from SharedRepository
      final parentData = sharedRepo.getParentData();
      if (parentData != null &&
          parentData.id != null &&
          parentData.id!.isNotEmpty) {
        parentId = parentData.id!;
        print('Using parent ID from parent data: $parentId');
      } else {
        // Ensure we're using the correct parent ID from token
        parentId = _ensureCorrectParentId(parentId);
      }

      print('Saving center - Parent ID: $parentId, Center ID: $centerId');

      // Create headers with only the auth-token format - exactly as expected by backend
      final Map<String, String> requestHeaders = {
        'Content-Type': 'application/json',
        'auth-token':
            token // This is the ONLY header format the backend accepts
      };

      print('Using these headers for saveCenter: $requestHeaders');
      print('Token being used: $token');

      try {
        // Log the token payload for debugging
        final parts = token.split('.');
        if (parts.length == 3) {
          final payload = json.decode(
              utf8.decode(base64Url.decode(base64Url.normalize(parts[1]))));
          print('Token payload: $payload');
          print('Token issued at: ${payload['iat']}');
          print(
              'Current timestamp: ${DateTime.now().millisecondsSinceEpoch ~/ 1000}');
        }
      } catch (e) {
        print('Error parsing token: $e');
      }

      final response = await http.post(
        Uri.parse('$baseUrl/api/parent/save-center'),
        headers: requestHeaders,
        body: json.encode({
          'parentId': parentId,
          'centerId': centerId,
        }),
      );

      print('Save center response: ${response.statusCode} - ${response.body}');

      // Parse error response for more details
      if (response.statusCode == 401) {
        print('Received 401 Unauthorized - ${response.body}');

        try {
          final errorData = json.decode(response.body);
          final errorType = errorData['errorType'];

          print('Authentication error type: $errorType');
          print(
              'Error message: ${errorData['message'] ?? 'No message provided'}');

          // Clear the invalid/expired token
          await sharedRepo.deleteToken();

          // Provide more specific error messages
          if (errorType == 'TOKEN_EXPIRED') {
            throw Exception('Your session has expired. Please log in again.');
          } else if (errorType == 'INVALID_TOKEN') {
            throw Exception('Invalid session. Please log in again.');
          } else {
            throw Exception('Authentication failed. Please log in again.');
          }
        } catch (e) {
          // If we can't parse the error response, use a generic message
          print('Error parsing error response: $e');
          await sharedRepo.deleteToken();
          throw Exception('Authentication failed. Please log in again.');
        }
      }

      // Special handling for "already saved" case
      if (response.statusCode == 400) {
        try {
          final Map<String, dynamic> errorData = json.decode(response.body);
          final message = errorData['message'] ?? '';

          if (message.toLowerCase().contains('already saved')) {
            print('Center already saved - treating as success');

            // CRITICAL: Invalidate cache after successful save
            final cacheKey = '${parentId}_$centerId';
            _globalCache.remove(cacheKey);
            _cacheTimestamps.remove(cacheKey);

            return true; // Treat "already saved" as success
          }
        } catch (e) {
          print('Error parsing 400 response: $e');
        }
      }

      _handleApiError(response, 'saveCenter');

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final Map<String, dynamic> data = json.decode(response.body);

        // CRITICAL: Invalidate cache after successful save
        final cacheKey = '${parentId}_$centerId';
        _globalCache.remove(cacheKey);
        _cacheTimestamps.remove(cacheKey);

        return data['success'] ?? false;
      }

      throw Exception(
          'Failed to save center: ${response.statusCode} - ${response.body}');
    } catch (e) {
      print('Error saving center: $e');
      throw Exception('Failed to save center: ${e.toString()}');
    }
  }

  @override
  Future<bool> unsaveCenter({
    required String parentId,
    required String centerId,
  }) async {
    try {
      // Get token from UserModel first, then fall back to SharedPreferences
      final sharedRepo = locator<SharedRepository>();
      final userModel = sharedRepo.getUserData();
      String? token;

      // First try to get token from UserModel
      if (userModel != null) {
        token = userModel.token;
        print('Using token from UserModel for unsaveCenter');
      }

      // If token not available in UserModel, try SharedPreferences
      if (token == null || token.isEmpty) {
        token = sharedRepo.getToken();
        print('Using token from SharedPreferences for unsaveCenter');
      }

      // Validate the token
      if (token == null || token.isEmpty) {
        print('Authentication token is missing');
        throw Exception('Authentication failed - please log in again');
      }

      // Check if token is expired using AuthHelper
      try {
        final authHelper = locator<AuthHelper>();
        if (authHelper.isTokenExpired(token)) {
          print('Token is expired, clearing it');
          sharedRepo.deleteToken();
          throw Exception(
              'Authentication failed - token expired, please log in again');
        }
      } catch (e) {
        print('Error validating token: $e');
        if (e.toString().contains('Authentication failed')) {
          rethrow;
        }
      }

      // Get parent data from SharedRepository
      final parentData = sharedRepo.getParentData();
      if (parentData != null &&
          parentData.id != null &&
          parentData.id!.isNotEmpty) {
        parentId = parentData.id!;
        print('Using parent ID from parent data: $parentId');
      } else {
        // Ensure we're using the correct parent ID from token
        parentId = _ensureCorrectParentId(parentId);
      }

      print('Unsaving center - Parent ID: $parentId, Center ID: $centerId');

      // Create headers with only the auth-token format
      final Map<String, String> requestHeaders = {
        'Content-Type': 'application/json',
        'auth-token':
            token // This is the ONLY header format the backend accepts
      };

      print('Using these headers for unsaveCenter: $requestHeaders');

      final response = await http.post(
        Uri.parse('$baseUrl/api/parent/unsave-center'),
        headers: requestHeaders,
        body: json.encode({
          'parentId': parentId,
          'centerId': centerId,
        }),
      );

      print(
          'Unsave center response: ${response.statusCode} - ${response.body}');

      // If token is invalid, prompt user to login again
      if (response.statusCode == 401) {
        print('Received 401 Unauthorized - Token is invalid or expired');
        // Clear the invalid token
        await sharedRepo.deleteToken();
        throw Exception('Authentication failed - please log in again');
      }

      // Special handling for "not saved" or "not found" cases
      if (response.statusCode == 400) {
        try {
          final Map<String, dynamic> errorData = json.decode(response.body);
          final message = errorData['message'] ?? '';

          if (message.toLowerCase().contains('not found') ||
              message.toLowerCase().contains('not saved')) {
            print('Center not saved - treating unsave as success');

            // CRITICAL: Invalidate cache after successful unsave
            final cacheKey = '${parentId}_$centerId';
            _globalCache.remove(cacheKey);
            _cacheTimestamps.remove(cacheKey);

            return true; // Treat "not saved" as successful unsave
          }
        } catch (e) {
          print('Error parsing 400 response: $e');
        }
      }

      _handleApiError(response, 'unsaveCenter');

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final Map<String, dynamic> data = json.decode(response.body);

        // CRITICAL: Invalidate cache after successful unsave
        final cacheKey = '${parentId}_$centerId';
        _globalCache.remove(cacheKey);
        _cacheTimestamps.remove(cacheKey);

        return data['success'] ?? false;
      }
      throw Exception(
          'Failed to unsave center: ${response.statusCode} - ${response.body}');
    } catch (e) {
      print('Error unsaving center: $e');
      throw Exception('Failed to unsave center: ${e.toString()}');
    }
  }

  @override
  Future<bool> isCenterSaved({
    required String parentId,
    required String centerId,
  }) async {
    // FORCE USE OF BATCH ENDPOINT - NO MORE INDIVIDUAL CALLS
    final results = await batchCheckSavedCenters(
      parentId: parentId,
      centerIds: [centerId],
    );
    return results[centerId] ?? false;
  }

  // Batch check multiple centers at once (SUPER EFFICIENT)
  Future<Map<String, bool>> batchCheckSavedCenters({
    required String parentId,
    required List<String> centerIds,
  }) async {
    if (centerIds.isEmpty) return {};

    // Check cache first
    final Map<String, bool> results = {};
    final List<String> uncachedIds = [];
    final currentTime = DateTime.now().millisecondsSinceEpoch;

    for (final centerId in centerIds) {
      final cacheKey = '${parentId}_$centerId';
      final cachedValue = _globalCache[cacheKey];
      final cacheTime = _cacheTimestamps[cacheKey] ?? 0;

      if (cachedValue != null && (currentTime - cacheTime) < _cacheExpiry) {
        results[centerId] = cachedValue;
      } else {
        uncachedIds.add(centerId);
      }
    }

    // If all are cached, return immediately
    if (uncachedIds.isEmpty) {
      return results;
    }

    // Check for pending batch request
    final batchKey = '${parentId}_${uncachedIds.join(',')}';
    if (_pendingBatchRequests.containsKey(batchKey)) {
      final batchResults = await _pendingBatchRequests[batchKey]!;
      results.addAll(batchResults);
      return results;
    }

    // Create batch request
    final batchFuture = _performBatchCheck(parentId, uncachedIds);
    _pendingBatchRequests[batchKey] = batchFuture;

    try {
      final batchResults = await batchFuture;
      results.addAll(batchResults);

      // Cache results
      for (final entry in batchResults.entries) {
        final cacheKey = '${parentId}_${entry.key}';
        _globalCache[cacheKey] = entry.value;
        _cacheTimestamps[cacheKey] = currentTime;
      }

      return results;
    } finally {
      _pendingBatchRequests.remove(batchKey);
    }
  }

  Future<Map<String, bool>> _performBatchCheck(
      String parentId, List<String> centerIds) async {
    try {
      final token = locator<SharedRepository>().getToken();
      if (token?.isEmpty ?? true) {
        // Return all false if no token
        return Map.fromIterable(centerIds, value: (id) => false);
      }

      final Map<String, String> requestHeaders = {
        'Content-Type': 'application/json',
        'auth-token': token!
      };

      final response = await http.post(
        Uri.parse('$baseUrl/api/parent/batch-check-saved/$parentId'),
        headers: requestHeaders,
        body: json.encode({
          'centerIds': centerIds,
        }),
      );

      if (response.statusCode == 401) {
        await locator<SharedRepository>().deleteToken();
        return Map.fromIterable(centerIds, value: (id) => false);
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final Map<String, dynamic> rawResults = data['data'];
          final Map<String, bool> results = {};

          for (final centerId in centerIds) {
            results[centerId] = rawResults[centerId] == true;
          }

          return results;
        }
      }

      // Return all false on error
      return Map.fromIterable(centerIds, value: (id) => false);
    } catch (e) {
      // Return all false on error
      return Map.fromIterable(centerIds, value: (id) => false);
    }
  }
}
