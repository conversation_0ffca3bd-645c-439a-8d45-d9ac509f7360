import 'package:class_z/core/imports.dart';
import 'package:http/http.dart' as http;
import 'dart:math';
import 'package:class_z/core/common/domain/repositories/saved_center_repository.dart';
import 'package:class_z/core/utils/auth_helper.dart';
import 'package:flutter/foundation.dart';

class SavedCenterRepositoryImpl implements SavedCenterRepository {
  final String baseUrl = AppText.device;
  final bool useMockData = false;

  // Get token and create headers with the correct format
  Map<String, String> get _headers {
    // Get the token from SharedRepository
    final sharedRepo = locator<SharedRepository>();
    final userModel = sharedRepo.getUserData();
    String? token;

    // First try to get token from UserModel
    if (userModel != null) {
      token = userModel.token;
      if (token.length > 10) {
        print('Using token from UserModel: ${token.substring(0, 10)}...');
      } else
        print('Using token from UserModel (short token)');
    }

    // Fallback to getting token from SharedPreferences if not in UserModel
    if (token == null || token.isEmpty) {
      token = sharedRepo.getToken();
      if (token != null && token.length > 10) {
        print(
            'Using token from SharedPreferences: ${token.substring(0, 10)}...');
      } else if (token != null) {
        print('Using token from SharedPreferences: $token');
      }
    }

    // Validate token if available
    if (token != null && token.isNotEmpty) {
      try {
        final authHelper = locator<AuthHelper>();
        if (authHelper.isTokenExpired(token)) {
          print('Token is expired, clearing it');
          sharedRepo.deleteToken();
          token = null;
        }
      } catch (e) {
        print('Error validating token: $e');
      }
    }

    // Create headers with only the expected format
    final headers = {
      'Content-Type': 'application/json',
    };

    if (token != null && token.isNotEmpty) {
      // Only use the auth-token header as expected by the backend
      headers['auth-token'] = token;
      print(
          'Added auth-token header: ${token.substring(0, min(10, token.length))}...');
    } else {
      print('No token available to add to headers');
    }

    return headers;
  }

  // Utility method to get user ID from token
  String? _getUserIdFromToken(String? token) {
    if (token == null || token.isEmpty) return null;

    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = json
          .decode(utf8.decode(base64Url.decode(base64Url.normalize(parts[1]))));

      return payload['id'] as String?;
    } catch (e) {
      print('Error extracting user ID from token: $e');
      return null;
    }
  }

  // Utility method to ensure we're using the correct parent ID
  String _ensureCorrectParentId(String parentId) {
    // If we already have a valid parent ID, use it
    if (parentId.isNotEmpty) {
      print('Using provided parent ID: $parentId');
      return parentId;
    }

    // Try to get parent ID from parent data
    final parentData = locator<SharedRepository>().getParentData();
    if (parentData != null &&
        parentData.id != null &&
        parentData.id!.isNotEmpty) {
      print('Using parent ID from parent data: ${parentData.id}');
      return parentData.id!;
    }

    // Try to get parent ID from token
    final token = locator<SharedRepository>().getToken();
    final tokenUserId = _getUserIdFromToken(token);

    if (tokenUserId != null) {
      print('Using parent ID from token: $tokenUserId');
      return tokenUserId;
    }

    // If all else fails, return the original ID
    print(
        'No valid parent ID found, using provided ID as last resort: $parentId');
    return parentId;
  }

  // Helper method to handle API response errors
  void _handleApiError(http.Response response, String operation) {
    print('$operation response: ${response.statusCode} - ${response.body}');

    if (response.statusCode == 500) {
      try {
        final errorBody = json.decode(response.body);
        if (errorBody['error'] != null) {
          final errorMessage = errorBody['error'].toString();

          if (errorMessage.contains('not found')) {
            print('Parent ID not found in database');
            throw Exception(
                'Parent ID not found in database. Please update your profile or contact support.');
          }

          throw Exception('Server error: $errorMessage');
        }
      } catch (e) {
        if (e is Exception) rethrow;
        throw Exception('Server error (500)');
      }
    }

    if (response.statusCode == 401) {
      throw Exception('Authentication failed - please log in again');
    }

    if (response.statusCode >= 400) {
      throw Exception(
          'API Error: Status ${response.statusCode}, Body: ${response.body}');
    }
  }

  @override
  Future<List<CenterData>> getSavedCenters({
    required String parentId,
  }) async {
    try {
      // Get token from UserModel first, then fall back to SharedPreferences
      final sharedRepo = locator<SharedRepository>();
      final userModel = sharedRepo.getUserData();
      String? token;

      // First try to get token from UserModel
      if (userModel != null) {
        token = userModel.token;
        print('Using token from UserModel for getSavedCenters');
      }

      // If token not available in UserModel, try SharedPreferences
      if (token == null || token.isEmpty) {
        token = sharedRepo.getToken();
        print('Using token from SharedPreferences for getSavedCenters');
      }

      // Validate the token
      if (token == null || token.isEmpty) {
        print('Authentication token is missing');
        throw Exception('Authentication failed - please log in again');
      }

      // Check if token is expired using AuthHelper
      try {
        final authHelper = locator<AuthHelper>();
        if (authHelper.isTokenExpired(token)) {
          print('Token is expired, clearing it');
          sharedRepo.deleteToken();
          throw Exception(
              'Authentication failed - token expired, please log in again');
        }
      } catch (e) {
        print('Error validating token: $e');
        if (e.toString().contains('Authentication failed')) {
          rethrow;
        }
      }

      // Get parent data from SharedRepository
      final parentData = sharedRepo.getParentData();
      if (parentData != null &&
          parentData.id != null &&
          parentData.id!.isNotEmpty) {
        parentId = parentData.id!;
        print('Using parent ID from parent data: $parentId');
      } else {
        // Ensure we're using the correct parent ID from token
        parentId = _ensureCorrectParentId(parentId);
      }

      print('Making API call to get saved centers');
      print('Parent ID: $parentId');
      print('Base URL: $baseUrl');
      final url = Uri.parse('$baseUrl/api/parent/saved-centers/$parentId');
      print('Full URL: $url');

      final response = await http.get(
        url,
        headers: _headers,
      );

      print('Response status code: ${response.statusCode}');

      // If token is invalid, prompt user to login again
      if (response.statusCode == 401) {
        print('Received 401 Unauthorized - Token is invalid or expired');
        // Clear the invalid token
        await sharedRepo.deleteToken();
        throw Exception('Authentication failed - please log in again');
      }

      // Handle 500 error with "not found" message
      if (response.statusCode == 500) {
        try {
          final errorBody = json.decode(response.body);
          if (errorBody['error'] != null &&
              errorBody['error'].toString().contains('not found')) {
            print('Parent ID not found, returning empty list');
            return [];
          }
        } catch (e) {
          print('Error parsing error response: $e');
        }
      }

      // Handle other errors
      if (response.statusCode != 200) {
        _handleApiError(response, 'getSavedCenters');
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data['success'] == true && data['data'] != null) {
          final List<dynamic> centersData = data['data'];
          print('Received ${centersData.length} centers');
          return centersData
              .map((center) => CenterData.fromJson(center))
              .toList();
        }
        print('No centers found in response');
        return [];
      }

      throw Exception(
          'API Error: Status ${response.statusCode}, Body: ${response.body}');
    } catch (e, stackTrace) {
      print('Error in getSavedCenters: $e');
      print('Stack trace: $stackTrace');

      if (e.toString().contains('SocketException')) {
        throw Exception('Network error - please check your connection');
      }

      if (e.toString().contains('FormatException')) {
        throw Exception('Invalid response format from server');
      }

      throw Exception('Failed to get saved centers: ${e.toString()}');
    }
  }

  @override
  Future<bool> saveCenter({
    required String parentId,
    required String centerId,
  }) async {
    try {
      // Get token from UserModel first, then fall back to SharedPreferences
      final sharedRepo = locator<SharedRepository>();
      final userModel = sharedRepo.getUserData();
      String? token;

      // First try to get token from UserModel - this is the most reliable source
      if (userModel != null) {
        token = userModel.token;
        print(
            'Using token from UserModel for saveCenter: ${userModel.toString()}');
      }

      // If token not available in UserModel, try SharedPreferences
      if (token == null || token.isEmpty) {
        token = sharedRepo.getToken();
        print('Using token from SharedPreferences for saveCenter');
      }

      // Validate the token
      if (token == null || token.isEmpty) {
        print('Authentication token is missing');
        throw Exception('Authentication failed - please log in again');
      }

      // Check if token is expired using AuthHelper
      try {
        final authHelper = locator<AuthHelper>();
        if (authHelper.isTokenExpired(token)) {
          print('Token is expired, clearing it');
          sharedRepo.deleteToken();
          throw Exception(
              'Authentication failed - token expired, please log in again');
        }
      } catch (e) {
        print('Error validating token: $e');
        if (e.toString().contains('Authentication failed')) {
          rethrow;
        }
      }

      // Get parent data from SharedRepository
      final parentData = sharedRepo.getParentData();
      if (parentData != null &&
          parentData.id != null &&
          parentData.id!.isNotEmpty) {
        parentId = parentData.id!;
        print('Using parent ID from parent data: $parentId');
      } else {
        // Ensure we're using the correct parent ID from token
        parentId = _ensureCorrectParentId(parentId);
      }

      print('Saving center - Parent ID: $parentId, Center ID: $centerId');

      // Create headers with only the auth-token format - exactly as expected by backend
      final Map<String, String> requestHeaders = {
        'Content-Type': 'application/json',
        'auth-token':
            token // This is the ONLY header format the backend accepts
      };

      print('Using these headers for saveCenter: $requestHeaders');
      print('Token being used: $token');

      try {
        // Log the token payload for debugging
        final parts = token.split('.');
        if (parts.length == 3) {
          final payload = json.decode(
              utf8.decode(base64Url.decode(base64Url.normalize(parts[1]))));
          print('Token payload: $payload');
          print('Token issued at: ${payload['iat']}');
          print(
              'Current timestamp: ${DateTime.now().millisecondsSinceEpoch ~/ 1000}');
        }
      } catch (e) {
        print('Error parsing token: $e');
      }

      final response = await http.post(
        Uri.parse('$baseUrl/api/parent/save-center'),
        headers: requestHeaders,
        body: json.encode({
          'parentId': parentId,
          'centerId': centerId,
        }),
      );

      print('Save center response: ${response.statusCode} - ${response.body}');

      // If token is invalid, prompt user to login again
      if (response.statusCode == 401) {
        print('Received 401 Unauthorized - Token is invalid or expired');
        // Clear the invalid token
        await sharedRepo.deleteToken();
        // We'll let the SavedCenterBloc handle the authentication error
        throw Exception('Authentication failed - please log in again');
      }

      _handleApiError(response, 'saveCenter');

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final Map<String, dynamic> data = json.decode(response.body);
        return data['success'] ?? false;
      }

      throw Exception(
          'Failed to save center: ${response.statusCode} - ${response.body}');
    } catch (e) {
      print('Error saving center: $e');
      throw Exception('Failed to save center: ${e.toString()}');
    }
  }

  @override
  Future<bool> unsaveCenter({
    required String parentId,
    required String centerId,
  }) async {
    try {
      // Get token from UserModel first, then fall back to SharedPreferences
      final sharedRepo = locator<SharedRepository>();
      final userModel = sharedRepo.getUserData();
      String? token;

      // First try to get token from UserModel
      if (userModel != null) {
        token = userModel.token;
        print('Using token from UserModel for unsaveCenter');
      }

      // If token not available in UserModel, try SharedPreferences
      if (token == null || token.isEmpty) {
        token = sharedRepo.getToken();
        print('Using token from SharedPreferences for unsaveCenter');
      }

      // Validate the token
      if (token == null || token.isEmpty) {
        print('Authentication token is missing');
        throw Exception('Authentication failed - please log in again');
      }

      // Check if token is expired using AuthHelper
      try {
        final authHelper = locator<AuthHelper>();
        if (authHelper.isTokenExpired(token)) {
          print('Token is expired, clearing it');
          sharedRepo.deleteToken();
          throw Exception(
              'Authentication failed - token expired, please log in again');
        }
      } catch (e) {
        print('Error validating token: $e');
        if (e.toString().contains('Authentication failed')) {
          rethrow;
        }
      }

      // Get parent data from SharedRepository
      final parentData = sharedRepo.getParentData();
      if (parentData != null &&
          parentData.id != null &&
          parentData.id!.isNotEmpty) {
        parentId = parentData.id!;
        print('Using parent ID from parent data: $parentId');
      } else {
        // Ensure we're using the correct parent ID from token
        parentId = _ensureCorrectParentId(parentId);
      }

      print('Unsaving center - Parent ID: $parentId, Center ID: $centerId');

      // Create headers with only the auth-token format
      final Map<String, String> requestHeaders = {
        'Content-Type': 'application/json',
        'auth-token':
            token // This is the ONLY header format the backend accepts
      };

      print('Using these headers for unsaveCenter: $requestHeaders');

      final response = await http.post(
        Uri.parse('$baseUrl/api/parent/unsave-center'),
        headers: requestHeaders,
        body: json.encode({
          'parentId': parentId,
          'centerId': centerId,
        }),
      );

      print(
          'Unsave center response: ${response.statusCode} - ${response.body}');

      // If token is invalid, prompt user to login again
      if (response.statusCode == 401) {
        print('Received 401 Unauthorized - Token is invalid or expired');
        // Clear the invalid token
        await sharedRepo.deleteToken();
        throw Exception('Authentication failed - please log in again');
      }

      _handleApiError(response, 'unsaveCenter');

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final Map<String, dynamic> data = json.decode(response.body);
        return data['success'] ?? false;
      }
      throw Exception(
          'Failed to unsave center: ${response.statusCode} - ${response.body}');
    } catch (e) {
      print('Error unsaving center: $e');
      throw Exception('Failed to unsave center: ${e.toString()}');
    }
  }

  @override
  Future<bool> isCenterSaved({
    required String parentId,
    required String centerId,
  }) async {
    try {
      // Get token from UserModel first, then fall back to SharedPreferences
      final sharedRepo = locator<SharedRepository>();
      final userModel = sharedRepo.getUserData();
      String? token;

      // First try to get token from UserModel
      if (userModel != null) {
        token = userModel.token;
        print('Using token from UserModel for isCenterSaved');
      }

      // If token not available in UserModel, try SharedPreferences
      if (token == null || token.isEmpty) {
        token = sharedRepo.getToken();
        print('Using token from SharedPreferences for isCenterSaved');
      }

      // For this method, we return false instead of throwing if token is missing
      // to avoid disrupting the UI flow
      if (token == null || token.isEmpty) {
        print('Authentication token is missing for isCenterSaved');
        return false;
      }

      // Check if token is expired using AuthHelper
      try {
        final authHelper = locator<AuthHelper>();
        if (authHelper.isTokenExpired(token)) {
          print('Token is expired for isCenterSaved, clearing it');
          sharedRepo.deleteToken();
          return false; // Return false instead of throwing to avoid UI disruption
        }
      } catch (e) {
        print('Error validating token for isCenterSaved: $e');
        return false;
      }

      // Get parent data from SharedRepository
      final parentData = sharedRepo.getParentData();
      if (parentData != null &&
          parentData.id != null &&
          parentData.id!.isNotEmpty) {
        parentId = parentData.id!;
        print('Using parent ID from parent data: $parentId');
      } else {
        // Ensure we're using the correct parent ID from token
        parentId = _ensureCorrectParentId(parentId);
      }

      if (kDebugMode) {
        print(
            'Checking if center is saved - Parent ID: $parentId, Center ID: $centerId');
      }

      // Create headers with only the auth-token format
      final Map<String, String> requestHeaders = {
        'Content-Type': 'application/json',
        'auth-token':
            token // This is the ONLY header format the backend accepts
      };

      print('Using these headers for isCenterSaved: $requestHeaders');

      final response = await http.get(
        Uri.parse('$baseUrl/api/parent/is-center-saved/$parentId/$centerId'),
        headers: requestHeaders,
      );

      print(
          'Check saved status response: ${response.statusCode} - ${response.body}');

      // Handle 401 as not saved instead of throwing
      if (response.statusCode == 401) {
        print('Received 401 Unauthorized - Token is invalid or expired');
        // Clear the invalid token
        await sharedRepo.deleteToken();
        return false;
      }

      // Handle 404 as not saved
      if (response.statusCode == 404) {
        return false;
      }

      // Handle 500 with "not found" message
      if (response.statusCode == 500) {
        try {
          final errorBody = json.decode(response.body);
          if (errorBody['error'] != null &&
              errorBody['error'].toString().contains('not found')) {
            print('Parent ID not found, returning false');
            return false;
          }
        } catch (e) {
          print('Error parsing error response: $e');
        }
      }

      // Handle other errors
      if (response.statusCode != 200) {
        _handleApiError(response, 'isCenterSaved');
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final Map<String, dynamic> data = json.decode(response.body);
        return data['isSaved'] ?? false;
      }

      return false;
    } catch (e) {
      print('Error checking saved status: $e');
      // Return false instead of throwing to avoid UI disruption
      return false;
    }
  }

  @override
  Future<Map<String, bool>> batchCheckSavedCenters({
    required String parentId,
    required List<String> centerIds,
  }) async {
    try {
      // Get token from UserModel first, then fall back to SharedPreferences
      final sharedRepo = locator<SharedRepository>();
      final userModel = sharedRepo.getUserData();
      String? token;

      // First try to get token from UserModel
      if (userModel != null) {
        token = userModel.token;
        print('Using token from UserModel for batchCheckSavedCenters');
      }

      // If token not available in UserModel, try SharedPreferences
      if (token == null || token.isEmpty) {
        token = sharedRepo.getToken();
        print('Using token from SharedPreferences for batchCheckSavedCenters');
      }

      // For this method, we return empty map instead of throwing if token is missing
      if (token == null || token.isEmpty) {
        print('Authentication token is missing for batchCheckSavedCenters');
        return {};
      }

      // Check if token is expired using AuthHelper
      try {
        final authHelper = locator<AuthHelper>();
        if (authHelper.isTokenExpired(token)) {
          print('Token is expired for batchCheckSavedCenters, clearing it');
          sharedRepo.deleteToken();
          return {};
        }
      } catch (e) {
        print('Error validating token for batchCheckSavedCenters: $e');
        return {};
      }

      // Get parent data from SharedRepository
      final parentData = sharedRepo.getParentData();
      if (parentData != null &&
          parentData.id != null &&
          parentData.id!.isNotEmpty) {
        parentId = parentData.id!;
        print('Using parent ID from parent data: $parentId');
      } else {
        // Ensure we're using the correct parent ID from token
        parentId = _ensureCorrectParentId(parentId);
      }

      print(
          'Batch checking saved centers - Parent ID: $parentId, Center IDs: $centerIds');

      // Create headers with only the auth-token format
      final Map<String, String> requestHeaders = {
        'Content-Type': 'application/json',
        'auth-token': token
      };

      final response = await http.post(
        Uri.parse('$baseUrl/api/parent/batch-check-saved/$parentId'),
        headers: requestHeaders,
        body: json.encode({
          'centerIds': centerIds,
        }),
      );

      print('Batch check response: ${response.statusCode} - ${response.body}');

      // Handle 401 as empty map instead of throwing
      if (response.statusCode == 401) {
        print('Received 401 Unauthorized - Token is invalid or expired');
        await sharedRepo.deleteToken();
        return {};
      }

      // Handle 404 as empty map
      if (response.statusCode == 404) {
        return {};
      }

      // Handle 500 with "not found" message
      if (response.statusCode == 500) {
        try {
          final errorBody = json.decode(response.body);
          if (errorBody['error'] != null &&
              errorBody['error'].toString().contains('not found')) {
            print('Parent ID not found, returning empty map');
            return {};
          }
        } catch (e) {
          print('Error parsing error response: $e');
        }
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data['success'] == true && data['savedStatus'] != null) {
          final Map<String, dynamic> savedStatus = data['savedStatus'];
          return savedStatus.map((key, value) => MapEntry(key, value as bool));
        }
      }

      return {};
    } catch (e) {
      print('Error in batch check saved centers: $e');
      return {};
    }
  }
}
