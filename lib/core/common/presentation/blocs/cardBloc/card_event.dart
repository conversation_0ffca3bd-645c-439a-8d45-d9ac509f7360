part of 'card_bloc.dart';

abstract class CardEvent extends Equatable {
  const CardEvent();
  @override
  List<Object> get props => [];
}

class SaveCardEvent extends Card<PERSON>vent {
  final String number;
  final int expMonth;
  final int expYear;
  final String cvc;
  final String name;
  const SaveCardEvent({
    required this.number,
    required this.expMonth,
    required this.expYear,
    required this.cvc,
    required this.name,
  });
  @override
  List<Object> get props => [number, expMonth, expYear, cvc, name];
}
