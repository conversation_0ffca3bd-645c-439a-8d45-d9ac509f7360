import 'package:class_z/core/imports.dart';
import 'package:flutter/foundation.dart';

part 'announcement_event.dart';
part 'announcement_state.dart';

class AnnouncementBloc extends Bloc<AnnouncementEvent, AnnouncementState> {
  final GetAnnouncementUseCase getAnnouncementUseCase;
  final PostAnnouncementUseCase postAnnouncementUseCase;

  AnnouncementBloc({
    required this.getAnnouncementUseCase,
    required this.postAnnouncementUseCase,
  }) : super(AnnouncementInitial()) {
    on<GetAnnouncementEvent>(_getAnnouncementEvent);

    on<PostAnnouncementEvent>(_postAnnouncementEvent);
  }

  FutureOr<void> _getAnnouncementEvent(
      GetAnnouncementEvent event, Emitter<AnnouncementState> emit) async {
    emit(AnnouncementLoading());
    if (kDebugMode) {
      print('Getting announcements for ID: ${event.id}, type: ${event.type}');
    }
    try {
      final announcements =
          await getAnnouncementUseCase.call(event.id, event.type);

      announcements.fold(
          (failure) => emit(AnnouncementError(failure.message)),
          (announcements) =>
              emit(AnnouncementLoadedState(announcements: announcements)));
    } catch (e) {
      emit(AnnouncementError(e.toString()));
    }
  }

  FutureOr<void> _postAnnouncementEvent(
      PostAnnouncementEvent event, Emitter<AnnouncementState> emit) async {
    emit(AnnouncementLoading());
    try {
      final success = await postAnnouncementUseCase.call(event.payload);
      print('succes $success');
      success.fold((failure) => emit(AnnouncementError(failure.message)),
          (success) => emit(AnnouncementPostedState(success)));
    } catch (e) {
      emit(AnnouncementError(e.toString()));
    }
  }
}
