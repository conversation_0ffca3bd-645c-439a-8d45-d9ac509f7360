part of 'announcement_bloc.dart';

abstract class AnnouncementEvent extends Equatable {
  AnnouncementEvent();
  get props => [];
}

class GetAnnouncementEvent extends AnnouncementEvent {
  final String id;
  final String? type;

  GetAnnouncementEvent({required this.id, this.type});
}

class PostAnnouncementEvent extends AnnouncementEvent {
  final Map<String, dynamic> payload;

  PostAnnouncementEvent({required this.payload});
}
