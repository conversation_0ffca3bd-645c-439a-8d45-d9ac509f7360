part of 'announcement_bloc.dart';

abstract class AnnouncementState extends Equatable {
  AnnouncementState();
  get props => [];
}

class AnnouncementInitial extends AnnouncementState {}

class AnnouncementLoading extends AnnouncementState {}

class AnnouncementError extends AnnouncementState{
  final String message;

  AnnouncementError(this.message);
  
}

class AnnouncementLoadedState extends AnnouncementState{
final AnnouncementEntity? announcements;

  AnnouncementLoadedState({required this.announcements});

}
class AnnouncementPostedState extends AnnouncementState{
  final bool success;

  AnnouncementPostedState(this.success);
}
