part of 'campaign_bloc.dart';

abstract class CampaignEvent extends Equatable {
  const CampaignEvent();
  @override
  List<Object> get props => [];
}

class GetActiveCampaignsEvent extends CampaignEvent {
  const GetActiveCampaignsEvent();
}

class RecordCampaignClickEvent extends CampaignEvent {
  final String campaignId;

  const RecordCampaignClickEvent({required this.campaignId});

  @override
  List<Object> get props => [campaignId];
}
