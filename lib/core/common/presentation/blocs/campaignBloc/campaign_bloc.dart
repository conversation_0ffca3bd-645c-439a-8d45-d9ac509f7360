import 'package:class_z/core/common/domain/usecase/campaign_use_case.dart';
import 'package:class_z/core/common/domain/entities/campaign_entity.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

part 'campaign_event.dart';
part 'campaign_state.dart';

class CampaignBloc extends Bloc<CampaignEvent, CampaignState> {
  final GetActiveCampaignsUseCase getActiveCampaigns;
  final RecordCampaignClickUseCase recordCampaignClick;

  CampaignBloc({
    required this.getActiveCampaigns,
    required this.recordCampaignClick,
  }) : super(CampaignInitial()) {
    on<GetActiveCampaignsEvent>(_onGetActiveCampaigns);
    on<RecordCampaignClickEvent>(_onRecordCampaignClick);
  }

  Future<void> _onGetActiveCampaigns(
    GetActiveCampaignsEvent event,
    Emitter<CampaignState> emit,
  ) async {
    print("🎬 CampaignBloc: _onGetActiveCampaigns called");
    emit(CampaignLoading());
    print("🔄 CampaignBloc: Emitted CampaignLoading state");
    final response = await getActiveCampaigns();
    print("🔍 CampaignBloc: Use case response: $response");
    response.fold(
      (failure) {
        print(failure.message);
        emit(CampaignError(failure.message));
      },
      (campaigns) => emit(CampaignLoadedState(campaigns: campaigns)),
    );
  }

  Future<void> _onRecordCampaignClick(
    RecordCampaignClickEvent event,
    Emitter<CampaignState> emit,
  ) async {
    final response = await recordCampaignClick(event.campaignId);
    response.fold(
      (failure) {
        // Don't emit error for click tracking failures, just log
        print('Failed to record campaign click: ${failure.message}');
      },
      (success) => emit(CampaignClickRecordedState(success)),
    );
  }
}
