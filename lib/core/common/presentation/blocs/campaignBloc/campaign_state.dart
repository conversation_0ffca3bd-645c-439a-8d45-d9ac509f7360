part of 'campaign_bloc.dart';

abstract class CampaignState extends Equatable {
  const CampaignState();
  @override
  List<Object> get props => [];
}

class CampaignInitial extends CampaignState {}

class CampaignLoading extends CampaignState {}

class CampaignError extends CampaignState {
  final String message;

  const CampaignError(this.message);

  @override
  List<Object> get props => [message];
}

class CampaignLoadedState extends CampaignState {
  final List<CampaignEntity> campaigns;

  const CampaignLoadedState({required this.campaigns});

  @override
  List<Object> get props => [campaigns];
}

class CampaignClickRecordedState extends CampaignState {
  final bool success;

  const CampaignClickRecordedState(this.success);

  @override
  List<Object> get props => [success];
}
