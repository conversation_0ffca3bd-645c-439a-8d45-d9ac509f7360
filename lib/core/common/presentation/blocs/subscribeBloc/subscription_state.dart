part of 'subscription_bloc.dart';

abstract class SubscriptionState {}

class SubscribeInitial extends SubscriptionState {}

class SubscriptionLoadingState extends SubscriptionState {}

class SubscriptionErrorState extends SubscriptionState {
  final String error;

  SubscriptionErrorState({required this.error});
}

class GetSubscribeSuccessState extends SubscriptionState {
  final SubscriptionModel subscriptionModel;

  GetSubscribeSuccessState({required this.subscriptionModel});
}

class SubscriptionCancelledState extends SubscriptionState {}

class GetCardLoadingState extends SubscriptionState {}

class GetCardErrorState extends SubscriptionState {
  final String error;

  GetCardErrorState({required this.error});
}
