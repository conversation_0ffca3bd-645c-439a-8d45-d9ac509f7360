import 'package:class_z/core/imports.dart';

part 'subscription_event.dart';
part 'subscription_state.dart';

class SubscriptionBloc extends Bloc<SubscriptionEvent, SubscriptionState> {
  final GetSubscriptionUseCase getSubscriptionUseCase;
  final CancelSubscriptionUseCase cancelSubscriptionUseCase;

  SubscriptionBloc({
    required this.getSubscriptionUseCase,
    required this.cancelSubscriptionUseCase,
  }) : super(SubscribeInitial()) {
    on<GetSubscriptionPlanEvent>(getSubscriptionPlanEvent);
    on<CancelSubscriptionEvent>(cancelSubscriptionEvent);
  }

  FutureOr<void> getSubscriptionPlanEvent(
      GetSubscriptionPlanEvent event, Emitter<SubscriptionState> emit) async {
    try {
      emit(SubscriptionLoadingState());
      SubscriptionModel subscriptionModel = await getSubscriptionUseCase.call();
      emit(GetSubscribeSuccessState(subscriptionModel: subscriptionModel));
    } catch (e) {
      emit(SubscriptionErrorState(error: e.toString()));
    }
  }

  FutureOr<void> cancelSubscriptionEvent(
      CancelSubscriptionEvent event, Emitter<SubscriptionState> emit) async {
    try {
      emit(SubscriptionLoadingState());
      // Call backend API to cancel subscription
      bool success = await cancelSubscriptionUseCase.call();
      if (success) {
        emit(SubscriptionCancelledState());
      } else {
        emit(SubscriptionErrorState(error: "Failed to cancel subscription"));
      }
    } catch (e) {
      emit(SubscriptionErrorState(error: e.toString()));
    }
  }
}
