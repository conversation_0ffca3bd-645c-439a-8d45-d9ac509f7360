part of 'search_bloc.dart';

abstract class SearchEvent extends Equatable {
  const SearchEvent();

  List<Object?> get props => [];
}

class SearchQueryEvent extends SearchEvent {
  final String query;
  final int? priceMax;
  final int? priceMin;
  final int? ageFrom;
  final int? ageTo;
  final String? location;
  final String? sortBy;
  final double? rating;
  final bool? senService;
  final bool? isSearchingCoach;

  SearchQueryEvent({
    required this.query,
    this.priceMax,
    this.priceMin,
    this.ageFrom,
    this.ageTo,
    this.location,
    this.sortBy,
    this.rating,
    this.senService,
    this.isSearchingCoach,
  });

  @override
  List<Object?> get props => [
        query,
        priceMax,
        priceMin,
        ageFrom,
        ageTo,
        location,
        sortBy,
        rating,
        senService
      ];
}

class SearchCenterByCategory extends SearchEvent {
  final String category;
  final int? priceMax;
  final int? priceMin;
  final int? ageFrom;
  final int? ageTo;
  final String? location;
  final String? sortBy;
  final double? rating;
  final bool? senService;

  SearchCenterByCategory({
    required this.category,
    this.priceMax,
    this.priceMin,
    this.ageFrom,
    this.ageTo,
    this.location,
    this.sortBy,
    this.rating,
    this.senService,
  });

  @override
  List<Object?> get props => [
        category,
        priceMax,
        priceMin,
        ageFrom,
        ageTo,
        location,
        sortBy,
        rating,
        senService
      ];
}

class FindNearbyCenters extends SearchEvent {
  final double longitude;
  final double latitude;
  final double? maxDistance;
  final int? priceMin;
  final int? priceMax;
  final int? ageFrom;
  final int? ageTo;
  final String? sortBy;
  final double? rating;
  final bool? senService;

  FindNearbyCenters({
    required this.longitude,
    required this.latitude,
    this.maxDistance,
    this.priceMin,
    this.priceMax,
    this.ageFrom,
    this.ageTo,
    this.sortBy,
    this.rating,
    this.senService,
  });

  @override
  List<Object?> get props => [
        longitude,
        latitude,
        maxDistance,
        priceMin,
        priceMax,
        ageFrom,
        ageTo,
        sortBy,
        rating,
        senService
      ];
}

// Add a new event to clear search results
class ClearSearchEvent extends SearchEvent {
  @override
  List<Object?> get props => [];
}
