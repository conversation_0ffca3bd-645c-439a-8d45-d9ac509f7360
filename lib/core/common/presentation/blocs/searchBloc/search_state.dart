part of 'search_bloc.dart';

abstract class SearchState extends Equatable {
  const SearchState();

  @override
  List<Object?> get props => [];
}

class SearchInitial extends SearchState {}

class SearchLoading extends SearchState {}

class SearchError extends SearchState {
  final String error;

  SearchError(this.error);
  
  @override
  List<Object?> get props => [error];
}

class SearchSuccessState extends SearchState {
  final SearchModel search;
  const SearchSuccessState(this.search);

  @override
  List<Object?> get props => [search];
}

class SearchCentersByCategorySuccessState extends SearchState {
  final SearchModel searchs;

  SearchCentersByCategorySuccessState({required this.searchs});
  
  @override
  List<Object?> get props => [searchs];
}

class NearbyCentersSuccessState extends SearchState {
  final SearchModel searchs;

  NearbyCentersSuccessState({required this.searchs});
  
  @override
  List<Object?> get props => [searchs];
}
