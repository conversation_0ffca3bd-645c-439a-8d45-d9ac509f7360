import 'package:class_z/core/imports.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter/services.dart';

part 'search_event.dart';
part 'search_state.dart';

class SearchBloc extends Bloc<SearchEvent, SearchState> {
  final SearchQueryUseCase _searchQueryUseCase;
  final SearchQueryByCategoryUseCase _searchQueryByCategoryUseCase;
  final SearchRepoDomain _searchRepoDomain;
  bool _isLoading = false;

  SearchBloc(this._searchQueryUseCase, this._searchQueryByCategoryUseCase,
      this._searchRepoDomain)
      : super(SearchInitial()) {
    on<SearchQueryEvent>(_searchQueryEvent);
    on<SearchCenterByCategory>(_SearchCenterByCategory);
    on<FindNearbyCenters>(_findNearbyCenters);
    on<ClearSearchEvent>(_clearSearchEvent);
  }

  FutureOr<void> _searchQueryEvent(
      SearchQueryEvent event, Emitter<SearchState> emit) async {
    if (_isLoading) return;
    try {
      _isLoading = true;
      emit(SearchLoading());
      SearchModel search = await _searchQueryUseCase.call(
        event.query,
        event.ageFrom,
        event.ageTo,
        event.location,
        event.sortBy,
        rating: event.rating,
        senService: event.senService,
        isSearchingCoach: event.isSearchingCoach,
      );
      emit(SearchSuccessState(search));
    } catch (e) {
      print('Search error: ${e.toString()}');
      emit(SearchError(e.toString()));
    } finally {
      _isLoading = false;
    }
  }

  FutureOr<void> _SearchCenterByCategory(
      SearchCenterByCategory event, Emitter<SearchState> emit) async {
    if (_isLoading) return;
    try {
      _isLoading = true;
      emit(SearchLoading());

      // Diagnostic logging
      print("Received location filter: '${event.location}'");
      final isYourLocation = event.location?.trim() == 'Your location';
      print("Is 'Your location'?: $isYourLocation");

      // Handle "Your location" search
      if (isYourLocation) {
        print('Location is "Your location", finding nearby centers...');
        try {
          // Check for location permissions first
          LocationPermission permission = await Geolocator.checkPermission();
          if (permission == LocationPermission.denied) {
            permission = await Geolocator.requestPermission();
            if (permission == LocationPermission.denied) {
              throw Exception('Location permissions are denied');
            }
          }

          if (permission == LocationPermission.deniedForever) {
            // Permissions are denied forever, handle appropriately.
            throw Exception(
                'Location permissions are permanently denied, we cannot request permissions.');
          }

          // Get current location
          Position position = await Geolocator.getCurrentPosition(
              desiredAccuracy: LocationAccuracy.high);

          // Use the nearby centers logic
          SearchModel searchResult = await _searchRepoDomain.getNearbyCenters(
            latitude: position.latitude,
            longitude: position.longitude,
            maxDistance:
                2000, // Default search radius, can be made configurable
            priceMin: event.priceMin,
            priceMax: event.priceMax,
            ageFrom: event.ageFrom,
            ageTo: event.ageTo,
            sortBy: event.sortBy,
            rating: event.rating,
            senService: event.senService,
          );
          emit(NearbyCentersSuccessState(searchs: searchResult));
        } on PlatformException catch (e) {
          if (e.code == 'LOCATION_SERVICES_DISABLED') {
            emit(SearchError(
                'Location services are disabled. Please enable them in your settings.'));
          } else {
            emit(SearchError('Could not get location: ${e.message}'));
          }
        }
      } else {
        // Existing logic for category search
        print(
            'Location is not "Your location". Searching by category: ${event.category}');
        SearchModel searchResult = await _searchQueryByCategoryUseCase.call(
          query: event.category,
          ageFrom: event.ageFrom,
          ageTo: event.ageTo,
          location: event.location,
          priceMax: event.priceMax,
          priceMin: event.priceMin,
          sortBy: event.sortBy,
          rating: event.rating,
          senService: event.senService,
        );

        emit(SearchCentersByCategorySuccessState(searchs: searchResult));
      }
    } catch (e) {
      print('Category search error: ${e.toString()}');
      emit(SearchError(e.toString()));
    } finally {
      _isLoading = false;
    }
  }

  FutureOr<void> _findNearbyCenters(
      FindNearbyCenters event, Emitter<SearchState> emit) async {
    if (_isLoading) return;
    try {
      _isLoading = true;
      emit(SearchLoading());

      SearchModel searchResult = await _searchRepoDomain.getNearbyCenters(
          longitude: event.longitude,
          latitude: event.latitude,
          maxDistance: event.maxDistance,
          priceMin: event.priceMin,
          priceMax: event.priceMax,
          ageFrom: event.ageFrom,
          ageTo: event.ageTo,
          sortBy: event.sortBy,
          rating: event.rating,
          senService: event.senService);

      emit(NearbyCentersSuccessState(searchs: searchResult));
    } catch (e) {
      print('Nearby centers error: ${e.toString()}');
      emit(SearchError(e.toString()));
    } finally {
      _isLoading = false;
    }
  }

  FutureOr<void> _clearSearchEvent(
      ClearSearchEvent event, Emitter<SearchState> emit) {
    emit(SearchInitial());
  }
}
