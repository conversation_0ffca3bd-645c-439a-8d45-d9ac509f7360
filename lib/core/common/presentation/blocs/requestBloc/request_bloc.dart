import 'package:class_z/core/common/domain/usecase/cancel_join_request_use_case.dart';
import 'package:class_z/core/imports.dart';

part 'request_event.dart';
part 'request_state.dart';

class RequestBloc extends Bloc<RequestEvent, RequestState> {
  final SendJoinRequestUseCase sendJoinRequest;
  final GetRequestsByCenterUseCase getByCenter;
  final GetRequestsByCoachUseCase getByCoach;
  final UpdateRequestStatusUseCase updateStatus;
  final CancelJoinRequestUseCase cancelJoinRequest;
  final CheckExistingRequestUseCase checkExistingRequest;

  RequestBloc({
    required this.sendJoinRequest,
    required this.getByCenter,
    required this.getByCoach,
    required this.updateStatus,
    required this.cancelJoinRequest,
    required this.checkExistingRequest,
  }) : super(RequestInitial()) {
    on<SendJoinRequestEvent>(_onSendJoinRequest);
    on<UpdateRequestStatusEvent>(_onUpdateRequestStatus);
    on<GetRequestsByCenterEvent>(_onGetRequestsByCenter);
    on<GetRequestsByCoachEvent>(_onGetRequestsByCoach);
    on<CancelJoinRequestEvent>(_onCancelJoinRequest);
    on<CheckExistingRequestEvent>(_onCheckExistingRequest);
  }

  Future<void> _onSendJoinRequest(
    SendJoinRequestEvent event,
    Emitter<RequestState> emit,
  ) async {
    emit(RequestLoading());
    final response = await sendJoinRequest(event.coachId, event.centerId);
    response.fold((failure) {
      print(failure.message);
      emit(RequestError(failure.message));
    }, (request) => emit(RequestSentSuccessState(request)));
  }

  Future<void> _onUpdateRequestStatus(
    UpdateRequestStatusEvent event,
    Emitter<RequestState> emit,
  ) async {
    emit(RequestLoading());
    final response = await updateStatus(event.requestId, event.status);
    response.fold(
        (failure) => emit(RequestError(failure.message)),
        (updatedRequest) =>
            emit(RequestStatusUpdatedSuccessState(updatedRequest)));
  }

  Future<void> _onGetRequestsByCenter(
    GetRequestsByCenterEvent event,
    Emitter<RequestState> emit,
  ) async {
    emit(RequestLoading());
    final response = await getByCenter(event.centerId);
    response.fold((failure) => emit(RequestError(failure.message)),
        (requests) => emit(RequestSuccessState(requests)));
  }

  Future<void> _onGetRequestsByCoach(
    GetRequestsByCoachEvent event,
    Emitter<RequestState> emit,
  ) async {
    emit(RequestLoading());
    final response = await getByCoach(event.coachId);
    response.fold((failure) => emit(RequestError(failure.message)),
        (requests) => emit(RequestSuccessState(requests)));
  }

  Future<void> _onCancelJoinRequest(
    CancelJoinRequestEvent event,
    Emitter<RequestState> emit,
  ) async {
    emit(RequestLoading());
    final response = await cancelJoinRequest(event.coachId, event.centerId);
    response.fold(
      (failure) => emit(RequestError(failure.message)),
      (success) => emit(RequestCancelledSuccessState(success)),
    );
  }

  Future<void> _onCheckExistingRequest(
    CheckExistingRequestEvent event,
    Emitter<RequestState> emit,
  ) async {
    final response = await checkExistingRequest(event.coachId, event.centerId);
    response.fold(
      (failure) => emit(RequestError(failure.message)),
      (result) => emit(RequestExistenceCheckedState(
        exists: result['exists'] ?? false,
        status: result['status'] ?? 'initial',
        coachId: event.coachId,
        centerId: event.centerId,
        request: result['request'],
      )),
    );
  }
}
