part of 'request_bloc.dart';

abstract class RequestState extends Equatable {
  @override
  List<Object?> get props => [];
}

class RequestInitial extends RequestState {}

class RequestLoading extends RequestState {}

class RequestSuccessState extends RequestState {
  final List<RequestEntity>? requests;

  RequestSuccessState(this.requests);

  @override
  List<Object> get props => [requests ?? []];
}

class RequestSentSuccessState extends RequestState {
  final bool success;

  RequestSentSuccessState(this.success);

  @override
  List<Object> get props => [success];
}

class RequestStatusUpdatedSuccessState extends RequestState {
  final bool status;

  RequestStatusUpdatedSuccessState(this.status);

  @override
  List<Object> get props => [status];
}

class RequestError extends RequestState {
  final String message;

  RequestError(this.message);

  @override
  List<Object> get props => [message];
}

class RequestCancelledSuccessState extends RequestState {
  final bool success;

  RequestCancelledSuccessState(this.success);

  @override
  List<Object> get props => [success];
}

class RequestExistenceCheckedState extends RequestState {
  final bool exists;
  final String status;
  final String coachId;
  final String centerId;
  final Map<String, dynamic>? request;

  RequestExistenceCheckedState({
    required this.exists,
    required this.status,
    required this.coachId,
    required this.centerId,
    this.request,
  });

  @override
  List<Object?> get props => [exists, status, coachId, centerId, request];
}
