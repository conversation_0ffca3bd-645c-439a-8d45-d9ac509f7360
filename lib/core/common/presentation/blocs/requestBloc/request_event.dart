part of 'request_bloc.dart';

abstract class RequestEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class SendJoinRequestEvent extends RequestEvent {
  final String coachId;
  final String centerId;

  SendJoinRequestEvent({required this.coachId, required this.centerId});

  @override
  List<Object> get props => [coachId, centerId];
}

class UpdateRequestStatusEvent extends RequestEvent {
  final String requestId;
  final String status;

  UpdateRequestStatusEvent({required this.requestId, required this.status});

  @override
  List<Object> get props => [requestId, status];
}

class GetRequestsByCenterEvent extends RequestEvent {
  final String centerId;

  GetRequestsByCenterEvent({required this.centerId});

  @override
  List<Object> get props => [centerId];
}

class GetRequestsByCoachEvent extends RequestEvent {
  final String coachId;

  GetRequestsByCoachEvent({required this.coachId});

  @override
  List<Object> get props => [coachId];
}

class CancelJoinRequestEvent extends RequestEvent {
  final String coachId;
  final String centerId;

  CancelJoinRequestEvent({required this.coachId, required this.centerId});

  @override
  List<Object> get props => [coachId, centerId];
}

class CheckExistingRequestEvent extends RequestEvent {
  final String coachId;
  final String centerId;

  CheckExistingRequestEvent({required this.coachId, required this.centerId});

  @override
  List<Object> get props => [coachId, centerId];
}
