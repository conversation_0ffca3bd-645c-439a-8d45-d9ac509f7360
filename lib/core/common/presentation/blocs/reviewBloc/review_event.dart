part of 'review_bloc.dart';

abstract class ReviewEvent {}

class PostReviewEvent extends ReviewEvent {
  final Map<String, dynamic> payload;

  PostReviewEvent({required this.payload});
}

class PostReviewByParentEvent extends ReviewEvent {
  final Map<String, dynamic> payload;

  PostReviewByParentEvent({required this.payload});
}

class GetReviewByIdEvent extends ReviewEvent {
  final String id;
  final String type;

  GetReviewByIdEvent({required this.id, required this.type});
}

class GetReviewByIdandClassEvent extends ReviewEvent {
  final String id;
  final String type;
  final String classId;
  GetReviewByIdandClassEvent(
      {required this.id, required this.type, required this.classId});
}

class GetMomentsEvent extends ReviewEvent {
  final String childId;

  GetMomentsEvent({required this.childId});
}
