import 'package:class_z/core/imports.dart';

part 'review_event.dart';
part 'review_state.dart';

class ReviewBloc extends Bloc<ReviewEvent, ReviewState> {
  final ReviewUseCase reviewUseCase;
  final PostReviewByParentUseCase postReviewByParentUseCase;
  final GetMomentsUseCase getMomentsUseCase;

  ReviewBloc(
      {required this.reviewUseCase,
      required this.postReviewByParentUseCase,
      required this.getMomentsUseCase})
      : super(ReviewInitial()) {
    on<PostReviewEvent>(postReviewEvent);
    on<PostReviewByParentEvent>(_postReviewByParent);
    on<GetReviewByIdEvent>(getReviewByIdEvent);
    on<GetReviewByIdandClassEvent>(getReviewByIdandClassEvent);
    on<GetMomentsEvent>(_getMomentsEvent);
  }

  FutureOr<void> postReviewEvent(
      PostReviewEvent event, Emitter<ReviewState> emit) async {
    try {
      emit(ReviewLoadingState());
      bool review = await reviewUseCase.postReview(payload: event.payload);
      emit(PostReviewSuccessState(review: review));
    } catch (e) {
      emit(ReviewErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _postReviewByParent(
      PostReviewByParentEvent event, Emitter<ReviewState> emit) async {
    emit(ReviewLoadingState());
    final review = await postReviewByParentUseCase.call(payload: event.payload);
    review.fold((failure) => emit(ReviewErrorState(message: failure.message)),
        (success) {
      print('success: $success');
      emit(PostReviewSuccessState(review: success));
    });
  }

  FutureOr<void> getReviewByIdEvent(
      GetReviewByIdEvent event, Emitter<ReviewState> emit) async {
    try {
      emit(ReviewLoadingState());
      List<ReviewModel> reviews =
          await reviewUseCase.getReviewById(id: event.id, type: event.type);
      if (event.type == 'coach')
        emit(GetReviewByCoachIdSuccessState(reviews: reviews));
      else
        emit(GetReviewByCenterIdSuccessState(reviews: reviews));
    } catch (e) {
      emit(ReviewErrorState(message: e.toString()));
    }
  }

  FutureOr<void> getReviewByIdandClassEvent(
      GetReviewByIdandClassEvent event, Emitter<ReviewState> emit) async {
    try {
      emit(ReviewLoadingState());
      ReviewModel review = await reviewUseCase.getReviewByIdandClass(
          classId: event.classId, id: event.id, type: event.type);
      emit(GetReviewByIdandClassSuccessState(review: review));
    } catch (e) {
      emit(ReviewErrorState(message: e.toString()));
    }
  }

  FutureOr<void> _getMomentsEvent(
      GetMomentsEvent event, Emitter<ReviewState> emit) async {
    try {
      emit(ReviewLoadingState());
      List<ReviewModel> moments =
          await getMomentsUseCase.call(childId: event.childId);
      emit(GetMomentsSuccessState(moments: moments));
    } catch (e) {
      emit(ReviewErrorState(message: e.toString()));
    }
  }
}
