part of 'review_bloc.dart';

abstract class ReviewState {}

class ReviewInitial extends ReviewState {}

class ReviewLoadingState extends ReviewState {}

class ReviewErrorState extends ReviewState {
  final String message;

  ReviewErrorState({required this.message});
}

class PostReviewSuccessState extends ReviewState {
  final bool review;

  PostReviewSuccessState({required this.review});
}

class GetReviewByCoachIdSuccessState extends ReviewState {
  final List<ReviewModel> reviews;

  GetReviewByCoachIdSuccessState({required this.reviews});
}

class GetReviewByCenterIdSuccessState extends ReviewState {
  final List<ReviewModel> reviews;

  GetReviewByCenterIdSuccessState({required this.reviews});
}

class GetReviewByIdandClassSuccessState extends ReviewState {
  final ReviewModel review;

  GetReviewByIdandClassSuccessState({required this.review});
}

class GetMomentsSuccessState extends ReviewState {
  final List<ReviewModel> moments;

  GetMomentsSuccessState({required this.moments});
}
