part of 'notification_bloc.dart';

abstract class NotificationState extends Equatable {
  const NotificationState();
  get props => [];
}

class NotificationInitial extends NotificationState {}

class NotificationLoading extends NotificationState {}

class NotificationLoaded extends NotificationState {
  final List<NotificationEntity> notifications;

  NotificationLoaded({required this.notifications});
}

class NotificationMarkedRead extends NotificationState {
  final String notificationId;

  NotificationMarkedRead(this.notificationId);
}

class NotificationError extends NotificationState {
  final String message;

  NotificationError({required this.message});
}

class DeviceTokenChecked extends NotificationState {
  DeviceTokenChecked();
}

class NotificationDeleted extends NotificationState {
  final bool success;

  NotificationDeleted({required this.success});
}
