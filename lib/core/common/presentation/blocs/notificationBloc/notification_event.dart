part of 'notification_bloc.dart';

abstract class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object?> get props => [];
}

class GetNotificationsEvent extends NotificationEvent {
  final String userId;

  GetNotificationsEvent({required this.userId});

  @override
  List<Object?> get props => [userId];
}

class MarkNotificationReadEvent extends NotificationEvent {
  final String notificationId;

  MarkNotificationReadEvent({required this.notificationId});
}

class RefreshNotificationsEvent extends NotificationEvent {
  final String deviceToken;

  RefreshNotificationsEvent({required this.deviceToken});
}

class DeleteNotificationEvent extends NotificationEvent {
  final String notificationId;

  DeleteNotificationEvent({required this.notificationId});
}

class CheckDeviceTokenEvent extends NotificationEvent {
  final String deviceToken;
  final String id;

  CheckDeviceTokenEvent({required this.deviceToken, required this.id});
}
