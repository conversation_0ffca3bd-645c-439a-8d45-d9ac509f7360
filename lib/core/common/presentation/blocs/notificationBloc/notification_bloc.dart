import 'dart:async';

import 'package:class_z/features/notification/domain/entity/notification.dart';
import 'package:class_z/features/notification/domain/use_case/notification_use_case.dart';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'notification_event.dart';
part 'notification_state.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final GetNotifications getNotifications;
  final ReadNotification readNotification;
  final DeleteNotification deleteNotification;
  final CheckDeviceToken checkDeviceToken;

  List<NotificationEntity> _cachedNotifications = [];

  NotificationBloc({
    required this.getNotifications,
    required this.readNotification,
    required this.deleteNotification,
    required this.checkDeviceToken,
  }) : super(NotificationInitial()) {
    on<GetNotificationsEvent>(_getNotificationsEvent);
    on<DeleteNotificationEvent>(_deleteNotificationEvent);
    on<CheckDeviceTokenEvent>(_checkDeviceTokenEvent);
  }
  List<NotificationEntity> get cachedNotifications => _cachedNotifications;

  /// Fetch notifications with cache check
  FutureOr<void> _getNotificationsEvent(
      GetNotificationsEvent event, Emitter<NotificationState> emit) async {
    try {
      emit(NotificationLoading());

      if (event.userId.isEmpty) {
        throw Exception('User ID cannot be null or empty');
      }

      print('Fetching notifications for user: ${event.userId}');
      final result = await getNotifications(event.userId);

      result.fold(
        (failure) {
          print('Error fetching notifications: ${failure.message}');
          emit(NotificationError(message: 'Failed to load notifications'));
        },
        (notifications) {
          print('Successfully fetched ${notifications.length} notifications');
          _cachedNotifications = notifications; // ✅ Save to cache
          emit(NotificationLoaded(notifications: notifications));
        },
      );
    } catch (e, stackTrace) {
      print('Unexpected error in _getNotificationsEvent: $e');
      print('Stack trace: $stackTrace');
      emit(NotificationError(message: 'An unexpected error occurred'));
    }
  }

  /// Delete and update cache
  FutureOr<void> _deleteNotificationEvent(
      DeleteNotificationEvent event, Emitter<NotificationState> emit) async {
    emit(NotificationLoading());

    final result = await deleteNotification.call(event.notificationId);
    result.fold(
      (failure) => emit(NotificationError(message: failure.message)),
      (success) {
        _cachedNotifications
            .removeWhere((n) => n.id == event.notificationId); // ✅ Update cache
        emit(NotificationDeleted(success: success));
        emit(NotificationLoaded(
            notifications:
                _cachedNotifications)); // ✅ Reload with updated cache
      },
    );
  }

  /// Just perform the check
  FutureOr<void> _checkDeviceTokenEvent(
      CheckDeviceTokenEvent event, Emitter<NotificationState> emit) async {
    emit(NotificationLoading());
    final result = await checkDeviceToken(event.deviceToken, event.id);
    result.fold(
      (failure) => emit(NotificationError(message: failure.message)),
      (_) => emit(NotificationInitial()),
    );
  }
}
