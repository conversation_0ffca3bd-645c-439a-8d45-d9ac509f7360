import 'dart:async';

import 'package:class_z/features/roles/coach/data/models/coach_model.dart';
import 'package:class_z/core/common/data/models/user_model.dart';
import 'package:class_z/features/authentications/domain/repositories/auth_repo_domain.dart';
import 'package:class_z/features/authentications/domain/usecases/auth_usecase.dart';
import 'package:class_z/core/utils/shared_repo.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final SignUpUseCase signUpUseCase;
  final SharedRepository sharedRepository;
  final SignInUseCase signInUseCase;
  final GetUserUseCase getUserUseCase;
  // final UserInfoCompleterUseCase userInfoCompleterUseCase;
  // final CoachInfoCompleterUseCase coachInfoCompleterUseCase;
  final TokenValidationUseCase tokenValidationUseCase;
  final AuthRepositoryDomain authRepository;
  final ParentInfoCompleterUseCase parentInfoCompleterUseCase;
  final SendOTPUseCase sendOTPUseCase;
  final VerifyOTPUseCase verifyOTPUseCase;
  final ResetPasswordUseCase resetPasswordUseCase;
  AuthBloc(
      {required this.sharedRepository,
      required this.signUpUseCase,
      required this.signInUseCase,
      required this.getUserUseCase,
      required this.tokenValidationUseCase,
      required this.parentInfoCompleterUseCase,
      required this.authRepository,
      required this.sendOTPUseCase,
      required this.verifyOTPUseCase,
      required this.resetPasswordUseCase})
      : super(AuthInitial()) {
    on<SignInEvent>(_signInEvent);
    on<SignUpEvent>(signUpEvent);

    on<TokenValidationEvent>(tokenValidationEvent);
    on<ParentInfoCompleteEvent>(parentInfoCompleteEvent);
    on<CoachAccountInfoCompleteEvent>(coachAccountInfoCompleteEvent);
    on<GetUserEvent>(getUserEvent);
    on<SendOTPEvent>(sendOTPEvent);
    on<VerifyOTPEvent>(verifyOTPEvent);
    on<ResetPasswordEvent>(resetPasswordEvent);
    on<RefreshUserEvent>(refreshUserEvent);
    on<UpdateUserEvent>(_updateUserEvent);
  }

  FutureOr<void> _signInEvent(
      SignInEvent event, Emitter<AuthState> emit) async {
    try {
      emit(AuthLoading());

      final result = await signInUseCase.call(
        email: event.email,
        password: event.password,
        type: event.type,
      );

      // Check if result is a Right instance containing UserModel
      result.fold(
        (failure) {
          emit(AuthError(failure.toString()));
        },
        (userModel) {
          emit(AuthSignInSuccess(userModel));
        },
      );
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  FutureOr<void> _updateUserEvent(
      UpdateUserEvent event, Emitter<AuthState> emit) async {
    try {
      final result = await authRepository.updateUser(event.id, event.data);
      result.fold(
        (failure) => emit(AuthError(failure.toString())),
        (userModel) {
          emit(UserLoaded(userModel));
        },
      );
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  Future<void> signUpEvent(SignUpEvent event, Emitter<AuthState> emit) async {
    emit(AuthLoading());
    try {
      final user = await signUpUseCase.call(
          email: event.email,
          password: event.password,
          type: event.type,
          otp: event.otp);

      emit(AuthSignUpSuccess(user));
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  FutureOr<void> tokenValidationEvent(
    TokenValidationEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());

      // Check if token is empty
      if (event.token.isEmpty) {
        print("Token is empty or null in tokenValidationEvent");
        emit(TokenValidationSuccess(answer: false));
        return;
      }

      final checktoken = await tokenValidationUseCase.call(token: event.token);
      emit(TokenValidationSuccess(answer: checktoken));
    } catch (e) {
      print("Error in tokenValidationEvent: ${e.toString()}");
      emit(TokenValidationSuccess(
          answer: false)); // Fail gracefully instead of throwing error
    }
  }

  FutureOr<void> parentInfoCompleteEvent(
    ParentInfoCompleteEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());
      UserModel user =
          await parentInfoCompleterUseCase.call(updateData: event.updateData);
      emit(ParnetAccountInfoSuccessState(user: user));
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  FutureOr<void> coachAccountInfoCompleteEvent(
    CoachAccountInfoCompleteEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());
      // final coach = await coachInfoCompleterUseCase.call(
      //   type: event.type,
      //   nickname: event.nickname,
      //   fullname: event.fullname,
      // );

      // emit(CoachAccountInfoSuccessState(coach: coach));
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  FutureOr<void> getUserEvent(
      GetUserEvent event, Emitter<AuthState> emit) async {
    try {
      final user = await getUserUseCase.execute();
      emit(UserLoaded(user!));
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  FutureOr<void> sendOTPEvent(
      SendOTPEvent event, Emitter<AuthState> emit) async {
    try {
      await sendOTPUseCase.call(email: event.email);
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  FutureOr<void> verifyOTPEvent(
      VerifyOTPEvent event, Emitter<AuthState> emit) async {
    try {
      emit(VerifyLoading());
      bool success =
          await verifyOTPUseCase.call(email: event.email, otp: event.otp);
      emit(OTPVerifiedState(success: success));
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  FutureOr<void> resetPasswordEvent(
      ResetPasswordEvent event, Emitter<AuthState> emit) async {
    try {
      emit(AuthLoading());
      bool success = await resetPasswordUseCase.call(
        email: event.email,
        password: event.password,
      );
      emit(ResetPasswordSuccessState(success: success));
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  FutureOr<void> refreshUserEvent(
      RefreshUserEvent event, Emitter<AuthState> emit) async {
    try {
      final user = await getUserUseCase.execute();
      if (user != null) {
        emit(UserLoaded(user));
      }
    } catch (e) {
      // Don't emit error state to avoid disrupting the UI
    }
  }
}
