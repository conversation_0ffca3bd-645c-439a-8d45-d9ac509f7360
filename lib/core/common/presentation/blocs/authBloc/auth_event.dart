part of 'auth_bloc.dart';

abstract class AuthEvent {}

class SignInEvent extends AuthEvent {
  final String email;
  final String password;
  final String? type;

  SignInEvent({required this.email, required this.password, this.type});
}

class GetUserEvent extends AuthEvent {}

class RefreshUserEvent extends AuthEvent {}

class SignUpEvent extends AuthEvent {
  final String email;
  final String password;
  final String type;
  final String otp;

  SignUpEvent(
      {required this.email,
      required this.password,
      required this.type,
      required this.otp});
}

class SignOutEvent extends AuthEvent {}

class TokenValidationEvent extends AuthEvent {
  final String token;

  TokenValidationEvent(this.token);
}

class GetTokenEvent extends AuthEvent {}

class AuthTokenError extends AuthEvent {}

class ParentInfoCompleteEvent extends AuthEvent {
  final Map<String, dynamic> updateData;

  ParentInfoCompleteEvent({required this.updateData});
}

class CoachAccountInfoCompleteEvent extends AuthEvent {
  final String type;
  final String nickname;
  final String fullname;

  CoachAccountInfoCompleteEvent({
    required this.type,
    required this.nickname,
    required this.fullname,
  });
}

class SendOTPEvent extends AuthEvent {
  final String email;

  SendOTPEvent({required this.email});
}

class VerifyOTPEvent extends AuthEvent {
  final String email;
  final String otp;

  VerifyOTPEvent({required this.email, required this.otp});
}

class ResetPasswordEvent extends AuthEvent {
  final String email;
  final String password;

  ResetPasswordEvent({required this.email, required this.password});
}

class UpdateUserEvent extends AuthEvent {
  final String id;
  final Map<String, dynamic> data;

  UpdateUserEvent({required this.id, required this.data});
}
