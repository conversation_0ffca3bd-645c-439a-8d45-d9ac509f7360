part of 'auth_bloc.dart';

abstract class AuthState {}

class AuthInitial extends AuthState {}

///sign in
class AuthLoading extends AuthState {}
class VerifyLoading extends AuthState{}
class AuthError extends AuthState {
  final String message;

  AuthError(this.message);
}

class AuthSignInSuccess extends AuthState {
  final UserModel user;

  AuthSignInSuccess(this.user);
}

///user data
class UserLoaded extends AuthState {
  final UserModel user;

  UserLoaded(this.user);
}

///sign up
class AuthSignUpLoading extends AuthState {}

class AuthSignUpSuccess extends AuthState {
  final bool user;

  AuthSignUpSuccess(this.user);
}

class AuthSignedOut extends AuthState {}

///token validation
class TokenValidationSuccess extends AuthState {
  final bool answer;

  TokenValidationSuccess({required this.answer});
}

class TokenRetrieveSuccess extends AuthState {
  final String? token;

  TokenRetrieveSuccess(this.token);
}

///update
class ParnetAccountInfoSuccessState extends AuthState {
  final UserModel user;

  ParnetAccountInfoSuccessState({required this.user});
}

class CoachAccountInfoSuccessState extends AuthState {
  final CoachModel coach;

  CoachAccountInfoSuccessState({required this.coach});
}

class OTPVerifiedState extends AuthState {
  final bool success;

  OTPVerifiedState({required this.success});
}

class ResetPasswordSuccessState extends AuthState {
  final bool success;

  ResetPasswordSuccessState({required this.success});
}