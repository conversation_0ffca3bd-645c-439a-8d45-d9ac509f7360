import 'package:class_z/core/imports.dart';
import 'package:class_z/core/common/domain/repositories/saved_center_repository.dart';

part 'saved_center_event.dart';
part 'saved_center_state.dart';

class SavedCenterBloc extends Bloc<SavedCenterEvent, SavedCenterState> {
  final SavedCenterRepository savedCenterRepository;
  final SharedRepository sharedRepository;

  SavedCenterBloc({
    required this.savedCenterRepository,
    required this.sharedRepository,
  }) : super(SavedCenterInitial()) {
    on<SaveCenterEvent>(_saveCenterEvent);
    on<UnsaveCenterEvent>(_unsaveCenterEvent);
    on<GetSavedCentersEvent>(_getSavedCentersEvent);
    on<CheckCenterSavedEvent>(_checkCenterSavedEvent);
    on<AuthenticationErrorEvent>(_handleAuthenticationError);
  }

  Future<String?> _getParentId() async {
    try {
      // First try to get parent ID from parent data
      final parentData = sharedRepository.getParentData();
      if (parentData != null &&
          parentData.id != null &&
          parentData.id!.isNotEmpty) {
        return parentData.id;
      }

      // If parent data is null or empty, try to get from token
      final userModel = sharedRepository.getUserData();
      if (userModel?.token != null && userModel!.token.isNotEmpty) {
        try {
          // Decode JWT token to extract user info
          final parts = userModel.token.split('.');
          if (parts.length == 3) {
            final payload = parts[1];
            // Add padding if needed for base64 decoding
            String normalizedPayload = payload;
            switch (payload.length % 4) {
              case 1:
                normalizedPayload += '===';
                break;
              case 2:
                normalizedPayload += '==';
                break;
              case 3:
                normalizedPayload += '=';
                break;
            }

            final decoded = utf8.decode(base64Url.decode(normalizedPayload));
            final payloadMap = json.decode(decoded);

            if (payloadMap['id'] != null) {
              return payloadMap['id'].toString();
            }
          }
        } catch (e) {
          // Token decoding failed, continue to try other methods
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> _saveCenterEvent(
    SaveCenterEvent event,
    Emitter<SavedCenterState> emit,
  ) async {
    try {
      emit(SavedCenterLoading());

      // Get parent ID using the proper method
      final parentId = await _getParentId();
      if (parentId == null || parentId.isEmpty) {
        // Prompt user to log in when attempting to save a centre without authentication
        emit(SavedCenterError('Please log in to save centres'));
        return;
      }

      try {
        final success = await savedCenterRepository.saveCenter(
          parentId: parentId,
          centerId: event.centerId,
        );

        if (success) {
          emit(CenterSavedSuccess(event.centerId));
        } else {
          emit(SavedCenterError('Failed to save center'));
        }
      } catch (repoError) {
        // Check if it's an authentication error
        if (repoError.toString().contains('Authentication failed')) {
          emit(SavedCenterAuthError(repoError));
        } else {
          emit(SavedCenterError(repoError));
        }
      }
    } catch (e) {
      emit(SavedCenterError(e));
    }
  }

  Future<void> _unsaveCenterEvent(
    UnsaveCenterEvent event,
    Emitter<SavedCenterState> emit,
  ) async {
    try {
      emit(UnSavedCenterLoading());

      final parentId = await _getParentId();
      if (parentId == null) {
        // Prompt user to log in when attempting to unsave a centre without authentication
        emit(SavedCenterError('Please log in to manage saved centres'));
        return;
      }

      final success = await savedCenterRepository.unsaveCenter(
        parentId: parentId,
        centerId: event.centerId,
      );

      if (success) {
        emit(CenterUnsavedSuccess(event.centerId));
      } else {
        emit(SavedCenterError('Failed to unsave center'));
      }
    } catch (e) {
      emit(SavedCenterError(e.toString()));
    }
  }

  Future<void> _getSavedCentersEvent(
    GetSavedCentersEvent event,
    Emitter<SavedCenterState> emit,
  ) async {
    try {
      emit(SavedCenterLoading());

      final parentId = await _getParentId();
      if (parentId == null) {
        // User is not logged in – silently return an empty list instead of an error
        emit(SavedCentersLoaded([]));
        return;
      }

      final centers = await savedCenterRepository.getSavedCenters(
        parentId: parentId,
      );

      emit(SavedCentersLoaded(centers));
    } catch (e) {
      emit(SavedCenterError(e.toString()));
    }
  }

  Future<void> _checkCenterSavedEvent(
    CheckCenterSavedEvent event,
    Emitter<SavedCenterState> emit,
  ) async {
    try {
      final parentId = await _getParentId();
      if (parentId == null) {
        // No logged-in user – consider the centre as not saved without raising an error
        emit(CenterSavedStatus(event.centerId, false));
        return;
      }

      final isSaved = await savedCenterRepository.isCenterSaved(
        parentId: parentId,
        centerId: event.centerId,
      );

      emit(CenterSavedStatus(event.centerId, isSaved));
    } catch (e) {
      emit(SavedCenterError(e.toString()));
    }
  }

  Future<void> _handleAuthenticationError(
    AuthenticationErrorEvent event,
    Emitter<SavedCenterState> emit,
  ) async {
    try {
      // Clear the token from storage since it's invalid
      await sharedRepository.deleteToken();

      // Emit the auth error state to trigger the login dialog
      emit(SavedCenterAuthError(event.error));
    } catch (e) {
      emit(SavedCenterError('Failed to handle authentication error: $e'));
    }
  }
}
