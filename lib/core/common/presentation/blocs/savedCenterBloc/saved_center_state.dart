part of 'saved_center_bloc.dart';

abstract class SavedCenterState extends Equatable {
  const SavedCenterState();

  @override
  List<Object?> get props => [];
}

class SavedCenterInitial extends SavedCenterState {}

class SavedCenterLoading extends SavedCenterState {}

class UnSavedCenterLoading extends SavedCenterState {}

class SavedCenterError extends SavedCenterState {
  final dynamic error;

  const SavedCenterError(this.error);

  String get message => error.toString();

  @override
  List<Object?> get props => [error];
}

class SavedCenterAuthError extends SavedCenterState {
  final dynamic error;

  const SavedCenterAuthError(this.error);

  String get message => 'Authentication failed - please log in again';

  @override
  List<Object?> get props => [error];
}

class CenterSavedSuccess extends SavedCenterState {
  final String centerId;

  const CenterSavedSuccess(this.centerId);

  @override
  List<Object?> get props => [centerId];
}

class CenterUnsavedSuccess extends SavedCenterState {
  final String centerId;

  const CenterUnsavedSuccess(this.centerId);

  @override
  List<Object?> get props => [centerId];
}

class SavedCentersLoaded extends SavedCenterState {
  final List<CenterData> centers;

  const SavedCentersLoaded(this.centers);

  @override
  List<Object?> get props => [centers];
}

class CenterSavedStatus extends SavedCenterState {
  final String centerId;
  final bool isSaved;

  const CenterSavedStatus(this.centerId, this.isSaved);

  @override
  List<Object?> get props => [centerId, isSaved];
}
