part of 'saved_center_bloc.dart';

abstract class SavedCenterEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class SaveCenterEvent extends SavedCenterEvent {
  final String centerId;

  SaveCenterEvent(this.centerId);

  @override
  List<Object?> get props => [centerId];
}

class UnsaveCenterEvent extends SavedCenterEvent {
  final String centerId;

  UnsaveCenterEvent(this.centerId);

  @override
  List<Object?> get props => [centerId];
}

class GetSavedCentersEvent extends SavedCenterEvent {
  GetSavedCentersEvent();

  @override
  List<Object?> get props => [];
}

class CheckCenterSavedEvent extends SavedCenterEvent {
  final String centerId;

  CheckCenterSavedEvent(this.centerId);

  @override
  List<Object?> get props => [centerId];
}

class AuthenticationErrorEvent extends SavedCenterEvent {
  final Exception error;

  AuthenticationErrorEvent(this.error);

  @override
  List<Object?> get props => [error];
}
