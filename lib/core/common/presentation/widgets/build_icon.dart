// ignore_for_file: file_names

import 'package:class_z/config/themes/app_pallate.dart';
import 'package:flutter/material.dart';

class CustomIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final Color? color;
  final double? width;
  final double? height;
  final double? iconSize;

  const CustomIconButton(
      {Key? key,
      required this.icon,
      required this.onPressed,
      this.iconSize = 24,
      this.color = AppPallete.secondaryColor,
      this.height = 24,
      this.width = 24})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width,
      child: IconButton(
        iconSize: iconSize,
        onPressed: onPressed,
        icon: Icon(icon, color: color),
      ),
    );
  }
}
