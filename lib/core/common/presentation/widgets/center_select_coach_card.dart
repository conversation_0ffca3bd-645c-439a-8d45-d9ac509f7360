import 'package:class_z/core/imports.dart';
Widget centerSelectCoachCard(
    {required BuildContext context,
    required String imagePath,
    required double rating,
    required String name,
    required VoidCallback onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      height: 197.h,
      width: 184.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Stack(
        children: [
          CustomImageBuilder(
            imagePath: imagePath,
            height: 197.h,
            width: 184.w,
            borderRadius: 20.r,
          ),
          Positioned(
            top: 15.h,
            left: 10.w,
            child: Container(
              decoration: BoxDecoration(
                  boxShadow: [shadow(blurRadius: 4, opacity: 0.25)]),
              width: 40.w,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  customSvgPicture(
                      imagePath: ImagePath.starSvg,
                      height: 14.h,
                      width: 15.w,
                      color: AppPallete.rating),
                  customtext(
                      context: context,
                      newYear: rating.toString(),
                      font: 15.sp,
                      weight: FontWeight.w700,
                      color: AppPallete.white)
                ],
              ),
            ),
          ),
          Positioned(
            bottom: 17.h,
            left: 5.w,
            child: customtext(
                context: context,
                newYear: name,
                font: 18.sp,
                weight: FontWeight.w700,
                color: AppPallete.white,
                shadows: [shadow(blurRadius: 4, opacity: 0.25)]),
          )
        ],
      ),
    ),
  );
}
