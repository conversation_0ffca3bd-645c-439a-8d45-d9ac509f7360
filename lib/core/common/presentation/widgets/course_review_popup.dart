// import 'package:flutter/material.dart';
// import 'package:class_z/core/imports.dart';
// import 'package:shared_preferences/shared_preferences.dart';

// void showCourseReviewBottomSheet({
//   required BuildContext context,
//   required ClassModel classModel,
//   required bool isHalfCourse,
//   required String revieweeType, // "Center" or "Coach"
//   required String revieweeId,
//   required String reviewerId,
//   required String classId,
//   DateTime? sessionDate,
// }) {
//   double rating = 0.0;
//   final TextEditingController commentController = TextEditingController();

//   // Helper method to mark review as completed in SharedPreferences
//   Future<void> _markReviewAsCompleted() async {
//     final prefs = await SharedPreferences.getInstance();
//     final String firstLessonReviewPrefixKey = 'first_lesson_review_';
//     final String completionReviewPrefixKey = 'completion_review_';

//     // Mark both types of reviews as completed for this class
//     final String firstLessonKey =
//         "$firstLessonReviewPrefixKey${reviewerId}_$classId";
//     final String completionKey =
//         "$completionReviewPrefixKey${reviewerId}_$classId";

//     await prefs.setBool(firstLessonKey, true);
//     await prefs.setBool(completionKey, true);

//     print(
//         "[CourseReviewPopup] Marked review as completed: $firstLessonKey and $completionKey");
//   }

//   void setRating({required double newRating}) {
//     rating = newRating;
//   }

//   String title =
//       isHalfCourse ? "First Lesson Review" : "Course Completion Review";
//   String subtitle = isHalfCourse
//       ? "Please share your thoughts on the first lesson"
//       : "Please rate your overall experience with the course";
//   String revieweeTypeDisplay = revieweeType == "Center"
//       ? "How was ${classModel.center?.displayName ?? 'the center'}"
//       : "How was ${classModel.coach?.displayName ?? 'the coach'}";

//   showModalBottomSheet(
//     context: context,
//     isScrollControlled: true,
//     isDismissible: false,
//     enableDrag: false,
//     shape: RoundedRectangleBorder(
//       borderRadius: BorderRadius.only(
//         topLeft: Radius.circular(10.r),
//         topRight: Radius.circular(10.r),
//       ),
//     ),
//     builder: (BuildContext context) {
//       return FractionallySizedBox(
//         heightFactor: 0.7,
//         child: Padding(
//           padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 40.h),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Center(
//                 child: customtext(
//                   context: context,
//                   newYear: title,
//                   font: 25.sp,
//                   weight: FontWeight.w600,
//                 ),
//               ),
//               SizedBox(height: 10.h),
//               Center(
//                 child: customtext(
//                   context: context,
//                   newYear: subtitle,
//                   font: 16.sp,
//                   weight: FontWeight.w400,
//                 ),
//               ),
//               SizedBox(height: 25.h),
//               customtext(
//                 context: context,
//                 newYear: revieweeTypeDisplay,
//                 font: 18.sp,
//                 weight: FontWeight.w500,
//               ),
//               SizedBox(height: 15.h),
//               StatefulBuilder(builder: (context, setState) {
//                 return Row(
//                   children: List.generate(
//                     5,
//                     (index) => GestureDetector(
//                       onTap: () {
//                         setRating(newRating: index + 1.0);
//                         setState(() {}); // Rebuild to update star colors
//                       },
//                       child: customSvgPicture(
//                         imagePath: ImagePath.starSvg,
//                         color: index < rating
//                             ? AppPallete.rating
//                             : AppPallete.rateGray,
//                         height: 42.h,
//                         width: 42.w,
//                       ),
//                     ),
//                   ),
//                 );
//               }),
//               SizedBox(height: 15.h),
//               AuthField(
//                 controller: commentController,
//                 height: 120.h,
//                 width: double.infinity,
//                 border: 20.sp,
//                 hintText: "Your feedback (optional)",
//               ),
//               SizedBox(height: 25.h),
//               Center(
//                 child: Button(
//                   buttonText: "Submit Review",
//                   color: AppPallete.secondaryColor,
//                   height: 49.h,
//                   width: 249.w,
//                   onPressed: () {
//                     if (rating == 0) {
//                       ScaffoldMessenger.of(context).showSnackBar(
//                         SnackBar(content: Text("Please select a rating")),
//                       );
//                       return;
//                     }

//                     // Mark review as completed in SharedPreferences
//                     _markReviewAsCompleted();

//                     var payload = {
//                       "reviewerId": reviewerId,
//                       "reviewerType": "User",
//                       "revieweeId": revieweeId,
//                       "revieweeType": revieweeType,
//                       "classId": classId,
//                       "rating": rating,
//                       "title": isHalfCourse
//                           ? "First Lesson Review"
//                           : "Course Completion",
//                       "comment": commentController.text,
//                       "date": DateTime.now().toString(),
//                     };

//                     context
//                         .read<ReviewBloc>()
//                         .add(PostReviewEvent(payload: payload));
//                     NavigatorService.goBack();
//                   },
//                 ),
//               )
//             ],
//           ),
//         ),
//       );
//     },
//   );
// }
