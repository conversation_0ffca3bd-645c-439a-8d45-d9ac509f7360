import 'package:class_z/core/imports.dart';

Widget centerAddSlotCard(
    {required BuildContext context,
    required String imagePath,
    required String title,
    required String category,
    required String location,
    required String ageGroup,
    required String rate,
    required VoidCallback onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Padding(
      padding: EdgeInsets.only(left: 27.w, right: 27.w),
      child: Container(
          height: 107.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Stack(children: [
            CustomImageBuilder(
              imagePath: imagePath,
              height: 107.h,
              width: double.infinity,
              borderRadius: 20.r,
            ),
            Positioned(
                top: 65.h,
                left: 14.w,
                child: Row(children: [
                  customtext(
                      context: context,
                      newYear: title,
                      font: 17.sp,
                      color: Colors.white,
                      shadows: [_shadow()],
                      weight: FontWeight.w700),
                ])),
            Positioned(
                bottom: 10.h,
                left: 14.w,
                child: Row(children: [
                  customtext(
                      context: context,
                      newYear: category,
                      font: 12.sp,
                      color: Colors.white,
                      shadows: [_shadow()],
                      weight: FontWeight.w600),
                  SizedBox(
                    width: 13.w,
                  ),
                  customtext(
                      context: context,
                      newYear: "Age $ageGroup",
                      font: 12.sp,
                      color: Colors.white,
                      shadows: [_shadow()],
                      weight: FontWeight.w600),
                  SizedBox(
                    width: 13.w,
                  ),
                  customtext(
                      context: context,
                      newYear: location,
                      font: 12.sp,
                      color: Colors.white,
                      shadows: [_shadow()],
                      weight: FontWeight.w600),
                ])),
            Positioned(
                bottom: 10.h,
                right: 19.w,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    customSvgPicture(
                      imagePath: ImagePath.zSvg,
                      height: 15.h,
                      width: 15.w,
                    ),
                    SizedBox(width: 4.w),
                    customtext(
                      context: context,
                      newYear: rate,
                      color: Colors.white,
                      font: 15.sp,
                      weight: FontWeight.w700,
                      shadows: [_shadow()],
                    ),
                  ],
                ))
          ])),
    ),
  );
}

BoxShadow _shadow() {
  return BoxShadow(
    color: Colors.black.withOpacity(0.8),
    blurRadius: 2.0,
    offset: const Offset(1, 1),
  );
}
