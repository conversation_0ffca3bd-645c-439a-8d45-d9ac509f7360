import 'package:flutter/material.dart';

class HalfCircleProgressBar extends StatelessWidget {
  final double progress;
  final double size;

  const HalfCircleProgressBar({
    Key? key,
    required this.progress,
    required this.size,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(size, size / 2),
      painter: _HalfCircleProgressPainter(progress),
    );
  }
}

class _HalfCircleProgressPainter extends CustomPainter {
  final double progress;

  _HalfCircleProgressPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final double strokeWidth = 20.0;
    final Rect arcRect = Rect.fromLTWH(0, 0, size.width, size.height * 2);

    final Paint backgroundPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    final Paint progressPaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      arcRect,
      3.14, // π radians, which is 180 degrees (start from left)
      3.14, // Draw a full half-circle
      false,
      backgroundPaint,
    );

    canvas.drawArc(
      arcRect,
      3.14, // Start from the same position
      3.14 * progress, // Sweep according to progress
      false,
      progressPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
