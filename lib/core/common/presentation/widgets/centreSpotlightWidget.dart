import 'package:class_z/core/imports.dart';

Widget centreSpotlightWidget(BuildContext context,
    {required CenterData center}) {
  final double imageHeight = 143.h;
  final double imageWidth = 404.w;

  return GestureDetector(
    onTap: () {
      NavigatorService.pushNamed(AppRoutes.centreView);
    },
    child: Padding(
      padding: EdgeInsets.only(left: 13.w, right: 13.w),
      child: Container(
        height: imageHeight,
        width: imageWidth,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: center.mainImage?.url != null
                ? NetworkImage(center.mainImage!.url!) as ImageProvider
                : const AssetImage(ImagePath.school),
            fit: BoxFit.cover,
          ),
          borderRadius: const BorderRadius.all(Radius.circular(20)),
        ),
        child: Stack(
          children: [
            Positioned(
              top: 13.h,
              left: 10.w,
              child: Container(
                decoration: BoxDecoration(boxShadow: [shadow()]),
                width: 40.w,
                height: 15.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SingleStarRating(initialRating: center.rating ?? 0.0),
                    Text(
                      "${center.rating?.toStringAsFixed(1) ?? '0.0'}",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 15.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              top: 12.h,
              right: 17.w,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    decoration: BoxDecoration(boxShadow: [shadow()]),
                    child: Text(
                      "Age ${center.startAge ?? '6-12'}",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 13.sp,
                          fontWeight: FontWeight.w400),
                    ),
                  ),
                  SizedBox(height: 3.h),
                  if (center.sen == true)
                    Container(
                      width: 32.w,
                      decoration: BoxDecoration(
                          color: AppPallete.secondaryColor,
                          borderRadius: BorderRadius.circular(20.w)),
                      child: Center(
                        child: Text(
                          "SEN",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12.sp,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            Positioned(
              bottom: 10.w,
              left: 10.w,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    decoration: BoxDecoration(boxShadow: [shadow()]),
                    child: Text(
                      center.displayName ?? "Unknown Center",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w700),
                    ),
                  ),
                  SizedBox(
                    height: 3.h,
                  ),
                  Container(
                    decoration: BoxDecoration(boxShadow: [shadow()]),
                    child: Text(
                      center.address?.city ?? "Location Unknown",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 13.sp,
                          fontWeight: FontWeight.w400),
                    ),
                  ),
                  SizedBox(
                    height: 3.h,
                  ),
                  Container(
                    decoration: BoxDecoration(boxShadow: [shadow()]),
                    child: Text(
                      center.services?.join(", ") ?? "Services Unknown",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 13.sp,
                          fontWeight: FontWeight.w400),
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
                right: 18.5.w,
                bottom: 6.h,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(boxShadow: [shadow()]),
                          child: Text(
                            "From ${center.priceFrom?.toStringAsFixed(0) ?? '12'}",
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 15.sp,
                                fontWeight: FontWeight.w500),
                          ),
                        ),
                        SizedBox(width: 4.5.w),
                        customSvgPicture(
                            imagePath: ImagePath.zSvg,
                            height: 15.h,
                            width: 15.w,
                            color: Colors.white)
                      ],
                    ),
                    SizedBox(
                      height: 12.5.h,
                    ),
                    customSvgPicture(
                        imagePath: ImagePath.heartSvg,
                        height: 25.h,
                        color: AppPallete.heartSpot,
                        width: 25.w)
                  ],
                ))
          ],
        ),
      ),
    ),
  );
}
