import 'package:flutter/material.dart';
import 'package:class_z/features/roles/center/data/models/center_model.dart';

/// A widget that displays the opening hours of a center
/// in a clean, readable format with day ranges and multiple time slots.
class OpeningHoursFormatter {
  /// Format opening hours for display
  /// Returns a list of widgets representing each section of the opening hours
  static List<Widget> formatOpeningHours(
      BuildContext context, List<OpeningHour>? openingHours,
      {TextStyle? dayStyle, TextStyle? hoursStyle}) {
    if (openingHours == null || openingHours.isEmpty) {
      return [
        Text('Opening hours not available',
            style: dayStyle ??
                TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.brown))
      ];
    }

    // Default text styles
    final TextStyle defaultDayStyle = dayStyle ??
        TextStyle(
            fontSize: 14, fontWeight: FontWeight.bold, color: Colors.brown);

    final TextStyle defaultHoursStyle =
        hoursStyle ?? TextStyle(fontSize: 14, fontWeight: FontWeight.normal);

    // Prepare data
    final formattedData = _processOpeningHours(openingHours);
    final List<Widget> result = [];

    // Add today's hours first if available
    final DateTime now = DateTime.now();
    final String today = _getDayName(now.weekday);

    final todayHours = formattedData[today];
    if (todayHours != null) {
      result.add(Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 120,
            child: Text('Today', style: defaultDayStyle),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: todayHours.map((slot) {
                return Text(slot, style: defaultHoursStyle);
              }).toList(),
            ),
          ),
        ],
      ));

      if (result.isNotEmpty && formattedData.length > 1) {
        result.add(SizedBox(height: 8)); // Add spacing after today
      }
    }

    // Add day ranges
    formattedData.forEach((dayOrRange, hours) {
      // Skip today as it's already shown
      if (dayOrRange == today) return;

      result.add(Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 120,
            child: Text(dayOrRange, style: defaultDayStyle),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: hours.map((slot) {
                return Text(slot, style: defaultHoursStyle);
              }).toList(),
            ),
          ),
        ],
      ));

      if (dayOrRange != formattedData.keys.last) {
        result.add(SizedBox(height: 8)); // Add spacing between rows
      }
    });

    return result;
  }

  /// Creates a collapsible widget for opening hours
  /// Shows a summary by default with an option to expand
  static Widget createCollapsibleOpeningHours(
      BuildContext context, List<OpeningHour>? openingHours,
      {TextStyle? dayStyle, TextStyle? hoursStyle}) {
    if (openingHours == null || openingHours.isEmpty) {
      return Text('Opening hours not available',
          style: dayStyle ??
              TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.brown));
    }

    // The calendar icon is now provided by the parent widget, so we don't need to include it here
    return _CollapsibleOpeningHours(
      openingHours: openingHours,
      dayStyle: dayStyle,
      hoursStyle: hoursStyle,
    );
  }

  /// Process opening hours data to create day ranges and time slots
  static Map<String, List<String>> _processOpeningHours(
      List<OpeningHour> openingHours) {
    // Maps each day to its hours
    Map<String, List<String>> dayToHours = {};

    // Initialize all days as "Closed" by default
    List<String> allDays = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];

    // Set all days to "Closed" by default
    for (String day in allDays) {
      dayToHours[day] = ['Closed'];
    }

    // Convert OpeningHour objects to standardized time ranges
    for (var hour in openingHours) {
      final String day = hour.day ?? '';
      if (day.isEmpty) continue;

      final String openingTime = hour.openingTime ?? '';
      final String closingTime = hour.closingTime ?? '';

      // Skip days with empty opening/closing times (these are "closed" days)
      if (openingTime.isEmpty || closingTime.isEmpty) {
        dayToHours[day] = ['Closed'];
        continue;
      }

      // Format time range
      final String timeSlot = '$openingTime - $closingTime';

      // Override the "Closed" default with actual hours
      dayToHours[day] = [timeSlot];
    }

    // Group consecutive days with identical hours
    Map<String, List<String>> result = {};

    // Days of the week in order
    List<String> daysOrder = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];

    // Process day ranges
    int start = 0;
    while (start < daysOrder.length) {
      final String startDay = daysOrder[start];

      // If this day is closed, add it individually and continue
      if (dayToHours[startDay]!.contains('Closed')) {
        result[startDay] = dayToHours[startDay]!;
        start++;
        continue;
      }

      // Look for consecutive days with identical hours
      int end = start + 1;
      while (end < daysOrder.length &&
          !dayToHours[daysOrder[end]]!.contains('Closed') &&
          _areHoursIdentical(
              dayToHours[startDay]!, dayToHours[daysOrder[end]]!)) {
        end++;
      }

      // Create day range label
      String dayLabel;
      if (end - start > 1) {
        // For ranges like "Mon - Sat"
        dayLabel =
            '${_getShortDayName(startDay)} - ${_getShortDayName(daysOrder[end - 1])}';
      } else {
        // For single days
        dayLabel = startDay;
      }

      result[dayLabel] = dayToHours[startDay]!;
      start = end;
    }

    return result;
  }

  /// Check if two lists of time slots are identical
  static bool _areHoursIdentical(List<String> hours1, List<String> hours2) {
    if (hours1.length != hours2.length) return false;

    for (int i = 0; i < hours1.length; i++) {
      if (hours1[i] != hours2[i]) return false;
    }

    return true;
  }

  /// Get short day name (Mon, Tue, etc.) from full day name
  static String _getShortDayName(String fullDayName) {
    final Map<String, String> shortNames = {
      'Monday': 'Mon',
      'Tuesday': 'Tue',
      'Wednesday': 'Wed',
      'Thursday': 'Thu',
      'Friday': 'Fri',
      'Saturday': 'Sat',
      'Sunday': 'Sun',
    };

    return shortNames[fullDayName] ?? fullDayName.substring(0, 3);
  }

  /// Get day name from weekday number (1-7)
  static String _getDayName(int weekday) {
    final List<String> days = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];

    // Convert 1-7 to 0-6 index
    return days[weekday - 1];
  }
}

/// A StatefulWidget to properly handle the collapsible state
class _CollapsibleOpeningHours extends StatefulWidget {
  final List<OpeningHour> openingHours;
  final TextStyle? dayStyle;
  final TextStyle? hoursStyle;

  const _CollapsibleOpeningHours({
    required this.openingHours,
    this.dayStyle,
    this.hoursStyle,
  });

  @override
  State<_CollapsibleOpeningHours> createState() =>
      _CollapsibleOpeningHoursState();
}

class _CollapsibleOpeningHoursState extends State<_CollapsibleOpeningHours> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    // Default text styles
    final TextStyle defaultDayStyle = widget.dayStyle ??
        TextStyle(
            fontSize: 14, fontWeight: FontWeight.bold, color: Colors.brown);

    final TextStyle defaultHoursStyle = widget.hoursStyle ??
        TextStyle(fontSize: 14, fontWeight: FontWeight.normal);

    // Process the opening hours data
    final formattedData =
        OpeningHoursFormatter._processOpeningHours(widget.openingHours);

    // Get today's day name
    final DateTime now = DateTime.now();
    final String today = OpeningHoursFormatter._getDayName(now.weekday);
    final String tomorrow =
        OpeningHoursFormatter._getDayName((now.weekday % 7) + 1);

    // Count how many days we have to determine if we need the toggle
    final int totalDays = formattedData.length;
    final bool hasTodayData = formattedData.containsKey(today);
    final bool hasTomorrowData =
        formattedData.containsKey(tomorrow) && tomorrow != today;

    // Calculate how many days would be shown in collapsed view
    final int collapsedDaysCount =
        (hasTodayData ? 1 : 0) + (hasTomorrowData && !isExpanded ? 1 : 0);

    // Determine if we need to show the toggle button
    final bool shouldShowToggle = totalDays > 1;

    // Get the days to show in collapsed view
    List<String> collapsedDays = [];
    if (!isExpanded) {
      // In collapsed view, show a limited number of days
      if (hasTodayData) {
        collapsedDays.add(today);
      }
      if (hasTomorrowData) {
        collapsedDays.add(tomorrow);
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Collapsed view - show limited days
        if (!isExpanded)
          ...collapsedDays.map((day) => Column(
                children: [
                  if (day != collapsedDays.first) SizedBox(height: 4),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 80,
                        child: Text(day,
                            style: defaultDayStyle), // Show actual day name
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: formattedData[day]!.map((slot) {
                            return Text(slot, style: defaultHoursStyle);
                          }).toList(),
                        ),
                      ),
                    ],
                  ),
                ],
              )),

        // Expanded view - show all days
        if (isExpanded)
          ...formattedData.entries.map((entry) {
            return Column(
              children: [
                if (entry.key != formattedData.keys.first) SizedBox(height: 4),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 80,
                      child: Text(entry.key, style: defaultDayStyle),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: entry.value.map((slot) {
                          return Text(slot, style: defaultHoursStyle);
                        }).toList(),
                      ),
                    ),
                  ],
                ),
              ],
            );
          }).toList(),

        // Show toggle button only if there are more days to show
        if (shouldShowToggle)
          InkWell(
            onTap: () {
              setState(() {
                isExpanded = !isExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      isExpanded ? "Show less" : "Show more",
                      style: TextStyle(
                        color: Colors.blue,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(width: 4),
                    Icon(
                      isExpanded
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      size: 16,
                      color: Colors.blue,
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }
}
