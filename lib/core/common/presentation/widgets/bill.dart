import 'package:class_z/core/imports.dart';

Widget billBreakdownPage({required BuildContext context}) {
  return SizedBox(
    width: 406.w,
    height: 222.h,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        customtext(
            context: context,
            newYear: " 'Bill breakdown'",
            font: 20.sp,
            weight: FontWeight.w600),
        SizedBox(height: 18.h),
        Table(
          columnWidths: const {
            0: FlexColumnWidth(3),
            1: FlexColumnWidth(1),
            2: FlexColumnWidth(1),
            3: FlexColumnWidth(1),
          },
          border: const TableBorder(
            bottom: BorderSide(color: AppPallete.paleGrey),
          ),
          children: [
            TableRow(
              children: [
                customtext(
                    context: context,
                    newYear: "Item:",
                    font: 13.sp,
                    weight: FontWeight.w500),
                customtext(
                    context: context,
                    newYear: "QTY:",
                    font: 13.sp,
                    weight: FontWeight.w500),
                customtext(
                    context: context,
                    newYear: "Rate:",
                    font: 13.sp,
                    weight: FontWeight.w500),
                customtext(
                    context: context,
                    newYear: "SubTotal:",
                    font: 13.sp,
                    weight: FontWeight.w500),
              ],
            ),
            TableRow(
              children: [
                customtext(
                    context: context,
                    newYear: "Watercolour (intermediate)",
                    font: 13.sp,
                    weight: FontWeight.w400),
                customtext(
                    context: context,
                    newYear: 4.toString(),
                    font: 13.sp,
                    weight: FontWeight.w400),
                Row(
                  children: [
                    customSvgPicture(
                        imagePath: ImagePath.zSvg,
                        height: 15.sp,
                        width: 15.sp,
                        color: AppPallete.darkGrey),
                    SizedBox(
                      width: 3.w,
                    ),
                    customtext(
                        context: context,
                        newYear: 12.toString(),
                        font: 13.sp,
                        weight: FontWeight.w400),
                  ],
                ),
                Row(
                  children: [
                    customSvgPicture(
                        imagePath: ImagePath.zSvg,
                        height: 15.sp,
                        width: 15.sp,
                        color: AppPallete.darkGrey),
                    SizedBox(
                      width: 3.w,
                    ),
                    customtext(
                        context: context,
                        newYear: 48.toString(),
                        font: 13.sp,
                        weight: FontWeight.w400),
                  ],
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                customtext(
                    context: context,
                    newYear: "SubTotal:",
                    font: 13.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  width: 46.w,
                ),
                customtext(
                    context: context,
                    newYear: "Discount:",
                    font: 13.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  width: 46.w,
                ),
              ],
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Row(
                  children: [
                    customSvgPicture(
                        imagePath: ImagePath.zSvg,
                        height: 15.sp,
                        width: 15.sp,
                        color: AppPallete.darkGrey),
                    SizedBox(
                      width: 3.w,
                    ),
                    customtext(
                        context: context,
                        newYear: 48.toString(),
                        font: 13.sp,
                        weight: FontWeight.w400),
                  ],
                ),
                Row(
                  children: [
                    customtext(
                        context: context,
                        newYear: "-",
                        font: 13.sp,
                        weight: FontWeight.w400),
                    SizedBox(
                      width: 6.w,
                    ),
                    customSvgPicture(
                        imagePath: ImagePath.zSvg,
                        height: 15.sp,
                        width: 15.sp,
                        color: AppPallete.darkGrey),
                    SizedBox(
                      width: 3.w,
                    ),
                    customtext(
                        context: context,
                        newYear: 12.toString(),
                        font: 13.sp,
                        weight: FontWeight.w400),
                  ],
                ),
              ],
            ),
          ],
        ),
      ],
    ),
  );
}
