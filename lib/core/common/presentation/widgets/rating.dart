import 'package:class_z/config/themes/app_pallate.dart';
import 'package:flutter/material.dart';

class SingleStarRating extends StatelessWidget {
  final double initialRating;
  final double size;
  final Color color;

  const SingleStarRating({
    Key? key,
    required this.initialRating,
    this.size = 15.0,
    this.color = AppPallete.rating,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double fractionalRating = initialRating.clamp(0.0, 5.0) / 5.0;
    return Stack(
      children: [
        Icon(
          Icons.star_border,
          size: size,
          color: Colors.grey[300],
        ),
        ClipRect(
          clipper: _StarClipper(fractionalRating),
          child: Icon(
            Icons.star,
            size: size,
            color: color,
          ),
        ),
      ],
    );
  }
}

class _StarClipper extends CustomClipper<Rect> {
  final double ratingFraction;

  _StarClipper(this.ratingFraction);

  @override
  Rect getClip(Size size) {
    return Rect.fromLTRB(0, 0, size.width * ratingFraction, size.height);
  }

  @override
  bool shouldReclip(covariant CustomClipper<Rect> oldClipper) {
    return true;
  }
}
