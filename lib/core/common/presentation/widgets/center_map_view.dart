import 'package:class_z/core/imports.dart';
import 'package:class_z/services/map_service.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:class_z/core/common/presentation/widgets/simple_map_view.dart';
import 'package:url_launcher/url_launcher_string.dart';

class CenterMapView extends StatefulWidget {
  final Address? address;
  final String centerId;
  final double height;
  final double width;
  final double borderRadius;

  const CenterMapView({
    Key? key,
    required this.address,
    required this.centerId,
    required this.height,
    required this.width,
    this.borderRadius = 20,
  }) : super(key: key);

  @override
  State<CenterMapView> createState() => _CenterMapViewState();
}

class _CenterMapViewState extends State<CenterMapView> {
  // Set this to true to use GoogleMap, false to force SimpleMapView
  static const bool USE_GOOGLE_MAPS = true;

  // Controller for the Google Map
  final Completer<GoogleMapController> _controller =
      Completer<GoogleMapController>();

  // Default camera position (Hong Kong)
  static const CameraPosition _defaultPosition = CameraPosition(
    target: LatLng(22.3193, 114.1694),
    zoom: 14.5,
  );

  // Set to hold map markers
  Set<Marker> _markers = {};

  // Map service for geocoding
  final MapService _mapService = locator<MapService>();

  // Indicates if map is ready
  bool _isMapReady = false;

  // Flag to track if Google Maps has any errors
  bool _hasError = false;

  // Flag to determine if we should fall back to SimpleMapView
  bool _useFallback = false;

  // Current camera position
  CameraPosition _currentPosition = _defaultPosition;

  // Current coordinates for the map
  LatLng? _currentCoordinates;

  // Timer for delayed initialization
  Timer? _initTimer;

  // Maximum retries for map initialization
  static const int MAX_RETRIES = 3;
  int _retryCount = 0;

  @override
  void initState() {
    super.initState();

    // If we want to force SimpleMapView, set immediately
    if (!USE_GOOGLE_MAPS) {
      setState(() {
        _useFallback = true;
        _isMapReady = true;
      });
      return;
    }

    // Use a slight delay to allow the UI to build first
    _initTimer = Timer(Duration(milliseconds: 100), () {
      _initializeMap();
    });
  }

  /// Initialize the map with the center's address
  Future<void> _initializeMap() async {
    if (!mounted) return;

    if (widget.address == null) {
      // If no address, use fallback
      setState(() {
        _useFallback = true;
        _isMapReady = true;
        _currentCoordinates = _defaultPosition.target;
      });
      return;
    }

    try {
      // Check if address2 contains a Google Maps link
      if (_mapService.isGoogleMapsLink(widget.address?.address2)) {
        print('Address contains Google Maps link, using default display');

        // Use default coordinates for display
        _currentCoordinates = _defaultPosition.target;

        // Create a marker for the default location
        final markers = _mapService.getMarkerForCenter(
            widget.centerId, widget.address, _defaultPosition.target);

        if (mounted) {
          setState(() {
            _markers = markers;
            _currentPosition = _defaultPosition;
            _isMapReady = true;
          });
        }
        return;
      }

      // Get coordinates for the address
      final coordinates =
          await _mapService.getCoordinatesForAddress(widget.address);
      print('Map coordinates: $coordinates');

      // Store coordinates for opening in external map
      _currentCoordinates = coordinates;

      // Create a marker for the location
      final markers = _mapService.getMarkerForCenter(
          widget.centerId, widget.address, coordinates);

      // Update camera position
      final cameraPosition = CameraPosition(
        target: coordinates,
        zoom: 15.0,
      );

      if (mounted) {
        setState(() {
          _markers = markers;
          _currentPosition = cameraPosition;
          _isMapReady = true;
        });
      }
    } catch (e) {
      print('Error initializing map: $e');

      // Retry up to MAX_RETRIES times
      if (_retryCount < MAX_RETRIES) {
        _retryCount++;
        print('Retrying map initialization (${_retryCount}/${MAX_RETRIES})...');

        // Use exponential backoff for retries
        final delay = Duration(milliseconds: 500 * (1 << _retryCount));
        _initTimer = Timer(delay, _initializeMap);
        return;
      }

      if (mounted) {
        setState(() {
          _useFallback = true;
          _isMapReady = true; // Still mark as ready to show fallback view
          _currentCoordinates = _defaultPosition.target;
        });
      }
    }
  }

  /// Open the current location in Google Maps
  Future<void> _openInGoogleMaps() async {
    try {
      if (_currentCoordinates != null) {
        final label = widget.address != null
            ? _mapService.getAddressString(widget.address)
            : 'Location';

        await _mapService.launchGoogleMaps(_currentCoordinates!,
            label: label, context: context);
      } else if (widget.address != null) {
        // Check if address2 is a Google Maps link
        if (widget.address?.address2 != null &&
            _mapService.isGoogleMapsLink(widget.address!.address2)) {
          // Special handling for directions URLs - launch them directly
          if (widget.address!.address2!.contains('/maps/dir/')) {
            print(
                'Direct launching directions URL: ${widget.address!.address2}');
            final success = await launchUrlString(widget.address!.address2!);
            if (!success) {
              ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Could not open maps application')));
            }
            return;
          }

          // Try to extract coordinates from the link first
          final coordinates = _mapService
              .extractCoordinatesFromMapUrl(widget.address!.address2);
          if (coordinates != null) {
            // Use extracted coordinates
            await _mapService.launchGoogleMaps(coordinates, context: context);
            return;
          }

          // If no coordinates found, try to launch the URL directly
          final success = await launchUrlString(widget.address!.address2!);
          if (!success) {
            ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Could not open maps application')));
          }
          return;
        }

        // Check if address2 is available and not a Google Maps link
        if (widget.address?.address2 != null &&
            widget.address!.address2!.isNotEmpty &&
            !_mapService.isGoogleMapsLink(widget.address!.address2)) {
          // Use only address2 for Google Maps
          await _mapService.launchGoogleMapsWithAddress(
              _mapService.getAddressString(widget.address),
              context: context,
              useOnlyAddress2: true,
              addressObj: widget.address);
        } else {
          // Fallback to full address string if address2 is not available
          final addressString = _mapService.getAddressString(widget.address);
          if (addressString.isNotEmpty) {
            await _mapService.launchGoogleMapsWithAddress(addressString,
                context: context);
          }
        }
      }
    } catch (e) {
      print('Error launching maps: $e');

      // Error handling is now done in the map service
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _openInGoogleMaps,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        child: Container(
          height: widget.height,
          width: widget.width,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            border: Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              _buildMapContent(),

              // Add zoom controls if not using fallback
              if (!_useFallback && _isMapReady)
                Positioned(
                  right: 8,
                  bottom: 15,
                  child: Column(
                    children: [
                      _buildMapButton(Icons.add, () => _zoomIn()),
                      SizedBox(height: 4),
                      _buildMapButton(Icons.remove, () => _zoomOut()),
                    ],
                  ),
                ),

              // Add "Open in Maps" indicator only when not using fallback
              if (!_useFallback)
                Positioned(
                  right: 8,
                  top: 8,
                  child: GestureDetector(
                    onTap: _openInGoogleMaps,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 3),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(6),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.15),
                            blurRadius: 3,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.open_in_new,
                            size: 12,
                            color: Colors.black54,
                          ),
                          SizedBox(width: 4),
                          Text(
                            "Map",
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: Colors.black54,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMapButton(IconData icon, VoidCallback onPressed) {
    return Container(
      width: 28,
      height: 28,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: IconButton(
        padding: EdgeInsets.zero,
        icon: Icon(icon, size: 16),
        onPressed: onPressed,
        color: AppPallete.secondaryColor,
      ),
    );
  }

  Future<void> _zoomIn() async {
    if (_controller.isCompleted) {
      final controller = await _controller.future;
      controller.animateCamera(
        CameraUpdate.zoomIn(),
      );
    }
  }

  Future<void> _zoomOut() async {
    if (_controller.isCompleted) {
      final controller = await _controller.future;
      controller.animateCamera(
        CameraUpdate.zoomOut(),
      );
    }
  }

  Widget _buildMapContent() {
    if (!_isMapReady) {
      // Show a loading indicator while the map initializes
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(
              valueColor:
                  AlwaysStoppedAnimation<Color>(AppPallete.secondaryColor),
            ),
            SizedBox(height: 8),
            Text(
              "Loading map...",
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    // Use SimpleMapView if we've decided to fall back
    if (_useFallback) {
      return SimpleMapView(
        address: widget.address,
        height: widget.height,
        width: widget.width,
        borderRadius: widget.borderRadius,
        onTap: _openInGoogleMaps,
      );
    }

    // Show the Google map if ready and no fallback requested
    try {
      return GoogleMap(
        mapType: MapType.normal,
        initialCameraPosition: _currentPosition,
        markers: _markers,
        zoomControlsEnabled: false,
        mapToolbarEnabled: false,
        myLocationButtonEnabled: false,
        onMapCreated: (GoogleMapController controller) {
          if (!_controller.isCompleted) {
            _controller.complete(controller);
          }
        },
        onTap: (_) => _openInGoogleMaps(),
      );
    } catch (e) {
      print('Failed to create Google Map: $e');

      // If there's any error with GoogleMap widget, use the fallback
      return SimpleMapView(
        address: widget.address,
        height: widget.height,
        width: widget.width,
        borderRadius: widget.borderRadius,
        onTap: _openInGoogleMaps,
      );
    }
  }

  @override
  void dispose() {
    // Cancel any pending timers
    _initTimer?.cancel();

    // Only try to dispose if we're not using the fallback
    if (!_useFallback && _controller.isCompleted) {
      _controller.future
          .then((controller) => controller.dispose())
          .catchError((e) => print('Error disposing map controller: $e'));
    }
    super.dispose();
  }
}
