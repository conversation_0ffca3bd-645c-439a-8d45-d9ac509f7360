import 'package:class_z/core/imports.dart';

Widget courseCard({
  required BuildContext context,
  required String subject,
}) {
  return Container(
    height: 95.h,
    width: 181.w,
    padding: EdgeInsets.only(left: 14.w),
    decoration: BoxDecoration(
        color: AppPallete.dashboard,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 8.h,
        ),
        customtext(
            context: context,
            newYear: "Key Competency",
            font: 15.sp,
            weight: FontWeight.w500),
        Padding(
          padding: EdgeInsets.only(left: 51.w, top: 14.h),
          child: customtext(
              context: context,
              newYear: subject,
              font: 20.sp,
              weight: FontWeight.w700),
        ),
        SizedBox(
          height: 11.h,
        ),
        customtext(
            context: context,
            newYear: "Most frequently covered",
            font: 12.sp,
            weight: FontWeight.w500),
      ],
    ),
  );
}
