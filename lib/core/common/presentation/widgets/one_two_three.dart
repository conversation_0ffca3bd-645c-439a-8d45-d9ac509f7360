import 'package:class_z/core/imports.dart';

// Main Progress Steps Widget
Widget buildProgressSteps({
  required BuildContext context,
  required Color cont1,
  required Color cont2,
  required Color cont3,
}) {
  // Defining text colors based on the step colors
  final textColor1 = _getTextColor(cont1);
  final textColor2 = _getTextColor(cont2);
  final textColor3 = _getTextColor(cont3);

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      _buildProgressRow(
        context: context,
        step1: _buildStepCircle(context, "1", cont1, textColor1),
        step2: _buildStepCircle(context, "2", cont2, textColor2),
        step3: _buildStepCircle(context, "3", cont3, textColor3),
        line1Color: cont2,
        line2Color: cont3,
      ),
      _buildStepLabels(context),
    ],
  );
}

// Helper to determine text color
Color _getTextColor(Color containerColor) {
  return containerColor == AppPallete.secondaryColor
      ? Colors.white
      : Colors.black;
}

// Row of Progress Steps (Circles and Lines)
Widget _buildProgressRow({
  required BuildContext context,
  required Widget step1,
  required Widget step2,
  required Widget step3,
  required Color line1Color,
  required Color line2Color,
}) {
  return Padding(
    padding: EdgeInsets.symmetric(horizontal: 36.w),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        step1,
        SizedBox(
          width: 10.w,
        ),
        _buildStepLine(line1Color),
        step2,
        _buildStepLine(line2Color),
        step3,
      ],
    ),
  );
}

// Step Circle Widget
Widget _buildStepCircle(
    BuildContext context, String step, Color color, Color text) {
  return ClipRRect(
    borderRadius: BorderRadius.circular(34.w),
    child: Container(
      height: 34.h,
      width: 34.w,
      color: color,
      child: Center(
        child: customtext(
          context: context,
          newYear: step,
          font: 20.sp,
          color: text,
        ),
      ),
    ),
  );
}

// Step Line Divider Widget
Widget _buildStepLine(Color color) {
  return Expanded(
    child: Container(
      height: 1.h,
      color: color,
    ),
  );
}

// Step Labels Widget
Widget _buildStepLabels(BuildContext context) {
  return Padding(
    padding: EdgeInsets.symmetric(horizontal: 12.w),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildStepLabel(context, "choose program"),
        // const Spacer(),
        _buildStepLabel(context, "Enrolment"),
        //const Spacer(),
        _buildStepLabel(context, "confirmation"),
      ],
    ),
  );
}

// Step Label Widget
Widget _buildStepLabel(BuildContext context, String label) {
  return customtext(
    context: context,
    newYear: label,
    font: 15.sp,
  );
}
