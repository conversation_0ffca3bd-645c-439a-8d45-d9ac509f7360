import 'package:class_z/core/imports.dart';

Widget schduleCard(
    {required BuildContext context,
    required String title1,
    required String title2,
    required int classNumber,
    required DateTime date}) {
  return Container(
  //  height: 102.h,
    width: double.infinity,
    padding: EdgeInsets.only(left: 14.w),
    decoration: BoxDecoration(
        color: AppPallete.dashboard,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 8.h),
          child: customtext(
              context: context,
              newYear: title1,
              font: 12.sp,
              weight: FontWeight.w500),
        ),
        SizedBox(
          height: 18.h,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            customtext(
                context: context,
                newYear: classNumber.toString(),
                font: 20.sp,
                weight: FontWeight.w700),
            Si<PERSON><PERSON><PERSON>(
              width: 6.w,
            ),
            customtext(
                context: context,
                newYear: title2,
                font: 12.sp,
                weight: FontWeight.w500),
          ],
        ),
        Padding(
          padding: EdgeInsets.only(top: 11.h),
          child: customtext(
              context: context,
              newYear: "Since ${date.toLocal().toString().split(' ')[0]}",
              font: 13.sp,
              weight: FontWeight.w400),
        ),
      ],
    ),
  );
}
