import 'package:cached_network_image/cached_network_image.dart';
import 'package:class_z/core/imports.dart';
import 'package:flutter/foundation.dart';

class CustomImageBuilder extends StatelessWidget {
  final String? imagePath;
  final Uint8List? imageBytes;
  final double? height;
  final double? width;
  final EdgeInsets? margin;
  final double borderRadius;
  final Alignment? alignment;
  final List<BoxShadow>? boxshadow;
  final Widget? child; // Child for overlay

  const CustomImageBuilder({
    Key? key,
    this.imagePath,
    this.imageBytes,
    this.height,
    this.width,
    this.margin,
    required this.borderRadius,
    this.alignment,
    this.boxshadow,
    this.child,
  }) : super(key: key);

  bool _isNetworkImage(String? path) {
    if (path == null) return false;
    final uri = Uri.tryParse(path);
    return uri != null &&
        uri.hasScheme &&
        (uri.scheme == 'http' || uri.scheme == 'https');
  }

  bool _isFileImage(String? path) {
    if (path == null) return false;

    // Check if it's a file:// URL or a cache path
    if (path.startsWith('file://')) {
      final filePath = path.replaceFirst('file://', '');
      try {
        return File(filePath).existsSync();
      } catch (e) {
        // Logging removed as requested
        return false;
      }
    }

    // Check if it's a cache path
    if (path.contains('/data/user/0/com.example.class_z/cache/')) {
      try {
        return File(path).existsSync();
      } catch (e) {
        // Logging removed as requested
        return false;
      }
    }

    // Regular file path check
    try {
      return File(path).existsSync();
    } catch (e) {
      // Logging removed as requested
      return false;
    }
  }

  bool _isAssetImage(String? path) {
    return path != null && path.startsWith('lib/assets/');
  }

  @override
  Widget build(BuildContext context) {
    // Initialize SizeConfig if not already initialized
    if (!SizeConfig.isInitialized) {
      SizeConfig.init(context);
    }

    // Process the image path using the imageStringGenerator if needed
    final processedImagePath = imagePath != null &&
            imagePath!.isNotEmpty &&
            !_isAssetImage(imagePath) &&
            !_isNetworkImage(imagePath)
        ? imageStringGenerator(imagePath: imagePath)
        : imagePath;

    // Only log in debug mode and reduce frequency
    if (kDebugMode &&
        processedImagePath != null &&
        processedImagePath.isNotEmpty) {
      // Only log if it's an error condition or unusual case
      if (processedImagePath.contains('D:') ||
          processedImagePath.contains('Error') ||
          processedImagePath.contains('file://')) {
        // Logging removed as requested

        if (_isNetworkImage(processedImagePath)) {
          // Logging removed as requested
        } else if (_isFileImage(processedImagePath)) {
          // Logging removed as requested
        } else if (_isAssetImage(processedImagePath)) {
          // Logging removed as requested
        } else {
          // Logging removed as requested
        }
      }
    } else if (processedImagePath == null || processedImagePath.isEmpty) {
      if (kDebugMode) {
        // Logging removed as requested
      }
    }

    return Container(
      height: height,
      width: width,
      margin: margin,
      alignment: alignment,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: boxshadow,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Stack(
          fit: StackFit.expand,
          children: [
            _buildImage(context, processedImagePath),
            if (child != null) child!,
          ],
        ),
      ),
    );
  }

  Widget _buildImage(BuildContext context, String? processedImagePath) {
    if (imageBytes != null) {
      return Image.memory(
        imageBytes!,
        fit: BoxFit.cover,
        height: height,
        width: width,
        errorBuilder: (_, __, ___) => _defaultImage(context),
      );
    } else if (_isNetworkImage(processedImagePath)) {
      // Don't generate a unique cache key, use the URL directly
      return CachedNetworkImage(
        imageUrl: processedImagePath!,
        fit: BoxFit.cover,
        height: height,
        width: width,
        maxHeightDiskCache: 1500,
        maxWidthDiskCache: 1500,
        useOldImageOnUrlChange: false,
        memCacheHeight: 800, // Add memory cache constraints
        memCacheWidth: 800,
        cacheKey:
            Uri.encodeFull(processedImagePath), // Use encoded URL as cache key
        errorListener: (error) => {}, // Logging removed as requested
        httpHeaders: const {
          "Connection": "keep-alive"
        }, // Add keep-alive header
        placeholder: (context, url) => Container(
          color: AppPallete.lightGrey,
          child: Center(
            child: CircularProgressIndicator(
              color: AppPallete.secondaryColor,
            ),
          ),
        ),
        errorWidget: (_, error, stackTrace) {
          // Logging removed as requested
          return _defaultImage(context);
        },
      );
    } else if (_isFileImage(processedImagePath)) {
      // Handle file:// URLs
      String filePath = processedImagePath!;
      if (filePath.startsWith('file://')) {
        filePath = filePath.replaceFirst('file://', '');
      }

      try {
        return Image.file(
          File(filePath),
          fit: BoxFit.cover,
          height: height,
          width: width,
          errorBuilder: (_, error, stackTrace) {
            // Logging removed as requested
            return _defaultImage(context);
          },
        );
      } catch (e) {
        // Logging removed as requested
        return _defaultImage(context);
      }
    } else if (_isAssetImage(processedImagePath)) {
      return Image.asset(
        processedImagePath!,
        fit: BoxFit.cover,
        height: height,
        width: width,
        errorBuilder: (_, __, ___) => _defaultImage(context),
      );
    } else {
      return _defaultImage(context);
    }
  }

  Widget _defaultImage(BuildContext context) {
    return Container(
      color: AppPallete.lightGrey,
      child: const Center(
        child: Icon(
          Icons.image_not_supported,
          color: Colors.grey,
          size: 24,
        ),
      ),
    );
  }
}
