import 'package:class_z/core/imports.dart';
import 'package:class_z/core/widgets/compressed_image_picker.dart';

class AlbumCard extends StatefulWidget {
  final Function(List<File>)? onImagesSelected; // Callback for image selection

  const AlbumCard({super.key, this.onImagesSelected});

  @override
  State<AlbumCard> createState() => _AlbumCardState();
}

class _AlbumCardState extends State<AlbumCard> {
  final List<File> _images = [];

  Future<void> _addContainer() async {
    try {
      // Pick and compress image
      final result = await CompressedImagePicker.pickAndCompressImage(
        source: ImageSource.gallery,
        type: ImagePickerType.gallery,
        onProgress: (message) {
          print('📸 Gallery image processing: $message');
        },
      );

      if (result.isSuccess && result.file != null) {
        setState(() {
          _images.add(result.file!);
        });
        widget.onImagesSelected?.call(_images); // Send updated images

        // Show compression info if significant compression occurred
        if (result.compressionRatio > 10) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Image compressed: ${result.compressionInfo}'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else if (result.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result.error!)),
        );
      }
      // If cancelled, do nothing
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error selecting image: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          ..._images.map((file) => SizedBox(
                width: 107.w,
                height: 103.h,
                child: Image.file(
                  file,
                  fit: BoxFit.cover,
                ),
              )),
          SizedBox(width: 6.w),
          GestureDetector(
            onTap: _addContainer,
            child: Container(
              height: 103.h,
              width: 107.w,
              color: AppPallete.paleGrey,
              child: customSvgPicture(
                imagePath: ImagePath.plusSvg,
                height: 35.35.h,
                width: 34.w,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
