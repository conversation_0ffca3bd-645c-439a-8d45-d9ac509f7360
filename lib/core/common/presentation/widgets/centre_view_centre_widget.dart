import 'package:class_z/core/imports.dart';

Widget centreViewCentreWidget(BuildContext context,
    {required CenterData center}) {
  final double imageHeight = 163.h;
  final double imageWidth = 331.32.w;

  return GestureDetector(
    onTap: () {
      NavigatorService.pushNamed(AppRoutes.centreView);
    },
    child: Container(
      height: imageHeight,
      width: imageWidth,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: center.mainImage?.url != null
              ? NetworkImage(center.mainImage!.url!) as ImageProvider
              : const AssetImage(ImagePath.park),
          fit: BoxFit.cover,
        ),
        borderRadius: const BorderRadius.all(Radius.circular(20)),
      ),
      child: Stack(
        children: [
          Positioned(
            top: 8.h,
            left: 8.w,
            child: Row(
              children: [
                customSvgPicture(
                    imagePath: ImagePath.zSvg,
                    height: 18,
                    width: 18,
                    color: AppPallete.white),
                SizedBox(width: 4.w),
                Container(
                  margin: EdgeInsets.only(left: 5.w),
                  decoration: BoxDecoration(boxShadow: [shadow()]),
                  child: Text(
                    "From ${center.priceFrom?.toStringAsFixed(0) ?? '12'}",
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            top: 8.h,
            right: 8.w,
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(boxShadow: [shadow()]),
                      width: 40.w,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SingleStarRating(initialRating: center.rating ?? 0.0),
                          Text(
                            "${center.rating?.toStringAsFixed(1) ?? '0.0'}",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 15.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(width: 4.w),
                if (center.sen == true)
                  Container(
                    width: 32.w,
                    margin: EdgeInsets.only(right: 2.w),
                    padding: EdgeInsets.symmetric(horizontal: 4.w),
                    decoration: BoxDecoration(
                        color: AppPallete.secondaryColor,
                        borderRadius: BorderRadius.circular(20.w)),
                    child: Center(
                      child: Text(
                        "SEN",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12.sp,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Positioned(
            bottom: 8,
            left: 8,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(left: 5.w),
                  decoration: BoxDecoration(boxShadow: [shadow()]),
                  child: Text(
                    center.displayName ?? "Unknown Center",
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w700),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(left: 5.w),
                  decoration: BoxDecoration(boxShadow: [shadow()]),
                  child: Text(
                    center.address?.city ?? "Location Unknown",
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 13.sp,
                        fontWeight: FontWeight.w400),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(left: 5.w),
                  decoration: BoxDecoration(boxShadow: [shadow()]),
                  child: Text(
                    center.services?.join(", ") ?? "Services Unknown",
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 13.sp,
                        fontWeight: FontWeight.w400),
                  ),
                ),
              ],
            ),
          ),
          Positioned(
              right: 8.w,
              bottom: 8.w,
              child: CustomIconButton(
                  icon: Icons.favorite_border_outlined,
                  iconSize: 30,
                  color: Colors.white,
                  onPressed: () {}))
        ],
      ),
    ),
  );
}
