import 'package:class_z/core/imports.dart';

Widget newYear({
  required BuildContext context,
  required String newYear,
  required String newClass,
  required String offer,
  required String date,
}) {
  return Container(
    // height: 157.h,
    width: MediaQuery.of(context).size.width,
    decoration: BoxDecoration(
      gradient: LinearGradient(colors: [
        AppPallete.secondaryColor.withOpacity(0.5),
        AppPallete.color128.withOpacity(0.5),
        AppPallete.color234.withOpacity(0.5)
      ]),
      borderRadius: BorderRadius.circular(20.r),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 37.h),
          child: Align(
            alignment: Alignment.center,
            child: RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: "NEW YEAR\n",
                    style: TextStyle(
                      fontSize: 30.sp,
                      color: Colors.white,
                      fontFamily: 'ballo',
                      fontWeight: FontWeight.w400,
                      height: 1.0, // Adjust this value to control spacing
                      shadows: [
                        Shadow(
                          blurRadius: 15,
                          color: Colors.black.withOpacity(0.1),
                        )
                      ],
                    ),
                  ),
                  TextSpan(
                    text: "NEW CLASSES",
                    style: TextStyle(
                      fontSize: 30.sp,
                      color: Colors.white,
                      fontFamily: 'ballo',
                      fontWeight: FontWeight.w400,
                      height: 1.0, // Adjust this value to control spacing
                      shadows: [
                        Shadow(
                          blurRadius: 15,
                          color: Colors.black.withOpacity(0.1),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        SizedBox(
          height: 2.h,
        ),
        Center(child: _customText(context, offer, 20.sp, 'ballo')),
        Padding(
          padding:
              EdgeInsets.only(left: 22.w, right: 12.w, top: 0, bottom: 14.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _customText(context, date, 20.sp, 'ballo'),
              CustomIconButton(
                color: Colors.white,
                iconSize: 24.w,
                icon: Icons.arrow_forward_ios_sharp,
                onPressed: () {},
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

Widget _customText(
    BuildContext context, String text, double fontSize, String? fontFamily) {
  return Text(
    text,
    style: TextStyle(
      fontSize: fontSize,
      color: Colors.white,
      fontFamily: fontFamily ?? 'SF',
      fontWeight: FontWeight.w400,
      shadows: [shadow(blurRadius: 15, opacity: 0.1)],
    ),
  );
}
