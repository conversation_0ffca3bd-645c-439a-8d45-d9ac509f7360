import 'package:class_z/core/imports.dart';
import 'package:url_launcher/url_launcher.dart';

Widget timeTableCardFull(
    {required BuildContext context,
    required String user,
    required bool confirmed,
    required String date,
    required String location,
    required String course,
    required String time,
    required String classTime,
    required String special,
    required String coach,
    required String participantName,
    required String contact,
    required String email,
    required String coachingAddress}) {
  Color color = AppPallete.secondaryColor;

  Color textColor = AppPallete.white;
  if (confirmed == false) {
    color = AppPallete.paleGrey;
    textColor = Colors.black;
  }

  String displayAddress = coachingAddress;
  String? url;
  final urlRegex = RegExp(r'https?://[^\s]+');
  final match = urlRegex.firstMatch(coachingAddress);

  if (match != null) {
    url = match.group(0);
    if (url != null) {
      displayAddress = coachingAddress.substring(0, match.start).trim();
      if (displayAddress.endsWith(',')) {
        displayAddress =
            displayAddress.substring(0, displayAddress.length - 1).trim();
      }
    }
  }

  return Container(
    width: 380.w,
    decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
        borderRadius: BorderRadius.circular(20.r)),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        ///name and blue
        Container(
          height: 41.h,
          width: 380.w,
          decoration: BoxDecoration(
              color: color,
              boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20.r),
                  topRight: Radius.circular(20.r))),
          child: Padding(
            padding: EdgeInsets.only(left: 19.w, right: 19.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customtext(
                    context: context,
                    newYear: user,
                    font: 12.sp,
                    weight: FontWeight.w600,
                    color: textColor),
                customtext(
                    context: context,
                    newYear: confirmed ? "Confirmed" : "Pending",
                    font: 12.sp,
                    weight: FontWeight.w600,
                    color: textColor)
              ],
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 19.w, right: 19.w, top: 16.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              customtext(
                context: context,
                newYear: date,
                font: 12.sp,
                weight: FontWeight.w500,
              ),
              customtext(
                context: context,
                newYear: location,
                font: 12.sp,
                weight: FontWeight.w500,
              )
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 19.w, right: 19.w, top: 15.h),
          child: customtext(
            context: context,
            newYear: course,
            font: 20.sp,
            weight: FontWeight.w600,
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 19.w, right: 19.w, top: 11.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              customtext(
                context: context,
                newYear: time,
                font: 20.sp,
                weight: FontWeight.w500,
              ),
              SizedBox(
                width: 12.w,
              ),
              customtext(
                context: context,
                newYear: classTime,
                font: 12.sp,
                weight: FontWeight.w500,
              )
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 19.w, right: 19.w, top: 11.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              customtext(
                context: context,
                newYear: special,
                font: 12.sp,
                weight: FontWeight.w500,
              ),
              customtext(
                context: context,
                newYear: coach,
                font: 12.sp,
                weight: FontWeight.w500,
              )
            ],
          ),
        ),
        SizedBox(
          height: 17.h,
        ),
        customDivider(width: 329.w, padding: 26.w),
        SizedBox(
          height: 11.h,
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left Column
            Expanded(
              flex: 1, // Adjust flex if needed
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  customtext(
                    context: context,
                    newYear: "Participant:",
                    font: 12.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(height: 15.h),
                  customtext(
                    context: context,
                    newYear: "Contact:",
                    font: 12.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(height: 15.h),
                  customtext(
                    context: context,
                    newYear: "Your Email:",
                    font: 12.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(height: 15.h),
                  customtext(
                    context: context,
                    newYear: "Coaching Address:",
                    font: 12.sp,
                    weight: FontWeight.w500,
                  ),
                ],
              ),
            ),
            SizedBox(width: 14.w), // Gap between columns
            // Right Column
            Expanded(
              flex: 2, // Adjust flex if needed
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  customtext(
                    context: context,
                    newYear: participantName,
                    font: 12.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(height: 15.h),
                  customtext(
                    context: context,
                    newYear: contact,
                    font: 12.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(height: 15.h),
                  customtext(
                    context: context,
                    newYear: email,
                    font: 12.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(height: 15.h),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: customtext(
                          context: context,
                          newYear: displayAddress,
                          font: 12.sp,
                          weight: FontWeight.w500,
                          overflow: TextOverflow.visible,
                          maxLines: null,
                        ),
                      ),
                      if (url != null)
                        SizedBox(
                          width: 24.w,
                          height: 24.h,
                          child: IconButton(
                            padding: EdgeInsets.zero,
                            icon: const Icon(Icons.location_on_outlined),
                            iconSize: 20.sp,
                            color: AppPallete.secondaryColor,
                            onPressed: () async {
                              final uri = Uri.tryParse(url!);
                              if (uri != null && await canLaunchUrl(uri)) {
                                await launchUrl(uri);
                              }
                            },
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),

        SizedBox(
          height: 27.h,
        )
      ],
    ),
  );
}
