import 'package:class_z/core/imports.dart';

class CustomNavBar extends StatefulWidget {
  final int selectedIndex;
  final Function(int) onTabChange;

  CustomNavBar({
    Key? key,
    required this.selectedIndex,
    required this.onTabChange,
  }) : super(key: key);

  @override
  State<CustomNavBar> createState() => _CustomNavBarState();
}

class _CustomNavBarState extends State<CustomNavBar> {
  UserModel? userData;
  String? imagePath;
  String? fullUrl;

  @override
  void initState() {
    super.initState();
    _refreshUserData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    setState(() {
      _refreshUserData();
    });
  }

  void _refreshUserData() {
    // This method only updates the local fields. The caller is responsible for wrapping
    // it in setState if a rebuild is required. Avoiding an internal setState prevents
    // rebuild loops when this method is invoked from build().
    userData = locator<SharedRepository>().getUserData();
    imagePath = userData?.data?.parent?.image?.url;
    fullUrl = imageStringGenerator(imagePath: imagePath ?? '');
  }

  @override
  Widget build(BuildContext context) {
    // NOTE: Do not call setState from build to avoid rebuild loops.
    _refreshUserData();

    Color activeColor = AppPallete.secondaryColor;

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthSignInSuccess || state is UserLoaded) {
          // When authentication state changes, refresh data
          setState(() {
            _refreshUserData();
          });
        }
      },
      child: SizedBox(
        height: 76,
        width: double.infinity,
        child: Column(
          children: [
            const Divider(
              color: Colors.grey,
              thickness: 0.5,
              height: 1.0,
            ),
            Expanded(
              child: GNav(
                activeColor: activeColor,
                rippleColor: Colors.grey,
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                duration: const Duration(milliseconds: 300),
                selectedIndex: widget.selectedIndex,
                onTabChange:
                    widget.onTabChange, // Simply notify parent about tab change
                tabs: [
                  GButton(
                    icon: Icons.home,
                    iconSize: 30,
                  ),
                  GButton(
                    icon: Icons.search,
                    iconSize: 30,
                  ),
                  GButton(
                    icon: Icons.stacked_bar_chart,
                    iconSize: 60,
                    leading: imagePath != null
                        ? CustomImageBuilder(
                            borderRadius: 60,
                            height: 60,
                            width: 60,
                            imagePath: fullUrl,
                            child: Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: widget.selectedIndex == 2
                                      ? activeColor
                                      : Colors.transparent,
                                  width: 3,
                                ),
                              ),
                            ),
                          )
                        : Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.grey.shade300,
                              border: Border.all(
                                color: widget.selectedIndex == 2
                                    ? activeColor
                                    : Colors.transparent,
                                width: 3,
                              ),
                            ),
                            child: Center(
                              child: Text(
                                "No Image",
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.black54,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                  ),
                  GButton(
                    icon: Icons.calendar_month_outlined,
                    iconSize: 30,
                  ),
                  GButton(
                    icon: Icons.stacked_bar_chart,
                    iconSize: 30,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
