import 'package:class_z/core/imports.dart';

class CustomProfileTileWidget extends StatelessWidget {
  String imagePath;

  final bool exist;

  final String title;
  final String connectedCenter;

  CustomProfileTileWidget({
    required this.imagePath,
    required this.exist,
    required this.title,
    required this.connectedCenter,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 21),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: exist == true ? Colors.white : AppPallete.paleGrey,
        boxShadow: const [
          BoxShadow(
            color: Color(0x1A000000),
            blurRadius: 15,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: exist == true
          ? Row(
              children: [
                CustomImageBuilder(
                    borderRadius: 20,
                    imagePath: imagePath,
                    height: 79,
                    width: 79),
                const SizedBox(width: 20),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    customtext(
                        context: context,
                        newYear: title,
                        font: 20,
                        weight: FontWeight.w700),
                    customtext(
                        context: context,
                        newYear: 'Connected Center',
                        font: 16,
                        weight: FontWeight.w500,
                        color: AppPallete.darkGrey),
                    customtext(
                        context: context,
                        newYear: connectedCenter,
                        font: 15,
                        weight: FontWeight.w300,
                        color: AppPallete.darkGrey),
                  ],
                ),
              ],
            )
          : _buildAddNewProfile(context: context),
    );
  }

  Widget _buildAddNewProfile({required BuildContext context}) {
    return Container(
      color: AppPallete.paleGrey,
      padding: EdgeInsets.all(12), // Optional: add padding for spacing
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              CustomImageBuilder(
                borderRadius: 20,
                imagePath: ImagePath.plus,
                width: 34,
                height: 34,
              ),
              SizedBox(height: 4),
              customtext(
                context: context,
                newYear: "Add profile",
                font: 15.sp,
                weight: FontWeight.w500,
                color: AppPallete.greyWord,
              ),
            ],
          ),
          SizedBox(width: 12), // space between image and text
          Expanded(
            // 👈 This makes sure text doesn’t overflow
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                customtext(
                  context: context,
                  newYear:title,
                  font: 20,
                  weight: FontWeight.w700,
                  color: AppPallete.greyWord,
                ),
                SizedBox(height: 4),
                customtext(
                  context: context,
                  newYear:
                      'Access unlocks when permission is granted by the centre owner',
                  font: 15,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                  weight: FontWeight.w400,
                  color: AppPallete.greyWord,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
