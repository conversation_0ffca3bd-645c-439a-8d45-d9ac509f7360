import 'package:class_z/core/imports.dart';

class SimpleCenterCoachCard extends StatelessWidget {
  final String coach;
  final String location;
  final String image;
  final String id;
  final double rating;
  final VoidCallback? onTap;

  const SimpleCenterCoachCard({
    super.key,
    required this.coach,
    required this.location,
    required this.image,
    required this.id,
    required this.rating,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildCoachImage(context),
            SizedBox(width: 8.w),
            _buildCoachTextInfo(context),
          ],
        ),
      ),
    );
  }

  Widget _buildCoachImage(BuildContext context) {
    return Container(
      width: 100.w,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20.r)),
      child: Stack(
        children: [
          CustomImageBuilder(
            borderRadius: 20,
            imagePath: image,
            width: 100.w,
          ),
          Positioned(
            top: 8.h,
            left: 7.w,
            child: customRating(
              context: context,
              rating: rating.toString(),
              fontSize: 12.sp,
              weight: FontWeight.w700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCoachTextInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 11.h),
        customtext(
          context: context,
          newYear: coach,
          font: 18.sp,
          weight: FontWeight.w700,
        ),
        SizedBox(height: 14.h),
        Row(
          children: [
            customSvgPicture(
              imagePath: ImagePath.locationSvg,
              height: 18.89.h,
              width: 18.89.w,
            ),
            SizedBox(width: 4.w),
            customtext(
              context: context,
              newYear: location.isNotEmpty ? location : "Unknown",
              font: 15.sp,
              weight: FontWeight.w500,
            ),
          ],
        ),
        SizedBox(height: 14.h),
        customtext(
          context: context,
          newYear: id,
          font: 15.sp,
          weight: FontWeight.w400,
        ),
      ],
    );
  }
}
