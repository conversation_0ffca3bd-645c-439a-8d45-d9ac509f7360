// // ignore: file_names
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';

// class NetworkImageWithLoader extends StatelessWidget {
//   final String imageUrl;
//   final double height;
//   final double width;
//   final double? borderRadius;
//   final bool
//       isNetwork; // Add a flag to indicate if the image is from network or asset

//   const NetworkImageWithLoader({
//     Key? key,
//     required this.imageUrl,
//     required this.height,
//     required this.width,
//     this.borderRadius,
//     this.isNetwork = true, // Default to true for network images
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return ClipRRect(
//       borderRadius: BorderRadius.circular(borderRadius ?? 20.w),
//       child: isNetwork
//           ? Image.network(
//               imageUrl,
//               fit: BoxFit.fill,
//               height: height,
//               width: width,
//               loadingBuilder: (BuildContext context, Widget child,
//                   ImageChunkEvent? loadingProgress) {
//                 if (loadingProgress == null) return child; // Image loaded
//                 return Center(
//                   child: CircularProgressIndicator(
//                     value: loadingProgress.expectedTotalBytes != null
//                         ? loadingProgress.cumulativeBytesLoaded /
//                             (loadingProgress.expectedTotalBytes ?? 1)
//                         : null,
//                   ),
//                 );
//               },
//               errorBuilder:
//                   (BuildContext context, Object error, StackTrace? stackTrace) {
//                 return ClipRRect(
//                   borderRadius: BorderRadius.circular(borderRadius ?? 20.w),
//                   child: Container(
//                     height: height,
//                     width: width,
//                     color: Colors.grey, // Placeholder color for error
//                     child: Center(
//                       child: Text(
//                         'Image failed to load',
//                         style: TextStyle(color: Colors.white),
//                       ),
//                     ),
//                   ),
//                 );
//               },
//             )
//           : Image.asset(
//               imageUrl,
//               fit: BoxFit.cover,
//               height: height,
//               width: width,
//               errorBuilder:
//                   (BuildContext context, Object error, StackTrace? stackTrace) {
//                 return Container(
//                   color: Colors.grey, // Placeholder color for error
//                   child: Center(
//                     child: Text(
//                       'Image failed to load',
//                       style: TextStyle(color: Colors.white),
//                     ),
//                   ),
//                 );
//               },
//             ),
//     );
//   }
// }
