import 'dart:math';

import 'package:class_z/core/imports.dart';

Widget dashBoardCard({
  required BuildContext context,
  required String imagePath,
  required String rating,
  required String cooperative,
  required String involvement,
  required String preparedness,
  required String improvement,
}) {
  return Container(
    height: 278.h,
   // width: 388.w,
    decoration: BoxDecoration(
      color: AppPallete.dashboard,
      borderRadius: BorderRadius.circular(20.r),
      boxShadow: [
        shadow(blurRadius: 15, opacity: 0.1),
      ], // Assuming shadow() function is defined
    ),
    child: Stack(
      children: [
        Positioned(
            top: 8.h,
            left: 14.w,
            child: customtext(
                context: context, newYear: 'Performance', font: 20.sp)),
        Positioned(
          top: 53.h, // Adjust top positioning as needed
          left: 27.w, // Adjust left positioning as needed
          child: CustomImageBuilder(
            imagePath: "${AppText.device}$imagePath",
            height: 102.h,
            width: 102.w,
            borderRadius: 99.r,
          ),
        ),
        Positioned(
          top: 170.h,
          left: 33.w,
          child: Row(
            children: [
              customSvgPicture(
                imagePath: ImagePath.starSvg,
                height: 24.h,
                width: 27.w,
                color: AppPallete.rating,
              ),
              customtext(
                context: context,
                newYear: rating,
                font: 30.sp,
                weight: FontWeight.w700,
              ),
            ],
          ),
        ),
        Positioned(
          top: 186.h,
          left: 120.w,
          child: customtext(
            context: context,
            newYear: "/5",
            font: 12.sp,
            weight: FontWeight.w500,
          ),
        ),
        Positioned(
          bottom: 15.h,
          left: 11.w,
          child: SizedBox(
            width: 365.w,
            child: customtext(
              context: context,
              newYear:
                  "The rating report represents a summary of class performance, designed for easy understanding crafted by ClassZ’s Paediatric Team.",
              font: 12.sp,
              weight: FontWeight.w500,
            ),
          ),
        ),
        Positioned(
          top: 49.h,
          right: 15.h,
          child: RadarChart(
            values: [0.7, 0.6, 0.8, 0.7, 0.5, 0.6],
            labels: const [
              'Active Participation',
              'Social Inventory',
              'Readiness',
              'Enjoyment',
              'Distinctive Conduct',
              'Learning Progress',
            ],
          ),
        ),
      ],
    ),
  );
}

class HexagonRadarChartPainter extends CustomPainter {
  final List<double> values;
  final List<String> labels;
  final int levels;
  final Color fillColor;
  final Color strokeColor;
  final double strokeWidth;

  HexagonRadarChartPainter({
    required this.values,
    required this.labels,
    this.levels = 5,
    this.fillColor = Colors.blueAccent,
    this.strokeColor = Colors.blue,
    this.strokeWidth = 2.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    double radius = min(size.width / 2, size.height / 2);
    Offset center = Offset(size.width / 2, size.height / 2);

    Paint gridPaint = Paint()
      ..color = Colors.grey
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    Paint radarPaint = Paint()
      ..color = fillColor.withOpacity(0.5)
      ..style = PaintingStyle.fill;

    Paint outlinePaint = Paint()
      ..color = strokeColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    // Draw hexagonal grid
    for (int i = 1; i <= levels; i++) {
      double r = radius * (i / levels);
      _drawHexagon(canvas, center, r, gridPaint);
    }

    // Draw radar data (Hexagonal Path)
    Path radarPath = Path();
    for (int i = 0; i < values.length; i++) {
      double angle = (2 * pi / values.length) * i;
      double valueRadius = radius * values[i];
      double dx = center.dx + valueRadius * cos(angle);
      double dy = center.dy + valueRadius * sin(angle);
      if (i == 0) {
        radarPath.moveTo(dx, dy);
      } else {
        radarPath.lineTo(dx, dy);
      }
    }
    radarPath.close();
    canvas.drawPath(radarPath, radarPaint);
    canvas.drawPath(radarPath, outlinePaint);

    // Draw labels (around hexagonal chart)
    for (int i = 0; i < labels.length; i++) {
      double angle = (2 * pi / labels.length) * i;
      double dx = center.dx + (radius + 30) * cos(angle + 100);
      double dy = center.dy + (radius + 15) * sin(angle + 100);
      TextPainter textPainter = TextPainter(
        text: TextSpan(
          text: labels[i],
          style: TextStyle(
              fontSize: 10.sp,
              fontWeight: FontWeight.w400,
              color: Colors.black),
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout(maxWidth: 61.w);
      textPainter.paint(canvas,
          Offset(dx - textPainter.width / 2, dy - textPainter.height / 2));
    }
  }

  // Helper method to draw a hexagon
  void _drawHexagon(Canvas canvas, Offset center, double radius, Paint paint) {
    Path path = Path();
    for (int i = 0; i < 6; i++) {
      double angle = (pi / 3) * i;
      double dx = center.dx + radius * cos(angle + 100);
      double dy = center.dy + radius * sin(angle + 100);
      if (i == 0) {
        path.moveTo(dx, dy);
      } else {
        path.lineTo(dx, dy);
      }
    }
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

class RadarChart extends StatelessWidget {
  final List<double> values;
  final List<String> labels;

  RadarChart({required this.values, required this.labels});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(218.w, 153.17.h), // Specify the size you want
      painter: HexagonRadarChartPainter(
        values: values,
        labels: labels,
      ),
    );
  }
}
