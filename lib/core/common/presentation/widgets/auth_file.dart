import 'package:class_z/core/imports.dart';
import 'package:flutter/services.dart';

class AuthField extends StatefulWidget {
  final String? hintText;
  final String? labelText;
  final TextEditingController controller;
  final bool isObsecureText;
  final TextInputType? keyboard;
  final double? width;
  final double? height;
  final double? border;
  final Color? color;
  final FontWeight? weight;
  final int? maxline;
  final String? Function(String?)? validator;
  final Widget? suffixIcon;
  final VoidCallback? onTap;
  final bool? enable;
  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final ValueChanged<String>? onChanged;
  final List<TextInputFormatter>? inputFormatters;
  const AuthField({
    this.hintText,
    this.labelText,
    required this.controller,
    this.width,
    this.height,
    this.border,
    this.color,
    this.weight,
    this.validator,
    this.maxline,
    this.keyboard = TextInputType.text,
    this.isObsecureText = false,
    this.suffixIcon,
    this.onTap,
    this.enable,
    this.focusNode,
    this.textInputAction,
    this.onChanged,
    this.inputFormatters,
    super.key,
  });

  @override
  State<AuthField> createState() => _AuthFieldState();
}

class _AuthFieldState extends State<AuthField> {
  late final FocusNode _focusNode;
  late final ValueNotifier<String?> _errorTextNotifier;
  bool _showPassword = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _errorTextNotifier = ValueNotifier<String?>(null);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _errorTextNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    final double effectiveHeight = widget.height ?? AppBox.inputHeight;
    final double fontSize = (effectiveHeight * 0.4).clamp(12.sp, 18.sp);

    return ClipRRect(
      borderRadius: BorderRadius.circular(widget.border ?? 5.r),
      child: Container(
        width: widget.width ?? screenWidth,
        height: effectiveHeight,
        color: widget.color ?? AppPallete.inputBox,
        child: ValueListenableBuilder<String?>(
          valueListenable: _errorTextNotifier,
          builder: (context, errorText, child) {
            return TextFormField(
              enabled: widget.enable,
              controller: widget.controller,
              inputFormatters: widget.inputFormatters,
              maxLines: widget.maxline ?? 1,
              obscureText: widget.isObsecureText && !_showPassword,
              keyboardType: widget.keyboard,
              focusNode: _focusNode,
              cursorHeight: fontSize,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              validator: (value) {
                return widget.validator?.call(value);
              },
              textInputAction: widget.textInputAction ?? TextInputAction.done,
              onTap: () {
                _focusNode.requestFocus();
                widget.onTap?.call();
              },
              onEditingComplete: () {
                if (widget.textInputAction == TextInputAction.next) {
                  FocusScope.of(context).nextFocus();
                }
              },
              decoration: InputDecoration(
                labelText: widget.labelText,
                suffixIcon: widget.isObsecureText
                    ? IconButton(
                        icon: Icon(
                          _showPassword
                              ? Icons.visibility
                              : Icons.visibility_off,
                          color: AppPallete.greyWord,
                        ),
                        onPressed: () {
                          setState(() {
                            _showPassword = !_showPassword;
                          });
                        },
                      )
                    : widget.suffixIcon != null
                        ? InkWell(onTap: widget.onTap, child: widget.suffixIcon)
                        : null,
                filled: true,
                fillColor: AppPallete.transparentColor,
                hintText: errorText ?? widget.hintText,
                hintStyle: TextStyle(
                  color:
                      errorText != null ? AppPallete.red : AppPallete.greyWord,
                  fontSize: fontSize,
                  fontWeight: widget.weight ?? FontWeight.w400,
                ),
                isDense: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.border ?? 5.r),
                  borderSide: const BorderSide(
                    color: AppPallete.inputBox,
                    width: 1.0,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.border ?? 5.r),
                  borderSide: BorderSide(
                    color: errorText != null
                        ? AppPallete.red
                        : AppPallete.inputBox,
                    width: 1.0,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(widget.border ?? 5.r),
                  borderSide: BorderSide(
                    color: errorText != null
                        ? AppPallete.red
                        : AppPallete.inputBox,
                    width: 1.0,
                  ),
                ),
                errorStyle: const TextStyle(
                  height: 0,
                  fontSize: 0,
                ),
              ),
              style: TextStyle(
                color: AppPallete.darkGrey,
                fontSize: fontSize,
                fontWeight: FontWeight.w400,
                height: 1.0,
              ),
              onChanged: (value) {
                final error = widget.validator?.call(value);
                if (error != _errorTextNotifier.value) {
                  _errorTextNotifier.value = error;
                }
                widget.onChanged?.call(value);
              },
            );
          },
        ),
      ),
    );
  }
}
