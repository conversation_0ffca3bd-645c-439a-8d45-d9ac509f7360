
import 'package:class_z/core/imports.dart';

Widget centerCoachRequestCard(
    {required BuildContext context,
    required String requestId,
    required String coach,
    required String location,
    required String id,
    required double rating,
    required String imagePath}) {
  return Padding(
    padding: EdgeInsets.only(left: 11.w, right: 11.w),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CustomImageBuilder(
              borderRadius: 20,
              imagePath: imagePath,
              height: 100,
              width: 100,
              child: Positioned(
                top: 8,
                left: 7,
                child:
                    customRating(context: context, rating: rating.toString()),
              ),
            ),
            SizedBox(
              width: 8.w,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 11.h,
                ),
                customtext(
                    context: context,
                    newYear: coach,
                    font: 18.sp,
                    weight: FontWeight.w700),
                SizedBox(
                  height: 14.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    customSvgPicture(
                        imagePath: ImagePath.locationSvg,
                        height: 18.89.h,
                        width: 18.89.w),
                    customtext(
                        context: context,
                        newYear: location,
                        font: 15.sp,
                        weight: FontWeight.w500),
                  ],
                ),
                SizedBox(
                  height: 14.h,
                ),
                customtext(
                    context: context,
                    newYear: id,
                    font: 15.sp,
                    weight: FontWeight.w400),
              ],
            ),
            SizedBox(
              width: 50.w,
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Padding(
              padding: EdgeInsets.only(top: 24.h),
              child: GestureDetector(
                onTap: () {
                  context.read<RequestBloc>().add(UpdateRequestStatusEvent(
                      requestId: requestId, status: 'rejected'));
                },
                child: customtext(
                    context: context,
                    newYear: "Reject",
                    font: 15.sp,
                    weight: FontWeight.w500),
              ),
            ),
            Container(
              width: 1.w,
              height: 46.w,
              color: AppPallete.greyWord,
            ),
            Padding(
              padding: EdgeInsets.only(top: 24.h),
              child: GestureDetector(
                onTap: () {
                  context.read<RequestBloc>().add(UpdateRequestStatusEvent(
                      requestId: requestId, status: 'approved'));
                },
                child: customtext(
                    context: context,
                    newYear: "Add",
                    font: 15.sp,
                    weight: FontWeight.w500,
                    color: AppPallete.change),
              ),
            ),
          ],
        ),
        customDivider(width: 408.w, padding: 11.w)
      ],
    ),
  );
}
