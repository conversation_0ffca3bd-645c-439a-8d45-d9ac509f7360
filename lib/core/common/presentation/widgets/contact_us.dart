import 'package:class_z/core/imports.dart';

class ContactUs extends StatefulWidget {
  final String? email;
  const ContactUs({this.email, super.key});

  @override
  State<ContactUs> createState() => _ContactUsState();
}

class _ContactUsState extends State<ContactUs> {
  final subjectController = TextEditingController();
  final emailController = TextEditingController();
  final messageController = TextEditingController();

  // Predefined subject options
  final List<String> subjectOptions = [
    'Technical Support',
    'Billing Inquiry',
    'Class Booking Issue',
    'Payment Problem',
    'Centre Related',
    'Account Access',
    'Feature Request',
    'Bug Report',
    'Other'
  ];

  String selectedSubject = 'Technical Support'; // Default selection

  @override
  void initState() {
    super.initState();
    // Set the initial email value if provided
    if (widget.email != null && widget.email!.isNotEmpty) {
      emailController.text = widget.email!;
    }
  }

  @override
  void dispose() {
    subjectController.dispose();
    emailController.dispose();
    messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: CustomAppBar(
          title: "Contact Us",
          leading: CustomIconButton(
            icon: Icons.arrow_back_ios,
            onPressed: () {
              NavigatorService.goBack();
            },
          )),
      body: BlocListener<UserBloc, UserState>(
        listener: (context, state) {
          if (state is UserLoadingState) {
            loadingState(context: context);
          } else {
            hideLoadingDialog(context);
          }
          if (state is UserErrorState) {
            errorState(context: context, error: state.message);
          }
          if (state is ContactUsSuccessState) {
            // Show success message
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Message sent successfully'),
                duration: Duration(seconds: 2),
                backgroundColor: Colors.green,
              ),
            );
            // Clear only the message field
            messageController.clear();
          }
        },
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 33.w, top: 19.h),
                child: customtext(
                    context: context,
                    newYear: "We are here to help you!",
                    font: 50.sp,
                    weight: FontWeight.w700),
              ),
              SizedBox(
                height: 22.h,
              ),
              Container(
                color: AppPallete.secondaryColor,
                child: Padding(
                  padding: EdgeInsets.only(
                      left: 19.w, right: 19.w, top: 22.h, bottom: 22.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      customtext(
                          context: context,
                          newYear: "Please tell us what's in your mind!",
                          font: 17.sp,
                          color: Colors.white,
                          weight: FontWeight.w500),
                      SizedBox(
                        height: 10.h,
                      ),
                      AuthField(
                        controller: emailController,
                        hintText:
                            (widget.email == null || widget.email!.isEmpty)
                                ? "Your email"
                                : widget.email!,
                        height: 50.h,
                        width: 393.w,
                        color: Colors.white,
                        border: 15.r,
                        enable: (widget.email == null || widget.email!.isEmpty)
                            ? true
                            : false,
                      ),
                      SizedBox(
                        height: 19.h,
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10.r),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            isExpanded: true,
                            value: selectedSubject,
                            items: subjectOptions.map((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              if (newValue != null) {
                                setState(() {
                                  selectedSubject = newValue;
                                });
                              }
                            },
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 19.h,
                      ),
                      AuthField(
                        controller: messageController,
                        hintText: "Anything you'd like us to know",
                        height: 214.h,
                        width: 393.w,
                        color: Colors.white,
                        border: 15.r,
                      ),
                      SizedBox(
                        height: 16.h,
                      ),
                      Center(
                        child: Button(
                          buttonText: "Submit",
                          color: AppPallete.white,
                          height: 35.5.h,
                          width: 218.w,
                          textColorFinal: AppPallete.secondaryColor,
                          textSize: 17.sp,
                          fontWeight: FontWeight.w500,
                          onPressed: () {
                            final String currentEmail =
                                (widget.email == null || widget.email!.isEmpty)
                                    ? emailController.text
                                    : widget.email!;
                            if (currentEmail.isEmpty) {
                              // Optionally show an error if email is mandatory and not provided
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                    content: Text(
                                        'Please enter your email address.')),
                              );
                              return;
                            }
                            context.read<UserBloc>().add(
                                  ContactUsEvent(
                                    email: currentEmail,
                                    data: {
                                      "email": currentEmail,
                                      "subject": selectedSubject,
                                      "message": messageController.text
                                    },
                                  ),
                                );
                          },
                        ),
                      )
                    ],
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: 17.h, left: 33.w),
                child: customtext(
                    context: context,
                    newYear: "Contact Us",
                    font: 17.sp,
                    weight: FontWeight.w500),
              ),
              Padding(
                padding: EdgeInsets.only(left: 21.w, top: 12.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.email,
                      size: 19.r,
                    ),
                    SizedBox(
                      width: 7.w,
                    ),
                    customtext(
                        context: context,
                        newYear: "<EMAIL>",
                        font: 12.sp,
                        weight: FontWeight.w400)
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 21.w, top: 12.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.phone,
                      size: 19.r,
                    ),
                    SizedBox(
                      width: 7.w,
                    ),
                    customtext(
                        context: context,
                        newYear: "+852 1234 5678",
                        font: 12.sp,
                        weight: FontWeight.w400)
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 21.w, top: 12.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.date_range_outlined,
                      size: 19.r,
                    ),
                    SizedBox(
                      width: 7.w,
                    ),
                    customtext(
                        context: context,
                        newYear: "Mon-Sun 08:00-19:00",
                        font: 12.sp,
                        weight: FontWeight.w400)
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
