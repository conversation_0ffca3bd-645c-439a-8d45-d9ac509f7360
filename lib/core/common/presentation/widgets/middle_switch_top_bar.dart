import 'package:class_z/core/imports.dart';

Widget middleSwitchTopBar(
    {required BuildContext context, required String title}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Padding(
        padding: EdgeInsets.only(left: 19.w, top: 44.h),
        child: CustomIconButton(
          icon: Icons.arrow_back_ios,
          onPressed: () {
            NavigatorService.goBack();
          },
        ),
      ),
      <PERSON><PERSON><PERSON><PERSON>(height: 25.h),
      Padding(
        padding: EdgeInsets.only(left: 20.w, right: 32.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            customtext(
              context: context,
              newYear: title,
              font: 30.sp,
              weight: FontWeight.w500,
            ),
          ],
        ),
      ),
    ],
  );
}
