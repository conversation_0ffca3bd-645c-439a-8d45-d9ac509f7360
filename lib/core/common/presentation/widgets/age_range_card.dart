import 'package:class_z/core/imports.dart';

Widget ageRange({
  required BuildContext context,
}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.end,
    children: [
      // customtext(
      //   context: context, newYear: day, font: 15.sp, weight: FontWeight.w500),
      SizedBox(
        width: 29.w,
      ),
      const TimeDropdown(label: 'From'),
      SizedBox(
        width: 11.w,
      ),
      customtext(
          context: context, newYear: "-", font: 15.sp, weight: FontWeight.w500),
      SizedBox(
        width: 11.w,
      ),
      const TimeDropdown(label: 'To'),
    ],
  );
}

class TimeDropdown extends StatefulWidget {
  final String label;

  const TimeDropdown({super.key, required this.label});

  @override
  _TimeDropdownState createState() => _TimeDropdownState();
}

class _TimeDropdownState extends State<TimeDropdown> {
  String? selectedTime;

  final List<String> times = [
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '10',
    '11',
    '12',
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 92.w,
      height: 30.h,
      decoration: BoxDecoration(
        color: AppPallete.paleGrey,
        borderRadius: BorderRadius.circular(5.0),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          hint: Padding(
            padding: EdgeInsets.only(left: 6.86.w),
            child: customtext(
                context: context,
                newYear: widget.label,
                font: 15.sp,
                weight: FontWeight.w400),
          ),
          value: selectedTime,
          onChanged: (newValue) {
            setState(() {
              selectedTime = newValue;
            });
          },
          items: times.map((time) {
            return DropdownMenuItem(
              value: time,
              child: Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: customtext(
                    context: context,
                    newYear: time,
                    font: 15.sp,
                    weight: FontWeight.w400),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
