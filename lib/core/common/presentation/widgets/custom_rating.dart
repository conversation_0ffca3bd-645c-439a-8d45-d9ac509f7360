import 'package:class_z/core/imports.dart';

Widget customRating({
  required BuildContext context,
  required String rating,
  double? fontSize,
  FontWeight? weight,
  Color? ratingColor,
  Color? fontColor,
}) {
  return Container(
    // Container decoration with box shadow for better visibility
    decoration: BoxDecoration(
      color: Colors.transparent,
      boxShadow: [shadow(blurRadius: 30)],
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // Star icon with custom color
        customSvgPicture(
          imagePath: ImagePath.starSvg,
          height: 14.h, // Scalable height for the star icon
          width: 15.w, // Scalable width for the star icon
          color:
              ratingColor ?? AppPallete.rating, // Default to AppPallete.rating
        ),
        SizedBox(width: 5.w), // Small space between star and rating text
        // Rating text
        customtext(
          context: context,
          newYear: rating.toString(),
          font:
              fontSize ?? 15.sp, // Default to 15.sp if no fontSize is provided
          weight: weight ?? FontWeight.w700, // Default to FontWeight.w700
          color: fontColor ??
              AppPallete
                  .secondaryColor, // Default to white if no color is provided
        ),
      ],
    ),
  );
}
