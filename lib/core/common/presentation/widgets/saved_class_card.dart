import 'package:class_z/core/imports.dart';

Widget savedClassCard(
    {required BuildContext context,
    required bool edit,
    required String imagePath,
    required String title,
    required String category,
    required String coach,
    required String ageGroup,
    required String rate,
    required String time,
    required VoidCallback onDelete,
    required VoidCallback onTap,
    required String locationType,
    bool? hasNoCoach}) {
  double left = 28.w;
  if (edit == true) left = 7.w;
  return InkWell(
    onTap: onTap,
    child: Padding(
      padding: EdgeInsets.only(left: left, right: 26),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            if (edit == true)
              Padding(
                padding: EdgeInsets.only(right: 4.w),
                child: GestureDetector(
                  onTap: onDelete,
                  child: Container(
                      width: 17.w,
                      height: 17.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20.r),
                        color: AppPallete.secondaryColor,
                      ),
                      child: Icon(
                        Icons.remove,
                        color: Colors.white,
                        size: 9.w,
                      )),
                ),
              ),
            Expanded(
              child: Stack(
                children: [
                  CustomImageBuilder(
                    imagePath: imagePath,
                    height: 107,
                    width: double.infinity,
                    borderRadius: 0,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(
                          height: 10,
                        ),
                        if (time.isNotEmpty)
                          Padding(
                              padding: const EdgeInsets.only(right: 13.0),
                              child: customTimeContainer(
                                  context: context, duration: time)),
                        const Spacer(),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 14.0),
                          child: customtext(
                            context: context,
                            newYear: title,
                            font: 17.sp,
                            weight: FontWeight.w700,
                            color: AppPallete.white,
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 14.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  customtext(
                                      context: context,
                                      newYear: '($category)',
                                      font: 12.sp,
                                      weight: FontWeight.w500,
                                      color: AppPallete.white),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  customtext(
                                      context: context,
                                      newYear: ageGroup,
                                      font: 12.sp,
                                      weight: FontWeight.w600,
                                      color: AppPallete.white),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  customtext(
                                      context: context,
                                      newYear: locationType,
                                      font: 12.sp,
                                      weight: FontWeight.w600,
                                      color: AppPallete.white),
                                ],
                              ),
                              rate == "0"
                                  ? customtext(
                                      context: context,
                                      newYear: 'Free',
                                      font: 15.sp,
                                      weight: FontWeight.w700,
                                      color: AppPallete.white)
                                  : Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        customSvgPicture(
                                            imagePath: ImagePath.zSvg,
                                            height: 15.h,
                                            width: 15.w,
                                            color: AppPallete.white),
                                        SizedBox(width: 4.w),
                                        customtext(
                                            context: context,
                                            newYear: rate,
                                            font: 15.sp,
                                            weight: FontWeight.w700,
                                            color: AppPallete.white),
                                      ],
                                    ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 10),
                      ],
                    ),
                  ),
                  if (hasNoCoach == true)
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.brown.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        child: Center(
                          child: customtext(
                            context: context,
                            newYear: "No Coach Assigned",
                            font: 18.sp,
                            weight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
