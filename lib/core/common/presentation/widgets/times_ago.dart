String timeAgo(DateTime? createdAt) {
  if (createdAt == null) return '';
  final currentTime = DateTime.now();
  final difference = currentTime.difference(createdAt);

  if (difference.inMinutes < 60) {
    // Less than an hour
    return '${difference.inMinutes} mins';
  } else if (difference.inHours < 24) {
    // Less than a day
    return '${difference.inHours} hours';
  } else if (difference.inDays < 7) {
    // Less than a week
    return '${difference.inDays} days';
  } else if (difference.inDays < 30) {
    // Less than a month
    final date = createdAt.toLocal();
    return '${date.month}/${date.day}'; // Format date as MM/DD
  } else {
    // If more than a month ago
    final date = createdAt.toLocal();
    return '${date.month}/${date.day}/${date.year}'; // Format as MM/DD/YYYY
  }
}
