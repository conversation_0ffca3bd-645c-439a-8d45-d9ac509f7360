import 'package:class_z/core/imports.dart';

/// A widget that displays a badge with a count on top of an icon.
/// The badge is only visible if the count is greater than 0.
/// For counts over 99, it displays "99+" to avoid overflow.
class NotificationBadge extends StatelessWidget {
  final IconData icon;
  final int? badgeCount;
  final Color? iconColor;
  final Color? badgeColor;
  final double? iconSize;
  final double? badgeSize;
  final VoidCallback? onTap;
  final Key? key;

  const NotificationBadge({
    required this.icon,
    this.badgeCount,
    this.iconColor,
    this.badgeColor,
    this.iconSize,
    this.badgeSize,
    this.onTap,
    this.key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Default values
    final Color effectiveIconColor = iconColor ?? Colors.white;
    final Color effectiveBadgeColor = badgeColor ?? AppPallete.red;
    final double effectiveIconSize = iconSize ?? 30.0;
    final double effectiveBadgeSize = badgeSize ?? 17.45;

    // Format the badge text - handle null counts and large numbers
    final String badgeText = badgeCount == null
        ? ''
        : badgeCount! > 99
            ? '99+'
            : badgeCount.toString();

    return GestureDetector(
      onTap: onTap,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            height: 40.h,
            width: 40.w,
            decoration: BoxDecoration(
              color: AppPallete.lightGreyReal,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              size: effectiveIconSize,
              color: effectiveIconColor,
            ),
          ),
          if (badgeCount != null && badgeCount! > 0)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                padding: EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: effectiveBadgeColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                constraints: BoxConstraints(
                  minWidth: effectiveBadgeSize,
                  minHeight: effectiveBadgeSize,
                ),
                child: Center(
                  child: Text(
                    badgeText,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 7.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

// Keep the old function for backward compatibility
@Deprecated('Use NotificationBadge widget instead')
Widget notification_badge({
  required BuildContext context,
  required IconData icon,
  int? badgeCount,
  Color? iconColor,
  Color? badgeColor,
  double? iconSize,
  double? badgeSize,
  VoidCallback? onTap,
  Key? key,
}) {
  return NotificationBadge(
    icon: icon,
    badgeCount: badgeCount,
    iconColor: iconColor,
    badgeColor: badgeColor,
    iconSize: iconSize,
    badgeSize: badgeSize,
    onTap: onTap,
    key: key,
  );
}
