import 'package:class_z/core/imports.dart';

Widget walletCard(
    {required BuildContext context,
    required int money,
    required bool isLoggedIn,
    bool hasActiveSubscription = false}) {
  // Safe money value to handle API errors
  final displayMoney = money >= 0 ? money : 0;

  // Use fixed height that matches the reference design
  final cardHeight = 118.0;
  final cardWidth = MediaQuery.of(context).size.width * 0.9;

  return GestureDetector(
    onTap: () {
      if (isLoggedIn) {
        NavigatorService.pushNamed(AppRoutes.myWallet, arguments: displayMoney);
      }
    },
    child: Container(
      height: cardHeight,
      width: cardWidth,
      child: Stack(
        children: [
          // Background container with gradient
          Positioned(
            left: 0,
            top: 0,
            child: Container(
              width: cardWidth,
              height: cardHeight,
              decoration: BoxDecoration(
                gradient: GradientProvider.getLinearGradient(),
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 15,
                    offset: Offset(0, 0),
                  ),
                ],
              ),
            ),
          ),

          // Wallet Title
          Positioned(
            left: 18.w,
            top: 11.h,
            child: Text(
              "Wallet",
              style: TextStyle(
                color: Colors.white,
                fontSize: 23.sp,
                fontWeight: FontWeight.w600,
                shadows: [
                  Shadow(
                      offset: Offset(0, 0),
                      blurRadius: 15,
                      color: Colors.black.withOpacity(0.1))
                ],
              ),
            ),
          ),

          // Available Balance Text
          Positioned(
            left: 18.w,
            top: 44.h,
            child: Text(
              "Available balance",
              style: TextStyle(
                color: Colors.white,
                fontSize: 15.sp,
                fontWeight: FontWeight.w600,
                shadows: [
                  Shadow(
                      offset: Offset(0, 0),
                      blurRadius: 15,
                      color: Colors.black.withOpacity(0.1))
                ],
              ),
            ),
          ),

          // Balance Amount with Z icon
          Positioned(
            left: 18.w,
            top: 63.h,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                customSvgPicture(
                  imagePath: ImagePath.zSvg,
                  height: 24.h,
                  width: 24.w,
                  color: Colors.white,
                ),
                SizedBox(width: 3.w),
                Text(
                  displayMoney.toString(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 25.sp,
                    fontWeight: FontWeight.w600,
                    shadows: [
                      Shadow(
                          offset: Offset(0, 0),
                          blurRadius: 15,
                          color: Colors.black.withOpacity(0.1))
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Conditional Exclusive Card / Member status container
          if (isLoggedIn) // Only show status badge if logged in
            Positioned(
              right: 18.w,
              top: 18.h,
              child: Container(
                width: 124.w,
                height: 23.h,
                decoration: BoxDecoration(
                  color: hasActiveSubscription
                      ? AppPallete.secondaryColor
                      : Colors.white,
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Center(
                  child: Text(
                    hasActiveSubscription
                        ? "Exclusive Card"
                        : "Standard Member",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: hasActiveSubscription
                          ? Colors.white
                          : AppPallete.secondaryColor,
                      fontSize: 13.sp,
                      fontWeight: FontWeight.w500,
                      height: 1,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    ),
  );
}

// Custom painter for stripes
class StripePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;

    final stripeWidth = size.width / 10;
    final numStripes = (size.width / stripeWidth).ceil() + 1;

    for (int i = 0; i < numStripes; i++) {
      if (i % 2 == 0) continue; // Skip even indices for alternating pattern

      final path = Path()
        ..moveTo(i * stripeWidth, 0)
        ..lineTo((i + 1) * stripeWidth, 0)
        ..lineTo((i + 0.5) * stripeWidth, size.height)
        ..lineTo((i - 0.5) * stripeWidth, size.height)
        ..close();

      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

Widget moneyback(
    {required BuildContext context,
    required double z,
    required double zRebite}) {
  return Container(
    width: 79.w,
    height: 38.h,
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(5.r),
      boxShadow: [shadow(opacity: 0.1, xoffset: 0, yoffset: 0, blurRadius: 15)],
    ),
    child: Padding(
      padding: EdgeInsets.only(left: 5.w, top: 3.h),
      child: customtext(
        context: context,
        newYear: "${z}z for $zRebite z rebate",
        font: 13.sp,
        color: AppPallete.greyWord,
        weight: FontWeight.w600,
      ),
    ),
  );
}
