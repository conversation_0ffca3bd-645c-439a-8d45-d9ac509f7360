import 'dart:io';

import 'package:flutter/material.dart';

Widget buildImage({
  required String imagePath,
  required double height,
  required double width,
  double borderRadius = 0.0,
  BoxFit fit = BoxFit.cover,
}) {
  return ClipRRect(
    borderRadius: BorderRadius.circular(borderRadius),
    child: imagePath.startsWith('/data') || imagePath.startsWith('/storage')
        ? Image.file(
            File(imagePath),
            height: height,
            width: width,
            fit: fit,
          )
        : Image.asset(
            imagePath,
            height: height,
            width: width,
            fit: fit,
          ),
  );
}
