// ignore_for_file: file_names, library_private_types_in_public_api

import 'package:flutter/material.dart';

class DynamicButton extends StatefulWidget {
  final String buttonText;
  final Color initialColor;
  final Color activeColor;
  final GlobalKey<FormState> formKey;
  final VoidCallback onPressed;

  const DynamicButton({
    Key? key,
    required this.buttonText,
    required this.initialColor,
    required this.activeColor,
    required this.formKey,
    required this.onPressed,
  }) : super(key: key);

  @override
  _DynamicButtonState createState() => _DynamicButtonState();
}

class _DynamicButtonState extends State<DynamicButton> {
  late Color buttonColor;

  @override
  void initState() {
    super.initState();
    buttonColor = widget.initialColor;
  }

  void _updateButtonColor() {
    setState(() {
      buttonColor = widget.formKey.currentState?.validate() ?? false
          ? widget.activeColor
          : widget.initialColor;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () {
        if (widget.formKey.currentState!.validate()) {
          widget.onPressed();
        }
      },
      style: ElevatedButton.styleFrom(backgroundColor: buttonColor),
      child: Text(widget.buttonText),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateButtonColor();
  }
}
