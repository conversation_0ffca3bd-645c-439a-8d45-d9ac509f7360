import 'package:class_z/core/imports.dart';

Widget buildLocation(
    {required BuildContext context,
    required String text,
    double? font,
    double? horizontal,
    double? vertical,
    required Color color}) {
  return ClipRRect(
      borderRadius: BorderRadius.circular(20.r),
      child: Container(
        height: 20,
        width: 100,
        padding:
            EdgeInsets.only(left: horizontal ?? 5.w, right: vertical ?? 5.h),
        color: color,
        child: Center(
          child: GestureDetector(
            onTap: () {},
            child: customtext(
                context: context,
                newYear: text,
                font: font ?? 15.sp,
                color: AppPallete.darkGrey),
          ),
        ),
      ));
}
