import 'package:class_z/core/imports.dart';

Widget coachSpotLightWidget({
  required BuildContext context,
  required String ageGroup,
  required String name,
  required String imageUrl,
  required String location,
  required String price,
  required double rating,
  required String skills,
}) {
  return GestureDetector(
    onTap: () {
      NavigatorService.pushNamed(AppRoutes.coachView);
    },
    child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
            //  height: 241.h,
            width: 184.w,
            child: Stack(
              children: [
                CustomImageBuilder(
                  imagePath: ImagePath.coach,
                  height: 197.h,
                  width: 184.w,
                  borderRadius: 20.r,
                  alignment: Alignment.topCenter,
                ),
                Positioned(
                  top: 15.h,
                  left: 10.w,
                  child: Container(
                    decoration: BoxDecoration(
                        boxShadow: [shadow(blurRadius: 4, opacity: 0.25)]),
                    width: 40.w,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SingleStarRating(initialRating: rating),
                        Text(
                          rating.toString(),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 15.sp,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  top: 16.h,
                  right: 11.w,
                  child: Container(
                      decoration: BoxDecoration(
                          boxShadow: [shadow(blurRadius: 4, opacity: 0.25)]),
                      child: customtext(
                        context: context,
                        newYear: ageGroup,
                        font: 13.sp,
                        weight: FontWeight.w500,
                        color: Colors.white,
                      )),
                ),
                Positioned(
                  top: 147.h,
                  left: 5.w,
                  child: Container(
                      decoration: BoxDecoration(boxShadow: [shadow()]),
                      child: customtext(
                          context: context,
                          newYear: name,
                          font: 18.sp,
                          weight: FontWeight.w700,
                          color: Colors.white,
                          shadows: [shadow(blurRadius: 4, opacity: 0.25)])),
                ),
                Positioned(top: 32.h, right: 11.w, child: customSen()),
                Positioned(
                  top: 169.h,
                  left: 5.w,
                  child: Container(
                      margin: EdgeInsets.only(left: 5.w),
                      child: customtext(
                          context: context,
                          newYear: skills,
                          font: 13.sp,
                          weight: FontWeight.w500,
                          color: Colors.white,
                          shadows: [shadow()])),
                ),
              ],
            )),
        SizedBox(
          width: 170.62.w,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 1.w),
                    child: Row(
                      children: [
                        customSvgPicture(
                          imagePath: ImagePath.locationSvg,
                          height: 16.h,
                          width: 12.w,
                          color: AppPallete.svgColorLocation,
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 8.w),
                          child: Text(
                            location,
                            style: TextStyle(
                                color: AppPallete.wordsOfRequest,
                                fontSize: 15.sp,
                                fontWeight: FontWeight.w400),
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 2.h,
                  ),
                  Row(
                    children: [
                      customSvgPicture(
                          imagePath: ImagePath.zSvg,
                          height: 15.h,
                          width: 15.w,
                          color: AppPallete.svgColorLocation),
                      Padding(
                        padding: EdgeInsets.only(left: 8.w),
                        child: Text(
                          price,
                          style: TextStyle(
                              color: AppPallete.svgColorLocation,
                              fontSize: 15.sp,
                              fontWeight: FontWeight.w400),
                        ),
                      )
                    ],
                  ),
                ],
              ),
              CustomIconButton(
                  icon: Icons.favorite_border_outlined,
                  color: AppPallete.greyColor,
                  onPressed: () {})
            ],
          ),
        )
      ],
    ),
  );
}
