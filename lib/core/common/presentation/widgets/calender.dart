import 'package:class_z/core/imports.dart';
import 'package:table_calendar/table_calendar.dart';

class CalendarPage extends StatefulWidget {
  const CalendarPage({super.key});

  @override
  _CalendarPageState createState() => _CalendarPageState();
}

class _CalendarPageState extends State<CalendarPage> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Calendar UI'),
      ),
      body: Center(
        child: SizedBox(
          height: 273.h,
          width: 380.w,
          child: TableCalendar(
            firstDay: DateTime.utc(2020, 1, 1),
            lastDay: DateTime.utc(2030, 12, 31),
            focusedDay: _focusedDay,
            calendarFormat: _calendarFormat,
            selectedDayPredicate: (day) {
              return isSameDay(_selectedDay, day);
            },
            onDaySelected: (selectedDay, focusedDay) {
              setState(() {
                _selectedDay = selectedDay;
                _focusedDay = focusedDay; // update `_focusedDay` here as well
              });
            },
            onFormatChanged: (format) {
              if (_calendarFormat != format) {
                setState(() {
                  _calendarFormat = format;
                });
              }
            },
            onPageChanged: (focusedDay) {
              _focusedDay = focusedDay;
            },
            calendarStyle: CalendarStyle(
              cellMargin: const EdgeInsets.all(0), // Remove margin between cells
              cellPadding: const EdgeInsets.all(0), // Remove padding inside cells
              defaultTextStyle: TextStyle(fontSize: 14.sp), // Adjust text size
              todayDecoration: const BoxDecoration(
                color: Colors.blueAccent,
                shape: BoxShape.rectangle,
              ),
              selectedDecoration: const BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.rectangle,
              ),
            ),
            daysOfWeekStyle: DaysOfWeekStyle(
              weekdayStyle: TextStyle(fontSize: 12.sp),
              weekendStyle: TextStyle(fontSize: 12.sp),
            ),
            headerStyle: HeaderStyle(
              formatButtonVisible: false,
              titleCentered: true,
              leftChevronIcon: Icon(
                Icons.chevron_left,
                size: 24.sp,
              ),
              rightChevronIcon: Icon(
                Icons.chevron_right,
                size: 24.sp,
              ),
              titleTextStyle: TextStyle(fontSize: 18.sp),
            ),
          ),
        ),
      ),
    );
  }
}
