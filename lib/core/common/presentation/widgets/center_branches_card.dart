import 'package:class_z/core/imports.dart';

Widget centerBranchesCard(
    {required BuildContext context,
    required String imagePath,
    required String center,
    required String location,
    required String rating,
    required VoidCallback onTap}) {
  final double imageHeight = 92.h;
  final double imageWidth = 187.w;

  return GestureDetector(
    onTap: onTap,
    child: Container(
      height: imageHeight,
      width: imageWidth,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
      ),
      clipBehavior: Clip.antiAlias,
      child: Stack(
        children: [
          CustomImageBuilder(
            imagePath: imagePath,
            height: imageHeight,
            width: imageWidth,
            borderRadius: 0,
          ),
          Positioned(
            top: 8.h,
            left: 8.w,
            right: 8.w,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customSen(width: 27.w),
                Row(
                  children: [
                    customSvgPicture(
                        imagePath: ImagePath.starSvg,
                        color: AppPallete.rating,
                        height: 8.82.h,
                        width: 10.w,
                        boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
                    SizedBox(
                      width: 3.5.w,
                    ),
                    customtext(
                        context: context,
                        newYear: rating,
                        font: 12.sp,
                        weight: FontWeight.w700,
                        color: Colors.white,
                        shadows: [
                          shadow(
                              blurRadius: 4,
                              opacity: 0.25,
                              xoffset: 0,
                              yoffset: 4)
                        ])
                  ],
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 8,
            left: 8,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                customtext(
                    context: context,
                    newYear: center,
                    font: 13.sp,
                    weight: FontWeight.w700,
                    color: Colors.white,
                    shadows: [
                      shadow(
                          blurRadius: 4, opacity: 0.25, xoffset: 0, yoffset: 4)
                    ]),
                customtext(
                    context: context,
                    newYear: location,
                    font: 10.sp,
                    weight: FontWeight.w600,
                    color: Colors.white,
                    shadows: [
                      shadow(
                          blurRadius: 4, opacity: 0.25, xoffset: 0, yoffset: 4)
                    ])
              ],
            ),
          ),
        ],
      ),
    ),
  );
}
