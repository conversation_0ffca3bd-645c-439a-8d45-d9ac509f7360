import 'package:class_z/core/imports.dart';
import 'package:flutter/services.dart';


// ignore: must_be_immutable
class CustomPinCodeTextField extends StatelessWidget {
  final Alignment? alignment;
  final TextEditingController? controller;
  final TextEditingController? codeController; // Add this controller
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final BuildContext context;
  final Function(String)? onChanged;
  final FormFieldValidator<String>? validator;

  CustomPinCodeTextField({
    Key? key,
    this.alignment,
    this.controller,
    this.codeController, // Require this controller
    this.textStyle,
    this.hintStyle,
    required this.context,
    this.onChanged,
    this.validator,
  }) : super(key: key);

  Widget get pinCodeTextField => PinCodeTextField(
        appContext: context,
        length: 4,
        keyboardType: TextInputType.number,
        controller: controller,
        textStyle: textStyle,
        hintStyle: hintStyle,
        cursorColor: Colors.black,
        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        enableActiveFill: false,
        pinTheme: PinTheme(
            fieldHeight: 86.h,
            fieldWidth: 79.w,
            borderRadius: BorderRadius.circular(10),
            shape: PinCodeFieldShape.box,
            inactiveColor: AppPallete.secondaryColor),
        onChanged: (value) {
          if (value.length == 4) {
            codeController?.text = value;
          }
          if (onChanged != null) {
            onChanged!(value);
          }
        },
        validator: validator,
      );

  @override
  Widget build(BuildContext context) {
    return alignment != null
        ? Align(
            alignment: alignment ?? Alignment.center,
            child: pinCodeTextField,
          )
        : pinCodeTextField;
  }
}
