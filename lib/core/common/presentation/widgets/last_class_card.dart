import 'package:class_z/core/imports.dart';

Widget lastClassCard(
    {required BuildContext context,
    required String date,
    required String course,
    required String coach,
    required String center,
    required String image,
    required bool roundBottomCorners,
    VoidCallback? onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: ClipRRect(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20.r),
        topRight: Radius.circular(20.r),
        bottomLeft: roundBottomCorners
            ? const Radius.circular(10)
            : const Radius.circular(0),
        bottomRight: roundBottomCorners
            ? const Radius.circular(10)
            : const Radius.circular(0),
      ),
      child: Container(
        padding: EdgeInsets.only(left: 16.13.w, right: 6.5.w),
        decoration: BoxDecoration(
            color: AppPallete.dashboard,
            boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 16.h,
                  ),
                  Row(
                    children: [
                      customtext(
                          context: context,
                          newYear: date,
                          font: 15.sp,
                          weight: FontWeight.w500),
                      SizedBox(
                        width: 14.11.w,
                      ),
                      customtext(
                          context: context,
                          newYear: 'time',
                          font: 15.sp,
                          weight: FontWeight.w500),
                    ],
                  ),
                  SizedBox(
                    height: 12.h,
                  ),
                  customtext(
                      context: context,
                      newYear: course,
                      font: 20.sp,
                      weight: FontWeight.w500),
                  SizedBox(
                    height: 12.h,
                  ),
                  customtext(
                      context: context,
                      newYear: "by $coach",
                      font: 12.sp,
                      weight: FontWeight.w500),
                  SizedBox(
                    height: 12.h,
                  ),
                  customtext(
                      context: context,
                      newYear: center,
                      font: 12.sp,
                      weight: FontWeight.w500),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(right: 6.05.w, top: 5.h, bottom: 6.h),
              child: CustomImageBuilder(
                  imagePath: imageStringGenerator(imagePath: image),
                  height: 117.h,
                  width: 117.91.w,
                  borderRadius: 20.r),
            )
          ],
        ),
      ),
    ),
  );
}
