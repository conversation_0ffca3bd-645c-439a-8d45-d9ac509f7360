import 'package:class_z/core/imports.dart';

class DayTime {
  String day;
  String? openingTime;
  String? closingTime;

  DayTime({
    required this.day,
    this.openingTime,
    this.closingTime,
  });
}

Widget daySchedule({
  required BuildContext context,
  required DayTime dayTime,
}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.start,
    children: [
      Expanded(
        child: Align(
          alignment: Alignment.centerRight,
          child: customtext(
            context: context,
            newYear: dayTime.day,
            font: 15.sp,
            weight: FontWeight.w500,
          ),
        ),
      ),
      SizedBox(width: 29.w),
      Expanded(
        child: TimeDropdown(
          label: 'From',
          selectedTime: dayTime.openingTime,
          onTimeSelected: (time) {
            dayTime.openingTime = time;
          },
        ),
      ),
      SizedBox(width: 11.w),
      customtext(
        context: context,
        newYear: "-",
        font: 15.sp,
        weight: FontWeight.w500,
      ),
      SizedBox(width: 11.w),
      Expanded(
        child: TimeDropdown(
          label: 'To',
          selectedTime: dayTime.closingTime,
          onTimeSelected: (time) {
            dayTime.closingTime = time;
          },
        ),
      ),
    ],
  );
}

class TimeDropdown extends StatefulWidget {
  final String label;
  final String? selectedTime;
  final ValueChanged<String?> onTimeSelected;

  const TimeDropdown({
    super.key,
    required this.label,
    this.selectedTime,
    required this.onTimeSelected,
  });

  @override
  _TimeDropdownState createState() => _TimeDropdownState();
}

class _TimeDropdownState extends State<TimeDropdown> {
  String? _selectedTime;

  @override
  void initState() {
    super.initState();
    _selectedTime = widget.selectedTime ?? widget.label;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 30.h,
      decoration: BoxDecoration(
        color: AppPallete.paleGrey,
        borderRadius: BorderRadius.circular(5.0),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          hint: Padding(
            padding: EdgeInsets.only(left: 6.86.w),
            child: customtext(
              context: context,
              newYear: _selectedTime ?? widget.label,
              font: 15.sp,
              weight: FontWeight.w400,
            ),
          ),
          value: _selectedTime != widget.label
              ? _selectedTime
              : null, // Ensure value is correct
          onChanged: (newValue) {
            setState(() {
              _selectedTime = newValue;
              widget.onTimeSelected(newValue);
            });
          },
          items: [
            '6:00 AM',
            '7:00 AM',
            '8:00 AM',
            '9:00 AM',
            '10:00 AM',
            '11:00 AM',
            '12:00 PM',
            '1:00 PM',
            '2:00 PM',
            '3:00 PM',
            '4:00 PM',
            '5:00 PM',
            '6:00 PM',
            '7:00 PM',
            '8:00 PM',
            '9:00 PM',
            '10:00 PM',
          ].map((time) {
            return DropdownMenuItem<String>(
              value: time,
              child: Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: customtext(
                  context: context,
                  newYear: time,
                  font: 15.sp,
                  weight: FontWeight.w400,
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
