import 'package:class_z/core/imports.dart';

TextSpan customSpanText({
  required String text,
  Color? color,
  double? fontSize,
  FontWeight? fontWeight,
  double? letterSpacing,
  double? height,
  TextDecoration? decoration,
  TextAlign? textAlign,
}) {
  return TextSpan(
    text: text,
    style: TextStyle(
      color: color ?? AppPallete.black, // Default to black if not provided
      fontSize: fontSize ?? 16.sp, // Default size if not provided
      fontWeight: fontWeight ?? FontWeight.w400, // Default weight if not provided
      letterSpacing: letterSpacing ?? 0.0, // Default spacing if not provided
      height: height ?? 1.0, // Default line height if not provided
      decoration: decoration, // Allows for text decoration like underline
    ),
  );
}
