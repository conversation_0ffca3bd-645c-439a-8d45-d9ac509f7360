import 'package:class_z/core/imports.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String? subtitle;
  final Color backgroundColor;
  final double elevation;
  final TextStyle titleTextStyle;
  final Widget? leading;
  final List<Widget>? actions;

  const CustomAppBar({
    Key? key,
    required this.title,
    this.subtitle,
    this.backgroundColor = Colors.transparent,
    this.elevation = 0.0,
    this.titleTextStyle = const TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.w700,
      color: Colors.black,
    ),
    this.leading,
    this.actions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: subtitle == null
          ? Text(title)
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                customtext(
                    context: context,
                    newYear: title,
                    font: 20.w,
                    weight: FontWeight.w700),
                SizedBox(
                  height: 10.h,
                ),
                customtext(
                    context: context,
                    newYear: subtitle!,
                    font: 20.w,
                    weight: FontWeight.w500)
              ],
            ),
      backgroundColor: backgroundColor,
      elevation: elevation,
      titleTextStyle: titleTextStyle,
      centerTitle: true, // This centers the title correctly
      leading: leading,
      actions: actions,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
