import 'package:class_z/core/imports.dart';
import 'package:flutter_svg/svg.dart';
Widget customSvgPicture({
  required String imagePath,
  required double height,
  required double width,
  Color? color,
  Color? borderColor,
  double? borderWidth,
  List<BoxShadow>? boxShadow,
}) {
  return Container(
    height: height,
    width: width,
    decoration: BoxDecoration(
      boxShadow: boxShadow ?? [], // Handle null gracefully
      border: Border.all(
        color: borderColor ?? Colors.transparent, // Default to transparent
        width: borderWidth ?? 0, // Default to 0
      ),
    ),
    child: SvgPicture.asset(
      imagePath,
      height: height,
      width: width,
      colorFilter: color != null
          ? ColorFilter.mode(color, BlendMode.srcIn) // Apply color filter
          : null, // No filter if color is null
      placeholderBuilder: (context) => Center(
        child: CircularProgressIndicator(),
      ),
    ),
  );
}
