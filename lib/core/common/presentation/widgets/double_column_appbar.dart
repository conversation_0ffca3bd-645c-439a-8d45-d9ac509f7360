import 'package:class_z/core/imports.dart';


class CustomAppBarDouble extends StatelessWidget
    implements PreferredSizeWidget {
  final String title;
  final String title2;
  final Color backgroundColor;
  final double elevation;
  final TextStyle titleTextStyle;
  final Widget? leading;
  final List<Widget>? actions;

  const CustomAppBarDouble({
    Key? key,
    required this.title,
    required this.title2,
    this.backgroundColor = Colors.transparent,
    this.elevation = 0.0,
    this.titleTextStyle = const TextStyle(
      fontSize: 20,
      fontWeight: FontWeight.w700,
      color: Colors.black,
    ),
    this.leading,
    this.actions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Column(
        children: [
          Text(title),
          SizedBox(
            height: 10.h,
          ),
          customtext(
              context: context,
              newYear: title2,
              font: 20.sp,
              weight: FontWeight.w500)
        ],
      ),
      backgroundColor: backgroundColor,
      elevation: elevation,
      titleTextStyle: titleTextStyle,
      centerTitle: true, // This centers the title correctly
      leading: leading,
      actions: actions,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
