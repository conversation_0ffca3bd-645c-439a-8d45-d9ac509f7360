import 'package:class_z/core/imports.dart';

Widget topSixCoach({
  required String name,
  required String skills,
  required String imagepath,
  required int strike,
}) {
  return GestureDetector(
    onTap: () {
      NavigatorService.pushNamed(AppRoutes.coachView);
    },
    child: SizedB<PERSON>(
      width: 194.w,
      height: 45.83.h,
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(35.0.r),
            child: Image.asset(
              imagepath, // Replace with the actual image URL
              width: 37.0.w,
              height: 37.0.h,
              fit: BoxFit.cover,
            ),
          ),
          SizedBox(
            width: 5.w,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    fontSize: 10.0.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 6.0.h),
                Row(
                  children: [
                    Icon(
                      Icons.home,
                      size: 10.sp,
                      color: Colors.black,
                    ),
                    Icon(
                      Icons.home,
                      size: 10.sp,
                      color: Colors.black,
                    ),
                    Icon(
                      Icons.home,
                      size: 10.sp,
                      color: Colors.black,
                    ),
                  ],
                ),
                SizedBox(height: 6.0.h),
                SizedBox(
                  width: 110.w,
                  height: 10.h,
                  child: Text(
                    skills,
                    style: TextStyle(
                        fontSize: 10.0.sp, color: AppPallete.greyWord),
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              customSvgPicture(
                  imagePath: ImagePath.fireSvg,
                  height: 11.h,
                  width: 9.w,
                  color: AppPallete.secondaryColor),
              SizedBox(
                width: 2.w,
              ),
              Text(
                '$strike',
                style: TextStyle(
                  fontSize: 10.0.sp,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ],
      ),
    ),
  );
}
