import 'package:class_z/core/imports.dart';

class WalletAppbar extends StatelessWidget implements PreferredSizeWidget {
  final String profileImageUrl;
  final String name;
  final String id;
  final VoidCallback? onBackPressed;
  final int notificationCount;
  final int messageCount;
  final VoidCallback? onNotificationTap;
  final VoidCallback? onMessageTap;

  const WalletAppbar({
    Key? key,
    required this.profileImageUrl,
    required this.name,
    required this.id,
    this.onBackPressed,
    this.notificationCount = 0,
    this.messageCount = 0,
    this.onNotificationTap,
    this.onMessageTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon:
            const Icon(Icons.arrow_back_ios, color: AppPallete.secondaryColor),
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      ),
      actions: [
        Padding(
          padding: EdgeInsets.only(right: 34.w),
          child: SizedBox(
            width: 94.w,
            height: 42.34.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                NotificationBadge(
                  icon: Icons.notifications,
                  badgeCount: notificationCount,
                  onTap: onNotificationTap,
                ),
                SizedBox(width: 9.w),
                NotificationBadge(
                  icon: Icons.messenger_outline_sharp,
                  badgeCount: messageCount,
                  onTap: onMessageTap,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
