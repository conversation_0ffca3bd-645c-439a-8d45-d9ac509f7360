import 'package:class_z/core/imports.dart';

Widget reviewCard({
  required BuildContext context,
  required String title,
  required String rating,
  required String comment,
  required String dateTime,
}) {
  return Container(
    width: 158,
    height: 110,
    padding: EdgeInsets.all(8.w),
    decoration: BoxDecoration(
      color: Colors.white,
      border: Border.all(color: AppPallete.border, width: 1.w),
      borderRadius: BorderRadius.circular(20.r),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and Rating Row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: customtext(
                context: context,
                newYear: title,
                font: 20.sp,
                color: AppPallete.darkGrey,
                weight: FontWeight.bold,
              ),
            ),
            Row(
              children: [
                SingleStarRating(initialRating: double.tryParse(rating) ?? 0.0),
                SizedBox(width: 4.w), // Add spacing between star and rating
                Text(
                  rating,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 15.sp,
                  ),
                ),
              ],
            ),
          ],
        ),
        SizedBox(height: 8.h),

        // Comment Section
        Expanded(
          child: Text(
            comment,
            style: TextStyle(
              fontSize: 10.sp,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis, // Handle long comments
          ),
        ),
        SizedBox(height: 8.h),

        // Date-Time Section
        Align(
          alignment: Alignment.bottomRight,
          child: customtext(
            context: context,
            newYear: dateTime,
            font: 10.sp,
            weight: FontWeight.normal,
          ),
        ),
      ],
    ),
  );
}
