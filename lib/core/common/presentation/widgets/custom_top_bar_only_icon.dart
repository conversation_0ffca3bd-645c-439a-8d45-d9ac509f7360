import 'package:class_z/core/imports.dart';

Widget customTopBarOnlyIcon({
  required BuildContext context,
  IconData? icon1,
  IconData? icon2,
  double? rightPadding,
  double? topPadding,
  int? badgeCount1,
  int? badgeCount2,
  VoidCallback? onTap1,
  VoidCallback? onTap2,
}) {
  return Padding(
    padding:
        EdgeInsets.only(right: rightPadding ?? 34.w, top: topPadding ?? 70.h),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (icon1 != null || onTap1 != null)
          GestureDetector(
            onTap: onTap1,
            child: NotificationBadge(
              icon: icon1 ?? Icons.notifications,
              badgeCount: badgeCount1,
              onTap: onTap1,
            ),
          ),
        if ((icon1 != null || onTap1 != null) &&
            (icon2 != null || onTap2 != null))
          SizedBox(width: 4.w),
        if (icon2 != null || onTap2 != null)
          GestureDetector(
            onTap: onTap2,
            child: NotificationBadge(
              icon: icon2 ?? Icons.message,
              badgeCount: badgeCount2,
              onTap: onTap2,
            ),
          ),
      ],
    ),
  );
}
