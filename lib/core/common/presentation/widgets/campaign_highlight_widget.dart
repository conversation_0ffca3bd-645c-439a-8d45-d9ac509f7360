import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:class_z/core/common/data/models/campaign_model.dart';
import 'package:class_z/config/themes/app_pallate.dart';
import 'package:class_z/core/common/presentation/widgets/shadow.dart';
import 'package:class_z/core/common/presentation/widgets/customtext.dart';
import 'package:class_z/core/common/presentation/widgets/build_icon.dart';
import 'package:intl/intl.dart';

Widget campaignHighlightWidget({
  required BuildContext context,
  required CampaignModel campaign,
  VoidCallback? onTap,
  double? width,
  double? height,
}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      width: width ?? 331.w,
      height: height ?? 157.h,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: campaign.getGradientColors(),
        ),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          shadow(opacity: 0.1, xoffset: 0, yoffset: 4, blurRadius: 15)
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 24.h),
            child: Align(
              alignment: Alignment.center,
              child: RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: "${campaign.title?.toUpperCase()}\n",
                      style: TextStyle(
                        fontSize: 20.sp,
                        color: campaign.getTextColor(),
                        fontFamily: 'ballo',
                        fontWeight: FontWeight.w600,
                        height: 1.0,
                        shadows: [
                          Shadow(
                            blurRadius: 15,
                            color: Colors.black.withOpacity(0.1),
                          )
                        ],
                      ),
                    ),
                    TextSpan(
                      text: campaign.subtitle?.toUpperCase() ?? "",
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: campaign.getTextColor(),
                        fontFamily: 'ballo',
                        fontWeight: FontWeight.w400,
                        height: 1.0,
                        shadows: [
                          Shadow(
                            blurRadius: 15,
                            color: Colors.black.withOpacity(0.1),
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SizedBox(height: 2.h),
          if (campaign.formattedDiscount.isNotEmpty)
            Center(
                child: _customText(context, campaign.formattedDiscount, 14.sp,
                    'ballo', campaign.getTextColor())),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 16.w, right: 8.w, bottom: 8.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Flexible(
                    child: _customText(
                        context,
                        _formatDateRange(
                            campaign.validFrom, campaign.validUntil),
                        12.sp,
                        'ballo',
                        campaign.getTextColor()),
                  ),
                  if (onTap != null)
                    CustomIconButton(
                      color: campaign.getTextColor(),
                      iconSize: 16.w,
                      icon: Icons.arrow_forward_ios_sharp,
                      onPressed: onTap,
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _customText(BuildContext context, String text, double fontSize,
    String? fontFamily, Color textColor) {
  return Text(
    text,
    style: TextStyle(
      fontSize: fontSize,
      color: textColor,
      fontFamily: fontFamily ?? 'SF',
      fontWeight: FontWeight.w400,
      shadows: [shadow(blurRadius: 15, opacity: 0.1)],
    ),
  );
}

String _formatDateRange(DateTime? validFrom, DateTime? validUntil) {
  if (validFrom == null || validUntil == null) return "";

  final now = DateTime.now();
  final formatter = DateFormat('d MMM');

  // If both dates are in the same month
  if (validFrom.month == validUntil.month &&
      validFrom.year == validUntil.year) {
    return "${validFrom.day} - ${formatter.format(validUntil)}";
  }

  // Different months
  return "${formatter.format(validFrom)} - ${formatter.format(validUntil)}";
}

Widget campaignHighlightsSection({
  required BuildContext context,
  required List<CampaignModel> campaigns,
  Function(String)? onCampaignTap,
}) {
  if (campaigns.isEmpty) {
    return Container(
      height: 120.h,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: [
          AppPallete.secondaryColor.withOpacity(0.3),
          AppPallete.color128.withOpacity(0.3),
          AppPallete.color234.withOpacity(0.3)
        ]),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Center(
        child: customtext(
          context: context,
          newYear: "No active campaigns",
          font: 18.sp,
          weight: FontWeight.w500,
          color: Colors.grey[600]!,
        ),
      ),
    );
  }

  return SizedBox(
    height: 120.h,
    child: PageView.builder(
      controller: PageController(viewportFraction: 0.85),
      padEnds: false,
      itemCount: campaigns.length,
      itemBuilder: (context, index) {
        final campaign = campaigns[index];
        return Padding(
          padding: EdgeInsets.only(right: 9.w),
          child: campaignHighlightWidget(
            context: context,
            campaign: campaign,
            width: 331.w,
            height: 120.h,
            onTap: onCampaignTap != null
                ? () => onCampaignTap(campaign.id ?? '')
                : null,
          ),
        );
      },
    ),
  );
}
