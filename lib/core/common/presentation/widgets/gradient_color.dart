import 'package:flutter/material.dart';
import 'package:class_z/config/themes/app_pallate.dart';

class GradientProvider {
  static LinearGradient getLinearGradient() {
    return const LinearGradient(
      colors: [
        // AppPallete.secondaryColor,
        AppPallete.color76,
        AppPallete.color128,
        AppPallete.color255,
      ],
      begin: Alignment.topLeft,
      end: Alignment.centerRight,
    );
  }

  static LinearGradient getSearchGradient() {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        AppPallete.color76.withOpacity(0.8),
        AppPallete.color128.withOpacity(0.8),
      ],
    );
  }
}
