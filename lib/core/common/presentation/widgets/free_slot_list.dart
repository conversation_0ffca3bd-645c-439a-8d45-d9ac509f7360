import 'package:class_z/core/imports.dart';

Widget freeSlotList(
    {required BuildContext context,
    required String imagePath,
    required String title,
    required String category,
    required String coach,
    required String ageGroup,
    required String leastAge,
    required String time,
    ClassModel? classModel,
    List<EventModel>? eventDetails}) {
  return GestureDetector(
    onTap: () {
      if (classModel != null) {
        NavigatorService.pushNamed(
          AppRoutes.request,
          arguments: {
            'classModel': classModel,
            'eventDetails': eventDetails,
          },
        );
      } else {
        // Show an error message if classModel is not provided
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Unable to proceed. Class details not available.')),
        );
      }
    },
    child: ClipRRect(
      borderRadius: BorderRadius.circular(20.r),
      child: Container(
          height: 107.h,
          width: 375.w,
          decoration: BoxDecoration(
              image: DecorationImage(image: AssetImage(imagePath))),
          child: Stack(children: [
            Positioned(
                top: 9.h,
                left: 14.w,
                child: customtext(
                    context: context,
                    newYear: title,
                    font: 20.sp,
                    shadows: [_shadow()],
                    color: Colors.white,
                    weight: FontWeight.w700)),
            Positioned(
                top: 31.h,
                left: 14.w,
                child: customtext(
                    context: context,
                    newYear: category,
                    font: 15.sp,
                    shadows: [_shadow()],
                    color: Colors.white,
                    weight: FontWeight.w500)),
            Positioned(
                top: 50.h,
                left: 14.w,
                child: Row(children: [
                  customtext(
                      context: context,
                      newYear: coach,
                      font: 15.sp,
                      color: Colors.white,
                      shadows: [_shadow()],
                      weight: FontWeight.w500),
                ])),
            Positioned(
                bottom: 10.h,
                left: 14.w,
                child: Row(children: [
                  customtext(
                      context: context,
                      newYear: ageGroup,
                      font: 15.sp,
                      color: Colors.white,
                      shadows: [_shadow()],
                      weight: FontWeight.w700),
                ])),
            Positioned(
              top: 13.h,
              right: 19.w,
              child: customtext(
                  context: context,
                  newYear: time,
                  font: 15.sp,
                  shadows: [_shadow()],
                  color: Colors.white,
                  weight: FontWeight.w700),
            ),
            Positioned(
                bottom: 10.h,
                right: 40.w,
                child: Row(
                  children: [
                    customSvgPicture(
                        imagePath: ImagePath.zSvg,
                        height: 15.h,
                        width: 15.h,
                        color: Colors.white),
                    SizedBox(
                      width: 2.w,
                    ),
                    customtext(
                        context: context,
                        newYear: leastAge,
                        font: 15.sp,
                        shadows: [_shadow()],
                        weight: FontWeight.w700,
                        color: Colors.white),
                  ],
                ))
          ])),
    ),
  );
}

BoxShadow _shadow() {
  return BoxShadow(
    color: Colors.black.withOpacity(0.8), // rgba(0, 0, 0, 0.25)
    blurRadius: 15.0,
    offset: const Offset(0, 0),
  );
}
