import 'package:class_z/core/imports.dart';

Widget pendingReviewCard(
    {required BuildContext context,
    required String imagePath,
    required String name,
    required String course,
    String? dateTime,
    required VoidCallback onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: SizedB<PERSON>(
        width: 75.w,
        height: 123.h,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomImageBuilder(
              imagePath: "${AppText.device}$imagePath",
              height: 75.h,
              width: 75.w,
              borderRadius: 20.r,
            ),
            <PERSON><PERSON><PERSON><PERSON>(
              height: 5.h,
            ),
            SizedBox(
              height: 14.sp,
              width: 75.w,
              child: customtext(
                  context: context,
                  newYear: name,
                  font: 12.sp,
                  color: Colors.black,
                  weight: FontWeight.w400),
            ),
            Sized<PERSON><PERSON>(
              height: 14.sp,
              width: 75.w,
              child: customtext(
                  context: context,
                  newYear: course,
                  font: 12.sp,
                  color: Colors.black,
                  weight: FontWeight.w400),
            ),
            <PERSON><PERSON><PERSON><PERSON>(
              height: 14.sp,
              width: 75.w,
              child: customtext(
                  context: context,
                  newYear: dateTime ?? 'Unknown Date',
                  font: 12.sp,
                  color: Colors.black,
                  weight: FontWeight.w400),
            ),
          ],
        )),
  );
}
