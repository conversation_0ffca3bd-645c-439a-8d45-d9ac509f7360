import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class CustomCacheMgr extends CacheManager {
  static const key = 'classZImageCache';

  static CustomCacheMgr? _instance;

  static CustomCacheMgr get instance => _instance ??= CustomCacheMgr._();

  factory CustomCacheMgr() => instance;

  CustomCacheMgr._()
      : super(Config(
          key,
          stalePeriod: const Duration(days: 2),
          maxNrOfCacheObjects: 200,
          repo: JsonCacheInfoRepository(databaseName: key),
          fileService: HttpFileService(),
          fileSystem: IOFileSystem(key),
        ));

  // Helper to clear the cache when needed
  static Future<void> clearCache() async {
    await instance.emptyCache();
  }

  // Helper to remove a specific URL from cache
  static Future<void> removeFileFromCache(String url) async {
    await instance.removeFile(url);
  }
}
