import 'package:class_z/core/imports.dart';

Widget centerCoachSearchCard(
    {required BuildContext context,
    required String coach,
    required String location,
    required String id,
    required String situation,
    required double rating}) {
  Color color = AppPallete.secondaryColor;
  String check = "Add Child";
  Color textColor = AppPallete.white;
  double height = 72.w;
  if (situation == "pending") {
    color = AppPallete.greyWord;
    check = "Pending";
    textColor = AppPallete.white;
  } else if (situation == "added") {
    color = AppPallete.white;
    check = "Remove Coach";
    textColor = Colors.black;
    height = 43.w;
  }
  return Padding(
    padding: EdgeInsets.only(left: 11.w, right: 11.w),
    child: SizedBox(
      width: 408.w,
      height: 111.h,
      child: Row(
        children: [
          Container(
            height: 100.h,
            width: 100.w,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                image: const DecorationImage(
                    image: AssetImage(ImagePath.coach), fit: BoxFit.cover)),
            child: <PERSON><PERSON>(
              children: [
                Positioned(
                  top: 8.h,
                  left: 7.w,
                  child: Container(
                    width: 40.w,
                    height: 15.h,
                    decoration: BoxDecoration(
                        color: Colors.transparent,
                        boxShadow: [shadow(blurRadius: 4, opacity: 0.25)]),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        customSvgPicture(
                            imagePath: ImagePath.starSvg,
                            height: 14.h,
                            width: 15.w,
                            color: AppPallete.rating),
                        customtext(
                            context: context,
                            newYear: rating.toString(),
                            font: 15.sp,
                            weight: FontWeight.w700,
                            color: Colors.white)
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            width: 8.w,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 11.h,
              ),
              customtext(
                  context: context,
                  newYear: coach,
                  font: 18.sp,
                  weight: FontWeight.w700),
              SizedBox(
                height: 14.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  customSvgPicture(
                      imagePath: ImagePath.locationSvg,
                      height: 18.89.h,
                      width: 18.89.w),
                  customtext(
                      context: context,
                      newYear: location,
                      font: 15.sp,
                      weight: FontWeight.w500),
                ],
              ),
              SizedBox(
                height: 14.h,
              ),
              customtext(
                  context: context,
                  newYear: id,
                  font: 15.sp,
                  weight: FontWeight.w400),
            ],
          ),
          SizedBox(
            width: 50.w,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (situation == "added")
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.check,
                      size: 16.w,
                    ),
                    customtext(
                        context: context,
                        newYear: "Already added",
                        font: 15.sp,
                        weight: FontWeight.w500)
                  ],
                ),
              SizedBox(
                height: height,
              ),
              Button(
                  buttonText: check,
                  color: color,
                  shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                  width: 132.w,
                  height: 28.h,
                  textSize: 15.sp,
                  textColorFinal: textColor,
                  fontWeight: FontWeight.w500),
            ],
          )
        ],
      ),
    ),
  );
}
