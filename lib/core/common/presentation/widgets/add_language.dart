import 'package:class_z/core/imports.dart';


class AddLanguage extends StatefulWidget {
  final String label;
  final double height;
  final double width;
  Function(List<String>)? onLanguagesChanged; // Callback to pass languages back

  AddLanguage({
    super.key,
    required this.label,
    required this.height,
    required this.width,
    this.onLanguagesChanged, // Add this parameter
  });

  @override
  _AddLanguageState createState() => _AddLanguageState();
}

class _AddLanguageState extends State<AddLanguage> {
  List<String> selectedLanguages = [];

  final List<String> languages = [
    'English',
    'Bangla',
    'Hindi',
    'Mandarin',
    'Cantonese'
  ];

  void _updateLanguages() {
    if (widget.onLanguagesChanged != null) {
      widget.onLanguagesChanged!(selectedLanguages); // Call the callback safely
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Wrap(
            spacing: 10.w,
            runSpacing: 10.h,
            children: [
              ...selectedLanguages.map(
                (text) => Container(
                  height: 30.h,
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: AppPallete.paleGrey,
                    borderRadius: BorderRadius.circular(5.0),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      customtext(
                          context: context,
                          newYear: text,
                          font: 14.sp,
                          weight: FontWeight.w400),
                      SizedBox(width: 4.w),
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedLanguages.remove(text);
                          });
                          _updateLanguages();
                        },
                        child: Icon(
                          Icons.close,
                          size: 15.sp,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
        SizedBox(
          height: 20.h,
        ),
        Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: AppPallete.paleGrey,
            borderRadius: BorderRadius.circular(5.0),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              hint: Padding(
                padding: EdgeInsets.only(left: 6.86.w),
                child: customtext(
                    context: context,
                    newYear: widget.label,
                    font: 15.sp,
                    weight: FontWeight.w400),
              ),
              value: null,
              onChanged: (newValue) {
                setState(() {
                  if (newValue != null &&
                      !selectedLanguages.contains(newValue)) {
                    selectedLanguages.add(newValue);
                    _updateLanguages(); // Update languages after adding
                  }
                });
              },
              items: languages.map((language) {
                return DropdownMenuItem(
                  value: language,
                  child: customtext(
                      context: context,
                      newYear: language,
                      font: 15.sp,
                      weight: FontWeight.w400),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }
}
