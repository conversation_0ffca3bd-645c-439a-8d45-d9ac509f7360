import 'package:flutter/material.dart';

void successState({
  required BuildContext context,
  required String title,
  VoidCallback? onDismiss,
}) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green), // Success icon
            SizedBox(width: 10),
            Text(title), // Dialog title
          ],
        ),
        content:
            Text('Your review has been submitted successfully!'), // Message
        actions: [
          TextButton(
            child: Text('OK'),
            onPressed: () {
              Navigator.of(context).pop(); // Close the dialog
              if (onDismiss != null) {
                onDismiss(); // Call the callback if provided
              }
            },
          ),
        ],
      );
    },
  );
}
