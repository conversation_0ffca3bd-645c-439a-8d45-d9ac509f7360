import 'package:class_z/core/imports.dart';

Widget buildTopBar(BuildContext context) {
  return Padding(
    padding: EdgeInsets.only(top: 34.w),
    child: Row(
      children: [
        CustomIconButton(
          icon: Icons.arrow_back_ios,
          color: Colors.red,
          onPressed: () {},
        ),
        Expanded(
          child: Center(
            child: customtext(
              context: context,
              newYear: "Send Request",
              font: 20.sp,
              weight: FontWeight.w700,
            ),
          ),
        ),
        SizedBox(width: 48.w),
      ],
    ),
  );
}
