import 'package:class_z/core/imports.dart';
import 'package:flutter/foundation.dart';

void showCombinedCourseReviewBottomSheet({
  required BuildContext context,
  required ClassModel classModel,
  EventModel? sessionEvent,
  required bool isHalfCourse,
  required String reviewerId,
  required String classId,
  DateTime? sessionDate,
  String? actualReviewerType,
}) {
  // Center review state
  double centerRating = 0.0;
  TextEditingController centerCommentController = TextEditingController();

  // Coach review state
  double coachRating = 0.0;
  TextEditingController coachCommentController = TextEditingController();

  // Get center ID and Name
  String centerId = "";
  String centerName = 'the center';
  String centerImage = '';

  if (classModel.center != null) {
    if (classModel.center is CenterData) {
      final c = classModel.center as CenterData;
      centerId = c.id ?? "";
      centerName = c.displayName ?? centerName;
      if (c.mainImage?.url != null && c.mainImage!.url!.isNotEmpty) {
        // Use direct URL for images that start with http, otherwise prepend the base URL
        if (c.mainImage!.url!.startsWith('http')) {
          centerImage = c.mainImage!.url!;
        } else {
          centerImage = "${AppText.device}${c.mainImage!.url!}";
        }
        print(
            "[CombinedReviewPopup] Center image from CenterData: ${c.mainImage!.url!}");
        print("[CombinedReviewPopup] Processed center image URL: $centerImage");
      } else if (c.images != null &&
          c.images!.isNotEmpty &&
          c.images!.first.url != null &&
          c.images!.first.url!.isNotEmpty) {
        // Use direct URL for images that start with http, otherwise prepend the base URL
        if (c.images!.first.url!.startsWith('http')) {
          centerImage = c.images!.first.url!;
        } else {
          centerImage = "${AppText.device}${c.images!.first.url!}";
        }
        print(
            "[CombinedReviewPopup] Center image from images array: ${c.images!.first.url!}");
        print("[CombinedReviewPopup] Processed center image URL: $centerImage");
      }
    } else if (classModel.center is Map) {
      centerId = classModel.center["_id"] ?? "";
      centerName = classModel.center["displayName"] ?? centerName;
      if (classModel.center['mainImage'] is Map &&
          classModel.center['mainImage']['url'] is String &&
          classModel.center['mainImage']['url'].isNotEmpty) {
        String url = classModel.center['mainImage']['url'];
        // Use direct URL for images that start with http, otherwise prepend the base URL
        if (url.startsWith('http')) {
          centerImage = url;
        } else {
          centerImage = "${AppText.device}$url";
        }
        print("[CombinedReviewPopup] Center image from Map mainImage: $url");
        print("[CombinedReviewPopup] Processed center image URL: $centerImage");
      } else if (classModel.center['images'] is List &&
          classModel.center['images'].isNotEmpty &&
          classModel.center['images'][0]['url'] is String &&
          classModel.center['images'][0]['url'].isNotEmpty) {
        String url = classModel.center['images'][0]['url'];
        // Use direct URL for images that start with http, otherwise prepend the base URL
        if (url.startsWith('http')) {
          centerImage = url;
        } else {
          centerImage = "${AppText.device}$url";
        }
        print("[CombinedReviewPopup] Center image from Map images array: $url");
        print("[CombinedReviewPopup] Processed center image URL: $centerImage");
      }
    } else if (classModel.center is String) {
      centerId = classModel.center; // Assumes it's just an ID string
      // Try to fetch center details if we only have the ID
      if (centerId.isNotEmpty) {
        // This is a placeholder - you may need to implement a function to fetch center details by ID
        print(
            "[CombinedReviewPopup] Only center ID available: $centerId. Consider pre-fetching center details.");
      }
    }
  }
  if (centerId.isEmpty) {
    print("Warning: CombinedReviewPopup - centerId is empty.");
  }

  // Ensure centerId is not null before using it in payload, default to empty if it somehow becomes null after checks
  centerId = centerId ?? "";

  // DEBUG: Print center details
  if (kDebugMode) {
    print(
        '[CombinedReviewPopup] classModel.center type: ${classModel.center.runtimeType}');
    print(
        '[CombinedReviewPopup] classModel.center value: ${classModel.center}');
    print('[CombinedReviewPopup] Extracted centerId for payload: $centerId');
  }

  // Get coach ID, Name, and Image - prioritize sessionEvent
  String coachId = "";
  String coachName = 'the coach';
  String coachImage = '';

  final dynamic eventCoachDetails =
      sessionEvent?.coach; // Access coach object from event
  final dynamic classCoachDetails = classModel.coach;

  print(
      "[CombinedReviewPopup] eventCoachDetails type: ${eventCoachDetails?.runtimeType}");
  print(
      "[CombinedReviewPopup] classCoachDetails type: ${classCoachDetails?.runtimeType}");

  void extractCoachInfo(dynamic coachData) {
    print(
        "[CombinedReviewPopup] Extracting coach info from: ${coachData.runtimeType}");
    print("[CombinedReviewPopup] Coach data: $coachData");

    if (coachData is CoachModel) {
      coachId = coachData.id ?? coachId;
      coachName = coachData.displayName ?? coachName;
      if (coachData.mainImage?.url != null &&
          coachData.mainImage!.url!.isNotEmpty) {
        // Use direct URL for images that start with http, otherwise prepend the base URL
        if (coachData.mainImage!.url!.startsWith('http')) {
          coachImage = coachData.mainImage!.url!;
        } else {
          coachImage = "${AppText.device}${coachData.mainImage!.url!}";
        }
        print(
            "[CombinedReviewPopup] Coach image from CoachModel: ${coachData.mainImage!.url!}");
        print("[CombinedReviewPopup] Processed coach image URL: $coachImage");
      } else if (coachData.images != null &&
          coachData.images!.isNotEmpty &&
          coachData.images!.first.url != null &&
          coachData.images!.first.url!.isNotEmpty) {
        // Use direct URL for images that start with http, otherwise prepend the base URL
        if (coachData.images!.first.url!.startsWith('http')) {
          coachImage = coachData.images!.first.url!;
        } else {
          coachImage = "${AppText.device}${coachData.images!.first.url!}";
        }
        print(
            "[CombinedReviewPopup] Coach image from images array: ${coachData.images!.first.url!}");
        print("[CombinedReviewPopup] Processed coach image URL: $coachImage");
      }
    } else if (coachData is Map) {
      coachId = coachData["_id"] ?? coachId;
      coachName = coachData["displayName"] ?? coachName;
      print("[CombinedReviewPopup] Extracted coach name from Map: $coachName");

      if (coachData['mainImage'] is Map &&
          coachData['mainImage']['url'] is String &&
          coachData['mainImage']['url'].isNotEmpty) {
        String url = coachData['mainImage']['url'];
        // Use direct URL for images that start with http, otherwise prepend the base URL
        if (url.startsWith('http')) {
          coachImage = url;
        } else {
          coachImage = "${AppText.device}$url";
        }
        print("[CombinedReviewPopup] Coach image from Map mainImage: $url");
        print("[CombinedReviewPopup] Processed coach image URL: $coachImage");
      } else if (coachData['images'] is List &&
          coachData['images'].isNotEmpty &&
          coachData['images'][0]['url'] is String &&
          coachData['images'][0]['url'].isNotEmpty) {
        String url = coachData['images'][0]['url'];
        // Use direct URL for images that start with http, otherwise prepend the base URL
        if (url.startsWith('http')) {
          coachImage = url;
        } else {
          coachImage = "${AppText.device}$url";
        }
        print("[CombinedReviewPopup] Coach image from Map images array: $url");
        print("[CombinedReviewPopup] Processed coach image URL: $coachImage");
      }
    }
  }

  if (eventCoachDetails != null) {
    extractCoachInfo(eventCoachDetails);
  } else if (classCoachDetails != null) {
    extractCoachInfo(classCoachDetails);
  }

  if (coachId.isEmpty) {
    print("Warning: CombinedReviewPopup - coachId is empty.");
  }

  // Helper method to mark review as completed in SharedPreferences
  Future<void> _markReviewAsCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    final String firstLessonReviewPrefixKey = 'first_lesson_review_';
    final String completionReviewPrefixKey = 'completion_review_';

    // Mark both types of reviews as completed for this class
    final String firstLessonKey =
        "$firstLessonReviewPrefixKey${reviewerId}_$classId";
    final String completionKey =
        "$completionReviewPrefixKey${reviewerId}_$classId";

    await prefs.setBool(firstLessonKey, true);
    await prefs.setBool(completionKey, true);

    print(
        "[CombinedReviewPopup] Marked review as completed: $firstLessonKey and $completionKey");
  }

  void submitReviews() {
    bool canSubmitCenterReview = centerId.isNotEmpty && centerRating > 0;
    bool canSubmitCoachReview = coachId.isNotEmpty && coachRating > 0;

    if (!canSubmitCenterReview && classModel.center != null) {
      // If center exists but no rating/ID
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                "Please select a rating for the center or ensure center data is available.")),
      );
      return;
    }
    if (!canSubmitCoachReview &&
        (sessionEvent?.coach != null || classModel.coach != null)) {
      // If coach exists but no rating/ID
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                "Please select a rating for the coach or ensure coach data is available.")),
      );
      return;
    }

    // Check if at least one review can be submitted
    if (!canSubmitCenterReview && !canSubmitCoachReview) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                "Please provide a rating for at least one entity (center or coach).")),
      );
      return;
    }

    // Mark review as completed in SharedPreferences
    _markReviewAsCompleted();

    if (canSubmitCenterReview) {
      var centerPayload = {
        "reviewerId": reviewerId,
        "reviewerType": actualReviewerType ?? "user",
        "revieweeId": centerId,
        "revieweeType": "center",
        "classId": classId,
        "rating": centerRating,
        "title": isHalfCourse ? "First Lesson Review" : "Course Completion",
        "comment": centerCommentController.text,
        "date":
            sessionDate?.toIso8601String() ?? DateTime.now().toIso8601String(),
      };
      context.read<ReviewBloc>().add(PostReviewEvent(payload: centerPayload));
    }

    if (canSubmitCoachReview) {
      var coachPayload = {
        "reviewerId": reviewerId,
        "reviewerType": actualReviewerType ?? "user",
        "revieweeId": coachId,
        "revieweeType": "coach",
        "classId": classId,
        "rating": coachRating,
        "title": isHalfCourse ? "First Lesson Review" : "Course Completion",
        "comment": coachCommentController.text,
        "date":
            sessionDate?.toIso8601String() ?? DateTime.now().toIso8601String(),
      };
      context.read<ReviewBloc>().add(PostReviewEvent(payload: coachPayload));
    }

    NavigatorService.goBack();
  }

  // Add semi-transparent overlay
  showGeneralDialog(
    context: context,
    barrierDismissible: false,
    barrierLabel: "Review Dialog",
    barrierColor: Colors.black.withOpacity(0.4),
    transitionDuration: Duration(milliseconds: 300),
    pageBuilder: (_, __, ___) {
      return StatefulBuilder(
        builder: (BuildContext context, StateSetter setState) {
          return Material(
            type: MaterialType.transparency,
            child: Stack(
              children: [
                // Semi-transparent overlay
                Positioned.fill(
                  child: GestureDetector(
                    onTap: () {}, // Prevent closing when tapping outside
                    child: Container(color: Colors.black.withOpacity(0.4)),
                  ),
                ),

                // Review modal
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(40.r),
                        topRight: Radius.circular(40.r),
                      ),
                    ),
                    height: MediaQuery.of(context).size.height * 0.9,
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 20.w, vertical: 40.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Title
                            Center(
                              child: customtext(
                                context: context,
                                newYear: isHalfCourse
                                    ? "First Lesson Review"
                                    : "Course Completion Review",
                                font: 25.sp,
                                weight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(height: 10.h),

                            // Subtitle
                            Center(
                              child: customtext(
                                context: context,
                                newYear: isHalfCourse
                                    ? "Please share your thoughts on the first lesson"
                                    : "Please rate your overall experience with the course",
                                font: 16.sp,
                                weight: FontWeight.w400,
                                textAlign: TextAlign.center,
                              ),
                            ),

                            // Center Profile image
                            Center(
                              child: centerImage.isNotEmpty
                                  ? Container(
                                      height: 80.h,
                                      width: 80.w,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: Colors.grey[200],
                                        image: DecorationImage(
                                          image: NetworkImage(centerImage),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    )
                                  : CircleAvatar(
                                      radius: 40.r,
                                      backgroundColor: Colors.grey[200],
                                      child: Icon(Icons.business,
                                          size: 40.r, color: Colors.grey),
                                    ),
                            ),
                            SizedBox(height: 25.h),

                            // Center Review title
                            customtext(
                              context: context,
                              newYear: "How was $centerName?",
                              font: 18.sp,
                              weight: FontWeight.w500,
                            ),
                            SizedBox(height: 15.h),

                            // Center Star rating
                            Center(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: List.generate(
                                  5,
                                  (index) => GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        centerRating = index + 1.0;
                                      });
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10.w),
                                      child: customSvgPicture(
                                        imagePath: ImagePath.starSvg,
                                        color: index < centerRating
                                            ? AppPallete.rating
                                            : AppPallete.rateGray,
                                        height: 42.h,
                                        width: 42.w,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: 25.h),

                            // Center Comment field
                            Container(
                              decoration: BoxDecoration(
                                color: Color(0xFFF7F6F6),
                                borderRadius: BorderRadius.circular(20.r),
                              ),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 20.w, vertical: 20.h),
                              child: TextField(
                                controller: centerCommentController,
                                decoration: InputDecoration(
                                  hintText: "Type your review...",
                                  hintStyle: TextStyle(
                                    color: Color(0xFF898787),
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  border: InputBorder.none,
                                ),
                                maxLines: 3,
                              ),
                            ),
                            SizedBox(height: 40.h),

                            // Coach Profile image
                            Center(
                              child: coachImage.isNotEmpty
                                  ? Container(
                                      height: 80.h,
                                      width: 80.w,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: Colors.grey[200],
                                        image: DecorationImage(
                                          image: NetworkImage(coachImage),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    )
                                  : CircleAvatar(
                                      radius: 40.r,
                                      backgroundColor: Colors.grey[200],
                                      child: Icon(Icons.person,
                                          size: 40.r, color: Colors.grey),
                                    ),
                            ),
                            SizedBox(height: 25.h),

                            // Coach Review title
                            customtext(
                              context: context,
                              newYear: "How was $coachName?",
                              font: 18.sp,
                              weight: FontWeight.w500,
                            ),
                            SizedBox(height: 15.h),

                            // Coach Star rating
                            Center(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: List.generate(
                                  5,
                                  (index) => GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        coachRating = index + 1.0;
                                      });
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10.w),
                                      child: customSvgPicture(
                                        imagePath: ImagePath.starSvg,
                                        color: index < coachRating
                                            ? AppPallete.rating
                                            : AppPallete.rateGray,
                                        height: 42.h,
                                        width: 42.w,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: 25.h),

                            // Coach Comment field
                            Container(
                              decoration: BoxDecoration(
                                color: Color(0xFFF7F6F6),
                                borderRadius: BorderRadius.circular(20.r),
                              ),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 20.w, vertical: 20.h),
                              child: TextField(
                                controller: coachCommentController,
                                decoration: InputDecoration(
                                  hintText: "Type your review...",
                                  hintStyle: TextStyle(
                                    color: Color(0xFF898787),
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  border: InputBorder.none,
                                ),
                                maxLines: 3,
                              ),
                            ),
                            SizedBox(height: 40.h),

                            // Submit button
                            Center(
                              child: Button(
                                buttonText: "Rate",
                                color: AppPallete.secondaryColor,
                                height: 49.h,
                                width: 249.w,
                                onPressed: submitReviews,
                              ),
                            ),
                            SizedBox(height: 20.h),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    },
  );
}

void showCourseReviewBottomSheet({
  required BuildContext context,
  required ClassModel classModel,
  required bool isHalfCourse,
  required String revieweeType,
  required String revieweeId,
  required String reviewerId,
  required String classId,
  DateTime? sessionDate,
  String? actualReviewerType,
}) {
  double rating = 0.0;
  TextEditingController commentController = TextEditingController();

  // Get reviewee name and image
  String revieweeName = revieweeType.toLowerCase() == "center"
      ? (classModel.center?.displayName ?? 'the center')
      : (classModel.coach?.displayName ?? 'the coach');
  String revieweeImage = '';
  try {
    if (revieweeType.toLowerCase() == "center" &&
        classModel.center is CenterData) {
      final center = classModel.center as CenterData;
      if (center.mainImage != null && center.mainImage!.url != null) {
        revieweeImage = center.mainImage!.url!;
      } else if (center.images != null &&
          center.images!.isNotEmpty &&
          center.images!.first.url != null) {
        revieweeImage = center.images!.first.url!;
      }
    } else if (revieweeType.toLowerCase() == "coach" &&
        classModel.coach is CoachModel) {
      final coach = classModel.coach as CoachModel;
      if (coach.mainImage != null && coach.mainImage!.url != null) {
        revieweeImage = coach.mainImage!.url!;
      } else if (coach.images != null &&
          coach.images!.isNotEmpty &&
          coach.images!.first.url != null) {
        revieweeImage = coach.images!.first.url!;
      }
    }
  } catch (e) {
    print("Error loading reviewee image: $e");
    revieweeImage = '';
  }

  void submitReview() {
    if (rating == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Please select a rating")),
      );
      return;
    }

    var payload = {
      "reviewerId": reviewerId,
      "reviewerType": actualReviewerType ?? "user",
      "revieweeId": revieweeId,
      "revieweeType": revieweeType.toLowerCase(),
      "classId": classId,
      "rating": rating,
      "title": isHalfCourse ? "First Lesson Review" : "Course Completion",
      "comment": commentController.text,
      "date":
          sessionDate?.toIso8601String() ?? DateTime.now().toIso8601String(),
    };

    context.read<ReviewBloc>().add(PostReviewEvent(payload: payload));
    NavigatorService.goBack();
  }

  showGeneralDialog(
    context: context,
    barrierDismissible: false,
    barrierLabel: "Review Dialog",
    barrierColor: Colors.black.withOpacity(0.4),
    transitionDuration: Duration(milliseconds: 300),
    pageBuilder: (_, __, ___) {
      return StatefulBuilder(
        builder: (BuildContext context, StateSetter setState) {
          return Material(
            type: MaterialType.transparency,
            child: Stack(
              children: [
                // Semi-transparent overlay
                Positioned.fill(
                  child: GestureDetector(
                    onTap: () {}, // Prevent closing when tapping outside
                    child: Container(color: Colors.black.withOpacity(0.4)),
                  ),
                ),

                // Review modal
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(40.r),
                        topRight: Radius.circular(40.r),
                      ),
                    ),
                    height: MediaQuery.of(context).size.height * 0.9,
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 20.w, vertical: 40.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Title
                            Center(
                              child: customtext(
                                context: context,
                                newYear: isHalfCourse
                                    ? "First Lesson Review"
                                    : "Course Completion Review",
                                font: 25.sp,
                                weight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(height: 10.h),

                            // Subtitle
                            Center(
                              child: customtext(
                                context: context,
                                newYear: isHalfCourse
                                    ? "Please share your thoughts on the first lesson"
                                    : "Please rate your overall experience with the course",
                                font: 16.sp,
                                weight: FontWeight.w400,
                                textAlign: TextAlign.center,
                              ),
                            ),

                            // Reviewee Profile image
                            Center(
                              child: CircleAvatar(
                                radius: 40.r,
                                backgroundImage: revieweeImage.isNotEmpty
                                    ? NetworkImage(revieweeImage)
                                    : null,
                                backgroundColor: Colors.grey[200],
                                child: revieweeImage.isEmpty
                                    ? Icon(Icons.person,
                                        size: 40.r, color: Colors.grey)
                                    : null,
                              ),
                            ),
                            SizedBox(height: 25.h),

                            // Reviewee Review title
                            customtext(
                              context: context,
                              newYear: "How was $revieweeName?",
                              font: 18.sp,
                              weight: FontWeight.w500,
                            ),
                            SizedBox(height: 15.h),

                            // Reviewee Star rating
                            Center(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: List.generate(
                                  5,
                                  (index) => GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        rating = index + 1.0;
                                      });
                                    },
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10.w),
                                      child: customSvgPicture(
                                        imagePath: ImagePath.starSvg,
                                        color: index < rating
                                            ? AppPallete.rating
                                            : AppPallete.rateGray,
                                        height: 42.h,
                                        width: 42.w,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: 25.h),

                            // Comment field
                            Container(
                              decoration: BoxDecoration(
                                color: Color(0xFFF7F6F6),
                                borderRadius: BorderRadius.circular(20.r),
                              ),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 20.w, vertical: 20.h),
                              child: TextField(
                                controller: commentController,
                                decoration: InputDecoration(
                                  hintText: "Type your review...",
                                  hintStyle: TextStyle(
                                    color: Color(0xFF898787),
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  border: InputBorder.none,
                                ),
                                maxLines: 3,
                              ),
                            ),
                            SizedBox(height: 40.h),

                            // Submit button
                            Center(
                              child: Button(
                                buttonText: "Rate",
                                color: AppPallete.secondaryColor,
                                height: 49.h,
                                width: 249.w,
                                onPressed: submitReview,
                              ),
                            ),
                            SizedBox(height: 20.h),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    },
  );
}
