import 'package:class_z/core/imports.dart';

Widget changeChildCard(
    {required BuildContext context,
    required String imagePath,
    required String name,
    required bool sen,
    required String school,
    required String dateTime}) {
  // final String date = DateFormat('dd/MM/yyyy').format(dateTime);
  return Container(
    width: 390.w,
   // height: 105.h,
    decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
    child: Row(
      children: [
        SizedBox(
          width: 12.w,
        ),
        CustomImageBuilder(
            imagePath: imagePath,
            height: 80.h,
            width: 80.w,
            borderRadius: 99.r),
        SizedBox(
          width: 11.w,
        ),
        Padding(
          padding: EdgeInsets.only(top: 11.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  customtext(
                      context: context,
                      newYear: name,
                      font: 17.sp,
                      weight: FontWeight.w500),
                  Sized<PERSON>ox(
                    width: 15.w,
                  ),
                  customSen()
                ],
              ),
              SizedBox(
                height: 17.h,
              ),
              customtext(
                  context: context,
                  newYear: school,
                  font: 13.sp,
                  weight: FontWeight.w400),
              SizedBox(
                height: 17.h,
              ),
              customtext(
                  context: context,
                  newYear: dateTime,
                  font: 13.sp,
                  weight: FontWeight.w400),
            ],
          ),
        )
      ],
    ),
  );
}
