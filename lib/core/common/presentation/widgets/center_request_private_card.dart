import 'package:class_z/core/imports.dart';
class CenterRequestPrivateCard extends StatefulWidget {
  final String imagePath;
  final String center;
  final String course;
  final String rating;
  final String date;
  final String duration;
  final String classTime;
  final String location;
  final String totalStudent;
  final bool sen;
  final String remarks;

  CenterRequestPrivateCard({
    required this.imagePath,
    required this.center,
    required this.course,
    required this.rating,
    required this.date,
    required this.duration,
    required this.classTime,
    required this.location,
    required this.totalStudent,
    required this.sen,
    required this.remarks,
  });

  @override
  _CenterRequestPrivateCardState createState() =>
      _CenterRequestPrivateCardState();
}

class _CenterRequestPrivateCardState extends State<CenterRequestPrivateCard> {
  bool offerMade = false;
  final totalAmountController = TextEditingController();
  void _makeOffer() {
    setState(() {
      offerMade = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 380.w,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.r),
          color: Colors.white,
          boxShadow: [
            shadow(blurRadius: 15, opacity: 0.1, xoffset: 0, yoffset: 0)
          ]),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ///heading

          Container(
            height: 49.h,
            width: 380.w,
            padding: EdgeInsets.only(left: 15.w, right: 12.w),
            decoration: BoxDecoration(
                color: AppPallete.color185,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    topRight: Radius.circular(20.r))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    CustomImageBuilder(
                        imagePath: widget.imagePath,
                        height: 22.h,
                        width: 22.w,
                        borderRadius: 99.r),
                    SizedBox(
                      width: 7.w,
                    ),
                    customtext(
                        context: context,
                        newYear: widget.center,
                        font: 15.sp,
                        weight: FontWeight.w600),
                  ],
                ),
                Row(
                  children: [
                    Icon(
                      Icons.message_outlined,
                      size: 14.w,
                      color: AppPallete.darkGrey,
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    Row(
                      children: [
                        customSvgPicture(
                            imagePath: ImagePath.starSvg,
                            height: 14.h,
                            width: 14.w,
                            color: AppPallete.rating),
                        customtext(
                            context: context,
                            newYear: widget.rating,
                            font: 15.sp,
                            weight: FontWeight.w700),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),

          ///rest
          Padding(
            padding: EdgeInsets.only(left: 15.5.w, right: 15.5.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 12.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      height: 12.h,
                      child: customtext(
                          context: context,
                          newYear: widget.date,
                          font: 12.sp,
                          weight: FontWeight.w500),
                    ),
                    Row(
                      children: [
                        customSvgPicture(
                            imagePath: ImagePath.locationSvg,
                            height: 16.67.h,
                            width: 11.67.w,
                            color: AppPallete.darkGrey),
                        SizedBox(
                          height: 15.h,
                          child: customtext(
                              context: context,
                              newYear: "Upon Request",
                              font: 14.sp,
                              weight: FontWeight.w500),
                        ),
                      ],
                    )
                  ],
                ),
                SizedBox(
                  height: 15.h,
                ),
                SizedBox(
                  height: 20.h,
                  child: customtext(
                      context: context,
                      newYear: widget.course,
                      font: 20.sp,
                      weight: FontWeight.w500),
                ),
                SizedBox(
                  height: 11.h,
                ),
                SizedBox(
                  height: 20.h,
                  child: Row(
                    children: [
                      customtext(
                          context: context,
                          newYear: widget.classTime,
                          font: 20.sp,
                          weight: FontWeight.w500),
                      SizedBox(
                        width: 12.w,
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 7.h),
                        child: customtext(
                            context: context,
                            newYear: widget.duration,
                            font: 12.sp,
                            weight: FontWeight.w500),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 22.h,
                ),
                customDivider(width: 343.w, padding: 0),
                SizedBox(
                  height: 21.h,
                ),
                customtext(
                    context: context,
                    newYear: "Coaching address:",
                    font: 15.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 20.h,
                ),
                customtext(
                    context: context,
                    newYear: widget.location,
                    font: 15.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 11.38.h,
                ),
                customDivider(width: 343.w, padding: 0),
                SizedBox(
                  height: 21.h,
                ),
                customtext(
                    context: context,
                    newYear: "Number of Student:",
                    font: 15.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 20.h,
                ),
                Row(
                  children: [
                    customtext(
                        context: context,
                        newYear: widget.totalStudent,
                        font: 15.sp,
                        weight: FontWeight.w500),
                    SizedBox(
                      width: 3.w,
                    ),
                    if (widget.sen == true)
                      customtext(
                          context: context,
                          newYear: "(SEN service needed)",
                          font: 15.sp,
                          weight: FontWeight.w500),
                  ],
                ),
                SizedBox(
                  height: 8.h,
                ),
                customDivider(width: 343.w, padding: 0),
                SizedBox(
                  height: 21.h,
                ),
                customtext(
                    context: context,
                    newYear: "Remarks:",
                    font: 15.sp,
                    weight: FontWeight.w500),
                SizedBox(
                  height: 20.h,
                ),
                SizedBox(
                  height: 32.h,
                  child: customtext(
                      context: context,
                      newYear: widget.remarks,
                      font: 15.sp,
                      weight: FontWeight.w500),
                ),
                SizedBox(
                  height: 11.38.h,
                ),
                customDivider(width: 343.w, padding: 0),
                SizedBox(
                  height: 20.h,
                ),
                customtext(
                    context: context,
                    newYear: "By accepting, you acknowledged that",
                    font: 13.sp,
                    weight: FontWeight.w400),
                SizedBox(
                  height: 15.h,
                ),
                customtext(
                    context: context,
                    newYear:
                        "1. You have responsibility to coach the class on time with quality service",
                    font: 13.sp,
                    weight: FontWeight.w400),
                SizedBox(
                  height: 15.h,
                ),
                Row(
                  children: [
                    customtext(
                        context: context,
                        newYear: "2. Missed class may be subject to a",
                        font: 13.sp,
                        weight: FontWeight.w400),
                    customtext(
                        context: context,
                        newYear: " service charge",
                        font: 13.sp,
                        weight: FontWeight.w400,
                        color: AppPallete.secondaryColor),
                  ],
                ),
                SizedBox(
                  height: 15.h,
                ),
                customtext(
                    context: context,
                    newYear:
                        "3. You acknowledged the location of the class would be nearby the mentioned coaching address upon and agreed to deliver class correspondingly",
                    font: 13.sp,
                    weight: FontWeight.w400),
                SizedBox(
                  height: 15.h,
                ),
                customtext(
                    context: context,
                    newYear:
                        "4. You agreed to deliver class according to the amenities and description you have declared",
                    font: 13.sp,
                    weight: FontWeight.w400),
                SizedBox(
                  height: 19.h,
                ),
                customDivider(width: 343.w, padding: 0),
                if (offerMade == false)
                  _bottomLine(context: context)
                else
                  _totalPrice(context: context),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _bottomLine({required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.only(left: 55.w, right: 17.5.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          customtext(
              context: context,
              newYear: "Reject",
              font: 15.sp,
              weight: FontWeight.w500),
          Container(
            width: 1.w,
            height: 46.h,
            color: AppPallete.greyWord,
          ),
          GestureDetector(
            onTap: () {
              setState(() {
                _makeOffer();
              });
            },
            child: customtext(
                context: context,
                newYear: "Make your price offer",
                color: AppPallete.secondaryColor,
                font: 15.sp,
                weight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget _totalPrice({required BuildContext context}) {
    return Padding(
      padding: EdgeInsets.only(left: 3.w, right: 3.w, top: 6.h, bottom: 6.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            height: 15.h,
            width: 71.w,
            child: customtext(
                context: context,
                newYear: "Total Price:",
                font: 15.sp,
                weight: FontWeight.w500),
          ),
          SizedBox(
            width: 13.w,
          ),
          AuthField(
            controller: totalAmountController,
            width: 211.w,
            height: 34.h,
            hintText: "HKD",
            keyboard: TextInputType.number,
          ),
          SizedBox(
            width: 13.w,
          ),
          Container(
            width: 30.w,
            height: 30.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(99.r),
                color: AppPallete.secondaryColor),
            child: Icon(
              Icons.send,
              color: Colors.white,
              size: 24.w,
            ),
          )
        ],
      ),
    );
  }
}
