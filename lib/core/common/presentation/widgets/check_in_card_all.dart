import 'package:class_z/core/imports.dart';

Widget checkInCardAll(
    {required BuildContext context,
    required EventModel event,
    required VoidCallback onTap}) {
  final String formatedDate = DateFormat("dd-MM-yyyy").format(event.date!);
  double width = MediaQuery.of(context).size.width;
  return GestureDetector(
    onTap: onTap,
    child: Container(
      height: 143.h,
      width: 186.w,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20.r)),
      child: Stack(
        children: [
          Positioned(
              child: CustomImageBuilder(
                  imagePath: imageStringGenerator(
                      imagePath: event.classId?.mainImage?.url ?? ''),
                  height: 143.h,
                  width: width,
                  borderRadius: 20.r)),
          Positioned(
              top: 8.h,
              left: 9.w,
              right: 11.w,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    height: 22.h,
                    width: 73.w,
                    decoration: BoxDecoration(
                        color: AppPallete.secondaryColor,
                        borderRadius: BorderRadius.circular(20.r)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 6.w,
                        ),
                        customSvgPicture(
                            imagePath: ImagePath.locationSvg,
                            height: 16.67.w,
                            width: 11.67.w,
                            color: Colors.white),
                        SizedBox(
                          width: 5.33.w,
                        ),
                        customtext(
                            context: context,
                            newYear: "center",
                            font: 15.sp,
                            color: Colors.white,
                            weight: FontWeight.w400,
                            shadows: [shadow(blurRadius: 4, opacity: 0.25)]),
                      ],
                    ),
                  ),
                  Container(
                    width: 88.w,
                    height: 18.h,
                    decoration: BoxDecoration(
                        color: AppPallete.white,
                        borderRadius: BorderRadius.circular(20.r),
                        boxShadow: [shadow(blurRadius: 4, opacity: 0.25)]),
                    child: Center(
                      child: customtext(
                          context: context,
                          newYear: event.title ?? '',
                          font: 15.sp,
                          weight: FontWeight.w400,
                          color: AppPallete.darkGrey),
                    ),
                  )
                ],
              )),
          Positioned(
              top: 95.h,
              left: 10.w,
              right: 11.w,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  customtext(
                      context: context,
                      newYear: event.title ?? 'Unkwown',
                      font: 18.sp,
                      weight: FontWeight.w700,
                      color: Colors.white,
                      shadows: [shadow(blurRadius: 4, opacity: 0.25)]),
                  customtext(
                      context: context,
                      newYear: formatedDate,
                      font: 18.sp,
                      weight: FontWeight.w700,
                      color: Colors.white,
                      shadows: [shadow(blurRadius: 4, opacity: 0.25)])
                ],
              )),
          Positioned(
              top: 116.h,
              left: 10.w,
              right: 11.w,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      customSvgPicture(
                          imagePath: ImagePath.groupSvg,
                          height: 10.h,
                          width: 11.w,
                          color: Colors.white),
                      SizedBox(
                        width: 4.w,
                      ),
                      customtext(
                          context: context,
                          newYear: "3/5",
                          font: 18.sp,
                          weight: FontWeight.w700,
                          color: Colors.white,
                          shadows: [shadow(blurRadius: 4, opacity: 0.25)]),
                      SizedBox(
                        width: 12.w,
                      ),
                      customtext(
                          context: context,
                          newYear: "Age ${event.classId?.ageFrom}",
                          font: 18.sp,
                          weight: FontWeight.w700,
                          color: Colors.white,
                          shadows: [shadow(blurRadius: 4, opacity: 0.25)]),
                    ],
                  ),
                  customtext(
                      context: context,
                      newYear: 'time',
                      font: 18.sp,
                      weight: FontWeight.w700,
                      color: Colors.white,
                      shadows: [shadow(blurRadius: 4, opacity: 0.25)]),
                ],
              )),
        ],
      ),
    ),
  );
}
