import 'package:class_z/core/imports.dart';

Widget studentListCard(
    {required BuildContext context,
    required String imagePath,
    required String name,
    required String rating,
    required String age,
    required bool sen,
    required String id}) {
      print(imagePath);
  return Container(
    width: 408.w,
    height: 111.h,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Stack(
              children: [
                CustomImageBuilder(
                    imagePath: imagePath,
                    height: 100.h,
                    width: 100.w,
                    borderRadius: 20.r),
                Positioned(
                  top: 8.h,
                  left: 7.w,
                  child: Container(
                    height: 12.h,
                    width: 34.w,
                    decoration: BoxDecoration(
                        boxShadow: [shadow(blurRadius: 4, opacity: 0.25)]),
                    child: Row(
                      children: [
                        customSvgPicture(
                            imagePath: ImagePath.starSvg,
                            height: 11.h,
                            width: 13.w,
                            color: AppPallete.rating),
                        customtext(
                            context: context,
                            newYear: rating,
                            font: 12.sp,
                            color: Colors.white,
                            weight: FontWeight.w700)
                      ],
                    ),
                  ),
                )
              ],
            ),
            SizedBox(
              width: 8.w,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    customtext(
                        context: context,
                        newYear: name,
                        font: 18.sp,
                        weight: FontWeight.w700),
                    SizedBox(
                      width: 8.w,
                    ),
                    if(sen==true)
                    customSen()
                  ],
                ),
                SizedBox(
                  height: 16.h,
                ),
                customtext(
                    context: context,
                    newYear: "Age: $age",
                    font: 15.sp,
                    weight: FontWeight.w400),
                SizedBox(
                  height: 16.h,
                ),
                customtext(
                    context: context,
                    newYear: id,
                    font: 15.sp,
                    weight: FontWeight.w400),
              ],
            ),
            Padding(
              padding: EdgeInsets.only(left: 0,right: 10.w, top: 63.h),
              child: Button(
                  shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                  buttonText: "Message",
                  onPressed: () {
                    NavigatorService.pushNamed(AppRoutes.centerMessage);
                  },
                  color: AppPallete.secondaryColor,
                  width: 132.w,
                  fontWeight: FontWeight.w500,
                  textSize: 15.sp,
                  height: 28.h),
            ),
          ],
        ),
        SizedBox(
          height: 10.h,
        ),
        customDivider(width: 408.w, padding: 0)
      ],
    ),
  );
}
