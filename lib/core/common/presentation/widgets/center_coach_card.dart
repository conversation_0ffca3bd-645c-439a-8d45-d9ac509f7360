import 'package:class_z/core/imports.dart';

class CenterCoachCard extends StatefulWidget {
  final String coach;
  final String location;
  final String image;
  final String id;
  final double rating;
  final bool added;
  final bool? pending;
  final VoidCallback onTap;
  final bool manager;

  const CenterCoachCard({
    super.key,
    required this.coach,
    required this.location,
    required this.image,
    required this.id,
    required this.rating,
    required this.added,
    this.pending,
    required this.onTap,
    required this.manager,
  });

  @override
  State<CenterCoachCard> createState() => _CenterCoachCardState();
}

class _CenterCoachCardState extends State<CenterCoachCard> {
  late String buttonState;

  @override
  void initState() {
    super.initState();
    buttonState = widget.added ? 'added' : 'notAdded';
    print(
        "CenterCoachCard initialized with state: $buttonState, added: ${widget.added}, pending: ${widget.pending}, coach: ${widget.coach}, id: ${widget.id}");
  }

  @override
  void didUpdateWidget(CenterCoachCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update button state when widget properties change
    if (oldWidget.added != widget.added) {
      setState(() {
        buttonState = widget.added ? 'added' : 'notAdded';
      });
    }
  }

  void _setButtonState(String state) {
    print("Setting button state to: $state");
    setState(() => buttonState = state);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<OwnerBloc, OwnerState>(
      listener: (context, state) {
        if (state is OwnerErrorState) {
          // Check if the error is "already assigned" - this means the coach was actually added
          if (state.message.contains("already assigned") ||
              state.message.contains("Coach already assigned to this center")) {
            _setButtonState('added');
            // ScaffoldMessenger.of(context).showSnackBar(
            //   const SnackBar(content: Text('Coach is already assigned to this centre')),
            // );
          } else {
            errorState(context: context, error: state.message);
          }
        } else if (state is AssignCoachSuccessState && state.success == true) {
          // When request is successfully sent
          _setButtonState('pending');
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Request sent to coach successfully')),
          );
        } else if (state is RemoveCoachSuccessState) {
          // When coach is successfully removed
          _setButtonState('notAdded');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Coach removed successfully')),
          );
        }
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildCoachDetails(),
          _buildActionButton(),
        ],
      ),
    );
  }

  Widget _buildCoachDetails() {
    return Row(
      children: [
        _buildCoachImage(),
        SizedBox(width: 8.w),
        _buildCoachTextInfo(),
      ],
    );
  }

  Widget _buildCoachImage() {
    return Container(
      height: 100.h,
      width: 100.w,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20.r)),
      child: Stack(
        children: [
          CustomImageBuilder(
            borderRadius: 20,
            imagePath: widget.image,
            height: 100.h,
            width: 100.w,
          ),
          Positioned(
            top: 8.h,
            left: 7.w,
            child: customRating(
              context: context,
              rating: widget.rating.toString(),
              fontSize: 12.sp,
              weight: FontWeight.w700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCoachTextInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 11.h),
        customtext(
          context: context,
          newYear: widget.coach,
          font: 18.sp,
          weight: FontWeight.w700,
        ),
        SizedBox(height: 14.h),
        Row(
          children: [
            customSvgPicture(
              imagePath: ImagePath.locationSvg,
              height: 18.89.h,
              width: 18.89.w,
            ),
            SizedBox(width: 4.w),
            customtext(
              context: context,
              newYear: widget.location.isNotEmpty ? widget.location : "Unknown",
              font: 15.sp,
              weight: FontWeight.w500,
            ),
          ],
        ),
        SizedBox(height: 14.h),
        SizedBox(
          width: 82.w,
          child: customtext(
            context: context,
            newYear: widget.id,
            font: 15.sp,
            weight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton() {
    // If coach is already added to the center or button state shows added
    if (widget.added || buttonState == 'added') {
      print(
          "Coach ${widget.coach} is already added - showing Remove coach button");
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _styledButton(
            text: "Display to public",
            color: AppPallete.secondaryColor,
          ),
          SizedBox(height: 10.h),
          _styledButton(
            text: "Remove coach",
            color: AppPallete.white,
            textColor: Colors.black,
            shadows: [shadow(blurRadius: 15, opacity: 0.1)],
            onPressed: widget.onTap,
          ),
        ],
      );
    }

    // If coach has a pending request or button was clicked to send a request
    final isPending = widget.pending == true || buttonState == 'pending';

    print(
        "Coach ${widget.coach} is not added - isPending: $isPending, buttonState: $buttonState");

    return _styledButton(
      text: isPending ? "Pending" : "Add Coach",
      color: isPending ? Colors.grey.shade400 : AppPallete.secondaryColor,
      onPressed: isPending
          ? null // Disable button if pending
          : () {
              print("Add Coach button clicked for ${widget.coach}");
              widget.onTap();
              _setButtonState('pending'); // Update local state to show pending
            },
    );
  }

  Widget _styledButton({
    required String text,
    required Color color,
    Color? textColor,
    List<BoxShadow>? shadows,
    VoidCallback? onPressed,
  }) {
    return Button(
      borderRadius: 5,
      buttonText: text,
      color: color,
      textColorFinal: textColor,
      shadows: shadows,
      width: 132.w,
      height: 28.h,
      textSize: 15.sp,
      fontWeight: FontWeight.w500,
      onPressed: onPressed,
    );
  }
}
