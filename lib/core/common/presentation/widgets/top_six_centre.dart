import 'package:class_z/core/imports.dart';
Widget topSix({
  required String name,
  required String location,
  required String skills,
  required String imagePath,
  required int strike,
}) {
  return GestureDetector(
    onTap: () {
      NavigatorService.pushNamed(AppRoutes.centreView);
    },
    child: Sized<PERSON><PERSON>(
      width: 194.w,
      height: 45.83.h,
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(35.0.r),
            child: Image.asset(
              imagePath, // Replace with the actual image URL
              width: 37.0.w,
              height: 37.0.h,
              fit: BoxFit.cover,
            ),
          ),
          SizedBox(
            width: 6.w,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    fontSize: 10.0.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 6.0.h),
                Text(
                  location,
                  style:
                      TextStyle(fontSize: 10.0.sp, color: AppPallete.greyWord),
                ),
                <PERSON><PERSON><PERSON><PERSON>(height: 6.0.h),
                SizedBox(
                  width: 110.w,
                  height: 10.h,
                  child: Text(
                    skills,
                    style: TextStyle(
                      fontSize: 10.0.sp,
                      color: AppPallete.greyWord,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              customSvgPicture(
                  imagePath: ImagePath.fireSvg,
                  height: 11.h,
                  width: 9.w,
                  color: AppPallete.secondaryColor),
              SizedBox(
                width: 2.w,
              ),
              Text(
                '$strike',
                style: TextStyle(
                  fontSize: 10.0.sp,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ],
      ),
    ),
  );
}
