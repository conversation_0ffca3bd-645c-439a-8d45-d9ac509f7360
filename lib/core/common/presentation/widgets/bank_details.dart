import 'package:class_z/core/imports.dart';
class BankDetailsSTL extends StatefulWidget {
  final String label;
  final double height;
  final double width;
  final Function(String?) onBankSelected;
  const BankDetailsSTL(
      {super.key,
      required this.height,
      required this.width,
      required this.onBankSelected,
      required this.label});

  @override
  _BankDetailsSTLState createState() => _BankDetailsSTLState();
}

class _BankDetailsSTLState extends State<BankDetailsSTL> {
  String? selectedTime;

  List<String> times = [
    "HSBC (Hong Kong and Shanghai Banking Corporation)",
    "Standard Chartered Bank (Hong Kong)",
    "Bank of China (Hong Kong)",
    "Hang Seng Bank",
    "China CITIC Bank International",
    "DBS Bank (Hong Kong)",
    "Bank of East Asia (BEA)",
    "OCBC Wing Hang Bank",
    "Citibank (Hong Kong)",
    "Fubon Bank (Hong Kong)",
    "China Merchants Bank (Hong Kong)",
    "Shanghai Commercial and Savings Bank",
    "Industrial and Commercial Bank of China (ICBC) (Asia)",
    "Chong Hing Bank",
    "Nanyang Commercial Bank",
    "BOC Credit Card (International) Ltd.",
    "Bank of Communications Co., Ltd. (Hong Kong Branch)",
    "China Construction Bank (Asia)",
    "Taipei Fubon Commercial Bank",
    "Shanghai Pudong Development Bank (Hong Kong Branch)"
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: AppPallete.paleGrey,
        borderRadius: BorderRadius.circular(5.0),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          hint: Padding(
            padding: EdgeInsets.only(left: 6.86.w),
            child: customtext(
                context: context,
                newYear: widget.label,
                font: 15.sp,
                weight: FontWeight.w400),
          ),
          value: selectedTime,
          onChanged: (newValue) {
            setState(() {
              selectedTime = newValue;
            });
            widget.onBankSelected(newValue);
          },
          items: times.map((time) {
            return DropdownMenuItem(
              value: time,
              child: Padding(
                padding: EdgeInsets.only(left: 8.w),
                child: SizedBox(
                  width: widget.width - 40.w,
                  child: customtext(
                      context: context,
                      newYear: time,
                      font: 15.sp,
                      weight: FontWeight.w400),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
