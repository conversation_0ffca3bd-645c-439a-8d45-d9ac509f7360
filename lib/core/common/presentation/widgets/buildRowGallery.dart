import 'package:class_z/core/imports.dart';

Widget buildRowGallery(
    {required BuildContext context,
    required String startAge,
    required String center,
    required String location,
    required double imageHeight,
    required double imageWidth,
    required String category,
    required String imagepath,
    required double rating,
    required VoidCallback onTap,
    bool isSaved = false,
    String? centerId,
    bool isSenService = false,
    bool showZCoinIcon = false, // Add parameter to control Z icon display
    Function(bool)? onFavoriteToggle}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      borderRadius: BorderRadius.circular(20),
      onTap: onTap,
      child: Container(
        height: imageHeight,
        width: imageWidth,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(20)),
        ),
        child: Stack(
          children: [
            CustomImageBuilder(
                borderRadius: 20.r,
                imagePath: imagepath,
                height: imageHeight,
                width: double.infinity),
            Positioned(
              top: 14.h,
              left: 9.w,
              child: Container(
                padding: EdgeInsets.only(left: 2.5.w, right: 4.w),
                decoration: BoxDecoration(
                    color: AppPallete.secondaryColor,
                    borderRadius: BorderRadius.circular(20.r),
                    boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
                child: Row(
                  children: [
                    // Only show Z icon when using ZCoin currency
                    if (showZCoinIcon) ...[
                      customSvgPicture(
                          imagePath: ImagePath.zSvg,
                          height: 15.h,
                          width: 15,
                          color: Colors.white),
                      SizedBox(width: 4.w),
                    ],
                    Text(
                      startAge.isNotEmpty ? startAge : "Contact",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 15.sp,
                          fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              top: 14.h,
              right: 12.57.w,
              child: Container(
                width: 48.75.w,
                height: 20.28.h,
                decoration: BoxDecoration(
                  color: AppPallete.secondaryColor,
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    customSvgPicture(
                        imagePath: ImagePath.starSvg,
                        height: 14.h,
                        width: 15.w,
                        color: AppPallete.rating),
                    customtext(
                        context: context,
                        newYear: rating > 0 ? rating.toStringAsFixed(1) : "NEW",
                        font: rating > 0 ? 15.sp : 12.sp,
                        weight: FontWeight.w700,
                        shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                        color: Colors.white)
                  ],
                ),
              ),
            ),
            // Only show SEN badge if center actually offers SEN services
            if (isSenService)
              Positioned(
                top: 37.h,
                right: 14.32.w,
                child: Container(
                  width: 32.w,
                  margin: EdgeInsets.only(right: 2.w),
                  padding: EdgeInsets.symmetric(horizontal: 4.w),
                  decoration: BoxDecoration(
                      color: AppPallete.white,
                      borderRadius: BorderRadius.circular(20.w)),
                  child: Center(
                      child: customtext(
                          context: context,
                          newYear: "SEN",
                          font: 12.sp,
                          weight: FontWeight.w400,
                          color: AppPallete.secondaryColor)),
                ),
              ),
            Positioned(
              bottom: 34.h,
              left: 10.w,
              child: SizedBox(
                height: 20.h,
                child: customtext(
                    context: context,
                    newYear: center,
                    font: 15.sp,
                    weight: FontWeight.w700,
                    shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                    color: Colors.white),
              ),
            ),
            Positioned(
              bottom: 21.17.h,
              left: 10.w,
              child: SizedBox(
                height: 15.23.h,
                child: customtext(
                    context: context,
                    newYear: location,
                    font: 13.sp,
                    weight: FontWeight.w400,
                    shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                    color: Colors.white),
              ),
            ),
            Positioned(
              bottom: 3.17.h,
              left: 10.w,
              child: SizedBox(
                height: 18.28.h,
                child: customtext(
                    context: context,
                    newYear: category,
                    font: 13.sp,
                    weight: FontWeight.w400,
                    shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                    color: Colors.white),
              ),
            ),
            Positioned(
                right: 17.13.w,
                bottom: 12.69.w,
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(20),
                    onTap: () {
                      if (onFavoriteToggle != null && centerId != null) {
                        onFavoriteToggle(!isSaved);
                      }
                    },
                    child: customSvgPicture(
                        imagePath: ImagePath.heartSvg,
                        height: 25.h,
                        color: isSaved ? AppPallete.red : AppPallete.white,
                        width: 25.w),
                  ),
                ))
          ],
        ),
      ),
    ),
  );
}

// Using a different name to avoid conflict
Widget buildCenterRowGallery(
    {required BuildContext context,
    required CenterData center,
    required bool isSaved,
    required Function(bool) onFavoriteToggle,
    VoidCallback? onTap,
    double? imageHeight,
    double? imageWidth,
    ClassModel? fromClass}) {
  final String centerName = center.legalName ?? "Centre";
  final String location = center.address?.address1 ?? "Location not available";
  final String category = center.description ?? "Educational Centre";

  // Fix image path construction
  final String imagePath = center.mainImage?.url != null
      ? "${AppText.device}${center.mainImage!.url}"
      : ""; // Handle null image

  // Fix rating display - show actual rating or hide if no reviews
  final double rating = center.rating ?? 0.0;

  final String? centerId = center.id;

  // Check if this center provides SEN services
  final bool isSenService = center.sen ?? false;

  // Calculate pricing like in centre_view.dart
  final parentData = locator<SharedRepository>().getParentData();
  final userBalance = parentData?.balance ?? 0;

  // Get pricing from backend or fallback to class charge
  double? priceFrom = center.priceFrom?.toDouble();
  double? priceTo = center.priceTo?.toDouble();

  // FRONTEND WORKAROUND: If center doesn't have pricing but we have class data, use class charge
  if ((priceFrom == null || priceTo == null) &&
      fromClass != null &&
      fromClass.charge != null) {
    final classCharge = fromClass.charge!.toDouble();
    priceFrom ??= classCharge;
    priceTo ??= classCharge;
    print('Using class charge as fallback pricing: $classCharge');
  }

  // Since backend now provides correct pricing (excluding SEN), we no longer force FREE for SEN-tagged centers.
  // The SEN tag will still be shown. If a center ONLY offers SEN, priceFrom will be null, and it will show FREE.
  bool showZCoin = false;
  if (priceFrom != null && priceFrom > 0) {
    // Conservative logic: require significant balance buffer for ZCoin display
    showZCoin = userBalance >= (priceFrom * 1.5); // Require 50% buffer
  }

  // Format price text like in centre_view.dart
  String priceText;
  String currencyText;

  if (priceFrom != null && priceFrom > 0) {
    if (showZCoin) {
      currencyText = ""; // Z icon is shown separately
      if (priceFrom == priceTo) {
        priceText = priceFrom.toStringAsFixed(0);
      } else {
        priceText =
            '${priceFrom.toStringAsFixed(0)} - ${priceTo?.toStringAsFixed(0)}';
      }
    } else {
      currencyText = "HKD";
      if (priceFrom == priceTo) {
        priceText = (priceFrom * 25).toStringAsFixed(0); // ZCoin to HKD
      } else {
        priceText =
            '${(priceFrom * 25).toStringAsFixed(0)} - ${(priceTo! * 25).toStringAsFixed(0)}';
      }
    }
  } else {
    // If no price, show "Contact"
    priceText = "Contact";
    currencyText = "";
  }

  String displayText =
      currencyText.isNotEmpty ? '$currencyText $priceText' : priceText;

  // Use the default implementation with parsed data
  return buildRowGallery(
    context: context,
    startAge: displayText,
    center: centerName,
    location: location,
    imageHeight: imageHeight ?? 163.h,
    imageWidth: imageWidth ?? double.infinity,
    category: category,
    imagepath: imagePath,
    rating: rating,
    isSaved: isSaved,
    centerId: centerId,
    isSenService: isSenService,
    showZCoinIcon: showZCoin, // Only show Z icon when using ZCoin currency
    onFavoriteToggle: onFavoriteToggle,
    onTap: onTap ??
        () {
          NavigatorService.pushNamed(AppRoutes.centreView, arguments: {
            'center': center,
            'isSaved': isSaved,
            'bottomView': true
          });
        },
  );
}
