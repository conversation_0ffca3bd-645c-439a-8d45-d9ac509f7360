import 'package:class_z/core/imports.dart';

/// A simple map view that doesn't rely on Google Maps API
/// This is a fallback for when the Google Maps integration fails
class SimpleMapView extends StatelessWidget {
  final Address? address;
  final double height;
  final double width;
  final double borderRadius;
  final VoidCallback? onTap;

  const SimpleMapView({
    Key? key,
    required this.address,
    required this.height,
    required this.width,
    this.borderRadius = 20,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Container(
          height: height,
          width: width,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Background image from assets with gradient overlay
              Container(
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(ImagePath.locationInfo),
                    fit: BoxFit.cover,
                    colorFilter: ColorFilter.mode(
                      Colors.white.withOpacity(0.15),
                      BlendMode.lighten,
                    ),
                  ),
                ),
              ),

              // Semi-transparent overlay with gradient
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.white.withOpacity(0.0),
                      Colors.white.withOpacity(0.2),
                      Colors.white.withOpacity(0.4),
                    ],
                  ),
                ),
              ),

              // Google logo attribution at the bottom
              Positioned(
                right: 5,
                bottom: 5,
                child: Row(
                  children: [
                    Text(
                      "Google",
                      style: TextStyle(
                        fontSize: 7,
                        color: Colors.black54,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

              // Open in Maps indicator
              Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 2,
                        offset: Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.open_in_new,
                        size: 10,
                        color: Colors.black54,
                      ),
                      SizedBox(width: 2),
                      Text(
                        "Map",
                        style: TextStyle(
                          fontSize: 8,
                          fontWeight: FontWeight.w500,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Location pin and address
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated location pin
                    TweenAnimationBuilder(
                      tween: Tween<double>(begin: 0, end: 1),
                      duration: Duration(milliseconds: 800),
                      builder: (context, double value, child) {
                        return Transform.translate(
                          offset: Offset(0, -3 * (1 - value)),
                          child: Opacity(
                            opacity: value,
                            child: Icon(
                              Icons.location_on,
                              color: AppPallete.secondaryColor,
                              size: 36,
                            ),
                          ),
                        );
                      },
                    ),
                    SizedBox(height: 6),

                    // Address container
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(6),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.15),
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        _getShortAddress(),
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ),

                    // Show full address if available
                    if (_getFullAddress().isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4.0),
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                          margin: EdgeInsets.symmetric(horizontal: 8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(4),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 3,
                                offset: Offset(0, 1),
                              ),
                            ],
                          ),
                          child: Text(
                            _getFullAddress(),
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            style: TextStyle(
                              fontSize: 9,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getShortAddress() {
    if (address == null) return "Location";

    final parts = [
      if (address?.city != null && address!.city!.isNotEmpty) address!.city,
      if (address?.region != null && address!.region!.isNotEmpty)
        address!.region,
    ];

    return parts.isNotEmpty ? parts.join(", ") : "Location";
  }

  String _getFullAddress() {
    if (address == null) return "";

    final parts = [
      if (address?.address1 != null && address!.address1!.isNotEmpty)
        address!.address1,
      if (address?.address2 != null && address!.address2!.isNotEmpty)
        address!.address2,
      if (address?.city != null && address!.city!.isNotEmpty) address!.city,
      if (address?.region != null && address!.region!.isNotEmpty)
        address!.region,
    ];

    return parts.join(", ");
  }
}
