import 'package:class_z/core/imports.dart';

class PricingPlanCard extends StatelessWidget {
  final Color color;
  final bool isSelected;
  final VoidCallback onTap;
  final bool currentPlan;
  final String name;
  final int amount;
  final int zCoin;
  final String interval;

  const PricingPlanCard(
      {super.key,
      required this.name,
      required this.amount,
      required this.zCoin,
      required this.interval,
      required this.currentPlan,
      required this.color,
      required this.isSelected,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SizedBox(
          height: 106.h,
          width: constraints.maxWidth,
          child: Stack(
            children: [
              Positioned(
                top: 12.h,
                child: GestureDetector(
                  onTap: onTap,
                  child: Container(
                    height: 94.h,
                    width: constraints.maxWidth,
                    decoration: BoxDecoration(
                      color: color,
                      border: Border.all(
                          color: isSelected
                              ? AppPallete.secondaryColor
                              : Colors.transparent),
                      borderRadius: BorderRadius.circular(20.w),
                      boxShadow: [
                        BoxShadow(
                            blurRadius: 15,
                            color: Colors.black.withOpacity(0.1))
                      ],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(left: 23.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              currentPlan
                                  ? Text(
                                      "Current Plan",
                                      style: TextStyle(
                                          fontSize: 13.sp,
                                          color: AppPallete.secondaryColor,
                                          fontWeight: FontWeight.w500),
                                    )
                                  : SizedBox(height: 11.h),
                              Text(
                                name,
                                style: TextStyle(
                                    fontSize: 17.sp,
                                    color: AppPallete.darkGrey,
                                    fontWeight: FontWeight.w700),
                              ),
                              Text(
                                "$amount/ $interval",
                                style: TextStyle(
                                    fontSize: 15.sp,
                                    color: AppPallete.darkGrey,
                                    fontWeight: FontWeight.w300),
                              ),
                              Text(
                                "~17-24 classes",
                                style: TextStyle(
                                    fontSize: 15.sp,
                                    color: AppPallete.darkGrey,
                                    fontWeight: FontWeight.w300),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(right: 25.w),
                          child: Row(
                            children: [
                              Text(
                                "$zCoin",
                                style: TextStyle(
                                    fontSize: 35.sp,
                                    color: AppPallete.darkGrey,
                                    fontWeight: FontWeight.w500),
                              ),
                              SizedBox(width: 7.w),
                              customSvgPicture(
                                  imagePath: ImagePath.zSvg,
                                  height: 45.h,
                                  width: 45.w,
                                  color: AppPallete.darkGrey)
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 0,
                right: 25.w,
                child: Container(
                  height: 23.h,
                  width: 73.w,
                  decoration: BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: Center(
                    child: Text(
                      "10% OFF",
                      style: TextStyle(
                          fontSize: 13.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
