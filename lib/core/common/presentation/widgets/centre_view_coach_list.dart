import 'package:class_z/core/imports.dart';

Widget centreViewCoachList(
    {required BuildContext context,
    required String ageGroup,
    required String name,
    required String imageUrl,
    required double rating,
    required String skills,
    required bool sen,
    required VoidCallback onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: AspectRatio(
      aspectRatio: 0.934,
      child: Container(
          // height: 197.h,
          // width: 184.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.13),
                blurRadius: 18,
                spreadRadius: 2,
                offset: Offset(0, 8),
              ),
            ],
          ),
          child: Stack(
            children: [
              CustomImageBuilder(
                imagePath: imageUrl,
                height: 197.h,
                width: 184.w,
                borderRadius: 20.r,
              ),
              Positioned(
                top: 15.h,
                left: 10.h,
                child: Container(
                  decoration: BoxDecoration(
                      boxShadow: [shadow(blurRadius: 4, opacity: 0.25)]),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SingleStarRating(initialRating: rating),
                      SizedBox(width: 4.w),
                      Text(
                        rating.toString(),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 15.sp,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                  left: 5.w,
                  bottom: 32.h,
                  child: customtext(
                      context: context,
                      newYear: name,
                      font: 18.sp,
                      weight: FontWeight.bold,
                      color: Colors.white,
                      shadows: [shadow(blurRadius: 4, opacity: 0.25)])),
              Positioned(
                  left: 5.w,
                  bottom: 15.h,
                  child: customtext(
                      context: context,
                      newYear: skills,
                      font: 13.sp,
                      weight: FontWeight.w500,
                      color: Colors.white,
                      shadows: [shadow(blurRadius: 4, opacity: 0.25)])),
            ],
          )),
    ),
  );
}
