import 'package:class_z/core/imports.dart';

Widget centreListCourse(
    {required BuildContext context,
    required String title,
    required String imagePath,
    required String category,
    required String name,
    required String location,
    required String language,
    required String ageGroup,
    required int currentStudent,
    required int totalStudent,
    required bool sen,
    required VoidCallback onTap,
    bool isJoinable = true}) {
  Color color = Colors.white;
  location == 'online' ? color = AppPallete.color255 : color;
  return Opacity(
    opacity: isJoinable ? 1.0 : 0.5,
    child: Container(
      width: 345.w,
      color: color,
      child: GestureDetector(
        onTap: isJoinable ? onTap : null,
        child: Stack(
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        customtext(
                            context: context,
                            newYear: title,
                            font: 15.sp,
                            weight: FontWeight.w700),
                        SizedBox(
                          height: 5.h,
                        ),
                        customtext(
                            context: context,
                            newYear: "Intermediate",
                            font: 13.sp,
                            weight: FontWeight.w400),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        location == 'online'
                            ? _online(context: context)
                            : buildLocation(
                                context: context,
                                text: location,
                                color: AppPallete.scheduleColor2,
                                font: 13.sp),
                        SizedBox(
                          height: 5.h,
                        ),
                      ],
                    )
                  ],
                ),
                SizedBox(
                    height: 91.h,
                    width: 352.w,
                    child: Stack(children: [
                      Positioned(
                          top: 0.h,
                          left: 3.w,
                          right: 4.w,
                          child: CustomImageBuilder(
                              imagePath: imagePath,
                              height: 91.h,
                              width: 345.w,
                              borderRadius: 10)),
                      Positioned(
                          top: 52.h,
                          left: 11.w,
                          child: Row(children: [
                            customtext(
                                context: context,
                                newYear: name,
                                font: 15.sp,
                                color: Colors.white,
                                shadows: [_shadow()],
                                weight: FontWeight.w700),
                          ])),
                      Positioned(
                          top: 68.h,
                          left: 12.w,
                          child: Row(
                            children: [
                              customSvgPicture(
                                  imagePath: ImagePath.groupSvg,
                                  height: 12.h,
                                  width: 13.h,
                                  color: Colors.white),
                              SizedBox(
                                width: 2.w,
                              ),
                              customtext(
                                  context: context,
                                  newYear: "$currentStudent/$totalStudent",
                                  font: 15.sp,
                                  shadows: [_shadow()],
                                  weight: FontWeight.w700,
                                  color: Colors.white),
                              SizedBox(
                                width: 8.w,
                              ),
                              customtext(
                                  context: context,
                                  newYear: "Age $ageGroup",
                                  font: 15.sp,
                                  shadows: [_shadow()],
                                  weight: FontWeight.w700,
                                  color: Colors.white),
                            ],
                          )),
                      Positioned(
                          top: 68.h,
                          right: 17.w,
                          child: customtext(
                              context: context,
                              newYear: language,
                              font: 15.sp,
                              shadows: [_shadow()],
                              weight: FontWeight.w700,
                              color: Colors.white)),
                      // SEN badge positioned at top-right corner inside the image
                      if (sen)
                        Positioned(
                          top: 8.h,
                          right: 12.w,
                          child: customSen(),
                        ),
                    ])),
              ],
            ),
            if (!isJoinable)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Center(
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.7),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        "Registration Closed",
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14.sp,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    ),
  );
}

BoxShadow _shadow() {
  return BoxShadow(
    color: Colors.black.withOpacity(0.8), // rgba(0, 0, 0, 0.25)
    blurRadius: 15.0,
    offset: const Offset(0, 0),
  );
}

Widget _online({required BuildContext context}) {
  return Row(
    children: [
      customSvgPicture(
          imagePath: ImagePath.locationSvg, height: 16.67.h, width: 11.67.w),
      SizedBox(
        width: 6.33.w,
      ),
      customtext(
          context: context,
          newYear: "online",
          font: 15.sp,
          weight: FontWeight.w400)
    ],
  );
}
