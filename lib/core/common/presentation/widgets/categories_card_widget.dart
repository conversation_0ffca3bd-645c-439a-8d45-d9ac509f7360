import 'package:class_z/core/imports.dart';

Widget categoriesCard(
    {required BuildContext context,
    required String text,
    required VoidCallback onPressed}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      borderRadius: BorderRadius.circular(20.r),
      onTap: onPressed,
      child: Container(
        height: 117.h,
        width: double.infinity,
        decoration: BoxDecoration(
          gradient: GradientProvider.getSearchGradient(),
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Stack(
          children: [
            Positioned(
              top: 88.h,
              left: 14.w,
              child: customtext(
                  context: context,
                  newYear: text,
                  font: 15.sp,
                  weight: FontWeight.w500,
                  color: Colors.white,
                  shadows: [shadow(blurRadius: 15, yoffset: 0.1)]),
            )
          ],
        ),
      ),
    ),
  );
}
