import 'package:flutter/material.dart';

Widget customtext({
  required BuildContext context,
  required String newYear,
  required double font,
  FontWeight? weight,
  Color? color,
  Color? underlineColor,
  FontWeight? fontWeight,
  FontStyle? fontStyle,
  double? letterSpacing,
  double? wordSpacing,
  TextDecoration? textDecoration,
  double? height,
  TextAlign? textAlign,
  TextOverflow? overflow,
  int? maxLines,
  double? padding,
  List<Shadow>? shadows,
  String? family,
  bool underline = false, // Default to false for clarity
}) {
  return Padding(
    padding: EdgeInsets.only(left: padding ?? 0),
    child: Stack(
      children: [
        if (underline && underlineColor != null)
          Positioned(
            bottom: 0, // Position the underline at the bottom of the text
            left: 0,
            right: 0,
            child: Container(
              height: 1.5, // Customize underline thickness
              color: underlineColor,
            ),
          ),
        Text(
          newYear,
          style: TextStyle(
            fontSize: font,
            color: color ?? Colors.black,
            fontFamily: family ?? 'SF',
            fontWeight: weight ?? fontWeight ?? FontWeight.w400,
            fontStyle: fontStyle,
            letterSpacing: letterSpacing,
            wordSpacing: wordSpacing,
            decoration:
                underline ? TextDecoration.none : (textDecoration ?? TextDecoration.none),
            height: height,
            shadows: shadows ?? [],
          ),
          textAlign: textAlign,
          overflow: overflow,
          maxLines: maxLines,
        ),
      ],
    ),
  );
}
