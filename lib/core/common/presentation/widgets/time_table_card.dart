import 'package:class_z/core/imports.dart';

Widget timeTableCard(
    {required BuildContext context,
    required String user,
    required bool confirmed,
    required String date,
    required String location,
    required String course,
    required String time,
    required String classTime,
    required String special,
    required String coach,
    GetOrderByUserModel? order,
    EventElement? event}) {
  Color color = AppPallete.secondaryColor;
  String onTap = AppRoutes.ordersDetailsConTimetable;
  Color textColor = AppPallete.white;
  if (confirmed == false) {
    color = AppPallete.paleGrey;
    textColor = Colors.black;
    onTap = AppRoutes.ordersDetailsPending;
  }
  return Material(
    color: Colors.transparent,
    child: InkWell(
      borderRadius: BorderRadius.circular(20.r),
      onTap: () {
        //  showReviewBottomSheet(context);
        if (order != null)
          NavigatorService.pushNamed(onTap, arguments: order);
        else {
          NavigatorService.pushNamed(AppRoutes.eventDetailsConTimetable,
              arguments: event);
        }
      },
      child: Container(
        decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
            borderRadius: BorderRadius.circular(20.r)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ///name and blue
            Container(
              height: 41.h,
              decoration: BoxDecoration(
                  color: color,
                  boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20.r),
                      topRight: Radius.circular(20.r))),
              child: Padding(
                padding: EdgeInsets.only(left: 19.w, right: 19.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    customtext(
                        context: context,
                        newYear: user,
                        font: 12.sp,
                        weight: FontWeight.w600,
                        color: textColor),
                    customtext(
                        context: context,
                        newYear: confirmed ? "Confirmed" : "Pending",
                        font: 12.sp,
                        weight: FontWeight.w600,
                        color: textColor)
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 19.w, right: 19.w, top: 16.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  customtext(
                    context: context,
                    newYear: date,
                    font: 12.sp,
                    weight: FontWeight.w500,
                  ),
                  customtext(
                    context: context,
                    newYear: location,
                    font: 12.sp,
                    weight: FontWeight.w500,
                  )
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 19.w, right: 19.w, top: 15.h),
              child: customtext(
                context: context,
                newYear: course,
                font: 20.sp,
                weight: FontWeight.w600,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 19.w, right: 19.w, top: 11.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  customtext(
                    context: context,
                    newYear: time,
                    font: 20.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(
                    width: 12.w,
                  ),
                  customtext(
                    context: context,
                    newYear: classTime,
                    font: 12.sp,
                    weight: FontWeight.w500,
                  )
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 19.w, right: 19.w, top: 11.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  customtext(
                    context: context,
                    newYear: special,
                    font: 12.sp,
                    weight: FontWeight.w500,
                  ),
                  customtext(
                    context: context,
                    newYear: coach,
                    font: 12.sp,
                    weight: FontWeight.w500,
                  )
                ],
              ),
            ),
            SizedBox(
              height: 16.h,
            )
          ],
        ),
      ),
    ),
  );
}
