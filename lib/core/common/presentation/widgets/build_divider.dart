import 'package:class_z/config/themes/app_pallate.dart';
import 'package:flutter/material.dart';

Widget customDivider({double? width, double? padding, double? right}) {
  return Padding(
    padding: EdgeInsets.only(left: padding ?? 0, right: right ?? 0),
    child: Container(
      width: width, // The width will be null here if not provided
      height: 1,
      color: AppPallete.dividerTime,
    ),
  );
}
