import 'package:class_z/core/imports.dart';
import 'package:flutter/foundation.dart';

String imageStringGenerator({String? imagePath}) {
  if (imagePath == null || imagePath.isEmpty) {
    if (kDebugMode) {
      print('[ImageGenerator] Input is null or empty, returning ""');
    }
    return ""; // Return empty string for null or empty paths
  }

  if (kDebugMode) {
    print('[ImageGenerator] Input: "$imagePath"');
  }

  try {
    // First sanitize the URL to fix common typos
    String sanitizedPath = AppText.sanitizeUrl(imagePath);
    if (kDebugMode && sanitizedPath != imagePath) {
      print('[ImageGenerator] Sanitized: "$sanitizedPath"');
    }

    String finalUrl;

    // If the image path already contains the base URL, validate and fix it if needed
    if (sanitizedPath.startsWith('http')) {
      // Check for cache paths that incorrectly start with the API URL
      if (sanitizedPath.contains('/data/user/0/com.example.class_z/cache/')) {
        // For cache paths, use file:// protocol instead
        final cachePath = sanitizedPath.replaceFirst(AppText.device, 'file://');
        if (kDebugMode) {
          print(
              '[ImageGenerator] Detected cache path, converting to: "$cachePath"');
        }
        return cachePath;
      }

      // Check for malformed URLs (like D:\ paths appended to base URL)
      if (sanitizedPath.contains(RegExp(r'[A-Z]:'))) {
        // Extract the actual file path after the drive letter
        final match = RegExp(r'[A-Z]:\\(.+)').firstMatch(sanitizedPath);
        if (match != null) {
          final cleanPath = match.group(1)!.replaceAll('\\', '/');
          finalUrl = '${AppText.device}/$cleanPath';
          if (kDebugMode) {
            print(
                '[ImageGenerator] Detected Windows path, converting to: "$finalUrl"');
          }
          return finalUrl;
        }
      }
      if (kDebugMode) {
        print(
            '[ImageGenerator] Path is already a full URL, returning: "$sanitizedPath"');
      }
      return sanitizedPath; // Return as-is if it's already a valid URL
    }

    // Handle relative server paths
    if (sanitizedPath.startsWith('/')) {
      finalUrl = AppText.device + sanitizedPath;
      if (kDebugMode) {
        print(
            '[ImageGenerator] Detected relative server path, converting to: "$finalUrl"');
      }
      return finalUrl;
    }

    // For paths that don't start with / or http, assume they're relative to uploads
    finalUrl = '${AppText.device}/uploads/$sanitizedPath';
    if (kDebugMode) {
      print(
          '[ImageGenerator] Detected relative upload path, converting to: "$finalUrl"');
    }
    return finalUrl;
  } catch (e, stackTrace) {
    if (kDebugMode) {
      print('[ImageGenerator] ERROR: $e');
      print(stackTrace);
    }
    // Return original path or empty string if any error occurs
    return imagePath;
  }
}

String addressGenerator({Address? address, String? condition = null}) {
  if (address == null) {
    return condition == 'city'
        ? "City not available"
        : condition == 'region'
            ? "Region not available"
            : condition == 'both'
                ? "Location not available"
                : "Address not available";
  }

  if (condition == 'city') {
    return address.city != null && address.city!.isNotEmpty
        ? address.city!
        : "City not available";
  } else if (condition == 'region') {
    return address.region != null && address.region!.isNotEmpty
        ? address.region!
        : "Region not available";
  } else if (condition == 'both') {
    final city =
        address.city != null && address.city!.isNotEmpty ? address.city! : "";
    final region = address.region != null && address.region!.isNotEmpty
        ? address.region!
        : "";

    if (city.isNotEmpty && region.isNotEmpty) {
      return "$city, $region";
    } else if (city.isNotEmpty) {
      return city;
    } else if (region.isNotEmpty) {
      return region;
    } else {
      return "Location not available";
    }
  }

  // For full address
  final parts = [
    if (address.address1 != null && address.address1!.isNotEmpty)
      address.address1,
    if (address.address2 != null && address.address2!.isNotEmpty)
      address.address2,
    if (address.city != null && address.city!.isNotEmpty) address.city,
    if (address.region != null && address.region!.isNotEmpty) address.region,
  ];

  return parts.isNotEmpty ? parts.join(", ") : "Address not available";
}

String dateGenerator({DateTime? date, bool monthName = false, String? format}) {
  if (date == null) {
    return 'Unknown date'; // Handle the case where date is null
  }
  // Remove unnecessary debug prints
  if (monthName == true) {
    List<String> monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];

    // Get the month index (0 for Jan, 1 for Feb, etc.)
    String month = monthNames[
        date.month - 1]; // date.month starts from 1 (Jan), so subtract 1

    // Return formatted date with day, month name, and year
    return '${date.day.toString().padLeft(2, '0')} $month ${date.year}';
  }
  if (kDebugMode && format != null) {
    // Logging removed as requested
  }
  if (format == 'yyyy-MM-dd') {
    // Remove unnecessary debug print
    return DateFormat('yyyy-MM-dd').format(date);
  }
  return DateFormat('dd/MM/yyyy').format(date);
}

String timeGenerator(DateTime time) {
  return DateFormat('HH:mm').format(time);
}

String titleGenerateWithAddress() {
  final centerData = locator<SharedRepository>().getCenterData();
  return '${centerData?.displayName}-${addressGenerator(address: centerData?.address, condition: 'city')}';
}
