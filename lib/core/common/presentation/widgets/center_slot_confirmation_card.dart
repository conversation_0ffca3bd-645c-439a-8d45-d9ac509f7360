import 'package:class_z/core/imports.dart';

Widget centerSlotConfirmationCard(
    {required BuildContext context,
    required Color firstColor,
    Color? firstTextColor,
    required String firstText,
    required String address,
    required String date,
    required String center,
    required String course,
    required String time,
    required String classTime,
    required String numberOfStudent,
    required String ageStart,
    required String agefinish,
    required String coach,
    required String charges,
    required bool senFriendly,
    required String currentStudent}) {
  return Padding(
    padding: const EdgeInsets.symmetric(horizontal: 24),
    child: Container(
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 45,
              width: double.infinity,
              decoration: BoxDecoration(
                  color: firstColor,
                  borderRadius: BorderRadius.only(
                      topLeft: const Radius.circular(20),
                      topRight: const Radius.circular(20))),
              child: Padding(
                padding: const EdgeInsets.only(top: 15, left: 15),
                child: customtext(
                  context: context,
                  newYear: firstText,
                  font: 15.sp,
                  weight: FontWeight.w500,
                  color: firstTextColor ?? Colors.white,
                ),
              ),
            ),
            const SizedBox(
              height: 12,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      customtext(
                        context: context,
                        newYear: date,
                        font: 15.sp,
                        weight: FontWeight.w500,
                      ),
                      Padding(
                        padding: EdgeInsets.only(right: 31.w),
                        child: Row(
                          children: [
                            customSvgPicture(
                                imagePath: ImagePath.locationSvg,
                                height: 16.67.h,
                                width: 11.67.w),
                            SizedBox(
                              width: 5.33.w,
                            ),
                            customtext(
                              context: context,
                              newYear:
                                  address == 'center' ? 'center' : 'OffSite',
                              font: 15.sp,
                              weight: FontWeight.w500,
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 11,
                  ),
                  customtext(
                    context: context,
                    newYear: course,
                    font: 20.sp,
                    weight: FontWeight.w600,
                  ),
                  SizedBox(
                    height: 11.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      customtext(
                        context: context,
                        newYear: time,
                        font: 20.sp,
                        weight: FontWeight.w500,
                      ),
                      SizedBox(
                        width: 12.w,
                      ),
                      customtext(
                        context: context,
                        newYear: classTime,
                        font: 12.sp,
                        weight: FontWeight.w500,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 13.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          customSvgPicture(
                              imagePath: ImagePath.groupSvg,
                              height: 11.h,
                              width: 11.w),
                          SizedBox(
                            width: 5.w,
                          ),
                          customtext(
                            context: context,
                            newYear: () {
                              if (currentStudent == '#') {
                                return "$numberOfStudent";
                              }
                              final current = int.tryParse(currentStudent) ?? 0;
                              final max = int.tryParse(numberOfStudent) ?? 0;
                              if (current >= max && max > 0) {
                                return "$max (Full)";
                              }
                              return "$current/$max";
                            }(),
                            font: 15.sp,
                            weight: FontWeight.w500,
                          ),
                        ],
                      ),
                      SizedBox(
                        width: 18.w,
                      ),
                      customtext(
                        context: context,
                        newYear: "Age $ageStart- $agefinish",
                        font: 15.sp,
                        weight: FontWeight.w500,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 9.h,
                  ),
                  customtext(
                    context: context,
                    newYear: " by $coach",
                    font: 15.sp,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(
                    height: 17.h,
                  ),
                  customDivider(width: 343.w, padding: 0),
                  SizedBox(
                    height: 20.h,
                  ),
                  customtext(
                      context: context,
                      newYear: "Coaching address:",
                      font: 15.sp,
                      weight: FontWeight.w500),
                  SizedBox(
                    height: 20.h,
                  ),
                  address == 'center'
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            customtext(
                                context: context,
                                newYear: "On-site coaching: ",
                                font: 15.sp,
                                weight: FontWeight.w500,
                                color: AppPallete.secondaryColor),
                            SizedBox(
                              width: 15.w,
                            ),
                            customtext(
                                context: context,
                                newYear: center,
                                font: 15.sp,
                                weight: FontWeight.w500),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            customtext(
                                context: context,
                                newYear: "Off-site coaching: ",
                                font: 15.sp,
                                weight: FontWeight.w500,
                                color: AppPallete.secondaryColor),
                            SizedBox(
                              width: 15.w,
                            ),
                            Flexible(
                              child: customtext(
                                  context: context,
                                  newYear: address,
                                  font: 15.sp,
                                  weight: FontWeight.w500),
                            ),
                          ],
                        ),
                  SizedBox(
                    height: 20.h,
                  ),
                  customDivider(width: 343.w, padding: 0),
                  SizedBox(
                    height: 20.h,
                  ),
                  customtext(
                      context: context,
                      newYear: "Charges:",
                      font: 15.sp,
                      weight: FontWeight.w500),
                  SizedBox(
                    height: 15.h,
                  ),
                  Row(
                    children: [
                      customtext(
                        context: context,
                        newYear: "Student per class: ",
                        font: 15.sp,
                        weight: FontWeight.w500,
                      ),
                      customtext(
                        context: context,
                        newYear: charges,
                        font: 15.sp,
                        weight: FontWeight.w500,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 15.h,
                  ),
                  senFriendly == true
                      ? customtext(
                          context: context,
                          newYear: "Special note: SEN-friendly",
                          font: 15.sp,
                          weight: FontWeight.w500,
                        )
                      : SizedBox(),
                  SizedBox(
                    height: 20.h,
                  ),
                  customDivider(width: 343.w, padding: 0),
                  SizedBox(
                    height: 20.h,
                  ),
                  customtext(
                    context: context,
                    newYear: "By proceeding, you acknowledged that",
                    font: 13.sp,
                    weight: FontWeight.w400,
                  ),
                  SizedBox(
                    height: 15.h,
                  ),
                  customtext(
                    context: context,
                    newYear:
                        "1. You have responsibility to coach the class on time with quality service",
                    font: 13.sp,
                    weight: FontWeight.w400,
                  ),
                  SizedBox(
                    height: 15.h,
                  ),
                  Row(
                    children: [
                      customtext(
                        context: context,
                        newYear: "2. Missed class may be subject to a",
                        font: 13.sp,
                        weight: FontWeight.w400,
                      ),
                      customtext(
                        context: context,
                        newYear: " service charge",
                        color: AppPallete.secondaryColor,
                        font: 13.sp,
                        weight: FontWeight.w400,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 54.h,
                  )
                ],
              ),
            ),
          ],
        )),
  );
}
