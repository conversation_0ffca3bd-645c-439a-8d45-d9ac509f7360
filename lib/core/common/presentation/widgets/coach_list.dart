import 'package:class_z/core/imports.dart';

Widget coachList(
    {required BuildContext context,
    required String imagepath,
    required String course,
    required String level,
    required String name,
    required String duration,
    required String classTime,
    required String location,
    required String language,
    required String ageGroup,
    required String student,
    required String fee}) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Padding(
        padding: EdgeInsets.only(left: 8.w),
        child: customtext(context: context, newYear: "7.00", font: 15.sp),
      ),
      Padding(
        padding: EdgeInsets.only(left: 15.w, right: 15.w),
        child: GestureDetector(
          onTap: () {
            NavigatorService.pushNamed(AppRoutes.freeslot);
          },
          child: SizedBox(
              height: 147.h,
              width: 352.w,
              child: Stack(children: [
                Positioned(
                    top: 38.h,
                    left: 3.w,
                    right: 4.w,
                    child: CustomImageBuilder(
                        imagePath: imagepath,
                        height: 91.h,
                        width: 345.w,
                        borderRadius: 10)),
                Positioned(
                    left: 3.w,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        customtext(
                            context: context,
                            newYear: course,
                            font: 15.sp,
                            weight: FontWeight.w700),
                        SizedBox(
                          height: 5.h,
                        ),
                        customtext(
                            context: context,
                            newYear: level,
                            font: 13.sp,
                            weight: FontWeight.w400),
                      ],
                    )),
                Positioned(
                  top: 3.h,
                  right: 4.w,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      customtext(
                          context: context,
                          newYear: location,
                          font: 15.sp,
                          weight: FontWeight.w400),
                      SizedBox(
                        height: 5.h,
                      ),
                      customSen()
                    ],
                  ),
                ),
                Positioned(
                    top: 44.h,
                    left: 11.w,
                    child: Row(children: [
                      customtext(
                          context: context,
                          newYear: name,
                          font: 15.sp,
                          color: Colors.white,
                          shadows: [_shadow()],
                          weight: FontWeight.w700),
                    ])),
                Positioned(
                  top: 44.h,
                  right: 17.w,
                  child: customtext(
                      context: context,
                      newYear: "45mins",
                      font: 15.sp,
                      shadows: [_shadow()],
                      color: Colors.white,
                      weight: FontWeight.w700),
                ),
                Positioned(
                    top: 106.h,
                    left: 12.w,
                    child: Row(
                      children: [
                        customSvgPicture(
                            imagePath: ImagePath.peopleSvg,
                            height: 12.h,
                            width: 13.h,
                            color: Colors.white),
                        SizedBox(
                          width: 2.w,
                        ),
                        customtext(
                            context: context,
                            newYear: "3/5",
                            font: 15.sp,
                            shadows: [_shadow()],
                            weight: FontWeight.w700,
                            color: Colors.white),
                        SizedBox(
                          width: 8.w,
                        ),
                        customtext(
                            context: context,
                            newYear: "Age 3-6",
                            font: 15.sp,
                            shadows: [_shadow()],
                            weight: FontWeight.w700,
                            color: Colors.white),
                      ],
                    )),
                Positioned(
                    top: 91.h,
                    right: 17.w,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Row(
                          children: [
                            customSvgPicture(
                                imagePath: ImagePath.zSvg,
                                height: 15.h,
                                width: 15.h,
                                color: Colors.white),
                            SizedBox(
                              width: 2.w,
                            ),
                            customtext(
                                context: context,
                                newYear: "12",
                                font: 15.sp,
                                shadows: [_shadow()],
                                weight: FontWeight.w700,
                                color: Colors.white),
                          ],
                        ),
                        customtext(
                            context: context,
                            newYear: "Cantonese",
                            font: 15.sp,
                            shadows: [_shadow()],
                            weight: FontWeight.w700,
                            color: Colors.white),
                      ],
                    ))
              ])),
        ),
      ),
    ],
  );
}

BoxShadow _shadow() {
  return BoxShadow(
    color: Colors.black.withOpacity(0.8), // rgba(0, 0, 0, 0.25)
    blurRadius: 15.0,
    offset: const Offset(0, 0),
  );
}
