// ignore_for_file: file_names

import 'package:class_z/core/imports.dart';
import 'package:flutter/foundation.dart';

Widget centreViewImageWidget(
    {required BuildContext context,
    required List<CenterImage> images,
    required bool sen,
    required Function(bool)? onFavoriteToggle,
    required String centerId,
    final bool isSaved = false}) {
  // Use imageStringGenerator for each image URL
  List<String> imagePaths = images
      .map((image) => imageStringGenerator(imagePath: image.url))
      .toList();

  return Stack(
    children: [
      ImageSlider(
        imagePaths: imagePaths,
        height: 143.h,
        width: 404.w,
      ),
      if (sen == true)
        Positioned(
          top: 8.h,
          left: 8.w,
          child: Container(
            height: 15.h,
            width: 32.w,
            decoration: BoxDecoration(
              color: AppPallete.secondaryColor,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Center(
              child: Text(
                'SEN',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w400),
              ),
            ),
          ),
        ),
      Positioned(
          right: 18.w,
          bottom: 10.h,
          child: InkWell(
            onTap: () {
              // Only log in debug mode to reduce console spam
              if (kDebugMode) {
                print(
                    'Heart tapped for center $centerId, current saved: $isSaved, toggling to: ${!isSaved}');
              }
              if (onFavoriteToggle != null) {
                onFavoriteToggle(!isSaved);
              }
            },
            child: customSvgPicture(
                imagePath: ImagePath.heartSvg,
                height: 25.h,
                color: isSaved == true ? AppPallete.red : AppPallete.white,
                width: 25.w),
          ))
    ],
  );
}
