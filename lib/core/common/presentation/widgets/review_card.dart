import 'package:class_z/core/imports.dart';

void showReviewBottomSheet(BuildContext context) {
  double centerRating = 0.0;
  double coachRating = 0.0;
  TextEditingController centerController = TextEditingController();
  TextEditingController coachController = TextEditingController();

  void setRating({required double rating, required bool isCenter}) {
    if (isCenter) {
      centerRating = rating;
    } else {
      coachRating = rating;
    }
  }

  showModalBottomSheet(
    context: context,
    isScrollControlled: true, // Ensure the modal is scrollable
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(10.r),
        topRight: Radius.circular(10.r),
      ),
    ),
    builder: (BuildContext context) {
      return FractionallySizedBox(
        heightFactor:
            723.h / MediaQuery.of(context).size.height, // Set the height factor
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 40.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: customtext(
                  context: context,
                  newYear: "Rate your Experience",
                  font: 25.sp,
                  weight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 25.h),
              customtext(
                context: context,
                newYear: "How was ABC Center",
                font: 18.sp,
                weight: FontWeight.w500,
              ),
              SizedBox(height: 15.h),
              buildReviewStars(
                  rating: centerRating, isCenter: true, setRating: setRating),
              SizedBox(height: 15.h),
              AuthField(
                controller: centerController,
                height: 90.h,
                width: double.infinity, // Set width to infinity
                border: 20.sp,
              ),
              SizedBox(height: 25.h),
              customtext(
                context: context,
                newYear: "How was Dipu Islam",
                font: 18.sp,
                weight: FontWeight.w500,
              ),
              SizedBox(height: 15.h),
              buildReviewStars(
                  rating: coachRating, isCenter: false, setRating: setRating),
              SizedBox(height: 15.h),
              AuthField(
                controller: coachController,
                height: 90.h,
                width: double.infinity, // Set width to infinity
                border: 20.sp,
              ),
              SizedBox(height: 25.h),
              Center(
                child: Button(
                  buttonText: "Rate",
                  color: AppPallete.secondaryColor,
                  height: 49.h,
                  width: 249.w,
                  onPressed: () {
                    var payload = {
                      "reviewerId": "67208916c0a4306932548d17",
                      "reviewerType": "User",
                      "revieweeId": "67190235d493e133bbdab127",
                      "revieweeType": "Center",
                      "rating": 5,
                      "title": "Good",
                      "comment": "Great dassasdasdasfaewrte!"
                    };
                    context
                        .read<ReviewBloc>()
                        .add(PostReviewEvent(payload: payload));
                    NavigatorService.goBack();
                  },
                ),
              )
            ],
          ),
        ),
      );
    },
  );
}

Widget buildReviewStars(
    {required double rating,
    required bool isCenter,
    required Function setRating}) {
  return Row(
    children: List.generate(
      5,
      (index) => GestureDetector(
        onTap: () => setRating(rating: index + 1.0, isCenter: isCenter),
        child: customSvgPicture(
          imagePath: ImagePath.starSvg,
          color: index < rating ? AppPallete.rating : AppPallete.rateGray,
          height: 42.h,
          width: 42.w,
        ),
      ),
    ),
  );
}
