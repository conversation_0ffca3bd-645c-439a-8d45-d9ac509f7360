import 'package:class_z/core/imports.dart';

Widget coachCard({
  required BuildContext context,
  required String ageGroup,
  required String name,
  required String imageUrl,
  required String location,
  required String price,
  required double rating,
  required String skills,
}) {
  return GestureDetector(
    onTap: () {
      NavigatorService.pushNamed(AppRoutes.coachView);
    },
    child: SizedBox(
        height: 241.h,
        width: 184.w,
        child: Stack(
          children: [
            CustomImageBuilder(
              imagePath: ImagePath.coach,
              height: 197.h,
              width: 184.w,
              borderRadius: 20.r,
              alignment: Alignment.topCenter,
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: EdgeInsets.only(right: 8.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Align(
                      alignment: Alignment.center,
                      child: Padding(
                        padding: EdgeInsets.only(left: 10.w, right: 2.w),
                        child: Row(
                          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              decoration: BoxDecoration(boxShadow: [shadow()]),
                              width: 40.w,
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  SingleStarRating(initialRating: rating),
                                  Text(
                                    rating.toString(),
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 15.sp,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Spacer(),
                            Container(
                                margin: EdgeInsets.symmetric(vertical: 2.h),
                                decoration:
                                    BoxDecoration(boxShadow: [shadow()]),
                                child: customtext(
                                  context: context,
                                  newYear: ageGroup,
                                  font: 13.sp,
                                  weight: FontWeight.w500,
                                  color: Colors.white,
                                ))
                          ],
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: Container(
                        width: 32.w,
                        margin: EdgeInsets.only(right: 2.w),
                        padding: EdgeInsets.symmetric(horizontal: 4.w),
                        decoration: BoxDecoration(
                            color: AppPallete.secondaryColor,
                            borderRadius: BorderRadius.circular(20.w)),
                        child: Center(
                          child: Text(
                            "SEN",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.sp,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 103.h,
                    ),
                    Container(
                        margin: EdgeInsets.only(left: 5.w),
                        decoration: BoxDecoration(boxShadow: [shadow()]),
                        child: customtext(
                            context: context,
                            newYear: name,
                            font: 18.sp,
                            weight: FontWeight.w700,
                            color: Colors.white,
                            shadows: [shadow()])),
                    SizedBox(
                      height: 4.h,
                    ),
                    Container(
                        margin: EdgeInsets.only(left: 5.w),
                        child: customtext(
                            context: context,
                            newYear: skills,
                            font: 13.sp,
                            weight: FontWeight.w500,
                            color: Colors.white,
                            shadows: [shadow()])),
                    SizedBox(
                      height: 21.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(left: 1.w),
                              child: Row(
                                children: [
                                  customSvgPicture(
                                    imagePath: ImagePath.locationSvg,
                                    height: 16.h,
                                    width: 12.w,
                                    color: AppPallete.svgColorLocation,
                                  ),
                                  Padding(
                                    padding: EdgeInsets.only(left: 8.w),
                                    child: Text(
                                      location,
                                      style: TextStyle(
                                          color: AppPallete.wordsOfRequest,
                                          fontSize: 15.sp,
                                          fontWeight: FontWeight.w400),
                                    ),
                                  )
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 2.h,
                            ),
                            Row(
                              children: [
                                customSvgPicture(
                                    imagePath: ImagePath.zSvg,
                                    height: 15.h,
                                    width: 15.w,
                                    color: AppPallete.svgColorLocation),
                                Padding(
                                  padding: EdgeInsets.only(left: 8.w),
                                  child: Text(
                                    price,
                                    style: TextStyle(
                                        color: AppPallete.svgColorLocation,
                                        fontSize: 15.sp,
                                        fontWeight: FontWeight.w400),
                                  ),
                                )
                              ],
                            ),
                          ],
                        ),
                        CustomIconButton(
                            icon: Icons.favorite_border_outlined,
                            color: AppPallete.greyColor,
                            onPressed: () {})
                      ],
                    )
                  ],
                ),
              ),
            )
          ],
        )),
  );
}
