import 'package:class_z/core/imports.dart';

Widget centerTimeSlotEditCard({
  required BuildContext context,
  required String imagepath,
  required bool edit,
  required bool private,
  required String course,
  required String level,
  required String name,
  required String duration,
  required String classTime,
  required String location,
  required String language,
  required int totalStudent,
  required String ageGroup,
  required int student,
  required int fee,
  required VoidCallback onTap,
}) {
  Color backgroundColor = private ? AppPallete.color255 : Colors.white;

  return Container(
    color: backgroundColor,
    padding: EdgeInsets.symmetric(vertical: 12.h),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Time and Edit Icon Column
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.w),
          child: Column(
            children: [
              customtext(
                context: context,
                newYear: classTime,
                font: 15.sp,
              ),
              SizedBox(height: 61.h),
              if (edit)
                Container(
                  width: 17.w,
                  height: 17.h,
                  decoration: BoxDecoration(
                    color: AppPallete.secondaryColor,
                    borderRadius: BorderRadius.circular(99.r),
                  ),
                  child: Icon(
                    Icons.remove,
                    size: 9.92.w,
                    color: backgroundColor,
                  ),
                ),
            ],
          ),
        ),

        // Main Content Column
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(right: 15.w),
            child: InkWell(
              onTap: onTap,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Top Row: Course/Level and Location
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Course & Level
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          customtext(
                            context: context,
                            newYear: course,
                            font: 15.sp,
                            weight: FontWeight.w700,
                          ),
                          SizedBox(height: 4.h),
                          customtext(
                            context: context,
                            newYear: level,
                            font: 13.sp,
                            weight: FontWeight.w400,
                          ),
                        ],
                      ),

                      // Location & SEN
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              customSvgPicture(
                                imagePath: ImagePath.locationSvg,
                                height: 16.67.h,
                                width: 11.67.w,
                              ),
                              SizedBox(width: 4.w),
                              customtext(
                                context: context,
                                newYear: location,
                                font: 15.sp,
                                weight: FontWeight.w400,
                                color: AppPallete.change,
                              ),
                            ],
                          ),
                          SizedBox(height: 4.h),
                          customSen(),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 22),

                  // Image
                  Container(
                    width: double.infinity,
                    height: 110.h,
                    child: Stack(
                      children: [
                        CustomImageBuilder(
                          imagePath: imagepath,
                          borderRadius: 10,
                          height: 110.h,
                          width: double.infinity,
                        ),
                        Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 8),
// Duration
                              Padding(
                                  padding: const EdgeInsets.only(right: 13.0),
                                  child: customTimeContainer(
                                      context: context, duration: duration)),
                              const SizedBox(
                                height: 10,
                              ),
                              // Students and Age Group
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 9.0),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        customtext(
                                          context: context,
                                          newYear: name,
                                          font: 15.sp,
                                          color: Colors.white,
                                          shadows: [shadow()],
                                          weight: FontWeight.w700,
                                        ),
                                        Row(
                                          children: [
                                            customSvgPicture(
                                              imagePath: ImagePath.peopleSvg,
                                              height: 12.h,
                                              width: 13.h,
                                              color: Colors.white,
                                            ),
                                            SizedBox(width: 2.w),
                                            customtext(
                                              context: context,
                                              newYear: () {
                                                if (student >= totalStudent &&
                                                    totalStudent > 0) {
                                                  return "$totalStudent (Full)";
                                                }
                                                return "$student/$totalStudent";
                                              }(),
                                              font: 15.sp,
                                              shadows: [shadow()],
                                              weight: FontWeight.w700,
                                              color: Colors.white,
                                            ),
                                            SizedBox(width: 8.w),
                                            customtext(
                                              context: context,
                                              newYear: 'Age $ageGroup',
                                              font: 15.sp,
                                              shadows: [shadow()],
                                              weight: FontWeight.w700,
                                              color: Colors.white,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),

                                    // Fee
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        customtext(
                                          context: context,
                                          newYear: "${fee} Zcoins",
                                          font: 15.sp,
                                          shadows: [shadow()],
                                          weight: FontWeight.w700,
                                          color: Colors.white,
                                        ),

                                        // Language
                                        customtext(
                                          context: context,
                                          newYear: language,
                                          font: 15.sp,
                                          shadows: [shadow()],
                                          weight: FontWeight.w700,
                                          color: Colors.white,
                                        ),
                                      ],
                                    ),
                                    // Name
                                  ],
                                ),
                              ),
                              const SizedBox(height: 4),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 18,
                  ),
                  customDivider()
                ],
              ),
            ),
          ),
        ),
      ],
    ),
  );
}
