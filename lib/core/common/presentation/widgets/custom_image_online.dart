// import 'package:flutter/material.dart';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:class_z/core/imports.dart';
// import 'dart:io';

// class CustomImageBuilderOnline extends StatefulWidget {
//   final String imagePath;
//   final double height;
//   final double width;
//   final EdgeInsets? margin;
//   final double borderRadius;
//   final Alignment? alignment;
//   final List<BoxShadow>? boxshadow;
//   final BoxFit fit;

//   const CustomImageBuilderOnline({
//     Key? key,
//     required this.imagePath,
//     required this.height,
//     required this.width,
//     this.margin,
//     required this.borderRadius,
//     this.alignment,
//     this.boxshadow,
//     this.fit = BoxFit.cover,
//   }) : super(key: key);

//   @override
//   State<CustomImageBuilderOnline> createState() => _CustomImageBuilderOnlineState();
// }

// class _CustomImageBuilderOnlineState extends State<CustomImageBuilderOnline> with AutomaticKeepAliveClientMixin {
//   String? _processedUrl;
//   bool _isLoading = true;
//   bool _hasError = false;
//   Object? _error;
//   Image? _imageWidget;
  
//   @override
//   bool get wantKeepAlive => true; // Keep state when scrolling
  
//   @override
//   void initState() {
//     super.initState();
//     _isLoading = true;
    
//     // Set a timeout to ensure loading indicator doesn't show forever
//     Future.delayed(const Duration(seconds: 10), () {
//       if (mounted && _isLoading) {
//         setState(() {
//           _isLoading = false;
//           // Show placeholder if image still hasn't loaded after timeout
//         });
//       }
//     });
    
//     _loadImage();
//   }
  
//   @override
//   void didUpdateWidget(CustomImageBuilderOnline oldWidget) {
//     super.didUpdateWidget(oldWidget);
//     if (oldWidget.imagePath != widget.imagePath) {
//       _loadImage();
//     }
//   }
  
//   void _loadImage() {
//     if (widget.imagePath.isEmpty) {
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//       return;
//     }
    
//     // Log the image path for debugging
//     print("🖼 Image Path: ${widget.imagePath}");
    
//     // Sanitize the URL to fix common typos
//     final sanitizedUrl = AppText.sanitizeUrl(widget.imagePath);
    
//     try {
//       // Only attempt to load the image if the URL is valid
//       if (sanitizedUrl.startsWith('http') || sanitizedUrl.startsWith('file://')) {
//         _loadImageWithProvider(sanitizedUrl);
//       } else if (sanitizedUrl.startsWith('assets/') || sanitizedUrl.startsWith('lib/assets/')) {
//         _loadLocalAsset(sanitizedUrl);
//       } else {
//         // If URL doesn't match known patterns, fallback to local asset
//         if (mounted) {
//           setState(() {
//             _isLoading = false;
//           });
//         }
//       }
//     } catch (e) {
//       print("Error setting up image load: $e");
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     }
//   }
  
//   void _loadImageWithProvider(String url) {
//     try {
//       ImageProvider provider;
      
//       if (url.startsWith('file://')) {
//         provider = FileImage(File(url.replaceFirst('file://', '')));
//       } else {
//         // For network images, use CachedNetworkImageProvider for better caching
//         provider = CachedNetworkImageProvider(
//           url,
//           errorListener: (exception) {
//             print("Error loading cached image: $exception");
//             if (mounted) {
//               setState(() => _isLoading = false);
//             }
//           },
//         );
//       }
      
//       final image = Image(
//         image: provider,
//         fit: widget.fit,
//         width: widget.width?.w,
//         height: widget.height?.h,
//         errorBuilder: (context, error, stackTrace) {
//           print("Error loading image: $error");
//           return _buildErrorPlaceholder();
//         },
//       );
      
//       // Add image to cache
//       final imageStream = provider.resolve(const ImageConfiguration());
      
//       final imageStreamListener = ImageStreamListener(
//         (imageInfo, synchronousCall) {
//           if (mounted) setState(() => _isLoading = false);
//         },
//         onError: (exception, stackTrace) {
//           _isLoading = false;
//           if (mounted) setState(() {});
//           print("Error loading image: $exception");
//         },
//       );
      
//       imageStream.addListener(imageStreamListener);
      
//       // Set the image widget
//       setState(() {
//         _imageWidget = image;
//       });
//     } catch (e) {
//       print("Exception loading image: $e");
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     }
//   }
  
//   // Sanitizes the URL to fix common domain typos
//   String _sanitizeUrl(String url) {
//     return AppText.sanitizeUrl(url);
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       height: widget.height?.h,
//       width: widget.width?.w,
//       decoration: BoxDecoration(
//         color: widget.backgroundColor,
//         borderRadius: widget.borderRadius == null
//             ? null
//             : BorderRadius.circular(widget.borderRadius!.r),
//       ),
//       child: ClipRRect(
//         borderRadius: widget.borderRadius == null
//             ? BorderRadius.zero
//             : BorderRadius.circular(widget.borderRadius!.r),
//         child: Stack(
//           children: [
//             Center(
//               child: _imageWidget,
//             ),
//             if (_isLoading)
//               Center(
//                 child: SizedBox(
//                   height: 20.h,
//                   width: 20.w,
//                   child: const CircularProgressIndicator(
//                     strokeWidth: 2,
//                   ),
//                 ),
//               ),
//           ],
//         ),
//       ),
//     );
//   }
  
//   Widget _buildErrorPlaceholder() {
//     return Container(
//       height: widget.height?.h,
//       width: widget.width?.w,
//       color: Colors.grey.shade200,
//       child: Center(
//         child: Icon(
//           Icons.image_not_supported_outlined,
//           color: Colors.grey.shade500,
//           size: 24,
//         ),
//       ),
//     );
//   }
// }
