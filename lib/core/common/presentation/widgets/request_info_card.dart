import 'package:class_z/core/imports.dart';

// ignore: must_be_immutable
class RequestInfoCard extends StatefulWidget {
  DateTime date;
  String startTime;
  String endTime;
  String center;
  String classTime;
  String name;
  String student;
  String age;
  String cost;
  String? classId;

  List<DateTime>? eventDates;
  TextEditingController controller;
  TextEditingController childController;
  RequestInfoCard(
      {Key? key,
      required this.date,
      required this.startTime,
      required this.endTime,
      required this.center,
      required this.classTime,
      required this.name,
      required this.student,
      required this.age,
      required this.cost,
      required this.controller,
      required this.childController,
      this.classId,
      this.eventDates})
      : super(key: key);

  @override
  _RequestInfoCardState createState() => _RequestInfoCardState();
}

class _RequestInfoCardState extends State<RequestInfoCard> {
  bool isSelected = false;
  bool isExpanded = false;
  ChildModel? selectedData;
  @override
  Widget build(BuildContext context) {
    String date = DateFormat('dd/MM/yyyy').format(widget.date);

    int itemCount = isExpanded ? widget.eventDates!.length : 1;
    return Container(
        width: 380.w,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
            color: Colors.white,
            boxShadow: [
              shadow(blurRadius: 15, opacity: 0.1, xoffset: 0, yoffset: 0)
            ]),
        child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ///heading

              Container(
                width: 380.w,
                padding: EdgeInsets.only(left: 13.75.w, right: 23.5.w),
                decoration: BoxDecoration(
                    color: AppPallete.color255,
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20.r),
                        topRight: Radius.circular(20.r))),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 13.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        customtext(
                            context: context,
                            newYear: date,
                            font: 15.sp,
                            weight: FontWeight.w500),
                        customtext(
                            context: context,
                            newYear: widget.startTime,
                            font: 20.sp,
                            weight: FontWeight.w500),
                      ],
                    ),
                    SizedBox(
                      height: 5.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        customtext(
                            context: context,
                            newYear: widget.center,
                            font: 20.sp,
                            weight: FontWeight.w500),
                        customtext(
                            context: context,
                            newYear: widget.classTime,
                            font: 14.sp,
                            weight: FontWeight.w500),
                      ],
                    ),
                    SizedBox(
                      height: 12.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            customtext(
                                context: context,
                                newYear: "by ${widget.name}",
                                font: 15.sp,
                                weight: FontWeight.w500),
                            SizedBox(
                              width: 13.w,
                            ),
                            customSvgPicture(
                                imagePath: ImagePath.groupSvg,
                                height: 14.h,
                                width: 14.h),
                            SizedBox(
                              width: 6.w,
                            ),
                            customtext(
                                context: context,
                                newYear: widget.student,
                                font: 15.sp,
                                weight: FontWeight.w500),
                            SizedBox(
                              width: 13.w,
                            ),
                            customtext(
                                context: context,
                                newYear: widget.age,
                                font: 15.sp,
                                weight: FontWeight.w500),
                          ],
                        ),
                        Row(
                          children: [
                            customSvgPicture(
                                imagePath: ImagePath.zSvg,
                                height: 14.h,
                                width: 14.h),
                            customtext(
                                context: context,
                                newYear: widget.cost,
                                font: 15.sp,
                                weight: FontWeight.w700),
                          ],
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 12.h,
                    ),
                    if (widget.eventDates != null)
                      _courseName(itemCount: itemCount)
                  ],
                ),
              ),

              SizedBox(
                height: 17.h,
              ),
              Center(
                  child: Container(
                height: 85.h,
                width: 325.w,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: Colors.white,
                    boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
                child: Stack(
                  children: [
                    if (selectedData?.mainImage?.url != null)
                      Positioned(
                        top: 6.h,
                        left: 13.w,
                        child: CustomImageBuilder(
                            imagePath: selectedData?.mainImage?.url ?? "",
                            height: 71.h,
                            width: 71.w,
                            borderRadius: 99.r),
                      ),
                    if (selectedData?.mainImage?.url != null)
                      Positioned(
                          top: 10.h,
                          left: 103.5.w,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              customtext(
                                  context: context,
                                  newYear: selectedData?.fullname ?? "",
                                  font: 14.sp,
                                  weight: FontWeight.w500),
                              SizedBox(
                                height: 5.h,
                              ),
                              customtext(
                                  context: context,
                                  newYear: selectedData?.phone ?? "",
                                  font: 14.sp,
                                  weight: FontWeight.w500),
                              SizedBox(
                                height: 5.h,
                              ),
                              customtext(
                                  context: context,
                                  newYear: selectedData?.birthday ?? "",
                                  font: 14.sp,
                                  weight: FontWeight.w500),
                            ],
                          )),
                    if (selectedData == null)
                      Positioned.fill(
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.person_add_alt_1_rounded,
                                size: 28,
                                color: Colors.grey[400],
                              ),
                              SizedBox(height: 8.h),
                              customtext(
                                context: context,
                                newYear: "Please select a child",
                                font: 14.sp,
                                weight: FontWeight.w500,
                                color: Colors.grey[600],
                              ),
                            ],
                          ),
                        ),
                      ),
                    Positioned(
                      top: 10.h,
                      right: 20.h,
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () async {
                            final result = await Navigator.pushNamed(
                                context, AppRoutes.changeChild,
                                arguments: {
                                  'classId': widget.classId,
                                });
                            if (result != null && mounted) {
                              final data = result as Map<String, dynamic>;
                              setState(() {
                                selectedData = ChildModel(
                                  id: data['id'],
                                  mainImage: BusinessCertificate(
                                      url: data['imagePath']),
                                  fullname: data['name'],
                                  phone: data['phone'],
                                  birthday: data['dateTime'],
                                );
                                widget.childController.text = data['id'];
                              });
                            }
                          },
                          child: customtext(
                            context: context,
                            newYear: selectedData == null ? "Select" : "Change",
                            font: 13.sp,
                            weight: FontWeight.w500,
                            color: AppPallete.change,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )),

              ///SEN
              SizedBox(
                height: 24.5.h,
              ),
              Padding(
                padding: EdgeInsets.only(left: 24.5.w),
                child: customtext(
                  context: context,
                  newYear: "Special note",
                  font: 15.sp,
                  weight: FontWeight.w500,
                ),
              ),
              SizedBox(
                height: 15.h,
              ),
              Padding(
                padding: EdgeInsets.only(left: 24.5.w),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          isSelected = !isSelected;

                          if (isSelected) {
                            widget.controller.text = "SEN"; // Set desired text
                          } else {
                            widget.controller
                                .clear(); // Clear the text if deselected
                          }
                          print(widget.controller.text);
                        });
                      },
                      child: Container(
                        width: 20.w,
                        height: 20.h,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.black, width: 2),
                        ),
                        child: Center(
                          child: Container(
                            width: 10.w,
                            height: 10.h,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: isSelected
                                  ? Colors.black
                                  : Colors.transparent,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 6.w,
                    ),
                    customtext(
                      context: context,
                      newYear: "SEN support",
                      font: 15.sp,
                      weight: FontWeight.w500,
                    ),
                    // SizedBox(
                    //   width: 13.5.w,
                    // ),
                    // customSvgPicture(
                    //     imagePath: ImagePath.plusSvg, height: 15.h, width: 10.w),
                    // SizedBox(
                    //   width: 6.w,
                    // ),
                    // customSvgPicture(
                    //     imagePath: ImagePath.zSvg, height: 15.h, width: 15.w),
                    // SizedBox(
                    //   width: 5.w,
                    // ),
                    // customtext(
                    //   context: context,
                    //   newYear: "2/class",
                    //   font: 15.sp,
                    //   weight: FontWeight.w500,
                    // ),
                  ],
                ),
              ),
              SizedBox(
                height: 21.h,
              )
            ]));
  }

  Widget _courseName({required int itemCount}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final event = widget.eventDates?[index];
              String eventDate = DateFormat('dd/MM/yyyy').format(event!);
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  customtext(
                      context: context,
                      newYear: "#${index + 1}",
                      font: 15.sp,
                      weight: FontWeight.w500),
                  customtext(
                      context: context,
                      newYear: eventDate,
                      font: 15.sp,
                      weight: FontWeight.w500),
                  customtext(
                      context: context,
                      newYear: "${widget.startTime} - ${widget.endTime}",
                      font: 15.sp,
                      weight: FontWeight.w500),
                  customtext(
                      context: context,
                      newYear: widget.classTime,
                      font: 15.sp,
                      weight: FontWeight.w500)
                ],
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 10.h,
              );
            },
            itemCount: itemCount),
        if (widget.eventDates!.length > 1)
          Center(
            child: TextButton(
                onPressed: () {
                  setState(() {
                    isExpanded = !isExpanded;
                  });
                },
                child: customtext(
                    context: context,
                    newYear: isExpanded ? 'Hide' : 'More',
                    font: 13.sp,
                    weight: FontWeight.w400,
                    color: AppPallete.moreHide)),
          ),
      ],
    );
  }
}
