import 'package:class_z/core/imports.dart';

Widget experienceCard(
    {required BuildContext context,
    required int exp,
    required String tier,
    required double progress}) {
  double width = 286.w;
  int offerCheck = 0;
  if (tier == 'Learner')
    offerCheck = 1;
  else if (tier == 'Proficient')
    offerCheck = 2;
  else if (tier == 'Master')
    offerCheck = 3;
  else if (tier == 'Expert') offerCheck = 4;
  print(width * 0.7);
  return Container(
    padding: EdgeInsets.only(left: 24.w, right: 23.w),
    decoration: BoxDecoration(
        gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppPallete.secondaryColor.withOpacity(0.8),
              AppPallete.color76.withOpacity(0.7),
              AppPallete.color128.withOpacity(0.7),
              AppPallete.color234.withOpacity(0.5)
            ]),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 40.h,
        ),
        circleWithExp(
            context: context, progress: progress, tier: tier, exp: exp),
        SizedBox(
          height: 25.h,
        ),
        customtext(
            context: context,
            newYear: "Accumulative experience earned",
            font: 20.w,
            weight: FontWeight.w400,
            color: Colors.white),
        SizedBox(
          height: 25.h,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            customtext(
                context: context,
                newYear: "From class participation",
                font: 15.w,
                weight: FontWeight.w400,
                color: Colors.white),
            customtext(
                context: context,
                newYear: "$exp EXP",
                font: 15.w,
                weight: FontWeight.w400,
                color: Colors.white),
          ],
        ),
        SizedBox(
          height: 25.h,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            customtext(
                context: context,
                newYear: "From class performance",
                font: 15.sp,
                weight: FontWeight.w400,
                color: Colors.white),
            customtext(
                context: context,
                newYear: "$exp EXP",
                font: 15.sp,
                weight: FontWeight.w400,
                color: Colors.white),
          ],
        ),
        SizedBox(
          height: 25.h,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            customtext(
                context: context,
                newYear: "Total",
                font: 20.sp,
                weight: FontWeight.w400,
                color: Colors.white),
            customtext(
                context: context,
                newYear: "$exp EXP",
                font: 20.sp,
                weight: FontWeight.w400,
                color: Colors.white),
          ],
        ),
        SizedBox(
          height: 25.h,
        ),
        customtext(
            context: context,
            newYear: "Earn 1 EXP to advance to the next tier",
            font: 20.w,
            weight: FontWeight.w400,
            color: Colors.white),
        SizedBox(
          height: 25.h,
        ),
        customtext(
            context: context,
            newYear: "In the next tier, you'll receive:",
            font: 20.sp,
            weight: FontWeight.w400,
            color: Colors.white),
        SizedBox(
          height: 25.h,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _receiveToer(
                context: context,
                imagePath: ImagePath.prioritySvg,
                height: 39.h,
                width: 42.w,
                color: offerCheck >= 1 ? Colors.white : AppPallete.forgetButton,
                title: "Priority Support"),
            _receiveToer(
                context: context,
                imagePath: ImagePath.achievementSvg,
                height: 39.h,
                width: 42.w,
                color: offerCheck >= 2 ? Colors.white : AppPallete.forgetButton,
                title: "Achievement Certificate"),
          ],
        ),
        SizedBox(
          height: 25.h,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _receiveToer(
                context: context,
                imagePath: ImagePath.vipSvg,
                height: 37.5.h,
                width: 27.w,
                color: offerCheck >= 3 ? Colors.white : AppPallete.forgetButton,
                title: "VIP Access to Event"),
            _receiveToer(
                context: context,
                imagePath: ImagePath.exclusiveSvg,
                height: 32.h,
                width: 44.w,
                color: offerCheck == 4 ? Colors.white : AppPallete.forgetButton,
                title: "Exclusive Deal"),
          ],
        ),
        SizedBox(
          height: 25.h,
        )
      ],
    ),
  );
}

Widget circleWithExp(
    {required BuildContext context,
    required double progress,
    required String tier,
    required int exp}) {
  return Center(
    child: SizedBox(
      height: 158.h,
      width: 286.w,
      child: Stack(
        children: [
          // HalfCircleProgressBar positioned in the center
          Positioned(
            top: 20,
            left: 0,
            right: 0,
            bottom: 0,
            child: Align(
              alignment: Alignment.center,
              child: HalfCircleProgressBar(
                progress: progress,
                size: 250.w,
              ),
            ),
          ),
          // EXP text positioned in the center of the HalfCircleProgressBar
          Positioned(
            top: 63.h,
            left: 0,
            right: 0,
            child: Align(
              alignment: Alignment.center,
              child: customtext(
                context: context,
                newYear: "$exp EXP",
                font: 30.sp,
                weight: FontWeight.w500,
                color: Colors.white,
                shadows: [shadow(blurRadius: 15, opacity: 0.1)],
              ),
            ),
          ),
          // Current tier text positioned below the EXP text
          Positioned(
            top: 106.h,
            left: 0,
            right: 0,
            child: Align(
              alignment: Alignment.center,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  customtext(
                    context: context,
                    newYear: "Current tier:",
                    font: 20.sp,
                    weight: FontWeight.w500,
                    color: Colors.white,
                    shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                  ),
                  SizedBox(width: 6.w),
                  Container(
                    width: 93.w,
                    padding: EdgeInsets.only(left: 12.w, top: 5.h, bottom: 5.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Row(
                      children: [
                        customtext(
                          context: context,
                          newYear: tier,
                          font: 13.sp,
                          weight: FontWeight.w500,
                          color: AppPallete.secondaryColor,
                        ),
                        customSvgPicture(
                          imagePath: ImagePath.techSvg,
                          height: 15.83.h,
                          width: 14.97.w,
                          color: AppPallete.secondaryColor,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

Widget _receiveToer(
    {required BuildContext context,
    required String imagePath,
    required double height,
    required double width,
    required String title,
    required Color color}) {
  return Container(
    width: 167.w,
    padding: EdgeInsets.only(bottom: 11.h),
    decoration:
        BoxDecoration(color: color, borderRadius: BorderRadius.circular(20.r)),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: 21.h,
        ),
        customSvgPicture(
            imagePath: imagePath,
            height: height,
            width: width,
            color: AppPallete.secondaryColor),
        SizedBox(
          height: 6.h,
        ),
        customtext(
            context: context,
            newYear: title,
            font: 15.sp,
            weight: FontWeight.w400,
            color: AppPallete.secondaryColor),
      ],
    ),
  );
}
