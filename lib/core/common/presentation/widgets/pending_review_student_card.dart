import 'package:class_z/core/imports.dart';

Widget pendingReviewStudentCard(
    {required BuildContext context,
    required String imagePath,
    required String name,
    required String coach,
    required String course,
    required double rating,
    required int age,
    required DateTime dateTime,
    required VoidCallback onTap}) {
  final String formatedDate = DateFormat("dd-MM-yyyy").format(dateTime);
  final String formatedTime = DateFormat("HH:MM").format(dateTime);
  double width = MediaQuery.of(context).size.width - 34.w - 27.w;
  double height = MediaQuery.of(context).size.height * 0.6;

  return GestureDetector(
    onTap: onTap,
    child: SizedBox(
        width: width / 2,
        height: 241.h,
        child: Stack(
          children: [
            CustomImageBuilder(
                imagePath: imagePath,
                height: 197.h,
                width: width / 2,
                borderRadius: 20.r),
            Positioned(
                top: 15.h,
                left: 10.w,
                child:
                    customRating(context: context, rating: rating.toString())),
            Positioned(
              top: 16.h,
              right: 10.w,
              child: Container(
                //   height: 13.h,
                width: 40.w,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.r)),
                child: Center(
                  child: customtext(
                      context: context,
                      newYear: "Age $age",
                      font: 12.sp,
                      weight: FontWeight.w400,
                      shadows: [shadow(blurRadius: 4, opacity: 0.25)]),
                ),
              ),
            ),
            Positioned(top: 32.h, right: 11.w, child: customSen()),
            Positioned(
                top: 147.h,
                left: 5.w,
                child: customtext(
                    context: context,
                    newYear: name,
                    font: 18.sp,
                    weight: FontWeight.w700,
                    color: Colors.white,
                    shadows: [shadow(blurRadius: 4, opacity: 0.25)])),
            Positioned(
                top: 169.h,
                left: 5.w,
                child: customtext(
                    context: context,
                    newYear: course,
                    font: 12.sp,
                    weight: FontWeight.w400,
                    color: Colors.white,
                    shadows: [shadow(blurRadius: 4, opacity: 0.25)])),
            Positioned(
                bottom: 22.h,
                child: customtext(
                  context: context,
                  newYear: coach,
                  font: 15.sp,
                  weight: FontWeight.w400,
                  color: AppPallete.darkGrey,
                )),
            Positioned(
                bottom: 2.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    customtext(
                      context: context,
                      newYear: formatedDate,
                      font: 13.sp,
                      weight: FontWeight.w400,
                      color: AppPallete.darkGrey,
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    customtext(
                      context: context,
                      newYear: formatedTime,
                      font: 13.sp,
                      weight: FontWeight.w400,
                      color: AppPallete.darkGrey,
                    ),
                  ],
                ))
          ],
        )),
  );
}
