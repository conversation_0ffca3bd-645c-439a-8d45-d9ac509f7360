import 'package:class_z/core/imports.dart';

Widget customTimeContainer(
    {required BuildContext context, required String duration}) {
  return Align(
    alignment: Alignment.topRight,
    child: Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppPallete.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.access_time,
            size: 14.sp,
            color: AppPallete.secondaryColor,
          ),
          SizedBox(width: 4),
          customtext(
            context: context,
            newYear: duration,
            font: 13.sp,
            color: AppPallete.secondaryColor,
            weight: FontWeight.w600,
          ),
        ],
      ),
    ),
  );
}
