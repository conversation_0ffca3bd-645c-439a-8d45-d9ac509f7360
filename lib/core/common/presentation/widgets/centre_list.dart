import 'package:class_z/core/imports.dart';

// FIXED: Null data display prevention
// This widget now expects properly validated data from calling widgets
// All null checks and filtering should be done before calling this widget
// to prevent "null-null" age groups and other invalid data display
Widget centreList(
    {required BuildContext context,
    required String title,
    required String time,
    required String minute,
    required String imagePath,
    required String category,
    required String name,
    required String location,
    required String language,
    required String ageGroup,
    required String currentStudent,
    required String totalStudent,
    required String cost,
    required bool sen,
    required VoidCallback onTap}) {
  Color color = Colors.white;
  if (currentStudent == totalStudent) {
    color = AppPallete.color255;
  }
  print(imagePath);
  return Container(
    color: color,
    padding: EdgeInsets.only(right: 15.w, left: 8.w),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            customtext(context: context, newYear: time, font: 15.sp),
          ],
        ),
        SizedBox(
          width: 8.w,
        ),
        Expanded(
          child: GestureDetector(
            onTap: onTap,
            child: SizedBox(
                height: 147.h,
                width: 352.w,
                child: Stack(children: [
                  Positioned(
                      top: 41.h,
                      left: 3.w,
                      right: 4.w,
                      child: CustomImageBuilder(
                        imagePath: imagePath,
                        height: 91.h,
                        width: 345.w,
                        borderRadius: 10,
                      )),
                  Positioned(
                      left: 3.w,
                      right: 4.w,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          customtext(
                              context: context,
                              newYear: title,
                              font: 15.sp,
                              weight: FontWeight.w700),
                          location == 'online'
                              ? online(context: context)
                              : buildLocation(
                                  context: context,
                                  text: location,
                                  color: AppPallete.scheduleColor2,
                                  font: 13.sp),
                        ],
                      )),
                  Positioned(
                      left: 3.w,
                      right: 4.w,
                      top: 20.h,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          customtext(
                              context: context,
                              newYear: category,
                              font: 13.sp,
                              weight: FontWeight.w400),
                          customSen()
                        ],
                      )),
                  Positioned(
                      top: 90.h,
                      left: 11.w,
                      child: Row(children: [
                        customtext(
                            context: context,
                            newYear: name,
                            font: 15.sp,
                            color: Colors.white,
                            shadows: [_shadow()],
                            weight: FontWeight.w700),
                      ])),
                  Positioned(
                    top: 44.h,
                    right: 17.w,
                    child: customTimeContainer(
                      context: context,
                      duration: minute.isNotEmpty ? minute : "No Time",
                    ),
                  ),
                  Positioned(
                      top: 106.h,
                      left: 12.w,
                      child: Row(
                        children: [
                          customSvgPicture(
                              imagePath: ImagePath.groupSvg,
                              height: 12.h,
                              width: 13.h,
                              color: Colors.white),
                          SizedBox(
                            width: 2.w,
                          ),
                          customtext(
                              context: context,
                              newYear: "$currentStudent/$totalStudent",
                              font: 15.sp,
                              shadows: [_shadow()],
                              weight: FontWeight.w700,
                              color: Colors.white),
                          SizedBox(
                            width: 8.w,
                          ),
                          customtext(
                              context: context,
                              newYear: "Age $ageGroup",
                              font: 15.sp,
                              shadows: [_shadow()],
                              weight: FontWeight.w700,
                              color: Colors.white),
                        ],
                      )),
                  Positioned(
                      top: 91.h,
                      right: 17.w,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Row(
                            children: [
                              customSvgPicture(
                                  imagePath: ImagePath.zSvg,
                                  height: 15.h,
                                  width: 15.h,
                                  color: Colors.white),
                              SizedBox(
                                width: 2.w,
                              ),
                              customtext(
                                  context: context,
                                  newYear: cost,
                                  font: 15.sp,
                                  shadows: [_shadow()],
                                  weight: FontWeight.w700,
                                  color: Colors.white),
                            ],
                          ),
                          customtext(
                              context: context,
                              newYear: language,
                              font: 15.sp,
                              shadows: [_shadow()],
                              weight: FontWeight.w700,
                              color: Colors.white),
                        ],
                      ))
                ])),
          ),
        ),
      ],
    ),
  );
}

BoxShadow _shadow() {
  return BoxShadow(
    color: Colors.black.withOpacity(0.8), // rgba(0, 0, 0, 0.25)
    blurRadius: 15.0,
    offset: const Offset(0, 0),
  );
}

Widget online({required BuildContext context}) {
  return Row(
    children: [
      customSvgPicture(
          imagePath: ImagePath.locationSvg, height: 16.67.h, width: 11.67.w),
      SizedBox(
        width: 6.33.w,
      ),
      customtext(
          context: context,
          newYear: "online",
          font: 15.sp,
          weight: FontWeight.w400)
    ],
  );
}
