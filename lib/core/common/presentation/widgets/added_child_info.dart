import 'package:class_z/core/imports.dart';

Widget addedChildInfo(
    {required String id,
    required BuildContext context,
    required String imagePath,
    required String fullname,
    String? school,
    String? birthday,
    required bool sen,
    required String done,
    required VoidCallback removeTap,
    VoidCallback? onTap}) {
  print(imagePath);
  return GestureDetector(
    onTap: onTap,
    child: Container(
      //  height: 105.h,
      width: 390.w,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          shadow(blurRadius: 15, opacity: 0.1, xoffset: 0, yoffset: 0)
        ],
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (done == "done")
            GestureDetector(
              onTap: () {
                // Handle button tap
              },
              child: GestureDetector(
                onTap: removeTap,
                child: Container(
                  height: 17.h,
                  width: 17.w,
                  decoration: const BoxDecoration(
                    color: AppPallete.secondaryColor,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.remove,
                    color: Colors.white,
                    size: 16.0,
                  ),
                ),
              ),
            )
          else
            Padding(padding: EdgeInsets.only(left: 12.w)),
          Padding(
            padding: EdgeInsets.only(top: 22.h),
            child: CustomImageBuilder(
              imagePath: imagePath,
              height: 80.h,
              width: 80.w,
              borderRadius: 80.w,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 11.w, top: 11.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    customtext(
                      context: context,
                      newYear: fullname,
                      font: 17.sp,
                      weight: FontWeight.w500,
                    ),
                    SizedBox(width: 15.w),
                    if (sen == true) customSen(),
                  ],
                ),
                SizedBox(height: 17.h),
                customtext(
                  context: context,
                  newYear: school ?? "",
                  font: 13.sp,
                  weight: FontWeight.w400,
                ),
                SizedBox(height: 17.h),
                customtext(
                  context: context,
                  newYear: birthday ?? "",
                  font: 13.sp,
                  weight: FontWeight.w400,
                ),
                SizedBox(
                  height: 17.h,
                )
              ],
            ),
          ),
        ],
      ),
    ),
  );
}
