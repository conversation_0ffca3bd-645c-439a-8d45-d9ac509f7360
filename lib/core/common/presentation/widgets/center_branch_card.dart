import 'package:class_z/core/imports.dart';

Widget centerBranchCard(
    {required BuildContext context,
    required bool edit,
    required String imagePath,
    required String center,
    required String location,
    required String upcomingClass}) {
  return Container(
    width: 388.w,
    height: 139.h,
    decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
        color: Colors.white,
        boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
    child: Stack(
      children: [
        if (edit == true)
          Positioned(
              top: 0,
              left: 0,
              child: Container(
                  width: 17.w,
                  height: 17.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.r),
                    color: AppPallete.secondaryColor,
                  ),
                  child: Icon(
                    Icons.remove,
                    color: Colors.white,
                    size: 9.w,
                  ))),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 8.w, top: 7.h),
              child: CustomImageBuilder(
                  imagePath: imagePath,
                  height: 126.h,
                  width: 126.w,
                  borderRadius: 20.r),
            ),
            SizedBox(
              width: 8.w,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 35.h,
                ),
                customtext(
                    context: context,
                    newYear: center,
                    font: 20.sp,
                    weight: FontWeight.w700,
                    color: AppPallete.secondaryColor),
                SizedBox(
                  height: 10.h,
                ),
                Row(
                  children: [
                    Icon(
                      Icons.map,
                      size: 18.r,
                      color: AppPallete.darkGrey,
                    ),
                    customtext(
                        context: context,
                        newYear: location,
                        font: 15.sp,
                        weight: FontWeight.w400),
                  ],
                ),
                SizedBox(
                  height: 10.h,
                ),
                customtext(
                  context: context,
                  newYear: "Upcoming class: $upcomingClass",
                  font: 15.sp,
                  weight: FontWeight.w500,
                ),
              ],
            )
          ],
        ),
      ],
    ),
  );
}
