import 'package:class_z/core/imports.dart';

Widget Button(
    {required String buttonText,
    required Color color,
    double? height,
    double? width,
    double? textSize,
    List<BoxShadow>? shadows,
    FontWeight? fontWeight,
    Color? textColorFinal,
    VoidCallback? onPressed,
    double? borderRadius,
    BoxBorder? border}) {
  Color textColor =
      color == Colors.white ? AppPallete.redWordColor : Colors.white;
  return Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: onPressed,
      child: Container(
        width: width ?? double.infinity,
        height: height ?? 64.h,
        decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(borderRadius ?? 15),
            boxShadow: shadows ?? [],
            border: border),
        child: Center(
          child: Text(
            buttonText,
            style: TextStyle(
                color: textColorFinal ?? textColor,
                fontSize: textSize ?? 20.sp,
                fontWeight: fontWeight ?? FontWeight.w700),
          ),
        ),
      ),
    ),
  );
}
