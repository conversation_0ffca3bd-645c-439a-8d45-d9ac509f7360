import 'package:class_z/core/imports.dart';

Widget centerMessageCard(
    {required BuildContext context,
    required String imagePath,
    required String name,
    required String message,
    required bool read,
    required DateTime pastTime}) {
  String getTimeDifference() {
    final now = DateTime.now();
    final difference = now.difference(pastTime);

    if (difference.inSeconds < 60) {
      return '${difference.inSeconds} seconds';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} mins';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hours';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days';
    } else {
      return DateFormat.yMMMd().format(pastTime);
    }
  }

  return SizedBox(
    child: Row(
      children: [
        CustomImageBuilder(
            imagePath: imagePath,
            height: 60.h,
            width: 60.w,
            borderRadius: 60.r),
        SizedBox(
          width: 16.w,
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  customtext(
                      context: context,
                      newYear: name,
                      font: 15.sp,
                      weight: FontWeight.w500),
                  customtext(
                      context: context,
                      newYear: getTimeDifference(),
                      font: 12.sp,
                      weight: FontWeight.w400,
                      color: AppPallete.greyWord),
                ],
              ),
              SizedBox(
                height: 6.h,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  customtext(
                      context: context,
                      newYear: message,
                      font: 12.sp,
                      weight: FontWeight.w400),
                  Row(
                    children: [
                      if (read == false)
                        Icon(
                          Icons.circle,
                          size: 6.r,
                          color: AppPallete.darkGrey,
                        ),
                      CustomIconButton(
                          icon: Icons.arrow_forward_ios,
                          color: AppPallete.darkGrey,
                          height: 24.h,
                          width: 24.w,
                          onPressed: () {})
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
