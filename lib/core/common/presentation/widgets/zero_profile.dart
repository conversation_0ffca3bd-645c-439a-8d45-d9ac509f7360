import 'package:class_z/core/imports.dart';

Widget zeroProfile({required BuildContext context}) {
  return Column(
    children: [
      SizedBox(
        height: 224.h,
      ),
      GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AddChild(),
            ),
          );
        },
        child: Center(
          child: Container(
            height: 100.h,
            width: 100.w,
            decoration: BoxDecoration(
                color: AppPallete.secondaryColor,
                borderRadius: BorderRadius.circular(99.r)),
            child: SizedBox(
              height: 70.h,
              width: 70.w,
              child: CustomIconButton(
                  icon: Icons.add,
                  color: Colors.white,
                  iconSize: 42,
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => AddChild(),
                      ),
                    );
                  }),
            ),
          ),
        ),
      ),
      SizedBox(
        height: 29.h,
      ),
      Center(
        child: customtext(
            context: context,
            newYear: "Add new Profile",
            font: 20.sp,
            weight: FontWeight.w400,
            color: AppPallete.change),
      )
    ],
  );
}
