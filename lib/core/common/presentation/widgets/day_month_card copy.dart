import 'package:class_z/core/imports.dart';

Widget dayMonthCard(
    {required BuildContext context,
    required String title1,
    required String title2,
    required List<String> list1,
    required List<String> list2,
    bool minus = true}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.start,
    children: [
      DropDown(
        label: title1,
        times: list1,
        color: AppPallete.paleGrey,
      ),
      SizedBox(
        width: 11.w,
      ),
      if (minus == true)
        customtext(
            context: context,
            newYear: "-",
            font: 15.sp,
            weight: FontWeight.w500),
      SizedBox(
        width: 11.w,
      ),
      DropDown(
        label: title2,
        times: list2,
        color: AppPallete.paleGrey,
      ),
    ],
  );
}

class DropDown extends StatefulWidget {
  final String label;
  final List<String> times;
  final Color color;
  final String? selectedValue;
  final double? width;
  final double? height;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final ValueChanged<String?>? onChanged;

  const DropDown({
    Key? key,
    required this.label,
    required this.times,
    required this.color,
    this.selectedValue,
    this.width,
    this.height,
    this.controller,
    this.validator,
    this.onChanged,
  }) : super(key: key);

  @override
  _DropDownState createState() => _DropDownState();
}

class _DropDownState extends State<DropDown> {
  String? selectedTime;

  @override
  void initState() {
    super.initState();
    selectedTime = widget.selectedValue;
    if (widget.controller != null && selectedTime != null) {
      widget.controller!.text = selectedTime!;
    }
  }

  @override
  Widget build(BuildContext context) {
    double ratio=(widget.width??92.w)/430;
    return Container(
      width: widget.width,
      height: widget.height ?? 30.0,
      decoration: BoxDecoration(
        color: widget.color,
        borderRadius: BorderRadius.circular(5.0),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          isExpanded: true,
          hint: Padding(
            padding: EdgeInsets.only(left: 5.w),
            child: Text(
              widget.label,
              style: TextStyle(
                fontSize: 15.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          value: selectedTime,
          onChanged: (newValue) {
            setState(() {
              selectedTime = newValue;
              if (widget.controller != null && newValue != null) {
                widget.controller!.text = newValue;
              }
              if (widget.onChanged != null) {
                widget.onChanged!(newValue);
              }
            });
          },
          items: widget.times.map((time) {
            return DropdownMenuItem<String>(
              value: time,
              child: Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: customtext(context: context, newYear: time, font: 15.sp,weight: FontWeight.w400) 
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
