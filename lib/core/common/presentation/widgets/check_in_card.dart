import 'package:class_z/core/imports.dart';

Widget CheckInCard(
    {required BuildContext context,
    required EventModel event,
    required VoidCallback onTap}) {
  
  print('🔍 CheckInCard: Processing event with ID: ${event.id}');
  print('🔍 CheckInCard: Event date: ${event.date}');
  print('🔍 CheckInCard: Event classId exists: ${event.classId != null}');
  print('🔍 CheckInCard: Event classId ID: ${event.classId?.id}');
  print('🔍 CheckInCard: Event classProviding: ${event.classId?.classProviding}');
  
  // Check if event has valid class data - only require classId, not mainImage
  final bool hasValidClassData = event.classId != null;

  // Get start time from class dates or use fallback
  String startTime = 'Unknown start time';
  if (event.classId != null &&
      event.classId!.dates != null &&
      event.classId!.dates!.isNotEmpty) {
    startTime = event.classId!.dates!.first.startTime ?? 'Unknown start time';
    print('🔍 CheckInCard: Start time from dates: $startTime');
  } else if (event.startTime != null) {
    startTime = event.startTime!;
    print('🔍 CheckInCard: Start time from event: $startTime');
  } else {
    print('🔍 CheckInCard: No start time found, using default');
  }

  // If the event doesn't have valid class data, don't render it
  if (!hasValidClassData) {
    print('🚫 CheckInCard: Hiding event with classId: ${event.classId?.id ?? "null"}');
    return SizedBox.shrink(); // Return empty widget for invalid events
  }

  print('✅ CheckInCard: Showing event with classId: ${event.classId!.id}, startTime: $startTime');

  return GestureDetector(
    onTap: onTap,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Stack(
          children: [
            CustomImageBuilder(
              imagePath: imageStringGenerator(
                  imagePath: event.classId?.mainImage?.url ?? ''),
              height: 92.h,
              width: 187.w,
              borderRadius: 10.r,
            ),
            Positioned(
              top: 10.h,
              left: 9.w,
              right: 11.w,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  customSen(width: 27.w),
                  Row(
                    children: [
                      customSvgPicture(
                        imagePath: ImagePath.groupSvg,
                        height: 10.h,
                        width: 11.w,
                        color: Colors.white,
                      ),
                      customtext(
                        context: context,
                        newYear:
                            "${event.dateIdClassDate?.students?.length ?? 0}/${event.dateIdClassDate?.numberOfStudent ?? event.scheduleNumberOfStudent ?? 0}",
                        font: 12.sp,
                        weight: FontWeight.w700,
                        color: Colors.white,
                        shadows: [
                          shadow(
                              blurRadius: 4,
                              opacity: 0.25,
                              xoffset: 0,
                              yoffset: 4)
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h), // Space between image and text
        Row(
          children: [
            customtext(
              context: context,
              newYear: dateGenerator(date: event.date!),
              font: 13.sp,
              color: AppPallete.darkGrey,
              weight: FontWeight.w400,
            ),
            SizedBox(width: 10.w),
            customtext(
              context: context,
              newYear: startTime,
              font: 13.sp,
              color: AppPallete.darkGrey,
              weight: FontWeight.w400,
            ),
          ],
        ),
        customtext(
          context: context,
          newYear: event.classId?.classProviding ?? 'Unknown',
          font: 13.sp,
          color: AppPallete.darkGrey,
          weight: FontWeight.w400,
        ),
      ],
    ),
  );
}
