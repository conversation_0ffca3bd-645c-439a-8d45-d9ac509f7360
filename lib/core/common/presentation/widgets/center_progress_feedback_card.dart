import 'package:class_z/core/imports.dart';

Widget centerProgressFeedBackCard(
    {required BuildContext context,
    required String user,
    required bool confirmed,
    required String date,
    required String center,
    required String course,
    required String time,
    required String classTime,
    required bool special,
    required String coachName}) {
  return GestureDetector(
    onTap: () {
      NavigatorService.pushNamed(AppRoutes.ordersDetailsConTimetable);
    },
    child: Container(
      // height: 184.h,
      //width: 388.w,
      decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
          borderRadius: BorderRadius.circular(20.r)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          ///name and blue
          Container(
            height: 27.h,
            width: double.infinity,
            decoration: BoxDecoration(
                color: AppPallete.secondaryColor,
                boxShadow: [shadow(blurRadius: 15, opacity: 0.1)],
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    topRight: Radius.circular(20.r))),
            child: Padding(
              padding: EdgeInsets.only(left: 9.w, top: 8.h),
              child: customtext(
                  context: context,
                  newYear: user,
                  font: 12.sp,
                  weight: FontWeight.w600,
                  color: Colors.white,
                  shadows: [shadow(blurRadius: 15, opacity: 0.1)]),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 9.w, right: 14.w, top: 15.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                customtext(
                  context: context,
                  newYear: date,
                  font: 12.sp,
                  weight: FontWeight.w500,
                ),
                Row(
                  children: [
                    customSvgPicture(
                        imagePath: ImagePath.locationSvg,
                        height: 16.67.h,
                        width: 11.67.w),
                    SizedBox(
                      width: 8.33.w,
                    ),
                    customtext(
                      context: context,
                      newYear: "center",
                      font: 15.sp,
                      weight: FontWeight.w400,
                    ),
                  ],
                )
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.w, top: 11.h),
            child: customtext(
              context: context,
              newYear: course,
              font: 20.sp,
              weight: FontWeight.w600,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.w, top: 11.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                customtext(
                  context: context,
                  newYear: time,
                  font: 20.sp,
                  weight: FontWeight.w500,
                ),
                SizedBox(
                  width: 12.w,
                ),
                customtext(
                  context: context,
                  newYear: classTime,
                  font: 12.sp,
                  weight: FontWeight.w500,
                ),
              ],
            ),
          ),

          special == true
              ? Padding(
                  padding: EdgeInsets.only(left: 10.w, top: 11.h),
                  child: customtext(
                    context: context,
                    newYear: "Special note: SEN support",
                    font: 12.sp,
                    weight: FontWeight.w500,
                  ),
                )
              : SizedBox(),
          Padding(
            padding: EdgeInsets.only(left: 10.w, top: 11.h),
            child: customtext(
              context: context,
              newYear: "By $coachName",
              font: 12.sp,
              weight: FontWeight.w500,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 10.w, top: 11.h, bottom: 16.h),
            child: customtext(
              context: context,
              newYear: "Coaching address: $center",
              font: 12.sp,
              weight: FontWeight.w500,
            ),
          ),
        ],
      ),
    ),
  );
}
