import 'package:class_z/core/imports.dart';

/// A reusable custom back button widget that handles navigation errors gracefully.
///
/// This button supports both direct context-based navigation and NavigatorService.
/// It includes error handling to ensure that if one method fails, the other is attempted.
Widget customBackButton({final Color? color, final VoidCallback? onTap}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      borderRadius: BorderRadius.circular(20),
      onTap: onTap ??
          () {
            print('Custom back button tapped');
            // The onTap handler will be replaced with this default implementation
            // if not provided by the caller

            try {
              // Try to use NavigatorService first (global navigator)
              NavigatorService.goBack();
              print('NavigatorService.goBack() succeeded');
            } catch (e) {
              print('NavigatorService.goBack() failed: $e');
              // If NavigatorService fails, we can't do any fallback here because
              // we don't have a BuildContext. This is why it's better to provide
              // an onTap handler when using this button that accesses context.
              print('No fallback navigation possible without context');
            }
          },
      child:
          Icon(Icons.arrow_back_ios, color: color ?? AppPallete.secondaryColor),
    ),
  );
}

/// A context-aware version of the back button that can use both NavigatorService
/// and direct Navigator.of(context) for more reliable back navigation.
///
/// Use this instead of customBackButton when you need more reliable back navigation.
Widget contextAwareBackButton(
    {required BuildContext context,
    final Color? color,
    final VoidCallback? onTap}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      borderRadius: BorderRadius.circular(20),
      onTap: onTap ??
          () {
            print('Context-aware back button tapped');
            try {
              // First try direct Navigator (more reliable with context)
              if (Navigator.of(context).canPop()) {
                print('Using Navigator.of(context).pop()');
                Navigator.of(context).pop();
              } else {
                print(
                    'Navigator.of(context) cannot pop, trying NavigatorService');
                // Fallback to NavigatorService
                NavigatorService.goBack();
              }
            } catch (e) {
              print('All navigation attempts failed: $e');
              // Show a message to the user
              ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Cannot go back. Please try again.')));
            }
          },
      child:
          Icon(Icons.arrow_back_ios, color: color ?? AppPallete.secondaryColor),
    ),
  );
}
