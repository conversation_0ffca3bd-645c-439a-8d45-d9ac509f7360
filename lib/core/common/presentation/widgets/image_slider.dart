import 'package:class_z/core/imports.dart';

class ImageSlider extends StatelessWidget {
  final List<String> imagePaths;
  final double height;
  final double width;
  const ImageSlider(
      {super.key,
      required this.imagePaths,
      required this.height,
      required this.width});

  @override
  Widget build(BuildContext context) {
    final PageController pageController = PageController();

    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: SizedBox(
            height: heightRatio(height: height) * getHeight(context: context),
            width: double.infinity,
            child: PageView.builder(
              controller: pageController,
              itemCount: imagePaths.length,
              itemBuilder: (context, index) {
                final processedImagePath =
                    imageStringGenerator(imagePath: imagePaths[index]);
                return Image.network(
                  processedImagePath,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // Logging removed as requested
                    return Container(
                      color: Colors.grey.shade300,
                      child: Center(
                        child: Icon(
                          Icons.image_not_supported_outlined,
                          size: 30.sp,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ),
        SizedBox(height: 16.h), // Spacing between the image and the indicator
        Positioned(
          left: 0,
          right: 0,
          bottom: 8,
          child: Center(
            child: SmoothPageIndicator(
              controller: pageController,
              count: imagePaths.length,
              effect: const WormEffect(
                dotHeight: 8.0,
                dotWidth: 8.0,
                spacing: 16.0,
                activeDotColor: AppPallete.secondaryColor,
                dotColor: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
