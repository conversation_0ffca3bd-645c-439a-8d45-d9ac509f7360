import 'package:class_z/core/imports.dart';

Widget customBottomButton(
    {Color? color,
    Color? textColor,
    required String buttonText,
    required VoidCallback onTap}) {
  return SizedBox(
      height: 76.h,
      width: 430.w,
      child: Column(
        children: [
          Divider(
            color: AppPallete.greyWord,
            thickness: 0.5,
            height: 1.h,
          ),
          SizedBox(
            height: 12.h,
          ),
          Center(
            child: Container(
              decoration: BoxDecoration(
                  boxShadow: [shadow(blurRadius: 15, opacity: 0.1)]),
              child: <PERSON><PERSON>(
                  height: 49.h,
                  width: 289.w,
                  buttonText: buttonText,
                  textColorFinal: textColor,
                  onPressed: onTap,
                  color: color ?? AppPallete.secondaryColor),
            ),
          ),
        ],
      ));
}
