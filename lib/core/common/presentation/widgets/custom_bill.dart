import 'package:class_z/core/imports.dart';

class CustomBill extends StatelessWidget {
  final List<BillRow> rows; // Changed from DataRow to BillRow
  final int subtotal;
  final int? discount;
  final bool? zcoin;
  final String? discountLabel; // Added parameter for custom discount label
  final bool showBothCurrencies;
  const CustomBill(
      {required this.rows,
      required this.subtotal,
      this.discount = 0,
      this.zcoin = true,
      this.discountLabel,
      this.showBothCurrencies = false,
      Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    int summary = subtotal - (discount ?? 0);
    return Padding(
      padding: EdgeInsets.only(right: 13.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(flex: 3, child: _headerText("Item:")),
              Expanded(flex: 1, child: Center(child: _headerText("QTY:"))),
              Expanded(flex: 3, child: Center(child: _headerText("Rate:"))),
              Expanded(
                  flex: 3,
                  child: Align(
                      alignment: Alignment.centerRight,
                      child: _headerText("Subtotal:"))),
            ],
          ),
          SizedBox(height: 16.h), // Spacing between header and items

          // Dynamic rows passed from another page
          if (rows.isEmpty)
            SizedBox(height: 10.h)
          else
            ...rows.expand((dataRow) => [
                  _buildRow(dataRow, context),
                  SizedBox(height: 10.h), // 10.h gap between each row
                ]),

          customDivider(width: 406.w, padding: 0),
          SizedBox(
            height: 37.h,
          ),

          // Discount and Total Rows (Subtotal is already shown in table header)
          if ((discount ?? 0) > 0) ...[
            _summaryRow(
                context: context,
                label: "Discount:",
                negative: true,
                amount: discount.toString(),
                customLabel: discountLabel),
            SizedBox(
              height: 9.h,
            ),
          ],

          //   customDivider(width: 406.w, padding: 0),
          SizedBox(
            height: 10.h,
          ),
          _summaryRow(
              context: context, label: "Total:", amount: summary.toString()),
          SizedBox(
            height: 10.h,
          )
        ],
      ),
    );
  }

  // Helper method to create header text
  Widget _headerText(String text) {
    return Text(
      text,
      style: TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: 14.sp,
      ),
    );
  }

  // Helper method to build rows dynamically
  Widget _buildRow(BillRow dataRow, BuildContext context) {
    // Get cells based on whether they're already provided or need to be built
    List<DataCell> cells =
        dataRow.cells.isNotEmpty ? dataRow.cells : dataRow.buildCells(context);

    // Check if cells has at least 4 elements, if not pad with empty cells
    while (cells.length < 4) {
      cells.add(DataCell(SizedBox()));
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 0.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
              flex: 3,
              child: Padding(
                  padding: EdgeInsets.only(right: 4.w),
                  child: cells[0].child)), // More space for Item
          Expanded(
              flex: 1,
              child: Center(child: cells[1].child)), // Less space for QTY
          Expanded(
              flex: 3,
              child: Center(
                  child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 2.w),
                      child: cells[2].child))), // Center aligned for Rate
          Expanded(
              flex: 3,
              child: Align(
                  alignment: Alignment.centerRight,
                  child: cells[3].child)), // Right aligned for Subtotal
        ],
      ),
    );
  }

  // Helper method to create summary rows like subtotal, discount, total
  Widget _summaryRow(
      {required BuildContext context,
      required String label,
      required String amount,
      String? customLabel,
      bool? negative}) {
    return Align(
      alignment: Alignment.centerRight,
      child: SizedBox(
        width: 280.w, // Increased width to accommodate the discount label
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Expanded(
              child: Row(
                mainAxisSize: MainAxisSize.min, // Use minimum space
                children: [
                  Flexible(
                    child: Text(
                      label,
                      style: _itemTextStyle(),
                      overflow: TextOverflow.ellipsis, // Handle text overflow
                    ),
                  ),
                  if (customLabel != null)
                    Padding(
                      padding: EdgeInsets.only(left: 4.w), // Reduced padding
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 4.w, vertical: 2.h), // Reduced padding
                        decoration: BoxDecoration(
                          color: AppPallete.secondaryColor,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          customLabel,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            SizedBox(width: 10.w), // Reduced spacing
            rateWithIconTotal(
              context: context,
              rate: amount,
              negative: negative,
              zcoin: zcoin,
              showBoth: showBothCurrencies,
            )
          ],
        ),
      ),
    );
  }

  // Helper method to define the text style for items
  TextStyle _itemTextStyle() {
    return TextStyle(
      fontSize: 14.sp,
      fontWeight: FontWeight.w500,
    );
  }
}

Widget rateWithIconRate({
  required BuildContext context,
  required String rate,
  bool? zcoin = true,
  bool showBoth = false,
}) {
  if (showBoth) {
    final rateNum = num.tryParse(rate) ?? 0;
    final zcoinAmount = rateNum.toStringAsFixed(0);
    final hkdAmount = (rateNum * 25).toStringAsFixed(0); // 1 Zcoin = 25 HKD
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        customSvgPicture(imagePath: ImagePath.zSvg, height: 15.h, width: 15.w),
        SizedBox(width: 4.w),
        Flexible(
          child: customtext(
              context: context,
              newYear: '$zcoinAmount / HKD $hkdAmount',
              font: 12.sp,
              weight: FontWeight.w500),
        ),
      ],
    );
  }

  return Row(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      zcoin == false
          ? customtext(
              context: context,
              newYear: 'HKD ',
              font: 13.sp,
              weight: FontWeight.w500)
          : customSvgPicture(
              imagePath: ImagePath.zSvg, height: 15.h, width: 15.w),
      customtext(
          context: context, newYear: rate, font: 15.sp, weight: FontWeight.w500)
    ],
  );
}

Widget rateWithIconTotal({
  required BuildContext context,
  required String rate,
  bool? negative,
  bool? zcoin,
  bool showBoth = false,
}) {
  if (showBoth) {
    final rateNum = num.tryParse(rate) ?? 0;
    final zcoinAmount = rateNum.toStringAsFixed(0);
    final hkdAmount = (rateNum * 25).toStringAsFixed(0); // 1 Zcoin = 25 HKD
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (negative == true) Icon(Icons.remove, size: 14.w),
        customSvgPicture(imagePath: ImagePath.zSvg, height: 15.h, width: 15.w),
        SizedBox(width: 4.w),
        Flexible(
          child: customtext(
            context: context,
            newYear: '$zcoinAmount / HKD $hkdAmount',
            font: 12.sp,
            weight: FontWeight.w500,
            maxLines: 2,
          ),
        ),
      ],
    );
  }

  return Row(
    mainAxisSize: MainAxisSize.min, // Use minimum space
    mainAxisAlignment: MainAxisAlignment.end,
    children: [
      if (negative == true)
        Icon(
          Icons.remove,
          size: 14.w, // Slightly smaller icon
        )
      else
        SizedBox(width: 14.w),
      SizedBox(width: 2.w), // Reduced spacing
      zcoin == false
          ? customtext(
              context: context,
              newYear: 'HKD ',
              font: 13.sp,
              weight: FontWeight.w500)
          : customSvgPicture(
              imagePath: ImagePath.zSvg,
              height: 13.h,
              width: 13.w), // Slightly smaller icon
      customtext(
          context: context,
          newYear: rate,
          font: 15.sp,
          weight: FontWeight.w500,
          overflow: TextOverflow.ellipsis) // Handle text overflow
    ],
  );
}

// Custom BillRow class for easier use with CustomBill (renamed from DataRow)
class BillRow {
  final String item;
  final String qty;
  final String rate;
  final String subtotal;
  final List<DataCell> cells;

  // Constructor for creating cells from individual fields
  BillRow({
    required this.item,
    required this.qty,
    required this.rate,
    required this.subtotal,
  }) : cells = [];

  // Helper method to build cells with context
  List<DataCell> buildCells(BuildContext context) {
    return [
      DataCell(customtext(
          context: context,
          newYear: item,
          font: 13.sp,
          weight: FontWeight.w500)),
      DataCell(Center(
          child: customtext(
              context: context,
              newYear: qty,
              font: 13.sp,
              weight: FontWeight.w500))),
      DataCell(customtext(
          context: context,
          newYear: rate,
          font: 13.sp,
          weight: FontWeight.w500)),
      DataCell(Align(
          alignment: Alignment.centerRight,
          child: customtext(
              context: context,
              newYear: subtotal,
              font: 13.sp,
              weight: FontWeight.w500))),
    ];
  }

  // Constructor for direct cell creation (for backward compatibility)
  BillRow.fromCells({required this.cells})
      : item = '',
        qty = '',
        rate = '',
        subtotal = '';
}
