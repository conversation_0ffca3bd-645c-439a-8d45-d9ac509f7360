import 'package:class_z/core/imports.dart';

Widget privateClassCard(
    {required BuildContext context,
    required String imagePath,
    required String title,
    required String category,
    required String location,
    required String ageGroup,
    required double rate,
    required String time,
    required VoidCallback onTap}) {
  return GestureDetector(
    onTap: onTap,
    child: Padding(
      padding: EdgeInsets.only(left: 28.w, right: 27.w),
      child: Container(
          height: 107.h,
          width: 375.w,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.r),
              image: DecorationImage(
                  image: AssetImage(
                    imagePath,
                  ),
                  fit: BoxFit.cover)),
          child: Stack(children: [
            Positioned(
                top: 65.h,
                left: 14.w,
                child: customtext(
                    context: context,
                    newYear: title,
                    font: 17.sp,
                    shadows: [_shadow()],
                    color: Colors.white,
                    weight: FontWeight.w700)),
            Positioned(
                bottom: 10.h,
                left: 14.w,
                child: Row(children: [
                  customtext(
                      context: context,
                      newYear: "($category)",
                      font: 12.sp,
                      color: Colors.white,
                      shadows: [_shadow()],
                      weight: FontWeight.w500),
                  SizedBox(
                    width: 13.w,
                  ),
                  customtext(
                      context: context,
                      newYear: ageGroup,
                      font: 12.sp,
                      color: Colors.white,
                      shadows: [_shadow()],
                      weight: FontWeight.w500),
                  SizedBox(
                    width: 13.w,
                  ),
                  customtext(
                      context: context,
                      newYear: location,
                      font: 12.sp,
                      color: Colors.white,
                      shadows: [_shadow()],
                      weight: FontWeight.w500),
                ])),
            Positioned(
              top: 10.h,
              right: 19.w,
              child: Container(
                width: 59.w,
                height: 22.h,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.r)),
                child: Center(
                  child: customtext(
                      context: context,
                      newYear: time,
                      font: 15.sp,
                      color: AppPallete.secondaryColor,
                      weight: FontWeight.w700),
                ),
              ),
            ),
            Positioned(
                bottom: 10.h,
                right: 19.w,
                child: Row(
                  children: [
                    customSvgPicture(
                        imagePath: ImagePath.zSvg,
                        height: 15.h,
                        width: 15.w,
                        color: Colors.white,
                        boxShadow: [
                          shadow(blurRadius: 15, opacity: 0.1),
                        ]),
                    customtext(
                      context: context,
                      newYear: "$rate",
                      color: Colors.white,
                      font: 15.sp,
                      weight: FontWeight.w700,
                      shadows: [_shadow()],
                    ),
                  ],
                ))
          ])),
    ),
  );
}

BoxShadow _shadow() {
  return BoxShadow(
    color: Colors.black.withOpacity(0.8),
    blurRadius: 15.0,
    offset: const Offset(0, 0),
  );
}
