import 'package:class_z/core/imports.dart';

/// A reusable widget that provides a customizable top bar without a title.
///
/// This widget includes a back button and notification icons.
/// It now uses the more reliable contextAwareBackButton for better navigation.
Widget customNoTitleWidget({
  required BuildContext context,
  VoidCallback? onBackPressed,
  bool showNotifications = true,
  int notificationCount = 0,
  int messageCount = 0,
  VoidCallback? onNotificationTap,
  VoidCallback? onMessageTap,
}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Padding(
        padding: const EdgeInsets.only(left: 19.0),
        child: onBackPressed != null
            ? InkWell(
                onTap: onBackPressed,
                child: Icon(
                  Icons.arrow_back_ios,
                  color: AppPallete.secondaryColor,
                ),
              )
            : contextAwareBackButton(context: context),
      ),
      if (showNotifications)
        Padding(
          padding: EdgeInsets.only(right: 34.w),
          child: Sized<PERSON><PERSON>(
            width: 94.w,
            height: 42.34.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                NotificationBadge(
                  icon: Icons.notifications,
                  badgeCount: notificationCount,
                  onTap: onNotificationTap,
                ),
                SizedBox(width: 9.w),
                NotificationBadge(
                  icon: Icons.messenger_outline_sharp,
                  badgeCount: messageCount,
                  onTap: onMessageTap,
                ),
              ],
            ),
          ),
        ),
    ],
  );
}
