import 'package:class_z/core/imports.dart';
import 'package:class_z/core/config/app_config.dart';

Widget walletCardBig(
    {required BuildContext context,
    required int money,
    bool hasActiveSubscription = false}) {
  return Padding(
    padding: EdgeInsets.only(left: 17.w, right: 18.w),
    child: Container(
      width: getawidth(context: context),
      height: 278.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
        gradient: GradientProvider.getLinearGradient(),
      ),
      child: Stack(
        children: [
          Positioned(
            top: 18.h,
            right: 17.w,
            child: Container(
              width: 124.w,
              height: 23.h,
              decoration: BoxDecoration(
                  color: hasActiveSubscription
                      ? AppPallete.secondaryColor
                      : Colors.white,
                  borderRadius: BorderRadius.circular(20.r)),
              child: Center(
                child: customtext(
                    context: context,
                    newYear: hasActiveSubscription
                        ? "Exclusive Card"
                        : "Standard Member",
                    font: 13.sp,
                    color: hasActiveSubscription
                        ? Colors.white
                        : AppPallete.secondaryColor,
                    weight: FontWeight.w500),
              ),
            ),
          ),
          Positioned(
            top: 11.h,
            left: 18.w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 30.h,
                  child: customtext(
                      context: context,
                      newYear: "Wallet",
                      color: Colors.white,
                      font: 25.sp,
                      weight: FontWeight.w600),
                ),
                SizedBox(height: 3.h),
                Container(
                  height: 30.h,
                  child: customtext(
                      context: context,
                      newYear: "Available Balance",
                      font: 15.sp,
                      weight: FontWeight.w600,
                      color: Colors.white),
                ),
                SizedBox(height: 1.h),
                Row(
                  children: [
                    customSvgPicture(
                        imagePath: ImagePath.zSvg,
                        height: 21.h,
                        width: 21.w,
                        color: Colors.white),
                    SizedBox(width: 6.w),
                    Container(
                        height: 30.h,
                        child: customtext(
                            context: context,
                            newYear: money.toString(),
                            font: 25.sp,
                            weight: FontWeight.w600,
                            color: Colors.white))
                  ],
                ),
                SizedBox(height: 21.h),
                Container(
                  height: 18.h,
                  child: customtext(
                      context: context,
                      newYear: "Need more Zcoin? Click to purchase!",
                      font: 15.sp,
                      color: Colors.white,
                      weight: FontWeight.w600),
                ),
                SizedBox(height: 15.h),
                GestureDetector(
                  onTap: () {
                    _handleTopUpPress(context);
                  },
                  child: Container(
                    width: 358.w,
                    height: 98.h,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20.r),
                        color: Colors.white),
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 28.w, vertical: 12.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          customtext(
                              context: context,
                              newYear: "Top Up",
                              font: 17.sp,
                              weight: FontWeight.w700,
                              color: AppPallete.secondaryColor),
                          SizedBox(height: 8.h),
                          customtext(
                              context: context,
                              newYear: "HKD 25/ Zcoin",
                              font: 15.sp,
                              weight: FontWeight.w300,
                              color: AppPallete.secondaryColor),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}

void _handleTopUpPress(BuildContext context) {
  // Prevent multiple taps
  ScaffoldMessenger.of(context).clearSnackBars();

  // Get the current price per ZCoin from configuration in HKD
  final int pricePerZCoinHKD =
      AppConfig.pricePerZCoinHKD ?? 25; // Default to 25 HKD if not configured
  final int defaultZCoins = 1; // Default amount to purchase

  try {
    print("User tapped Top Up button");

    // Calculate the total price based on the price per ZCoin and default quantity in HKD
    final int totalPrice = pricePerZCoinHKD * defaultZCoins;

    // Navigate to the top-up screen with the correct parameters
    NavigatorService.pushNamed(AppRoutes.topUp, arguments: {
      'usd': totalPrice, // Pass the calculated total price
      'zCoin': defaultZCoins, // Default amount to purchase
      'discount': 0 // No discount when navigating from wallet
    });
  } catch (e) {
    print("Error navigating to Top Up: $e");
    // Show error message if navigation fails
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text("Could not open Top Up screen. Please try again."),
      duration: const Duration(seconds: 2),
    ));
  }
}

Widget _moneyback(
    {required BuildContext context,
    required double z,
    required double zRebite}) {
  return Container(
      width: 79.w,
      height: 38.h,
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(5.r),
          boxShadow: [
            shadow(opacity: 0.1, xoffset: 0, yoffset: 0, blurRadius: 15)
          ]),
      child: Padding(
        padding: EdgeInsets.only(left: 5.w, top: 3.h),
        child: customtext(
            context: context,
            newYear: "${z}z for $zRebite z rebate",
            font: 13.sp,
            color: AppPallete.greyWord,
            weight: FontWeight.w600),
      ));
}

Widget zCoin(
    {required BuildContext context,
    required int coins,
    required int zCoinRate,
    required String imagePath,
    required double height,
    required double width}) {
  return Stack(
    children: [
      CustomImageBuilder(
          imagePath: ImagePath.category,
          height: 122.h,
          width: 110.w,
          borderRadius: 20.r),
      Positioned(
          top: 9.h,
          left: 8.w,
          child: customtext(
              context: context,
              newYear: "$coins Zcoins",
              font: 10.sp,
              weight: FontWeight.w600,
              color: Colors.white)),
      Positioned(
          top: 93.h,
          left: 32.w,
          child: customtext(
              context: context,
              newYear: "$zCoinRate Zcoin",
              font: 10.sp,
              weight: FontWeight.w600,
              color: Colors.white)),
      Positioned(
          top: 30.h,
          left: 28.w,
          child: customSvgPicture(
              color: Colors.white,
              imagePath: imagePath,
              height: height,
              width: width)),
    ],
  );
}
