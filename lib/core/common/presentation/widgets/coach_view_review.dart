import 'package:class_z/core/imports.dart';

Widget coachReviewCard(BuildContext context, {required ReviewModel review}) {
  return Container(
    width: 158,
    height: 110,
    decoration: BoxDecoration(
      color: Colors.white,
      border: Border.all(color: AppPallete.border, width: 0.4.w),
      borderRadius: BorderRadius.circular(20),
    ),
    child: Padding(
      padding: EdgeInsets.all(12.w), // Adjust padding for content
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              customtext(
                context: context,
                newYear: review.title ?? "No Title",
                font: 20.sp,
                color: AppPallete.darkGrey,
                weight: FontWeight.bold,
              ),
              customRating(
                  context: context,
                  rating: review.rating?.toString() ?? "0",
                  fontColor: AppPallete.black),
            ],
          ),

          // Review Text
          Expanded(
            child: customtext(
                context: context,
                newYear: review.comment ?? "No comment",
                font: 15,
                fontWeight: FontWeight.normal),
          ),

          // Date
          Align(
            alignment: Alignment.bottomRight,
            child: customtext(
              context: context,
              newYear: review.date != null
                  ? "${review.date!.day}-${review.date!.month}-${review.date!.year}"
                  : "No date",
              font: 10,
              weight: FontWeight.normal,
            ),
          ),
        ],
      ),
    ),
  );
}
