// ignore_for_file: file_names

import 'package:flutter/material.dart';

class PositionedItemWidget extends StatelessWidget {
  final double? top;
  final double? left;
  final double? right;
  final double? bottom;
  final double? opacity;
  final Widget child;

  const PositionedItemWidget({
    Key? key,
    this.top,
    this.left,
    this.right,
    this.bottom,
    this.opacity,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: top,
      left: left,
      right: right,
      bottom: bottom,
      child: Opacity(
        opacity: opacity ?? 1.0, // Default opacity is 1.0 (fully opaque)
        child: child,
      ),
    );
  }
}
