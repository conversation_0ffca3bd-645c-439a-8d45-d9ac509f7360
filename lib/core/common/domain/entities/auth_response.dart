// domain/entities/auth_response.dart
abstract class AuthResponse {
  String get email;
  List<String>? get roles; // Changed from 'type' to 'roles'
}

// domain/entities/user_auth_response.dart
class UserAuthResponse implements AuthResponse {
  @override
  final String email;
  @override
  final List<String>? roles;
  final String? type;

  UserAuthResponse({
    this.type,
    required this.email,
    required this.roles,
  });
}

// domain/entities/center_auth_response.dart
class CenterAuthResponse implements AuthResponse {
  @override
  final String email;
  @override
  final List<String>? roles;
  final String? type;

  CenterAuthResponse({
    this.type,
    required this.email,
    required this.roles,
  });

  @override
  String toString() {
    return 'CenterAuthResponse(email: $email, roles: $roles)'; // Updated to include roles
  }
}

// // domain/errors/failure.dart
// abstract class Failure {}

// class ServerFailure extends Failure {
//   final String message;

//   ServerFailure(this.message);
// }
