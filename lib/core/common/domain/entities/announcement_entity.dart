import 'package:class_z/core/imports.dart';

class AnnouncementEntity extends Equatable {
  final String? id;
  final dynamic classId;
  final ClassDate? slotId;
  final String? title;
  final BusinessCertificate? mainImage;
  final String? senderName;
  final BusinessCertificate? senderImage;
  final List<MessageEntity>? messages;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? v;

  const AnnouncementEntity({
    this.id,
    this.classId,
    this.slotId,
    this.title,
    this.mainImage,
    this.senderName,
    this.senderImage,
    this.messages,
    this.createdAt,
    this.updatedAt,
    this.v,
  });

  @override
  List<Object?> get props => [
        id,
        classId,
        slotId,
        title,
        mainImage,
        senderName,
        senderImage,
        messages,
        createdAt,
        updatedAt,
        v,
      ];
}
