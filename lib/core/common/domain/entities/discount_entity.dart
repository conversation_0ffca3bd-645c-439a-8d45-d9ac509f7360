class DiscountEntity {
    String? id;
    String? code;
    int? discountPercentage;
    String? discountType;
    DateTime? validUntil;
    bool? userSpecific;
    List<String>? allowedUsers;
    bool? isActive;
    int? maxUsage;
    int? usageCount;
    DateTime? createdAt;
    DateTime? updatedAt;
    int? v;

    DiscountEntity({
        this.id,
        this.code,
        this.discountPercentage,
        this.discountType,
        this.validUntil,
        this.userSpecific,
        this.allowedUsers,
        this.isActive,
        this.maxUsage,
        this.usageCount,
        this.createdAt,
        this.updatedAt,
        this.v,
    });
}