import 'package:equatable/equatable.dart';

class CampaignEntity extends Equatable {
  final String? id;
  final String? title;
  final String? subtitle;
  final String? description;
  final String? imageUrl;
  final String? backgroundColor;
  final String? textColor;
  final int? discountPercentage;
  final String? discountCode;
  final DateTime? validFrom;
  final DateTime? validUntil;
  final bool? isActive;
  final int? priority;
  final String? targetAudience;
  final String? actionType;
  final Map<String, dynamic>? actionData;
  final int? clickCount;
  final int? impressionCount;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const CampaignEntity({
    this.id,
    this.title,
    this.subtitle,
    this.description,
    this.imageUrl,
    this.backgroundColor,
    this.textColor,
    this.discountPercentage,
    this.discountCode,
    this.validFrom,
    this.validUntil,
    this.isActive,
    this.priority,
    this.targetAudience,
    this.actionType,
    this.actionData,
    this.clickCount,
    this.impressionCount,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        subtitle,
        description,
        imageUrl,
        backgroundColor,
        textColor,
        discountPercentage,
        discountCode,
        validFrom,
        validUntil,
        isActive,
        priority,
        targetAudience,
        actionType,
        actionData,
        clickCount,
        impressionCount,
        createdAt,
        updatedAt,
      ];
}
