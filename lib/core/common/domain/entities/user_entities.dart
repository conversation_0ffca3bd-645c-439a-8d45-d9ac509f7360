class User {
  String? email;
  String? token;
  String? type;
  bool? isComplete;
  String? referal;
  String? phone;
  String? location;
  String? nickname;
  String? fullname;
  Image? image;

  User(
      {this.token,
      this.email,
      this.fullname,
      this.nickname,
      this.isComplete,
      this.location,
      this.phone,
      this.referal,
      this.type,
      this.image});
}

class Image {
  String? url;
  String? contentType;

  Image({
    this.url,
    this.contentType,
  });
}

class AuthToken {
  final bool auth;

  AuthToken({required this.auth});
}
