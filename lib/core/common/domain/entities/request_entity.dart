import 'package:class_z/core/imports.dart';

class RequestEntity extends Equatable {
  final String? id;
  final CenterData? centerId;
  final CoachModel? coachId;
  final String? status;
  final DateTime? dateRequested;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? v;

  const RequestEntity({
    this.id,
    this.centerId,
    this.coachId,
    this.status,
    this.dateRequested,
    this.createdAt,
    this.updatedAt,
    this.v,
  });

  @override
  List<Object?> get props => [
        id,
        centerId,
        coachId,
        status,
        dateRequested,
        createdAt,
        updatedAt,
        v,
      ];
}
