import 'package:class_z/core/imports.dart';

class SearchQueryUseCase {
  final SearchRepoDomain _searchRepoDomain;

  SearchQueryUseCase(this._searchRepoDomain);
  
  Future<SearchModel> call(
    String query, 
    int? ageFrom, 
    int? ageTo,
    String? location, 
    String? sortBy, 
    {double? rating, 
     bool? senService,
     bool? isSearchingCoach = false}
  ) async {
    return await _searchRepoDomain.getSearchResult(
      query, 
      ageFrom, 
      ageTo, 
      location, 
      sortBy,
      rating: rating,
      senService: senService,
      isSearchingCoach: isSearchingCoach,
    );
  }
}

class SearchQueryByCategoryUseCase {
  final SearchRepoDomain _searchRepoDomain;
  
  Future<SearchModel> call({
    String? query,
    int? priceMax,
    int? priceMin,
    int? ageFrom,
    int? ageTo,
    String? location,
    String? sortBy,
    double? rating,
    bool? senService
  }) async {
    return await _searchRepoDomain.getCentersByCategory(
      query: query,
      ageFrom: ageFrom,
      ageTo: ageTo,
      location: location,
      priceMax: priceMax,
      priceMin: priceMin,
      sortBy: sortBy,
      rating: rating,
      senService: senService
    );
  }

  SearchQueryByCategoryUseCase(this._searchRepoDomain);
}
