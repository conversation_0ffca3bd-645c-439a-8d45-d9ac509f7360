import 'package:class_z/core/error/failure.dart';
import 'package:class_z/core/common/data/repos/announcement_repo.dart';
import 'package:class_z/core/common/domain/entities/announcement_entity.dart';
import 'package:dartz/dartz.dart';

class PostAnnouncementUseCase {
  final AnnouncementRepoImpl _announcementRepoImpl;

  PostAnnouncementUseCase(this._announcementRepoImpl);

  Future<Either<Failure, bool>> call(Map<String, dynamic> payload) async {
    return await _announcementRepoImpl.postAnnouncement(payload);
  }
}

class GetAnnouncementUseCase {
  final AnnouncementRepoImpl _announcementRepoImpl;

  GetAnnouncementUseCase(this._announcementRepoImpl);
  Future<Either<Failure, AnnouncementEntity?>> call(
      String id, String? type) async {
    return await this._announcementRepoImpl.getAnnouncement(id, type);
  }
}
