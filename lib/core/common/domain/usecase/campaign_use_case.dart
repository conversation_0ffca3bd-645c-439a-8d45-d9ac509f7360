import 'package:class_z/core/error/failure.dart';
import 'package:class_z/core/common/data/repos/campaign_repo.dart';
import 'package:class_z/core/common/domain/entities/campaign_entity.dart';
import 'package:dartz/dartz.dart';

class GetActiveCampaignsUseCase {
  final CampaignRepoImpl _campaignRepoImpl;

  GetActiveCampaignsUseCase(this._campaignRepoImpl);

  Future<Either<Failure, List<CampaignEntity>>> call() async {
    return await _campaignRepoImpl.getActiveCampaigns();
  }
}

class RecordCampaignClickUseCase {
  final CampaignRepoImpl _campaignRepoImpl;

  RecordCampaignClickUseCase(this._campaignRepoImpl);

  Future<Either<Failure, bool>> call(String campaignId) async {
    return await _campaignRepoImpl.recordCampaignClick(campaignId);
  }
}
