import 'package:class_z/core/imports.dart';

import 'package:dartz/dartz.dart';

class GetBranchesByOwnerIdUseCase {
  final OwnerRepoDomain _ownerRepoDomain;

  GetBranchesByOwnerIdUseCase(this._ownerRepoDomain);
  Future<List<CenterData>> call(String ownerId) async {
    return await _ownerRepoDomain.getBranchesByOwner(ownerId);
  }
}

class DeleteBranchUseCase {
  final OwnerRepoDomain _ownerRepoDomain;

  DeleteBranchUseCase(this._ownerRepoDomain);
  Future<bool> call({required String branchId}) async {
    return await _ownerRepoDomain.deleteBranch(branchId: branchId);
  }
}

class UpdateBranchUseCase {
  final OwnerRepoDomain _ownerRepoDomain;

  UpdateBranchUseCase(this._ownerRepoDomain);
  Future<bool> call(
      {required String branchId, required Map<String, dynamic> data}) async {
    return await _ownerRepoDomain.updateBranch(branchId: branchId, data: data);
  }
}

class UpdateOwnerUseCase {
  final OwnerRepoDomain _ownerRepoDomain;

  UpdateOwnerUseCase(this._ownerRepoDomain);
  Future<bool> call(
      {required String ownerId, required Map<String, dynamic> data}) async {
    return await _ownerRepoDomain.updateOwner(ownerId: ownerId, data: data);
  }
}

class RequestCoachToJoinUseCase extends UseCase<bool, AssignCoachEntity> {
  final OwnerRepoImpl _ownerRepoImpl;

  RequestCoachToJoinUseCase(this._ownerRepoImpl);

  @override
  Future<Either<Failure, bool>> call(AssignCoachEntity params) {
    return _ownerRepoImpl.requestCoachToJoin(
        centerId: params.centerId, coachId: params.coachId, type: params.type);
  }
}

class RemoveCoachUseCase extends UseCase<bool, AssignCoachEntity> {
  final OwnerRepoImpl _ownerRepoImpl;
  RemoveCoachUseCase(this._ownerRepoImpl);
  Future<Either<Failure, bool>> call(AssignCoachEntity params) {
    return _ownerRepoImpl.removeCoach(
        centerId: params.centerId, coachId: params.coachId, type: params.type);
  }
}

// class RequestManagerToJoinUseCase extends UseCase<bool, AssignCoachEntity> {
//   final OwnerRepoImpl _ownerRepoImpl;
//   RequestManagerToJoinUseCase(this._ownerRepoImpl);
//   Future<Either<Failure, bool>> call(AssignCoachEntity params) {
//     return _ownerRepoImpl.requestManagerToJoin(
//         centerId: params.centerId, managerId: params.coachId);
//   }
// }

// class RemoveManagerUseCase extends UseCase<bool, AssignCoachEntity> {
//   final OwnerRepoImpl _ownerRepoImpl;
//   RemoveManagerUseCase(this._ownerRepoImpl);
//   Future<Either<Failure, bool>> call(AssignCoachEntity params) {
//     return _ownerRepoImpl.removeManager(
//         centerId: params.centerId, managerId: params.coachId);
//   }
// }
