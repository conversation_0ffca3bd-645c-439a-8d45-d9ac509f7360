import 'package:class_z/core/error/failure.dart';
import 'package:class_z/core/common/domain/repo_domain/request_repo_domain.dart';
import 'package:dartz/dartz.dart';

class CancelJoinRequestUseCase {
  final RequestRepository repo;

  CancelJoinRequestUseCase(this.repo);

  Future<Either<Failure, bool>> call(String coachId, String centerId) {
    return repo.cancelJoinRequest(coachId, centerId);
  }
}
