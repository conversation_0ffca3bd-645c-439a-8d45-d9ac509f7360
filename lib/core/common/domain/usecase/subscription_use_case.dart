import 'package:class_z/core/common/data/models/subscriptionModel.dart';
import 'package:class_z/core/common/domain/repo_domain/subscription_repo_domain.dart';

class GetSubscriptionUseCase {
  final SubscriptionRepoDomain subscriptionRepoDomain;

  GetSubscriptionUseCase({required this.subscriptionRepoDomain});
  Future<SubscriptionModel> call() async {
    return await this.subscriptionRepoDomain.getSubscription();
  }
}

class CancelSubscriptionUseCase {
  final SubscriptionRepoDomain subscriptionRepoDomain;

  CancelSubscriptionUseCase({required this.subscriptionRepoDomain});
  Future<bool> call() async {
    return await this.subscriptionRepoDomain.cancelSubscription();
  }
}
