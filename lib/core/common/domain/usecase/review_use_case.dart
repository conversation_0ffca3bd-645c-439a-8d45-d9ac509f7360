import 'package:class_z/core/common/data/models/reviewModel.dart';
import 'package:class_z/core/common/domain/repo_domain/review_repo_domain.dart';
import 'package:class_z/core/error/failure.dart';
import 'package:dartz/dartz.dart';

class ReviewUseCase {
  final ReviewRepoDomain reviewRepoDomain;

  ReviewUseCase({required this.reviewRepoDomain});
  Future<List<ReviewModel>> getReviewById(
      {required String id, required String type}) async {
    return await reviewRepoDomain.getReviewById(id: id, type: type);
  }

  Future<bool> postReview({required Map<String, dynamic> payload}) async {
    return await reviewRepoDomain.postReview(payload: payload);
  }

  Future<ReviewModel> getReviewByIdandClass(
      {required String classId,
      required String id,
      required String type}) async {
    return await reviewRepoDomain.getReviewByIdandClass(
        id: id, type: type, classId: classId);
  }
}

class GetMomentsUseCase {
  final ReviewRepoDomain reviewRepoDomain;

  GetMomentsUseCase({required this.reviewRepoDomain});
  Future<List<ReviewModel>> call({required String childId}) async {
    return await reviewRepoDomain.getMoments(childId: childId);
  }
}

class PostReviewByParentUseCase {
  final ReviewRepoDomain reviewRepoDomain;

  PostReviewByParentUseCase({required this.reviewRepoDomain});
  Future<Either<Failure, bool>> call(
      {required Map<String, dynamic> payload}) async {
    return await reviewRepoDomain.postReviewByParent(payload: payload);
  }
}
