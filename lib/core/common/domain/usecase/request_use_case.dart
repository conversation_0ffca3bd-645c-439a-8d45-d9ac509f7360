import 'package:class_z/core/error/failure.dart';
import 'package:class_z/core/common/domain/entities/request_entity.dart';
import 'package:class_z/core/common/domain/repo_domain/request_repo_domain.dart';
import 'package:dartz/dartz.dart';

class SendJoinRequestUseCase {
  final RequestRepository repo;
  SendJoinRequestUseCase(this.repo);

  Future<Either<Failure, bool>> call(String coachId, String centerId) {
    return repo.sendJoinRequest(coachId, centerId);
  }
}

class UpdateRequestStatusUseCase {
  final RequestRepository repo;
  UpdateRequestStatusUseCase(this.repo);

  Future<Either<Failure, bool>> call(String requestId, String status) {
    return repo.updateStatus(requestId, status);
  }
}

class GetRequestsByCenterUseCase {
  final RequestRepository repo;

  GetRequestsByCenterUseCase(this.repo);
  Future<Either<Failure, List<RequestEntity>?>> call(String centerId) {
    return repo.getRequestByCenter(centerId);
  }
}

class GetRequestsByCoachUseCase {
  final RequestRepository repo;

  GetRequestsByCoachUseCase(this.repo);
  Future<Either<Failure, List<RequestEntity>?>> call(String coachId) {
    return repo.getRequestByCoach(coachId);
  }
}

class CheckExistingRequestUseCase {
  final RequestRepository repo;

  CheckExistingRequestUseCase(this.repo);
  Future<Either<Failure, Map<String, dynamic>>> call(
      String coachId, String centerId) {
    return repo.checkExistingRequest(coachId, centerId);
  }
}
