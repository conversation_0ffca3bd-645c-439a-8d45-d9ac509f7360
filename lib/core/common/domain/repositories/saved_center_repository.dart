import 'package:class_z/core/imports.dart';

abstract class SavedCenterRepository {
  Future<bool> saveCenter({
    required String parentId,
    required String centerId,
  });

  Future<bool> unsaveCenter({
    required String parentId,
    required String centerId,
  });

  Future<List<CenterData>> getSavedCenters({
    required String parentId,
  });

  Future<bool> isCenterSaved({
    required String parentId,
    required String centerId,
  });

  // Batch check multiple centers at once (NEW EFFICIENT METHOD)
  Future<Map<String, bool>> batchCheckSavedCenters({
    required String parentId,
    required List<String> centerIds,
  });
}
