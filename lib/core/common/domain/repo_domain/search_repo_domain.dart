import 'package:class_z/core/imports.dart';

abstract class SearchRepoDomain {
  Future<SearchModel> getSearchResult(
      String query, int? ageFrom, int? ageTo, String? location, String? sortBy, {double? rating, bool? senService,bool? isSearchingCoach});
      
  Future<SearchModel> getCentersByCategory({
    String? query,
    int? priceMax,
    int? priceMin,
    int? ageFrom,
    int? ageTo,
    String? location,
    String? sortBy,
    double? rating,
    bool? senService
  });
  
  Future<SearchModel> getNearbyCenters({
    required double longitude,
    required double latitude,
    double? maxDistance,
    int? priceMin,
    int? priceMax,
    int? ageFrom,
    int? ageTo,
    String? sortBy,
    double? rating,
    bool? senService
  });
}
