import 'package:class_z/core/imports.dart';
import 'package:dartz/dartz.dart';

abstract class OwnerRepoDomain {
  Future<List<CenterData>> getBranchesByOwner(String ownerId);
  Future<bool> updateOwner(
      {required String ownerId, required Map<String, dynamic> data});
  Future<bool> deleteBranch({required String branchId});
  Future<bool> updateBranch(
      {required String branchId, required Map<String, dynamic> data});
  Future<Either<Failure, bool>> requestCoachToJoin(
      {required String centerId,
      required String coachId,
      required String type});
  Future<Either<Failure, bool>> removeCoach(
      {required String centerId,
      required String coachId,
      required String type});
  // Future<Either<Failure, bool>> requestManagerToJoin(
  //     {required String centerId, required String managerId});
  // Future<Either<Failure, bool>> removeManager(
  //     {required String centerId, required String managerId});
}
