// lib/shared/domain/repositories/shared_repository.dart
import 'package:class_z/core/common/data/models/user_model.dart';
import 'package:class_z/features/roles/coach/data/models/coach_model.dart';
abstract class SharedRepositoryDomain {
  Future<void> storeUser(Data data);
  Data? getUser();
  Future<void> storeCoach(CoachModel coach);
  CoachModel? getCoach();
  Future<void> deleteUser();
  Future<void> deleteCoach();
}
