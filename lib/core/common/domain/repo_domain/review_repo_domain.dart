import 'package:class_z/core/common/data/models/reviewModel.dart';
import 'package:dartz/dartz.dart';
import 'package:class_z/core/error/failure.dart';

abstract class ReviewRepoDomain {
  Future<bool> postReview({required Map<String, dynamic> payload});
  Future<Either<Failure, bool>> postReviewByParent(
      {required Map<String, dynamic> payload});
  Future<List<ReviewModel>> getReviewById(
      {required String id, required String type});
  Future<ReviewModel> getReviewByIdandClass(
      {required String id, required String type, required String classId});
  Future<List<ReviewModel>> getMoments({required String childId});
}
