import 'package:class_z/core/error/failure.dart';
import 'package:class_z/core/common/domain/entities/request_entity.dart';
import 'package:dartz/dartz.dart';

abstract class RequestRepository {
  Future<Either<Failure, bool>> sendJoinRequest(
      String coachId, String centerId);
  Future<Either<Failure, bool>> updateStatus(String requestId, String status);
  Future<Either<Failure, List<RequestEntity>?>> getRequestByCenter(
      String centerId);
  Future<Either<Failure, List<RequestEntity>?>> getRequestByCoach(
      String coachId);
  Future<Either<Failure, bool>> cancelJoinRequest(
      String coachId, String centerId);
  Future<Either<Failure, Map<String, dynamic>>> checkExistingRequest(
      String coachId, String centerId);
}
