import 'package:class_z/core/imports.dart';
import 'package:class_z/features/chats/data/repositories/chat_local_repository.dart';

/// Service to handle badge counts for notifications and messages
class BadgeCountService {
  // Singleton instance
  static final BadgeCountService _instance = BadgeCountService._internal();
  factory BadgeCountService() => _instance;
  BadgeCountService._internal();

  // Notification counts cache
  int _notificationCount = 0;
  int get notificationCount => _notificationCount;

  // Message counts cache
  int _messageCount = 0;
  int get messageCount => _messageCount;

  // Stream controllers to notify listeners of changes
  final _notificationCountController = StreamController<int>.broadcast();
  Stream<int> get notificationCountStream =>
      _notificationCountController.stream;

  final _messageCountController = StreamController<int>.broadcast();
  Stream<int> get messageCountStream => _messageCountController.stream;

  /// Initialize the service by fetching initial counts
  Future<void> initialize(String userId) async {
    await Future.wait([
      refreshNotificationCount(userId),
      refreshMessageCount(userId),
    ]);
  }

  /// Refresh the notification count for a user
  Future<void> refreshNotificationCount(String userId) async {
    try {
      final notificationBloc = locator<NotificationBloc>();

      // Force a refresh of notifications to get the latest count
      final result = await locator<GetNotifications>()(userId);

      result.fold(
        (failure) {
          print('Error getting notification count: ${failure.message}');
          // Keep existing count on error
        },
        (notifications) {
          // Count only unread notifications
          final unreadCount =
              notifications.where((n) => n.isRead == false).length;
          _notificationCount = unreadCount;
          _notificationCountController.add(unreadCount);
          print('Updated notification count: $unreadCount');
        },
      );
    } catch (e) {
      print('Error refreshing notification count: $e');
    }
  }

  /// Refresh the message count for a user
  Future<void> refreshMessageCount(String userId) async {
    if (userId.isEmpty) {
      print('Cannot refresh message count: userId is empty');
      return;
    }

    try {
      // Get the last messages
      final lastMessages = await locator<GetLastMessages>()(userId);

      // If we get null or empty lastMessages from the server, don't automatically reset to 0
      // Instead, check if we have local messages in storage first
      if (lastMessages.isEmpty) {
        try {
          // Check if there are any messages in local storage
          final localRepo = GetIt.instance.get<ChatLocalRepository>();
          final hasLocalMessages = await localRepo.hasAnyMessages(userId);

          if (hasLocalMessages) {
            // If we have local messages, keep the existing count or set to at least 1
            if (_messageCount == 0) {
              _messageCount = 1;
              _messageCountController.add(1);
              print(
                  'Server returned no messages but local messages exist, keeping count at 1');
            }
            return;
          } else {
            // Only set to 0 if we have no local messages either
            _messageCount = 0;
            _messageCountController.add(0);
            print('No messages found (server or local), setting count to 0');
            return;
          }
        } catch (e) {
          print('Error checking local messages: $e');
          // Don't change the count if we can't check local storage
          return;
        }
      }

      // For now, we'll use a simpler implementation
      // Count conversations with the most recent 24 hours as "unread"
      int unreadCount = 0;
      final now = DateTime.now();

      try {
        unreadCount = lastMessages.where((m) {
          try {
            // Check if the last message time is within the last 24 hours
            if (m.lastMessageTime == null) return false;

            final diff = now.difference(m.lastMessageTime!);
            return diff.inHours <
                24; // Consider messages from last 24h as unread
          } catch (e) {
            print('Error processing individual message timestamp: $e');
            return false;
          }
        }).length;
      } catch (e) {
        print('Error counting unread messages: $e');
        // Fallback to just counting total conversations
        unreadCount = lastMessages.length;
      }

      _messageCount = unreadCount;
      _messageCountController.add(unreadCount);
      print('Updated message count: $unreadCount');
    } catch (e) {
      print('Error refreshing message count: $e');
      // On error, don't update the count (keep existing)
    }
  }

  /// Mark a notification as read and update the count
  Future<void> markNotificationAsRead(
      String notificationId, String userId) async {
    try {
      final result = await locator<ReadNotification>()(notificationId);

      result.fold(
        (failure) {
          print('Error marking notification as read: ${failure.message}');
        },
        (success) {
          // Refresh the count after marking as read
          refreshNotificationCount(userId);
        },
      );
    } catch (e) {
      print('Error marking notification as read: $e');
    }
  }

  /// Manually set notification count (useful for testing or when we already know the count)
  void setNotificationCount(int count) {
    _notificationCount = count;
    _notificationCountController.add(count);
  }

  /// Manually set message count (useful for testing or when we already know the count)
  void setMessageCount(int count) {
    _messageCount = count;
    _messageCountController.add(count);
  }

  /// Dispose the stream controllers
  void dispose() {
    _notificationCountController.close();
    _messageCountController.close();
  }
}
