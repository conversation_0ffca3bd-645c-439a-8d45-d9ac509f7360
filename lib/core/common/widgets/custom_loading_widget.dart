import 'package:class_z/core/common/widgets/three_dots_loading.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomLoadingWidget extends StatelessWidget {
  const CustomLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 30.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const ThreeDotsLoading(
            dotColor: Color(0xFF3F91F1),
            dotSize: 20.0,
            spacing: 10.0,
          ),
          SizedBox(height: 40.h),
          Text(
            'A few more seconds...',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'SF Pro Display',
              fontWeight: FontWeight.w600,
              fontSize: 25,
              color: const Color(0xFF424242),
              letterSpacing: 0.5,
            ),
          ),
          SizedBox(height: 10.h),
          Text(
            'ClassZ performance reports, provided after each class, are thoughtfully crafted by clinical professionals.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'SF Pro Display',
              fontWeight: FontWeight.w300,
              fontSize: 15,
              color: const Color(0xFF898787),
              letterSpacing: 0.3,
            ),
          ),
          SizedBox(height: 20.h),
          const CircularProgressIndicator(),
        ],
      ),
    );
  }
}
