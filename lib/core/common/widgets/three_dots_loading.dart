import 'package:flutter/material.dart';

class ThreeDotsLoading extends StatefulWidget {
  const ThreeDotsLoading({
    super.key,
    this.dotColor = Colors.blue,
    this.dotSize = 15.0,
    this.spacing = 5.0,
  });

  final Color dotColor;
  final double dotSize;
  final double spacing;

  @override
  State<ThreeDotsLoading> createState() => _ThreeDotsLoadingState();
}

class _ThreeDotsLoadingState extends State<ThreeDotsLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            final double t =
                ((_controller.value * 3).floor() == index) ? 1.0 : 0.5;
            return Transform.scale(
              scale: t,
              child: child,
            );
          },
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: widget.spacing),
            width: widget.dotSize,
            height: widget.dotSize,
            decoration: BoxDecoration(
              color: widget.dotColor,
              shape: BoxShape.circle,
            ),
          ),
        );
      }),
    );
  }
}
