import 'package:class_z/core/imports.dart';

/// A utility class for testing the review functionality
class ReviewTestUtil {
  /// Shows a review popup with mock data for testing purposes
  static void showReviewPopupForTesting(BuildContext context) {
    try {
      // Create a mock main image for the center
      final mockMainImage = BusinessCertificate(
        url: 'https://via.placeholder.com/150',
        contentType: 'image/jpeg',
      );

      // Create mock image list
      final List<CenterImage> mockImages = [
        CenterImage(
          url: 'https://via.placeholder.com/150',
          contentType: 'image/jpeg',
        )
      ];

      // Create mock data for a class
      final mockClassModel = ClassModel(
        id: 'test-class-id',
        classProviding: 'Test Class',
        level: 'Intermediate',
        mainImage: mockMainImage,
        center: CenterData(
          id: 'test-center-id',
          displayName: 'Test Center',
          description: 'A test center for review testing',
          email: '<EMAIL>',
          mainImage: mockMainImage,
          images: mockImages,
        ),
        coach: Coach<PERSON>ode<PERSON>(
          id: 'test-coach-id',
          displayName: 'Test Coach',
          description: 'A test coach for review testing',
          email: '<EMAIL>',
          mainImage: mockMainImage,
          images: mockImages,
        ),
      );

      // Get current user ID
      final userData = locator<SharedRepository>().getUserData();
      final userId = userData?.data?.parent?.id ?? 'test-user-id';

      // Show the review popup
      showCombinedCourseReviewBottomSheet(
        context: context,
        classModel: mockClassModel,
        isHalfCourse: true,
        reviewerId: userId,
        classId: mockClassModel.id ?? '',
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Review popup shown with mock data'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error showing review popup: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Tests submitting a review
  static void testReviewSubmission(BuildContext context) {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text("Submitting test reviews..."),
              ],
            ),
          );
        },
      );

      // Get current user ID
      final userData = locator<SharedRepository>().getUserData();
      final userId = userData?.data?.parent?.id ?? 'test-user-id';

      // Create test payloads for center review
      final centerReviewPayload = {
        "reviewerId": userId,
        "reviewerType": "User",
        "revieweeId": "test-center-id",
        "revieweeType": "Center",
        "classId": "test-class-id",
        "rating": 4.0,
        "title": "Mid-Course Review",
        "comment": "This is a test review for the center",
        "date": DateTime.now().toString(),
      };

      // Create test payloads for coach review
      final coachReviewPayload = {
        "reviewerId": userId,
        "reviewerType": "User",
        "revieweeId": "test-coach-id",
        "revieweeType": "Coach",
        "classId": "test-class-id",
        "rating": 5.0,
        "title": "Mid-Course Review",
        "comment": "This is a test review for the coach",
        "date": DateTime.now().toString(),
      };

      // Submit center review
      BlocProvider.of<ReviewBloc>(context).add(
        PostReviewEvent(payload: centerReviewPayload),
      );

      // Wait a bit then submit coach review
      Future.delayed(Duration(seconds: 2), () {
        BlocProvider.of<ReviewBloc>(context).add(
          PostReviewEvent(payload: coachReviewPayload),
        );

        // Close loading dialog
        Navigator.pop(context);

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Test reviews submitted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      });
    } catch (e) {
      // Close loading dialog if open
      Navigator.pop(context);

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error in test review submission: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Tests retrieving reviews
  static void testReviewRetrieval(BuildContext context) {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 20),
                Text("Retrieving test reviews..."),
              ],
            ),
          );
        },
      );

      // Test retrieving center reviews
      BlocProvider.of<ReviewBloc>(context).add(
        GetReviewByIdEvent(
          id: 'test-center-id',
          type: 'Center',
        ),
      );

      // Wait a bit then get coach reviews
      Future.delayed(Duration(seconds: 2), () {
        BlocProvider.of<ReviewBloc>(context).add(
          GetReviewByIdEvent(
            id: 'test-coach-id',
            type: 'Coach',
          ),
        );

        // Close loading dialog
        Navigator.pop(context);

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Test reviews retrieved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      });
    } catch (e) {
      // Close loading dialog if open
      Navigator.pop(context);

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error in test review retrieval: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
