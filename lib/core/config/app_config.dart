/// AppConfig class to store application-wide configuration values
class AppConfig {
  /// Private constructor to prevent instantiation
  AppConfig._();

  /// Price per ZCoin in HKD (25 HKD = 1 Zcoin)
  static int? _pricePerZCoinHKD;

  /// Get the current price per ZCoin in HKD
  static int? get pricePerZCoinHKD => _pricePerZCoinHKD;

  /// Set the price per ZCoin in HKD
  static set pricePerZCoinHKD(int? value) {
    _pricePerZCoinHKD = value;
  }

  /// Initialize the configuration with default values
  static void initialize() {
    _pricePerZCoinHKD = 25; // Default price per ZCoin is 25 HKD
    print("AppConfig initialized: _pricePerZCoinHKD = $_pricePerZCoinHKD");
  }

  /// Convert HKD amount to Zcoin with rounding up from 3 HKD
  /// For example: 1-25 HKD = 1 Zcoin, 26-50 HKD = 2 Zcoin, etc.
  /// Rounds up if remainder is 3 HKD or more
  static int convertHKDToZcoin(double hkdAmount) {
    if (hkdAmount <= 0) return 0;

    // Ensure _pricePerZCoinHKD is initialized
    final pricePerZcoin = _pricePerZCoinHKD ?? 25;

    print(
        "Converting HKD $hkdAmount to Zcoin (rate: 1 Zcoin = $pricePerZcoin HKD)");

    final baseZcoins = (hkdAmount / pricePerZcoin).floor();
    final remainder = hkdAmount % pricePerZcoin;

    // Round up if remainder is 3 HKD or more
    final result =
        remainder >= 3 ? baseZcoins + 1 : (baseZcoins == 0 ? 1 : baseZcoins);

    print(
        "Conversion result: $hkdAmount HKD → $result Zcoin (base: $baseZcoins, remainder: $remainder)");

    return result;
  }

  /// Convert Zcoin amount to HKD
  static double convertZcoinToHKD(int zcoinAmount) {
    return zcoinAmount * (_pricePerZCoinHKD ?? 25).toDouble();
  }

  /// Format HKD amount for display
  static String formatHKDAmount(double amount) {
    return "HKD ${amount.toStringAsFixed(0)}";
  }

  /// Format Zcoin amount for display
  static String formatZcoinAmount(int amount) {
    return "$amount Zcoin${amount == 1 ? '' : 's'}";
  }

  /// Load configuration from the server
  static Future<void> loadFromServer() async {
    // This method would fetch configuration values from the server
    // For now, we'll just use the default values
    initialize();
  }
}
