import 'package:flutter/material.dart';

// Reference size from Figma design (e.g., 432x932)
const double FIGMA_DESIGN_WIDTH = 432.0;
const double FIGMA_DESIGN_HEIGHT = 932.0;

class SizeConfig {
  static MediaQueryData? _mediaQueryData;
  static double screenWidth = FIGMA_DESIGN_WIDTH; // Default value
  static double screenHeight = FIGMA_DESIGN_HEIGHT; // Default value
  static double defaultSize = 10.0; // Default value
  static Orientation orientation = Orientation.portrait; // Default value

  /// Initialize sizing configuration
  static void init(BuildContext context) {
    try {
      _mediaQueryData = MediaQuery.of(context);
      screenWidth = _mediaQueryData?.size.width ?? FIGMA_DESIGN_WIDTH;
      screenHeight = _mediaQueryData?.size.height ?? FIGMA_DESIGN_HEIGHT;
      orientation = _mediaQueryData?.orientation ?? Orientation.portrait;

      // Calculate default size for responsive scaling
      defaultSize = orientation == Orientation.landscape
          ? screenHeight * 0.024
          : screenWidth * 0.024;
    } catch (e) {
      print('Error initializing SizeConfig: $e');
      // Keep using default values if initialization fails
    }
  }

  /// Check if configuration is initialized
  static bool get isInitialized {
    return _mediaQueryData != null;
  }

  // Get proportionate height according to device screen height
  static double getProportionateScreenHeight(double inputHeight) {
    double screenHeight = SizeConfig.screenHeight;
    // 812 is the design height for iPhone X
    return (inputHeight / FIGMA_DESIGN_HEIGHT) * screenHeight;
  }

  // Get proportionate width according to device screen width
  static double getProportionateScreenWidth(double inputWidth) {
    double screenWidth = SizeConfig.screenWidth;
    // 375 is the design width for iPhone X
    return (inputWidth / FIGMA_DESIGN_WIDTH) * screenWidth;
  }
}

extension ResponsiveSizeExtension on num {
  // Scaling width based on the design width
  double get w => (this / FIGMA_DESIGN_WIDTH) * SizeConfig.screenWidth;

  // Scaling height based on the design height
  double get h => (this / FIGMA_DESIGN_HEIGHT) * SizeConfig.screenHeight;

  // Scaling font size based on the design width
  double get sp => (this / FIGMA_DESIGN_WIDTH) * SizeConfig.screenWidth;

  // Responsive rounding (combined width and height scaling)
  double get r => (this / FIGMA_DESIGN_WIDTH) * SizeConfig.screenWidth;
}
