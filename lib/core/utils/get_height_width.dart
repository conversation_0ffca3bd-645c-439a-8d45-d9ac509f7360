import 'package:flutter/material.dart';

double getHeight({required BuildContext context}) {
  return MediaQuery.of(context).size.height;
}

double getawidth({required BuildContext context}) {
  return MediaQuery.of(context).size.width;
}

double heightRatio({required double height}) {
  return height / 932;
}

double widthRatio({required double width}) {
  return width / 430;
}

class ScreenSizeProvider with ChangeNotifier {
  double _screenHeight = 0;
  double _screenWidth = 0;

  double get screenHeight => _screenHeight;
  double get screenWidth => _screenWidth;

  void setScreenSize(double width, double height) {
    _screenWidth = width;
    _screenHeight = height;
    notifyListeners();
  }
}
