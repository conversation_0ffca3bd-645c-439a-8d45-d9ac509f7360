class ApiUtils {
  static String buildEventUrl({
    required String device,
    required String filterType,
    required String filterValue,
    String? date,
  }) {
    Uri uri = Uri.parse("$device/api/events");

    Map<String, String> queryParams = {};

    if (date != null) queryParams["date"] = date;
    if (filterType.isNotEmpty && filterValue.isNotEmpty) {
      queryParams[filterType] = filterValue;
    }

    return uri.replace(queryParameters: queryParams).toString();
  }
}
