import 'package:flutter/material.dart';

class LoadingManager {
  static bool _isLoading = false;
  static OverlayEntry? _overlayEntry;

  static void show(BuildContext context) {
    if (_isLoading) return;

    _isLoading = true;
    _overlayEntry = OverlayEntry(
      builder: (context) => Container(
        color: Colors.black.withOpacity(0.5),
        child: const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  static void hide() {
    if (!_isLoading) return;

    _overlayEntry?.remove();
    _overlayEntry = null;
    _isLoading = false;
  }

  static bool get isLoading => _isLoading;
}
