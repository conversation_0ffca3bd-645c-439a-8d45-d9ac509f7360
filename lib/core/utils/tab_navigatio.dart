// lib/utils/tab_navigation_util.dart

import 'package:class_z/routes/navigator.dart';
import 'package:class_z/routes/routes.dart';

void handleTabIndexChanged(int index) {
  // Handle tab changes if necessary, e.g., navigate to a different route
  switch (index) {
    case 0:
      NavigatorService.pushNamedAndRemoveUntil(AppRoutes.userMain);
      break;
    case 1:
      NavigatorService.pushNamedAndRemoveUntil(AppRoutes.search);
      break;
    case 2:
      NavigatorService.pushNamedAndRemoveUntil(AppRoutes.middleProfileSwitch);
      break;
    case 3:
      NavigatorService.pushNamedAndRemoveUntil(AppRoutes.timeTableUp);
      break;
    case 4:
      NavigatorService.pushNamedAndRemoveUntil(
          AppRoutes.progressFeedbackUserDashboard);
      break;
    default:
      break;
  }
}
