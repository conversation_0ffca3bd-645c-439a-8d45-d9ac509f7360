import 'package:class_z/core/imports.dart';

/// Helper class for authentication-related functionality
class AuthHelper {
  final SharedRepository _sharedRepository;

  AuthHelper(this._sharedRepository);

  /// Validates the current token and returns true if valid, false otherwise
  bool validateCurrentToken() {
    final token = _sharedRepository.getToken();
    if (token == null || token.isEmpty) {
      print('No token available');
      return false;
    }

    return !isTokenExpired(token);
  }

  /// Check if a token is expired
  bool isTokenExpired(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) {
        print('Invalid token format');
        return true;
      }

      final payload = json
          .decode(utf8.decode(base64Url.decode(base64Url.normalize(parts[1]))));

      // Check if token has expiration claim
      if (payload['exp'] != null) {
        final expiry =
            DateTime.fromMillisecondsSinceEpoch(payload['exp'] * 1000);
        final isExpired = DateTime.now().isAfter(expiry);
        if (isExpired) {
          print('Token expired at: $expiry');
        }
        return isExpired;
      }

      // If token has iat (issued at) claim, check if it's older than 24 hours
      // This is a fallback if exp is not present
      if (payload['iat'] != null) {
        final issuedAt =
            DateTime.fromMillisecondsSinceEpoch(payload['iat'] * 1000);
        final difference = DateTime.now().difference(issuedAt).inHours;
        final isExpired = difference > 24;
        if (isExpired) {
          print(
              'Token issued at $issuedAt is over 24 hours old ($difference hours)');
        }
        return isExpired;
      }

      return false;
    } catch (e) {
      print('Error checking token expiration: $e');
      return true; // Assume expired if we can't parse it
    }
  }

  /// Handle authentication error by clearing token and returning to login
  void handleAuthError(BuildContext context) {
    // Clear the invalid token
    _sharedRepository.deleteToken();

    // Show dialog to inform user
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Session Expired'),
          content: Text('Your session has expired. Please log in again.'),
          actions: <Widget>[
            TextButton(
              child: Text('Login'),
              onPressed: () {
                // Clear navigation stack and go to login screen
                Navigator.of(context).popUntil((route) => route.isFirst);
                // Navigate to login screen - use the appropriate route
                Navigator.of(context).pushReplacementNamed('/login');
              },
            ),
          ],
        );
      },
    );
  }
}
