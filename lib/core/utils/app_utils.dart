class Category {
  static List<String> categories = [
    "Music",
    "Art",
    "Sports",
    "Science",
    "Technology",
    "Dance",
    "Math",
    "Language",
    "Coding",
    "Drama",
    "Health & Fitness",
    "Photography",
    "Cooking",
    "Engineering",
    "History",
    "Robotics",
    "Chess",
    "Public Speaking",
    "Writing",
    "Nature & Environment",
  ];
}

// const num FIGMA_DESIGN_WIDTH = 430;
// const num FIGMA_DESIGN_HEIGHT = 932;

// class SizeUtils {
//   static late BoxConstraints boxconstraints;
//   static late Orientation orientation;
//   static late DeviceType deviceType;
//   static late double width;
//   static late double height;
//   static void setScreenSize(
//       BoxConstraints constraints, Orientation currentOrientation) {
//     boxconstraints = constraints;
//     orientation = currentOrientation;
//     if (orientation == Orientation.portrait) {
//       width =
//           boxconstraints.maxWidth.isNonZero(defaultValue: FIGMA_DESIGN_WIDTH);
//       height = boxconstraints.maxHeight.isNonZero();
//     } else {}
//   }
// }

// extension FormatExtension on double {
//   double toDoubleValue({int fractionDigits = 2}) {
//     return double.parse(this.toStringAsFixed(fractionDigits));
//   }

//   double isNonZero({num defaultValue = 0.0}) {
//     return this > 0 ? this : defaultValue.toDouble();
//   }
// }

// extension ResponsiveExtension on num {
//   double get _width => SizeUtils.width;
//   double get h => ((this * _width) / FIGMA_DESIGN_WIDTH);
//   double get sp => ((this * _width) / FIGMA_DESIGN_WIDTH);
// }
