String formatDate(DateTime date) {
  final now = DateTime.now();
  final yesterday = DateTime.now().subtract(Duration(days: 1));

  if (isSameDay(date, now)) {
    return 'Today';
  } else if (isSameDay(date, yesterday)) {
    return 'Yesterday';
  } else {
    // For other dates, show formatted date
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

bool isSameDay(DateTime date1, DateTime date2) {
  return date1.year == date2.year &&
      date1.month == date2.month &&
      date1.day == date2.day;
}
