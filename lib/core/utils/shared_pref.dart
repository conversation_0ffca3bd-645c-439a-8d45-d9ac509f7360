import 'package:shared_preferences/shared_preferences.dart';

class SharedPref {
  static const String _userIdKey = 'user_id';

  static Future<String?> getUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_userIdKey);
    } catch (e) {
      print('Error getting user ID: $e');
      return null;
    }
  }

  static Future<bool> setUserId(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_userIdKey, userId);
    } catch (e) {
      print('Error setting user ID: $e');
      return false;
    }
  }

  static Future<bool> clear() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_userIdKey);
    } catch (e) {
      print('Error clearing user ID: $e');
      return false;
    }
  }
}
