import 'package:class_z/core/common/data/models/user_address_model.dart';
import 'package:class_z/features/roles/center/data/models/center_model.dart';
import 'package:class_z/features/roles/parent/data/models/parent_model.dart';
import 'package:class_z/core/common/data/models/user_model.dart';
import 'package:class_z/features/roles/coach/data/models/coach_model.dart';

import 'package:class_z/features/roles/owner/data/models/ownerModel.dart';
import 'package:class_z/core/common/presentation/widgets/class_schdule.dart';

import 'package:path/path.dart' as path;
import 'package:http_parser/http_parser.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive/hive.dart';
import 'dart:convert' show json, utf8, base64Url;

class SharedRepository {
  final SharedPreferences sharedPreferences;
  late Box<CenterData> _centerBox;
  late Box<OwnerModel> _ownerBox;
  late Box<UserModel> _userBox;

  SharedRepository({required this.sharedPreferences}) {
    _initializeHiveBoxes();
  }

  // Factory constructor to initialize asynchronously
  static Future<SharedRepository> create(
      {required SharedPreferences sharedPreferences}) async {
    final repository = SharedRepository(sharedPreferences: sharedPreferences);
    await repository._initializeHiveBoxes();
    return repository;
  }

  Future<void> _initializeHiveBoxes() async {
    try {
      // Ensure the Hive boxes are opened only once
      _centerBox = await Hive.openBox<CenterData>('centerModelBox');
      _userBox = await Hive.openBox<UserModel>('userModelBox');
    } catch (e) {
      throw Exception("Error initializing Hive boxes: $e");
    }
  }

  List<Map<String, String?>> convertToMap(List<DayTime> weekSchedule) {
    return weekSchedule.map((dayTime) {
      return {
        "day": dayTime.day,
        "openingTime": dayTime.openingTime,
        "closingTime": dayTime.closingTime,
      };
    }).toList();
  }

  MediaType getContentType(String filePath) {
    final extension = path.extension(filePath).toLowerCase();
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return MediaType('image', 'jpeg');
      case '.png':
        return MediaType('image', 'png');
      case '.gif':
        return MediaType('image', 'gif');
      default:
        return MediaType('application', 'octet-stream');
    }
  }

  String? getToken() {
    final token = sharedPreferences.getString('auth-token');
    if (token != null && isTokenExpired(token)) {
      // Return null to trigger re-authentication
      return null;
    }
    return token;
  }

  bool isTokenExpired(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return true;

      final payload = json
          .decode(utf8.decode(base64Url.decode(base64Url.normalize(parts[1]))));

      // Check if token has expiration claim
      if (payload['exp'] != null) {
        final expiry =
            DateTime.fromMillisecondsSinceEpoch(payload['exp'] * 1000);
        return DateTime.now().isAfter(expiry);
      }

      // If token has iat (issued at) claim, check if it's older than 24 hours
      // This is a fallback if exp is not present
      if (payload['iat'] != null) {
        final issuedAt =
            DateTime.fromMillisecondsSinceEpoch(payload['iat'] * 1000);
        return DateTime.now().difference(issuedAt).inHours > 24;
      }

      return false;
    } catch (e) {
      return true; // Assume expired if we can't parse it
    }
  }

  Future<void> storeToken(String token) async {
    await sharedPreferences.setString('auth-token', token);
  }

  Future<void> deleteToken() async {
    await sharedPreferences.remove('auth-token');
  }

  String getUserId() {
    UserModel? userModel = getUserData();
    return (userModel?.data?.parent?.id ?? '');
  }

  String getCenterId() {
    UserModel? userModel = getUserData();
    return (userModel?.data?.center?.id ?? '');
  }

  String getCoachId() {
    UserModel? userModel = getUserData();
    return (userModel?.data?.coach?.id ?? '');
  }

  String getOwnerId() {
    UserModel? userModel = getUserData();
    return (userModel?.data?.owner?.id ?? '');
  }

  String getBaseUserId() {
    UserModel? userModel = getUserData();
    return (userModel?.data?.center?.baseUser ??
        userModel?.data?.owner?.baseUser ??
        userModel?.data?.coach?.baseUser ??
        userModel?.data?.parent?.baseUser ??
        '');
  }

  Future<void> storeUserData(UserModel userModel) async {
    try {
      await _userBox.put('currentUser', userModel);
    } catch (e) {
      throw Exception("Error storing user data: $e");
    }
  }

  UserModel? getUserData() {
    try {
      return _userBox.get('currentUser');
    } catch (e) {
      throw Exception("Error fetching user data: $e");
    }
  }

  Future<void> logout() async {
    try {
      await _userBox.delete('currentUser');
      await deleteToken();
    } catch (e) {
      throw Exception("Error during logout: $e");
    }
  }

  Future<void> storeCenterData(CenterData centerModel) async {
    try {
      await _centerBox.put('currentCenter', centerModel);
    } catch (e) {
      throw Exception("Error storing center data: $e");
    }
  }

  Future<void> storeAllCenterData(List<CenterData> centers) async {
    try {
      for (var center in centers) {
        await _centerBox.add(center);
      }
    } catch (e) {
      throw Exception("Error storing all center data: $e");
    }
  }

  List<CenterData> getCenterModels() {
    try {
      return _centerBox.values.toList();
    } catch (e) {
      throw Exception("Error fetching center models: $e");
    }
  }

  CenterData? getCenterData() {
    try {
      UserModel? user = getUserData();
      return user?.data?.center;
    } catch (e) {
      throw Exception("Error fetching center data: $e");
    }
  }

  ParentData1? getParentData() {
    try {
      UserModel? user = getUserData();
      return user?.data?.parent;
    } catch (e) {
      throw Exception("Error fetching center data: $e");
    }
  }

  OwnerModel? getOwnerData() {
    try {
      UserModel? user = getUserData();
      return user?.data?.owner;
    } catch (e) {
      throw Exception("Error fetching owner data: $e");
    }
  }

  CoachModel? getCoachData() {
    try {
      UserModel? user = getUserData();
      return user?.data?.coach;
    } catch (e) {
      throw Exception("Error fetching center data: $e");
    }
  }

  Future<void> storeType(String type) async {
    await sharedPreferences.setString('type', type);
  }

  String? getType() => sharedPreferences.getString('type');

  Future<void> updateUserData(Data? data) async {
    UserModel? currentUser = _userBox.get('currentUser');
    if (currentUser?.data != null) {
      currentUser?.data = data;
      await _userBox.put('currentUser', currentUser!);
    } else {
      throw Exception("User data not found.");
    }
  }

  Future<void> updateOwnerData(OwnerModel updatedOwner) async {
    try {
      // Fetch current user data
      UserModel? currentUser = _userBox.get('currentUser');

      if (currentUser != null) {
        // Update the owner data inside the currentUser
        currentUser.data?.owner = updatedOwner;

        // Save the updated user model back to the box
        await _userBox.put('currentUser', currentUser);
      } else {
        throw Exception("User data not found.");
      }
    } catch (e) {
      throw Exception("Error updating owner data: $e");
    }
  }

  Future<void> updateCoachData(CoachModel? updatedCoach) async {
    try {
      // Fetch current user data
      UserModel? currentUser = _userBox.get('currentUser');

      if (currentUser != null) {
        if (updatedCoach != null) {
          // Update the owner data inside the currentUser
          currentUser.data?.coach = updatedCoach;

          // Save the updated user model back to the box
          await _userBox.put('currentUser', currentUser);
        }
      } else {
        throw Exception("User data not found.");
      }
    } catch (e) {
      throw Exception("Error updating owner data: $e");
    }
  }

  Future<void> updateCenterData(CenterData? updatedCenter) async {
    try {
      // Fetch current user data
      UserModel? currentUser = _userBox.get('currentUser');

      if (currentUser != null) {
        if (updatedCenter != null) {
          // Update the owner data inside the currentUser
          currentUser.data?.center = updatedCenter;

          // Save the updated user model back to the box
          await _userBox.put('currentUser', currentUser);
        }
      } else {
        throw Exception("User data not found.");
      }
    } catch (e) {
      throw Exception("Error updating owner data: $e");
    }
  }

  Future<void> updateParentData(ParentData1? updatedParent) async {
    try {
      // Fetch current user data
      UserModel? currentUser = _userBox.get('currentUser');

      if (currentUser != null) {
        if (updatedParent != null) {
          // Update the owner data inside the currentUser
          currentUser.data?.parent = updatedParent;

          // Save the updated user model back to the box
          await _userBox.put('currentUser', currentUser);
        }
      } else {
        throw Exception("User data not found.");
      }
    } catch (e) {
      throw Exception("Error updating owner data: $e");
    }
  }

  Future<void> updateAddress(List<UserAddress>? address) async {
    try {
      // Fetch current user data
      UserModel? currentUser = _userBox.get('currentUser');

      if (currentUser != null) {
        if (address != null) {
          // Update the owner data inside the currentUser
          currentUser.data?.parent?.location = address;

          // Save the updated user model back to the box
          await _userBox.put('currentUser', currentUser);
        }
      } else {
        throw Exception("User data not found.");
      }
    } catch (e) {
      throw Exception("Error updating adress data: $e");
    }
  }
}
