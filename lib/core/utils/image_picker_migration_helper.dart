import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:class_z/core/widgets/compressed_image_picker.dart';

/// Helper class to migrate from old ImagePicker to CompressedImagePicker
/// This provides drop-in replacements for common image picking patterns
class ImagePickerMigrationHelper {
  
  /// Replace basic image picking with compression
  /// 
  /// OLD CODE:
  /// ```dart
  /// final XFile? pickedImage = await _picker.pickImage(source: ImageSource.gallery);
  /// if (pickedImage != null) {
  ///   setState(() {
  ///     _image = pickedImage;
  ///   });
  /// }
  /// ```
  /// 
  /// NEW CODE:
  /// ```dart
  /// final result = await ImagePickerMigrationHelper.pickImageWithCompression(
  ///   context: context,
  ///   source: ImageSource.gallery,
  ///   type: ImagePickerType.mainImage,
  /// );
  /// if (result != null) {
  ///   setState(() {
  ///     _image = XFile(result.path);
  ///   });
  /// }
  /// ```
  static Future<File?> pickImageWithCompression({
    required BuildContext context,
    required ImageSource source,
    ImagePickerType type = ImagePickerType.mainImage,
    bool showProgress = true,
    bool showCompressionFeedback = true,
  }) async {
    try {
      CompressedImageResult? result;
      
      if (showProgress) {
        // Show progress dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Processing image...'),
              ],
            ),
          ),
        );

        result = await CompressedImagePicker.pickAndCompressImage(
          source: source,
          type: type,
          onProgress: (message) {
            print('📸 Image processing: $message');
          },
        );

        // Hide progress dialog
        Navigator.of(context).pop();
      } else {
        result = await CompressedImagePicker.pickAndCompressImage(
          source: source,
          type: type,
        );
      }

      if (result.isSuccess && result.file != null) {
        // Show compression feedback if significant compression occurred
        if (showCompressionFeedback && result.compressionRatio > 10) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Image optimized: ${result.compressionInfo}'),
              duration: Duration(seconds: 3),
            ),
          );
        }
        return result.file;
      } else if (result.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result.error!)),
        );
      }
      
      return null;
    } catch (e) {
      // Hide progress dialog if still showing
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error selecting image: $e')),
      );
      return null;
    }
  }

  /// Pick multiple images with compression
  static Future<List<File>> pickMultipleImagesWithCompression({
    required BuildContext context,
    ImagePickerType type = ImagePickerType.gallery,
    bool showProgress = true,
    bool showCompressionFeedback = true,
  }) async {
    try {
      List<CompressedImageResult> results;
      
      if (showProgress) {
        // Show progress dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('Processing images...'),
              ],
            ),
          ),
        );

        results = await CompressedImagePicker.pickAndCompressMultipleImages(
          type: type,
          onProgress: (message) {
            print('📸 Images processing: $message');
          },
        );

        // Hide progress dialog
        Navigator.of(context).pop();
      } else {
        results = await CompressedImagePicker.pickAndCompressMultipleImages(
          type: type,
        );
      }

      final files = <File>[];
      int totalCompressionCount = 0;
      double totalCompressionRatio = 0;

      for (final result in results) {
        if (result.isSuccess && result.file != null) {
          files.add(result.file!);
          if (result.compressionRatio > 0) {
            totalCompressionCount++;
            totalCompressionRatio += result.compressionRatio;
          }
        } else if (result.error != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(result.error!)),
          );
        }
      }

      // Show compression feedback
      if (showCompressionFeedback && totalCompressionCount > 0) {
        final avgCompression = totalCompressionRatio / totalCompressionCount;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Optimized $totalCompressionCount images (avg ${avgCompression.toStringAsFixed(1)}% reduction)',
            ),
            duration: Duration(seconds: 3),
          ),
        );
      }

      return files;
    } catch (e) {
      // Hide progress dialog if still showing
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error selecting images: $e')),
      );
      return [];
    }
  }

  /// Show image picker dialog with compression options
  static Future<File?> showImagePickerDialog({
    required BuildContext context,
    ImagePickerType type = ImagePickerType.mainImage,
    bool showCompressionInfo = true,
  }) async {
    final result = await CompressedImagePicker.showImagePickerDialog(
      context: context,
      type: type,
      showCompressionInfo: showCompressionInfo,
    );

    if (result != null && result.isSuccess && result.file != null) {
      // Show compression feedback if significant compression occurred
      if (result.compressionRatio > 10) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Image optimized: ${result.compressionInfo}'),
            duration: Duration(seconds: 3),
          ),
        );
      }
      return result.file;
    } else if (result != null && result.error != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(result.error!)),
      );
    }

    return null;
  }

  /// Quick replacement for profile image picking
  static Future<File?> pickProfileImage(BuildContext context) {
    return pickImageWithCompression(
      context: context,
      source: ImageSource.gallery,
      type: ImagePickerType.profile,
    );
  }

  /// Quick replacement for main image picking (class covers, center photos)
  static Future<File?> pickMainImage(BuildContext context) {
    return pickImageWithCompression(
      context: context,
      source: ImageSource.gallery,
      type: ImagePickerType.mainImage,
    );
  }

  /// Quick replacement for gallery image picking
  static Future<File?> pickGalleryImage(BuildContext context) {
    return pickImageWithCompression(
      context: context,
      source: ImageSource.gallery,
      type: ImagePickerType.gallery,
    );
  }

  /// Quick replacement for camera image picking
  static Future<File?> pickCameraImage(
    BuildContext context, {
    ImagePickerType type = ImagePickerType.mainImage,
  }) {
    return pickImageWithCompression(
      context: context,
      source: ImageSource.camera,
      type: type,
    );
  }
}

/// Extension to make migration easier
extension ImagePickerMigration on BuildContext {
  /// Quick access to compressed image picking
  Future<File?> pickCompressedImage({
    ImageSource source = ImageSource.gallery,
    ImagePickerType type = ImagePickerType.mainImage,
  }) {
    return ImagePickerMigrationHelper.pickImageWithCompression(
      context: this,
      source: source,
      type: type,
    );
  }

  /// Quick access to compressed profile image picking
  Future<File?> pickCompressedProfileImage() {
    return ImagePickerMigrationHelper.pickProfileImage(this);
  }

  /// Quick access to compressed main image picking
  Future<File?> pickCompressedMainImage() {
    return ImagePickerMigrationHelper.pickMainImage(this);
  }

  /// Quick access to compressed gallery image picking
  Future<File?> pickCompressedGalleryImage() {
    return ImagePickerMigrationHelper.pickGalleryImage(this);
  }
}
