import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

Future<String?> encodeFileToBase64(File? file) async {
  if (file == null) {
    return null; // Return null if the file is null
  }

  try {
    List<int> fileBytes = await file.readAsBytes();
    return base64Encode(fileBytes); // Return the base64 string
  } catch (e) {
    print("Error encoding file to base64: $e");
    return null; // Return null in case of any error
  }
}

Future<File?> decodeBase64ToFile(String base64String, String filePath) async {
  try {
    // Decode the base64 string to a list of bytes
    List<int> bytes = base64Decode(base64String);

    // Create a file at the given file path and write the decoded bytes to it
    File file = File(filePath);
    await file.writeAsBytes(bytes);

    return file; // Return the created file
  } catch (e) {
    print("Error decoding base64 to file: $e");
    return null; // Return null if an error occurs
  }
}

Uint8List? decodeBase64Image(String? base64String) {
  if (base64String == null || base64String.isEmpty) return null;
  return base64Decode(base64String);
}
