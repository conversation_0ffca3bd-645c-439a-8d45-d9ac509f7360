import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  
  const Failure({ this.message = "An unexpected error occurred."});
  
  @override
  List<Object?> get props => [message];
}
class ServerFailure extends Failure {
  const ServerFailure({String message = "Server Failure"}) : super(message: message);
}
class CacheFailure extends Failure {
  const CacheFailure({String message = "Cache Failure"}) : super(message: message);
}