# 🎓 ClassZ

ClassZ is a Flutter + Node.js application that connects parents with centers offering children’s classes like music, arts, and more. The app supports multi-role users (parents, coaches, centers), dynamic scheduling, and a streamlined class booking experience.

---

## 🧱 Tech Stack

### Frontend (Flutter)
- `flutter_bloc` for state management
- `flutter_screenutil` for responsive layouts
- Shared Preferences for session management
- Custom widgets: `DropDown`, `RadarChartWithLabels`, `CenterBranchCard`, etc.

### Backend (Node.js + MongoDB)
- Express.js API with role-based auth
- MongoDB via Mongoose
- Multer for file/image uploads
- Discriminators to support multi-role users

---

## 📲 Features

### ✅ User Roles
- **Parent**: Browse classes, register children, manage orders
- **Coach**: Assigned to classes, manages teaching
- **Center**: Creates classes, schedules, and handles bookings

### 📅 Scheduling System
: Input `weekday`, `time`, and `repeat count` to auto-generate class slots

### 📦 Orders & Class Booking
- Add class to request
- Use `ConfirmOrderEvent` to submit bookings
- Order includes class + child + parent info
- Orders fetched with deep population (`classId`, `center`, `coach`, etc.)

### 📁 Image Uploads
- Gallery-based selection in horizontal scrollable containers
- Multi-file upload via Postman (custom multer config)

### 📊 Dashboard Widgets
- Custom radar chart for visualizing class performance
- Widgets are sized using `ScreenUtil`

---

## 🧠 State Management

- `flutter_bloc` for event-driven updates
- Example: `AuthBloc` manages user session, order flow, and shared data access

---

## 📂 Directory Structure (App)

```bash
lib/
|── assets/  # Images, fonts, etc.
├── core/
│   ├── constants/
│   ├── error/
│   ├── network/
│   ├── service/
│   ├── utils/
│   ├── widgets/
│   └── theme/               # (optional)
│
├── routes/                  # (moved from core)
│   ├── app_router.dart
│   └── route_names.dart
│
├── dependency_injection/
│   └── injection.dart
│
├── features/
│   ├── authentications/
│   ├── chats/
│   └── notification/
│       ├── data/
│       ├── domain/
│       │   └── use_cases/
│       └── presentation/
│
├── config/                  # Env, flavors, etc             
├── app.dart
└── main.dart